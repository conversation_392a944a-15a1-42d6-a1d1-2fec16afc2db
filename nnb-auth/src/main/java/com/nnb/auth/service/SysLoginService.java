package com.nnb.auth.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.common.api.model.weCom.WeComUserInfoRes;
import com.common.api.service.WeComApiService;
import com.nnb.auth.form.DingLoginBody;
import com.nnb.auth.form.WeComLoginBody;
import com.nnb.auth.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.nnb.common.core.constant.Constants;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.constant.UserConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.enums.UserStatus;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.ServletUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.core.utils.ip.IpUtils;
import com.nnb.system.api.RemoteLogService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysLogininfor;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Resource
    private WeComApiService weComApiService;

    @Value("${auth.dingding.out.url}")
    private String dingOutUrl;

    @Value("${auth.dingding.in.url}")
    private String dingInUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger(SysLoginService.class);



    /**
     * 登录
     */
    public LoginUser login(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("登录用户：" + username + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        if (!SecurityUtils.matchesPassword(password, user.getPassword()))
        {
            recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码错误");
            throw new ServiceException("用户不存在/密码错误");
        }
        recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName)
    {
        recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode())
        {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogininfor(username, Constants.REGISTER, "注册成功");
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status 状态
     * @param message 消息内容
     * @return
     */
    public void recordLogininfor(String username, String status, String message)
    {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setUserName(username);
        logininfor.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        logininfor.setMsg(message);
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
        {
            logininfor.setStatus("0");
        }
        else if (Constants.LOGIN_FAIL.equals(status))
        {
            logininfor.setStatus("1");
        }
        remoteLogService.saveLogininfor(logininfor, SecurityConstants.INNER);
    }

    /**
     * 获取钉钉userId
     * @param dingLoginBody
     * @return
     * @throws Exception
     */
    public DingLoginBody getDingUserId(DingLoginBody dingLoginBody)throws Exception{
        String accessToken = HttpClientUtil.doGet(dingInUrl + "/openApi/dingTalk/token");
        if(StringUtils.isEmpty(accessToken)){
            throw new ServiceException("钉钉access_token为空");
        }
        dingLoginBody.setAccessToken(accessToken);
        Map<String, String> map = new HashMap<>();
        map.put("access_token", accessToken);
        map.put("code", dingLoginBody.getCode());
        //获取钉钉userid
        String json = HttpClientUtil.doGet(dingOutUrl + "/user/getuserinfo", map);
        LOGGER.error("获取钉钉userid返回结果为{}", json);
        JSONObject result = JSONObject.parseObject(json);

        if (StringUtils.isEmpty(json) || (!result.containsKey("userid")) || (StringUtils.isEmpty(result.getString("userid")))) {
            throw new ServiceException("钉钉userid为空");
        }
        dingLoginBody.setUserId(result.getString("userid"));
        return dingLoginBody;
    }

    /**
     * 钉钉登录
     * @param userid
     * @return
     */
    public LoginUser dingLogin(String userid)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(userid))
        {
            recordLogininfor(userid, Constants.LOGIN_FAIL, "钉钉userid为空");
            throw new ServiceException("钉钉userid为空");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getDingUserInfo(userid, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
            recordLogininfor(userid, Constants.LOGIN_FAIL, "钉钉登录用户不存在");
            throw new ServiceException("钉钉登录用户：" + userid + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogininfor(userid, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + userid + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogininfor(userid, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + userid + " 已停用");
        }

        recordLogininfor(userid, Constants.LOGIN_SUCCESS, "钉钉登录成功");
        return userInfo;
    }

    public LoginUser weComLogin(WeComLoginBody weComLoginBody) {
        //获取企业微信userid
        WeComUserInfoRes weComUserInfo = weComApiService.getUserInfo(
                weComLoginBody.getCode(), weComLoginBody.getCorpType(), weComLoginBody.getAppName()
        );
        // 用户名或密码为空 错误
        if (StrUtil.isEmpty(weComUserInfo.getUserId())) {
            recordLogininfor(weComUserInfo.getUserId(), Constants.LOGIN_FAIL, "企业微信userId为空");
            throw new ServiceException("企业微信userId为空");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getWeComUserInfo(weComUserInfo.getUserId(), SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }
        if (ObjectUtil.isNull(userResult) || ObjectUtil.isNull(userResult.getData())) {
            recordLogininfor(weComUserInfo.getUserId(), Constants.LOGIN_FAIL, "企业微信登录用户不存在");
            throw new ServiceException("企业微信登录用户：" + weComUserInfo.getUserId() + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogininfor(weComUserInfo.getUserId(), Constants.LOGIN_FAIL, "对不起，您的crm账号已被删除");
            throw new ServiceException("对不起，您的crm账号：" + user.getNickName() + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogininfor(weComUserInfo.getUserId(), Constants.LOGIN_FAIL, "用户crm已停用，请联系管理员");
            throw new ServiceException("对不起，您的crm账号：" + user.getNickName() + " 已停用");
        }
        recordLogininfor(weComUserInfo.getUserId(), Constants.LOGIN_SUCCESS, "企业微信登录成功");
        return userInfo;
    }
}
