package com.common.api.model.weCom;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: errcode        出错返回码，为0表示成功，非0表示调用失败
 *               errmsg	        返回码提示语
 *               access_token	获取到的凭证，最长为512字节
 *               expires_in	    凭证的有效时间（秒）
 * @Date: 2025-04-24
 * @Version: 1.0
 */
@NoArgsConstructor
@Data
public class AccessTokenRes implements Serializable {

    @JsonProperty("errcode")
    private Integer errCode;
    @JsonProperty("errmsg")
    private String errMsg;
    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("expires_in")
    private Integer expiresIn;
}
