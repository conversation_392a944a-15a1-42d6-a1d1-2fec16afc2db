package com.common.api.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-05-08
 * @Version: 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.ding")
public class DingProperties {

    private String appKey;
    private String appSecret;
    private Token token;
    private Message message;
    private Robot robot;

    @Data
    public static class Token {
        private String url;
    }

    @Data
    public static class Message {
        private String agentId;
        private String url;
    }

    @Data
    public static class Robot {
        private String url;
    }
}
