package com.common.api.properties;

import com.common.api.model.weCom.WeComApp;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-03-24
 * @Version: 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "we-come")
public class WeComProperties {

    private String libUrl;

    public static String STATIC_LIB_URL;

    private CorpId corpId;

    private PrivateKey privateKey;

    private Url url;

    private List<WeComApp> appList;

    public void setLibUrl(String libUrl) {
        WeComProperties.STATIC_LIB_URL = libUrl;
    }

    @Data
    public static class CorpId {
        private String hq;
        private String nnb;
    }

    @Data
    public static class PrivateKey {
        private String hq;
        private String nnb;
    }

    @Data
    public static class Url {
        private String getToken;
        private String getGroupChat;
        private String getUserInfo;
        private String getWeComApps;
        private String getJsApiTicket;
        private String getAppJsApiTicket;
        private String getGroupChatByRoomId;
    }

}
