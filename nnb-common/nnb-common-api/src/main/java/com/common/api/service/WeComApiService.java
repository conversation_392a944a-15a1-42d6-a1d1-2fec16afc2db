package com.common.api.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.common.api.config.WeComConfig;
import com.common.api.constants.ApiConstants;
import com.common.api.enums.CorpTypeEnum;
import com.common.api.model.weCom.AccessTokenRes;
import com.common.api.model.weCom.JsApiTicketRes;
import com.common.api.model.weCom.WeComUserInfoRes;
import com.common.api.properties.WeComProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnb.common.core.constant.CacheConstants;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-06-18
 * @Version: 1.0
 */
@Service
public class WeComApiService {

    @Resource
    private RedisService redisService;

    @Resource
    private WeComConfig weComConfig;

    @Resource
    private WeComProperties weComProperties;

    public String getAccessToken(Integer corpType, String appName) {
        //获取配置
        WeComConfig.TokenConfig tokenConfig = getTokenConfig(corpType, appName);
        //先从redis中取
        String redisToken = redisService.getCacheObject(tokenConfig.getRedisKey());
        if (StrUtil.isNotEmpty(redisToken)) {
            return redisToken;
        }
        //获取accessToken
        String resultJson = HttpUtil.get(
                String.format(weComProperties.getUrl().getGetToken(), tokenConfig.getCorpId(), tokenConfig.getSecret())
        );
        if (StrUtil.isEmpty(resultJson)) {
            throw new ServiceException(StrUtil.format("企业微信-类型【{}】,获取accessToken为空：{}", corpType));
        }
        //转类型解析
        AccessTokenRes accessTokenRes;
        try {
            accessTokenRes = new ObjectMapper().readValue(resultJson, AccessTokenRes.class);
            if (accessTokenRes.getErrCode() != 0) {
                throw new ServiceException(
                        StrUtil.format("企业微信-类型【{}】,获取accessToken失败：{}",
                                corpType, accessTokenRes.getErrMsg()
                        )
                );
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("企业微信 AccessTokenRes Json反序列化失败" + e.getMessage());
        }
        //放入redis
        redisService.setCacheObject(tokenConfig.getRedisKey(), accessTokenRes.getAccessToken());
        //获取过期时间
        if (ObjectUtil.isEmpty(accessTokenRes.getExpiresIn())) {
            redisService.expire(tokenConfig.getRedisKey(), 1, TimeUnit.HOURS);
        } else {
            LocalDateTime expireTime = LocalDateTime.now().plusSeconds(accessTokenRes.getExpiresIn());
            ZoneId zoneId = ZoneId.systemDefault();
            Date expireDate = Date.from(expireTime.atZone(zoneId).toInstant());
            redisService.expireAt(tokenConfig.getRedisKey(), expireDate);
        }
        return accessTokenRes.getAccessToken();
    }

    private WeComConfig.TokenConfig getTokenConfig(Integer corpType, String appName) {
        String corpName = CorpTypeEnum.getCorpName(corpType);
        return new WeComConfig.TokenConfig(
                String.format(CacheConstants.WE_COM_ACCESS_TOKEN_KEY, appName, corpName),
                weComConfig.getCorpId(corpType),
                weComConfig.getSecretKey(corpType, appName)
        );
    }

    /**
     * 获取访问用户身份
     * @param code 前端提供的code
     * @param corpType 企业类型
     * @return weComUserInfoRes
     */
    public WeComUserInfoRes getUserInfo(String code, Integer corpType, String appName) {
        // 获取accessToken
        String accessToken = getAccessToken(corpType, appName);
        // 获取JSON
        String resultJson = HttpUtil.get(String.format(weComProperties.getUrl().getGetUserInfo(), accessToken, code));
        //
        if (StrUtil.isEmpty(resultJson)) {
            throw new ServiceException(StrUtil.format("企业微信-类型【{}】,获取用户为空：{}", corpType));
        }
        //转类型解析
        WeComUserInfoRes weComUserInfoRes;
        try {
            weComUserInfoRes = new ObjectMapper().readValue(resultJson, WeComUserInfoRes.class);
            if (weComUserInfoRes.getErrCode() != 0) {
                throw new ServiceException(
                        StrUtil.format("企业微信-类型【{}】,获取用户失败：{}", corpType, weComUserInfoRes.getErrMsg())
                );
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("企业微信 AccessTokenRes Json反序列化失败" + e.getMessage());
        }
        return weComUserInfoRes;
    }

    /**
     * 获取企业 jsapi_ticket
     * 企业的 jsapi_ticket 是企业页面调用企业微信 JS 接口的临时票据，用于企业应用鉴权（getConfigSignature）
     * @param corpType corpType
     * @param appName appName
     * @return String
     */
    public String getJsApiTicket(Integer corpType, String appName){
        //先从redis中取
        String redisToken = redisService.getCacheObject(CacheConstants.WE_COM_JSAPI_TICKET_EN_KEY);
        if (StrUtil.isNotEmpty(redisToken)) {
            return redisToken;
        }
        //获取accessToken
        String resultJson = HttpUtil.get(
                String.format(weComProperties.getUrl().getGetJsApiTicket(), getAccessToken(corpType, appName))
        );
        if (StrUtil.isEmpty(resultJson)) {
            throw new ServiceException(StrUtil.format("企业微信-类型【{}】,获取jsapi_ticket为空：{}", corpType));
        }
        //转类型解析
        JsApiTicketRes jsApiTicketRes;
        try {
            jsApiTicketRes = new ObjectMapper().readValue(resultJson, JsApiTicketRes.class);
            if (jsApiTicketRes.getErrCode() != 0) {
                throw new ServiceException(
                        StrUtil.format("企业微信-类型【{}】,获取jsapi_ticket失败：{}",
                                corpType, jsApiTicketRes.getErrMsg()
                        )
                );
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("企业微信 JsApiTicketRes Json反序列化失败" + e.getMessage());
        }
        //放入redis
        redisService.setCacheObject(CacheConstants.WE_COM_JSAPI_TICKET_EN_KEY, jsApiTicketRes.getTicket());
        //获取过期时间
        LocalDateTime expireTime = LocalDateTime.now().plusSeconds(jsApiTicketRes.getExpiresIn());
        ZoneId zoneId = ZoneId.systemDefault();
        Date expireDate = Date.from(expireTime.atZone(zoneId).toInstant());
        redisService.expireAt(CacheConstants.WE_COM_JSAPI_TICKET_EN_KEY, expireDate);
        return jsApiTicketRes.getTicket();
    }

    /**
     * 获取应用 jsapi_ticket
     * 企业的 jsapi_ticket 是应用调用企业微信 JS 接口的临时票据，用于第三方应用鉴权（getAgentConfigSignature）
     * @param corpType corpType
     * @param appName appName
     * @return String
     */
    public String getAppJsApiTicket(Integer corpType, String appName){
        //先从redis中取
        String redisToken = redisService.getCacheObject(CacheConstants.WE_COM_JSAPI_TICKET_APP_KEY);
        if (StrUtil.isNotEmpty(redisToken)) {
            return redisToken;
        }
        //获取accessToken
        String resultJson = HttpUtil.get(
                String.format(weComProperties.getUrl().getGetAppJsApiTicket(), getAccessToken(corpType, appName))
        );
        if (StrUtil.isEmpty(resultJson)) {
            throw new ServiceException(StrUtil.format("企业微信-类型【{}】,获取app_jsapi_ticket为空：{}", corpType));
        }
        //转类型解析
        JsApiTicketRes jsApiTicketRes;
        try {
            jsApiTicketRes = new ObjectMapper().readValue(resultJson, JsApiTicketRes.class);
            if (jsApiTicketRes.getErrCode() != 0) {
                throw new ServiceException(
                        StrUtil.format("企业微信-类型【{}】,获取app_jsapi_ticket失败：{}",
                                corpType, jsApiTicketRes.getErrMsg()
                        )
                );
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("企业微信 appJsApiTicketRes Json反序列化失败" + e.getMessage());
        }
        //放入redis
        redisService.setCacheObject(CacheConstants.WE_COM_JSAPI_TICKET_APP_KEY, jsApiTicketRes.getTicket());
        //获取过期时间
        LocalDateTime expireTime = LocalDateTime.now().plusSeconds(jsApiTicketRes.getExpiresIn());
        ZoneId zoneId = ZoneId.systemDefault();
        Date expireDate = Date.from(expireTime.atZone(zoneId).toInstant());
        redisService.expireAt(CacheConstants.WE_COM_JSAPI_TICKET_APP_KEY, expireDate);
        return jsApiTicketRes.getTicket();
    }

    /**
     * 获取企业 JS-SDK
     * 企业的 jsapi_ticket 是企业页面调用企业微信 JS 接口的临时票据，用于企业应用鉴权（getConfigSignature）
     * @param corpType corpType
     * @param appName appName
     * @return String
     */
    public Map<String, Object> getJsSdkSign(Integer corpType, String appName, String url){
        Map<String, Object> resultMap = generateJsSdkSignature(getJsApiTicket(corpType, appName), url);
        resultMap.put("corpId", weComConfig.getCorpId(corpType));
        return resultMap;
    }

    /**
     * 获取应用 JS-SDK
     * 企业的 jsapi_ticket 是企业页面调用企业微信 JS 接口的临时票据，用于企业应用鉴权（getConfigSignature）
     * @param corpType corpType
     * @param appName appName
     * @return String
     */
    public Map<String, Object> getAppJsSdkSign(Integer corpType, String appName, String url){
        Map<String, Object> resultMap = generateJsSdkSignature(getAppJsApiTicket(corpType, appName), url);
        resultMap.put("corpId", weComConfig.getCorpId(corpType));
        resultMap.put("agentId", weComConfig.getAgentId(corpType, appName));
        return resultMap;
    }


    /**
     * 生成JS-SDK签名
     * @param jsapiTicket jsapi_ticket票据
     * @return 签名字符串
     */
    private Map<String, Object> generateJsSdkSignature(String jsapiTicket, String url) {
        String nonceStr = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signatureStr = String.format(ApiConstants.JS_SDK_SIGN, jsapiTicket, nonceStr, timestamp, url);
        try {
            // 使用SHA-1算法进行加密
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(signatureStr.getBytes(StandardCharsets.UTF_8));

            // 将加密后的字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : crypt.digest()) {
                sb.append(String.format("%02x", b & 0xff));
            }
            // 返回签名相关信息
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("timestamp", timestamp);
            resultMap.put("nonceStr", nonceStr);
            resultMap.put("signature", sb.toString());
            return resultMap;
        } catch (Exception e) {
            throw new ServiceException("生成JS-SDK签名失败: " + e.getMessage());
        }
    }
}
