package com.nnb.common.log.aspect;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;

import cn.hutool.json.JSONUtil;
import com.nnb.common.log.annotation.LogAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;

/**
 * 日志记录
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class LogAnnotationAspect {

    /**
     * Do around
     *
     * @param joinPoint
     */
    @Around("@annotation(logAnnotation)")
    public Object doAround(final ProceedingJoinPoint joinPoint, LogAnnotation logAnnotation) throws Throwable {
        //获取当前方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        //请求地址
        String requestURL = request.getRequestURI();

        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        //浏览器
        String browser = userAgent.getBrowser().getName();
        //操作系统
        String os = userAgent.getOs().getName();

        //ip-客户端反向代理后失效，如何解决？？？
        String ipAddress = request.getRemoteAddr();


        log.info("=============={}===============", logAnnotation.value());
        log.info("方法开始执行:{}", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss.SSS"));
        log.info("IP地址:{}", ipAddress);
        log.info("OS信息:{}", os);
        log.info("browser信息:{}", browser);
        log.info("请求路径:{}", requestURL);
        log.info("请求方法:[{}]", request.getMethod());
        log.info("请求签名:{}", signature);
        for (Object o : joinPoint.getArgs()) {
            if (o instanceof ServletRequest) {
                log.info("request参数[{}]", JSONUtil.toJsonStr(((ServletRequest) o).getParameterMap()));
            } else {
                log.info("其它参数[{}]", JSONUtil.toJsonStr(o));
            }
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object returnValue = joinPoint.proceed();
        stopWatch.stop();

        log.info("返回内容:[{}]", JSONUtil.toJsonStr(returnValue).length() > 500 ? JSONUtil.toJsonStr(returnValue).substring(0, 500) : JSONUtil.toJsonStr(returnValue));

        log.info("方法执行结束:{}, 耗时:{}:ms", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss.SSS"), stopWatch.getTime());

        return returnValue;
    }
}
