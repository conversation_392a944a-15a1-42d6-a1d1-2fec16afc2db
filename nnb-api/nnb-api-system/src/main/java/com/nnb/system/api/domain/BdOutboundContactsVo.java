package com.nnb.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value="BdOutboundContactsVo",description="通话记录对象Vo")
public class BdOutboundContactsVo {
    private static final long serialVersionUID = -9002001475025993915L;

    /** 联系人名称 */
    @ApiModelProperty("联系人名称")
    private String contactsName;
    /** 联系方式 */
    @ApiModelProperty("联系方式")
    private String numPhone;
    /** 创建人名称 */
    @ApiModelProperty("通话录音")
    private String vcContent;

    /** 创建时间 */
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date datCreatedAt;

    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getNumPhone() {
        return numPhone;
    }

    public void setNumPhone(String numPhone) {
        this.numPhone = numPhone;
    }

    public String getVcContent() {
        return vcContent;
    }

    public void setVcContent(String vcContent) {
        this.vcContent = vcContent;
    }

    public Date getDatCreatedAt() {
        return datCreatedAt;
    }

    public void setDatCreatedAt(Date datCreatedAt) {
        this.datCreatedAt = datCreatedAt;
    }
}
