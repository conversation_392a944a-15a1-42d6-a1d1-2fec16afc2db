package com.nnb.system.api.domain;

import java.util.StringJoiner;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-05
 * @Version: 1.0
 */
public class DistributionClueDTO {

    private Long clueId;
    private Long userId;
    private Long bdCustomersFollowId;
    private String bdGuestSrcName;
    private String contactWay;

    public DistributionClueDTO() {
    }

    public DistributionClueDTO(Long clueId, Long userId, Long bdCustomersFollowId, String bdGuestSrcName, String contactWay) {
        this.clueId = clueId;
        this.userId = userId;
        this.bdCustomersFollowId = bdCustomersFollowId;
        this.bdGuestSrcName = bdGuestSrcName;
        this.contactWay = contactWay;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getBdCustomersFollowId() {
        return bdCustomersFollowId;
    }

    public void setBdCustomersFollowId(Long bdCustomersFollowId) {
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public String getBdGuestSrcName() {
        return bdGuestSrcName;
    }

    public void setBdGuestSrcName(String bdGuestSrcName) {
        this.bdGuestSrcName = bdGuestSrcName;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public String getInvokeTargetParams(){
        StringJoiner stringJoiner = new StringJoiner(", ");
        stringJoiner.add(clueId + "L")
                .add(userId + "L")
                .add(bdCustomersFollowId + "L")
                .add("'" + bdGuestSrcName + "'")
                .add("'" + contactWay + "'");
        return stringJoiner.toString();
    }
}
