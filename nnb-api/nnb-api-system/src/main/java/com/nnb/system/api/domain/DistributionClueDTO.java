package com.nnb.system.api.domain;

import java.util.StringJoiner;

/**
 * @Author: <PERSON>-xy
 * @Description:
 * @Date: 2024-12-05
 * @Version: 1.0
 */
public class DistributionClueDTO {

    private Long clueId;
    private Long userId;
    private Long bdCustomersFollowId;

    public DistributionClueDTO() {
    }

    public DistributionClueDTO(Long clueId, Long userId, Long bdCustomersFollowId) {
        this.clueId = clueId;
        this.userId = userId;
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getBdCustomersFollowId() {
        return bdCustomersFollowId;
    }

    public void setBdCustomersFollowId(Long bdCustomersFollowId) {
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public String getInvokeTargetParams(){
        StringJoiner stringJoiner = new StringJoiner(", ");
        stringJoiner.add(clueId + "L")
                .add(userId + "L")
                .add(bdCustomersFollowId + "L");
        return stringJoiner.toString();
    }
}
