package com.nnb.system.api.factory;

import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.system.api.RemoteCustomerInsideService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: Chen-xy
 * @Description: customer服务 内部 定时任务 降级处理
 * @Date: 2024-12-04
 * @Version: 1.0
 */
@Component
public class RemoteCustomerInsideFallbackFactory implements FallbackFactory<RemoteCustomerInsideService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteCustomerInsideFallbackFactory.class);

    @Override
    public RemoteCustomerInsideService create(Throwable throwable) {

        log.error("customer服务内部定时任务调用失败:{}", throwable.getMessage());
        return new RemoteCustomerInsideService(){

            @Override
            public R<Boolean> sendDingTalkNotifications(Long clueId, Long Integer, String userName,
                                                           String bdGuestSrcName, String contactWay,
                                                           String dingTalkUserId, Integer dingTalkType) {
                return R.fail("推广线索发送钉钉消息定时任务失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> distributionClueManager(Long clueId, Long userId, Long bdCustomersFollowId) {
                return R.fail("推广线索移交经理定时任务失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> fallIntoTheSea(Long clueId, Long bdCustomersFollowId) {
                return R.fail("推广线索经理未分配掉入公海定时任务失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> fallIntoThePerson(Long clueId, Long userId, Long bdCustomersFollowId, Integer type) {
                return R.fail("推广线索数字部门掉入个人定时任务失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> fallIntoTheNichePool(Long clueId, Long bdCustomersFollowId, Integer type) {
                return R.fail("推广线索数字部门掉入商机池定时任务失败:" + throwable.getMessage());
            }
        };
    }
}
