package com.nnb.system.api.factory;

import com.nnb.common.core.domain.R;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.system.api.RemoteThirdPartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-03
 * @Version: 1.0
 */
@Component
public class RemoteThirdPartFallbackFactory implements FallbackFactory<RemoteThirdPartService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteThirdPartFallbackFactory.class);

    @Override
    public RemoteThirdPartService create(Throwable throwable) {
        log.error("third服务内部定时任务调用失败:{}", throwable.getMessage());

        return new RemoteThirdPartService() {

            @Override
            public R<Boolean> retrieveSessionContent() {
                return R.fail("企业微信获取会话内容定时任务失败:" + throwable.getMessage());
            }
        };
    }
}
