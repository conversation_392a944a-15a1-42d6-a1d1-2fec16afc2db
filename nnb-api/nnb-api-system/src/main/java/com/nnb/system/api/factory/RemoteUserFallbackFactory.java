package com.nnb.system.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.QueryLegworkDept;
import com.nnb.system.api.domain.SysDept;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.nnb.common.core.domain.R;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<List<SysUser>> getSysDeptChildren(Long deptId, String source) {
                return R.fail("获取部门及其子部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getSysDepByIdList(String deptId, String source) {
                return R.fail("获取部门集合失败" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R getSysDept(){
                return R.fail("获取客户发展部及其子部门失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getUserInfoById(Long userId, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> getUserListByIds(String userIds, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getSysDepByIds(List<Long> deptIds, String source) {
                return R.fail("获取用户部门失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> getUserListByNickName(String nickName, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> getAssignLegworkDept(QueryLegworkDept queryLegworkDeptDTO, String source) {
                return R.fail("获取可分配用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysDept>> getDeptChildrenByPid(Long deptId, String source) {
                return R.fail("获取部门及其子部门失败：" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> getUserListInOrderSendEmail(Long deptId, String source) {
                return R.fail("获取具有审核权限的部门经理失败：" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> getDingUserInfo(String dingUserId, String source) {
                return R.fail("根据钉钉userid获取用户信息失败：" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> getWeComUserInfo(String weComUserId, String source) {
                return R.fail("根据企业微信userid获取用户信息失败：" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getUserByShareUserPhone(String shareUserPhone, String source) {
                return R.fail("根据小程序手机号获取用户信息失败：" + throwable.getMessage());
            }

            @Override
            public R<JSONObject> getAllDeptSecondDeptName() {
                return R.fail("获取二级部门名称失败：" + throwable.getMessage());
            }
        };
    }
}
