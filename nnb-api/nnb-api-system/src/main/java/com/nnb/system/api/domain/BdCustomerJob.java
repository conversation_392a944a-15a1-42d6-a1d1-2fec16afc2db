package com.nnb.system.api.domain;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 北斗内部定时任务
 * @Date: 2024-12-04
 * @Version: 1.0
 */
public class BdCustomerJob extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("任务ID")
    private Long jobId;

    @ApiModelProperty("任务名称")
    private String jobName;

    @ApiModelProperty("任务组名")
    private String jobGroup;

    @Excel(name = "调用目标字符串")
    @ApiModelProperty("调用目标字符串")
    private String invokeTarget;

    @ApiModelProperty("cron执行表达式")
    private String cronExpression;

    @ApiModelProperty("cron计划策略：0=默认,1=立即触发执行,2=触发一次执行,3=不触发立即执行")
    private String misfirePolicy;

    @ApiModelProperty("是否并发执行（0允许 1禁止）")
    private String concurrent;

    @ApiModelProperty("任务状态（0正常 1暂停）")
    private String status;

    @ApiModelProperty("北斗线索id")
    private Long clueId;

    @ApiModelProperty("北斗跟进记录id")
    private Long bdCustomersFollowId;

    @ApiModelProperty("执行状态：0未执行；1执行")
    private Integer executionStatus;

    private List<Long> clueIdList;

    public BdCustomerJob() {
    }

    public BdCustomerJob(String jobName, String jobGroup, String invokeTarget, String cronExpression, String misfirePolicy, String concurrent, Long clueId, Long bdCustomersFollowId) {
        this.jobName = jobName;
        this.jobGroup = jobGroup;
        this.invokeTarget = invokeTarget;
        this.cronExpression = cronExpression;
        this.misfirePolicy = misfirePolicy;
        this.concurrent = concurrent;
        this.clueId = clueId;
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getInvokeTarget() {
        return invokeTarget;
    }

    public void setInvokeTarget(String invokeTarget) {
        this.invokeTarget = invokeTarget;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public String getMisfirePolicy() {
        return misfirePolicy;
    }

    public void setMisfirePolicy(String misfirePolicy) {
        this.misfirePolicy = misfirePolicy;
    }

    public String getConcurrent() {
        return concurrent;
    }

    public void setConcurrent(String concurrent) {
        this.concurrent = concurrent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Long getBdCustomersFollowId() {
        return bdCustomersFollowId;
    }

    public void setBdCustomersFollowId(Long bdCustomersFollowId) {
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public Integer getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(Integer executionStatus) {
        this.executionStatus = executionStatus;
    }

    public List<Long> getClueIdList() {
        return clueIdList;
    }

    public void setClueIdList(List<Long> clueIdList) {
        this.clueIdList = clueIdList;
    }

    @Override
    public String toString() {
        return "BdCustomerJob{" +
                "jobId=" + jobId +
                ", jobName='" + jobName + '\'' +
                ", jobGroup='" + jobGroup + '\'' +
                ", invokeTarget='" + invokeTarget + '\'' +
                ", cronExpression='" + cronExpression + '\'' +
                ", misfirePolicy='" + misfirePolicy + '\'' +
                ", concurrent='" + concurrent + '\'' +
                ", status='" + status + '\'' +
                ", clueId=" + clueId +
                ", bdCustomersFollowId=" + bdCustomersFollowId +
                ", executionStatus=" + executionStatus +
                '}';
    }

    public enum ConcurrentEnum{
        //是否并发执行（0允许 1禁止）
        ALLOW("0"),
        PROHIBIT("1");

        private final String value;

        public String getValue() {
            return value;
        }

        ConcurrentEnum(String value) {
            this.value = value;
        }
    }

    public enum MisfirePolicyEnum{
        //0=默认,1=立即触发执行,2=触发一次执行,3=不触发立即执行
        DEFAULT_EXECUTE("0"),
        EXECUTE_IMMEDIATELY("1"),
        ABANDONING_EXECUTION("3");

        private final String value;

        public String getValue() {
            return value;
        }

        MisfirePolicyEnum(String value) {
            this.value = value;
        }
    }

    public enum ExecutionStatusEnum{
        //执行状态：0未执行；1执行
        NOT_EXECUTE(0),
        EXECUTE(1);

        private final Integer value;

        public Integer getValue() {
            return value;
        }

        ExecutionStatusEnum(Integer value) {
            this.value = value;
        }
    }

    public enum StatusEnum{
        //任务状态（0正常 1暂停）
        NORMAL("0"),
        SUSPEND("1");

        private final String value;

        public String getValue() {
            return value;
        }

        StatusEnum(String value) {
            this.value = value;
        }
    }
}
