package com.nnb.system.api;

import com.nnb.common.core.constant.ServiceNameConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.system.api.domain.DistributionClueDTO;
import com.nnb.system.api.factory.RemoteCustomerInsideFallbackFactory;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: Chen-xy
 * @Description: customer服务 内部 定时任务服务
 * @Date: 2024-12-04
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteCustomerInsideService",
        value = ServiceNameConstants.CUSTOMER_SERVICE, fallbackFactory = RemoteCustomerInsideFallbackFactory.class,
        configuration = RemoteErpService.Configuration.class)
public interface RemoteCustomerInsideService {

    @GetMapping(value = "/bdJob/send-DingTalk-Notifications")
    R<Boolean> sendDingTalkNotifications(@RequestParam("clueId") Long clueId,
                                            @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                            @RequestParam("userName") String userName,
                                            @RequestParam("bdGuestSrcName") String bdGuestSrcName,
                                            @RequestParam("contactWay") String contactWay,
                                            @RequestParam("dingTalkUserId") String dingTalkUserId,
                                            @RequestParam("dingTalkType") Integer dingTalkType
    );

    @GetMapping(value = "/bdJob/distribution-clue-manager")
    R<Boolean> distributionClueManager(@RequestParam("clueId") Long clueId,
                                          @RequestParam("userId") Long userId,
                                          @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId
    );

    @GetMapping(value = "/bdJob/fall-into-the-sea")
    R<Boolean> fallIntoTheSea(@RequestParam("clueId") Long clueId,
                                 @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId
    );

    @GetMapping(value = "/bdJob/fall-into-the-person")
    R<Boolean> fallIntoThePerson(@RequestParam("clueId") Long clueId,
                                    @RequestParam("userId") Long userId,
                                    @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                    @RequestParam("type") Integer type
    );

    @GetMapping(value = "/bdJob/fall-into-the-niche-pool")
    R<Boolean> fallIntoTheNichePool(@RequestParam("clueId") Long clueId,
                                       @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                       @RequestParam("type") Integer type
    );

    class Configuration {
        @Bean
        Encoder feignFormEncoder(ObjectFactory<HttpMessageConverters> converters) {
            return new SpringFormEncoder(new SpringEncoder(converters));
        }
    }
}
