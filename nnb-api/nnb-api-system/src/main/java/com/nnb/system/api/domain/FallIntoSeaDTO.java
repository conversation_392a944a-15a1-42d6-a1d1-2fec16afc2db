package com.nnb.system.api.domain;

import org.springframework.web.bind.annotation.RequestParam;

import java.util.StringJoiner;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-06
 * @Version: 1.0
 */
public class FallIntoSeaDTO {

    Long clueId;
    Long bdCustomersFollowId;

    public FallIntoSeaDTO() {
    }

    public FallIntoSeaDTO(Long clueId, Long bdCustomersFollowId) {
        this.clueId = clueId;
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Long getBdCustomersFollowId() {
        return bdCustomersFollowId;
    }

    public void setBdCustomersFollowId(Long bdCustomersFollowId) {
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public String getInvokeTargetParams(){
        StringJoiner stringJoiner = new StringJoiner(", ");
        stringJoiner.add(clueId + "L")
                .add(bdCustomersFollowId + "L");
        return stringJoiner.toString();
    }
}
