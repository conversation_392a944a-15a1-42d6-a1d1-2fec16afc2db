package com.nnb.system.api.domain;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.annotation.Excel.ColumnType;
import com.nnb.common.core.annotation.Excel.Type;
import com.nnb.common.core.annotation.Excels;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@ApiModel(value="SysUser",description="user对象")
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户id")
    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /** 用户账号 */
    @Excel(name = "登录名称")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    @ApiModelProperty(value = "员工姓名")
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    private String email;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /** 最后登录时间 */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    /** 部门对象 */
    @Excels({
            @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
            @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    /** 角色对象 */
    private List<SysRole> roles;

    /** 角色组 */
    private Long[] roleIds;

    /** 岗位组 */
    private Long[] postIds;

    /** 角色ID */
    private Long roleId;

    /** crm用户原始ID */
    private Long oldUserId;

    /** 用户成员等级 */
    @ApiModelProperty(value = "员工等级")
    private String userLevel;

    /** 用户成员等级 */
    private List<String> userLevelList;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date userCreateTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("修改时间")
    private Date userUpdateTime;

    /**
     * 员工工号
     */
    @ApiModelProperty("员工工号")
    private String userNum;

    @ApiModelProperty("办公电话-提单使用")
    private String officePhone;

    @ApiModelProperty("主体")
    private Integer principalPart;


    @ApiModelProperty("上次重置密码成功的时间")
    private Date resetTime;

    @ApiModelProperty("当前登录人的crm_enterprise_id  启照多部门区分:  1：牛牛帮部门, 2:启照多部门")
    private Integer crmEnterpriseId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("入职时间")
    private Date entryTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("离职时间")
    private Date leaveTime;

    @ApiModelProperty("在职天数")
    private Integer daysOnTheJob;

    @ApiModelProperty("员工部门ID")
    private Long personDeptId;

    @ApiModelProperty("支付公司主体")
    private Long companyId;

    @ApiModelProperty("获取当前登陆人部门及下属部门员工，传1，否则不传")
    private Long selfDept;

    @ApiModelProperty("钉钉user_id")
    private String dingUserId;

    @ApiModelProperty("企业微信user_id")
    private String weChatId;

    @ApiModelProperty("是否是领导：0.否 1.是")
    private String isLeader;

    @ApiModelProperty("小程序登录电话，用来分享提单记录销售使用")
    private String xcxPhone;

    @ApiModelProperty("小程序登录电话绑定牛牛帮还是后企")
    private Integer xcxEnterpriseDominant;

    @ApiModelProperty("第三方呼叫id")
    private String callUserId;

    @ApiModelProperty("绑定主体")
    private String onlineMainId;

    @ApiModelProperty("所属部门ids")
    private String ancestors;

    public SysUser()
    {

    }

    public String getWeChatId() {
        return weChatId;
    }

    public void setWeChatId(String weChatId) {
        this.weChatId = weChatId;
    }

    public String getCallUserId() {
        return callUserId;
    }

    public void setCallUserId(String callUserId) {
        this.callUserId = callUserId;
    }

    public Date getResetTime() {
        return resetTime;
    }

    public void setResetTime(Date resetTime) {
        this.resetTime = resetTime;
    }

    public Date getUserCreateTime() {
        return userCreateTime;
    }

    public void setUserCreateTime(Date userCreateTime) {
        this.userCreateTime = userCreateTime;
    }

    public Date getUserUpdateTime() {
        return userUpdateTime;
    }

    public void setUserUpdateTime(Date userUpdateTime) {
        this.userUpdateTime = userUpdateTime;
    }

    public List<String> getUserLevelList() {
        return userLevelList;
    }

    public void setUserLevelList(List<String> userLevelList) {
        this.userLevelList = userLevelList;
    }

    public SysUser(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public boolean isAdmin()
    {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    @Size(min = 0, max = 50, message = "用户昵称长度不能超过50个字符")
    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 50, message = "用户账号长度不能超过50个字符")
    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 100, message = "邮箱长度不能超过100个字符")
    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    @JsonProperty
    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getLoginIp()
    {
        return loginIp;
    }

    public void setLoginIp(String loginIp)
    {
        this.loginIp = loginIp;
    }

    public Date getLoginDate()
    {
        return loginDate;
    }

    public void setLoginDate(Date loginDate)
    {
        this.loginDate = loginDate;
    }

    public SysDept getDept()
    {
        return dept;
    }

    public void setDept(SysDept dept)
    {
        this.dept = dept;
    }

    public List<SysRole> getRoles()
    {
        return roles;
    }

    public void setRoles(List<SysRole> roles)
    {
        this.roles = roles;
    }

    public Long[] getRoleIds()
    {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds)
    {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds()
    {
        return postIds;
    }

    public void setPostIds(Long[] postIds)
    {
        this.postIds = postIds;
    }

    public Long getRoleId()
    {
        return roleId;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public Long getOldUserId() {
        return oldUserId;
    }

    public void setOldUserId(Long oldUserId) {
        this.oldUserId = oldUserId;
    }

    public String getUserLevel() {
        return userLevel;
    }

    public void setUserLevel(String userLevel) {
        this.userLevel = userLevel;
    }

    public String getUserNum() {
        return userNum;
    }

    public void setUserNum(String userNum) {
        this.userNum = userNum;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public Integer getPrincipalPart() {
        return principalPart;
    }

    public void setPrincipalPart(Integer principalPart) {
        this.principalPart = principalPart;
    }

    public Integer getCrmEnterpriseId() {
        return crmEnterpriseId;
    }

    public void setCrmEnterpriseId(Integer crmEnterpriseId) {
        this.crmEnterpriseId = crmEnterpriseId;
    }

    public Date getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(Date entryTime) {
        this.entryTime = entryTime;
    }

    public Date getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = leaveTime;
    }

    public Integer getDaysOnTheJob() {
        return daysOnTheJob;
    }

    public void setDaysOnTheJob(Integer daysOnTheJob) {
        this.daysOnTheJob = daysOnTheJob;
    }

    public Long getPersonDeptId() {
        return personDeptId;
    }

    public void setPersonDeptId(Long personDeptId) {
        this.personDeptId = personDeptId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getSelfDept() {
        return selfDept;
    }

    public void setSelfDept(Long selfDept) {
        this.selfDept = selfDept;
    }

    public String getDingUserId() {
        return dingUserId;
    }

    public void setDingUserId(String dingUserId) {
        this.dingUserId = dingUserId;
    }

    public String getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(String isLeader) {
        this.isLeader = isLeader;
    }

    public String getXcxPhone() {
        return xcxPhone;
    }

    public void setXcxPhone(String xcxPhone) {
        this.xcxPhone = xcxPhone;
    }

    public Integer getXcxEnterpriseDominant() {
        return xcxEnterpriseDominant;
    }

    public void setXcxEnterpriseDominant(Integer xcxEnterpriseDominant) {
        this.xcxEnterpriseDominant = xcxEnterpriseDominant;
    }

    public String getOnlineMainId() {
        return onlineMainId;
    }

    public void setOnlineMainId(String onlineMainId) {
        this.onlineMainId = onlineMainId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("deptId", getDeptId())
                .append("userName", getUserName())
                .append("nickName", getNickName())
                .append("email", getEmail())
                .append("phonenumber", getPhonenumber())
                .append("sex", getSex())
                .append("avatar", getAvatar())
                .append("password", getPassword())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("loginIp", getLoginIp())
                .append("loginDate", getLoginDate())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("dept", getDept())
                .toString();
    }
}
