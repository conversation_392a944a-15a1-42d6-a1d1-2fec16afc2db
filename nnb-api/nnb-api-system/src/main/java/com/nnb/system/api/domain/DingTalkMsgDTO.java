package com.nnb.system.api.domain;

import java.util.StringJoiner;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-04
 * @Version: 1.0
 */

public class DingTalkMsgDTO {

    private Long clueId;

    private Long bdCustomersFollowId;

    private String userName;

    private String bdGuestSrcName;

    private String contactWay;

    private String dingTalkUserId;

    private Integer dingTalkType;

    public DingTalkMsgDTO() {
    }

    public DingTalkMsgDTO(Long clueId, String userName, String bdGuestSrcName, String contactWay, String dingTalkUserId) {
        this.clueId = clueId;
        this.userName = userName;
        this.bdGuestSrcName = bdGuestSrcName;
        this.contactWay = contactWay;
        this.dingTalkUserId = dingTalkUserId;
    }

    public DingTalkMsgDTO(Long clueId, Long bdCustomersFollowId, String userName, String bdGuestSrcName, String contactWay, String dingTalkUserId, Integer dingTalkType) {
        this.clueId = clueId;
        this.bdCustomersFollowId = bdCustomersFollowId;
        this.userName = userName;
        this.bdGuestSrcName = bdGuestSrcName;
        this.contactWay = contactWay;
        this.dingTalkUserId = dingTalkUserId;
        this.dingTalkType = dingTalkType;
    }

    public Long getClueId() {
        return clueId;
    }

    public void setClueId(Long clueId) {
        this.clueId = clueId;
    }

    public Long getBdCustomersFollowId() {
        return bdCustomersFollowId;
    }

    public void setBdCustomersFollowId(Long bdCustomersFollowId) {
        this.bdCustomersFollowId = bdCustomersFollowId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getBdGuestSrcName() {
        return bdGuestSrcName;
    }

    public void setBdGuestSrcName(String bdGuestSrcName) {
        this.bdGuestSrcName = bdGuestSrcName;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public String getDingTalkUserId() {
        return dingTalkUserId;
    }

    public void setDingTalkUserId(String dingTalkUserId) {
        this.dingTalkUserId = dingTalkUserId;
    }

    public Integer getDingTalkType() {
        return dingTalkType;
    }

    public void setDingTalkType(Integer dingTalkType) {
        this.dingTalkType = dingTalkType;
    }

    public String getInvokeTargetParams(){
        StringJoiner stringJoiner = new StringJoiner(", ");
        stringJoiner.add(clueId + "L")
                .add(bdCustomersFollowId + "L")
                .add("'" + userName + "'")
                .add("'" + bdGuestSrcName + "'")
                .add("'" + contactWay + "'")
                .add("'" + dingTalkUserId + "'")
                .add(String.valueOf(dingTalkType));
        return stringJoiner.toString();
    }

    public enum DingTalkTypeEnum{
        //1:10分钟，2:50分钟
        TEN(1),
        FIFTY(2),
        THIRTY(3),
        TWENTY_FIVE(4);

        private final Integer value;

        public Integer getValue() {
            return value;
        }

        DingTalkTypeEnum(Integer value) {
            this.value = value;
        }
    }
}
