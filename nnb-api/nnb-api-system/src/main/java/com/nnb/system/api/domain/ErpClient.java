package com.nnb.system.api.domain;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 企业/个人客户对象 erp_client
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@ApiModel(value="ErpClient",description="企业/个人客户对象")
public class ErpClient extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 企业id */
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long numEnterpriseId;

    /** 个人id */
    @Excel(name = "个人id")
    @ApiModelProperty("个人id")
    private Long numPersonalId;

    /** 线索id，企业最后一个经绑定的线索 */
    @Excel(name = "线索id，企业最后一个经绑定的线索")
    @ApiModelProperty("线索id，企业最后一个经绑定的线索")
    private Long numClueId;
    /**
     * 联系人名称。
     */
    @ApiModelProperty("联系人名称。")
    private String contactName;

    /**
     * 联系电话。
     */
    @ApiModelProperty("联系电话。")
    private String contactPhone;

    /** 企业状态，0 未成交；1 已成交 */
    @Excel(name = "企业状态，0 未成交；1 已成交")
    @ApiModelProperty("企业状态，0 未成交；1 已成交")
    private Integer numStatus;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty("城市id")
    private Long numCityId;

    /** 客户类型：1 企业；2 个人 */
    @Excel(name = "客户类型：1 企业；2 个人")
    @ApiModelProperty("客户类型：1 企业；2 个人")
    private Integer numType;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String vcRemark;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date datSigningDatecreatedTime;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private Long numUpdatedBy;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private Date datSigningDateupdatedTime;

    @ApiModelProperty("用户标签：（1：会计）")
    private Integer label;

    @ApiModelProperty("来源:1提单，2老客，3会计")
    private Integer source;

    @ApiModelProperty("置顶：1是0否")
    private Integer top;

    @ApiModelProperty("空号检测")
    private Integer spaceCheck;

    @ApiModelProperty("风险检测")
    private Integer riskCheck;

    @ApiModelProperty("邮箱")
    private String postBox;

    @ApiModelProperty("性别")
    private Integer sex;

    @ApiModelProperty("角色")
    private String role;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNumEnterpriseId() {
        return numEnterpriseId;
    }

    public void setNumEnterpriseId(Long numEnterpriseId) {
        this.numEnterpriseId = numEnterpriseId;
    }

    public Long getNumPersonalId() {
        return numPersonalId;
    }

    public void setNumPersonalId(Long numPersonalId) {
        this.numPersonalId = numPersonalId;
    }

    public Long getNumClueId() {
        return numClueId;
    }

    public void setNumClueId(Long numClueId) {
        this.numClueId = numClueId;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Integer getNumStatus() {
        return numStatus;
    }

    public void setNumStatus(Integer numStatus) {
        this.numStatus = numStatus;
    }

    public Long getNumCityId() {
        return numCityId;
    }

    public void setNumCityId(Long numCityId) {
        this.numCityId = numCityId;
    }

    public Integer getNumType() {
        return numType;
    }

    public void setNumType(Integer numType) {
        this.numType = numType;
    }

    public String getVcRemark() {
        return vcRemark;
    }

    public void setVcRemark(String vcRemark) {
        this.vcRemark = vcRemark;
    }

    public Long getNumCreatedBy() {
        return numCreatedBy;
    }

    public void setNumCreatedBy(Long numCreatedBy) {
        this.numCreatedBy = numCreatedBy;
    }

    public Date getDatSigningDatecreatedTime() {
        return datSigningDatecreatedTime;
    }

    public void setDatSigningDatecreatedTime(Date datSigningDatecreatedTime) {
        this.datSigningDatecreatedTime = datSigningDatecreatedTime;
    }

    public Long getNumUpdatedBy() {
        return numUpdatedBy;
    }

    public void setNumUpdatedBy(Long numUpdatedBy) {
        this.numUpdatedBy = numUpdatedBy;
    }

    public Date getDatSigningDateupdatedTime() {
        return datSigningDateupdatedTime;
    }

    public void setDatSigningDateupdatedTime(Date datSigningDateupdatedTime) {
        this.datSigningDateupdatedTime = datSigningDateupdatedTime;
    }

    public Integer getLabel() {
        return label;
    }

    public void setLabel(Integer label) {
        this.label = label;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getTop() {
        return top;
    }

    public void setTop(Integer top) {
        this.top = top;
    }

    public Integer getSpaceCheck() {
        return spaceCheck;
    }

    public void setSpaceCheck(Integer spaceCheck) {
        this.spaceCheck = spaceCheck;
    }

    public Integer getRiskCheck() {
        return riskCheck;
    }

    public void setRiskCheck(Integer riskCheck) {
        this.riskCheck = riskCheck;
    }

    public String getPostBox() {
        return postBox;
    }

    public void setPostBox(String postBox) {
        this.postBox = postBox;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }
}
