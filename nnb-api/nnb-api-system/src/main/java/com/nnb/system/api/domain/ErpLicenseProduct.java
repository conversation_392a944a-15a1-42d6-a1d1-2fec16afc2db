package com.nnb.system.api.domain;

import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 执照产品对象 erp_license_product
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */

@ApiModel(value="ErpLicenseProduct",description="执照产品对象")
public class ErpLicenseProduct extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("执照Id")
    private Integer licenseId;

    @ApiModelProperty("产品Id")
    private Integer productId;

    @ApiModelProperty("产品价格")
    private BigDecimal productPrice;

    public ErpLicenseProduct() {
    }

    public ErpLicenseProduct(Integer licenseId, Integer productId, BigDecimal productPrice) {
        this.licenseId = licenseId;
        this.productId = productId;
        this.productPrice = productPrice;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(Integer licenseId) {
        this.licenseId = licenseId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public BigDecimal getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(BigDecimal productPrice) {
        this.productPrice = productPrice;
    }
}
