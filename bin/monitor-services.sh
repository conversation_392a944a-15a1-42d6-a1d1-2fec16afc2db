#!/bin/bash

# 服务监控和自动重启脚本
# 使用方法: ./monitor-services.sh [配置文件路径]

# 默认配置文件路径
CONFIG_FILE="${1:-./service-monitor.conf}"
LOG_FILE="/var/log/service-monitor.log"

# 确保日志文件存在
touch "$LOG_FILE"

# 记录日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    log "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 加载配置文件
source "$CONFIG_FILE"

# 检查必要的变量是否已定义
if [ -z "$SERVICES" ]; then
    log "错误: 配置文件中未定义服务列表 (SERVICES)"
    exit 1
fi

# 监控并重启服务的函数
monitor_and_restart() {
    local service_name="$1"
    local jar_path="$2"
    local java_opts="$3"
    local process_keyword="$4"
    
    # 检查服务是否运行
    if pgrep -f "$process_keyword" > /dev/null; then
        log "服务 $service_name 正在运行"
    else
        log "警告: 服务 $service_name 未运行，正在重启..."
        
        # 获取jar文件所在目录
        local jar_dir=$(dirname "$jar_path")
        
        # 切换到jar目录
        cd "$jar_dir" || { log "错误: 无法切换到目录 $jar_dir"; return 1; }
        
        # 启动服务
        # nohup java $java_opts -jar "$jar_path" > "$jar_dir/nohup.out" 2>&1 &
        nohup java -jar "$jar_path" --spring.profiles.active=test >/dev/null 2>&1 &
        # 检查启动是否成功
        sleep 10
        if pgrep -f "$process_keyword" > /dev/null; then
            log "服务 $service_name 已成功重启"
            
            # 发送通知（可选）
            if [ -n "$NOTIFICATION_CMD" ]; then
                $NOTIFICATION_CMD "服务器通知" "服务 $service_name 已自动重启"
            fi
        else
            log "错误: 服务 $service_name 重启失败"
            
            # 发送失败通知（可选）
            if [ -n "$NOTIFICATION_CMD" ]; then
                $NOTIFICATION_CMD "服务器警告" "服务 $service_name 重启失败，请手动检查"
            fi
        fi
    fi
}

# 主循环
log "服务监控脚本已启动"

while true; do
    for service in $SERVICES; do
        # 获取服务配置
        service_name="${service}_NAME"
        jar_path="${service}_JAR_PATH"
        java_opts="${service}_JAVA_OPTS"
        process_keyword="${service}_PROCESS_KEYWORD"
        
        # 检查配置是否存在
        if [ -z "${!service_name}" ] || [ -z "${!jar_path}" ] || [ -z "${!process_keyword}" ]; then
            log "错误: 服务 $service 配置不完整"
            continue
        fi
        
        # 监控并在需要时重启服务
        monitor_and_restart "${!service_name}" "${!jar_path}" "${!java_opts}" "${!process_keyword}"
    done
    
    # 等待下一次检查
    sleep "$CHECK_INTERVAL"
done