# SystemD 服务安装指南

本指南介绍如何将监控脚本安装为 systemd 系统服务，实现开机自启动和系统级管理。

## 🚀 快速安装

### 1. 安装为系统服务

```bash
# 切换到root用户
sudo su -

# 进入脚本目录
cd /path/to/your/monitor

# 执行安装
./install-systemd.sh install
```

### 2. 验证安装

```bash
# 查看服务状态
./install-systemd.sh status

# 或者直接使用systemctl
systemctl status service-monitor.service
systemctl status log-cleanup.timer
```

## 📋 服务说明

### 创建的服务

1. **service-monitor.service**
   - 服务监控和自动重启
   - 开机自启动
   - 异常时自动重启
   - 安装位置：`/etc/systemd/system/service-monitor.service`

2. **log-cleanup.timer + log-cleanup.service**
   - 定时日志清理
   - 每周日凌晨2点执行
   - 清理7天前的service-monitor.log
   - 安装位置：`/etc/systemd/system/log-cleanup.*`

### 文件布局

```
/opt/monitor/                          # 安装目录
├── monitor-services.sh                # 监控脚本
├── log-cleanup.sh                     # 清理脚本
└── service-monitor.conf               # 配置文件

/etc/systemd/system/                   # systemd服务目录
├── service-monitor.service            # 监控服务
├── log-cleanup.service               # 清理服务
└── log-cleanup.timer                 # 清理定时器
```

## 🛠️ 管理命令

### 使用安装脚本管理

```bash
# 安装服务
./install-systemd.sh install

# 查看状态
./install-systemd.sh status

# 重启服务
./install-systemd.sh restart

# 查看日志
./install-systemd.sh logs

# 卸载服务
./install-systemd.sh uninstall
```

### 使用systemctl管理

```bash
# 启动/停止服务
systemctl start service-monitor.service
systemctl stop service-monitor.service

# 重启服务
systemctl restart service-monitor.service

# 查看状态
systemctl status service-monitor.service

# 启用/禁用开机自启
systemctl enable service-monitor.service
systemctl disable service-monitor.service

# 查看日志
journalctl -u service-monitor.service -f
journalctl -u service-monitor.service --since "1 hour ago"

# 定时器管理
systemctl start log-cleanup.timer
systemctl status log-cleanup.timer
systemctl list-timers log-cleanup.timer
```

## 📊 监控和日志

### 查看服务日志

```bash
# 实时查看监控服务日志
journalctl -u service-monitor.service -f

# 查看最近50条日志
journalctl -u service-monitor.service -n 50

# 查看指定时间段的日志
journalctl -u service-monitor.service --since "2024-01-01" --until "2024-01-02"

# 查看日志清理服务日志
journalctl -u log-cleanup.service -n 20
```

### 查看定时器状态

```bash
# 查看所有定时器
systemctl list-timers

# 查看特定定时器
systemctl list-timers log-cleanup.timer

# 查看定时器详细信息
systemctl status log-cleanup.timer
```

## ⚙️ 配置修改

### 修改监控配置

```bash
# 编辑配置文件
vim /opt/monitor/service-monitor.conf

# 重启服务使配置生效
systemctl restart service-monitor.service
```

### 修改定时器时间

```bash
# 编辑定时器文件
vim /etc/systemd/system/log-cleanup.timer

# 重新加载配置
systemctl daemon-reload

# 重启定时器
systemctl restart log-cleanup.timer
```

### 常用定时器配置

```ini
# 每天凌晨2点
OnCalendar=*-*-* 02:00:00

# 每周一凌晨3点
OnCalendar=Mon *-*-* 03:00:00

# 每月1号凌晨4点
OnCalendar=*-*-01 04:00:00

# 每小时执行
OnCalendar=hourly

# 每天执行
OnCalendar=daily
```

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细错误信息
   systemctl status service-monitor.service -l
   journalctl -u service-monitor.service --no-pager
   
   # 检查脚本权限
   ls -la /opt/monitor/
   
   # 检查配置文件
   cat /opt/monitor/service-monitor.conf
   ```

2. **定时器不执行**
   ```bash
   # 检查定时器状态
   systemctl list-timers log-cleanup.timer
   
   # 手动执行一次
   systemctl start log-cleanup.service
   
   # 查看执行日志
   journalctl -u log-cleanup.service
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x /opt/monitor/*.sh
   
   # 确保配置文件可读
   chmod 644 /opt/monitor/service-monitor.conf
   ```

### 调试模式

```bash
# 停止服务
systemctl stop service-monitor.service

# 手动运行脚本进行调试
cd /opt/monitor
./monitor-services.sh

# 查看详细日志
journalctl -u service-monitor.service -f --output=verbose
```

## 🔄 升级和维护

### 升级脚本

```bash
# 停止服务
systemctl stop service-monitor.service

# 备份当前版本
cp -r /opt/monitor /opt/monitor.backup.$(date +%Y%m%d)

# 复制新版本脚本
cp monitor-services.sh /opt/monitor/
cp log-cleanup.sh /opt/monitor/

# 重启服务
systemctl start service-monitor.service
```

### 备份配置

```bash
# 备份配置文件
cp /opt/monitor/service-monitor.conf /opt/monitor/service-monitor.conf.backup

# 备份systemd服务文件
cp /etc/systemd/system/service-monitor.service /etc/systemd/system/service-monitor.service.backup
```

## 📈 性能监控

### 查看资源使用

```bash
# 查看服务资源使用情况
systemctl status service-monitor.service

# 查看进程信息
ps aux | grep monitor-services.sh

# 查看内存使用
systemctl show service-monitor.service --property=MemoryCurrent
```

### 设置资源限制

编辑服务文件 `/etc/systemd/system/service-monitor.service`：

```ini
[Service]
# 内存限制
MemoryLimit=100M

# CPU限制
CPUQuota=50%

# 文件描述符限制
LimitNOFILE=1024
```

## 🔐 安全考虑

1. **最小权限原则**：服务以root用户运行，确保只有必要的权限
2. **文件权限**：确保配置文件不被普通用户修改
3. **日志安全**：定期清理敏感信息
4. **网络安全**：钉钉通知使用内网地址

## 📞 支持

如果遇到问题，请：

1. 查看服务日志：`journalctl -u service-monitor.service`
2. 检查配置文件：`/opt/monitor/service-monitor.conf`
3. 验证脚本权限：`ls -la /opt/monitor/`
4. 测试网络连接：`curl http://127.0.0.1:9405/weCom/sendWarmDingMessage`
