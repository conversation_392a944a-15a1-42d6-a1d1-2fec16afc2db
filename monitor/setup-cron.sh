#!/bin/bash

# 设置定时任务脚本
# 用于配置日志清理和服务监控的定时任务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_CLEANUP_SCRIPT="$SCRIPT_DIR/log-cleanup.sh"
MONITOR_SCRIPT="$SCRIPT_DIR/monitor-services.sh"

# 记录日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 检查脚本是否存在
check_scripts() {
    if [ ! -f "$LOG_CLEANUP_SCRIPT" ]; then
        log "错误: 日志清理脚本不存在: $LOG_CLEANUP_SCRIPT"
        exit 1
    fi
    
    if [ ! -f "$MONITOR_SCRIPT" ]; then
        log "错误: 服务监控脚本不存在: $MONITOR_SCRIPT"
        exit 1
    fi
    
    # 确保脚本有执行权限
    chmod +x "$LOG_CLEANUP_SCRIPT"
    chmod +x "$MONITOR_SCRIPT"
}

# 设置cron任务
setup_cron() {
    log "开始设置定时任务..."
    
    # 备份现有的crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # 获取当前的crontab内容
    current_cron=$(crontab -l 2>/dev/null || echo "")
    
    # 定义新的cron任务
    log_cleanup_cron="0 2 * * 0 $LOG_CLEANUP_SCRIPT --check >/dev/null 2>&1"
    monitor_check_cron="*/5 * * * * pgrep -f 'monitor-services.sh' >/dev/null || nohup $MONITOR_SCRIPT >/dev/null 2>&1 &"
    
    # 检查是否已经存在相关任务
    if echo "$current_cron" | grep -q "$LOG_CLEANUP_SCRIPT"; then
        log "日志清理任务已存在，跳过添加"
    else
        log "添加日志清理定时任务（每周日凌晨2点执行）"
        current_cron="$current_cron
$log_cleanup_cron"
    fi
    
    if echo "$current_cron" | grep -q "monitor-services.sh"; then
        log "服务监控任务已存在，跳过添加"
    else
        log "添加服务监控检查任务（每5分钟检查一次）"
        current_cron="$current_cron
$monitor_check_cron"
    fi
    
    # 应用新的crontab
    echo "$current_cron" | crontab -
    
    log "定时任务设置完成"
    log "当前的crontab内容:"
    crontab -l
}

# 移除cron任务
remove_cron() {
    log "开始移除定时任务..."
    
    # 获取当前的crontab内容
    current_cron=$(crontab -l 2>/dev/null || echo "")
    
    # 移除相关任务
    new_cron=$(echo "$current_cron" | grep -v "$LOG_CLEANUP_SCRIPT" | grep -v "monitor-services.sh")
    
    # 应用新的crontab
    echo "$new_cron" | crontab -
    
    log "定时任务移除完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install    安装定时任务"
    echo "  remove     移除定时任务"
    echo "  status     显示当前定时任务状态"
    echo "  help       显示此帮助信息"
    echo ""
    echo "定时任务说明:"
    echo "  1. 日志清理: 每周日凌晨2点检查并清理7天前的日志"
    echo "  2. 服务监控: 每5分钟检查监控脚本是否运行，如未运行则自动启动"
}

# 显示状态
show_status() {
    log "当前定时任务状态:"
    echo ""
    
    current_cron=$(crontab -l 2>/dev/null || echo "")
    
    if echo "$current_cron" | grep -q "$LOG_CLEANUP_SCRIPT"; then
        echo "✓ 日志清理任务已配置"
        echo "$current_cron" | grep "$LOG_CLEANUP_SCRIPT"
    else
        echo "✗ 日志清理任务未配置"
    fi
    
    if echo "$current_cron" | grep -q "monitor-services.sh"; then
        echo "✓ 服务监控任务已配置"
        echo "$current_cron" | grep "monitor-services.sh"
    else
        echo "✗ 服务监控任务未配置"
    fi
    
    echo ""
    echo "完整的crontab内容:"
    crontab -l 2>/dev/null || echo "无定时任务"
}

# 主程序
main() {
    case "$1" in
        "install")
            check_scripts
            setup_cron
            ;;
        "remove")
            remove_cron
            ;;
        "status")
            show_status
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            echo "错误: 未知选项 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"
