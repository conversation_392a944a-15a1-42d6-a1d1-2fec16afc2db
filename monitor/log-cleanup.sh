#!/bin/bash

# 日志清理脚本
# 使用方法: ./log-cleanup.sh [天数] [是否发送通知]
# 示例: ./log-cleanup.sh 7 true

# 默认配置
DAYS_TO_KEEP="${1:-7}"
SEND_NOTIFICATION="${2:-true}"
LOG_CLEANUP_FLAG="/var/log/.log_cleanup_flag"

# 钉钉通知配置
DING_API_URL="http://127.0.0.1:9405/weCom/sendWarmDingMessage"
DING_USER_LIST=("admin" "ops" "monitor")

# 记录日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "/var/log/log-cleanup.log"
}

# 发送钉钉通知函数
send_ding_notification() {
    local content="$1"
    
    if [ "$SEND_NOTIFICATION" != "true" ]; then
        return 0
    fi
    
    local user_list_json=""
    
    # 构建用户列表JSON
    if [ ${#DING_USER_LIST[@]} -gt 0 ]; then
        user_list_json="["
        for i in "${!DING_USER_LIST[@]}"; do
            if [ $i -gt 0 ]; then
                user_list_json+=","
            fi
            user_list_json+="\"${DING_USER_LIST[$i]}\""
        done
        user_list_json+="]"
    else
        user_list_json="[]"
    fi
    
    # 发送POST请求
    local response=$(curl -s -X POST "$DING_API_URL" \
        -H "Content-Type: application/json" \
        -d "{\"content\":\"$content\",\"userList\":$user_list_json}" \
        --connect-timeout 10 \
        --max-time 30)
    
    if [ $? -eq 0 ]; then
        log "钉钉通知发送成功: $content"
    else
        log "钉钉通知发送失败: $content"
    fi
}

# 主要的日志清理函数
cleanup_logs() {
    log "开始执行日志清理任务，保留 $DAYS_TO_KEEP 天内的日志..."
    
    local cleaned_files=0
    local total_size_before=0
    local total_size_after=0
    
    # 计算清理前的总大小
    total_size_before=$(find /var/log -name "*.log" -o -name "*.out" -o -name "nohup.out" 2>/dev/null | xargs du -cb 2>/dev/null | tail -1 | cut -f1)
    
    # 清理系统日志目录
    log "清理 /var/log 目录下的日志文件..."
    cleaned_files=$((cleaned_files + $(find /var/log -name "*.log" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
    find /var/log -name "*.log" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    
    cleaned_files=$((cleaned_files + $(find /var/log -name "*.out" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
    find /var/log -name "*.out" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    
    cleaned_files=$((cleaned_files + $(find /var/log -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
    find /var/log -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    
    # 清理应用日志目录
    if [ -d "/opt/logs" ]; then
        log "清理 /opt/logs 目录下的日志文件..."
        cleaned_files=$((cleaned_files + $(find /opt/logs -name "*.log" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
        find /opt/logs -name "*.log" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    fi
    
    # 清理应用部署目录的日志
    if [ -d "/data/server" ]; then
        log "清理 /data/server 目录下的日志文件..."
        cleaned_files=$((cleaned_files + $(find /data/server -name "*.log" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
        find /data/server -name "*.log" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
        
        cleaned_files=$((cleaned_files + $(find /data/server -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
        find /data/server -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    fi
    
    # 清理当前目录下的日志文件
    log "清理当前目录下的日志文件..."
    cleaned_files=$((cleaned_files + $(find . -maxdepth 2 -name "*.log" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
    find . -maxdepth 2 -name "*.log" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    
    cleaned_files=$((cleaned_files + $(find . -maxdepth 2 -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -print | wc -l)))
    find . -maxdepth 2 -name "nohup.out" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \; 2>/dev/null
    
    # 计算清理后的总大小
    total_size_after=$(find /var/log -name "*.log" -o -name "*.out" -o -name "nohup.out" 2>/dev/null | xargs du -cb 2>/dev/null | tail -1 | cut -f1)
    
    # 计算释放的空间
    local freed_space=$((total_size_before - total_size_after))
    local freed_mb=$((freed_space / 1024 / 1024))
    
    # 更新清理标记文件
    echo "$(date +%s)" > "$LOG_CLEANUP_FLAG"
    
    log "日志清理任务完成"
    log "清理文件数量: $cleaned_files"
    log "释放磁盘空间: ${freed_mb}MB"
    
    # 发送通知
    local notification_content="【系统维护】日志清理任务完成
- 清理规则: 删除${DAYS_TO_KEEP}天前的日志文件
- 清理文件数量: ${cleaned_files}个
- 释放磁盘空间: ${freed_mb}MB
- 清理时间: $(date +'%Y-%m-%d %H:%M:%S')"
    
    send_ding_notification "$notification_content"
}

# 检查是否需要清理（基于时间间隔）
check_and_cleanup() {
    local current_time=$(date +%s)
    local cleanup_flag_time=0
    local force_cleanup="${3:-false}"
    
    # 检查上次清理时间
    if [ -f "$LOG_CLEANUP_FLAG" ]; then
        cleanup_flag_time=$(cat "$LOG_CLEANUP_FLAG" 2>/dev/null || echo 0)
    fi
    
    # 计算时间差（秒）
    local time_diff=$((current_time - cleanup_flag_time))
    local seven_days_seconds=$((7 * 24 * 3600))
    
    # 如果距离上次清理超过7天，或者强制清理，则执行清理
    if [ $time_diff -gt $seven_days_seconds ] || [ "$force_cleanup" = "true" ]; then
        cleanup_logs
    else
        local remaining_days=$(((seven_days_seconds - time_diff) / 86400))
        log "距离下次自动清理还有 $remaining_days 天"
    fi
}

# 主程序
main() {
    log "日志清理脚本启动"
    
    # 创建日志目录（如果不存在）
    mkdir -p /var/log
    
    # 检查参数
    if [ "$1" = "--force" ]; then
        log "强制执行日志清理"
        cleanup_logs
    elif [ "$1" = "--check" ]; then
        log "检查是否需要清理"
        check_and_cleanup "$DAYS_TO_KEEP" "$SEND_NOTIFICATION" "false"
    else
        log "执行日志清理（保留 $DAYS_TO_KEEP 天）"
        cleanup_logs
    fi
    
    log "日志清理脚本结束"
}

# 执行主程序
main "$@"
