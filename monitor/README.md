# 服务监控和日志清理系统

本系统提供了完整的服务监控、自动重启、日志清理和钉钉通知功能。

## 功能特性

### 1. 服务监控功能
- 自动监控配置的服务状态
- 服务异常时自动重启
- 支持自定义检查间隔
- 实时日志记录

### 2. 钉钉通知功能
- 服务启动/停止通知
- 服务重启成功/失败通知
- 日志清理完成通知
- 支持自定义通知用户列表

### 3. 日志清理功能
- 定期清理过期日志文件
- 支持多个目录的日志清理
- 统计清理文件数量和释放空间
- 可配置保留天数

## 文件说明

### 核心脚本
- `monitor-services.sh` - 主要的服务监控脚本
- `log-cleanup.sh` - 独立的日志清理脚本
- `setup-cron.sh` - 定时任务配置脚本
- `service-monitor.conf` - 服务监控配置文件

## 快速开始

### 1. 配置服务监控

编辑 `service-monitor.conf` 文件，配置需要监控的服务：

```bash
# 检查间隔（秒）
CHECK_INTERVAL=60

# 钉钉通知配置
DING_API_URL="http://127.0.0.1:9405/weCom/sendWarmDingMessage"
DING_USER_LIST=("admin" "ops" "monitor" "devops")

# 要监控的服务列表
SERVICES="GATEWAY AUTH SYSTEM"

# Gateway 服务配置
GATEWAY_NAME="nnb-gateway 服务"
GATEWAY_JAR_PATH="/data/server/crm/java/nnb-gateway.jar"
GATEWAY_JAVA_OPTS="-Xms512m -Xmx1024m"
GATEWAY_PROCESS_KEYWORD="nnb-gateway.jar"
```

### 2. 启动服务监控

```bash
# 启动监控脚本
./monitor-services.sh

# 或者指定配置文件
./monitor-services.sh ./service-monitor.conf
```

### 3. 设置定时任务

```bash
# 安装定时任务
./setup-cron.sh install

# 查看定时任务状态
./setup-cron.sh status

# 移除定时任务
./setup-cron.sh remove
```

### 4. 手动执行日志清理

```bash
# 执行日志清理（默认保留7天）
./log-cleanup.sh

# 保留指定天数的日志
./log-cleanup.sh 10

# 强制执行清理
./log-cleanup.sh --force

# 检查是否需要清理
./log-cleanup.sh --check
```

## 钉钉通知配置

### API接口
- **地址**: `http://127.0.0.1:9405/weCom/sendWarmDingMessage`
- **方法**: POST
- **参数**:
  - `content`: 告警内容（字符串）
  - `userList`: 发送人集合（List<String>类型）

### 通知场景
1. **服务监控脚本启动**: 脚本启动时发送通知
2. **服务停止**: 检测到服务停止时发送告警
3. **服务重启成功**: 服务自动重启成功后发送通知
4. **服务重启失败**: 服务重启失败时发送紧急告警
5. **日志清理完成**: 定期日志清理完成后发送统计信息

## 日志清理规则

### 清理目录
- `/var/log/` - 系统日志目录
- `/opt/logs/` - 应用日志目录
- `/data/server/` - 服务部署目录
- 当前目录及子目录

### 清理文件类型
- `*.log` - 所有日志文件
- `*.out` - 输出文件
- `nohup.out` - nohup输出文件

### 清理策略
- 默认保留7天内的日志
- 每7天自动执行一次清理
- 可手动指定保留天数
- 清理前统计文件数量和大小

## 定时任务说明

系统会自动配置以下定时任务：

```bash
# 每周日凌晨2点检查并清理日志
0 2 * * 0 /path/to/log-cleanup.sh --check

# 每5分钟检查监控脚本是否运行
*/5 * * * * pgrep -f 'monitor-services.sh' || nohup /path/to/monitor-services.sh &
```

## 故障排除

### 1. 服务监控不工作
- 检查配置文件是否正确
- 确认服务路径和关键字配置
- 查看监控日志：`tail -f /var/log/service-monitor.log`

### 2. 钉钉通知发送失败
- 检查网络连接
- 确认API地址是否正确
- 验证用户列表配置

### 3. 日志清理失败
- 检查目录权限
- 确认磁盘空间充足
- 查看清理日志：`tail -f /var/log/log-cleanup.log`

### 4. 定时任务不执行
- 检查cron服务状态：`systemctl status cron`
- 查看cron日志：`tail -f /var/log/cron`
- 确认脚本权限：`ls -la bin/*.sh`

## 日志文件位置

- 服务监控日志: `/var/log/service-monitor.log`
- 日志清理日志: `/var/log/log-cleanup.log`
- 服务输出日志: `{jar目录}/nohup.out`

## 注意事项

1. **权限要求**: 脚本需要有足够的权限访问服务目录和日志目录
2. **网络要求**: 钉钉通知需要能访问指定的API地址
3. **磁盘空间**: 确保有足够的磁盘空间进行日志清理操作
4. **服务配置**: 每个服务的配置必须完整且正确
5. **备份建议**: 重要日志建议在清理前进行备份

## 扩展配置

### 添加新服务监控
在 `service-monitor.conf` 中添加新服务配置：

```bash
# 添加到服务列表
SERVICES="GATEWAY AUTH SYSTEM NEW_SERVICE"

# 新服务配置
NEW_SERVICE_NAME="新服务名称"
NEW_SERVICE_JAR_PATH="/path/to/service.jar"
NEW_SERVICE_JAVA_OPTS="-Xms512m -Xmx1024m"
NEW_SERVICE_PROCESS_KEYWORD="service.jar"
```

### 自定义通知用户
修改配置文件中的用户列表：

```bash
DING_USER_LIST=("user1" "user2" "user3")
```

### 调整清理策略
修改日志保留天数和清理频率：

```bash
# 在cron中修改清理频率
# 每天凌晨执行：0 2 * * *
# 每3天执行：0 2 */3 * *
```
