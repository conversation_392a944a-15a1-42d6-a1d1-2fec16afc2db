package com.nnb.customer.service.impl.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Mapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-03-04
 * @Version: 1.0
 */
@Data
@Document(indexName = "clue_test_index")
public class BdClueIndexDO {

    @Id
    @Mapping(mappingPath = "/id.json")
    @Field(type = FieldType.Long, name = "id")
    private Long id;

    @Field(type = FieldType.Long, name = "follow_user_id")
    private Long followUserId;

    @Field(type = FieldType.Long, name = "customer_id")
    private Long customerId;

    @Field(type = FieldType.Keyword, name = "vc_customer_name")
    private String vcCustomerName;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @Field(type = FieldType.Date, name = "bd_clue_status_dat_follow_time",format = {},pattern = "yyyy-MM-dd'T'HH:mm:ss+08:00")
    private Date bdClueStatusDatFollowTime;

    @Field(type = FieldType.Nested, name = "bd_clue_contacts")
    @Mapping(mappingPath = "/bd_clue_contacts.json")
    private List<BdClueContact> bdClueContactList;
}
