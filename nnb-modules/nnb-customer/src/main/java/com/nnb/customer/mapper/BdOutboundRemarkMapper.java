package com.nnb.customer.mapper;

import java.util.List;
import com.nnb.customer.domain.BdOutboundRemark;
import com.nnb.customer.model.BdOutboundRemarkVo;

/**
 * 外呼记录备注Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface BdOutboundRemarkMapper 
{
    /**
     * 查询外呼记录备注
     * 
     * @param id 外呼记录备注主键
     * @return 外呼记录备注
     */
    public BdOutboundRemark selectBdOutboundRemarkById(Long id);

    /**
     * 查询外呼记录备注列表
     * 
     * @param bdOutboundRemark 外呼记录备注
     * @return 外呼记录备注集合
     */
    public List<BdOutboundRemark> selectBdOutboundRemarkList(BdOutboundRemark bdOutboundRemark);

    /**
     * 新增外呼记录备注
     * 
     * @param bdOutboundRemark 外呼记录备注
     * @return 结果
     */
    public int insertBdOutboundRemark(BdOutboundRemark bdOutboundRemark);

    /**
     * 修改外呼记录备注
     * 
     * @param bdOutboundRemark 外呼记录备注
     * @return 结果
     */
    public int updateBdOutboundRemark(BdOutboundRemark bdOutboundRemark);

    /**
     * 删除外呼记录备注
     * 
     * @param id 外呼记录备注主键
     * @return 结果
     */
    public int deleteBdOutboundRemarkById(Long id);

    /**
     * 批量删除外呼记录备注
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdOutboundRemarkByIds(Long[] ids);

    /**
     * 查询外呼记录备注列表
     *
     * @param bdOutboundRemark 外呼记录备注
     * @return 外呼记录备注集合
     */
    public List<BdOutboundRemarkVo> selectBdOutboundRemarkVoList(BdOutboundRemark bdOutboundRemark);
}
