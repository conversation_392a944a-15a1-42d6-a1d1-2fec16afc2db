package com.nnb.customer.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2022-09-13
 * @Version: 1.0
 */
@Slf4j
public class ChinaUmsUtil {

    /**
     * open-body-sig方式获取到Authorization 的值
     *
     * @param appId
     * @param appKey
     * @param body
     * @return
     * @throws Exception
     */
    public static String getOpenBodySig(String appId, String appKey, String body) {
        try {
            // eg:20190227113148
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            // eg:be46cd581c9f46ecbd71b9858311ea12
            String nonce = UUID.randomUUID().toString().replace("-", "");
            byte[] data = body.getBytes("UTF-8");
            InputStream is = new ByteArrayInputStream(data);
            // eg:d60bc3aedeb853e2a11c0c096baaf19954dd9b752e48dea8e919e5fb29a42a8d
            String bodyDigest = testSHA256(is);
            // eg:f0ec96ad2c3848b5b810e7aadf369e2f + 20190227113148 + be46cd581c9f46ecbd71b9858311ea12 + d60bc3aedeb853e2a11c0c096baaf19954dd9b752e48dea8e919e5fb29a42a8d
            String str1_C = appId + timestamp + nonce + bodyDigest;
            byte[] localSignature = hmacSHA256(str1_C.getBytes(), appKey.getBytes());
            // Signature
            String localSignatureStr = Base64.encodeBase64String(localSignature);
            return ("OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"");
        } catch (Exception e) {
            log.error("open-body-sig方式获取到Authorization 的值失败", e);
        }
        return null;
    }


    /**
     * 进行加密
     *
     * @param is
     * @return 加密后的结果
     */
    private static String testSHA256(InputStream is) {
        try {
            return DigestUtils.sha256Hex(is);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param data
     * @param key
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static byte[] hmacSHA256(byte[] data, byte[] key) {
        try {
            String algorithm = "HmacSHA256";
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(key, algorithm));
            return mac.doFinal(data);
        } catch (Exception e) {
            log.error("open-body-sig方式获取到hmacSHA256失败", e);
        }
        return null;
    }
}
