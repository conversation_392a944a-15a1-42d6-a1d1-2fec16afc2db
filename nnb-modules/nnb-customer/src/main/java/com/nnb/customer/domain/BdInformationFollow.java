package com.nnb.customer.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel(value="BdInformationFollow",description="资料上传日志")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class BdInformationFollow {
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;

    /** 线索id */
    @Excel(name = "线索id")
    @ApiModelProperty("线索id")
    private Long clueId;

    /** 领取人id */
    @Excel(name = "领取人id")
    @ApiModelProperty("领取人id")
    private Long userId;

    /** 领取人姓名 */
    @Excel(name = "领取人姓名")
    @ApiModelProperty("领取人姓名")
    private String userName;

    /** 领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领取时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("跟进时间")
    private Date CreatedTime;

    /** 线索节点 1 外呼管理，2 线索管理，3客保管理，4线索精选，5呼叫中心 */
    @Excel(name = "线索节点 1 外呼管理，2 线索管理，3客保管理，4线索精选，5呼叫中心")
    @ApiModelProperty("线索节点 1 外呼管理，2 线索管理，3客保管理，4线索精选，5呼叫中心")
    private Long numType;


}
