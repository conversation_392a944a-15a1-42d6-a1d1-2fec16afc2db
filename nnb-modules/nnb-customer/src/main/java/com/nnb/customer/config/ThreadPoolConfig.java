package com.nnb.customer.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * @Author: Chen-xy
 * @Description: 自定义线程池
 * @Date: 2022-09-03
 * @Version: 1.0
 */
//@Configuration
public class ThreadPoolConfig {

    // 线程缓冲队列
    private static final BlockingQueue<Runnable> blockingQueue = new LinkedBlockingDeque<>(2000);
    // 核心线程数，会一直存活，即使没有任务，线程池也会维护线程的最少数量
    private static final int SIZE_CORE_POOL = 20;
    // 线程池维护线程的最大数量
    private static final int SIZE_MAX_POOL = 100;
    // 线程池维护线程所允许的空闲时间
    private static final long ALIVE_TIME = 10;
    // 拒绝策略：当有任务添加到线程池被拒绝时，线程池会将被拒绝的任务添加到"线程池正在运行的线程"中去运行。
    private static final RejectedExecutionHandler rejectHandler = new ThreadPoolExecutor.CallerRunsPolicy();

    @Bean
    public ThreadPoolExecutor threadPoolExecutor(){
        return new ThreadPoolExecutor(SIZE_CORE_POOL, SIZE_MAX_POOL, ALIVE_TIME, TimeUnit.SECONDS, blockingQueue, rejectHandler);
    }
}
