package com.nnb.customer.mapper;

import java.util.List;
import java.util.Map;

import com.nnb.customer.domain.BdConsult;
import com.nnb.customer.domain.BdNeedProduct;
import com.nnb.customer.domain.es.ESBdNeedProduct;
import com.nnb.customer.domain.vo.clue.BdNeedProductVO;
import org.apache.ibatis.annotations.Param;

/**
 * 线索意向产品关系Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface BdNeedProductMapper
{
    /**
     * 查询线索意向产品关系
     *
     * @param id 线索意向产品关系主键
     * @return 线索意向产品关系
     */
    public BdNeedProduct selectBdNeedProductById(Long id);

    /**
     * 查询线索意向产品关系列表
     *
     * @param bdNeedProduct 线索意向产品关系
     * @return 线索意向产品关系集合
     */
    public List<BdNeedProduct> selectBdNeedProductList(BdNeedProduct bdNeedProduct);

    /**
     * 新增线索意向产品关系
     *
     * @param bdNeedProduct 线索意向产品关系
     * @return 结果
     */
    public int insertBdNeedProduct(BdNeedProduct bdNeedProduct);

    /**
     * 修改线索意向产品关系
     *
     * @param bdNeedProduct 线索意向产品关系
     * @return 结果
     */
    public int updateBdNeedProduct(BdNeedProduct bdNeedProduct);

    /**
     * 删除线索意向产品关系
     *
     * @param id 线索意向产品关系主键
     * @return 结果
     */
    public int deleteBdNeedProductById(Long id);

    /**
     * 批量删除线索意向产品关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdNeedProductByIds(Long[] ids);

    /**
     * 删除线索意向产品关系(byClueId)
     *
     * @param clueId 线索意向产品
     * @return 结果
     */
    public int deleteBdNeedProductByClueId(Long clueId);

    public BdNeedProductVO getProductByClueId(Long clueId);
    public List<BdNeedProductVO> getProductByClueIdList(@Param("clueIdList") List<Long> clueIdList);

    public List<BdNeedProduct> selectBdNeedProductByConfigurationsId(Long configurationsId);

    public List<BdNeedProduct> selectBdNeedProductListGroupByClueIds(String ids);

    List<ESBdNeedProduct> selectByClueIdListEs(@Param("bdClueIdList") List<Long> bdClueIdList);
}
