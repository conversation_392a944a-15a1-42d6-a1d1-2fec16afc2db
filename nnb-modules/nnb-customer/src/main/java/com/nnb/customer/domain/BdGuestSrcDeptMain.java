package com.nnb.customer.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 线索来源可见部门配置对象 bd_guest_src_dept_main
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="BdGuestSrcDeptMain",description="线索来源可见部门配置对象")
public class BdGuestSrcDeptMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 状态 1 有效 0 无效 */
    @Excel(name = "状态 1 有效 0 无效")
    @ApiModelProperty("状态 1 有效 0 无效")
    private Integer numStatus;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long numUpdatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNumStatus(Integer numStatus)
    {
        this.numStatus = numStatus;
    }

    public Integer getNumStatus()
    {
        return numStatus;
    }
    public void setNumCreatedBy(Long numCreatedBy) 
    {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() 
    {
        return numCreatedBy;
    }
    public void setNumUpdatedBy(Long numUpdatedBy) 
    {
        this.numUpdatedBy = numUpdatedBy;
    }

    public Long getNumUpdatedBy() 
    {
        return numUpdatedBy;
    }
    public void setDatCreatedAt(Date datCreatedAt) 
    {
        this.datCreatedAt = datCreatedAt;
    }

    public Date getDatCreatedAt() 
    {
        return datCreatedAt;
    }
    public void setDatUpdatedAt(Date datUpdatedAt) 
    {
        this.datUpdatedAt = datUpdatedAt;
    }

    public Date getDatUpdatedAt() 
    {
        return datUpdatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("numStatus", getNumStatus())
            .append("numCreatedBy", getNumCreatedBy())
            .append("numUpdatedBy", getNumUpdatedBy())
            .append("datCreatedAt", getDatCreatedAt())
            .append("datUpdatedAt", getDatUpdatedAt())
            .toString();
    }
}
