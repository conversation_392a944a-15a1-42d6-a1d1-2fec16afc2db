package com.nnb.customer.service;

import java.util.List;

import com.nnb.customer.domain.BdClueStatus;

/**
 * 线索状态相关数据Service接口
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface IBdClueStatusService {
    /**
     * 查询线索状态相关数据
     *
     * @param id 线索状态相关数据主键
     * @return 线索状态相关数据
     */
    public BdClueStatus selectBdClueStatusById(Long id);

    /**
     * 查询线索状态相关数据列表
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 线索状态相关数据集合
     */
    public List<BdClueStatus> selectBdClueStatusList(BdClueStatus bdClueStatus);

    /**
     * 新增线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int insertBdClueStatus(BdClueStatus bdClueStatus);

    /**
     * 修改线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int updateBdClueStatus(BdClueStatus bdClueStatus);

    /**
     * 批量删除线索状态相关数据
     *
     * @param ids 需要删除的线索状态相关数据主键集合
     * @return 结果
     */
    public int deleteBdClueStatusByIds(Long[] ids);

    /**
     * 删除线索状态相关数据信息
     *
     * @param id 线索状态相关数据主键
     * @return 结果
     */
    public int deleteBdClueStatusById(Long id);

    /**
     * 根据线索id获取信息
     *
     * @param clueId 线索Id
     * @return 结果
     */
    public BdClueStatus selectInfoByClueId(Long clueId);
}
