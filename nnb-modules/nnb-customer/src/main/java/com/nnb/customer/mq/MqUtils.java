package com.nnb.customer.mq;

import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Mq操作工具类
 */
public class MqUtils {


    @Autowired
    private static AmqpTemplate amqpTemplate;


    /**
     * 已topic的形式发送Mq消息
     * @param queueName
     * @param mess
     */
    public static void send(String queueName, String mess) {
        amqpTemplate.convertAndSend("topic", queueName, mess);
    }
}
