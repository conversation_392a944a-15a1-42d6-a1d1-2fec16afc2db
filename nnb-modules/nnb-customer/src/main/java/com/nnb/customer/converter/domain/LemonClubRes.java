package com.nnb.customer.converter.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: Chen-xy
 * @Description: 柠檬会支付相应实体
 * @Date: 2024-03-06
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class LemonClubRes {

    //内部交易流水号
    private String tradeId;
    //交易链接地址
    private String paymentUrl;
}
