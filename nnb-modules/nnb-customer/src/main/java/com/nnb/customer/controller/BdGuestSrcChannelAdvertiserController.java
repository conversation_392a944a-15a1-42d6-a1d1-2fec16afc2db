package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.customer.domain.clue.BdGuestSrcChannelAdvertiser;
import com.nnb.customer.service.IBdGuestSrcChannelAdvertiserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 北斗线索来源渠道账户关系Controller
 * 
 * <AUTHOR>
 * @date 2023-12-11
 */
@RestController
@RequestMapping("/channeladvertiser")
@Api(tags = "BdGuestSrcChannelAdvertiserController", description = "北斗线索来源渠道账户关系")
public class BdGuestSrcChannelAdvertiserController extends BaseController
{
    @Autowired
    private IBdGuestSrcChannelAdvertiserService bdGuestSrcChannelAdvertiserService;

    /**
     * 查询北斗线索来源渠道账户关系列表
     */
    @ApiOperation(value = "查询北斗线索来源渠道账户关系列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdGuestSrcChannelAdvertiser.class)})
    @PreAuthorize(hasPermi = "erp:advertiser:list")
    @GetMapping("/list")
    public TableDataInfo list(BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser)
    {
        startPage();
        List<BdGuestSrcChannelAdvertiser> list = bdGuestSrcChannelAdvertiserService.selectBdGuestSrcChannelAdvertiserList(bdGuestSrcChannelAdvertiser);
        return getDataTable(list);
    }

    /**
     * 导出北斗线索来源渠道账户关系列表
     */
    @ApiOperation(value = "导出北斗线索来源渠道账户关系列表")
    @PreAuthorize(hasPermi = "erp:advertiser:export")
    //@Log(title = "北斗线索来源渠道账户关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser) throws IOException
    {
        List<BdGuestSrcChannelAdvertiser> list = bdGuestSrcChannelAdvertiserService.selectBdGuestSrcChannelAdvertiserList(bdGuestSrcChannelAdvertiser);
        ExcelUtil<BdGuestSrcChannelAdvertiser> util = new ExcelUtil<BdGuestSrcChannelAdvertiser>(BdGuestSrcChannelAdvertiser.class);
        util.exportExcel(response, list, "北斗线索来源渠道账户关系数据");
    }

    /**
     * 获取北斗线索来源渠道账户关系详细信息
     */
    @ApiOperation(value = "获取北斗线索来源渠道账户关系详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdGuestSrcChannelAdvertiser.class)})
    @PreAuthorize(hasPermi = "erp:advertiser:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="北斗线索来源渠道账户关系id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdGuestSrcChannelAdvertiserService.selectBdGuestSrcChannelAdvertiserById(id));
    }

    /**
     * 新增北斗线索来源渠道账户关系
     */
    @ApiOperation(value = "新增北斗线索来源渠道账户关系")
    @PreAuthorize(hasPermi = "erp:advertiser:add")
    //@Log(title = "北斗线索来源渠道账户关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser)
    {
        return toAjax(bdGuestSrcChannelAdvertiserService.insertBdGuestSrcChannelAdvertiser(bdGuestSrcChannelAdvertiser));
    }

    /**
     * 修改北斗线索来源渠道账户关系
     */
    @ApiOperation(value = "修改北斗线索来源渠道账户关系")
    @PreAuthorize(hasPermi = "erp:advertiser:edit")
    //@Log(title = "北斗线索来源渠道账户关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser)
    {
        return toAjax(bdGuestSrcChannelAdvertiserService.updateBdGuestSrcChannelAdvertiser(bdGuestSrcChannelAdvertiser));
    }

    /**
     * 删除北斗线索来源渠道账户关系
     */
    @ApiOperation(value = "删除北斗线索来源渠道账户关系")
    @PreAuthorize(hasPermi = "erp:advertiser:remove")
    //@Log(title = "北斗线索来源渠道账户关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdGuestSrcChannelAdvertiserService.deleteBdGuestSrcChannelAdvertiserByIds(ids));
    }
}
