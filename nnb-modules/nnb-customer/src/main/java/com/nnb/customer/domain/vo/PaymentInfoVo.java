package com.nnb.customer.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: Chen-xy
 * @Description: PaymentInfo的vo
 * @Date: 2022-09-07
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentInfoVo implements Serializable {

    @ApiModelProperty("主键id")
    private String id;

    @Excel(name = "唯一交易流水号")
    @ApiModelProperty("唯一交易流水号")
    private String billNo;

    @ApiModelProperty("订单Id")
    private String orderId;

    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String orderNumber;

    @ApiModelProperty("当前登录者Id")
    private Integer userId;

    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private String userName;

    @Excel(name = "部门")
    @ApiModelProperty("部门")
    private String deptName;

    @Excel(name = "线索Id")
    @ApiModelProperty("线索Id")
    private Integer clueId;

    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private BigDecimal fee;

    @ApiModelProperty("支付公司主体id")
    private Integer payCompanyId;

    @Excel(name = "支付公司主体")
    @ApiModelProperty("支付公司主体")
    private String payCompanyName;

    @ApiModelProperty("二维码链接")
    private String payUrl;

    @ApiModelProperty("二维码图片")
    private String payImg;

    @ApiModelProperty("支付方式")
    private Integer orderType;

    @ApiModelProperty("支付有效期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payValidTime;

    @Excel(name = "创建时间", width = 30, localDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Excel(name = "支付状态", readConverterExp = "1=已支付,0=未支付")
    @ApiModelProperty("支付状态")
    private Integer payStatus;

    @Excel(name = "支付时间", width = 30, localDateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("支付时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @Excel(name = "交易记录id")
    @ApiModelProperty("交易id，")
    private String tradeId;

    @ApiModelProperty("次数")
    private int num;

    @ApiModelProperty("二维码id")
    private String qrCodeId;

    @Excel(name = "客户id")
    @ApiModelProperty("客户id")
    private String clientId;

    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("商户号")
    private String mid;

    @ApiModelProperty("终端号")
    private String tid;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("回调返回json")
    private String callBackJson;

    @ApiModelProperty("是否为复制的交易记录")
    private Integer isCopy;

    @ApiModelProperty("更新人")
    private Long updatedUser;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty("收银台id")
    private String cashierId;

    @ApiModelProperty("收款部门id")
    private Long deptId;

    @ApiModelProperty("是否报单：0未报单、1已报单")
    private Integer reportedOrNot;

    @ApiModelProperty("收款方式：0二维码、1对公转账、2现金")
    private Integer paymentType;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("开户行")
    private String openingBank;

    @ApiModelProperty("账号")
    private String bankAccount;

    @ApiModelProperty("现金单据编号")
    private String documentNumber;

    @ApiModelProperty("0：crm，1：柠檬会")
    private Integer sourceType;

    @ApiModelProperty("柠檬会userId")
    private Long lemonUserId;

    private String reportedOrNotStr;
    private String paymentTypeStr;

}
