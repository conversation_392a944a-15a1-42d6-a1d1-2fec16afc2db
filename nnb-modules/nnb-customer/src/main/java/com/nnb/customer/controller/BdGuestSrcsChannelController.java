package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdGuestSrcsChannel;
import com.nnb.customer.service.IBdGuestSrcsChannelService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 获客来源媒体账号配置Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdGuestSrcsChannel")
@Api(tags = "BdGuestSrcsChannelController", description = "获客来源媒体账号配置")
public class BdGuestSrcsChannelController extends BaseController
{
    @Autowired
    private IBdGuestSrcsChannelService bdGuestSrcsChannelService;

    /**
     * 查询获客来源媒体账号配置列表
     */
    @ApiOperation(value = "查询获客来源媒体账号配置列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdGuestSrcsChannel.class)})
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:list")
    @GetMapping("/list")
    public TableDataInfo list(BdGuestSrcsChannel bdGuestSrcsChannel)
    {
        startPage();
        List<BdGuestSrcsChannel> list = bdGuestSrcsChannelService.selectBdGuestSrcsChannelList(bdGuestSrcsChannel);
        return getDataTable(list);
    }

    /**
     * 导出获客来源媒体账号配置列表
     */
    @ApiOperation(value = "导出获客来源媒体账号配置列表")
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:export")
    //@Log(title = "获客来源媒体账号配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdGuestSrcsChannel bdGuestSrcsChannel) throws IOException
    {
        List<BdGuestSrcsChannel> list = bdGuestSrcsChannelService.selectBdGuestSrcsChannelList(bdGuestSrcsChannel);
        ExcelUtil<BdGuestSrcsChannel> util = new ExcelUtil<BdGuestSrcsChannel>(BdGuestSrcsChannel.class);
        util.exportExcel(response, list, "获客来源媒体账号配置数据");
    }

    /**
     * 获取获客来源媒体账号配置详细信息
     */
    @ApiOperation(value = "获取获客来源媒体账号配置详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdGuestSrcsChannel.class)})
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="获客来源媒体账号配置id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdGuestSrcsChannelService.selectBdGuestSrcsChannelById(id));
    }

    /**
     * 新增获客来源媒体账号配置
     */
    @ApiOperation(value = "新增获客来源媒体账号配置")
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:add")
    //@Log(title = "获客来源媒体账号配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdGuestSrcsChannel bdGuestSrcsChannel)
    {
        return toAjax(bdGuestSrcsChannelService.insertBdGuestSrcsChannel(bdGuestSrcsChannel));
    }

    /**
     * 修改获客来源媒体账号配置
     */
    @ApiOperation(value = "修改获客来源媒体账号配置")
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:edit")
    //@Log(title = "获客来源媒体账号配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdGuestSrcsChannel bdGuestSrcsChannel)
    {
        return toAjax(bdGuestSrcsChannelService.updateBdGuestSrcsChannel(bdGuestSrcsChannel));
    }

    /**
     * 删除获客来源媒体账号配置
     */
    @ApiOperation(value = "删除获客来源媒体账号配置")
    @PreAuthorize(hasPermi = "customer:BdGuestSrcsChannel:remove")
    //@Log(title = "获客来源媒体账号配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdGuestSrcsChannelService.deleteBdGuestSrcsChannelByIds(ids));
    }
}
