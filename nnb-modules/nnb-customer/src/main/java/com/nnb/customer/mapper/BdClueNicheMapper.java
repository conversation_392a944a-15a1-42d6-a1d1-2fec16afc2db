package com.nnb.customer.mapper;

import com.nnb.customer.domain.AdClueSouce;
import com.nnb.customer.domain.BdClue;
import com.nnb.customer.domain.BdInformationFollow;
import com.nnb.customer.domain.nichepool.*;
import com.nnb.customer.domain.vo.clue.BdClueDetailVo;
import com.nnb.customer.model.BdAutoAssignVo;
import com.nnb.customer.model.BdClueScrs;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 北斗线索Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface BdClueNicheMapper {

    List<NichePoolVo> getNichePool(@Param("nichePoolSecondParams") List<NichePoolSecondParam> nichePoolSecondParams, @Param("filterFirst") Integer filterFirst, @Param("enterpriseDominant") Integer enterpriseDominant);

    List<BdClueNicheVo> getBdClueNicheVo(@Param("bdClueNicheParam") BdClueNicheParam bdClueNicheParam, @Param("nichePoolSecondParams") List<NichePoolSecondParam> nichePoolSecondParams, @Param("filterFirst") Integer filterFirst);

    Long getBdClueNicheVoCount(@Param("bdClueNicheParam") BdClueNicheParam bdClueNicheParam, @Param("nichePoolSecondParams") List<NichePoolSecondParam> nichePoolSecondParams, @Param("filterFirst") Integer filterFirst);

    /**
     *
     * @param nichePoolSecondParams
     * @param filterFirst
     * @param statisticsType 1:已成交；2：剩余可拨打数
     * @param userCityId
     * @return
     */
    Long getNichePoolCount(@Param("nichePoolSecondParams") List<NichePoolSecondParam> nichePoolSecondParams, @Param("filterFirst") Integer filterFirst, @Param("statisticsType") Integer statisticsType, @Param("userCityId") Long userCityId, @Param("enterpriseDominant") Integer enterpriseDominant);

    List<NichePoolClueVo> getNichePoolClueVo(Long nicheFlowConfId);


}
