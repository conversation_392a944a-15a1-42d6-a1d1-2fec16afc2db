package com.nnb.customer.domain.popularizeclue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("推广线索渠道看板")
public class PopularizeClueChannelBoardVo {

    private Long id;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("现金费用")
    private BigDecimal channelCost;

    @ApiModelProperty("线索成本")
    private BigDecimal clueCost;

    @ApiModelProperty("线索量")
    private BigDecimal clueCount;

    @ApiModelProperty("成交量")
    private BigDecimal turnover;

    @ApiModelProperty("成交率")
    private String closeRate;

    @ApiModelProperty("客单价")
    private BigDecimal perCustomerTransaction;

    @ApiModelProperty("成交额")
    private BigDecimal turnoverAmount;

    @ApiModelProperty("ROI")
    private BigDecimal ROI;

    @ApiModelProperty("成交成本")
    private BigDecimal turnoverCost;

    @ApiModelProperty("当期成交量")
    private Integer  turnoverNowCount;

    @ApiModelProperty("当期成交率")
    private BigDecimal closeNowRate;

    @ApiModelProperty("当期成交率")
    private String closeNowRateStr;

    @ApiModelProperty("当期成交额")
    private BigDecimal turnoverNowAmount;

    @ApiModelProperty("当期ROI")
    private BigDecimal nowROI;

    @ApiModelProperty("当期成交量比")
    private BigDecimal  turnoverNowCountRate;




}
