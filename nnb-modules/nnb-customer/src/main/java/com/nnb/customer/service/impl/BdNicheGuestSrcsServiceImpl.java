package com.nnb.customer.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdNicheGuestSrcsMapper;
import com.nnb.customer.domain.BdNicheGuestSrcs;
import com.nnb.customer.service.IBdNicheGuestSrcsService;

/**
 * 商机线索来源关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-09
 */
@Service
public class BdNicheGuestSrcsServiceImpl implements IBdNicheGuestSrcsService 
{
    @Autowired
    private BdNicheGuestSrcsMapper bdNicheGuestSrcsMapper;

    /**
     * 查询商机线索来源关系
     * 
     * @param numNicheId 商机线索来源关系主键
     * @return 商机线索来源关系
     */
    @Override
    public BdNicheGuestSrcs selectBdNicheGuestSrcsByNumNicheId(Long numNicheId)
    {
        return bdNicheGuestSrcsMapper.selectBdNicheGuestSrcsByNumNicheId(numNicheId);
    }

    /**
     * 查询商机线索来源关系列表
     * 
     * @param bdNicheGuestSrcs 商机线索来源关系
     * @return 商机线索来源关系
     */
    @Override
    public List<BdNicheGuestSrcs> selectBdNicheGuestSrcsList(BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        return bdNicheGuestSrcsMapper.selectBdNicheGuestSrcsList(bdNicheGuestSrcs);
    }

    /**
     * 新增商机线索来源关系
     * 
     * @param bdNicheGuestSrcs 商机线索来源关系
     * @return 结果
     */
    @Override
    public int insertBdNicheGuestSrcs(BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        return bdNicheGuestSrcsMapper.insertBdNicheGuestSrcs(bdNicheGuestSrcs);
    }

    /**
     * 修改商机线索来源关系
     * 
     * @param bdNicheGuestSrcs 商机线索来源关系
     * @return 结果
     */
    @Override
    public int updateBdNicheGuestSrcs(BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        return bdNicheGuestSrcsMapper.updateBdNicheGuestSrcs(bdNicheGuestSrcs);
    }

    /**
     * 批量删除商机线索来源关系
     * 
     * @param numNicheIds 需要删除的商机线索来源关系主键
     * @return 结果
     */
    @Override
    public int deleteBdNicheGuestSrcsByNumNicheIds(Long[] numNicheIds)
    {
        return bdNicheGuestSrcsMapper.deleteBdNicheGuestSrcsByNumNicheIds(numNicheIds);
    }

    /**
     * 删除商机线索来源关系信息
     * 
     * @param numNicheId 商机线索来源关系主键
     * @return 结果
     */
    @Override
    public int deleteBdNicheGuestSrcsByNumNicheId(Long numNicheId)
    {
        return bdNicheGuestSrcsMapper.deleteBdNicheGuestSrcsByNumNicheId(numNicheId);
    }
}
