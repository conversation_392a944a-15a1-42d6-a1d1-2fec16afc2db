package com.nnb.customer.domain.es;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-03-11
 * @Version: 1.0
 */
@Data
public class ESBdClue implements Serializable {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("跟进人id")
    private Long followUserId;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String vcCustomerName;

    @ApiModelProperty("公司名称")
    private String vcCompanyName;

    @ApiModelProperty("所属城市")
    private Long cityId;

    @ApiModelProperty("联系方式")
    private String vcPhone;

    @ApiModelProperty("手机号归属地id")
    private Long locationId;

    @ApiModelProperty("qq号")
    private String vcQq;

    @ApiModelProperty("微信号")
    private String vcWeixin;

    @ApiModelProperty("抖音号")
    private String vcDy;

    @ApiModelProperty("状态1私海(客保中心)2公海3黑名单（删除-合并下沉池）4,已成交 9,线索中心 10 已成交客户解绑（待定）12. 商机移入公海和黑名单以后，同一条数据又新建时的状态 99 外来线索  13.其他  14线索管理15.呼叫中心 16.商机池 17.下沉池 18.合并上海重复线索 19.推广公海")
    private Integer numStatus;

    @ApiModelProperty("线索来源")
    private Long guestSrcsId;

    @ApiModelProperty("线索来源类型：1.销售录入 2,投放线索  3.微信支付创建  4.螳螂系统订单创建 5.小程序起名7.推广线索")
    private Integer numTag;

    @ApiModelProperty("跟进状态 1.移交待分配、2.待领取、3.未跟进、4.跟进中（填写跟进记录即为跟进中）、5.已成交、6.已入公海7.客保中,8. 首次未分配,9 跟进未转化,10 已掉保 11已放弃")
    private Integer numGjStatus;

    @ApiModelProperty("第三方订单号")
    private String vcOtherOrder;

    @ApiModelProperty("销售录入方式:1.手动 2.批量,3.资料上传")
    private Integer numWhereGet;

    @ApiModelProperty("地址")
    private String vcAddress;

    @ApiModelProperty("备注")
    private String vcRemark;

    @ApiModelProperty("节点时间,(最后一次操作时间)")
    private String datNodeTime;

    @ApiModelProperty("创建人")
    private Long numCreateUserid;

    @ApiModelProperty("最后修改人")
    private Long numLastUpdUserid;

    @ApiModelProperty("创建时间")
    private String datCreateTime;

    @ApiModelProperty("最后修改时间")
    private String datLastUpd;

    @ApiModelProperty("前所属人id")
    private Long numBeforeFollowUserId;

    @ApiModelProperty("crm_customers_main.id")
    private Long crmIdOld;

    @ApiModelProperty("客户需求描述; 线索咨询内容")
    private String descriptionOld;

    @ApiModelProperty("拉黑时间")
    private String blackInOld;

    @ApiModelProperty("财税线索类型，1员工转介绍，2公司老客户，3客户转介绍")
    private Integer csTypeOld;

    @ApiModelProperty("介绍人/介绍企业id  cs_type=1:user.id; 2,3:customer.id")
    private Long introducerOld;

    @ApiModelProperty("后端线索类型，用来自动流转，caishuiConfig.id")
    private Integer backEndTypeOld;

    @ApiModelProperty("推广获客的url")
    private String urlOld;

    @ApiModelProperty("opportunitys.id")
    private Long opportunitysIdOld;

    @ApiModelProperty("淘宝订单号")
    private String tbOrderOld;

    @ApiModelProperty("客户ID")
    private String clientIdOld;

    @ApiModelProperty("呼叫中心是否重置字段信息")
    private Integer callCenterResetOld;

    @ApiModelProperty("guest_srcs_id_new首次标记时间")
    private String guestSrcsIdNewTimeOld;

    @ApiModelProperty("线索所属行业id：crm_user_trade表对应id")
    private Long crmUserTradeIdOld;

    @ApiModelProperty("启照多线索区分:  1：北斗线索, 2:启照多线索")
    private Integer crmEnterpriseId;

    @ApiModelProperty("推广线索ID")
    private String popularizeClueId;

    @ApiModelProperty("推广线索来源ID")
    private Long popularizeClueSourceId;

    @ApiModelProperty("企业主体 1：北京小苗, 2：上海企苗, 3：后企之秀")
    private Integer enterpriseDominant;

    @ApiModelProperty("首咨分数：0、10、20、40、60")
    private Integer firstConsultScore;

    @ApiModelProperty("线索报名金额")
    private BigDecimal registrationAmount;

    @ApiModelProperty("线索是否已申请 0：否，1：是")
    private Integer isApply;

    @ApiModelProperty("最近接通时间")
    private String lastConnectedTime;

    @ApiModelProperty("是否跟进 0：否，1：是")
    private Integer isFollow;

    @ApiModelProperty("线索是否加入过商机池 0：否，1：是")
    private Integer isIntoNichepool;

    @ApiModelProperty("是否手动批量添加线索 0：否，1：是")
    private Integer isManualAdd;
}
