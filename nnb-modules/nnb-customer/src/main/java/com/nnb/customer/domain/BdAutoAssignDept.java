package com.nnb.customer.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 线索自动分配流转-部门关系对象 bd_auto_assign_dept
 * 
 * <AUTHOR>
 * @date 2022-08-02
 */
@ApiModel(value="BdAutoAssignDept",description="线索自动分配流转-部门关系对象")
public class BdAutoAssignDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 线索分配流转配置id */
    @Excel(name = "线索分配流转配置id")
    @ApiModelProperty("线索分配流转配置id")
    private Long autoAssignId;

    /** 部门id */
    @Excel(name = "部门id")
    @ApiModelProperty("部门id")
    private Long deptId;

    public void setAutoAssignId(Long autoAssignId) 
    {
        this.autoAssignId = autoAssignId;
    }

    public Long getAutoAssignId() 
    {
        return autoAssignId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("autoAssignId", getAutoAssignId())
            .append("deptId", getDeptId())
            .toString();
    }
}
