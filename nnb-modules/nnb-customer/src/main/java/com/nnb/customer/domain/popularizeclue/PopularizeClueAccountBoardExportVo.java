package com.nnb.customer.domain.popularizeclue;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("推广线索账户看板")
public class PopularizeClueAccountBoardExportVo {

    private Long id;

    @ApiModelProperty("账户ID")
    private String advertiserId;

    @Excel(name = "渠道名称")
    @ApiModelProperty("渠道名称")
    private String channelName;

    @Excel(name = "账户")
    @ApiModelProperty("账户")
    private String advertiserName;

    @Excel(name = "展示数")
    @ApiModelProperty("展示数")
    private BigDecimal showCount;

    @Excel(name = "点击数")
    @ApiModelProperty("点击数")
    private BigDecimal clickCount;

    @Excel(name = "点击率")
    @ApiModelProperty("点击率")
    private BigDecimal clickRate;

    @Excel(name = "转化数")
    @ApiModelProperty("转化数")
    private BigDecimal convert;

    @Excel(name = "页面转化率")
    @ApiModelProperty("页面转化率")
    private BigDecimal convertRate;

    @Excel(name = "重复名单")
    @ApiModelProperty("重复名单")
    private BigDecimal repeatCount;

    @Excel(name = "重复率")
    @ApiModelProperty("重复率")
    private BigDecimal repeatRate;

    @Excel(name = "接通线索量")
    @ApiModelProperty("接通线索量")
    private BigDecimal connectClueCount;

    @Excel(name = "接通线索率")
    @ApiModelProperty("接通线索率")
    private BigDecimal connectClueRate;

    @Excel(name = "有效线索数")
    @ApiModelProperty("有效线索数")
    private BigDecimal effectiveClueCount;

    @Excel(name = "有效率")
    @ApiModelProperty("有效率")
    private BigDecimal effectiveClueRate;

    @Excel(name = "总消费")
    @ApiModelProperty("总消费")
    private BigDecimal channelCost;

    @Excel(name = "进系统总名单")
    @ApiModelProperty("进系统总名单")
    private BigDecimal inSystemCount;

    @Excel(name = "总成本")
    @ApiModelProperty("总成本")
    private BigDecimal totalCost;

    @Excel(name = "总成交量")
    @ApiModelProperty("总成交量")
    private BigDecimal turnOver;

    @Excel(name = "成交额")
    @ApiModelProperty("成交额")
    private BigDecimal turnoverAmount;

    @Excel(name = "成交率")
    @ApiModelProperty("成交率")
    private BigDecimal closeRate;

    @Excel(name = "当期成交量-报名")
    @ApiModelProperty("当期成交量1")
    private BigDecimal turnOverNowOne;

    @Excel(name = "当期成交额-报名")
    @ApiModelProperty("当期成交额1")
    private BigDecimal turnoverAmountNowOne;

    @Excel(name = "当期ROI-报名")
    @ApiModelProperty("当期ROI1")
    private BigDecimal ROINowOne;

    @Excel(name = "累计成交量-报名")
    @ApiModelProperty("累计成交量1")
    private BigDecimal accumulativeTurnOverOne;

    @Excel(name = "累计成交额-报名")
    @ApiModelProperty("累计成交额1")
    private BigDecimal accumulativeTurnOverAmountOne;

    @Excel(name = "ROI-报名")
    @ApiModelProperty("ROI1")
    private BigDecimal ROIOne;

    @Excel(name = "当期成交量-订单")
    @ApiModelProperty("当期成交量2")
    private BigDecimal turnOverNowTwo;

    @Excel(name = "当期成交额-订单")
    @ApiModelProperty("当期成交额2")
    private BigDecimal turnoverAmountNowTwo;

    @Excel(name = "当期ROI-订单")
    @ApiModelProperty("当期ROI2")
    private BigDecimal ROINowTwo;

    @Excel(name = "累计成交量-订单")
    @ApiModelProperty("累计成交量2")
    private BigDecimal accumulativeTurnOverTwo;

    @Excel(name = "累计成交额-订单")
    @ApiModelProperty("累计成交额2")
    private BigDecimal accumulativeTurnOverAmountTwo;

    @Excel(name = "ROI-订单")
    @ApiModelProperty("ROI2")
    private BigDecimal ROITwo;

    @Excel(name = "当期成交量-财务")
    @ApiModelProperty("当期成交量-财务")
    private BigDecimal turnOverNowFinance;

    @Excel(name = "当期成交额-财务")
    @ApiModelProperty("当期成交额-财务")
    private BigDecimal turnoverAmountNowFinance;

    @Excel(name = "当期ROI-财务")
    @ApiModelProperty("当期ROI-财务")
    private BigDecimal ROINowFinance;

    @Excel(name = "累计成交量-财务")
    @ApiModelProperty("累计成交量-财务")
    private BigDecimal accumulativeTurnOverFinance;

    @Excel(name = "累计成交额-财务")
    @ApiModelProperty("累计成交额-财务")
    private BigDecimal accumulativeTurnOverAmountFinance;

    @Excel(name = "累计ROI-财务")
    @ApiModelProperty("累计ROI-财务")
    private BigDecimal ROIAccumulativeFinance;




}
