package com.nnb.customer.model;

import com.nnb.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-02-21
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BdImportClueErrorDto implements Serializable {

    @Excel(name = "错误信息")
    private String errorMsg;
}
