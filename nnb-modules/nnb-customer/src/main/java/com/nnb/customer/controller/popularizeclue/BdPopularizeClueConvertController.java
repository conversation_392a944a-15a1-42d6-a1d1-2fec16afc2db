package com.nnb.customer.controller.popularizeclue;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.customer.domain.popularizeclue.BdPopularizeClueConvert;
import com.nnb.customer.service.popularclue.IBdPopularizeClueConvertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 推广线索-转化Controller
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
@RestController
@RequestMapping("/convert")
@Api(tags = "BdPopularizeClueConvertController", description = "推广线索-转化")
public class BdPopularizeClueConvertController extends BaseController
{
    @Autowired
    private IBdPopularizeClueConvertService bdPopularizeClueConvertService;

    /**
     * 查询推广线索-转化列表
     */
    @ApiOperation(value = "查询推广线索-转化列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdPopularizeClueConvert.class)})
    @PreAuthorize(hasPermi = "erp:convert:list")
    @GetMapping("/list")
    public TableDataInfo list(BdPopularizeClueConvert bdPopularizeClueConvert)
    {
        startPage();
        List<BdPopularizeClueConvert> list = bdPopularizeClueConvertService.selectBdPopularizeClueConvertList(bdPopularizeClueConvert);
        return getDataTable(list);
    }

    /**
     * 导出推广线索-转化列表
     */
    @ApiOperation(value = "导出推广线索-转化列表")
    @PreAuthorize(hasPermi = "erp:convert:export")
    //@Log(title = "推广线索-转化", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdPopularizeClueConvert bdPopularizeClueConvert) throws IOException
    {
        List<BdPopularizeClueConvert> list = bdPopularizeClueConvertService.selectBdPopularizeClueConvertList(bdPopularizeClueConvert);
        ExcelUtil<BdPopularizeClueConvert> util = new ExcelUtil<BdPopularizeClueConvert>(BdPopularizeClueConvert.class);
        util.exportExcel(response, list, "推广线索-转化数据");
    }

    /**
     * 获取推广线索-转化详细信息
     */
    @ApiOperation(value = "获取推广线索-转化详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdPopularizeClueConvert.class)})
    @PreAuthorize(hasPermi = "erp:convert:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="推广线索-转化id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdPopularizeClueConvertService.selectBdPopularizeClueConvertById(id));
    }

    /**
     * 新增推广线索-转化
     */
    @ApiOperation(value = "新增推广线索-转化")
    @PreAuthorize(hasPermi = "erp:convert:add")
    //@Log(title = "推广线索-转化", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdPopularizeClueConvert bdPopularizeClueConvert)
    {
        return toAjax(bdPopularizeClueConvertService.insertBdPopularizeClueConvert(bdPopularizeClueConvert));
    }

    /**
     * 修改推广线索-转化
     */
    @ApiOperation(value = "修改推广线索-转化")
    @PreAuthorize(hasPermi = "erp:convert:edit")
    //@Log(title = "推广线索-转化", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdPopularizeClueConvert bdPopularizeClueConvert)
    {
        return toAjax(bdPopularizeClueConvertService.updateBdPopularizeClueConvert(bdPopularizeClueConvert));
    }

    /**
     * 删除推广线索-转化
     */
    @ApiOperation(value = "删除推广线索-转化")
    @PreAuthorize(hasPermi = "erp:convert:remove")
    //@Log(title = "推广线索-转化", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdPopularizeClueConvertService.deleteBdPopularizeClueConvertByIds(ids));
    }

    @ApiOperation("根据推广线索插入转化线索")
    @GetMapping("/addPopularizeClueConvert/{type}")
    public void addPopularizeClueConvert(@PathVariable Integer type) {
        bdPopularizeClueConvertService.addPopularizeClueConvert(type);
    }
}
