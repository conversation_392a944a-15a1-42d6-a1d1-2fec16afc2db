package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdClueIssue;
import com.nnb.customer.service.IBdClueIssueService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 线索下发记录Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdClueIssue")
@Api(tags = "BdClueIssueController", description = "线索下发记录")
public class BdClueIssueController extends BaseController
{
    @Autowired
    private IBdClueIssueService bdClueIssueService;

    /**
     * 查询线索下发记录列表
     */
    @ApiOperation(value = "查询线索下发记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueIssue.class)})
    @PreAuthorize(hasPermi = "customer:BdClueIssue:list")
    @GetMapping("/list")
    public TableDataInfo list(BdClueIssue bdClueIssue)
    {
        startPage();
        List<BdClueIssue> list = bdClueIssueService.selectBdClueIssueList(bdClueIssue);
        return getDataTable(list);
    }

    /**
     * 导出线索下发记录列表
     */
    @ApiOperation(value = "导出线索下发记录列表")
    @PreAuthorize(hasPermi = "customer:BdClueIssue:export")
    //@Log(title = "线索下发记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdClueIssue bdClueIssue) throws IOException
    {
        List<BdClueIssue> list = bdClueIssueService.selectBdClueIssueList(bdClueIssue);
        ExcelUtil<BdClueIssue> util = new ExcelUtil<BdClueIssue>(BdClueIssue.class);
        util.exportExcel(response, list, "线索下发记录数据");
    }

    /**
     * 获取线索下发记录详细信息
     */
    @ApiOperation(value = "获取线索下发记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueIssue.class)})
    @PreAuthorize(hasPermi = "customer:BdClueIssue:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="线索下发记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdClueIssueService.selectBdClueIssueById(id));
    }

    /**
     * 新增线索下发记录
     */
    @ApiOperation(value = "新增线索下发记录")
    @PreAuthorize(hasPermi = "customer:BdClueIssue:add")
    //@Log(title = "线索下发记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdClueIssue bdClueIssue)
    {
        return toAjax(bdClueIssueService.insertBdClueIssue(bdClueIssue));
    }

    /**
     * 修改线索下发记录
     */
    @ApiOperation(value = "修改线索下发记录")
    @PreAuthorize(hasPermi = "customer:BdClueIssue:edit")
    //@Log(title = "线索下发记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdClueIssue bdClueIssue)
    {
        return toAjax(bdClueIssueService.updateBdClueIssue(bdClueIssue));
    }

    /**
     * 删除线索下发记录
     */
    @ApiOperation(value = "删除线索下发记录")
    @PreAuthorize(hasPermi = "customer:BdClueIssue:remove")
    //@Log(title = "线索下发记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdClueIssueService.deleteBdClueIssueByIds(ids));
    }
}
