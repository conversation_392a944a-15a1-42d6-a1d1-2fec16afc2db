package com.nnb.customer.controller;

import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.customer.service.IBdClueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 北斗线索对接螳螂Controller
 */
@RestController
@RequestMapping("/BdClueTangLang")
@Api(tags = "BdClueController", description = "北斗线索对接螳螂")
@Slf4j
public class BdClueTangLangController {

    @Autowired
    private IBdClueService bdClueService;


    @ApiOperation(value = "将螳螂系统的客户数据添加客保至CRM系统")
    @GetMapping("createKbByTangLang")
    public AjaxResult createKbByTangLang(){
        bdClueService.createKbByTangLang();
        return AjaxResult.success();
    }


}
