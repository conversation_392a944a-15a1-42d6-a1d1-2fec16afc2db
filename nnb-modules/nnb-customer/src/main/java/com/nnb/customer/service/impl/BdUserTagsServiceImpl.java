package com.nnb.customer.service.impl;

import java.util.List;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.customer.domain.BdRoleTags;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdUserTagsMapper;
import com.nnb.customer.domain.BdUserTags;
import com.nnb.customer.service.IBdUserTagsService;

/**
 * 用户标签配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-02-22
 */
@Service
public class BdUserTagsServiceImpl implements IBdUserTagsService 
{
    @Autowired
    private BdUserTagsMapper bdUserTagsMapper;

    /**
     * 查询用户标签配置
     * 
     * @param numId 用户标签配置主键
     * @return 用户标签配置
     */
    @Override
    public BdUserTags selectBdUserTagsByNumId(Long numId)
    {
        return bdUserTagsMapper.selectBdUserTagsByNumId(numId);
    }

    /**
     * 查询用户标签配置列表
     * 
     * @param bdUserTags 用户标签配置
     * @return 用户标签配置
     */
    @Override
    public List<BdUserTags> selectBdUserTagsList(BdUserTags bdUserTags)
    {
        return bdUserTagsMapper.selectBdUserTagsList(bdUserTags);
    }

    @Override
    public List<BdUserTags> selectBdUserTagsListByName(BdUserTags bdUserTags) {
        return bdUserTagsMapper.selectBdUserTagsByName(bdUserTags);
    }

    /**
     * 新增用户标签配置
     * 
     * @param bdUserTags 用户标签配置
     * @return 结果
     */
    @Override
    public int insertBdUserTags(BdUserTags bdUserTags)
    {
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();
        bdUserTags.setNumCreateUserid(userId);
        bdUserTags.setNumLastUpdUserid(userId);
        bdUserTags.setDatCreateTime(date);
        bdUserTags.setUpdateTime(date);
        return bdUserTagsMapper.insertBdUserTags(bdUserTags);
    }

    /**
     * 修改用户标签配置
     * 
     * @param bdUserTags 用户标签配置
     * @return 结果
     */
    @Override
    public int updateBdUserTags(BdUserTags bdUserTags)
    {
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();
        bdUserTags.setNumLastUpdUserid(userId);
        bdUserTags.setUpdateTime(date);
        return bdUserTagsMapper.updateBdUserTags(bdUserTags);
    }

    /**
     * 批量删除用户标签配置
     * 
     * @param numIds 需要删除的用户标签配置主键
     * @return 结果
     */
    @Override
    public int deleteBdUserTagsByNumIds(Long[] numIds)
    {
        return bdUserTagsMapper.deleteBdUserTagsByNumIds(numIds);
    }

    /**
     * 删除用户标签配置信息
     * 
     * @param numId 用户标签配置主键
     * @return 结果
     */
    @Override
    public int deleteBdUserTagsByNumId(Long numId)
    {
        return bdUserTagsMapper.deleteBdUserTagsByNumId(numId);
    }

    @Override
    public List<BdRoleTags> selectBdRoleTagsList(BdRoleTags tags) {

        return bdUserTagsMapper.selectBdRoleTagsList(tags);

    }

}
