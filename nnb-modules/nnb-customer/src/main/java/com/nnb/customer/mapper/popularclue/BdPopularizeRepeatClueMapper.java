package com.nnb.customer.mapper.popularclue;

import com.nnb.customer.domain.clue.RepeatClue;
import com.nnb.customer.domain.popularizeclue.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 推广重复线索Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-11
 */
public interface BdPopularizeRepeatClueMapper
{
    /**
     * 查询推广重复线索
     *
     * @param id 推广重复线索主键
     * @return 推广重复线索
     */
    public BdPopularizeRepeatClue selectBdPopularizeRepeatClueById(Long id);

    /**
     * 查询推广重复线索列表
     *
     * @param bdPopularizeRepeatClue 推广重复线索
     * @return 推广重复线索集合
     */
    public List<BdPopularizeRepeatClue> selectBdPopularizeRepeatClueList(BdPopularizeRepeatClue bdPopularizeRepeatClue);

    /**
     * 新增推广重复线索
     *
     * @param bdPopularizeRepeatClue 推广重复线索
     * @return 结果
     */
    public int insertBdPopularizeRepeatClue(BdPopularizeRepeatClue bdPopularizeRepeatClue);

    /**
     * 修改推广重复线索
     *
     * @param bdPopularizeRepeatClue 推广重复线索
     * @return 结果
     */
    public int updateBdPopularizeRepeatClue(BdPopularizeRepeatClue bdPopularizeRepeatClue);

    /**
     * 删除推广重复线索
     *
     * @param id 推广重复线索主键
     * @return 结果
     */
    public int deleteBdPopularizeRepeatClueById(Long id);

    /**
     * 批量删除推广重复线索
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdPopularizeRepeatClueByIds(Long[] ids);

    public Long countBdPopularizeRepeatClue(@Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO, @Param("advertiserId") String advertiserId);

    public List<String> getBdPopularizeRepeatClue(@Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO, @Param("advertiserId") String advertiserId);

    PopularizeClueAccountBoardVo getPopularizeClueAccountBoardVo(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueDetail> getPopularizeClueCount(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    PopularizeClueAccountPhone getPopularizeCluePhone(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    Long getFirstConsultEffectiveCount(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<Long> getCreateClueFirstConsultEffectiveCount(@Param("guestSrcId") Long guestSrcId,  @Param("enterpriseDominant")Integer enterpriseDominant, @Param("popularizeClueChannelDTO")PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<RegistrationClueVo> getRegistrationCount(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<RegistrationClueVo> getCreateClueRegistrationCount(@Param("guestSrcId") Long guestSrcId,  @Param("enterpriseDominant")Integer enterpriseDominant, @Param("popularizeClueChannelDTO")PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<ClueOrderVo> getClueOrderVo(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueAccountPhone> getClueOrderDetailVo(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);
    PopularizeClueAccountPhone getOrderPhone(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO, @Param("isImport") Integer isImport);

    List<ClueOrderVo> getImportClueOrderVo(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<ClueOrderVo> getImportClueFinanceVo(@Param("vcPhones") List<String> vcPhones,@Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<ClueOrderVo> getCreateClueOrderVo(@Param("guestSrcId") Long guestSrcId,  @Param("enterpriseDominant")Integer enterpriseDominant, @Param("popularizeClueChannelDTO")PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueAccountPhone> getCreateClueOrderDetailVo(@Param("guestSrcId") Long guestSrcId,  @Param("enterpriseDominant")Integer enterpriseDominant, @Param("popularizeClueChannelDTO")PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueAccountPhone> getOrderDetailByShHq(@Param("popularizeClueChannelDTO")PopularizeClueChannelDTO popularizeClueChannelDTO);

    @Select("select id, advertiser_name as 'advertiserName' from ad_advertiser_info where status = 1")
    List<AdvertiserInfo> getAdvertiserInfo();

    @Select("select ac.cost, aa.rebate_ratio FROM `ad_clue_source` ads LEFT JOIN ad_advertiser_info aai ON ads.id = aai.source_id LEFT JOIN ad_cost ac ON aai.advertiser_id = ac.advertiser_id LEFT JOIN ad_app aa ON aai.ad_app_id = aa.id " +
            "where ads.id = #{advertiserId} and ac.date >= #{popularizeClueChannelDTO.dateStart} and ac.date <= #{popularizeClueChannelDTO.dateEnd}")
    List<AdCostAndApp> getAdCostAndApp(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    @Select("select id, advertiser_name as 'advertiserName' from ad_advertiser_info where status = 1 and id = #{advertiserInfoId}")
    AdvertiserInfo getAdvertiserInfoById(String advertiserInfoId);

    List<String> getImportCluePhone(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    @Select("SELECT acs.name as channelName,aai.advertiser_name as accountName FROM ad_clue ac LEFT JOIN ad_advertiser_info aai ON ac.advertiser_id = aai.advertiser_id LEFT JOIN ad_clue_source acs ON aai.source_id = acs.id WHERE aai.STATUS = 1 AND ac.bd_clue_id = #{clueId} limit 1")
    PopularizeClueAccountPhone getChannelAndAccount(Long clueId);


    @Select("SELECT GROUP_CONCAT( id ) as clueIds,vc_phone as vcPhone FROM bd_clue WHERE enterprise_dominant = 2 AND num_status IN ( 2, 9, 14, 15, 16 ) AND vc_phone IS NOT NULL AND vc_phone != 0 AND popularize_clue_id IS NULL GROUP BY vc_phone HAVING COUNT( id ) > 2 limit #{clueCount}")
    List<RepeatClue> mergeRepeatClue(Integer clueCount);

    List<PopularizeClueAccountOrder> selectBdPopularizeOrder(@Param("orderIds") List<Long> orderIds);
}
