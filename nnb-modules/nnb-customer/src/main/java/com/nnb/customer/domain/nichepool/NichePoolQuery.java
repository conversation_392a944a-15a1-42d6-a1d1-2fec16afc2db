package com.nnb.customer.domain.nichepool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商机池检索条件
 */
@Data
public class NichePoolQuery {

    @ApiModelProperty("创建日期")
    private Date createDate;

    @ApiModelProperty("线索来源")
    private Long guestSrcsIds;

    @ApiModelProperty("推广来源")
    private Long adClueSourceIds;

    @ApiModelProperty("录入方式")
    private Integer numWhereGet;

    @ApiModelProperty("下沉原因")
    private Integer numSinkIds;

    @ApiModelProperty("名称")
    private String vcCustomerName;

    @ApiModelProperty("是否加入过客保 0：否，1：是")
    private Integer isAddClueKb;

    @ApiModelProperty("进入公海次数")
    private Integer enterCommonSeaCount;

    @ApiModelProperty("线索信息关键字")
    private String clueKeyWord;

    @ApiModelProperty("空号检测结果")
    private Integer numCheckPhoneStatus;

    @ApiModelProperty("风险号检测结果")
    private Integer numForbidCheckResult;

    @ApiModelProperty("AI外呼任务")
    private Long aiConfigurationsIds;

    @ApiModelProperty("AI外呼标签")
    private Long numAiTagIds;

    @ApiModelProperty("AI外呼意向度")
    private Long vcAiWish;

    @ApiModelProperty("线索所在节点")
    private Integer numStatus;

    @ApiModelProperty("人工外呼意向度  0.未标记,1.无意向 , 2.低意向 ,3.高意向")
    private Integer numManAiWish;

    @ApiModelProperty("意向度检测  0.未质检,1.质检符合 , 2.质检不符合 ")
    private Integer numQualityTesting;

    @ApiModelProperty("空号检测时间")
    private Date datCheckPhoneTime;

    @ApiModelProperty("所属城市")
    private Long cityId;

    @ApiModelProperty("意向产品")
    private Long needProductIds;

    @ApiModelProperty("号码状态1=&gt;关机, 2=&gt;拒接, 3=&gt;空号, 4=&gt;停机 5=&gt;正常")
    private Long numPhoneStatus;

    @ApiModelProperty("手机号归属地")
    private Long locationIds;

    @ApiModelProperty("是否有微信号 0：否，1：是")
    private Integer isHasWeiXin;

    @ApiModelProperty("是否有手机号 0：否，1：是")
    private Integer isHasPhone;

    @ApiModelProperty("是否有跟进 0：否，1：是")
    private Integer numFollowStatus;

    @ApiModelProperty("是否接通 0：否，1：是")
    private Integer numIsConnect;

    @ApiModelProperty("是否老客户 0：否，1：是")
    private Integer numIsFriend;

    @ApiModelProperty("是否有多个联系人 0：否，1：是")
    private Integer isHasManyContact;

    @ApiModelProperty("联系人角色")
    private String vcContactRole;

    @ApiModelProperty("是否有企业名称 0：否，1：是")
    private Integer isHasCompanyName;

    @ApiModelProperty("拨打状态 1.待拨打2.未接通 .3联系中")
    private Integer numDialStatus;

    @ApiModelProperty("最近跟近人")
    private Long followUserIds;

    @ApiModelProperty("最近跟近部门")
    private Long followDeptIds;

    @ApiModelProperty("最大通话时长")
    private BigDecimal datMaxCallTime;

    @ApiModelProperty("线索拨打次数")
    private Integer clueDialNum;

    @ApiModelProperty("线索拨通次数")
    private Integer clueConnectNum;











}
