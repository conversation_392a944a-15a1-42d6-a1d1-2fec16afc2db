package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdClueIssue;

/**
 * 线索下发记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface IBdClueIssueService 
{
    /**
     * 查询线索下发记录
     * 
     * @param id 线索下发记录主键
     * @return 线索下发记录
     */
    public BdClueIssue selectBdClueIssueById(Long id);

    /**
     * 查询线索下发记录列表
     * 
     * @param bdClueIssue 线索下发记录
     * @return 线索下发记录集合
     */
    public List<BdClueIssue> selectBdClueIssueList(BdClueIssue bdClueIssue);

    /**
     * 新增线索下发记录
     * 
     * @param bdClueIssue 线索下发记录
     * @return 结果
     */
    public int insertBdClueIssue(BdClueIssue bdClueIssue);

    /**
     * 修改线索下发记录
     * 
     * @param bdClueIssue 线索下发记录
     * @return 结果
     */
    public int updateBdClueIssue(BdClueIssue bdClueIssue);

    /**
     * 批量删除线索下发记录
     * 
     * @param ids 需要删除的线索下发记录主键集合
     * @return 结果
     */
    public int deleteBdClueIssueByIds(Long[] ids);

    /**
     * 删除线索下发记录信息
     * 
     * @param id 线索下发记录主键
     * @return 结果
     */
    public int deleteBdClueIssueById(Long id);
}
