package com.nnb.customer.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdChannelConsultMapper;
import com.nnb.customer.domain.BdChannelConsult;
import com.nnb.customer.service.IBdChannelConsultService;

/**
 * 媒体账户咨询业务关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
@Service
public class BdChannelConsultServiceImpl implements IBdChannelConsultService 
{
    @Autowired
    private BdChannelConsultMapper bdChannelConsultMapper;

    /**
     * 查询媒体账户咨询业务关系
     * 
     * @param channelId 媒体账户咨询业务关系主键
     * @return 媒体账户咨询业务关系
     */
    @Override
    public BdChannelConsult selectBdChannelConsultByChannelId(Long channelId)
    {
        return bdChannelConsultMapper.selectBdChannelConsultByChannelId(channelId);
    }

    /**
     * 查询媒体账户咨询业务关系列表
     * 
     * @param bdChannelConsult 媒体账户咨询业务关系
     * @return 媒体账户咨询业务关系
     */
    @Override
    public List<BdChannelConsult> selectBdChannelConsultList(BdChannelConsult bdChannelConsult)
    {
        return bdChannelConsultMapper.selectBdChannelConsultList(bdChannelConsult);
    }

    /**
     * 新增媒体账户咨询业务关系
     * 
     * @param bdChannelConsult 媒体账户咨询业务关系
     * @return 结果
     */
    @Override
    public int insertBdChannelConsult(BdChannelConsult bdChannelConsult)
    {
        return bdChannelConsultMapper.insertBdChannelConsult(bdChannelConsult);
    }

    /**
     * 修改媒体账户咨询业务关系
     * 
     * @param bdChannelConsult 媒体账户咨询业务关系
     * @return 结果
     */
    @Override
    public int updateBdChannelConsult(BdChannelConsult bdChannelConsult)
    {
        return bdChannelConsultMapper.updateBdChannelConsult(bdChannelConsult);
    }

    /**
     * 批量删除媒体账户咨询业务关系
     * 
     * @param channelIds 需要删除的媒体账户咨询业务关系主键
     * @return 结果
     */
    @Override
    public int deleteBdChannelConsultByChannelIds(Long[] channelIds)
    {
        return bdChannelConsultMapper.deleteBdChannelConsultByChannelIds(channelIds);
    }

    /**
     * 删除媒体账户咨询业务关系信息
     * 
     * @param channelId 媒体账户咨询业务关系主键
     * @return 结果
     */
    @Override
    public int deleteBdChannelConsultByChannelId(Long channelId)
    {
        return bdChannelConsultMapper.deleteBdChannelConsultByChannelId(channelId);
    }
}
