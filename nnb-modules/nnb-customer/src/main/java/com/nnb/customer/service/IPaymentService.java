package com.nnb.customer.service;

import com.nnb.customer.converter.domain.LemonClubReq;
import com.nnb.customer.converter.domain.LemonClubRes;
import com.nnb.customer.domain.PayCompany;
import com.nnb.customer.domain.dto.PaymentInfoDto;
import com.nnb.customer.domain.fuioupay.request.PreCreateReq;
import com.nnb.customer.domain.vo.PaymentInfoVo;
import com.nnb.customer.domain.PaymentInfo;
import com.nnb.customer.model.form.CashierOrderForm;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2022-09-07
 * @Version: 1.0
 */
public interface IPaymentService {


    /**
     * 获取支付公司主体
     *
     * @return
     */
    Integer getUserCompanyPay(Integer userId);

    /**
     * @param paymentInfo 请求实体
     * @return
     */
    PaymentInfoVo paymentQRCode(PaymentInfo paymentInfo);

    /**
     * 柠檬会支付
     * @param lemonClubReq
     * @return
     */
    LemonClubRes paymentQRCodeLemon(LemonClubReq lemonClubReq);

    /**
     * 获取支付主体详情
     *
     * @return
     */
    List<PayCompany> getCompanyPayType();

    /**
     * 添加交易记录
     * @param paymentInfo
     * @return
     */
    int addPaymentInfo(PaymentInfo paymentInfo);

    /**
     * 获取支付列表
     * @param paymentInfoDto
     * @return
     */
    List<PaymentInfoVo> getPaymentInfoList(PaymentInfoDto paymentInfoDto);

    /**
     * 获取用户支付主体
     * @return
     */
    Integer getUserPayCompany();

    /**
     * 根据客户或线索id查询二维码信息
     * @return
     */
    PaymentInfoVo getQRCodeByClueIdOrClientId(PaymentInfo paymentInfo);

    /**
     * 修改交易记录
     * @param paymentInfo
     */
    boolean modifyPayment(PaymentInfo paymentInfo);

    /**
     * 收银台下单-富友
     * @param form
     * @return
     */
    String cashierOrder(CashierOrderForm form);

    /**
     * 检验二维码是否可用
     * @param form
     * @return
     */
    Integer checkCashierOrder(CashierOrderForm form);

    /**
     * 组装请求参数
     */
    PreCreateReq setRequest(CashierOrderForm form);

}
