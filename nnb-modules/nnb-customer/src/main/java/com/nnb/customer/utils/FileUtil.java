package com.nnb.customer.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2022-09-07
 * @Version: 1.0
 */
@Slf4j
public class FileUtil {

    /**
     * File转MultipartFile
     * @param file
     * @return
     */
    public static MultipartFile fileToMultipartFile(File file){
        MultipartFile multipartFile = null;
        try {
            InputStream inputStream = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(), inputStream);
        }catch (Exception e){
            e.printStackTrace();
        }
        return  multipartFile;
    }

    public static MultipartFile downLoadMultipartFileByUrl(String urlStr) {
       return fileToMultipartFile(downLoadByUrl(urlStr));
    }


    /**
     * OSS远程地址生成本地文件
     *
     * @param fileUrl
     * @return
     */
    public static File getFileByUrl(String fileUrl) {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        BufferedOutputStream stream = null;
        InputStream inputStream = null;
        File file = null;
        String suffix = "";
        try {
            suffix = fileUrl.substring(fileUrl.lastIndexOf(".") + 1);
            URL imageUrl = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) imageUrl.openConnection();
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            inputStream = conn.getInputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            file = File.createTempFile("pattern", "." + suffix);
            log.info("临时文件创建成功={}", file.getCanonicalPath());
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            stream = new BufferedOutputStream(fileOutputStream);
            stream.write(outStream.toByteArray());
        } catch (Exception e) {
            log.error("创建人脸获取服务器图片异常", e);
        } finally {
            try {
                if (inputStream != null) inputStream.close();
                if (stream != null) stream.close();
                outStream.close();
            } catch (Exception e) {
                log.error("关闭流异常", e);
            }
        }
        return file;
    }

    /**
     * 从网络Url中下载文件
     *
     * @param urlStr url的路径
     * @throws IOException
     */
    public static File downLoadByUrl(String urlStr) {
        File file = null;
        try {
            String fileName = getFileName(urlStr);
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为5秒
            conn.setConnectTimeout(5 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            InputStream inputStream = conn.getInputStream();
            //获取自己数组
            byte[] getData = readInputStream(inputStream);

            File tempFile = File.createTempFile("temp", File.separator + fileName);
            FileOutputStream fos = new FileOutputStream(tempFile);
            fos.write(getData);
            file = tempFile;
            fos.close();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    private static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[4 * 1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    /**
     * 从src文件路径获取文件名
     *
     * @param srcRealPath src文件路径
     * @return 文件名
     */
    private static String getFileName(String srcRealPath) {
        return StringUtils.substringAfterLast(srcRealPath, "/");
    }
}
