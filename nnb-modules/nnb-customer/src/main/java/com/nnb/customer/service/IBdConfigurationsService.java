package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdConfigurations;
import com.nnb.customer.model.BdConfigurationsDto;
import com.nnb.customer.model.BdConfigurationsVo;

/**
 * 配置字典Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface IBdConfigurationsService 
{
    /**
     * 查询配置字典
     * 
     * @param id 配置字典主键
     * @return 配置字典
     */
    public BdConfigurations selectBdConfigurationsById(Long id);

    /**
     * 查询配置字典列表
     * 
     * @param bdConfigurations 配置字典
     * @return 配置字典集合
     */
    public List<BdConfigurations> selectBdConfigurationsList(BdConfigurations bdConfigurations);

    /**
     * 新增配置字典
     * 
     * @param bdConfigurations 配置字典
     * @return 结果
     */
    public int insertBdConfigurations(BdConfigurations bdConfigurations);

    /**
     * 修改配置字典
     * 
     * @param bdConfigurations 配置字典
     * @return 结果
     */
    public int updateBdConfigurations(BdConfigurationsDto bdConfigurations);

    /**
     * 批量删除配置字典
     * 
     * @param ids 需要删除的配置字典主键集合
     * @return 结果
     */
    public int deleteBdConfigurationsByIds(Long[] ids);

    /**
     * 删除配置字典信息
     * 
     * @param id 配置字典主键
     * @return 结果
     */
    public int deleteBdConfigurationsById(Long id);

    /**
     * 新增配置字典
     *
     * @param dto 配置字典
     * @return 结果
     */
    public int insertDto(BdConfigurationsDto dto);

    /**
     * 查询配置字典列表
     *
     * @param bdConfigurations 配置字典
     * @return 配置字典集合
     */
    public List<BdConfigurationsVo> selectBdConfigurationsVoList(BdConfigurations bdConfigurations);

    /**
     * 查询配置字典
     *
     * @param id 配置字典主键
     * @return 配置字典
     */
    public BdConfigurationsVo selectBdConfigurationsVoById(Long id);


}
