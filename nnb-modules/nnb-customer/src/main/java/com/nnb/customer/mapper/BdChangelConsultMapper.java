package com.nnb.customer.mapper;

import java.util.List;
import com.nnb.customer.domain.BdChangelConsult;

/**
 * 投放流转咨询业务关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface BdChangelConsultMapper 
{
    /**
     * 查询投放流转咨询业务关系
     * 
     * @param changelUserId 投放流转咨询业务关系主键
     * @return 投放流转咨询业务关系
     */
    public BdChangelConsult selectBdChangelConsultByChangelUserId(Long changelUserId);

    /**
     * 查询投放流转咨询业务关系列表
     * 
     * @param bdChangelConsult 投放流转咨询业务关系
     * @return 投放流转咨询业务关系集合
     */
    public List<BdChangelConsult> selectBdChangelConsultList(BdChangelConsult bdChangelConsult);

    /**
     * 新增投放流转咨询业务关系
     * 
     * @param bdChangelConsult 投放流转咨询业务关系
     * @return 结果
     */
    public int insertBdChangelConsult(BdChangelConsult bdChangelConsult);

    /**
     * 修改投放流转咨询业务关系
     * 
     * @param bdChangelConsult 投放流转咨询业务关系
     * @return 结果
     */
    public int updateBdChangelConsult(BdChangelConsult bdChangelConsult);

    /**
     * 删除投放流转咨询业务关系
     * 
     * @param changelUserId 投放流转咨询业务关系主键
     * @return 结果
     */
    public int deleteBdChangelConsultByChangelUserId(Long changelUserId);

    /**
     * 批量删除投放流转咨询业务关系
     * 
     * @param changelUserIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdChangelConsultByChangelUserIds(Long[] changelUserIds);

}
