package com.nnb.customer.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 线索跟进记录对象 bd_clue_follow
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@ApiModel(value="BdClueFollow",description="线索跟进记录对象")
public class BdClueFollow extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;

    /** 线索id */
    @Excel(name = "线索id")
    @ApiModelProperty("线索id")
    private Long clueId;

    /** 跟进人id */
    @Excel(name = "跟进人id")
    @ApiModelProperty("跟进人id")
    private Long userId;

    /** 跟进时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("跟进时间")
    private Date datCreatedAt;

    /** 状态 1普通跟进，2放弃,3拨打电话 */
    @Excel(name = "状态 1普通跟进，2放弃,3拨打电话")
    @ApiModelProperty("状态 1普通跟进，2放弃,3拨打电话")
    private Long numType;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String vcRemark;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setClueId(Long clueId) 
    {
        this.clueId = clueId;
    }

    public Long getClueId() 
    {
        return clueId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDatCreatedAt(Date datCreatedAt) 
    {
        this.datCreatedAt = datCreatedAt;
    }

    public Date getDatCreatedAt() 
    {
        return datCreatedAt;
    }
    public void setNumType(Long numType) 
    {
        this.numType = numType;
    }

    public Long getNumType() 
    {
        return numType;
    }
    public void setVcRemark(String vcRemark) 
    {
        this.vcRemark = vcRemark;
    }

    public String getVcRemark() 
    {
        return vcRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clueId", getClueId())
            .append("userId", getUserId())
            .append("datCreatedAt", getDatCreatedAt())
            .append("numType", getNumType())
            .append("vcRemark", getVcRemark())
            .toString();
    }
}
