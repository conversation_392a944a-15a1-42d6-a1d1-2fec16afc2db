package com.nnb.customer.mapper;

import java.util.List;
import java.util.Map;

import com.nnb.customer.domain.BdClueRoleTage;
import com.nnb.customer.domain.BdClueUserTags;
import com.nnb.customer.domain.BdNeedProduct;
import com.nnb.customer.domain.es.ESBdClueUserTags;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户标签Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-22
 */
@Repository
public interface BdClueUserTagsMapper
{
    /**
     * 查询用户标签
     *
     * @param numId 用户标签主键
     * @return 用户标签
     */
    BdClueUserTags selectBdClueUserTagsByNumId(Long numId);

    List<BdClueUserTags> selectBdClueUserTagsListByClueIdList(@Param("bdClueIdList") List<Long> bdClueIdList);

    /**
     * 查询用户标签列表
     *
     * @param bdClueUserTags 用户标签
     * @return 用户标签集合
     */


    /**
     * 新增用户标签
     *
     * @param bdClueUserTags 用户标签
     * @return 结果
     */
    public int insertBdClueUserTags(BdClueUserTags bdClueUserTags);

    /**
     * 修改用户标签
     *
     * @param bdClueUserTags 用户标签
     * @return 结果
     */
    public int updateBdClueUserTags(BdClueUserTags bdClueUserTags);

    /**
     * 删除用户标签
     *
     * @param numId 用户标签主键
     * @return 结果
     */
    public int deleteBdClueUserTagsByNumId(Long numId);

    /**
     * 批量删除用户标签
     *
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdClueUserTagsByNumIds(Long[] numIds);

    public int deleteBdClueUserTagsByNumIdList(@Param("numIds") List<Long> numIds);
    /**
     * 批量删除用户标签byclueid
     *
     * @param clueIds 需要删除的数据
     * @return 结果
     */
    public int deleteBdClueUserTagsByClueId(@Param("clueIds")List<Long> clueIds);


    List<BdClueUserTags> selectBdClueUserTagsList(BdClueUserTags bdClueUserTags);

    int deleteBdClueRoleTagsByClueId(@Param("clueIds") List<Long> clueIds);

    int insertBdClueRoleTags(BdClueRoleTage bdClueRoleTage);

    List<BdClueUserTags> selectBdClueUserTagsListByIds(String ids);

    public List<BdClueUserTags> selectBdClueUserTagsListByClueIds(String ids);

    List<ESBdClueUserTags> selectBdClueTagsEs(@Param("bdClueIdList") List<Long> bdClueIdList);

    void selectBdClueUserTagsListByIdList(@Param("idList") List<Long> idList);
}
