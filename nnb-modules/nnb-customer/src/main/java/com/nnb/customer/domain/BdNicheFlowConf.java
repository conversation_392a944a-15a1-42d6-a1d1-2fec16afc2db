package com.nnb.customer.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 商机流转配置对象 bd_niche_flow_conf
 *
 * <AUTHOR>
 * @date 2022-02-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "BdNicheFlowConf", description = "商机流转配置对象")
public class BdNicheFlowConf extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long numId;

    @Excel(name = "商机名称")
    @ApiModelProperty("商机名称")
    private String vcNicheName;

    @Excel(name = "简介")
    @ApiModelProperty("简介")
    private String vcProfiles;

    @Excel(name = "线索创建时间")
    @ApiModelProperty("线索创建时间")
    private Long numPrefixDateLimit;

    @Excel(name = "线索客保时间")
    @ApiModelProperty("线索客保时间")
    private Long numClueProtectDay;

    @Excel(name = "停留周期")
    @ApiModelProperty("停留周期")
    private Long numKeepDay;

    @Excel(name = "下个流转节点")
    @ApiModelProperty("下个流转节点")
    private Long numNextNicheFlowId;

    @Excel(name = "电话拨打周期")
    @ApiModelProperty("电话拨打周期")
    private Long numDialPeriod;

    @Excel(name = "状态", readConverterExp = "0=停用,1=常用")
    @ApiModelProperty("状态（0停用 1正常）")
    private String numStatus;

    @ApiModelProperty("排序")
    private Long numSort;

    @ApiModelProperty("创建人")
    private Long numCreateUserid;

    @ApiModelProperty("最后修改人")
    private Long numLastUpdUserid;

    @ApiModelProperty("创建时间")
    private Date datCreateTime;

    @ApiModelProperty("最后修改时间")
    private Date datLastUpd;

    @ApiModelProperty("操作类型  默认传1 筛选城市权限")
    private Integer operationType;

    @ApiModelProperty("当前登录人的部门城市ID")
    private Long userCityId;

    @ApiModelProperty("商机池检索条件")
    private String nichePoolQueryParam;

    @ApiModelProperty("线索总数")
    private Long clueTotal;

    @ApiModelProperty("接通率")
    private BigDecimal callCompletingRate;

    @ApiModelProperty("客保率")
    private BigDecimal customerProtectRate;

    @ApiModelProperty("商机池大类ID")
    private Long oppCategoryId;

    @ApiModelProperty("商机池大类名称")
    private String oppCategoryName;

    @ApiModelProperty("商机池大类名称")
    private List<Long> oppCategoryNameIds;

    @ApiModelProperty("商机池大类ids")
    private List<Long> oppCategoryIds;

    @ApiModelProperty("当前登录人的部门ID")
    private Long userDeptId;

    @ApiModelProperty("当前登录人的ID")
    private Long userId;

    @ApiModelProperty("主键ids")
    private List<Long> ids;

    @ApiModelProperty("是否有所有权限，0:没有，1:有")
    private Integer permissionsAll = 0;

    @ApiModelProperty("配置可见部门or人：1,部门，2:个人")
    private Integer deptOrUser;
}
