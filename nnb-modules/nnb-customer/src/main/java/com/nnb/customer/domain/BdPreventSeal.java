package com.nnb.customer.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 手机防封号记录对象 bd_prevent_seal
 * 
 * <AUTHOR>
 * @date 2022-03-14
 */
@ApiModel(value="BdPreventSeal",description="手机防封号记录对象")
public class BdPreventSeal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long numId;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String vcPhone;

    /** 解封时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "解封时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("解封时间")
    private Date datUnsealDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty("修改人")
    private Long numUpdatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedAt;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("修改时间")
    private Date datUpdatedAt;

    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setVcPhone(String vcPhone) 
    {
        this.vcPhone = vcPhone;
    }

    public String getVcPhone() 
    {
        return vcPhone;
    }
    public void setDatUnsealDate(Date datUnsealDate) 
    {
        this.datUnsealDate = datUnsealDate;
    }

    public Date getDatUnsealDate() 
    {
        return datUnsealDate;
    }
    public void setNumCreatedBy(Long numCreatedBy) 
    {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() 
    {
        return numCreatedBy;
    }
    public void setNumUpdatedBy(Long numUpdatedBy) 
    {
        this.numUpdatedBy = numUpdatedBy;
    }

    public Long getNumUpdatedBy() 
    {
        return numUpdatedBy;
    }
    public void setDatCreatedAt(Date datCreatedAt) 
    {
        this.datCreatedAt = datCreatedAt;
    }

    public Date getDatCreatedAt() 
    {
        return datCreatedAt;
    }
    public void setDatUpdatedAt(Date datUpdatedAt) 
    {
        this.datUpdatedAt = datUpdatedAt;
    }

    public Date getDatUpdatedAt() 
    {
        return datUpdatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("vcPhone", getVcPhone())
            .append("datUnsealDate", getDatUnsealDate())
            .append("numCreatedBy", getNumCreatedBy())
            .append("numUpdatedBy", getNumUpdatedBy())
            .append("datCreatedAt", getDatCreatedAt())
            .append("datUpdatedAt", getDatUpdatedAt())
            .toString();
    }
}
