package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdClueDistribution;
import com.nnb.customer.service.IBdClueDistributionService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 线索分配记录Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdClueDistribution")
@Api(tags = "BdClueDistributionController", description = "线索分配记录")
public class BdClueDistributionController extends BaseController
{
    @Autowired
    private IBdClueDistributionService bdClueDistributionService;

    /**
     * 查询线索分配记录列表
     */
    @ApiOperation(value = "查询线索分配记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueDistribution.class)})
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:list")
    @GetMapping("/list")
    public TableDataInfo list(BdClueDistribution bdClueDistribution)
    {
        startPage();
        List<BdClueDistribution> list = bdClueDistributionService.selectBdClueDistributionList(bdClueDistribution);
        return getDataTable(list);
    }

    /**
     * 导出线索分配记录列表
     */
    @ApiOperation(value = "导出线索分配记录列表")
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:export")
    //@Log(title = "线索分配记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdClueDistribution bdClueDistribution) throws IOException
    {
        List<BdClueDistribution> list = bdClueDistributionService.selectBdClueDistributionList(bdClueDistribution);
        ExcelUtil<BdClueDistribution> util = new ExcelUtil<BdClueDistribution>(BdClueDistribution.class);
        util.exportExcel(response, list, "线索分配记录数据");
    }

    /**
     * 获取线索分配记录详细信息
     */
    @ApiOperation(value = "获取线索分配记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueDistribution.class)})
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="线索分配记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdClueDistributionService.selectBdClueDistributionById(id));
    }

    /**
     * 新增线索分配记录
     */
    @ApiOperation(value = "新增线索分配记录")
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:add")
    //@Log(title = "线索分配记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdClueDistribution bdClueDistribution)
    {
        return toAjax(bdClueDistributionService.insertBdClueDistribution(bdClueDistribution));
    }

    /**
     * 修改线索分配记录
     */
    @ApiOperation(value = "修改线索分配记录")
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:edit")
    //@Log(title = "线索分配记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdClueDistribution bdClueDistribution)
    {
        return toAjax(bdClueDistributionService.updateBdClueDistribution(bdClueDistribution));
    }

    /**
     * 删除线索分配记录
     */
    @ApiOperation(value = "删除线索分配记录")
    @PreAuthorize(hasPermi = "customer:BdClueDistribution:remove")
    //@Log(title = "线索分配记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdClueDistributionService.deleteBdClueDistributionByIds(ids));
    }
}
