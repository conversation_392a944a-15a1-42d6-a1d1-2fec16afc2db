package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdGuestSrcs;
import com.nnb.customer.domain.vo.BdGuestSrcsVo;
import com.nnb.customer.model.BdGuestSrcsDto;
import com.nnb.customer.model.BdGuestSrcsListVo;

/**
 * 获客来源配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface IBdGuestSrcsService 
{
    /**
     * 查询获客来源配置
     * 
     * @param id 获客来源配置主键
     * @return 获客来源配置
     */
    public BdGuestSrcs selectBdGuestSrcsById(Long id);

    public BdGuestSrcsVo selectBdGuestSrcsVoById(Long id);

    /**
     * 查询获客来源配置列表
     * 
     * @param bdGuestSrcs 获客来源配置
     * @return 获客来源配置集合
     */
    public List<BdGuestSrcs> selectBdGuestSrcsList(BdGuestSrcs bdGuestSrcs);

    public List<BdGuestSrcsVo> selectBdGuestSrcsVO(BdGuestSrcs bdGuestSrcs);

    /**
     * 新增获客来源配置
     * 
     * @param bdGuestSrcs 获客来源配置
     * @return 结果
     */
    public int insertBdGuestSrcs(BdGuestSrcs bdGuestSrcs);

    /**
     * 修改获客来源配置
     * 
     * @param bdGuestSrcs 获客来源配置
     * @return 结果
     */
    public int updateBdGuestSrcs(BdGuestSrcs bdGuestSrcs);

    /**
     * 批量删除获客来源配置
     * 
     * @param ids 需要删除的获客来源配置主键集合
     * @return 结果
     */
    public int deleteBdGuestSrcsByIds(Long[] ids);

    /**
     * 删除获客来源配置信息
     * 
     * @param id 获客来源配置主键
     * @return 结果
     */
    public int deleteBdGuestSrcsById(Long id);

    /**
     * 新增获客来源配置
     *
     * @param dto 获客来源配置
     * @return 结果
     */
    public int insertBdGuestSrcsByDto(BdGuestSrcsDto dto);

    /**
     * 修改获客来源配置
     *
     * @param dto 获客来源配置
     * @return 结果
     */
    public int updateBdGuestSrcsByDto(BdGuestSrcsDto dto);

    /**
     * 查询获客来源配置列表（树型）
     *
     * @return 获客来源配置集合
     */
    public List<BdGuestSrcsListVo> selectBdGuestSrcsChildren();

    /**
     * 查询获客来源配置列表-新增线索使用
     *
     * @return 获客来源配置集合
     */
    public List<BdGuestSrcsListVo> selectBdGuestSrcsChildrenByRoles(String flag);
}
