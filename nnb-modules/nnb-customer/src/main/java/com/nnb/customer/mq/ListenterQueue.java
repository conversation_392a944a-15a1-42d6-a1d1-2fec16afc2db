package com.nnb.customer.mq;

import com.alibaba.fastjson.JSONObject;
import com.nnb.customer.domain.BdStandInsideLetter;
import com.nnb.customer.mapper.BdStandInsideLetterMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ListenterQueue {

    @Autowired
    private BdStandInsideLetterMapper bdStandInsideLetterMapper;

    @RabbitListener(queues = "customer.message.notice.queue")
    @RabbitHandler
    public void process(String mess) {
        log.info("customer.message.notice.queue监听到的mess为{}", mess);
        try {
            BdStandInsideLetter bdStandInsideLetter = JSONObject.parseObject(mess, BdStandInsideLetter.class);
            bdStandInsideLetterMapper.insertBdStandInsideLetter(bdStandInsideLetter);
        } catch (Exception e) {
            log.error("customer.message.notice.queue监听异常，异常信息为", e);
        }
    }
}
