package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.nnb.customer.domain.BdOutbound;
import com.nnb.customer.domain.SealPhone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdPreventSeal;
import com.nnb.customer.service.IBdPreventSealService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 手机防封号记录Controller
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@RestController
@RequestMapping("/BdPreventSeal")
@Api(tags = "BdPreventSealController", description = "手机防封号记录")
public class BdPreventSealController extends BaseController {
    @Autowired
    private IBdPreventSealService bdPreventSealService;

    /**
     * 查询手机防封号记录列表
     */
    @ApiOperation(value = "查询手机防封号记录列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdPreventSeal.class)})
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:list")
    @GetMapping("/list")
    public TableDataInfo list(BdPreventSeal bdPreventSeal) {
        startPage();
        List<BdPreventSeal> list = bdPreventSealService.selectBdPreventSealList(bdPreventSeal);
        return getDataTable(list);
    }

    /**
     * 导出手机防封号记录列表
     */
    @ApiOperation(value = "导出手机防封号记录列表")
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:export")
    //@Log(title = "手机防封号记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdPreventSeal bdPreventSeal) throws IOException {
        List<BdPreventSeal> list = bdPreventSealService.selectBdPreventSealList(bdPreventSeal);
        ExcelUtil<BdPreventSeal> util = new ExcelUtil<BdPreventSeal>(BdPreventSeal.class);
        util.exportExcel(response, list, "手机防封号记录数据");
    }

    /**
     * 获取手机防封号记录详细信息
     */
    @ApiOperation(value = "获取手机防封号记录详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdPreventSeal.class)})
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:query")
    @GetMapping(value = "/{numId}")
    public AjaxResult getInfo(@ApiParam(name = "numId", value = "手机防封号记录id") @PathVariable("numId") Long numId) {
        return AjaxResult.success(bdPreventSealService.selectBdPreventSealByNumId(numId));
    }

    /**
     * 新增手机防封号记录
     */
    @ApiOperation(value = "新增手机防封号记录")
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:add")
    //@Log(title = "手机防封号记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdPreventSeal bdPreventSeal) {
        return toAjax(bdPreventSealService.insertBdPreventSeal(bdPreventSeal));
    }

    /**
     * 修改手机防封号记录
     */
    @ApiOperation(value = "修改手机防封号记录")
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:edit")
    //@Log(title = "手机防封号记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdPreventSeal bdPreventSeal) {
        return toAjax(bdPreventSealService.updateBdPreventSeal(bdPreventSeal));
    }

    /**
     * 删除手机防封号记录
     */
    @ApiOperation(value = "删除手机防封号记录")
    @PreAuthorize(hasPermi = "customer:BdPreventSeal:remove")
    //@Log(title = "手机防封号记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{numIds}")
    public AjaxResult remove(@PathVariable Long[] numIds) {
        return toAjax(bdPreventSealService.deleteBdPreventSealByNumIds(numIds));
    }

    /**
     * 校验电话是否触发防封号
     */
    @ApiOperation(value = "校验电话是否触发防封号")
    @GetMapping("/checkPhone")
    public AjaxResult checkPhone(String phone) {
        Boolean seal = bdPreventSealService.checkBdPreventSealByPhone(phone);

        return seal ? AjaxResult.error("触发防封号规则，无法拨打") : AjaxResult.success();
    }


    /**
     * 校验电话是否触发防封号
     */
    @ApiOperation(value = "校验电话是否触发防封号")
    @GetMapping("/verifyPhoneIsPreventSealByPhone")
    public AjaxResult verifyPhoneIsPreventSealByPhone(String phone) {
        Boolean seal = bdPreventSealService.verifyPhoneIsPreventSealByPhone(phone);

        return seal ? AjaxResult.error("触发防封号规则，无法拨打") : AjaxResult.success();
    }

    /**
     * 检测是否插入防封号
     */
    @ApiOperation(value = "校验电话是否触发防封号")
    @GetMapping("/checkInsertPhone")
    public AjaxResult checkInsertPhone(BdOutbound bdOutbound) {
        Boolean seal = bdPreventSealService.checkInsertPhone(bdOutbound);
        return AjaxResult.success();
    }

    /**
     * 编辑防封号规则
     */
    @ApiOperation(value = "编辑防封号规则")
    @PostMapping("/modifyRule")
    public AjaxResult modifyRule(@RequestBody SealPhone sealPhone) {
        bdPreventSealService.modifyRule(sealPhone);
        return AjaxResult.success();
    }

    /**
     * 查询防封号规则
     */
    @ApiOperation(value = "查询防封号规则")
    @GetMapping("/selectPhoneRule")
    public AjaxResult selectPhoneRule() {
        return AjaxResult.success(bdPreventSealService.selectPhoneRule());
    }

}
