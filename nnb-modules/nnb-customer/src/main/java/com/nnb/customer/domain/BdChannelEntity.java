package com.nnb.customer.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 媒体账户管理配置，实体。
 *
 * <AUTHOR>
 * @date 2022-06-28 13:27:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BdChannel", description = "媒体账户管理配置对象")
public class BdChannelEntity extends BaseEntity {

    /**
     * 主键。
     */
    @ApiModelProperty("主键。")
    private Integer id;

    /**
     * 媒体账号名称。
     */
    @ApiModelProperty("媒体账号名称。")
    private String name;

    /**
     * 备注。
     */
    @ApiModelProperty("备注。")
    private String remark;

    /**
     * 状态 1启用，2停用。
     */
    @ApiModelProperty("状态 1启用，2停用。")
    private Integer status;

    /**
     * 运营方id operator.id。
     */
    @ApiModelProperty("运营方id operator.id。")
    private Integer operatorId;

    /**
     * 投放平台id。
     */
    @ApiModelProperty("投放平台id。")
    private Integer launchPlatformId;

    /**
     * 创建人。
     */
    @ApiModelProperty("创建人。")
    private Integer createdBy;

    /**
     * 更新人。
     */
    @ApiModelProperty("更新人。")
    private Integer updatedBy;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间。
     */
    @ApiModelProperty("更新时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

}
