package com.nnb.customer.domain.chinaUms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 银商查询订单实体类
 * @Date: 2022-09-05
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="QueryReq",description="银商查询订单实体类")
public class QueryReq implements Serializable {

    @ApiModelProperty("消息ID")
    private String msgId;

    @ApiModelProperty("报文请求时间")
    private String requestTimestamp;

    @ApiModelProperty("请求系统预留字段")
    private String srcReserve;

    @ApiModelProperty("商户号")
    private String mid;

    @ApiModelProperty("终端号")
    private String tid;

    @ApiModelProperty("业务类型")
    private String instMid;

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty("退款订单号")
    private String refundOrderId;

    @ApiModelProperty("预授权完成订单号")
    private String authedOrderId;

    @ApiModelProperty("订单时间")
    private String billDate;
}
