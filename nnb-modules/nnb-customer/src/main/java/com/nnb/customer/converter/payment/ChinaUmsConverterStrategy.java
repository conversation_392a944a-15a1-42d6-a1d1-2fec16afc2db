package com.nnb.customer.converter.payment;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.customer.config.ChinaUmsPropertiesConfig;
import com.nnb.customer.constant.PaymentConstants;
import com.nnb.customer.converter.PaymentConverterStrategy;
import com.nnb.customer.domain.OrderInfo;
import com.nnb.customer.domain.OssFileForUploadVO;
import com.nnb.customer.domain.PayCompany;
import com.nnb.customer.domain.PaymentLog;
import com.nnb.customer.domain.chinaUms.request.QrCodeReq;
import com.nnb.customer.domain.vo.PaymentInfoVo;
import com.nnb.customer.mapper.PaymentMapper;
import com.nnb.customer.domain.PaymentInfo;
import com.nnb.customer.service.OssService;
import com.nnb.customer.service.thirdparty.IChinaUmsService;
import com.nnb.customer.utils.FileUtil;
import com.nnb.customer.utils.QRCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description: 银联可重复二维码
 * @Date: 2022-09-07
 * @Version: 1.0
 */
@Slf4j
@Service("2:CHINA_UMS:REUSABLE")
public class ChinaUmsConverterStrategy implements PaymentConverterStrategy<PaymentInfoVo, PaymentInfo> {

    @Autowired
    private OssService ossService;

    @Autowired
    private PaymentMapper paymentMapper;

    @Autowired
    private IChinaUmsService chinaUmsService;

    @Autowired
    private ChinaUmsPropertiesConfig propertiesConfig;

    @Override
    public PaymentInfoVo getPaymentQRCodeInfo(PaymentInfo paymentInfo) {

        String remark = null;
        int opType = 0;
        //定义TradeId流水号
        String randomTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddmmHHssSSS"));
        //入库时候，TradeId就是msgId
        String msgId = propertiesConfig.getMsgSrcId() + randomTime + RandomUtil.randomString(5);
        //入库返回实体类
        PaymentInfoVo paymentInfoVo = new PaymentInfoVo();
        //定义创建时间
        LocalDateTime createTime = LocalDateTime.now();
        LocalDateTime payValidTime = createTime.plusMinutes(propertiesConfig.getExpiredTime());
        //判断是否第一次生成二维码
        PaymentInfoVo queryEntry = new PaymentInfoVo();
        //线索 or 客户提单时
        if (ObjectUtil.isNotEmpty(paymentInfo.getClueId())) {
//            opType = PaymentConstants.OP_TYPE_CLUE;
//            remark = String.format(PaymentConstants.OP_TYPE_OPERATION_CLUE, paymentInfo.getClueId());
            queryEntry.setClueId(paymentInfo.getClueId());
        } else {
//            opType = PaymentConstants.OP_TYPE_CLIENT;
//            remark = String.format(PaymentConstants.OP_TYPE_OPERATION_CLIENT, paymentInfo.getClueId());
            queryEntry.setClientId(paymentInfo.getClientId());
        }

        //此判断对应场景：订单详情生产二维码
        if (ObjectUtil.isEmpty(paymentInfo.getClueId()) && StringUtils.isEmpty(paymentInfo.getClientId())) {
            if (StringUtils.isEmpty(paymentInfo.getOrderId())) {
                throw new ServiceException("线索/客户/订单id都为空！");
            } else {
                opType = PaymentConstants.OP_TYPE_ORDER_DETAIL;
                remark = String.format(PaymentConstants.OP_TYPE_OPERATION_ORDER_DETAIL, paymentInfo.getOrderId());
                queryEntry.setOrderId(paymentInfo.getOrderId());
            }
        }
        queryEntry.setUserId(paymentInfo.getUserId());

        //判断是否第一次生成二维码
        List<PaymentInfo> paymentInfos = paymentMapper.selectPaymentInfoList(queryEntry);
        if (paymentInfos.isEmpty()) {
            //是第一次生成二维码
            getNewQRCode(paymentInfo, msgId, paymentInfoVo, createTime, payValidTime, opType, remark);
        } else {
            //不是第一次生成，先取其中一条记录
            PaymentInfo info = paymentInfos.get(0);

            //先判断是更换支付主体还是重新提单（更换主体、重新提单时，OperationType一定不为空）
            if (ObjectUtil.isNotEmpty(paymentInfo.getOperationType())) {
                //切换主体
                if (1 == paymentInfo.getOperationType()) {
//                    opType = PaymentConstants.OP_TYPE_SWITCH;
//                    remark = String.format(PaymentConstants.OP_TYPE_OPERATION_SWITCH, paymentInfo.getClueId(), paymentInfo.getClientId());
                    //关联上订单号，线索/客户
                    paymentInfo.setOrderId(info.getOrderId());
                    paymentInfo.setOrderNumber(info.getOrderNumber());
                    paymentInfo.setClueId(info.getClueId());
                    paymentInfo.setClientId(info.getClientId());
                    //生成新的二维码
                    getNewQRCode(paymentInfo, msgId, paymentInfoVo, createTime, payValidTime, opType, remark);
                }
                //重新提单
                if (2 == paymentInfo.getOperationType()) {
                    //生成新的二维码
//                    opType = PaymentConstants.OP_TYPE_REBUILD;
//                    remark = String.format(PaymentConstants.OP_TYPE_OPERATION_REBUILD, paymentInfo.getClueId(), paymentInfo.getClientId());
                    getNewQRCode(paymentInfo, msgId, paymentInfoVo, createTime, payValidTime, opType, remark);
                }
            } else if (LocalDateTime.now().isAfter(info.getPayValidTime())) {
                //判断支付二维码是否过期
                //二维码过期，从新生成
                Map<String, Object> response = getQRCode(paymentInfo, msgId, payValidTime);
                //回填返回信息
                paymentInfoVo.setPayValidTime(payValidTime);
                paymentInfoVo.setCreatedTime(createTime);
                paymentInfoVo.setTradeId(MapUtil.getStr(response, "msgId"));
                paymentInfoVo.setPayUrl(MapUtil.getStr(response, "billQRCode"));
                paymentInfoVo.setPayImg(MapUtil.getStr(response, "upload"));
                paymentInfoVo.setQrCodeId(MapUtil.getStr(response, "qrCodeId"));
                paymentInfoVo.setClientId(info.getClientId());
                paymentInfoVo.setClueId(info.getClueId());
                paymentInfoVo.setOrderId(info.getOrderId());
                paymentInfoVo.setOrderNumber(info.getOrderNumber());
                paymentInfoVo.setUserId(paymentInfo.getUserId());
                paymentInfoVo.setPayCompanyId(paymentInfo.getPayCompanyId());
                paymentInfoVo.setPayStatus(PaymentConstants.UN_PAID);
                //插入数据库数据
                paymentMapper.insertPayment(paymentInfoVo);
                //操作记录
//                opType = PaymentConstants.OP_TYPE_OUT_TIME;
//                remark = String.format(PaymentConstants.OP_TYPE_OPERATION_OUT_TIME, paymentInfo.getClueId(), paymentInfo.getClientId());
            } else {
                //直接返回信息
                BeanUtils.copyProperties(info, paymentInfoVo);
                //不是同一条支付记录，不做主键id的返回，只返回二维码信息用于支付，回调时插入新的记录
                paymentInfoVo.setId(null);
            }
        }

        //回填客户信息等到返回结果
        backFillReturnResult(paymentInfo, paymentInfoVo);

        return paymentInfoVo;
    }

    /**
     * 添加操作记录
     *
     * @param payId
     * @param userId
     * @param opType
     * @param remark
     */
    private void addOperationRecord(String payId, Integer userId, Integer opType, String remark) {
        PaymentLog paymentLog = new PaymentLog();
        paymentLog.setPayId(Integer.valueOf(payId));
        paymentLog.setOpType(opType);
        paymentLog.setCreateUser(userId.longValue());
        paymentLog.setCreateTime(LocalDateTime.now());
        paymentLog.setRemark(remark);
        paymentMapper.insertPaymentLog(paymentLog);
    }

    /**
     * 回填客户信息等到返回结果
     *
     * @param paymentInfo
     * @param paymentInfoVo
     */
    private void backFillReturnResult(PaymentInfo paymentInfo, PaymentInfoVo paymentInfoVo) {
        String name = null;
        //此判断对应详情页生成支付码，因为只传了orderId
        if (ObjectUtil.isEmpty(paymentInfo.getClueId()) && StringUtils.isEmpty(paymentInfo.getClientId())) {
            OrderInfo orderInfo = paymentMapper.selectOrderInfoById(paymentInfo.getOrderId());
            if (PaymentConstants.COMMIT_ORDER_TYPE_CLUE.equals(orderInfo.getCommitOrderType())) {
                paymentInfo.setClueId(orderInfo.getClueId());
            } else {
                paymentInfo.setClientId(orderInfo.getClientId());
            }
        }

        if (ObjectUtil.isEmpty(paymentInfo.getClueId())) {
            //企业客户
            name = paymentMapper.selectClientName(paymentInfo.getClientId());
        } else {
            //个人客户
            name = paymentMapper.selectClueName(paymentInfo.getClueId());
        }
        paymentInfoVo.setName(name);

        PayCompany payCompany = paymentMapper.selectPayCompanyMsg(paymentInfo.getPayCompanyId());
        paymentInfoVo.setPayCompanyName(payCompany.getName());
    }

    /**
     * 获取一个新的二维码，并插入记录
     *
     * @param paymentInfo
     * @param msgId
     * @param paymentInfoVo
     * @param createTime
     * @param payValidTime
     */
    private void getNewQRCode(PaymentInfo paymentInfo, String msgId,
                              PaymentInfoVo paymentInfoVo, LocalDateTime createTime,
                              LocalDateTime payValidTime, Integer opType, String remark) {

        //调用第三方
        Map<String, Object> response = getQRCode(paymentInfo, msgId, payValidTime);

        //封装数据，回填返回信息，以及入库
        paymentInfoVo.setPayValidTime(payValidTime);
        paymentInfoVo.setCreatedTime(createTime);
        paymentInfoVo.setTradeId(MapUtil.getStr(response, "msgId"));
        paymentInfoVo.setOrderId(paymentInfo.getOrderId());
        if (StringUtils.isNotEmpty(paymentInfo.getOrderId())) {
            OrderInfo orderInfo = paymentMapper.selectOrderInfoById(paymentInfo.getOrderId());
            paymentInfoVo.setOrderNumber(orderInfo.getOrderNumber());
        }

        paymentInfoVo.setPayUrl(MapUtil.getStr(response, "billQRCode"));
        paymentInfoVo.setPayImg(MapUtil.getStr(response, "upload"));
        paymentInfoVo.setPayStatus(PaymentConstants.UN_PAID);
        paymentInfoVo.setUserId(paymentInfo.getUserId());

        if (ObjectUtil.isEmpty(paymentInfo.getClueId()) && StringUtils.isEmpty(paymentInfo.getClientId())) {
            OrderInfo orderInfo = paymentMapper.selectOrderInfoById(paymentInfo.getOrderId());
            if (PaymentConstants.COMMIT_ORDER_TYPE_CLUE.equals(orderInfo.getCommitOrderType())) {
                paymentInfo.setClueId(orderInfo.getClueId());
                paymentInfoVo.setClueId(orderInfo.getClueId());
            } else {
                paymentInfo.setClientId(orderInfo.getClientId());
                paymentInfoVo.setClientId(orderInfo.getClientId());
            }
        } else {
            paymentInfoVo.setClueId(paymentInfo.getClueId());
            paymentInfoVo.setClientId(paymentInfo.getClientId());
        }

        paymentInfoVo.setPayCompanyId(paymentInfo.getPayCompanyId());
        paymentInfoVo.setQrCodeId(MapUtil.getStr(response, "qrCodeId"));
        //插入记录
        paymentMapper.insertPayment(paymentInfoVo);
        //日志记录
        if (PaymentConstants.OP_TYPE_ORDER_DETAIL == opType) {
            addOperationRecord(paymentInfoVo.getId(), paymentInfo.getUserId(), opType, remark);
        }
    }

    /**
     * 调用第三方获取二维码
     *
     * @param paymentInfo
     * @param msgId
     * @param payValidTime
     * @return
     */
    private Map<String, Object> getQRCode(PaymentInfo paymentInfo, String msgId, LocalDateTime payValidTime) {
        QrCodeReq qrCodeReq = new QrCodeReq();
        qrCodeReq.setMid(paymentInfo.getMid());
        qrCodeReq.setTid(paymentInfo.getTid());
        qrCodeReq.setMsgId(msgId);
        qrCodeReq.setSrcReserve(msgId);
        qrCodeReq.setQRCodeType(PaymentConstants.FIXED_QR_CODE_WITHOUT_AMOUNT);
        qrCodeReq.setExpireTime(payValidTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        Map<String, Object> response = chinaUmsService.getQrCode(qrCodeReq);
        if (!PaymentConstants.CHINA_UMS_SUCCESS.equals(MapUtil.getStr(response, "errCode"))) {
            log.info("银商第三方下单生成二维码返回数据为：{}", JSONUtil.toJsonStr(response));
            throw new ServiceException("订单生成二维码失败，请重试或联系管理员");
        }

        //上传二维码至oss
        File file = QRCodeUtil.toFile(PaymentConstants.CHAR_SET, MapUtil.getStr(response, "billQRCode"), PaymentConstants.QR_WIDTH, PaymentConstants.QR_HEIGHT);
        OssFileForUploadVO upload = ossService.upload(FileUtil.fileToMultipartFile(file), PaymentConstants.MODULE_NAME);

        //放入上传路径
        response.put("upload", upload.getWrapFilePath());

        return response;
    }
}
