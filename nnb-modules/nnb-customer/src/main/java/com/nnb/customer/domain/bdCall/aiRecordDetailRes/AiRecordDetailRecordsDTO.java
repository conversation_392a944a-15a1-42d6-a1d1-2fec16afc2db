package com.nnb.customer.domain.bdCall.aiRecordDetailRes;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-11-04
 * @Version: 1.0
 */
@NoArgsConstructor
@Data
public class AiRecordDetailRecordsDTO implements Serializable {

    @JsonProperty("id")
    private String id;
    @JsonProperty("record_id")
    private String recordId;
    @JsonProperty("task_id")
    private String taskId;
    @JsonProperty("notify")
    private String notify;
    @JsonProperty("callid")
    private String callId;
    @JsonProperty("error_code")
    private Integer errorCode;
    @JsonProperty("asrtype")
    private Object asrtype;
    @JsonProperty("callee")
    private String callee;
    @JsonProperty("gender")
    private Object gender;
    @JsonProperty("asr_elapse")
    private Object asrElapse;
    @JsonProperty("record_ms")
    private Object recordMs;
    @JsonProperty("volume_gain")
    private Object volumeGain;
    @JsonProperty("question")
    private String question;
    @JsonProperty("split_question")
    private Object splitQuestion;
    @JsonProperty("question_index")
    private Integer questionIndex;
    @JsonProperty("speak_ms")
    private Integer speakMs;
    @JsonProperty("play_ms")
    private Integer playMs;
    @JsonProperty("keyword")
    private Object keyword;
    @JsonProperty("answer_id")
    private String answerId;
    @JsonProperty("answer_text")
    private String answerText;
    @JsonProperty("answer_content")
    private String answerContent;
    @JsonProperty("word_class")
    private String wordClass;
    @JsonProperty("answer_action")
    private Object answerAction;
    @JsonProperty("playback_after_action")
    private Integer playbackAfterAction;
    @JsonProperty("upstream_answer_action")
    private Object upstreamAnswerAction;
    @JsonProperty("upstream_answer_content")
    private Object upstreamAnswerContent;
    @JsonProperty("upstream_answer_text")
    private Object upstreamAnswerText;
    @JsonProperty("upstream_answer_id")
    private Object upstreamAnswerId;
    @JsonProperty("score")
    private Integer score;
    @JsonProperty("sequence")
    private Integer sequence;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("bridge_status")
    private Object bridgeStatus;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("is_regular")
    private Integer isRegular;
    @JsonProperty("expression")
    private Object expression;
    @JsonProperty("extra")
    private Object extra;
}
