package com.nnb.customer.service.popularclue;

import com.nnb.customer.domain.popularizeclue.BdPopularizeClueConvert;

import java.util.List;

/**
 * 推广线索-转化Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
public interface IBdPopularizeClueConvertService {
    /**
     * 查询推广线索-转化
     *
     * @param id 推广线索-转化主键
     * @return 推广线索-转化
     */
    public BdPopularizeClueConvert selectBdPopularizeClueConvertById(Long id);

    /**
     * 查询推广线索-转化列表
     *
     * @param bdPopularizeClueConvert 推广线索-转化
     * @return 推广线索-转化集合
     */
    public List<BdPopularizeClueConvert> selectBdPopularizeClueConvertList(BdPopularizeClueConvert bdPopularizeClueConvert);

    /**
     * 新增推广线索-转化
     *
     * @param bdPopularizeClueConvert 推广线索-转化
     * @return 结果
     */
    public int insertBdPopularizeClueConvert(BdPopularizeClueConvert bdPopularizeClueConvert);

    /**
     * 修改推广线索-转化
     *
     * @param bdPopularizeClueConvert 推广线索-转化
     * @return 结果
     */
    public int updateBdPopularizeClueConvert(BdPopularizeClueConvert bdPopularizeClueConvert);

    /**
     * 批量删除推广线索-转化
     *
     * @param ids 需要删除的推广线索-转化主键集合
     * @return 结果
     */
    public int deleteBdPopularizeClueConvertByIds(Long[] ids);

    /**
     * 删除推广线索-转化信息
     *
     * @param id 推广线索-转化主键
     * @return 结果
     */
    public int deleteBdPopularizeClueConvertById(Long id);

    /**
     * 根据推广线索插入转化线索
     */
    public void addPopularizeClueConvert(Integer type);
}
