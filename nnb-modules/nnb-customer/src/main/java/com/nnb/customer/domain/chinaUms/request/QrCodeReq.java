package com.nnb.customer.domain.chinaUms.request;

import com.nnb.customer.domain.chinaUms.Goods;
import com.nnb.customer.domain.chinaUms.SubOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 获取二维码实体类
 * @Date: 2022-09-05
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="QrCodeReq",description="获取二维码实体类")
public class QrCodeReq implements Serializable {

    @ApiModelProperty("消息ID")
    private String msgId;

    @ApiModelProperty("报文请求时间")
    private String requestTimestamp;

    @ApiModelProperty("请求系统预留字段")
    private String srcReserve;

    @ApiModelProperty("商户号")
    private String mid;

    @ApiModelProperty("终端号")
    private String tid;

    @ApiModelProperty("业务类型")
    private String instMid;

    @ApiModelProperty("账单号,// 商户订单号 {来源编号(4位)}{时间(yyyyMMddmmHHssSSS)(17位)}{7位随机数}")
    private String billNo;

    @ApiModelProperty("账单日期")
    private String billDate;

    @ApiModelProperty("账单描述")
    private String billDesc;

    @ApiModelProperty("支付总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("分账标记")
    private boolean divisionFlag;

    @ApiModelProperty("平台商户分账金额")
    private BigDecimal platformAmount;

    @ApiModelProperty("请求系统预留字段")
    private List<Goods> goods;

    @ApiModelProperty("子订单信息")
    private SubOrder subOrders;

    @ApiModelProperty("会员号")
    private String memberId;

    @ApiModelProperty("桌号、柜台号、房间号")
    private String counterNo;

    @ApiModelProperty("账单过期时间")
    private String expireTime;

    @ApiModelProperty("支付结果通知地址")
    private String notifyUrl;

    @ApiModelProperty("支付结果回跳商户页面")
    private String returnUrl;

    @ApiModelProperty("二维码 ID ")
    private String qrCodeId;

    @ApiModelProperty("系统 ID ")
    private String systemId;

    @ApiModelProperty("担保交易标识")
    private String secureTransaction;

    @ApiModelProperty("钱包选项")
    private String walletOption;

    @ApiModelProperty("实名认证姓名")
    private String name;

    @ApiModelProperty("实名认证手机号")
    private String mobile;

    @ApiModelProperty("实名认证证件类型")
    private String certType;

    @ApiModelProperty("实名认证证件号")
    private String certNo;

    @ApiModelProperty("是否需要实名认证")
    private String fixBuyer;

    @ApiModelProperty("是否需要限制信用卡支付")
    private String limitCreditCard;

    @ApiModelProperty("预授权交易标识 ")
    private String preauthTransaction;

    @ApiModelProperty("花呗分期数 ")
    private String installmentNumber;

    @ApiModelProperty("扫码点餐信息")
    private String retCommParams;

    @ApiModelProperty("是否开启第三方分期交易贴息")
    private boolean thirdPartyInstalSubsFlag;

    @ApiModelProperty("异步分账标记")
    private boolean asynDivisionFlag;

    @ApiModelProperty("是否支持花呗分期")
    private String supportInstalFlag;

    @ApiModelProperty("二维码类型")
    private int qRCodeType;

}
