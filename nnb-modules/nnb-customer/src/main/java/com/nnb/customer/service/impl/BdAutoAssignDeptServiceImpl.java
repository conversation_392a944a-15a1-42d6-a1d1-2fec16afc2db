package com.nnb.customer.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdAutoAssignDeptMapper;
import com.nnb.customer.domain.BdAutoAssignDept;
import com.nnb.customer.service.IBdAutoAssignDeptService;

/**
 * 线索自动分配流转-部门关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-08-02
 */
@Service
public class BdAutoAssignDeptServiceImpl implements IBdAutoAssignDeptService 
{
    @Autowired
    private BdAutoAssignDeptMapper bdAutoAssignDeptMapper;

    /**
     * 查询线索自动分配流转-部门关系
     * 
     * @param autoAssignId 线索自动分配流转-部门关系主键
     * @return 线索自动分配流转-部门关系
     */
    @Override
    public BdAutoAssignDept selectBdAutoAssignDeptByAutoAssignId(Long autoAssignId)
    {
        return bdAutoAssignDeptMapper.selectBdAutoAssignDeptByAutoAssignId(autoAssignId);
    }

    /**
     * 查询线索自动分配流转-部门关系列表
     * 
     * @param bdAutoAssignDept 线索自动分配流转-部门关系
     * @return 线索自动分配流转-部门关系
     */
    @Override
    public List<BdAutoAssignDept> selectBdAutoAssignDeptList(BdAutoAssignDept bdAutoAssignDept)
    {
        return bdAutoAssignDeptMapper.selectBdAutoAssignDeptList(bdAutoAssignDept);
    }

    /**
     * 新增线索自动分配流转-部门关系
     * 
     * @param bdAutoAssignDept 线索自动分配流转-部门关系
     * @return 结果
     */
    @Override
    public int insertBdAutoAssignDept(BdAutoAssignDept bdAutoAssignDept)
    {
        return bdAutoAssignDeptMapper.insertBdAutoAssignDept(bdAutoAssignDept);
    }

    /**
     * 修改线索自动分配流转-部门关系
     * 
     * @param bdAutoAssignDept 线索自动分配流转-部门关系
     * @return 结果
     */
    @Override
    public int updateBdAutoAssignDept(BdAutoAssignDept bdAutoAssignDept)
    {
        return bdAutoAssignDeptMapper.updateBdAutoAssignDept(bdAutoAssignDept);
    }

    /**
     * 批量删除线索自动分配流转-部门关系
     * 
     * @param autoAssignIds 需要删除的线索自动分配流转-部门关系主键
     * @return 结果
     */
    @Override
    public int deleteBdAutoAssignDeptByAutoAssignIds(Long[] autoAssignIds)
    {
        return bdAutoAssignDeptMapper.deleteBdAutoAssignDeptByAutoAssignIds(autoAssignIds);
    }

    /**
     * 删除线索自动分配流转-部门关系信息
     * 
     * @param autoAssignId 线索自动分配流转-部门关系主键
     * @return 结果
     */
    @Override
    public int deleteBdAutoAssignDeptByAutoAssignId(Long autoAssignId)
    {
        return bdAutoAssignDeptMapper.deleteBdAutoAssignDeptByAutoAssignId(autoAssignId);
    }
}
