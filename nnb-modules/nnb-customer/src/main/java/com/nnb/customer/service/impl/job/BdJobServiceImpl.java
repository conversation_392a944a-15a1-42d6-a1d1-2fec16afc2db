package com.nnb.customer.service.impl.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.common.api.model.DingMessageDTO;
import com.common.api.service.DingService;
import com.common.xxlJob.config.XxlJobProperties;
import com.common.xxlJob.model.XxlJobGroup;
import com.common.xxlJob.model.XxlJobInfo;
import com.common.xxlJob.service.XxlJobService;
import com.common.xxlJob.utils.CronUtils;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.customer.constant.BdJobConstants;
import com.nnb.customer.constant.CustomerBdConstants;
import com.nnb.customer.constant.ESTablesConstants;
import com.nnb.customer.domain.BatchUpdateParam;
import com.nnb.customer.domain.BdClue;
import com.nnb.customer.domain.BdClueUserTags;
import com.nnb.customer.enums.ClueStatusEnum;
import com.nnb.customer.mapper.BdClueMapper;
import com.nnb.customer.mapper.BdClueUserTagsMapper;
import com.nnb.customer.mapper.BdCustomerJobMapper;
import com.nnb.customer.mapper.BdCustomersFollowMapper;
import com.nnb.customer.service.IBdClueService;
import com.nnb.customer.service.job.IBdJobService;
import com.nnb.customer.utils.EsClueSyncUtil;
import com.nnb.system.api.RemoteJobService;
import com.nnb.system.api.domain.BdCustomerJob;
import com.nnb.system.api.domain.*;
import com.xxl.job.core.glue.GlueTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-04
 * @Version: 1.0
 */
@Slf4j
@Service
public class BdJobServiceImpl implements IBdJobService {

    @Value("${crm.url}")
    private String crmUrl;

    //适用部门
    @Value("${taskJob.numberDept.numberDeptIds}")
    private String numberDeptIds;

    //线索掉入的个人id
    @Value("${taskJob.numberDept.fallInToUserId}")
    private String fallInToUserId;

    //掉入商机池标签
    @Value("${taskJob.numberDept.numberTagId}")
    private String numberTagId;

    //小部门经理
    @Value("${taskJob.numberDept.smallLeader}")
    private String smallLeader;

    @Resource
    private XxlJobProperties properties;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private RemoteJobService remoteJobService;

    @Resource
    private DingService dingService;

    private BdCustomersFollowMapper bdCustomersFollowMapper;

    private IBdClueService bdClueService;

    private BdCustomerJobMapper bdCustomerJobMapper;

    private EsClueSyncUtil esClueSyncUtil;

    private BdClueUserTagsMapper bdClueUserTagsMapper;

    private BdClueMapper bdClueMapper;

    @Override
    public Boolean createCustomerJob(BdCustomerJob bdCustomerJob) {
        //调用job服务
        R<Integer> resultR = remoteJobService.addCustomerJob(bdCustomerJob, SecurityConstants.INNER);
        if (resultR.getCode() == 200) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean sendDingTalkNotifications(DingTalkMsgDTO dingTalkMsgDTO) {
        log.info("定时任务 sendDingTalkNotifications 执行，参数：{}", JSONUtil.toJsonStr(dingTalkMsgDTO));
        //判断此线索需不需要发钉钉消息，依据：查询此线索的跟进记录（有写跟进记录才算跟进 numStatus = 20）
        List<Long> idList = bdCustomersFollowMapper.selectHasFollow(
                dingTalkMsgDTO.getClueId(), dingTalkMsgDTO.getBdCustomersFollowId()
        );
        //处理10分钟的
        handleCustomerTenMinutes(dingTalkMsgDTO, idList);
        //处理50分钟
        handleCustomerFiftyMinutes(dingTalkMsgDTO, idList);
        //处理30分钟的
        handleCustomerThirtyMinutes(dingTalkMsgDTO, idList);
        //处理25分钟的
        handleCustomerTwentyFiveMinutes(dingTalkMsgDTO, idList);
        return Boolean.TRUE;
    }

    private void handleCustomerTwentyFiveMinutes(DingTalkMsgDTO dingTalkMsgDTO, List<Long> idList) {
        if (DingTalkMsgDTO.DingTalkTypeEnum.TWENTY_FIVE.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
            Date date = new Date();
            //处理50分钟，不仅要给自己发还要给经理发，没有跟进记录
            if (CollUtil.isEmpty(idList)) {
                //发送钉钉消息 - 个人
                sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG_FALLING_OUT);
                //发送钉钉消息 - 经理（查询经理信息）
                sendDingTalkMessagesManager(dingTalkMsgDTO, date, BdJobConstants.DING_TALK_MSG_FALLING_OUT);
                //30分钟再通知一遍
                createCustomerThirtyMinutesTask(dingTalkMsgDTO,
                        CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(5))
                );
            } else {
                //跳到半小时判定，5分钟后执行，看有没有加入客保
                createCustomerThirtyMinutesTask(dingTalkMsgDTO,
                        CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(5))
                );
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_TWENTY_FIVE_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_TWENTY_FIVE_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
    }

    private void handleCustomerThirtyMinutes(DingTalkMsgDTO dingTalkMsgDTO, List<Long> idList) {
        if (DingTalkMsgDTO.DingTalkTypeEnum.THIRTY.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
            Date date = new Date();
            //处理30分钟，不仅要给自己发还要给经理发，没有跟进记录，并且直接下掉
            if (CollUtil.isEmpty(idList)) {
                //发送钉钉消息 - 个人
                sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG_OUT);
                //发送钉钉消息 - 经理（查询经理信息）
                sendDingTalkMessagesManager(dingTalkMsgDTO, date, BdJobConstants.DING_TALK_MSG_OUT);
                //给掉入个人发消息
                String dingUserId = bdCustomerJobMapper.selectDingUserIdByUserId(Long.parseLong(fallInToUserId));
                dingTalkMsgDTO.setDingTalkUserId(dingUserId);
                sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG_OUT_PERSON);
                //未跟进的线索 掉至苑顺凤线索管理
                fallInToPerson(Long.parseLong(fallInToUserId), dingTalkMsgDTO.getClueId());
                //创建定时任务 个人当天未加入客保的 掉入商机池
                theSameDayInToNichePool(dingTalkMsgDTO.getClueId(), dingTalkMsgDTO.getBdCustomersFollowId());
            } else {
                //创建掉入个人定时任务
                FallIntoPersonDTO fallIntoPersonDTO = new FallIntoPersonDTO(
                        dingTalkMsgDTO.getClueId(), Long.parseLong(fallInToUserId),
                        dingTalkMsgDTO.getBdCustomersFollowId(),
                        FallIntoPersonDTO.TypeEnum.THE_SAME_DAY.getType()
                );
                //构建今晚时间
                LocalTime localTime = LocalTime.of(0, 0, 0); // 例如，设置时间为10:30:45
                LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), localTime).plusDays(1);
                createFallIntoPersonTask(
                        BdJobConstants.TaskEnum.CUSTOMER_PERSON.getName(),
                        BdJobConstants.TaskEnum.CUSTOMER_PERSON.getGroup(),
                        fallIntoPersonDTO, localDateTime
                );
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_THIRTY_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_THIRTY_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
    }

    private void theSameDayInToNichePool(Long clueId, Long followId) {
        FallIntoNichePoolDTO fallIntoNichePoolDTO = new FallIntoNichePoolDTO(
                clueId, followId, FallIntoNichePoolDTO.TypeEnum.THE_SAME_DAY.getType()
        );
        //构建今晚时间
        LocalTime localTime = LocalTime.of(0, 0, 0); // 例如，设置时间为10:30:45
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), localTime).plusDays(1);
        createFallIntoNichePoolTask(
                fallIntoNichePoolDTO, BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL.getGroup(), localDateTime
        );
    }

    private void createFallIntoNichePoolTask(FallIntoNichePoolDTO fallIntoNichePoolDTO,
                                             String taskName, String taskGroup, LocalDateTime localDateTime) {
        //构建任务参数
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                taskName, taskGroup,
                String.format(
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_NICHE_POOL.getMethodName(),
                        fallIntoNichePoolDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(localDateTime),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                fallIntoNichePoolDTO.getClueId(), fallIntoNichePoolDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(fallIntoNichePoolDTO),
                BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_NICHE_POOL.getBeanHandleName()
        );
    }

    private void createFallIntoPersonTask(String taskName, String taskGroup,
                                          FallIntoPersonDTO fallIntoPersonDTO, LocalDateTime localDateTime) {
        //已跟进的线索，未加入客保当天24点掉，启动新的定时任务
        //构建任务参数
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                taskName, taskGroup,
                String.format(
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_PERSON.getMethodName(),
                        fallIntoPersonDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(localDateTime),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                fallIntoPersonDTO.getClueId(), fallIntoPersonDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(fallIntoPersonDTO),
                BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_PERSON.getBeanHandleName()
        );
    }

    private void handleCustomerFiftyMinutes(DingTalkMsgDTO dingTalkMsgDTO, List<Long> idList) {
        if (DingTalkMsgDTO.DingTalkTypeEnum.FIFTY.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
            Date date = new Date();
            //处理50分钟，不仅要给自己发还要给经理发，没有跟进记录
            if (CollUtil.isEmpty(idList)) {
                //发送钉钉消息 - 个人
                sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG_FALLING_OUT);
                //发送钉钉消息 - 经理（查询经理信息）
                sendDingTalkMessagesManager(dingTalkMsgDTO, date, BdJobConstants.DING_TALK_MSG_FALLING_OUT);
                //更新 executionStatus 为执行
                bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                        BdJobConstants.TaskEnum.CUSTOMER_FIFTY_MINUTES.getGroup(),
                        dingTalkMsgDTO.getClueId(),
                        BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
                );
            }
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_FIFTY_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
    }

    private void sendDingTalkMessagesManager(DingTalkMsgDTO dingTalkMsgDTO, Date date, String template) {
        SysDept sysDept = bdCustomerJobMapper.selectSysDeptByDingId(dingTalkMsgDTO.getDingTalkUserId());
        //25分钟，30分钟小部门经理发送消息
        if (DingTalkMsgDTO.DingTalkTypeEnum.TWENTY_FIVE.getValue().equals(dingTalkMsgDTO.getDingTalkType())
                || DingTalkMsgDTO.DingTalkTypeEnum.THIRTY.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
            //小部门经理
            SysDept finalSysDept = sysDept;
            Arrays.stream(smallLeader.split("\\|"))
                    .filter(config -> config.split("&")[0].equals(finalSysDept.getDeptId().toString())
                            || finalSysDept.getAncestors().contains(config.split("&")[0])
                    ).findAny()
                    .ifPresent(config -> {
                        dingTalkMsgDTO.setDingTalkUserId(config.split("&")[1]);
                        //小经理发送钉钉消息
                        sendDingTalkMessages(dingTalkMsgDTO, template);
                    });
        }
        while (StrUtil.isEmpty(sysDept.getLeader())) {
            sysDept = bdCustomerJobMapper.selectSysDeptByDeptId(sysDept.getParentId());
            if (sysDept.getParentId() == 0) {
                break;
            }
        }
        if (StrUtil.isNotEmpty(sysDept.getLeader())) {
            SysUser sysUser = bdCustomerJobMapper.selectDingUserIdByEmail(sysDept.getLeader());
            dingTalkMsgDTO.setDingTalkUserId(sysUser.getDingUserId());
            //经理发送钉钉消息
            sendDingTalkMessages(dingTalkMsgDTO, template);
            //走50分钟定时的为 通用部门
            if (DingTalkMsgDTO.DingTalkTypeEnum.FIFTY.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
                //创建定时任务，10分钟后执行 - 分配给部门经理/负责人
                createCustomerManagerTask(dingTalkMsgDTO, sysUser, date);
            }
        } else {
            log.error("未查询到部门领导，clueId【{}】，个人dingId【{}】",
                    dingTalkMsgDTO.getClueId(), dingTalkMsgDTO.getDingTalkUserId()
            );
        }
    }

    private void handleCustomerTenMinutes(DingTalkMsgDTO dingTalkMsgDTO, List<Long> idList) {
        if (DingTalkMsgDTO.DingTalkTypeEnum.TEN.getValue().equals(dingTalkMsgDTO.getDingTalkType())) {
            Date date = new Date();
            //分部门判定，数字部门
            boolean match = hasNumberUser(dingTalkMsgDTO);
            //处理10分钟的，没有跟进记录
            if (CollUtil.isEmpty(idList)) {
                //发送钉钉消息
                sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG);
                //创建定时任务，分部门判定，数字部门的15分钟后执行，其他的40分钟后执行
                if (match) {
                    //15分钟后执行
                    createCustomerTwentyFiveMinutesTask(dingTalkMsgDTO, date);
                } else {
                    //40分钟后执行
                    createCustomerFiftyMinutesTask(dingTalkMsgDTO, date);
                }
            } else {
                //有跟进记录，且为数字部门时
                if (match) {
                    //跳到半小时判定，20分钟后执行，看有没有加入客保
                    createCustomerThirtyMinutesTask(dingTalkMsgDTO,
                            CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(20))
                    );
                }
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_TEN_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_TEN_MINUTES.getGroup(),
                    dingTalkMsgDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
    }

    private boolean hasNumberUser(DingTalkMsgDTO dingTalkMsgDTO) {
        SysUser sysUser = bdCustomerJobMapper.selectUserByDingUserId(dingTalkMsgDTO.getDingTalkUserId());
        List<String> userDeptIds = Arrays.asList(sysUser.getAncestors().split(","));
        //判断是否包含此部门
        return userDeptIds.stream()
                .anyMatch(deptId -> Arrays.asList(numberDeptIds.split(",")).contains(deptId)
                        || sysUser.getDeptId().equals(Long.parseLong(deptId)));
    }

    private void createCustomerManagerTask(DingTalkMsgDTO dingTalkMsgDTO, SysUser sysUser, Date date) {
        DistributionClueDTO distributionClueDTO = new DistributionClueDTO(
                dingTalkMsgDTO.getClueId(), sysUser.getUserId(), dingTalkMsgDTO.getBdCustomersFollowId()
        );
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                BdJobConstants.TaskEnum.CUSTOMER_MANAGER.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_MANAGER.getGroup(),
                String.format(
                        BdJobConstants.InvokeTargetEnum.TRANSFER_CLUES_TO_THE_MANAGER.getMethodName(),
                        distributionClueDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(10)),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                dingTalkMsgDTO.getClueId(), dingTalkMsgDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(distributionClueDTO),
                BdJobConstants.InvokeTargetEnum.TRANSFER_CLUES_TO_THE_MANAGER.getBeanHandleName()
        );
    }

    private void createCustomerTwentyFiveMinutesTask(DingTalkMsgDTO dingTalkMsgDTO, Date date) {
        //copy 一个
        DingTalkMsgDTO dingTalkMsgTenDTO = new DingTalkMsgDTO();
        BeanUtils.copyProperties(dingTalkMsgDTO, dingTalkMsgTenDTO);
        //放入属性
        dingTalkMsgTenDTO.setDingTalkType(DingTalkMsgDTO.DingTalkTypeEnum.TWENTY_FIVE.getValue());
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                BdJobConstants.TaskEnum.CUSTOMER_TWENTY_FIVE_MINUTES.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_TWENTY_FIVE_MINUTES.getGroup(),
                String.format(
                        BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getMethodName(),
                        dingTalkMsgTenDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(15)),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                dingTalkMsgTenDTO.getClueId(), dingTalkMsgTenDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(dingTalkMsgTenDTO),
                BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getBeanHandleName()
        );
    }

    private void createCustomerThirtyMinutesTask(DingTalkMsgDTO dingTalkMsgDTO, String cronTime) {
        //copy 一个
        DingTalkMsgDTO dingTalkMsgTenDTO = new DingTalkMsgDTO();
        BeanUtils.copyProperties(dingTalkMsgDTO, dingTalkMsgTenDTO);
        //放入属性
        dingTalkMsgTenDTO.setDingTalkType(DingTalkMsgDTO.DingTalkTypeEnum.THIRTY.getValue());
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                BdJobConstants.TaskEnum.CUSTOMER_THIRTY_MINUTES.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_THIRTY_MINUTES.getGroup(),
                String.format(
                        BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getMethodName(),
                        dingTalkMsgTenDTO.getInvokeTargetParams()
                ),
                cronTime,
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                dingTalkMsgTenDTO.getClueId(), dingTalkMsgTenDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(dingTalkMsgTenDTO),
                BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getBeanHandleName()
        );
    }

    private void createCustomerFiftyMinutesTask(DingTalkMsgDTO dingTalkMsgDTO, Date date) {
        //copy 一个
        DingTalkMsgDTO dingTalkMsgTenDTO = new DingTalkMsgDTO();
        BeanUtils.copyProperties(dingTalkMsgDTO, dingTalkMsgTenDTO);
        //放入属性
        dingTalkMsgTenDTO.setDingTalkType(DingTalkMsgDTO.DingTalkTypeEnum.FIFTY.getValue());
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                BdJobConstants.TaskEnum.CUSTOMER_FIFTY_MINUTES.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_FIFTY_MINUTES.getGroup(),
                String.format(
                        BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getMethodName(),
                        dingTalkMsgTenDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusMinutes(40)),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                dingTalkMsgTenDTO.getClueId(), dingTalkMsgTenDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(dingTalkMsgTenDTO),
                BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getBeanHandleName()
        );
    }

    @Override
    public Boolean distributionClueManager(DistributionClueDTO distributionClueDTO) {
        log.info("定时任务 distributionClueManager 执行，参数：{}", JSONUtil.toJsonStr(distributionClueDTO));
        Date date = new Date();
        //处理50分钟，不仅要给自己发还要给经理发
        List<Long> idList = bdCustomersFollowMapper.selectHasFollow(
                distributionClueDTO.getClueId(), distributionClueDTO.getBdCustomersFollowId()
        );
        handleCustomerManager(distributionClueDTO, idList, date);
        return Boolean.TRUE;
    }

    private void handleCustomerManager(DistributionClueDTO distributionClueDTO, List<Long> idList, Date date) {
        //没有跟进记录
        if (CollUtil.isEmpty(idList)) {
            //线索移交给部门经理
            fallInToPerson(distributionClueDTO.getUserId(), distributionClueDTO.getClueId());
            //创建定时任务，一个小时后掉入公海
            createCustomerIntoCommonTask(distributionClueDTO, date);
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_MANAGER.getGroup(),
                    distributionClueDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
        }
        //更新 status 为暂停 任务状态（0正常 1暂停）
        bdCustomerJobMapper.updateStatusByGroupAndClueId(
                BdJobConstants.TaskEnum.CUSTOMER_MANAGER.getGroup(),
                distributionClueDTO.getClueId(),
                BdCustomerJob.StatusEnum.SUSPEND.getValue()
        );
    }

    private void fallInToPerson(Long userId, Long clueId) {
        BatchUpdateParam batchUpdateParam = new BatchUpdateParam();
        batchUpdateParam.setInside(BdJobConstants.INTERNAL_CALL);
        batchUpdateParam.setActType(CustomerBdConstants.ActType.PERSONAL);
        BdClue bdClue = new BdClue();
        bdClue.setFollowUserId(userId);
        batchUpdateParam.setBdClue(bdClue);
        List<Long> clueList = new ArrayList<>();
        clueList.add(clueId);
        batchUpdateParam.setBdClueIdList(clueList);
        bdClueService.batchUpdateBdClue(batchUpdateParam);
        //更新es
        esClueSyncUtil.syncBdClueEs(
                Collections.singletonList(clueId),
                ESTablesConstants.BD_CLUE, ESTablesConstants.BD_CLUE_DETAIL,
                ESTablesConstants.BD_CLUE_STATISTICS, ESTablesConstants.BD_CLUE_USER_TAGS
        );
    }

    private void createCustomerIntoCommonTask(DistributionClueDTO distributionClueDTO, Date date) {
        FallIntoSeaDTO fallIntoSeaDTO = new FallIntoSeaDTO(
                distributionClueDTO.getClueId(), distributionClueDTO.getBdCustomersFollowId()
        );
        BdCustomerJob bdCustomerJob = new BdCustomerJob(
                BdJobConstants.TaskEnum.CUSTOMER_INTO_COMMON.getName(),
                BdJobConstants.TaskEnum.CUSTOMER_INTO_COMMON.getGroup(),
                String.format(
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_SEA.getMethodName(),
                        fallIntoSeaDTO.getInvokeTargetParams()
                ),
                CronUtils.generateCron(DateUtil.toLocalDateTime(date).plusHours(1)),
                BdCustomerJob.MisfirePolicyEnum.ABANDONING_EXECUTION.getValue(),
                BdCustomerJob.ConcurrentEnum.ALLOW.getValue(),
                distributionClueDTO.getClueId(), distributionClueDTO.getBdCustomersFollowId()
        );
        this.createCustomerJob(bdCustomerJob);
        //创建xxl-job
        createXxlJobTask(
                bdCustomerJob, JSONUtil.toJsonStr(fallIntoSeaDTO),
                BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_SEA.getBeanHandleName()
        );
    }

    @Override
    public Boolean fallIntoTheSea(FallIntoSeaDTO fallIntoSeaDTO) {
        log.info("定时任务 fallIntoTheSea 执行，参数：{}", JSONUtil.toJsonStr(fallIntoSeaDTO));
        //部门经理一个小时未处理的掉到推广线索公海
        List<Long> idList = bdCustomersFollowMapper.selectHasFollow(
                fallIntoSeaDTO.getClueId(), fallIntoSeaDTO.getBdCustomersFollowId()
        );
        //没有跟进记录
        if (CollUtil.isEmpty(idList)) {
            //掉入公海
            bdClueService.toCommon(new ArrayList<>(Collections.singletonList(fallIntoSeaDTO.getClueId())));
            //更新es
            esClueSyncUtil.syncBdClueEs(
                    Collections.singletonList(fallIntoSeaDTO.getClueId()),
                    ESTablesConstants.BD_CLUE, ESTablesConstants.BD_CLUE_DETAIL, ESTablesConstants.BD_CLUE_STATISTICS
            );
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_INTO_COMMON.getGroup(),
                    fallIntoSeaDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
        }
        //更新 status 为暂停 任务状态（0正常 1暂停）
        bdCustomerJobMapper.updateStatusByGroupAndClueId(
                BdJobConstants.TaskEnum.CUSTOMER_INTO_COMMON.getGroup(),
                fallIntoSeaDTO.getClueId(),
                BdCustomerJob.StatusEnum.SUSPEND.getValue()
        );
        return Boolean.TRUE;
    }

    @Override
    public Boolean fallIntoThePerson(FallIntoPersonDTO fallIntoPersonDTO) {
        log.info("定时任务 fallIntoThePerson 执行，参数：{}", JSONUtil.toJsonStr(fallIntoPersonDTO));
        if (FallIntoPersonDTO.TypeEnum.THE_SAME_DAY.getType().equals(fallIntoPersonDTO.getType())) {
            //判断此线索有没有加入过客保
            List<Long> idList = bdCustomersFollowMapper.selectHasInCustomer(
                    fallIntoPersonDTO.getClueId(), fallIntoPersonDTO.getBdCustomersFollowId()
            );
            if (CollUtil.isEmpty(idList)) {
                //没有加入过客保掉入个人
                fallInToPerson(fallIntoPersonDTO.getUserId(), fallIntoPersonDTO.getClueId());
                //创建定时任务掉入到个人后，当天未加入客保的 24点掉入商机池
                theSameDayInToNichePool(fallIntoPersonDTO.getClueId(), fallIntoPersonDTO.getBdCustomersFollowId());
            } else {
                //加入过客保 创建定时任务三天后未成交的 24点掉入个人
                FallIntoPersonDTO fallIntoPersonDTOJob = new FallIntoPersonDTO(
                        fallIntoPersonDTO.getClueId(), Long.parseLong(fallInToUserId),
                        fallIntoPersonDTO.getBdCustomersFollowId(),
                        FallIntoPersonDTO.TypeEnum.THREE_DAYS_LATER.getType()
                );
                //构建今晚时间
                LocalTime localTime = LocalTime.of(0, 0, 0); // 例如，设置时间为10:30:45
                LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), localTime).plusDays(3);
                createFallIntoPersonTask(
                        BdJobConstants.TaskEnum.CUSTOMER_PERSON_THREE.getName(),
                        BdJobConstants.TaskEnum.CUSTOMER_PERSON_THREE.getGroup(),
                        fallIntoPersonDTOJob, localDateTime);
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_PERSON.getGroup(),
                    fallIntoPersonDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_PERSON.getGroup(),
                    fallIntoPersonDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        } else {
            //判断又没有成交
            BdClue bdClue = bdClueMapper.selectBdClueById(fallIntoPersonDTO.getClueId());
            if (!ClueStatusEnum.CLOSED.getStatus().equals(bdClue.getNumStatus())) {
                //没有成交掉入个人
                fallInToPerson(fallIntoPersonDTO.getUserId(), fallIntoPersonDTO.getClueId());
                //创建定时任务掉入到个人后，3天未成交的 24点掉入商机池
                theSameDayInToNichePool(fallIntoPersonDTO.getClueId(), fallIntoPersonDTO.getBdCustomersFollowId());
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_PERSON_THREE.getGroup(),
                    fallIntoPersonDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_PERSON_THREE.getGroup(),
                    fallIntoPersonDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean fallIntoTheNichePool(FallIntoNichePoolDTO fallIntoNichePoolDTO) {
        log.info("定时任务 fallIntoTheNichePool 执行，参数：{}", JSONUtil.toJsonStr(fallIntoNichePoolDTO));
        if (fallIntoNichePoolDTO.getType().equals(FallIntoNichePoolDTO.TypeEnum.THE_SAME_DAY.getType())) {
            //判断此线索又没有加入过客保
            List<Long> idList = bdCustomersFollowMapper.selectHasInCustomer(
                    fallIntoNichePoolDTO.getClueId(), fallIntoNichePoolDTO.getBdCustomersFollowId()
            );
            if (CollUtil.isEmpty(idList)) {
                //没有加入过客保掉入商机池（打上单独的标签），创建池子后，从池子里搜索
                BdClueUserTags bdClueUserTags = new BdClueUserTags();
                bdClueUserTags.setNumClueId(fallIntoNichePoolDTO.getClueId());
                bdClueUserTags.setNumUserTagId(Long.parseLong(numberTagId));
                bdClueUserTagsMapper.insertBdClueUserTags(bdClueUserTags);
            } else {
                //加入过客保 创建定时任务三天后未成交的 24点掉入商机池
                FallIntoNichePoolDTO fallIntoPersonDTO = new FallIntoNichePoolDTO(
                        fallIntoNichePoolDTO.getClueId(), fallIntoNichePoolDTO.getBdCustomersFollowId(),
                        FallIntoNichePoolDTO.TypeEnum.THREE_DAYS_LATER.getType()
                );
                //构建今晚时间
                LocalTime localTime = LocalTime.of(0, 0, 0); // 例如，设置时间为10:30:45
                LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), localTime).plusDays(3);
                createFallIntoNichePoolTask(
                        fallIntoPersonDTO, BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL_THREE.getName(),
                        BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL_THREE.getGroup(), localDateTime
                );
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL.getGroup(),
                    fallIntoNichePoolDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL.getGroup(),
                    fallIntoNichePoolDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        } else {
            //判断又没有成交
            BdClue bdClue = bdClueMapper.selectBdClueById(fallIntoNichePoolDTO.getClueId());
            if (!ClueStatusEnum.CLOSED.getStatus().equals(bdClue.getNumStatus())) {
                //未成交掉入商机池
                BdClueUserTags bdClueUserTags = new BdClueUserTags();
                bdClueUserTags.setNumClueId(fallIntoNichePoolDTO.getClueId());
                bdClueUserTags.setNumUserTagId(Long.parseLong(numberTagId));
                bdClueUserTagsMapper.insertBdClueUserTags(bdClueUserTags);
            }
            //更新 executionStatus 为执行
            bdCustomerJobMapper.updateExecutionStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL_THREE.getGroup(),
                    fallIntoNichePoolDTO.getClueId(),
                    BdCustomerJob.ExecutionStatusEnum.EXECUTE.getValue()
            );
            //更新 status 为暂停 任务状态（0正常 1暂停）
            bdCustomerJobMapper.updateStatusByGroupAndClueId(
                    BdJobConstants.TaskEnum.CUSTOMER_INTO_NICHE_POOL_THREE.getGroup(),
                    fallIntoNichePoolDTO.getClueId(),
                    BdCustomerJob.StatusEnum.SUSPEND.getValue()
            );
        }
        //更新es
        esClueSyncUtil.syncBdClueEs(
                Collections.singletonList(fallIntoNichePoolDTO.getClueId()),
                ESTablesConstants.BD_CLUE, ESTablesConstants.BD_CLUE_DETAIL,
                ESTablesConstants.BD_CLUE_STATISTICS, ESTablesConstants.BD_CLUE_USER_TAGS
        );
        return Boolean.TRUE;
    }

    private void createXxlJobTask(BdCustomerJob bdCustomerJob, String paramsJson, String executorHandler) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setJobDesc(bdCustomerJob.getJobName());
        xxlJobInfo.setAuthor(BdJobConstants.INNER_SYSTEM);
        xxlJobInfo.setScheduleConf(bdCustomerJob.getCronExpression());
        xxlJobInfo.setGlueType(GlueTypeEnum.BEAN.getDesc());
        xxlJobInfo.setExecutorHandler(executorHandler);
        xxlJobInfo.setExecutorParam(paramsJson);
        XxlJobGroup xxlJobGroup = new XxlJobGroup();
        xxlJobGroup.setAppname(properties.getExecutor().getAppname());
        xxlJobService.addAndStart(xxlJobInfo, xxlJobGroup);
    }

    @Override
    public String sendDingMessageTest() {
        DingTalkMsgDTO dingTalkMsgDTO = new DingTalkMsgDTO(21116016L, 120849521L,
                "cccccc", "推广账户来源-分配引擎", "手机号为：17601683550",
                "16618221903342648", 2);
        sendDingTalkMessages(dingTalkMsgDTO, BdJobConstants.DING_TALK_MSG_FALLING_OUT);
        return "";
    }

    @Override
    public Boolean inactiveScheduledTasks(Integer jobId, String cronTime) {
        List<BdCustomerJob> bdCustomerJobs = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(jobId)){
            BdCustomerJob bdCustomerJob = bdCustomerJobMapper.selectBdCustomerJobByJobId(jobId.longValue());
            bdCustomerJobs.add(bdCustomerJob);
        } else {
            BdCustomerJob bdCustomerJob = new BdCustomerJob();
            bdCustomerJob.setStatus(BdCustomerJob.StatusEnum.NORMAL.getValue());
            bdCustomerJobs = bdCustomerJobMapper.selectBdCustomerJobList(bdCustomerJob);
            //List<BdCustomerJob> bdCustomerJobs = bdCustomerJobMapper.selectUnexecuted();
        }
        for (BdCustomerJob en : bdCustomerJobs) {
            //判定执行时间是否过期，过期放入新的时间,没过期则放入现在的时间
            LocalDateTime cronLocalTime = CronUtils.convertCronToLocalDateTime(en.getCronExpression(), 2025);
            if (cronLocalTime.isAfter(LocalDateTime.now())){
                en.setCronExpression(en.getCronExpression() + " 2025-2025");
            } else {
                //en.setCronExpression("01 01 23 07 05 ? 2025-2025");
                en.setCronExpression(cronTime);
            }
            //分类型处理
            if (en.getInvokeTarget().contains("sendDingTalkNotifications")) {
                //处理参数
                LinkedList<String> params = getParams(en.getInvokeTarget());
                DingTalkMsgDTO dingTalkMsgDTO = new DingTalkMsgDTO(
                        Long.valueOf(params.get(0)), Long.valueOf(params.get(1)), params.get(2),
                        params.get(3), params.get(4), params.get(5), Integer.valueOf(params.get(6))
                );
                //创建任务
                createXxlJobTask(
                        en, JSONUtil.toJsonStr(dingTalkMsgDTO),
                        BdJobConstants.InvokeTargetEnum.SEND_DING_TALK_MSG.getBeanHandleName()
                );
            } else if (en.getInvokeTarget().contains("distributionClueManager")) {
                //处理参数
                LinkedList<String> params = getParams(en.getInvokeTarget());
                DistributionClueDTO distributionClueDTO = new DistributionClueDTO(
                        Long.valueOf(params.get(0)), Long.valueOf(params.get(1)), Long.valueOf(params.get(2))
                );
                //创建任务
                createXxlJobTask(
                        en, JSONUtil.toJsonStr(distributionClueDTO),
                        BdJobConstants.InvokeTargetEnum.TRANSFER_CLUES_TO_THE_MANAGER.getBeanHandleName()
                );
            } else if (en.getInvokeTarget().contains("fallIntoTheSea")) {
                //处理参数
                LinkedList<String> params = getParams(en.getInvokeTarget());
                FallIntoSeaDTO fallIntoSeaDTO = new FallIntoSeaDTO(
                        Long.valueOf(params.get(0)), Long.valueOf(params.get(1))
                );
                //创建任务
                createXxlJobTask(
                        en, JSONUtil.toJsonStr(fallIntoSeaDTO),
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_SEA.getBeanHandleName()
                );
            } else if (en.getInvokeTarget().contains("fallIntoThePerson")) {
                //处理参数
                LinkedList<String> params = getParams(en.getInvokeTarget());
                FallIntoPersonDTO fallIntoPersonDTO = new FallIntoPersonDTO(
                        Long.valueOf(params.get(0)), Long.valueOf(params.get(1)),
                        Long.valueOf(params.get(2)), Integer.valueOf(params.get(3))
                );
                //创建任务
                createXxlJobTask(
                        en, JSONUtil.toJsonStr(fallIntoPersonDTO),
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_PERSON.getBeanHandleName()
                );
            } else if (en.getInvokeTarget().contains("fallIntoTheNichePool")) {
                //处理参数
                LinkedList<String> params = getParams(en.getInvokeTarget());
                FallIntoNichePoolDTO fallIntoNichePoolDTO = new FallIntoNichePoolDTO(
                        Long.valueOf(params.get(0)), Long.valueOf(params.get(1)),Integer.valueOf(params.get(2))
                );
                //创建任务
                createXxlJobTask(
                        en, JSONUtil.toJsonStr(fallIntoNichePoolDTO),
                        BdJobConstants.InvokeTargetEnum.FALL_INTO_THE_NICHE_POOL.getBeanHandleName()
                );
            }
        }

        return Boolean.TRUE;
    }

    private LinkedList<String> getParams(String input){
        LinkedList<String> linkedList = new LinkedList<>();
        // 正则匹配括号中的内容
        Pattern pattern = Pattern.compile("\\((.*?)\\)");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String paramsStr = matcher.group(1); // 获取括号内内容
            String[] paramsArray = paramsStr.split(",");

            for (int i = 0; i < paramsArray.length; i++) {
                String param = paramsArray[i].trim(); // 去除前后空格

                // 处理 Long 类型的 L 后缀
                if (param.endsWith("L")) {
                    param = param.substring(0, param.length() - 1);
                }

                // 去除单引号包裹的字符串
                if (param.startsWith("'") && param.endsWith("'")) {
                    param = param.substring(1, param.length() - 1);
                }
                paramsArray[i] = param;
                //System.out.println("参数[" + i + "]：" + param);
                linkedList.add(param);
            }
        } else {
            return new LinkedList<>();
        }
        return linkedList;
    }

    private void sendDingTalkMessages(DingTalkMsgDTO dingTalkMsgDTO, String template) {
        String content = String.format(template,
                dingTalkMsgDTO.getUserName(), dingTalkMsgDTO.getBdGuestSrcName(),
                dingTalkMsgDTO.getClueId(), dingTalkMsgDTO.getContactWay(),
                crmUrl, crmUrl
        );
        DingMessageDTO req = new DingMessageDTO(
                dingTalkMsgDTO.getDingTalkUserId(), BdJobConstants.CLUE_FOLLOW_UP_REMINDER, content
        );
        dingService.sendDingMessageToUser(req);
    }

    @Autowired
    private void setBdCustomersFollowMapper(BdCustomersFollowMapper bdCustomersFollowMapper) {
        this.bdCustomersFollowMapper = bdCustomersFollowMapper;
    }

    @Autowired
    private void setBdClueService(IBdClueService bdClueService) {
        this.bdClueService = bdClueService;
    }

    @Autowired
    private void setBdCustomerJobMapper(BdCustomerJobMapper bdCustomerJobMapper) {
        this.bdCustomerJobMapper = bdCustomerJobMapper;
    }

    @Autowired
    private void setEsClueSyncUtil(EsClueSyncUtil esClueSyncUtil) {
        this.esClueSyncUtil = esClueSyncUtil;
    }

    @Autowired
    private void setBdClueUserTagsMapper(BdClueUserTagsMapper bdClueUserTagsMapper) {
        this.bdClueUserTagsMapper = bdClueUserTagsMapper;
    }

    @Autowired
    private void setBdClueMapper(BdClueMapper bdClueMapper) {
        this.bdClueMapper = bdClueMapper;
    }
}
