package com.nnb.customer.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PolicyConditions;
import com.nnb.common.core.exception.ServiceException;

import com.nnb.common.core.utils.StringUtils;
import com.nnb.customer.domain.OssCallbackParam;
import com.nnb.customer.domain.OssCallbackResult;
import com.nnb.customer.domain.OssFileForUploadVO;
import com.nnb.customer.domain.OssPolicyResult;
import com.nnb.customer.service.OssService;
import com.nnb.customer.utils.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * oss上传管理Service实现类
 */
@Service
public class OssServiceImpl implements OssService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OssServiceImpl.class);
    @Value("${aliyun.oss.policy.expire}")
    private int ALIYUN_OSS_EXPIRE;
    @Value("${aliyun.oss.maxSize}")
    private int ALIYUN_OSS_MAX_SIZE;
    @Value("${aliyun.oss.callback}")
    private String ALIYUN_OSS_CALLBACK;
    @Value("${aliyun.oss.bucketName}")
    private String ALIYUN_OSS_BUCKET_NAME;
    @Value("${aliyun.oss.endpoint}")
    private String ALIYUN_OSS_ENDPOINT;
    @Value("${aliyun.oss.dir.prefix}")
    private String ALIYUN_OSS_DIR_PREFIX;

    @Autowired
    private OSSClient ossClient;

    /**
     * 签名生成
     */
    @Override
    public OssPolicyResult policy() {
        OssPolicyResult result = new OssPolicyResult();
        // 存储目录
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dir = ALIYUN_OSS_DIR_PREFIX + sdf.format(new Date());
        // 签名有效期
        long expireEndTime = System.currentTimeMillis() + ALIYUN_OSS_EXPIRE * 1000;
        Date expiration = new Date(expireEndTime);
        // 文件大小
        long maxSize = ALIYUN_OSS_MAX_SIZE * 1024 * 1024;
        // 回调
        OssCallbackParam callback = new OssCallbackParam();
        callback.setCallbackUrl(ALIYUN_OSS_CALLBACK);
        callback.setCallbackBody("filename=${object}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}");
        callback.setCallbackBodyType("application/x-www-form-urlencoded");
        // 提交节点
        String action = "http://" + ALIYUN_OSS_BUCKET_NAME + "." + ALIYUN_OSS_ENDPOINT;
        try {
            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, maxSize);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir);
            String postPolicy = ossClient.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes("utf-8");
            String policy = BinaryUtil.toBase64String(binaryData);
            String signature = ossClient.calculatePostSignature(postPolicy);
            String callbackData = BinaryUtil.toBase64String(JSONUtil.parse(callback).toString().getBytes("utf-8"));
            // 返回结果
            result.setAccessKeyId(ossClient.getCredentialsProvider().getCredentials().getAccessKeyId());
            result.setPolicy(policy);
            result.setSignature(signature);
            result.setDir(dir);
            result.setCallback(callbackData);
            result.setHost(action);
        } catch (Exception e) {
            LOGGER.error("签名生成失败", e);
        }
        return result;
    }

    @Override
    public OssCallbackResult callback(HttpServletRequest request) {
        OssCallbackResult result = new OssCallbackResult();
        String filename = request.getParameter("filename");
        filename = "http://".concat(ALIYUN_OSS_BUCKET_NAME).concat(".").concat(ALIYUN_OSS_ENDPOINT).concat("/").concat(filename);
        result.setFilename(filename);
        result.setSize(request.getParameter("size"));
        result.setMimeType(request.getParameter("mimeType"));
        result.setWidth(request.getParameter("width"));
        result.setHeight(request.getParameter("height"));
        return result;
    }

    /**
     * 上传文件。
     *
     * @param file 待上传文件。
     * @return 返回回调。
     * <AUTHOR>
     * @since 2022-07-15 14:49:11
     */
    @Override
    public OssFileForUploadVO uploadFile(MultipartFile file) {
        OssFileForUploadVO ossFileForUploadVO = new OssFileForUploadVO();

        try {
            ossFileForUploadVO.setFileName(getName(file.getOriginalFilename()));
            ossFileForUploadVO.setFileOriginalName(file.getOriginalFilename());
            ossFileForUploadVO.setFileType(file.getContentType());
            ossFileForUploadVO.setFileSuffix(getSuffix(Objects.requireNonNull(file.getOriginalFilename())));
            ossFileForUploadVO.setFileSize(file.getSize());
            ossFileForUploadVO.setFilePath(uploadFile(ossFileForUploadVO.getFileName(), file.getInputStream(), ossFileForUploadVO.getFileSuffix()));
            ossFileForUploadVO.setWrapFilePath(wrapFilePath(ossFileForUploadVO.getFilePath()));

            return ossFileForUploadVO;
        } catch (IOException e) {
            throw new ServiceException("上传失败");
        }

    }

    @Override
    public OssFileForUploadVO uploadRecordFile(MultipartFile file) {
        OssFileForUploadVO ossFileForUploadVO = new OssFileForUploadVO();

        try {
            ossFileForUploadVO.setFileName(getName(file.getOriginalFilename()));
            ossFileForUploadVO.setFileOriginalName(file.getOriginalFilename());
            ossFileForUploadVO.setFileType(file.getContentType());
            ossFileForUploadVO.setFileSuffix(getSuffix(Objects.requireNonNull(file.getOriginalFilename())));
            ossFileForUploadVO.setFileSize(file.getSize());
            ossFileForUploadVO.setFilePath(uploadRecordFile(ossFileForUploadVO.getFileName(), file.getInputStream(), ossFileForUploadVO.getFileSuffix()));
            ossFileForUploadVO.setWrapFilePath(wrapFilePath(ossFileForUploadVO.getFilePath()));

            return ossFileForUploadVO;
        } catch (IOException e) {
            throw new ServiceException("上传失败");
        }
    }

    @Override
    public OssFileForUploadVO upload(MultipartFile file, String moduleName) {
        OssFileForUploadVO ossFileForUploadVO = new OssFileForUploadVO();
        //判断存储空间是否存在
        if (!ossClient.doesBucketExist(ALIYUN_OSS_BUCKET_NAME)) {
            ossClient.createBucket(ALIYUN_OSS_BUCKET_NAME);
            //设置权限
            ossClient.setBucketAcl(ALIYUN_OSS_BUCKET_NAME, CannedAccessControlList.PublicRead);
        }

        String filePath = new DateTime().toString("yyyyMMdd");
        String originalFilename = file.getOriginalFilename();
        String fileName = UUID.randomUUID().toString().replace("-", "") + (StringUtils.isEmpty(originalFilename) ? file.getName() : originalFilename);
        String fileUrl = moduleName + "/" + filePath + "/" + fileName;
        String endpoint = ALIYUN_OSS_ENDPOINT;
        if (ALIYUN_OSS_ENDPOINT.contains("http")) {
            endpoint = endpoint.substring(7, endpoint.length());
        }
        //文件上传
        try {
            InputStream inputStream = file.getInputStream();
            ObjectMetadata objectMetadata = getObjectMetadata(inputStream.available());
            ossClient.putObject(ALIYUN_OSS_BUCKET_NAME, fileUrl, inputStream, objectMetadata);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 关闭OSSClient。
//        ossClient.shutdown();
        ossFileForUploadVO.setFileName(getName(file.getOriginalFilename()));
        ossFileForUploadVO.setFileOriginalName(file.getOriginalFilename());
        ossFileForUploadVO.setFileType(file.getContentType());
        ossFileForUploadVO.setFileSuffix(getSuffix(Objects.requireNonNull(file.getOriginalFilename())));
        ossFileForUploadVO.setFileSize(file.getSize());
        ossFileForUploadVO.setFilePath(fileUrl);
        ossFileForUploadVO.setWrapFilePath("http://" + ALIYUN_OSS_BUCKET_NAME + "." + endpoint + "/" + fileUrl);
        return ossFileForUploadVO;
    }


    /**
     * 获取文件名称（不包含后缀）。
     *
     * @param str 待处理文件名称。
     * @return 返回文件名称。
     */
    private String getName(String str) {
        String fileName = str;
        int pointIndex = str.lastIndexOf(".");
        if (pointIndex != -1 && pointIndex != 0) {
            fileName = str.substring(0, pointIndex);
        }
        return fileName;
    }

    /**
     * 获取文件后缀。
     *
     * @param str 文件名称。
     * @return 返回文件类型：0未知，1图片，2音频，3视频。
     */
    private String getSuffix(String str) {
        String suffix = "";

        int pointIndex = str.lastIndexOf(".");
        if (pointIndex != -1) {
            suffix = str.substring(pointIndex + 1);
        }

        return suffix;
    }


    /**
     * ObjectMetaData是用户对该object的描述，
     * 由一系列name-value对组成；其中ContentLength是必须设置的，以便SDK可以正确识别上传Object的大小
     */
    private static ObjectMetadata getObjectMetadata(long length) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(length);
        // 被下载时网页的缓存行为
        objectMetadata.setCacheControl("no-cache");
        objectMetadata.setHeader("Pragma", "no-cache");
        return objectMetadata;
    }

    /**
     * 上传文件
     */
    public String uploadFile(String fileName, InputStream is, String fileSuffix) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String randomNickname = RandomUtils.getRandomNickname(4);
        String key = "pdf/" + sdf.format(new Date()) + "/" + fileName + randomNickname + "." + fileSuffix;
        try {
            ObjectMetadata objectMetadata = getObjectMetadata(is.available());
            ossClient.putObject(ALIYUN_OSS_BUCKET_NAME, key, is, objectMetadata);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("文件上传失败");
        } finally {
            IOUtils.safeClose(is);
        }
        return key;
    }

    /**
     * 录音文件上传
     *
     * @param fileName
     * @param is
     * @param fileSuffix
     * @return
     */
    public String uploadRecordFile(String fileName, InputStream is, String fileSuffix) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String key = "record/" + sdf.format(new Date()) + "/" + fileName + "." + fileSuffix;
        try {
            ObjectMetadata objectMetadata = getObjectMetadata(is.available());
            ossClient.putObject(ALIYUN_OSS_BUCKET_NAME, key, is, objectMetadata);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("文件上传失败");
        } finally {
            IOUtils.safeClose(is);
        }
        return key;


    }

    /**
     * 补全地址。
     *
     * @param filePath 待补全地址。
     * @return 返回补全后的地址。
     */
    public String wrapFilePath(String filePath) {
        if (StringUtil.isNotBlank(filePath)) {
            filePath = "https://crm-file-com.oss-cn-beijing.aliyuncs.com/" + filePath;
        }
        return filePath;
    }


}
