package com.nnb.customer.mapper;

import com.nnb.customer.domain.BdClue;
import com.nnb.customer.domain.BdPopularizeClueSource;
import com.nnb.customer.domain.clue.AdClue;
import com.nnb.customer.domain.nichepool.AdSourceVo;
import com.nnb.customer.domain.nichepool.NichePoolVo;
import com.nnb.customer.domain.popularizeclue.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 推广线索来源Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
public interface BdPopularizeClueSourceMapper
{
    /**
     * 查询推广线索来源
     *
     * @param id 推广线索来源主键
     * @return 推广线索来源
     */
    public BdPopularizeClueSource selectBdPopularizeClueSourceById(Long id);

    /**
     * 查询推广线索来源列表
     *
     * @param bdPopularizeClueSource 推广线索来源
     * @return 推广线索来源集合
     */
    public List<BdPopularizeClueSource> selectBdPopularizeClueSourceList(BdPopularizeClueSource bdPopularizeClueSource);

    /**
     * 新增推广线索来源
     *
     * @param bdPopularizeClueSource 推广线索来源
     * @return 结果
     */
    public int insertBdPopularizeClueSource(BdPopularizeClueSource bdPopularizeClueSource);

    /**
     * 修改推广线索来源
     *
     * @param bdPopularizeClueSource 推广线索来源
     * @return 结果
     */
    public int updateBdPopularizeClueSource(BdPopularizeClueSource bdPopularizeClueSource);

    /**
     * 删除推广线索来源
     *
     * @param id 推广线索来源主键
     * @return 结果
     */
    public int deleteBdPopularizeClueSourceById(Long id);

    /**
     * 批量删除推广线索来源
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdPopularizeClueSourceByIds(Long[] ids);

    @Select("select acs.name from bd_popularize_clue_source bpcs left join ad_clue_source acs on bpcs.ad_clue_source_id = acs.id where bpcs.clue_id = #{clueId}")
    public List<String> getAdSourceName(@Param("clueId")Long clueId);

    public List<AdSourceVo> getAdSourceVo(@Param("clueIdList") List<Long> clueIdList);

    @Select("select aai.advertiser_name from ad_clue ac left join ad_advertiser_info aai on  ac.advertiser_id = aai.advertiser_id where aai.status = 1 and ac.bd_clue_id = #{clueId} limit 1")
    public String getAdvertiserName(@Param("clueId")Long clueId);

    @Select("select count(1) from (select count(id) from bd_popularize_clue_source where ad_clue_source_id = #{sourceId} and create_time >= #{popularizeClueChannelDTO.dateStart} and create_time <= #{popularizeClueChannelDTO.dateEnd} group by clue_id) tmp")
    public int getClueCountBySourceId(@Param("sourceId") Long sourceId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueOrderVo> getPopularizeClueOrderVo(@Param("sourceId") Long sourceId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    Long countImportPopularizeClue(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    PopularizeClueAccountPhone getPopularizeClueAccountPhone(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<PopularizeClueTLJsonVo> getImportClueApply(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    @Select("select id, name as label from ad_clue_source order by created_at ASC")
    List<TreeSelect> getClueSource();

    @Select("SELECT aai.id, aai.advertiser_name AS label,acs.id as sourceId,acs.name as sourceName, aai.advertiser_id as advertiserId FROM ad_clue_source acs LEFT JOIN ad_advertiser_info aai ON acs.id = aai.source_id WHERE aai.status = 1 and aai.advertiser_role = 1  ORDER BY acs.created_at ")
    List<TreeSelect> getAdvertiserTree();

    Long getPopularClueCount(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    List<String> getPopularClueCountPhone(@Param("advertiserId") String advertiserId, @Param("popularizeClueChannelDTO") PopularizeClueChannelDTO popularizeClueChannelDTO);

    @Select("select advertiser_name from ad_advertiser_info where id = #{advertiserId}")
    String getImportAdvertiserName(Long advertiserId);

    @Select("select name from ad_clue_source where id = #{sourceId}")
    String getChannelName(Long sourceId);

    @Select("select id from bd_clue where num_status = 2 and popularize_clue_id is not null")
    List<BdClue> getPopularCommon();

    @Select("select id as id, clue_json as clueJson from ad_clue where bd_clue_id = #{clueId}")
    List<AdClue> getAdClueByClueId(Long clueId);

    @Select("select aa.id as adAppId,aa.type as type from ad_advertiser_info aai left join ad_app aa on aai.ad_app_id = aa.id where aai.id = #{advertiserId} and aai.ad_app_id is not null ")
    AdSourceVo getAdKeyWordVo(Long advertiserId);

    @Select("select id as id, clue_json as clueJson from ad_clue where clue_id = #{clueId}")
    List<AdClue> getAdClueByThirdClueId(String clueId);
}
