package com.nnb.customer.mapper;


import com.nnb.customer.domain.BdOperatorEntity;
import com.nnb.customer.domain.vo.BdOperatorForListDTO;
import com.nnb.customer.domain.vo.BdOperatorForListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运营方，Mapper接口。
 *
 * <AUTHOR>
 * @date 2022-06-28 13:48:28
 */
public interface BdOperatorMapper {

    /**
     * 获取运营方列表。
     *
     * @param query 查询条件。
     * @return 返回运营方列表。
     * <AUTHOR>
     * @since 2022-06-28 16:49:18
     */
    List<BdOperatorForListVO> getList(@Param("query") BdOperatorForListDTO query);

    /**
     * 新增运营方。
     *
     * @param operatorEntity 待新增实体。
     * <AUTHOR>
     * @since 2022-06-28 16:56:30
     */
    public void saveOperator(@Param("entity") BdOperatorEntity operatorEntity);

    /**
     * 编辑运营方。
     *
     * @param operatorEntity 待编辑实体。
     * <AUTHOR>
     * @since 2022-06-28 16:56:30
     */
    public void submitOperator(@Param("entity") BdOperatorEntity operatorEntity);

    /**
     * 批量删除运营方。
     *
     * @param ids 待删除运营方标识。
     * <AUTHOR>
     * @since 2022-06-28 17:15:11
     */
    public void deleteOperatorBatch(@Param("ids") List<Integer> ids);

}
