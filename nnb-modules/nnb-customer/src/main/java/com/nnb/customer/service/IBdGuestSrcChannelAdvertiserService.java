package com.nnb.customer.service;

import com.nnb.customer.domain.clue.BdGuestSrcChannelAdvertiser;

import java.util.List;
/**
 * 北斗线索来源渠道账户关系Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface IBdGuestSrcChannelAdvertiserService 
{
    /**
     * 查询北斗线索来源渠道账户关系
     * 
     * @param id 北斗线索来源渠道账户关系主键
     * @return 北斗线索来源渠道账户关系
     */
    public BdGuestSrcChannelAdvertiser selectBdGuestSrcChannelAdvertiserById(Long id);

    /**
     * 查询北斗线索来源渠道账户关系列表
     * 
     * @param bdGuestSrcChannelAdvertiser 北斗线索来源渠道账户关系
     * @return 北斗线索来源渠道账户关系集合
     */
    public List<BdGuestSrcChannelAdvertiser> selectBdGuestSrcChannelAdvertiserList(BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser);

    /**
     * 新增北斗线索来源渠道账户关系
     * 
     * @param bdGuestSrcChannelAdvertiser 北斗线索来源渠道账户关系
     * @return 结果
     */
    public int insertBdGuestSrcChannelAdvertiser(BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser);

    /**
     * 修改北斗线索来源渠道账户关系
     * 
     * @param bdGuestSrcChannelAdvertiser 北斗线索来源渠道账户关系
     * @return 结果
     */
    public int updateBdGuestSrcChannelAdvertiser(BdGuestSrcChannelAdvertiser bdGuestSrcChannelAdvertiser);

    /**
     * 批量删除北斗线索来源渠道账户关系
     * 
     * @param ids 需要删除的北斗线索来源渠道账户关系主键集合
     * @return 结果
     */
    public int deleteBdGuestSrcChannelAdvertiserByIds(Long[] ids);

    /**
     * 删除北斗线索来源渠道账户关系信息
     * 
     * @param id 北斗线索来源渠道账户关系主键
     * @return 结果
     */
    public int deleteBdGuestSrcChannelAdvertiserById(Long id);
}
