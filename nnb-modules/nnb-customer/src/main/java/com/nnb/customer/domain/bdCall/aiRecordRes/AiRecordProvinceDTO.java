package com.nnb.customer.domain.bdCall.aiRecordRes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-10-30
 * @Version: 1.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Data
public class AiRecordProvinceDTO implements Serializable {
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("pid")
    private Object pid;
    @JsonProperty("city")
    private String city;
    @JsonProperty("area_code")
    private String areaCode;
    @JsonProperty("post_code")
    private String postCode;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
}
