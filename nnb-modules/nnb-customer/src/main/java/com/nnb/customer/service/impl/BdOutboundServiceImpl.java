package com.nnb.customer.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.SpringUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.customer.constant.CustomerBdConstants;
import com.nnb.customer.domain.*;
import com.nnb.customer.domain.dto.CallDurationStatisticsDTO;
import com.nnb.customer.domain.vo.outbound.BdOutboundStaticsVo;
import com.nnb.customer.domain.vo.outbound.BdOutboundStaticsVoRe;
import com.nnb.customer.enums.ClueStatusEnum;
import com.nnb.customer.mapper.*;
import com.nnb.customer.model.BdClueScrs;
import com.nnb.customer.model.BdOutboundContactsVo;
import com.nnb.customer.model.BdOutboundDto;
import com.nnb.customer.model.BdOutboundVo;
import com.nnb.customer.service.IBdClueService;
import com.nnb.customer.service.IBdPreventSealService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.service.IBdOutboundService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通话记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@Service
public class BdOutboundServiceImpl implements IBdOutboundService {
    @Autowired
    private BdOutboundMapper bdOutboundMapper;

    @Autowired
    private BdClueMapper bdClueMapper;
    @Autowired
    private BdClueDetailMapper bdClueDetailMapper;
    @Autowired
    private BdClueContactsMapper bdClueContactsMapper;
    @Autowired
    private BdClueSplitMapper bdClueSplitMapper;
    @Autowired
    private IBdClueService bdClueService;
    @Autowired
    private IBdPreventSealService bdPreventSealService;
    @Autowired
    private TokenService tokenService;

    @Autowired
    RemoteUserService remoteUserService;

    /**
     * 查询通话记录
     *
     * @param id 通话记录主键
     * @return 通话记录
     */
    @Override
    public BdOutbound selectBdOutboundById(Long id) {
        return bdOutboundMapper.selectBdOutboundById(id);
    }

    /**
     * 查询通话记录列表
     *
     * @param bdOutbound 通话记录
     * @return 通话记录
     */
    @Override
    public List<BdOutbound> selectBdOutboundList(BdOutbound bdOutbound) {
        return bdOutboundMapper.selectBdOutboundList(bdOutbound);
    }

    /**
     * 新增通话记录
     *
     * @param bdOutbound 通话记录
     * @return 结果
     */
    @Override
    public int insertBdOutbound(BdOutbound bdOutbound) {
        return bdOutboundMapper.insertBdOutbound(bdOutbound);
    }

    /**
     * 修改通话记录
     *
     * @param bdOutbound 通话记录
     * @return 结果
     */
    @Override
    public int updateBdOutbound(BdOutbound bdOutbound) {
        bdOutbound.setDatUpdatedAt(DateUtil.date());
        bdOutbound.setNumUpdatedBy(SecurityUtils.getUserId());
        return bdOutboundMapper.updateBdOutbound(bdOutbound);
    }

    /**
     * 批量删除通话记录
     *
     * @param ids 需要删除的通话记录主键
     * @return 结果
     */
    @Override
    public int deleteBdOutboundByIds(Long[] ids) {
        return bdOutboundMapper.deleteBdOutboundByIds(ids);
    }

    /**
     * 删除通话记录信息
     *
     * @param id 通话记录主键
     * @return 结果
     */
    @Override
    public int deleteBdOutboundById(Long id) {
        return bdOutboundMapper.deleteBdOutboundById(id);
    }

    @Override
    public Map<String, Object> selectBdOutboundVoList(BdOutboundDto dto) {
        if (StringUtils.isNotEmpty(dto.getDatCreatedAtBegin())) {
            dto.setDatCreatedAtBegin(dto.getDatCreatedAtBegin() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(dto.getDatCreatedAtEnd())) {
            dto.setDatCreatedAtEnd(dto.getDatCreatedAtEnd() + " 23:59:59");
        }
        Map<String, Object> map = new HashMap<>();
        List<BdOutboundContactsVo> vos = new ArrayList<>();
        Long total = 0L;
        if (StringUtils.isNotEmpty(dto.getVcPhone()) && dto.getVcPhone().length() == 11) {
            String cipherTextPhone = dto.getVcPhone().substring(0, 3) + "****" + dto.getVcPhone().substring(7);
            dto.setCipherTextPhone(cipherTextPhone);
        }
//        Long userId = SecurityUtils.getUserId();
//        if (userId != 1L) {
//            dto.setNumCreatedBy(SecurityUtils.getUserId());
//        }
        if (new Long(1L).equals(dto.getNumCreatedBy())) {
            dto.setNumCreatedBy(null);
        }
        LoginUser loginUser = tokenService.getLoginUser();
        setEnterpriseDominant(dto, loginUser);

        if (loginUser.getPermissions().contains("*:*:*") || loginUser.getPermissions().contains(CustomerBdConstants.Jurisdiction.CLUE_DEPTDATA)) {
            total = SpringUtils.getBean(IBdOutboundService.class).countQueryBdOutboundVoDeptList(dto);
            vos = SpringUtils.getBean(IBdOutboundService.class).queryBdOutboundVoDeptList(dto);
        } else {
            total = SpringUtils.getBean(IBdOutboundService.class).countQueryBdOutboundVoPerList(dto);
            vos = SpringUtils.getBean(IBdOutboundService.class).queryBdOutboundVoPerList(dto);
        }

        //查询老客户以及以成交客户，13 和 63
        if (!vos.isEmpty()) {
            //查询联系人
            vos.forEach(item -> {
                Long numClueId = item.getClueId();
                if(Objects.nonNull(numClueId) && 0 != numClueId){
                    List<BdClueContacts> clueContacts = bdClueContactsMapper.getBdClueContactByClueId(numClueId);
                    if (CollectionUtils.isNotEmpty(clueContacts)) {
                        item.setContactsName(clueContacts.get(0).getVcName());
                    }
                }
            });


            List<Long> clueIdList = vos.stream().map(BdOutboundContactsVo::getClueId).collect(Collectors.toList());
            List<BdOutboundContactsVo> mapList = bdOutboundMapper.selectUserTagsByClueIdList(clueIdList);
            for (BdOutboundContactsVo vo : vos) {
                List<BdOutboundContactsVo> maps = mapList.stream().filter(m -> m.getNumClueId().equals(vo.getClueId())).collect(Collectors.toList());
                if (maps.size() == 1) {
                    BdOutboundContactsVo contactsVo = maps.get(0);
                    if ("1".equals(contactsVo.getNumIsFriend()) || "4".equals(contactsVo.getNumStatus())) {
                        //手机号加密
                        if (!vo.getNumPhone().contains("****")) {
                            String phone = vo.getNumPhone().substring(0, 3) + "****" + vo.getNumPhone().substring(7);
                            vo.setNumPhone(phone);
                        }
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(vos)) {
            vos = vos.stream().peek(vo -> {
                String time = DateUtils.conversion(vo.getNumContentTime());
                vo.setContentDate(time);
            }).collect(Collectors.toList());
        }
        map.put("total", total);
        map.put("list", vos);
        return map;
    }


    /**
     * 设置企业主体
     * @param dto
     * @param loginUser
     */
    private void setEnterpriseDominant(BdOutboundDto dto, LoginUser loginUser) {
        if (Objects.nonNull(loginUser)) {
            List<SysRole> roles = loginUser.getSysUser().getRoles();
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(roles)) {
                List<Long> collect = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
                collect.removeAll(Collections.singleton(null));
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(collect) && (!collect.contains(1L))) {
                    dto.setEnterpriseDominant(Objects.nonNull(loginUser.getSysUser().getDept()) ? loginUser.getSysUser().getDept().getEnterpriseDominant() : null);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BdOutboundVo insertMsg(BdOutboundVo bdOutbound) {
        LoginUser loginUser = tokenService.getLoginUser();
        Long deptId = loginUser.getSysUser().getDeptId();
        Long clueId = null;
        Integer enterpriseDominant = null;
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();
        if(Objects.nonNull(loginUser.getSysUser().getDept())){
            enterpriseDominant = loginUser.getSysUser().getDept().getEnterpriseDominant();
        }
        if (ObjectUtil.isEmpty(bdOutbound.getNumPhone())) {
            throw new ServiceException("电话号码为空！");
        }
        if (bdOutbound.getNumType().intValue() == 1) {
            //防封号校验
            boolean isSeal = bdPreventSealService.checkBdPreventSealByPhone(bdOutbound.getNumPhone());
            if (isSeal) {
                throw new ServiceException("触发防封号规则，无法拨打");
            }
            BdClueScrs bdClueScrs = bdClueMapper.selectBdClueListSrcs(bdOutbound.getNumPhone(), loginUser.getSysUser().getCrmEnterpriseId(), enterpriseDominant);
            if (ObjectUtil.isNotEmpty(bdClueScrs) && !bdClueScrs.getNumStatus().equals(ClueStatusEnum.CLOSED.getStatus())) {
                if (bdClueScrs.getNumStatus().equals(ClueStatusEnum.CUSTOMER.getStatus())
                        && !userId.equals(bdClueScrs.getFollowUserId())) {
                    throw new ServiceException("已在客保中，无法拨打");
                }
                if (bdClueScrs.getNumStatus().equals(ClueStatusEnum.BLACKLIST.getStatus())
                        || bdClueScrs.getNumStatus().equals(ClueStatusEnum.SINK.getStatus())) {
                    throw new ServiceException("已在下沉池，无法拨打");
                }
                //线索移交到拨打人
                bdClueService.transferClue(bdClueScrs, deptId, Boolean.FALSE);
                clueId = bdClueScrs.getId();
                bdOutbound.setNumCustomerId(bdClueScrs.getCustomerId());
                bdOutbound.setNumContactsId(bdClueContactsMapper.selectBdClueContactsList(
                        BdClueContacts.builder().clueId(clueId).build()).get(0).getId());
                bdOutbound.setClueId(clueId);
                //客户名称
                bdOutbound.setVcCustomerName(bdClueScrs.getVcCustomerName());
            }
        }
        bdOutbound.setNumIsCreate(ObjectUtil.isNotEmpty(clueId) ? CustomerBdConstants.Y : CustomerBdConstants.N);
        bdOutbound.setNumCreatedBy(userId);
        bdOutbound.setNumUpdatedBy(userId);
        bdOutbound.setDatCreatedAt(date);
        bdOutbound.setDatUpdatedAt(date);
        //插入卡卡userId
        if (ObjectUtil.isNotEmpty(userId)){
            String callUserId = bdOutboundMapper.selectCallUserId(loginUser.getUserid());
            if (StrUtil.isNotEmpty(callUserId)){
                bdOutbound.setCallUserId(callUserId);
            }
        }
        bdOutboundMapper.insertBdOutbound(bdOutbound);
        //是否添加防拨打记录
        bdPreventSealService.checkInsertPhone(bdOutbound);
        return bdOutbound;
    }

    @Override
    public BdOutboundVo insertMsgNew(BdOutboundVo vo) {
        LoginUser loginUser = tokenService.getLoginUser();
        Long deptId = loginUser.getSysUser().getDeptId();
        Long clueId = null;
        Long userId = SecurityUtils.getUserId();
        Integer enterpriseDominant = null;
        if(Objects.nonNull(loginUser.getSysUser().getDept())){
            enterpriseDominant = loginUser.getSysUser().getDept().getEnterpriseDominant();
        }
        DateTime date = DateUtil.date();
        if (vo.getNumType().intValue() == 1) {
            BdClueScrs bdClueScrs = bdClueMapper.selectBdClueListSrcs(vo.getNumPhone(), loginUser.getSysUser().getCrmEnterpriseId(), enterpriseDominant);
            if (ObjectUtil.isNotEmpty(bdClueScrs) && !bdClueScrs.getNumStatus().equals(ClueStatusEnum.CLOSED.getStatus())) {
                //线索移交到拨打人
                bdClueService.transferClue(bdClueScrs, deptId, Boolean.FALSE);
                clueId = bdClueScrs.getId();
                vo.setNumCustomerId(bdClueScrs.getCustomerId());
                vo.setNumContactsId(bdClueContactsMapper.selectBdClueContactsList(
                        BdClueContacts.builder().clueId(clueId).build()).get(0).getId());
                vo.setClueId(clueId);
                //客户名称
                vo.setVcCustomerName(bdClueScrs.getVcCustomerName());
            }
        }
        vo.setNumIsCreate(ObjectUtil.isNotEmpty(clueId) ? CustomerBdConstants.Y : CustomerBdConstants.N);
        vo.setNumCreatedBy(userId);
        vo.setNumUpdatedBy(userId);
        vo.setDatCreatedAt(date);
        vo.setDatUpdatedAt(date);
        //插入卡卡userId
        if (ObjectUtil.isNotEmpty(userId)){
            String callUserId = bdOutboundMapper.selectCallUserId(loginUser.getUserid());
            if (StrUtil.isNotEmpty(callUserId)){
                vo.setCallUserId(callUserId);
            }
        }
        bdOutboundMapper.insertBdOutbound(vo);
        //是否添加防拨打记录
        bdPreventSealService.checkInsertPhone(vo);
        return vo;
    }

    @Override
    public List<BdOutboundStaticsVoRe> userCallDurationStatistics(CallDurationStatisticsDTO
                                                                          callDurationStatisticsDTO) {
        if (StringUtils.isNotEmpty(callDurationStatisticsDTO.getStatisticsStartTime())) {
            callDurationStatisticsDTO.setStatisticsStartTime(callDurationStatisticsDTO.getStatisticsStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(callDurationStatisticsDTO.getStatisticsEndTime())) {
            callDurationStatisticsDTO.setStatisticsEndTime(callDurationStatisticsDTO.getStatisticsEndTime() + " 23:59:59");
        }

        LoginUser loginUser = tokenService.getLoginUser();
        Set<String> permissions = loginUser.getPermissions();
        List<BdOutboundStaticsVo> bdOutboundStaticsVos = new ArrayList<>();
        //bd:clue:deptData  部门经理权限
        if (permissions.contains("*:*:*") || permissions.contains(CustomerBdConstants.Jurisdiction.CLUE_DEPTDATA)) {
            if(loginUser.getSysUser().getUserId() != 1L){
                List<Long> deptIdList = null;
                R<SysUser> info = remoteUserService.getUserInfoById(loginUser.getSysUser().getUserId(), SecurityConstants.INNER);
                if(200 == info.getCode()){
                    Long deptId = info.getData().getDeptId();
                    R<List<SysDept>> sysDeptChildren = remoteUserService.getDeptChildrenByPid(deptId, SecurityConstants.INNER);
                    if(200 == sysDeptChildren.getCode()){
                        List<SysDept> data = sysDeptChildren.getData();
                        deptIdList = data.stream().map(SysDept::getDeptId).collect(Collectors.toList());
                    }
                }
                if (CollectionUtils.isEmpty(callDurationStatisticsDTO.getDeptId())) {
                    callDurationStatisticsDTO.setDeptId(deptIdList);
                } else {
                    List<Long> deptIds = new ArrayList<>();
                    List<Long> list = new ArrayList<>();
                    list.add(-1L);
                    List<Long> deptId = callDurationStatisticsDTO.getDeptId();
                    for (Long aLong : deptId) {
                        if (deptIdList.contains(aLong)) {
                            deptIds.add(aLong);
                        }
                    }
                    callDurationStatisticsDTO.setDeptId(CollectionUtils.isNotEmpty(deptIds) ? deptIds : list);
                }
            }
            bdOutboundStaticsVos = statisticsDepartmentList(callDurationStatisticsDTO);
        } else {
            R<List<SysUser>> sysDeptChildrenL = null;
            List<Long> deptIdList2 = null;
            if (CollUtil.isEmpty(callDurationStatisticsDTO.getDeptId())) {
                if (loginUser.getSysUser().getUserId() == 1845L || loginUser.getSysUser().getUserId() == 2978L) {
                    sysDeptChildrenL = remoteUserService.getSysDeptChildren(18L, SecurityConstants.INNER);
                    List<SysUser> data1 = sysDeptChildrenL.getData();
                    deptIdList2 = data1.stream().map(SysUser::getDeptId).collect(Collectors.toList());
                    callDurationStatisticsDTO.setDeptId(deptIdList2);
                }

                callDurationStatisticsDTO.setDeptId(deptIdList2);
            } else {
                List<Long> deptIds = new ArrayList<>(callDurationStatisticsDTO.getDeptId());
                callDurationStatisticsDTO.setDeptId(deptIds);
            }
            List<Long> userIds = new ArrayList<>();
            long userId = 1845;
            long userId1 = 2978;
            userIds.add(userId);
            userIds.add(userId1);
            if (!userIds.contains(loginUser.getSysUser().getUserId())) {
                callDurationStatisticsDTO.setUserId(loginUser.getSysUser().getUserId());
            }
            bdOutboundStaticsVos = statisticsPersonList(callDurationStatisticsDTO);

        }
        List<BdOutboundStaticsVoRe> bdOutboundStaticsVoReList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bdOutboundStaticsVos)) {
            bdOutboundStaticsVos.forEach(val -> {
                        BdOutboundStaticsVoRe bdOutboundStaticsVoRe = new BdOutboundStaticsVoRe();
                        //计算 拨打次数，接通次数，接通率
                        String numIsAnswerStr = val.getNumIsAnswerStr();
                        if (StringUtils.isNotEmpty(numIsAnswerStr)) {
                            String[] split = numIsAnswerStr.split(",");
                            val.setDialCount(split.length);
                            List<String> list = Arrays.asList(split);
                            List<String> answerList = list.stream().filter("1"::equals).collect(Collectors.toList());
                            val.setConnectCount(answerList.size());
                            val.setConnectRate(getPercentStr(val.getConnectCount(), val.getDialCount()));
                        }
                        String numContentTimeStr = val.getNumContentTimeStr();
                        if (StringUtils.isNotEmpty(numContentTimeStr)) {
                            String[] split = numContentTimeStr.split(",");
                            List<String> list = new ArrayList<>(Arrays.asList(split));
                            BigDecimal sum = BigDecimal.ZERO;
                            for (String content : list) {
                                BigDecimal bigDecimal = new BigDecimal(content);
                                sum = sum.add(bigDecimal);
                            }
                            val.setCallDuration(second2Time(sum.longValue()));
                        }
                        BeanUtils.copyProperties(val, bdOutboundStaticsVoRe);
                        if (1 == callDurationStatisticsDTO.getStatisticsType()) {
                            Date createdAt = bdOutboundStaticsVoRe.getDatCreatedAt();
                            String date = convertDate(createdAt);
                            String convertDate = convertDate(new Date());
                            bdOutboundStaticsVoRe.setDatCreatedAtStr(date + "~" + convertDate);
                        }
                        bdOutboundStaticsVoRe.setStatisticsType(callDurationStatisticsDTO.getStatisticsType());
                        bdOutboundStaticsVoReList.add(bdOutboundStaticsVoRe);
                    }
            );
        }
        return bdOutboundStaticsVoReList;
    }

    /**
     * 格式化日期
     *
     * @param date
     * @return
     */
    public String convertDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(date);
        return format;
    }

    @DataScope(deptAlias = "fsd")
    @Override
    public List<BdOutboundStaticsVo> statisticsDepartmentList(CallDurationStatisticsDTO callDurationStatisticsDTO) {
        List<BdOutboundStaticsVo> bdOutboundStaticsVos = bdOutboundMapper.userCallDurationStatistics(callDurationStatisticsDTO);
        return bdOutboundStaticsVos;
    }

    @DataScope(deptAlias = "sd", userAlias = "su")
    @Override
    public List<BdOutboundStaticsVo> statisticsPersonList(CallDurationStatisticsDTO callDurationStatisticsDTO) {
        List<BdOutboundStaticsVo> bdOutboundStaticsVos = bdOutboundMapper.userCallDurationStatistics(callDurationStatisticsDTO);
        return bdOutboundStaticsVos;
    }

    @Override
    @DataScope(deptAlias = "fsd")
    public Long countQueryBdOutboundVoDeptList(BdOutboundDto dto) {
        return bdOutboundMapper.countQueryBdOutboundVoDeptList(dto);
    }

    @Override
    @DataScope(deptAlias = "fsd")
    public List<BdOutboundContactsVo> queryBdOutboundVoDeptList(BdOutboundDto dto) {
        return bdOutboundMapper.queryBdOutboundVoDeptList(dto);
    }

    @Override
    @DataScope(deptAlias = "su", userAlias = "su")
    public Long countQueryBdOutboundVoPerList(BdOutboundDto dto) {
        return bdOutboundMapper.countQueryBdOutboundVoPerList(dto);
    }

    @Override
    @DataScope(deptAlias = "su", userAlias = "su")
    public List<BdOutboundContactsVo> queryBdOutboundVoPerList(BdOutboundDto dto) {
        return bdOutboundMapper.queryBdOutboundVoPerList(dto);
    }

    @Override
    public List<BdOutboundContactsVo> selectBdOutboundVosByEnterpriseId(Long enterpriseId) {
        return bdOutboundMapper.selectBdOutboundVosByEnterpriseId(enterpriseId);
    }

    /**
     * 得到百分比的字符串，比如传参 1,3，返回33%
     */
    public String getPercentStr(Integer diff, Integer sum) {
        DecimalFormat df = new DecimalFormat("0");//格式化小数
        float num = (float) diff / sum * 100;
        String str = df.format(num);
        return str + "%";
    }

    /**
     * @param second 秒
     * @description: 秒转换为时分秒 HH:mm:ss 格式 仅当小时数大于0时 展示HH
     * @return: {@link String}
     * @author: pzzhao
     * @date: 2022-05-08 13:55:17
     */
    public static String second2Time(Long second) {
        if (second == null || second < 0) {
            return "00:00";
        }

        long h = second / 3600;
        long m = (second % 3600) / 60;
        long s = second % 60;
        String str = "";
        if (h > 0) {
            str = (h < 10 ? ("0" + h) : h) + "时";
        }
        str += (m < 10 ? ("0" + m) : m) + "分";
        str += (s < 10 ? ("0" + s) : s) + "秒";
        return str;

    }

}
