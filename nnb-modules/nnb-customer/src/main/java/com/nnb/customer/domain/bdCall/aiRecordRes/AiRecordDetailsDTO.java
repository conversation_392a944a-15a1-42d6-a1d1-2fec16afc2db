package com.nnb.customer.domain.bdCall.aiRecordRes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-10-30
 * @Version: 1.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Data
public class AiRecordDetailsDTO implements Serializable {
    @JsonProperty("id")
    private String id;
    @JsonProperty("record_id")
    private String recordId;
    @JsonProperty("task_id")
    private String taskId;
    @JsonProperty("notify")
    private String notify;
    @JsonProperty("callid")
    private String callId;
    @JsonProperty("error_code")
    private Integer errorCode;
    @JsonProperty("asrtype")
    private String asrType;
    @JsonProperty("callee")
    private String callee;
    @JsonProperty("gender")
    private Integer gender;
    @JsonProperty("asr_elapse")
    private Integer asrElapse;
    @JsonProperty("record_ms")
    private Integer recordMs;
    @JsonProperty("volume_gain")
    private Double volumeGain;
    @JsonProperty("question")
    private String question;
    @JsonProperty("split_question")
    private Object splitQuestion;
    @JsonProperty("question_index")
    private Integer questionIndex;
    @JsonProperty("speak_ms")
    private Integer speakMs;
    @JsonProperty("play_ms")
    private Integer playMs;
    @JsonProperty("keyword")
    private Object keyword;
    @JsonProperty("answer_id")
    private Object answerId;
    @JsonProperty("answer_text")
    private Object answerText;
    @JsonProperty("answer_content")
    private String answerContent;
    @JsonProperty("word_class")
    private Object wordClass;
    @JsonProperty("answer_action")
    private Object answerAction;
    @JsonProperty("playback_after_action")
    private Integer playbackAfterAction;
    @JsonProperty("upstream_answer_action")
    private Object upstreamAnswerAction;
    @JsonProperty("upstream_answer_content")
    private Object upstreamAnswerContent;
    @JsonProperty("upstream_answer_text")
    private Object upstreamAnswerText;
    @JsonProperty("upstream_answer_id")
    private Object upstreamAnswerId;
    @JsonProperty("score")
    private Integer score;
    @JsonProperty("sequence")
    private Integer sequence;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("bridge_status")
    private Integer bridgeStatus;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("is_regular")
    private Integer isRegular;
    @JsonProperty("expression")
    private Object expression;
    @JsonProperty("tags")
    private List<?> tags;
}
