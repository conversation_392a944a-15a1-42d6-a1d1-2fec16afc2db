package com.nnb.customer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: Chen-xy
 * @Description: 富装柜接口url配置类
 * @Date: 2022-09-01
 * @Version: 1.0
 */
@Component
@ConfigurationProperties(prefix = "fuioupay")
public class FuioupayPropertiesConfig {

    /**
     * url地址前缀
     */
    private String prefix;

    /**
     *商户号
     */
    private String mchnt_cd;

    /**
     *mchnt_key密钥
     */
    private String mchnt_key;

    /**
     * 统一下单接口
     */
    private String preCreateUrl;

    /**
     * 聚合统一下单接口
     */
    private String wxPreCreateUrl;

    /**
     * 支付结果查询接口
     */
    private String commonQueryUrl;

    /**
     * 请求退款接口
     */
    private String commonRefundUrl;

    /**
     * 异步回调接口
     */
    private String notifyUrl;

    /**
     * 订单前缀
     */
    private String orderPrefix;

    /**
     * appId
     */
    private String appId;

    /**
     * 二维码过期时间
     */
    private Integer expiredTime;

    /**
     * 收银台链接
     * @return
     */
    private String cashierUrl;

    /**
     * 数字营销部门
     */
    private String marketingDeptIds;

    /**
     * 回调php地址
     * @return
     */
    private String callBackPhp;

    /**
     * php token
     * @return
     */
    private String phpToken;

    /**
     * 柠檬会订单绑定crm userId（默认186
     * @return
     */
    private String lemonCrmUserId;

    public String getLemonCrmUserId() {
        return lemonCrmUserId;
    }

    public void setLemonCrmUserId(String lemonCrmUserId) {
        this.lemonCrmUserId = lemonCrmUserId;
    }

    public String getPhpToken() {
        return phpToken;
    }

    public void setPhpToken(String phpToken) {
        this.phpToken = phpToken;
    }

    public String getCallBackPhp() {
        return callBackPhp;
    }

    public void setCallBackPhp(String callBackPhp) {
        this.callBackPhp = callBackPhp;
    }

    public String getMarketingDeptIds() {
        return marketingDeptIds;
    }

    public void setMarketingDeptIds(String marketingDeptIds) {
        this.marketingDeptIds = marketingDeptIds;
    }

    public String getCashierUrl() {
        return cashierUrl;
    }

    public void setCashierUrl(String cashierUrl) {
        this.cashierUrl = cashierUrl;
    }

    public Integer getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Integer expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOrderPrefix() {
        return orderPrefix;
    }

    public void setOrderPrefix(String orderPrefix) {
        this.orderPrefix = orderPrefix;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getCommonRefundUrl() {
        return prefix + commonRefundUrl;
    }

    public String getPreCreateUrl() {
        return prefix + preCreateUrl;
    }

    public String getWxPreCreateUrl() {
        return prefix + wxPreCreateUrl;
    }

    public String getCommonQueryUrl() {
        return prefix + commonQueryUrl;
    }

    public void setCommonRefundUrl(String commonRefundUrl) {
        this.commonRefundUrl = commonRefundUrl;
    }

    public void setPreCreateUrl(String preCreateUrl) {
        this.preCreateUrl = preCreateUrl;
    }

    public void setWxPreCreateUrl(String wxPreCreateUrl) {
        this.wxPreCreateUrl = wxPreCreateUrl;
    }

    public void setCommonQueryUrl(String commonQueryUrl) {
        this.commonQueryUrl = commonQueryUrl;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getMchnt_cd() {
        return mchnt_cd;
    }

    public void setMchnt_cd(String mchnt_cd) {
        this.mchnt_cd = mchnt_cd;
    }

    public String getMchnt_key() {
        return mchnt_key;
    }

    public void setMchnt_key(String mchnt_key) {
        this.mchnt_key = mchnt_key;
    }
}
