package com.nnb.customer.model;

import com.nnb.customer.domain.BdOutboundRemark;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="BdOutboundRemarkVo",description="外呼记录备注对象Vo")
public class BdOutboundRemarkVo extends BdOutboundRemark {
    private static final long serialVersionUID = 8421170799661945657L;

    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    private String createUseName;

}
