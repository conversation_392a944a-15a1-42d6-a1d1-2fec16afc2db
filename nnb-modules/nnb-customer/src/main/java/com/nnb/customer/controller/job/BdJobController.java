package com.nnb.customer.controller.job;

import com.nnb.common.core.domain.R;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.customer.service.job.IBdJobService;
import com.nnb.system.api.domain.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-04
 * @Version: 1.0
 */
@RestController
@RequestMapping("/bdJob")
public class BdJobController extends BaseController {

    private IBdJobService bdJobService;

    @ApiOperation(value = "新增定时任务")
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody BdCustomerJob bdCustomerJob) {
        return R.ok(bdJobService.createCustomerJob(bdCustomerJob));
    }

    @ApiOperation(value = "发送钉钉通知")
    @GetMapping("/send-DingTalk-Notifications")
    public R<Boolean> sendDingTalkNotifications(@RequestParam("clueId") Long clueId,
                                                @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                                @RequestParam("userName") String userName,
                                                @RequestParam("bdGuestSrcName") String bdGuestSrcName,
                                                @RequestParam("contactWay") String contactWay,
                                                @RequestParam("dingTalkUserId") String dingTalkUserId,
                                                @RequestParam("dingTalkType") Integer dingTalkType) {
        DingTalkMsgDTO dingTalkMsgDTO = new DingTalkMsgDTO(
                clueId, bdCustomersFollowId, userName, bdGuestSrcName, contactWay, dingTalkUserId, dingTalkType
        );
        return R.ok(bdJobService.sendDingTalkNotifications(dingTalkMsgDTO));
    }

    @ApiOperation(value = "投放线索分配给部门经理（负责人）")
    @GetMapping("/distribution-clue-manager")
    public R<Boolean> distributionClueManager(@RequestParam("clueId") Long clueId,
                                              @RequestParam("userId") Long userId,
                                              @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId) {
        DistributionClueDTO distributionClueDTO = new DistributionClueDTO(clueId, userId, bdCustomersFollowId);
        return R.ok(bdJobService.distributionClueManager(distributionClueDTO));
    }

    @ApiOperation(value = "线索掉入公海")
    @GetMapping("/fall-into-the-sea")
    public R<Boolean> fallIntoTheSea(@RequestParam("clueId") Long clueId,
                                     @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId) {
        FallIntoSeaDTO fallIntoSeaDTO = new FallIntoSeaDTO(clueId, bdCustomersFollowId);
        return R.ok(bdJobService.fallIntoTheSea(fallIntoSeaDTO));
    }

    @ApiOperation(value = "线索掉入个人")
    @GetMapping("/fall-into-the-person")
    public R<Boolean> fallIntoThePerson(@RequestParam("clueId") Long clueId,
                                        @RequestParam("userId") Long userId,
                                        @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                        @RequestParam("type") Integer type) {
        FallIntoPersonDTO fallIntoSeaDTO = new FallIntoPersonDTO(clueId, userId, bdCustomersFollowId, type);
        return R.ok(bdJobService.fallIntoThePerson(fallIntoSeaDTO));
    }

    @ApiOperation(value = "线索掉入商机池")
    @GetMapping("/fall-into-the-niche-pool")
    public R<Boolean> fallIntoTheNichePool(@RequestParam("clueId") Long clueId,
                                           @RequestParam("bdCustomersFollowId") Long bdCustomersFollowId,
                                           @RequestParam("type") Integer type) {
        FallIntoNichePoolDTO fallIntoNichePoolDTO = new FallIntoNichePoolDTO(clueId, bdCustomersFollowId, type);
        return R.ok(bdJobService.fallIntoTheNichePool(fallIntoNichePoolDTO));
    }

    @ApiOperation(value = "处理未生效定时任务")
    @GetMapping("/inactiveScheduledTasks")
    public R<Boolean> inactiveScheduledTasks(@RequestParam("jobId") Integer jobId, @RequestParam("cronTime") String cronTime) {
        return R.ok(bdJobService.inactiveScheduledTasks(jobId, cronTime));
    }

    @ApiOperation(value = "发送消息测试")
    @GetMapping("/sendDingMessageTest")
    public AjaxResult sendDingMessageTest() {
        return AjaxResult.success(bdJobService.sendDingMessageTest());
    }

    @Autowired
    public void setIBdJobService(IBdJobService bdJobService) {
        this.bdJobService = bdJobService;
    }
}
