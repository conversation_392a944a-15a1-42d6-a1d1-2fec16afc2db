package com.nnb.customer.enums;

import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首咨状态
 */
public enum FirstConsultEnum {
    valid(1, "有效"),
    inValid(2, "无效"),
    block_call(3, "未接通"),

            ;
    private Integer firstConsult;

    private String firstConsultName;


    public Integer getFirstConsult() {
        return firstConsult;
    }

    public void setFirstConsult(Integer firstConsult) {
        this.firstConsult = firstConsult;
    }

    public String getFirstConsultName() {
        return firstConsultName;
    }

    public void setFirstConsultName(String firstConsultName) {
        this.firstConsultName = firstConsultName;
    }

    FirstConsultEnum(Integer firstConsult, String firstConsultName) {
        this.firstConsult = firstConsult;
        this.firstConsultName = firstConsultName;
    }

    public static String getNFirstConsultName(Integer firstConsult) {
        List<FirstConsultEnum> collect = Arrays.stream(FirstConsultEnum.values()).filter(item -> item.firstConsult.equals(firstConsult)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect.get(0).getFirstConsultName();
        }
        return null;
    }
}
