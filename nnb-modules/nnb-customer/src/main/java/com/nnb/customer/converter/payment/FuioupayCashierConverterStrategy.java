package com.nnb.customer.converter.payment;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.customer.config.FuioupayPropertiesConfig;
import com.nnb.customer.constant.PaymentConstants;
import com.nnb.customer.converter.PaymentConverterStrategy;
import com.nnb.customer.domain.OrderInfo;
import com.nnb.customer.domain.OssFileForUploadVO;
import com.nnb.customer.domain.PayCompany;
import com.nnb.customer.domain.PaymentInfo;
import com.nnb.customer.domain.vo.PaymentInfoVo;
import com.nnb.customer.mapper.PaymentMapper;
import com.nnb.customer.service.OssService;
import com.nnb.customer.utils.FileUtil;
import com.nnb.customer.utils.QRCodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-08-22
 * @Version: 1.0
 */
@Service("1:FUIOUPAY:DISPOSABLE")
public class FuioupayCashierConverterStrategy implements PaymentConverterStrategy<PaymentInfoVo, PaymentInfo> {

    @Autowired
    private OssService ossService;

    @Autowired
    private PaymentMapper paymentMapper;

    @Autowired
    private FuioupayPropertiesConfig propertiesConfig;

    @Override
    public PaymentInfoVo getPaymentQRCodeInfo(PaymentInfo paymentInfo) {

        PaymentInfoVo paymentInfoVo = buildReturnParams(paymentInfo);
        //TODO 插入收银台id 插入记录
        paymentMapper.insertPayment(paymentInfoVo);
        return paymentInfoVo;

    }

    /**
     * 回填客户信息等到返回结果
     *
     * @param paymentInfo
     * @param paymentInfoVo
     */
    private void backFillCustomerReturnResult(PaymentInfo paymentInfo, PaymentInfoVo paymentInfoVo) {
        String name = null;
        //此判断对应详情页生成支付码，因为只传了orderId
        if (ObjectUtil.isEmpty(paymentInfo.getClueId()) && StringUtils.isEmpty(paymentInfo.getClientId())) {
            OrderInfo orderInfo = paymentMapper.selectOrderInfoById(paymentInfo.getOrderId());
            if (PaymentConstants.COMMIT_ORDER_TYPE_CLUE.equals(orderInfo.getCommitOrderType())) {
                paymentInfo.setClueId(orderInfo.getClueId());
            } else {
                paymentInfo.setClientId(orderInfo.getClientId());
            }
        }

        if (ObjectUtil.isEmpty(paymentInfo.getClueId())) {
            //企业客户
            name = paymentMapper.selectClientName(paymentInfo.getClientId());
        } else {
            //个人客户
            name = paymentMapper.selectClueName(paymentInfo.getClueId());
        }
        paymentInfoVo.setName(name);

        PayCompany payCompany = paymentMapper.selectPayCompanyMsg(paymentInfo.getPayCompanyId());
        paymentInfoVo.setPayCompanyName(payCompany.getName());
    }


    /**
     * 组装返回参数
     *
     * @param paymentInfo  本系统下单请求体
     * @return PaymentInfoVo
     */
    private PaymentInfoVo buildReturnParams(PaymentInfo paymentInfo) {
        //定义创建时间
        LocalDateTime createTime = LocalDateTime.now();
        //第三方交易流水号，己方交易流水号，己方订单号，创建时间，支付有效时间，支付完成时间，二维码链接，二维码图片
        PaymentInfoVo paymentInfoVo = new PaymentInfoVo();

        //此判断对应场景：订单详情生产二维码
        if (ObjectUtil.isEmpty(paymentInfo.getClueId()) && StringUtils.isEmpty(paymentInfo.getClientId())) {
            if (StringUtils.isEmpty(paymentInfo.getOrderId())) {
                throw new ServiceException("线索/客户/订单id都为空！");
            } else {
                paymentInfoVo.setOrderId(paymentInfo.getOrderId());
                OrderInfo orderInfo = paymentMapper.selectOrderInfoById(paymentInfo.getOrderId());
                paymentInfoVo.setOrderNumber(orderInfo.getOrderNumber());
                if (PaymentConstants.COMMIT_ORDER_TYPE_CLUE.equals(orderInfo.getCommitOrderType())) {
                    paymentInfo.setClueId(orderInfo.getClueId());
                    paymentInfoVo.setClueId(orderInfo.getClueId());
                } else {
                    paymentInfo.setClientId(orderInfo.getClientId());
                    paymentInfoVo.setClientId(orderInfo.getClientId());
                }
            }
        }else {
            //线索 or 客户提单时
            if (ObjectUtil.isNotEmpty(paymentInfo.getClueId())) {
                paymentInfoVo.setClueId(paymentInfo.getClueId());
            } else {
                paymentInfoVo.setClientId(paymentInfo.getClientId());
            }
        }

        //自定义生成收银台id，回调时返回
        String cashierId = UUID.randomUUID().toString().replace("-", "");
        paymentInfoVo.setCashierId(cashierId);
        //创建时间
        paymentInfoVo.setCreatedTime(createTime);
        //收银台链接
        backFillCustomerReturnResult(paymentInfo, paymentInfoVo);
        String cashierUrl = String.format(PaymentConstants.CASHIER_URL_TEMPLATE,
                propertiesConfig.getCashierUrl(), cashierId, paymentInfoVo.getName());
        paymentInfoVo.setQrCodeId(cashierUrl);
        //收银台链接转二维码上传至oss
        File file = QRCodeUtil.toFile(PaymentConstants.CHAR_SET, cashierUrl, PaymentConstants.QR_WIDTH, PaymentConstants.QR_HEIGHT);
        OssFileForUploadVO upload = ossService.upload(FileUtil.fileToMultipartFile(file), PaymentConstants.MODULE_NAME);
        paymentInfoVo.setPayImg(upload.getWrapFilePath());
        //其它信息
        paymentInfoVo.setPayStatus(PaymentConstants.UN_PAID);
        paymentInfoVo.setUserId(paymentInfo.getUserId());
        paymentInfoVo.setPayCompanyId(paymentInfo.getPayCompanyId());
        return paymentInfoVo;
    }
}
