package com.nnb.customer.mapper;

import java.util.Date;
import java.util.List;
import com.nnb.customer.domain.BdClueDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 线索分配记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@Repository
@Mapper
public interface BdClueDistributionMapper 
{
    /**
     * 查询线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 线索分配记录
     */
    public BdClueDistribution selectBdClueDistributionById(Long id);

    /**
     * 查询线索分配记录列表
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 线索分配记录集合
     */
    public List<BdClueDistribution> selectBdClueDistributionList(BdClueDistribution bdClueDistribution);

    /**
     * 新增线索分配记录
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 结果
     */
    public int insertBdClueDistribution(BdClueDistribution bdClueDistribution);
    public int insertBdClueDistributionList(@Param("list") List<BdClueDistribution> list);

    /**
     * 修改线索分配记录
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 结果
     */
    public int updateBdClueDistribution(BdClueDistribution bdClueDistribution);

    /**
     * 删除线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 结果
     */
    public int deleteBdClueDistributionById(Long id);

    /**
     * 批量删除线索分配记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdClueDistributionByIds(Long[] ids);

    public Date maxCreatedTime(BdClueDistribution bdClueDistribution);

    public Date minCreatedTime(BdClueDistribution bdClueDistribution);
}
