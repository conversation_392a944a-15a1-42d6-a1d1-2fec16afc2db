package com.nnb.customer.service.impl;

import java.util.List;

import com.nnb.customer.domain.BdLaunchPlatformEntity;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdLaunchPlatformMapper;
import com.nnb.customer.service.IBdLaunchPlatformService;

import javax.annotation.Resource;

/**
 * 投放平台管理配置，业务层实现。
 *
 * <AUTHOR>
 * @date 2022-06-28 11:20:32
 */
@Service
public class BdLaunchPlatformServiceImpl implements IBdLaunchPlatformService {
    @Resource
    private BdLaunchPlatformMapper bdLaunchPlatformMapper;

    /**
     * 查询投放平台管理配置列表。
     *
     * @param bdLaunchPlatform 投放平台管理配置。
     * @return 投放平台管理配置。
     * <AUTHOR>
     * @since 2022-06-28 11:22:06
     */
    @Override
    public List<BdLaunchPlatformEntity> selectBdLaunchPlatformList(BdLaunchPlatformEntity bdLaunchPlatform) {
        return bdLaunchPlatformMapper.selectBdLaunchPlatformList(bdLaunchPlatform);
    }

}
