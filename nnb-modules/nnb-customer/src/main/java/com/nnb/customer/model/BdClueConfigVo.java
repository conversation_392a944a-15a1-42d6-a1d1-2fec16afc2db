package com.nnb.customer.model;

import com.nnb.customer.domain.BdClueConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="BdClueConfigVo",description="部门线索管理时限配置Vo对象")
public class BdClueConfigVo extends BdClueConfig {
    private static final long serialVersionUID = 8404372461365739134L;

    @ApiModelProperty("部门名称")
    private String deptName;

}
