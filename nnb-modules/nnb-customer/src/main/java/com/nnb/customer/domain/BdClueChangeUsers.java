package com.nnb.customer.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 投放流转配置对象 bd_clue_change_users
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@ApiModel(value="BdClueChangeUsers",description="投放流转配置对象")
public class BdClueChangeUsers extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    @ApiModelProperty("主键自增")
    private Long id;

    /** 城市id  二级 */
    @Excel(name = "城市id  二级")
    @ApiModelProperty("城市id  二级")
    private Long areaId;

    /** 部门id */
    @Excel(name = "部门id")
    @ApiModelProperty("部门id")
    private Long deptId;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private Long numStatus;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private Long numUpdatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAreaId(Long areaId) 
    {
        this.areaId = areaId;
    }

    public Long getAreaId() 
    {
        return areaId;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setNumStatus(Long numStatus) 
    {
        this.numStatus = numStatus;
    }

    public Long getNumStatus() 
    {
        return numStatus;
    }
    public void setNumCreatedBy(Long numCreatedBy) 
    {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() 
    {
        return numCreatedBy;
    }
    public void setNumUpdatedBy(Long numUpdatedBy) 
    {
        this.numUpdatedBy = numUpdatedBy;
    }

    public Long getNumUpdatedBy() 
    {
        return numUpdatedBy;
    }
    public void setDatCreatedAt(Date datCreatedAt) 
    {
        this.datCreatedAt = datCreatedAt;
    }

    public Date getDatCreatedAt() 
    {
        return datCreatedAt;
    }
    public void setDatUpdatedAt(Date datUpdatedAt) 
    {
        this.datUpdatedAt = datUpdatedAt;
    }

    public Date getDatUpdatedAt() 
    {
        return datUpdatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("areaId", getAreaId())
            .append("deptId", getDeptId())
            .append("numStatus", getNumStatus())
            .append("numCreatedBy", getNumCreatedBy())
            .append("numUpdatedBy", getNumUpdatedBy())
            .append("datCreatedAt", getDatCreatedAt())
            .append("datUpdatedAt", getDatUpdatedAt())
            .toString();
    }
}
