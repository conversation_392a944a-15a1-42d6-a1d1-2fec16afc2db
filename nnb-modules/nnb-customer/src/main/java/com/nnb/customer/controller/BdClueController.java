package com.nnb.customer.controller;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.customer.constant.CustomerBdConstants;
import com.nnb.customer.domain.clue.CLueApply;
import com.nnb.customer.domain.nichepool.*;
import com.nnb.customer.model.BdImportClueErrorDto;
import com.nnb.customer.service.es.BdCLueEsService;
import com.nnb.customer.service.es.ESService;
import com.nnb.customer.utils.EsClueSyncPoolUtil;
import com.nnb.customer.utils.EsClueSyncUtil;
import org.apache.commons.collections.CollectionUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import com.alibaba.fastjson.JSON;

import com.baidu.aip.ocr.AipOcr;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.web.page.PageDomain;
import com.nnb.common.core.web.page.TableSupport;
import com.nnb.common.log.annotation.LogAnnotation;
import com.nnb.customer.domain.*;
import com.nnb.customer.model.BdClueDto;
import com.nnb.customer.model.BdImportClueADto;

import lombok.extern.slf4j.Slf4j;
import org.junit.validator.ValidateWith;
import org.springframework.amqp.core.AmqpTemplate;
import com.nnb.customer.model.BdImportClueDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.service.IBdClueService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 北斗线索Controller
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdClue")
@Api(tags = "BdClueController", description = "北斗线索")
@Slf4j
public class BdClueController extends BaseController {

    @Autowired
    private IBdClueService bdClueService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Resource
    private EsClueSyncUtil esClueSyncUtil;

    @Autowired
    private BdCLueEsService bdCLueEsService;

    /**
     * 获取线索列表各标签页数量
     */
    @ApiOperation(value = "获取线索列表各标签页数量")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClue.class)})
    @GetMapping(value = "/clueCount")
    public AjaxResult clueCount(BdClueSplit split) {
        return AjaxResult.success(bdClueService.clueCount(split));
    }


    /**
     * 线索精选卡片查询
     */
    @ApiOperation(value = "线索精选卡片查询")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClueResult.class)})
    @GetMapping("/queryChoiceCard")
    public AjaxResult queryChoiceCard(BdNicheFlowConf bdNicheFlowConf) {
        return AjaxResult.success(bdClueService.queryChoiceCard(bdNicheFlowConf));
    }

    /**
     * 批量更新线索batchUpdateBdClue::通过页面选择线索id操作数据
     */
//    @PreAuthorize(hasPermi = "customer:BdClue:batchUpdateBdClue")
    @ApiOperation(value = "批量更新线索")
    @PutMapping("/batchUpdateBdClue")
    public AjaxResult batchUpdateBdClue(@RequestBody BatchUpdateParam batchUpdateParam) {
        if (batchUpdateParam.getActType().equals(CustomerBdConstants.ActType.RECEIVE)) {
            return AjaxResult.success("您成功领取" + bdClueService.batchUpdateBdClue(batchUpdateParam) + "条！");
        } else {
            return toAjax(bdClueService.batchUpdateBdClue(batchUpdateParam));
        }
    }

//    /**
//     * 批量更新线索::通过高级筛选条件批量操作全数据
//     */
//    @ApiOperation(value = "批量更新线索::通过高级筛选条件批量操作全数据")
//    @PutMapping("/batchUpdateBySelect")
//    public AjaxResult batchUpdateBySelect(@RequestBody BatchUpdateParam batchUpdateParam)
//    {
//        //封装clueIdList
//        List<Long> clueIdList = bdClueService.selectBdClueIdList(batchUpdateParam.getBdClueParam());
//        if (ObjectUtil.isEmpty(clueIdList)){
//            return AjaxResult.error("筛选结果无数据！");
//        }
//        batchUpdateParam.setBdClueIdList(clueIdList);
//        return toAjax(bdClueService.batchUpdateBdClue(batchUpdateParam));
//    }

//    /**
//     * 线索管理新增分配按钮
//     * @param batchUpdateParam
//     * @return
//     * @throws ServiceException
//     */
//    @ApiOperation(value = "分配")
//    @PutMapping("/updateBySelect")
//    public AjaxResult batchUpdate(@RequestBody BatchUpdateParam batchUpdateParam) throws ServiceException {
//        List<Long> bdClueIdList = batchUpdateParam.getBdClueIdList();
//        int size = bdClueIdList.size();
//        if (size > 1) {
//            throw new ServiceException("数量不能大于1");
//        }
//
//        return toAjax(bdClueService.batchUpdateBdClue(batchUpdateParam));
//    }

    /**
     * 批量删除标签
     */
    @ApiOperation(value = "批量删除标签")
    @PutMapping("/batchDelUserTag")
    public AjaxResult batchDelUserTag(@RequestBody BatchAddUserTagParam batchAddUserTagParam) {
        return toAjax(bdClueService.batchDelUserTagService(batchAddUserTagParam));
    }

    /**
     * 批量新增标签
     */
    @ApiOperation(value = "批量新增标签")
    @PostMapping("/batchAddUserTag")
    public AjaxResult batchAddUserTag(@RequestBody BatchAddUserTagParam batchAddUserTagParam) {
        return toAjax(bdClueService.batchAddUserTagService(batchAddUserTagParam));
    }

    /**
     * 编辑用户标签
     */
    @ApiOperation(value = "修改用户标签")
    @PutMapping("/editUserTags/{id}")
    public AjaxResult editUserTags(@RequestBody List<Long> cartIds, @PathVariable Long numClueId) {
        return toAjax(bdClueService.updateBdClueUserTags(numClueId, cartIds));
    }

    /**
     * 查询北斗线索列表分页查询-线索中心,公海列表
     */
    @ApiOperation(value = "查询北斗线索列表分页查询-线索中心，公海列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClueResult.class)})
    @GetMapping("/pageList")
    public TableDataInfo pageList(BdClueParam bdClueParam) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        bdClueParam.setQueryNum(((pageNum - 1) * pageSize));
        bdClueParam.setQuerySize(pageSize);
        Long total = bdClueService.selectCountBdClueList(bdClueParam);
        List<BdClueResult> list = bdClueService.selectBdCluePageList(bdClueParam);
        return getDataTableAndTotal(list, total);
    }


    /**
     * 跟进线索列表分页查询-呼叫中心（15）、线索管理（14）、客保管理（1）、线索精选（16）列表
     */
    @ApiOperation(value = "跟进线索列表分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClueResult.class)})
    @GetMapping("/tempCluePageList")
    public TableDataInfo tempCluePageList(BdClueParam bdClueParam) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        bdClueParam.setQueryNum(((pageNum - 1) * pageSize));
        bdClueParam.setQuerySize(pageSize);
        Map<String, Object> resultMap = bdClueService.selectTempCluePageList(bdClueParam);
        List<BdClueResult> list = MapUtil.get(resultMap, "list", new TypeReference<List<BdClueResult>>() {
        }, new ArrayList<>());
        return getDataTableAndTotal(list, MapUtil.getLong(resultMap, "total"));
    }


    /**
     * 客保数据导出
     *
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "客保数据")
//    @PreAuthorize(hasPermi = "customer:BdClueKb:export")
    //@Log(title = "线索客保记录", businessType = BusinessType.EXPORT)
    @PostMapping("/exportKb")
    public void exportKb(HttpServletResponse response, BdClueParam bdClueParam) throws IOException {
        List<BdClueResult> list = bdClueService.selectBdlueKb(bdClueParam);
        ExcelUtil<BdClueResult> util = new ExcelUtil<BdClueResult>(BdClueResult.class);
        util.exportExcel(response, list, "客保管理投放线索数据");
    }

    @ApiOperation(value = "线索管理数据")
//    @PreAuthorize(hasPermi = "customer:BdClueGl:export")
    //@Log(title = "线索客保记录", businessType = BusinessType.EXPORT)
    @PostMapping("/exportGl")
    public void exportGl(HttpServletResponse response, BdClueParam bdClueParam) throws IOException {
        List<BdClueResult> list = bdClueService.selectBdlueGl(bdClueParam);
        ExcelUtil<BdClueResult> util = new ExcelUtil<BdClueResult>(BdClueResult.class);
        util.exportExcel(response, list, "线索管理投放线索数据");
    }


    /**
     * 查询北斗线索列表
     */
    @ApiOperation(value = "查询北斗线索列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClue.class)})
    @PreAuthorize(hasPermi = "customer:BdClue:list")
    @GetMapping("/list")
    public TableDataInfo list(BdClue bdClue) {
        Page<Object> objects = startPageReturn();
        List<BdClue> list = bdClueService.selectBdClueList(bdClue);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    /**
     * 导出北斗线索列表
     */
    @ApiOperation(value = "导出北斗线索列表")
    @PreAuthorize(hasPermi = "customer:BdClue:export")
    //@Log(title = "北斗线索", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdClue bdClue) throws IOException {
        List<BdClue> list = bdClueService.selectBdClueList(bdClue);
        ExcelUtil<BdClue> util = new ExcelUtil<BdClue>(BdClue.class);
        util.exportExcel(response, list, "北斗线索数据");
    }


    /**
     * 导入北斗线索
     *
     * @param file
     * @param updateSupport
     * @param creatType     创建节点，9：线索中心；14：线索管理；1：客保管理
     * @return
     * @throws Exception
     */
    @PostMapping("/importData")
    public void importData(MultipartFile file, Long creatType, HttpServletResponse response) throws Exception {
        ExcelUtil<BdImportClueDto> util = new ExcelUtil<>(BdImportClueDto.class);
        List<BdImportClueDto> bdClueList = util.importExcel(file.getInputStream());
        Map<String, Object> resutlMap = bdClueService.importBdClueList(bdClueList, creatType);
        //获取成功数量
        String successNum = MapUtil.getStr(resutlMap, "successNum", "0");
        //获取导出错误信息
        List<BdImportClueErrorDto> errorExportList = MapUtil.get(
                resutlMap, "errorExportList", new TypeReference<List<BdImportClueErrorDto>>() {
        }, new ArrayList<>());
        errorExportList.add(0, new BdImportClueErrorDto("共导入" + successNum + "条"));
        ExcelUtil<BdImportClueErrorDto> errorUtil = new ExcelUtil<>(BdImportClueErrorDto.class);
        errorUtil.exportExcel(response, errorExportList, "导入日志", "导入日志");
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<BdImportClueDto> util = new ExcelUtil<BdImportClueDto>(BdImportClueDto.class);
        util.importTemplateExcel(response, "北斗线索数据模板");
    }

    /**
     * 获取北斗线索详细信息
     */
    @ApiOperation(value = "获取北斗线索详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClue.class)})
    //@PreAuthorize(hasPermi = "customer:BdClue:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name = "id", value = "北斗线索id") @PathVariable("id") Long id) {
        return AjaxResult.success(bdClueService.selectBdClueById(id));
    }

    /**
     * 新增北斗线索
     */
    @ApiOperation(value = "新增北斗线索")
    //@PreAuthorize(hasPermi = "customer:BdClue:add")
    //@Log(title = "北斗线索", businessType = BusinessType.INSERT)
    @PostMapping
    @LogAnnotation("新增北斗线索")
    public AjaxResult add(@RequestBody BdClueDto dto) {
        //return toAjax(bdClueService.insertBdClue(bdClue));
        AjaxResult ajaxResult = toAjax(bdClueService.insertBdClueDto(dto));
        //UPDATE ES
        EsClueSyncPoolUtil.getPool().execute(() -> esClueSyncUtil.clueSync(dto.getId()));
        return ajaxResult;
    }

    /**
     * 小程序起名点击报名
     */
    @ApiOperation(value = "小程序起名点击报名")
    @PostMapping("/companyNameAdd")
    @LogAnnotation("新增北斗线索")
    public AjaxResult companyNameAdd(@RequestBody BdClueDto dto) {
        //return toAjax(bdClueService.insertBdClue(bdClue));
        return toAjax(bdClueService.companyNameAdd(dto));
    }

    /**
     * 新增北斗推广线索
     */
    @ApiOperation(value = "新增北斗推广线索")
    @PostMapping("/addPopularizeClue")
    @LogAnnotation("新增北斗推广线索")
    public AjaxResult addPopularizeClue(@RequestBody BdClueDto dto) {
        return AjaxResult.success(bdClueService.insertBdClueByPopularize(dto));
    }

    /**
     * 修改北斗线索
     */
    @ApiOperation(value = "修改北斗线索")
    @PreAuthorize(hasPermi = "customer:BdClue:edit")
    //@Log(title = "北斗线索", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdClue bdClue) {
        return toAjax(bdClueService.updateBdClue(bdClue));
    }

    /**
     * 删除北斗线索
     */
    @ApiOperation(value = "删除北斗线索")
    @PreAuthorize(hasPermi = "customer:BdClue:remove")
    //@Log(title = "北斗线索", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bdClueService.deleteBdClueByIds(ids));
    }

    /**
     * 校验当前联系电话所属线索状态
     */
    @ApiOperation(value = "校验当前联系电话所属线索状态")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClue.class)})
    //@PreAuthorize(hasPermi = "customer:BdClue:query")
    @GetMapping(value = "/phoneStatus/{phone}")
    public AjaxResult phoneStatus(@ApiParam(name = "phone", value = "联系电话") @PathVariable("phone") String phone) {
        return AjaxResult.success(bdClueService.phoneStatus(phone));
    }

    /**
     * 校验当前联系电话所属推广线索状态
     */
    @ApiOperation(value = "校验当前联系电话所属推广线索状态")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClue.class)})
    @GetMapping(value = "/popularPhoneStatus/{phone}")
    public AjaxResult popularPhoneStatus(@ApiParam(name = "phone", value = "联系电话") @PathVariable("phone") String phone) {
        return AjaxResult.success(bdClueService.popularPhoneStatus(phone));
    }

    /**
     * 线索置完成
     */
    @PutMapping("/completionClue")
    public AjaxResult completionClue(@RequestParam("numClueId") Long numClueId) {
        return toAjax(bdClueService.completionClue(numClueId));
    }

    /**
     * 客保掉保 (cron = "0 1 0 * * ? ")
     */
    @GetMapping(value = "/task/refreshKb")
    public AjaxResult refreshKb() {
        bdClueService.refreshKb();
        return AjaxResult.success();
    }

    /**
     * 线索管理回收
     */
    @GetMapping(value = "/task/refreshClueManagement")
    public AjaxResult refreshClueManagement() {
        bdClueService.refreshClueManagement();
        return AjaxResult.success();
    }

    /**
     * 商机池流转下一商机池
     */
    @GetMapping(value = "/task/refreshOpportunity")
    public AjaxResult refreshOpportunity() {
        bdClueService.refreshOpportunity();
        return AjaxResult.success();
    }

    /**
     * 线索中心、公海，根据自动流转配置下发线索管理（高意向，入过客保）
     */
    @GetMapping(value = "/task/refreshAutoAssign")
    public AjaxResult refreshAutoAssign() {
        bdClueService.refreshAutoAssign();
        return AjaxResult.success();
    }

    /**
     * 自动分配跟进统计
     */
    @GetMapping(value = "/task/refreshAutoAssignCount")
    public AjaxResult refreshAutoAssignCount() {
        bdClueService.refreshAutoAssignCount();
        return AjaxResult.success();
    }


    /**
     * 定时任务 客户发展部  人工意向度是高意向的 加到指定得商机池中
     *
     * @return
     */
    @GetMapping(value = "/task/moveNichePool")
    public AjaxResult moveNichePool() {
        bdClueService.moveNichePool();
        return AjaxResult.success();
    }


    @GetMapping(value = "/mq")
    public void mqTest() {

        for (int i = 0; i < 5; i++) {
            BdStandInsideLetter bdStandInsideLetter = new BdStandInsideLetter();
            bdStandInsideLetter.setToUserId(1L);//收件人id
            bdStandInsideLetter.setFromUserId(2L);//收件人id
            bdStandInsideLetter.setLetterContent(String.valueOf(111222));
            bdStandInsideLetter.setType(2);//呼叫中心到线索管理
            System.out.println(bdStandInsideLetter.toString());
            amqpTemplate.convertAndSend("topic", "customer.message.notice.queue", JSON.toJSONString(bdStandInsideLetter));
            //amqpTemplate.convertAndSend("topic", "customer.message.notice.queue", "bdStandInsideLetter.toString()");
        }

    }

    /**
     * 客保批量分配按钮
     */
    @PutMapping("/update")
    public AjaxResult update(@RequestBody BatchUpdateParam batchUpdateParam) {
        //封装clueIdList
        List<Long> clueIdList = bdClueService.selectBdClueIdList(batchUpdateParam.getBdClueParam());
        batchUpdateParam.setBdClueIdList(clueIdList);
        return toAjax(bdClueService.update(batchUpdateParam));
    }

    /**
     * 接通量意向量统计
     */
    @GetMapping("/statement")
    public TableDataInfo statement(BdStatementVo bdStatementParam) {
        startPage();
        List<BdStatementVo> statement = bdClueService.statement(bdStatementParam);
        return getDataTable(statement);
    }

    /**
     * @param file
     * @param creatType 创建节点，9：线索中心；14：线索管理；1：客保管理
     * @return
     * @throws Exception
     */
    @PostMapping("/importDataA")
    public AjaxResult importDataA(MultipartFile file, boolean updateSupport, Long creatType) throws Exception {
        log.info("——————操作用户::{}-图片识别-线索id::{}::{}", file, updateSupport, creatType);
        ExcelUtil<BdImportClueADto> util = new ExcelUtil<BdImportClueADto>(BdImportClueADto.class);
        List<BdImportClueADto> bdClueAList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        log.info("资料上传的数据为:{}", bdClueAList);
        String message = bdClueService.importBdClueAList(bdClueAList, creatType, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplateA")
    public void importTemplateA(HttpServletResponse response) throws IOException {
        ExcelUtil<BdImportClueADto> util = new ExcelUtil<BdImportClueADto>(BdImportClueADto.class);
        util.importTemplateExcel(response, "资料上传模板");
    }

    @GetMapping(value = "/task/moveCenter")
    public AjaxResult moveCenter() {
        bdClueService.moveCenter();
        return AjaxResult.success();
    }

    @ApiOperation(value = "分配线索勾选全部数据接口")
    @PutMapping("/operateAllData")
    public AjaxResult operateAllData(@RequestBody OperateAllDataParam operateAllDataParam) {
        try {
            return bdClueService.operateAllData(operateAllDataParam);
        } catch (Exception e) {
            log.error("批量操作数据异常，异常信息为{}", e);
            return AjaxResult.error("操作失败");
        }
    }

    /**
     * 图片识别
     *
     * @param creatType 创建节点，9：线索中心；14：线索管理；1：客保管理
     * @return
     */
    @ApiOperation(value = "图片识别")
    @PostMapping("/sample")
    public AjaxResult sample(MultipartFile file, Long creatType) {
        log.info("——————操作用户::{}-图片识别-线索id::{}", file, creatType);
        String operName = SecurityUtils.getUsername();
        String message = bdClueService.importBdOcrClueList(file, creatType, operName);
        log.info("图片识别的返回结果{}", message);
        return AjaxResult.success(message);

    }

    @ApiOperation(value = "根据id获取线索信息")
    @GetMapping("/getBdClueById/{id}")
    public R<BdClue> getBdClueById(@PathVariable(value = "id") Long id) {
        return R.ok(bdClueService.getBdClueById(id));
    }

    @ApiOperation(value = "根据phone创建一条已成交的线索的相关信息")
    @GetMapping("/addBdClueByPhone/{phone}/{clientId}/{numCityId}/{contactName}")
    public R<BdClue> addBdClueByPhone(@PathVariable(value = "phone") String phone, @PathVariable(value = "clientId") Long clientId,  @PathVariable(value = "numCityId") Long numCityId,  @PathVariable(value = "contactName") String contactName) {
        return R.ok(bdClueService.addBdClueByPhone(phone, clientId, numCityId, contactName));
    }


    @ApiOperation(value = "根据phone创建一条已成交的线索的相关信息")
    @GetMapping("/sendEmail")
    public R addBdClueByPhone(String email, String title, String msg) {
        return R.ok(bdClueService.sendMeal(email, title, msg));
    }

    @ApiOperation(value = "客保管理变更跟进人")
    @PostMapping("updateKbUser")
    public AjaxResult updateKbUser(@RequestBody BdClue bdClue) {
        return toAjax(bdClueService.updateKbUser(bdClue));
    }

    @GetMapping(value = "/getInfoByClueIds")
    public R<List<BdClue>> getInfoByClueIds(@RequestParam("clueIds") List<Long> clueIds) {
        return R.ok(bdClueService.getInfoByClueIds(clueIds));
    }

    @GetMapping(value = "/getAdCLueSource")
    public AjaxResult getAdCLueSource(){
        return AjaxResult.success(bdClueService.getAdClueSouce());
    }

    @GetMapping(value = "/resolveClueNodeTime")
    public AjaxResult resolveClueNodeTime(){
        return AjaxResult.success(bdClueService.resolveClueNodeTime());
    }

    @ApiOperation(value = "同步钉钉信息")
    @GetMapping("/updateUserDingID")
    public R updateUserDingID(@RequestParam(required = false, value = "num")String num){
        return R.ok(bdClueService.updateUserDingID(num));
    }

    @ApiOperation(value = "商机池")
    @PostMapping("/getNichePool")
    public TableDataInfo getNichePool(@RequestBody NichePoolFirstParam nichePoolFirstParam){
        List<NichePoolVo> nichePoolVos = new ArrayList<>();
        Integer pageNum = nichePoolFirstParam.getPageNo();
        Integer pageSize = nichePoolFirstParam.getPageSize();
        Long total = 0L;

        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            Page<Object> page = PageHelper.startPage(pageNum, pageSize, null);
            nichePoolVos = bdClueService.getNichePool(nichePoolFirstParam);
            total = page.getTotal();
        }
        return getDataTableAndTotal(nichePoolVos, total);
    }

    @ApiOperation(value = "商机池列表检索")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClueNicheVo.class)})
    @GetMapping("/getBdClueNicheVo")
    public TableDataInfo getBdClueNicheVo(BdClueNicheParam bdClueNicheParam) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<BdClueNicheVo> bdClueNicheVo = new ArrayList<>();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        Long total = 0L;
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            bdClueNicheParam.setQueryNum(pageNum);
            bdClueNicheParam.setQuerySize(pageSize);
            bdClueNicheVo = bdClueService.getBdClueNicheVo(bdClueNicheParam, total);
            total = CollectionUtils.isNotEmpty(bdClueNicheVo) ? bdClueNicheVo.get(0).getTotal() : 0L;
        }
        return getDataTableAndTotal(bdClueNicheVo, total);
    }

    @ApiOperation(value = "商机池")
    @PostMapping("/getNichePoolByEs")
    public TableDataInfo getNichePoolByEs(@RequestBody NichePoolFirstParam nichePoolFirstParam)throws IOException{
        List<NichePoolVo> nichePoolVos = new ArrayList<>();
        Integer pageNum = nichePoolFirstParam.getPageNo();
        Integer pageSize = nichePoolFirstParam.getPageSize();
        Long total = 0L;

        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            nichePoolVos = bdCLueEsService.getNichePoolBoolQuery(nichePoolFirstParam, total);
            total = (CollectionUtils.isNotEmpty(nichePoolVos) ? nichePoolVos.get(0).getTotal() : 0L);
        }
        return getDataTableAndTotal(nichePoolVos, total);
    }

    @ApiOperation(value = "商机池列表检索")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = BdClueNicheVo.class)})
    @GetMapping("/getBdClueNicheVoByEs")
    public TableDataInfo getBdClueNicheVoByEs(BdClueNicheParam bdClueNicheParam) throws IOException{
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<BdClueNicheVo> bdClueNicheVo = new ArrayList<>();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        Long total = 0L;
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            bdClueNicheParam.setQueryNum(pageNum);
            bdClueNicheParam.setQuerySize(pageSize);
            bdClueNicheVo = bdCLueEsService.getBdClueNicheVoBoolQuery(bdClueNicheParam, total);
            total = CollectionUtils.isNotEmpty(bdClueNicheVo) ? bdClueNicheVo.get(0).getTotal() : 0L;
        }
        return getDataTableAndTotal(bdClueNicheVo, total);
    }

    @ApiOperation(value = "导出商机池手机号")
    @GetMapping("/export-niche-pool-data")
    public void exportNichePoolData(BdClueNicheParam bdClueNicheParam, HttpServletResponse response) throws IOException {

        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<BdClueNicheVo> bdClueNicheVo = new ArrayList<>();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        Long total = 0L;
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            bdClueNicheParam.setQueryNum(pageNum);
            bdClueNicheParam.setQuerySize(pageSize);
            bdClueNicheVo = bdCLueEsService.getBdClueNicheVoBoolQuery(bdClueNicheParam, total);
            total = CollectionUtils.isNotEmpty(bdClueNicheVo) ? bdClueNicheVo.get(0).getTotal() : 0L;
        }
        List<BdClueNicheParam> collect = new ArrayList<>();

//        int page = total.intValue() / 1000;
//        if (total.intValue() % 1000 != 0){
//            page = page + 1;
//        }
//        for (int i = 1; i < page; i++) {
        bdClueNicheParam.setQueryNum(1);
        bdClueNicheParam.setQuerySize(3334);
        bdClueNicheVo = bdCLueEsService.getBdClueNicheVoBoolQuery(bdClueNicheParam, total);
        List<BdClueNicheParam> list = bdClueNicheVo.stream()
                .map(en -> {
                    BdClueNicheParam param = new BdClueNicheParam();
                    param.setPhone(en.getVcPhone());
                    return param;
                }).collect(Collectors.toList());
        collect.addAll(list);
//        }
        ExcelUtil<BdClueNicheParam> util = new ExcelUtil<BdClueNicheParam>(BdClueNicheParam.class);
        util.exportExcel(response, collect, "hao", "hao");
    }


    @ApiOperation(value = "统计线索")
    @GetMapping("/statisticsBdClue")
    public R statisticsBdClue() {
        bdClueService.statisticsClue();
        return R.ok();
    }

    @ApiOperation(value = "线索报名")
    @PostMapping("/bdClueApply")
    public AjaxResult bdClueApply(@RequestBody CLueApply cLueApply) {
        return toAjax(bdClueService.bdClueApply(cLueApply));
    }

    @ApiOperation(value = "统计该手机号下是否有财务审核通过的订单")
    @GetMapping("/countFinishOrder/{vcPhone}")
    public AjaxResult countFinishOrder(@PathVariable("vcPhone")String vcPhone) {
        return AjaxResult.success(bdClueService.countFinishOrder(vcPhone));
    }

    @ApiOperation(value = "不在客保管理，线索管理定时更新企业主体")
    @GetMapping("/updateEnterpriseDominant")
    public void updateEnterpriseDominant(){
        bdClueService.updateEnterpriseDominant();
    }

}
