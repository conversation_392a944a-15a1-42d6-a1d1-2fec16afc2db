package com.nnb.customer.service.thirdparty;

import com.alibaba.fastjson.JSONObject;
import com.nnb.customer.config.FeignMultipartSupportConfig;
import com.nnb.customer.model.EmptyPhoneCheckParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

/**
 * 空号检测请求
 *
 * <AUTHOR>
 * @date 2022-02-24
 */
@FeignClient(name = "nnb-customer", url = "http://api.zqyjc.com/open/api" ,configuration = FeignMultipartSupportConfig.class)
public interface IBdEmptyPhoneCheck {
    @RequestMapping(value = "/batchCheck",method = RequestMethod.POST, consumes = "application/json")
    JSONObject todo(EmptyPhoneCheckParam emptyPhoneCheck);

    @RequestMapping(value = "/batchCheckNew",method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    JSONObject todoNew(EmptyPhoneCheckParam emptyPhoneCheck);
}
