package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdClueDistribution;

/**
 * 线索分配记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface IBdClueDistributionService 
{
    /**
     * 查询线索分配记录
     * 
     * @param id 线索分配记录主键
     * @return 线索分配记录
     */
    public BdClueDistribution selectBdClueDistributionById(Long id);

    /**
     * 查询线索分配记录列表
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 线索分配记录集合
     */
    public List<BdClueDistribution> selectBdClueDistributionList(BdClueDistribution bdClueDistribution);

    /**
     * 新增线索分配记录
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 结果
     */
    public int insertBdClueDistribution(BdClueDistribution bdClueDistribution);

    /**
     * 修改线索分配记录
     * 
     * @param bdClueDistribution 线索分配记录
     * @return 结果
     */
    public int updateBdClueDistribution(BdClueDistribution bdClueDistribution);

    /**
     * 批量删除线索分配记录
     * 
     * @param ids 需要删除的线索分配记录主键集合
     * @return 结果
     */
    public int deleteBdClueDistributionByIds(Long[] ids);

    /**
     * 删除线索分配记录信息
     * 
     * @param id 线索分配记录主键
     * @return 结果
     */
    public int deleteBdClueDistributionById(Long id);
}
