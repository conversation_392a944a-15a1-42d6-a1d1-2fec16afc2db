package com.nnb.customer.mapper;

import java.util.List;
import com.nnb.customer.domain.BdGuestSrcsChannel;

/**
 * 获客来源媒体账号配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface BdGuestSrcsChannelMapper 
{
    /**
     * 查询获客来源媒体账号配置
     * 
     * @param id 获客来源媒体账号配置主键
     * @return 获客来源媒体账号配置
     */
    public BdGuestSrcsChannel selectBdGuestSrcsChannelById(Long id);

    /**
     * 查询获客来源媒体账号配置列表
     * 
     * @param bdGuestSrcsChannel 获客来源媒体账号配置
     * @return 获客来源媒体账号配置集合
     */
    public List<BdGuestSrcsChannel> selectBdGuestSrcsChannelList(BdGuestSrcsChannel bdGuestSrcsChannel);

    /**
     * 新增获客来源媒体账号配置
     * 
     * @param bdGuestSrcsChannel 获客来源媒体账号配置
     * @return 结果
     */
    public int insertBdGuestSrcsChannel(BdGuestSrcsChannel bdGuestSrcsChannel);

    /**
     * 修改获客来源媒体账号配置
     * 
     * @param bdGuestSrcsChannel 获客来源媒体账号配置
     * @return 结果
     */
    public int updateBdGuestSrcsChannel(BdGuestSrcsChannel bdGuestSrcsChannel);

    /**
     * 删除获客来源媒体账号配置
     * 
     * @param id 获客来源媒体账号配置主键
     * @return 结果
     */
    public int deleteBdGuestSrcsChannelById(Long id);

    /**
     * 批量删除获客来源媒体账号配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdGuestSrcsChannelByIds(Long[] ids);

    public int deleteBdGuestSrcsChannelByGuest(Long guestId);
}
