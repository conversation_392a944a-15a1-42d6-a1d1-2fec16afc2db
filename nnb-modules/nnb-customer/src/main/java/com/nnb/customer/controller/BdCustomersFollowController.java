package com.nnb.customer.controller;

import java.util.List;

import com.nnb.customer.domain.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.service.IBdCustomersFollowService;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 客户跟进记录Controller
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdCustomersFollow")
@Api(tags = "BdCustomersFollowController", description = "客户跟进记录")
public class BdCustomersFollowController extends BaseController
{
    @Autowired
    private IBdCustomersFollowService bdCustomersFollowService;

    /**BdClueController
     * 查询客户跟进记录列表
     */
    @ApiOperation(value = "查询客户跟进记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdCustomersFollow.class)})
    //@PreAuthorize(hasPermi = "customer:BdCustomersFollow:list")R
    @GetMapping("/list")
    public TableDataInfo list(BdCustomersFollowParam bdCustomersFollow)
    {
        startPage();
        List<BdCustomersFollowResult> list = bdCustomersFollowService.selectBdCustomersFollowList(bdCustomersFollow);
        return getDataTable(list);
    }

    /**
     * 获取客户跟进记录详细信息
     */
    @ApiOperation(value = "获取客户跟进记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdCustomersFollow.class)})
    @PreAuthorize(hasPermi = "customer:BdCustomersFollow:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="客户跟进记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdCustomersFollowService.selectBdCustomersFollowById(id));
    }

    /**
     * 新增客户跟进记录
     */
    @ApiOperation(value = "新增客户跟进记录")
    @PostMapping
    public AjaxResult add(@RequestBody BdAddFollowParam bdAddFollowParam)
    {
        return toAjax(bdCustomersFollowService.insertBdCustomersFollowParam(bdAddFollowParam));
    }

    /**
     * 新增老客户跟进记录
     */
    @ApiOperation(value = "新增老客户跟进记录")
    @PostMapping("/addOldEnterpriseFollow")
    public AjaxResult addOldEnterpriseFollow(@RequestBody OldEnterpriseAddFollowParam bdAddFollowParam)
    {
        return toAjax(bdCustomersFollowService.insertOldEnterpriseFollowParam(bdAddFollowParam));
    }

    /**
     * 修改客户跟进记录
     */
    @ApiOperation(value = "修改客户跟进记录")
    @PreAuthorize(hasPermi = "customer:BdCustomersFollow:edit")
    //@Log(title = "客户跟进记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdCustomersFollow bdCustomersFollow)
    {
        return toAjax(bdCustomersFollowService.updateBdCustomersFollow(bdCustomersFollow));
    }

    /**
     * 删除客户跟进记录
     */
    @ApiOperation(value = "删除客户跟进记录")
    @PreAuthorize(hasPermi = "customer:BdCustomersFollow:remove")
    //@Log(title = "客户跟进记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdCustomersFollowService.deleteBdCustomersFollowByIds(ids));
    }

    /**
     * 轮询查询未提醒的线索记录
     * @param numUserId
     * @return
     */
    @ApiOperation(value = "轮询查询未提醒的线索记录")
    //@Log(title = "客户跟进记录", businessType = BusinessType.DELETE)
    @GetMapping("/getUnRemindFollow/{numUserId}")
    public AjaxResult getUnRemindFollow(@PathVariable("numUserId") Long numUserId){
        return AjaxResult.success(bdCustomersFollowService.getUnRemindFollow(numUserId));
    }

    @ApiOperation(value = "查询最后一次商机池绑定的意向产品")
    @GetMapping("/getNicheFollowProduct")
    public AjaxResult getNicheFollowProduct(@RequestParam("clueId") Long clueId){
        return AjaxResult.success(bdCustomersFollowService.getNicheFollowProduct(clueId));
    }
}
