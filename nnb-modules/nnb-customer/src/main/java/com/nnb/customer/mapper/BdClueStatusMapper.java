package com.nnb.customer.mapper;

import java.util.List;
import com.nnb.customer.domain.BdClueStatus;
import com.nnb.customer.domain.es.ESBdClueStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 线索状态相关数据Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@Repository
public interface BdClueStatusMapper {
    /**
     * 查询线索状态相关数据
     *
     * @param id 线索状态相关数据主键
     * @return 线索状态相关数据
     */
    public BdClueStatus selectBdClueStatusById(Long id);

    /**
     * 查询线索状态相关数据列表
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 线索状态相关数据集合
     */
    public List<BdClueStatus> selectBdClueStatusList(BdClueStatus bdClueStatus);

    /**
     * 新增线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int insertBdClueStatus(BdClueStatus bdClueStatus);

    /**
     * 修改线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int updateBdClueStatus(BdClueStatus bdClueStatus);

    /**
     * 修改线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int updateBdClueStatusByClueId(BdClueStatus bdClueStatus);

    /**
     * 删除线索状态相关数据
     *
     * @param id 线索状态相关数据主键
     * @return 结果
     */
    public int deleteBdClueStatusById(Long id);

    /**
     * 批量删除线索状态相关数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdClueStatusByIds(Long[] ids);

    /**
     * 根据线索id获取信息
     *
     * @param clueId 线索id
     * @return 结果
     */
    BdClueStatus selectInfoByClueId(Long clueId);

    List<BdClueStatus> selectInfoByClueIdList(@Param("clueIdList") List<Long> clueIdList);


    /**
     * 新增或修改线索状态相关数据
     *
     * @param bdClueStatus 线索状态相关数据
     * @return 结果
     */
    public int replaceBdClueStatus(BdClueStatus bdClueStatus);

    List<ESBdClueStatus> selectByClueIdListEs(@Param("bdClueIdList") List<Long> bdClueIdList);
}
