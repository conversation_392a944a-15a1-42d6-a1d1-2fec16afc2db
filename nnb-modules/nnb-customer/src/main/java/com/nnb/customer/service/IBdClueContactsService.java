package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdClueContacts;

/**
 * 线索联系人Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface IBdClueContactsService 
{
    /**
     * 查询线索联系人
     * 
     * @param id 线索联系人主键
     * @return 线索联系人
     */
    public BdClueContacts selectBdClueContactsById(Long id);

    /**
     * 查询线索联系人列表
     * 
     * @param bdClueContacts 线索联系人
     * @return 线索联系人集合
     */
    public List<BdClueContacts> selectBdClueContactsList(BdClueContacts bdClueContacts);

    /**
     * 新增线索联系人
     * 
     * @param bdClueContacts 线索联系人
     * @return 结果
     */
    public int insertBdClueContacts(BdClueContacts bdClueContacts);

    /**
     * 修改线索联系人
     * 
     * @param bdClueContacts 线索联系人
     * @return 结果
     */
    public int updateBdClueContacts(BdClueContacts bdClueContacts);

    /**
     * 批量删除线索联系人
     * 
     * @param ids 需要删除的线索联系人主键集合
     * @return 结果
     */
    public int deleteBdClueContactsByIds(Long[] ids);

    /**
     * 删除线索联系人信息
     * 
     * @param id 线索联系人主键
     * @return 结果
     */
    public int deleteBdClueContactsById(Long id);

    /**
     * 置顶线索联系人
     *
     * @param bdClueContacts 线索联系人
     * @return 结果
     */
    public int editSort(BdClueContacts bdClueContacts);

    /**
     *
     * @param clueId
     * @return
     */
    public List<BdClueContacts> getBdClueContactByClueId(Long clueId);
}
