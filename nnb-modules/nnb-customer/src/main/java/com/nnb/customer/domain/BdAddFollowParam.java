package com.nnb.customer.domain;

import com.nnb.customer.domain.product.BdClueProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BdCustomersFollow",description="客户跟进记录对象")
public class BdAddFollowParam extends BdCustomersFollow {

    @ApiModelProperty("用户标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty("人工外呼意向度")
    private Integer numManAiWish;

    @ApiModelProperty("拨打状态")
    private Long numDialStatus;

    @ApiModelProperty("联系人角色标签id集合")
    private List<Long> roleTagIds;

    @ApiModelProperty("操作菜单， 1：启照多,2:线索管理，3：客保管理")
    private Integer operateMenuType;

    @ApiModelProperty("首咨， 1有效、2无效、3未接通")
    private Integer firstConsult;

    @ApiModelProperty("首咨分数： 0、10、20、40、60")
    private Integer firstConsultScore;

    @ApiModelProperty("首咨无效原因， 1没咨询过、2空号及停机、3外地业务、4电商微信号不存在或不允许添加、5业务做不了")
    private Integer firstConsultInvalid;

    @ApiModelProperty("是否校验首咨字段：1校验")
    private Integer source;

    @ApiModelProperty("商机池Id")
    private Long nicheFlowConfId;

    @ApiModelProperty("产品意向度集合")
    private List<BdClueProduct> productIntentionList;
}
