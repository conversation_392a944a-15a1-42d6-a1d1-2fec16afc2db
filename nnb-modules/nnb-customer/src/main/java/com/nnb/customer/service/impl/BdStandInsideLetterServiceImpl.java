package com.nnb.customer.service.impl;

import java.util.List;

import com.nnb.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.customer.mapper.BdStandInsideLetterMapper;
import com.nnb.customer.domain.BdStandInsideLetter;
import com.nnb.customer.service.IBdStandInsideLetterService;

/**
 * 站内信记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-08-02
 */
@Service
public class BdStandInsideLetterServiceImpl implements IBdStandInsideLetterService 
{
    @Autowired
    private BdStandInsideLetterMapper bdStandInsideLetterMapper;

    /**
     * 查询站内信记录
     * 
     * @param id 站内信记录主键
     * @return 站内信记录
     */
    @Override
    public BdStandInsideLetter selectBdStandInsideLetterById(Long id)
    {
        return bdStandInsideLetterMapper.selectBdStandInsideLetterById(id);
    }

    /**
     * 查询站内信记录列表
     * 
     * @param bdStandInsideLetter 站内信记录
     * @return 站内信记录
     */
    @Override
    public List<BdStandInsideLetter> selectBdStandInsideLetterList(BdStandInsideLetter bdStandInsideLetter)
    {
        return bdStandInsideLetterMapper.selectBdStandInsideLetterList(bdStandInsideLetter);
    }

    /**
     * 新增站内信记录
     * 
     * @param bdStandInsideLetter 站内信记录
     * @return 结果
     */
    @Override
    public int insertBdStandInsideLetter(BdStandInsideLetter bdStandInsideLetter)
    {
        bdStandInsideLetter.setCreateTime(DateUtils.getNowDate());
        return bdStandInsideLetterMapper.insertBdStandInsideLetter(bdStandInsideLetter);
    }

    /**
     * 修改站内信记录
     * 
     * @param bdStandInsideLetter 站内信记录
     * @return 结果
     */
    @Override
    public int updateBdStandInsideLetter(BdStandInsideLetter bdStandInsideLetter)
    {
        bdStandInsideLetter.setUpdateTime(DateUtils.getNowDate());
        return bdStandInsideLetterMapper.updateBdStandInsideLetter(bdStandInsideLetter);
    }

    /**
     * 批量删除站内信记录
     * 
     * @param ids 需要删除的站内信记录主键
     * @return 结果
     */
    @Override
    public int deleteBdStandInsideLetterByIds(Long[] ids)
    {
        return bdStandInsideLetterMapper.deleteBdStandInsideLetterByIds(ids);
    }

    /**
     * 删除站内信记录信息
     * 
     * @param id 站内信记录主键
     * @return 结果
     */
    @Override
    public int deleteBdStandInsideLetterById(Long id)
    {
        return bdStandInsideLetterMapper.deleteBdStandInsideLetterById(id);
    }
}
