package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdNicheGuestSrcs;
import com.nnb.customer.service.IBdNicheGuestSrcsService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 商机线索来源关系Controller
 * 
 * <AUTHOR>
 * @date 2022-03-09
 */
@RestController
@RequestMapping("/BdNicheGuestSrcs")
@Api(tags = "BdNicheGuestSrcsController", description = "商机线索来源关系")
public class BdNicheGuestSrcsController extends BaseController
{
    @Autowired
    private IBdNicheGuestSrcsService bdNicheGuestSrcsService;

    /**
     * 查询商机线索来源关系列表
     */
    @ApiOperation(value = "查询商机线索来源关系列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdNicheGuestSrcs.class)})
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:list")
    @GetMapping("/list")
    public TableDataInfo list(BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        startPage();
        List<BdNicheGuestSrcs> list = bdNicheGuestSrcsService.selectBdNicheGuestSrcsList(bdNicheGuestSrcs);
        return getDataTable(list);
    }

    /**
     * 导出商机线索来源关系列表
     */
    @ApiOperation(value = "导出商机线索来源关系列表")
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:export")
    //@Log(title = "商机线索来源关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdNicheGuestSrcs bdNicheGuestSrcs) throws IOException
    {
        List<BdNicheGuestSrcs> list = bdNicheGuestSrcsService.selectBdNicheGuestSrcsList(bdNicheGuestSrcs);
        ExcelUtil<BdNicheGuestSrcs> util = new ExcelUtil<BdNicheGuestSrcs>(BdNicheGuestSrcs.class);
        util.exportExcel(response, list, "商机线索来源关系数据");
    }

    /**
     * 获取商机线索来源关系详细信息
     */
    @ApiOperation(value = "获取商机线索来源关系详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdNicheGuestSrcs.class)})
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:query")
    @GetMapping(value = "/{numNicheId}")
    public AjaxResult getInfo(@ApiParam(name="numNicheId",value="商机线索来源关系id") @PathVariable("numNicheId") Long numNicheId)
    {
        return AjaxResult.success(bdNicheGuestSrcsService.selectBdNicheGuestSrcsByNumNicheId(numNicheId));
    }

    /**
     * 新增商机线索来源关系
     */
    @ApiOperation(value = "新增商机线索来源关系")
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:add")
    //@Log(title = "商机线索来源关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        return toAjax(bdNicheGuestSrcsService.insertBdNicheGuestSrcs(bdNicheGuestSrcs));
    }

    /**
     * 修改商机线索来源关系
     */
    @ApiOperation(value = "修改商机线索来源关系")
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:edit")
    //@Log(title = "商机线索来源关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdNicheGuestSrcs bdNicheGuestSrcs)
    {
        return toAjax(bdNicheGuestSrcsService.updateBdNicheGuestSrcs(bdNicheGuestSrcs));
    }

    /**
     * 删除商机线索来源关系
     */
    @ApiOperation(value = "删除商机线索来源关系")
    @PreAuthorize(hasPermi = "customer:BdNicheGuestSrcs:remove")
    //@Log(title = "商机线索来源关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{numNicheIds}")
    public AjaxResult remove(@PathVariable Long[] numNicheIds)
    {
        return toAjax(bdNicheGuestSrcsService.deleteBdNicheGuestSrcsByNumNicheIds(numNicheIds));
    }
}
