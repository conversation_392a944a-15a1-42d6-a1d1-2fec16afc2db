package com.nnb.customer.controller;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.customer.domain.ComDictRegion;
import com.nnb.customer.domain.vo.region.ComDictRegionVo;
import com.nnb.customer.service.ComDictRegionService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/ComDictRegion")
@Api(tags = "ComDictRegionController", description = "获取区域标签")
@Slf4j
public class ComDictRegionController {

    @Autowired
    private ComDictRegionService comDictRegionService;

    @GetMapping("/getComDictRegion")
    public AjaxResult getComDictRegion() {
        try {
            List<ComDictRegionVo> list = new ArrayList<>();
            List<ComDictRegion> comDictRegion = comDictRegionService.getComDictRegion();
            if(CollectionUtils.isNotEmpty(comDictRegion)){
                comDictRegion.forEach(val ->{
                    ComDictRegionVo comDictRegionVo = new ComDictRegionVo();
                    BeanUtils.copyProperties(val, comDictRegionVo);
                    list.add(comDictRegionVo);
                });
            }
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("获取区域异常，异常信息为：", e);
            return AjaxResult.error();
        }
    }
}
