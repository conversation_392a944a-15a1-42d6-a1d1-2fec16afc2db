package com.nnb.customer.service;

import com.nnb.customer.domain.nichepool.OppPoolCategories;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-08-14
 * @Version: 1.0
 */
public interface IOppPoolCategoriesService {

    /**
     * 查询商机池大类树结构
     *
     * @param oppPoolCategories 商机池大类
     * @return 商机池大类集合
     */
    List<OppPoolCategories> treeList(OppPoolCategories oppPoolCategories);

    /**
     * 查询商机池大类
     *
     * @param id 商机池大类主键
     * @return 商机池大类
     */
    OppPoolCategories selectOppPoolCategoriesById(Long id);

    /**
     * 查询商机池大类列表
     *
     * @param oppPoolCategories 商机池大类
     * @return 商机池大类集合
     */
    List<OppPoolCategories> selectOppPoolCategoriesList(OppPoolCategories oppPoolCategories);

    /**
     * 新增商机池大类
     *
     * @param oppPoolCategories 商机池大类
     * @return 结果
     */
    int insertOppPoolCategories(OppPoolCategories oppPoolCategories);

    /**
     * 修改商机池大类
     *
     * @param oppPoolCategories 商机池大类
     * @return 结果
     */
    int updateOppPoolCategories(OppPoolCategories oppPoolCategories);

    /**
     * 批量删除商机池大类
     *
     * @param ids 需要删除的商机池大类主键集合
     * @return 结果
     */
    int deleteOppPoolCategoriesByIds(List<Long> ids);

    /**
     * 删除商机池大类信息
     *
     * @param id 商机池大类主键
     * @return 结果
     */
    int deleteOppPoolCategoriesById(Long id);

}
