package com.nnb.customer.domain.nichepool;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商机池检索第一层")
public class NichePoolFirstParam {

    @ApiModelProperty("条件类型一 1：所有，2：任意")
    private Integer filterFirst;

    private List<NichePoolSecondParam> conditionOne;

    private Integer pageNo;

    private Integer pageSize;







}
