package com.nnb.customer.domain.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-03-11
 * @Version: 1.0
 */
@Data
public class ESBdClueDetail implements Serializable {

    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("是否接通 1.接通  2.未接通")
    private Integer numAiCallStatus;

    @ApiModelProperty("ai外呼等级 :1.A/2.B/3.C/4.D/5.E/6.F/7.G")
    private String vcAiWish;

    @ApiModelProperty("ai外呼任务id,configurations配置表type=9")
    private Long aiConfigurationsId;

    @ApiModelProperty("最新ai外呼标签id")
    private Long numAiTagId;

    @ApiModelProperty("人工外呼意向度 0.未标记,1.无意向 , 2.低意向 ,3.高意向")
    private Integer numManAiWish;

    @ApiModelProperty("意向度质检 0.未质检,1.质检符合 , 2.质检不符合")
    private Long numQualityTesting;

    @ApiModelProperty("空号检测结果 0：空号 1：实号 4：沉默号 5：风险号 9：未检测， 12号码错误")
    private Integer numCheckPhoneStatus;

    @ApiModelProperty("空号检测时间")
    private String datCheckPhoneTime;

    @ApiModelProperty("风险号检测结果 高风险 1；低风险 2；未检测 3")
    private Integer numForbidCheckResult;

    @ApiModelProperty("拨打状态 1.待拨打2.未接通 .3联系中")
    private Integer numDialStatus;

    @ApiModelProperty("投放线索所在地")
    private Long numSeat;

    @ApiModelProperty("投放线索来源")
    private Long numAdvertising;

    @ApiModelProperty("咨询业务id")
    private Long numConsultId;

    @ApiModelProperty("输单原因")
    private String vcWhyNoDeal;

    @ApiModelProperty("预计成交时间")
    private String datDealTime;

    @ApiModelProperty("预计成交价格 + 备注")
    private String vcDealPrice;

    @ApiModelProperty("销售阶段 1.初步接洽2.需求确定3.方案/报价4.谈判审核5.赢单6.输单")
    private Integer numSaleRank;

    @ApiModelProperty("商机id configurations.op_type =7")
    private Long numConfigSjId;

    @ApiModelProperty("下发时间 ,(也可用作到各个节点的时间)")
    private String datXfTime;

    @ApiModelProperty("线索管理标签  1自建线索 2分配线索 3领取线索")
    private Integer numTab;

    @ApiModelProperty("商机池停留时间")
    private String datStayDate;

    @ApiModelProperty("进入商机池时间")
    private String datIntoOppDate;

    @ApiModelProperty("下沉原因")
    private Long numSinkId;

    @ApiModelProperty("掉保时间")
    private String datPublicIn;

    @ApiModelProperty("号码状态1=>关机, 2=>拒接, 3=>空号, 4=>停机 5=>正常")
    private Integer numPhoneStatus;

    @ApiModelProperty("呼叫中心重置：0未重置， 1重置")
    private Integer callCenterReset;

    @ApiModelProperty("ai外呼状态：1 队列种；  2 空闲中；")
    private Integer numAiJobStatus;

    @ApiModelProperty("是否疑似代帐 0.否  1.是")
    private Integer isSuspectedAccount;

    @ApiModelProperty("1有效、2无效、3未接通")
    private Integer firstConsult;

    @ApiModelProperty("1没咨询过、 2空号及停机、 3外地业务、 4电商微信号不存在或不允许添加、 5业务做不了")
    private Integer firstConsultInvalid;

    @ApiModelProperty("新商机池id")
    private Long nicheFlowConfId;

    @ApiModelProperty("螳螂线下表导入，存储json")
    private String tanglangImportJosn;
}
