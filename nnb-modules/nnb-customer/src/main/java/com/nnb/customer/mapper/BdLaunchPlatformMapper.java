package com.nnb.customer.mapper;

import java.util.List;

import com.nnb.customer.domain.BdLaunchPlatformEntity;

/**
 * 投放平台管理配置，Mapper接口。
 *
 * <AUTHOR>
 * @date 2022-06-28 11:21:53
 */
public interface BdLaunchPlatformMapper {

    /**
     * 查询投放平台管理配置列表。
     *
     * @param bdLaunchPlatform 投放平台管理配置。
     * @return 投放平台管理配置集合。
     * <AUTHOR>
     * @since 2022-06-28 11:22:06
     */
    public List<BdLaunchPlatformEntity> selectBdLaunchPlatformList(BdLaunchPlatformEntity bdLaunchPlatform);

}
