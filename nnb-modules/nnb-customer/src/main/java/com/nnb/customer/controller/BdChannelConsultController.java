package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdChannelConsult;
import com.nnb.customer.service.IBdChannelConsultService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 媒体账户咨询业务关系Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdChannelConsult")
@Api(tags = "BdChannelConsultController", description = "媒体账户咨询业务关系")
public class BdChannelConsultController extends BaseController
{
    @Autowired
    private IBdChannelConsultService bdChannelConsultService;

    /**
     * 查询媒体账户咨询业务关系列表
     */
    @ApiOperation(value = "查询媒体账户咨询业务关系列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdChannelConsult.class)})
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:list")
    @GetMapping("/list")
    public TableDataInfo list(BdChannelConsult bdChannelConsult)
    {
        startPage();
        List<BdChannelConsult> list = bdChannelConsultService.selectBdChannelConsultList(bdChannelConsult);
        return getDataTable(list);
    }

    /**
     * 导出媒体账户咨询业务关系列表
     */
    @ApiOperation(value = "导出媒体账户咨询业务关系列表")
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:export")
    //@Log(title = "媒体账户咨询业务关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdChannelConsult bdChannelConsult) throws IOException
    {
        List<BdChannelConsult> list = bdChannelConsultService.selectBdChannelConsultList(bdChannelConsult);
        ExcelUtil<BdChannelConsult> util = new ExcelUtil<BdChannelConsult>(BdChannelConsult.class);
        util.exportExcel(response, list, "媒体账户咨询业务关系数据");
    }

    /**
     * 获取媒体账户咨询业务关系详细信息
     */
    @ApiOperation(value = "获取媒体账户咨询业务关系详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdChannelConsult.class)})
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:query")
    @GetMapping(value = "/{channelId}")
    public AjaxResult getInfo(@ApiParam(name="channelId",value="媒体账户咨询业务关系id") @PathVariable("channelId") Long channelId)
    {
        return AjaxResult.success(bdChannelConsultService.selectBdChannelConsultByChannelId(channelId));
    }

    /**
     * 新增媒体账户咨询业务关系
     */
    @ApiOperation(value = "新增媒体账户咨询业务关系")
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:add")
    //@Log(title = "媒体账户咨询业务关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdChannelConsult bdChannelConsult)
    {
        return toAjax(bdChannelConsultService.insertBdChannelConsult(bdChannelConsult));
    }

    /**
     * 修改媒体账户咨询业务关系
     */
    @ApiOperation(value = "修改媒体账户咨询业务关系")
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:edit")
    //@Log(title = "媒体账户咨询业务关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdChannelConsult bdChannelConsult)
    {
        return toAjax(bdChannelConsultService.updateBdChannelConsult(bdChannelConsult));
    }

    /**
     * 删除媒体账户咨询业务关系
     */
    @ApiOperation(value = "删除媒体账户咨询业务关系")
    @PreAuthorize(hasPermi = "customer:BdChannelConsult:remove")
    //@Log(title = "媒体账户咨询业务关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{channelIds}")
    public AjaxResult remove(@PathVariable Long[] channelIds)
    {
        return toAjax(bdChannelConsultService.deleteBdChannelConsultByChannelIds(channelIds));
    }
}
