package com.nnb.customer.domain.bdCall;

import lombok.Data;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 卡卡配置缓存对象
 * 预处理dominant字段为Set，提高查找效率
 *
 * @Author: Chen-xy
 * @Description: 优化的卡卡配置缓存对象
 * @Version: 1.0
 */
@Data
public class BdCallKaConfigCache implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 原始的dominant字符串
     */
    private String dominant;

    /**
     * 预处理后的dominant集合，用于快速查找
     */
    private Set<String> dominantSet;

    private String accessKey;

    /**
     * 无参构造函数，用于序列化
     */
    public BdCallKaConfigCache() {
        this.dominantSet = new HashSet<>();
    }

    /**
     * 构造函数，从原始配置对象转换
     */
    public BdCallKaConfigCache(BdCallKaConfig config) {
        if (config == null) {
            this.dominantSet = new HashSet<>();
            return;
        }

        this.id = config.getId();
        this.dominant = config.getDominant();
        this.accessKey = config.getAccessKey();

        // 预处理dominant字符串为Set
        if (config.getDominant() != null && !config.getDominant().trim().isEmpty()) {
            this.dominantSet = new HashSet<>(Arrays.asList(config.getDominant().split(",")));
        } else {
            this.dominantSet = new HashSet<>();
        }
    }

    /**
     * 检查是否包含指定的dominant值
     */
    public boolean containsDominant(String dominantValue) {
        return dominantSet.contains(dominantValue);
    }
}
