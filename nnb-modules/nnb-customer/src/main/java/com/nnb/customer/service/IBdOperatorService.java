package com.nnb.customer.service;


import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.customer.domain.BdOperatorEntity;
import com.nnb.customer.domain.vo.BdOperatorForListDTO;
import com.nnb.customer.domain.vo.BdOperatorForListVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 运营方，业务层接口。
 *
 * <AUTHOR>
 * @date 2022-06-28 13:47:41
 */
public interface IBdOperatorService {

    /**
     * 获取运营方列表。
     *
     * @param query 查询条件。
     * @return 返回运营方列表。
     * <AUTHOR>
     * @since 2022-06-28 16:49:18
     */
    public List<BdOperatorForListVO> getList(BdOperatorForListDTO query);

    /**
     * 新增运营方。
     *
     * @param operatorEntity 待新增实体。
     * <AUTHOR>
     * @since 2022-06-28 16:56:30
     */
    public void saveOperator(BdOperatorEntity operatorEntity);

    /**
     * 编辑运营方。
     *
     * @param operatorEntity 待编辑实体。
     * <AUTHOR>
     * @since 2022-06-28 16:56:30
     */
    public void submitOperator(BdOperatorEntity operatorEntity);

    /**
     * 批量删除运营方。
     *
     * @param ids 待删除运营方标识。
     * <AUTHOR>
     * @since 2022-06-28 17:15:11
     */
    public void deleteOperatorBatch(List<Integer> ids);

}
