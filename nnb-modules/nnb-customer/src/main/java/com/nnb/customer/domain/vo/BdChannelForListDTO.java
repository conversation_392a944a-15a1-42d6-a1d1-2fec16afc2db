package com.nnb.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 媒体账号，分页集合，DTO。
 *
 * <AUTHOR>
 * @since 2022/6/28 14:48
 */
@Data
public class BdChannelForListDTO {

    /**
     * 媒体账户名称。
     */
    @ApiModelProperty("媒体账户名称。")
    private String channelName;

    /**
     * 媒体账户状态：1-启用；2-停用。
     */
    @ApiModelProperty("媒体账户状态：1-启用；2-停用。")
    private Integer status;

    /**
     * 投放平台标识。
     */
    @ApiModelProperty("投放平台标识。")
    private Integer platformId;

    /**
     * 运营方标识。
     */
    @ApiModelProperty("运营方标识。")
    private Integer operatorId;

}
