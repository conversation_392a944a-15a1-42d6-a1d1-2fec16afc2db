package com.nnb.customer.domain.fuioupay.request;

import com.nnb.customer.domain.fuioupay.GoodsDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 统一下单请求体
 * @Date: 2022-08-31
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="PreCreateReq",description="统一下单请求体")
public class PreCreateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Builder.Default
    @ApiModelProperty("版本号")
    private String version = "1.0";

    @ApiModelProperty("富友分配的商户号例：0002900F0313432")
    private String mchnt_cd;

    @ApiModelProperty("随机字符串")
    private String random_str;

    @ApiModelProperty("订单类型：\n" +
            "WECHAT\n" +
            "WXXS(微信线上)\n" +
            "WXBX(微信保险类)\n" +
            "ALBX(支付宝保险类)\n" +
            "UNIONPAY(银联二维码)")
    private String order_type;

    @ApiModelProperty("订单总金额，以分为单位")
    private String order_amt;

    @ApiModelProperty("商户系统内部的订单号")
    private String mchnt_order_no;

    //    @NotBlank(message = "交易起始时间不能为空")
    @ApiModelProperty("交易起始时间,订单生成时间格式：yyyyMMddHHmmss")
    private String txn_begin_ts;

    @ApiModelProperty("商品描述")
    private String goods_des;

    @ApiModelProperty("商品详情")
    private List<GoodsDetail> goods_detail;

    @ApiModelProperty("商品标记")
    private String goods_tag;

    //    @NotBlank(message = "终端号不能为空")
    @ApiModelProperty("终端号，随机8字节数字字母组合")
    private String term_id;

    //    @NotBlank(message = "终端IP不能为空")
    @ApiModelProperty("终端IP")
    private String term_ip;

    @ApiModelProperty("附加数据")
    private String addn_inf;

    @Builder.Default
    @ApiModelProperty("货币类型，默认人民币：CNY")
    private String curr_type = "CNY";

    //    @NotBlank(message = "通知URL不能为空")
    @ApiModelProperty("通知URL，接收富友异步通知回调地址\n" +
            "通知url必须为直接可访问的url，不能携带参数主扫")
    private String notify_url;

    @ApiModelProperty("子商户公众号id")
    private String reserved_sub_appid;

    @ApiModelProperty("限制支付，no_credit:不能使用信用卡，credit_group:不能使用花呗和信用卡不参与签名")
    private String reserved_limit_pay;

    @Builder.Default
    @ApiModelProperty("交易关闭时间,默认填0（2小时）")
    private Integer reserved_expire_minute = 0;

    @ApiModelProperty("富友终端号（如果不是用的富友的POS终端，此字段千万不要填，不然会影响清算）")
    private String reserved_fy_term_id;

    @ApiModelProperty("用户身份证号码(实名保险类交易必填)")
    private String reserved_user_creid;

    @ApiModelProperty("用户姓名")
    private String reserved_user_truename;

    @ApiModelProperty("用户手机号(仅支付宝认证)")
    private String reserved_user_mobile;

    @ApiModelProperty("保险公司订单号，认证机构单号(使用英文逗号分隔)")
    private String reserved_bxno_inf;

    @ApiModelProperty("富友终端类型\n" +
            "0：其他\n" +
            "1：富友终端\n" +
            "2：POS机\n" +
            "3：台卡\n" +
            "4：PC软件")
    private String reserved_fy_term_type;

    @ApiModelProperty("终端序列号")
    private String reserved_fy_term_sn;

    @ApiModelProperty("设备信息，托传给微信。用于单品券核销\n" +
            "示例参照：" +
            "//IOS 移动应用\n" +
            "{\"type\":\"IOS\",\"app_name\":\"王者荣耀\",\"app_url\":\"com.tencent.wzryIOS\"}//bundle_id\n" +
            "//安卓移动应用\n" +
            "{\"type\":\"Android\",\"app_name\":\"王者荣耀\",\"app_url\":\"com.tencent.tmgp.sgame\"}//package_name\n" +
            "//WAP 网站应用\n" +
            "{\"type\":\"Wap\",\"app_name\":\"京东官网\",\"app_url\":\"https://m.jd.com\"}//wetside")
    private String reserved_device_info;

    @ApiModelProperty("花呗分期信息，JSON串\n" +
            "示例参照：{\n" +
            "    \"reserved_ali_extend_params\":{\n" +
            "        \"dynamic_token_out_biz_no\":\"66666\",\n" +
            "        \"hb_fq_num\":\"3\",\n" +
            "        \"industry_reflux_info\":{\n" +
            "            \"scene_code\":\"metro_tradeorder\",\n" +
            "            \"channel\":\"xxxx\",\n" +
            "            \"scene_data\":{\n" +
            "                \"asset_name\":\"ALIPAY\"\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "}")
    private String reserved_ali_extend_params;

    @ApiModelProperty("花呗分期期数：仅支持3、6、12")
    private String hb_fq_num;

    @ApiModelProperty("花呗分期商家手续费比例，目前仅支持用户出资，如需使用，请填写0！目前该字段未生效，我司会默认当0处理")
    private String hb_fq_seller_percent;

    @ApiModelProperty("行业数据回流信息\n" +
            "示例参照：{\n" +
            "    \\\"scene_code\\\":\\\"metro_tradeorder\\\",\n" +
            "    \\\"channel\\\":\\\"xxxx\\\",\n" +
            "    \\\"scene_data\\\":{\n" +
            "        \\\"asset_name\\\":\\\"ALIPAY\\\"\n" +
            "    }\n" +
            "}")
    private String industry_reflux_info;

    @NotBlank
    @ApiModelProperty("签名，拼接\n" +
                            "mchnt_cd+\"|\"\n" +
                            "+ order_type +\"|\"\n" +
                            "+ order_amt +\"|\"\n" +
                            "+ mchnt_order_no+\"|\"\n" +
                            "+ txn_begin_ts+\"|\"\n" +
                            "+ goods_des +\"|\"\n" +
                            "+ term_id +\"|\"\n" +
                            "+ term_ip +\"|\"\n" +
                            "+ notify_url +\"|\"\n" +
                            "+ random_str +\"|\"\n" +
                            "+ version + \"|\"\n" +
                            "+ mchnt_key\n" +
                            "\n" +
                            "并做md5摘要\n" +
                            "其中 mchnt_key 为商户密钥，系统分配")
    private String sign;

    @ApiModelProperty("终端信息说明字段段")
    private String reserved_terminal_info;

    @ApiModelProperty("用户标识")
    private String openid;

    @ApiModelProperty("JSAPI--公众号线下支付\n" +
            "GZXS--公众号线上\n" +
            "APP--app支付(暂不可用)\n" +
            "FWC--支付宝服务窗\n" +
            "LETPAY-小程序\n" +
            "LPXS--小程序线上\n" +
            "WXBXJS(微信公众号保险类)\n" +
            "WXBXLET(微信小程序保险类)\n" +
            "WXBXAPP(微信APP保险类)\n" +
            "ALBXJS(支付宝服务窗保险类)\n" +
            "APPLEPAY(相机扫码)\n" +
            "UNIONPAY(云闪付扫码)")
    private String trade_type;

}
