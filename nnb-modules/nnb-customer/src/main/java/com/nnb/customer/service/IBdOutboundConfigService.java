package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdOutboundConfig;

/**
 * 外呼记录配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
public interface IBdOutboundConfigService 
{
    /**
     * 查询外呼记录配置
     * 
     * @param id 外呼记录配置主键
     * @return 外呼记录配置
     */
    public BdOutboundConfig selectBdOutboundConfigById(Long id);

    /**
     * 查询外呼记录配置列表
     * 
     * @param bdOutboundConfig 外呼记录配置
     * @return 外呼记录配置集合
     */
    public List<BdOutboundConfig> selectBdOutboundConfigList(BdOutboundConfig bdOutboundConfig);

    /**
     * 新增外呼记录配置
     * 
     * @param bdOutboundConfig 外呼记录配置
     * @return 结果
     */
    public int insertBdOutboundConfig(BdOutboundConfig bdOutboundConfig);

    /**
     * 修改外呼记录配置
     * 
     * @param bdOutboundConfig 外呼记录配置
     * @return 结果
     */
    public int updateBdOutboundConfig(BdOutboundConfig bdOutboundConfig);

    /**
     * 批量删除外呼记录配置
     * 
     * @param ids 需要删除的外呼记录配置主键集合
     * @return 结果
     */
    public int deleteBdOutboundConfigByIds(Long[] ids);

    /**
     * 删除外呼记录配置信息
     * 
     * @param id 外呼记录配置主键
     * @return 结果
     */
    public int deleteBdOutboundConfigById(Long id);
}
