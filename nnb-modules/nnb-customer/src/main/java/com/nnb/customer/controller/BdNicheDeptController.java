package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdNicheDept;
import com.nnb.customer.service.IBdNicheDeptService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 商机部门关系Controller
 * 
 * <AUTHOR>
 * @date 2022-02-25
 */
@RestController
@RequestMapping("/BdNicheDept")
@Api(tags = "BdNicheDeptController", description = "商机部门关系")
public class BdNicheDeptController extends BaseController
{
    @Autowired
    private IBdNicheDeptService bdNicheDeptService;

    /**
     * 查询商机部门关系列表
     */
    @ApiOperation(value = "查询商机部门关系列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdNicheDept.class)})
    @PreAuthorize(hasPermi = "customer:BdNicheDept:list")
    @GetMapping("/list")
    public TableDataInfo list(BdNicheDept bdNicheDept)
    {
        startPage();
        List<BdNicheDept> list = bdNicheDeptService.selectBdNicheDeptList(bdNicheDept);
        return getDataTable(list);
    }

    /**
     * 导出商机部门关系列表
     */
    @ApiOperation(value = "导出商机部门关系列表")
    @PreAuthorize(hasPermi = "customer:BdNicheDept:export")
    //@Log(title = "商机部门关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdNicheDept bdNicheDept) throws IOException
    {
        List<BdNicheDept> list = bdNicheDeptService.selectBdNicheDeptList(bdNicheDept);
        ExcelUtil<BdNicheDept> util = new ExcelUtil<BdNicheDept>(BdNicheDept.class);
        util.exportExcel(response, list, "商机部门关系数据");
    }

    /**
     * 获取商机部门关系详细信息
     */
    @ApiOperation(value = "获取商机部门关系详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdNicheDept.class)})
    @PreAuthorize(hasPermi = "customer:BdNicheDept:query")
    @GetMapping(value = "/{numNicheId}")
    public AjaxResult getInfo(@ApiParam(name="numNicheId",value="商机部门关系id") @PathVariable("numNicheId") Long numNicheId)
    {
        return AjaxResult.success(bdNicheDeptService.selectBdNicheDeptByNumNicheId(numNicheId));
    }

    /**
     * 新增商机部门关系
     */
    @ApiOperation(value = "新增商机部门关系")
    @PreAuthorize(hasPermi = "customer:BdNicheDept:add")
    //@Log(title = "商机部门关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdNicheDept bdNicheDept)
    {
        return toAjax(bdNicheDeptService.insertBdNicheDept(bdNicheDept));
    }

    /**
     * 修改商机部门关系
     */
    @ApiOperation(value = "修改商机部门关系")
    @PreAuthorize(hasPermi = "customer:BdNicheDept:edit")
    //@Log(title = "商机部门关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdNicheDept bdNicheDept)
    {
        return toAjax(bdNicheDeptService.updateBdNicheDept(bdNicheDept));
    }

    /**
     * 删除商机部门关系
     */
    @ApiOperation(value = "删除商机部门关系")
    @PreAuthorize(hasPermi = "customer:BdNicheDept:remove")
    //@Log(title = "商机部门关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{numNicheIds}")
    public AjaxResult remove(@PathVariable Long[] numNicheIds)
    {
        return toAjax(bdNicheDeptService.deleteBdNicheDeptByNumNicheIds(numNicheIds));
    }
}
