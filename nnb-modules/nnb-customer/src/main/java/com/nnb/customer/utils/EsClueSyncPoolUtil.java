package com.nnb.customer.utils;

import java.util.concurrent.*;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-03-08
 * @Version: 1.0
 */
public class EsClueSyncPoolUtil {

    // 线程缓冲队列
    private static final BlockingQueue<Runnable> blockingQueue = new ArrayBlockingQueue<Runnable>(100);
    // 核心线程数，会一直存活，即使没有任务，线程池也会维护线程的最少数量
    private static final int SIZE_CORE_POOL = 3;
    // 线程池维护线程的最大数量
    private static final int SIZE_MAX_POOL = 5;
    // 线程池维护线程所允许的空闲时间
    private static final long ALIVE_TIME = 5000;
    // 拒绝策略：当有任务添加到线程池被拒绝时，线程池会将被拒绝的任务添加到"线程池正在运行的线程"中去运行。
    private static final RejectedExecutionHandler rejectHandler = new ThreadPoolExecutor.CallerRunsPolicy();

    private static final ThreadPoolExecutor pool;

    static {
        pool = new ThreadPoolExecutor(SIZE_CORE_POOL, SIZE_MAX_POOL, ALIVE_TIME, TimeUnit.MILLISECONDS, blockingQueue, rejectHandler);
        //启动所有核心线程数使其等待
        pool.prestartAllCoreThreads();
    }

    public static ThreadPoolExecutor getPool() {
        return pool;
    }
}
