package com.nnb.customer.service;

import java.util.List;
import com.nnb.customer.domain.BdGuestSrcDeptVice;

/**
 * 线索来源可见部门关联Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-16
 */
public interface IBdGuestSrcDeptViceService 
{
    /**
     * 查询线索来源可见部门关联
     * 
     * @param mainId 线索来源可见部门关联主键
     * @return 线索来源可见部门关联
     */
    public BdGuestSrcDeptVice selectBdGuestSrcDeptViceByMainId(Long mainId);

    /**
     * 查询线索来源可见部门关联列表
     * 
     * @param bdGuestSrcDeptVice 线索来源可见部门关联
     * @return 线索来源可见部门关联集合
     */
    public List<BdGuestSrcDeptVice> selectBdGuestSrcDeptViceList(BdGuestSrcDeptVice bdGuestSrcDeptVice);

    /**
     * 新增线索来源可见部门关联
     * 
     * @param bdGuestSrcDeptVice 线索来源可见部门关联
     * @return 结果
     */
    public int insertBdGuestSrcDeptVice(BdGuestSrcDeptVice bdGuestSrcDeptVice);

    /**
     * 修改线索来源可见部门关联
     * 
     * @param bdGuestSrcDeptVice 线索来源可见部门关联
     * @return 结果
     */
    public int updateBdGuestSrcDeptVice(BdGuestSrcDeptVice bdGuestSrcDeptVice);

    /**
     * 批量删除线索来源可见部门关联
     * 
     * @param mainIds 需要删除的线索来源可见部门关联主键集合
     * @return 结果
     */
    public int deleteBdGuestSrcDeptViceByMainIds(Long[] mainIds);

    /**
     * 删除线索来源可见部门关联信息
     * 
     * @param mainId 线索来源可见部门关联主键
     * @return 结果
     */
    public int deleteBdGuestSrcDeptViceByMainId(Long mainId);
}
