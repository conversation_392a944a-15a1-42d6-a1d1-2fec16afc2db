package com.nnb.customer.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 线索联系人对象 bd_clue_contacts
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@ApiModel(value="BdClueContacts",description="线索联系人对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BdClueContacts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("id")
    private Long id;

    /** 线索id */
    @Excel(name = "线索id")
    @ApiModelProperty("线索id")
    private Long clueId;

    /** 线索人 */
    @Excel(name = "线索人")
    @ApiModelProperty("线索人")
    private String vcName;

    /** 联系方式 */
    @Excel(name = "联系方式")
    @ApiModelProperty("联系方式")
    private String vcPhone;

    /** 联系方式归属地 */
    @Excel(name = "联系方式归属地")
    @ApiModelProperty("联系方式归属地")
    private Long locationId;

    /** 邮箱 */
    @Excel(name = "邮箱")
    @ApiModelProperty("邮箱")
    private String vcEmail;

    /** QQ号 */
    @Excel(name = "QQ号")
    @ApiModelProperty("QQ号")
    private String vcQq;

    /** 微信号 */
    @Excel(name = "微信号")
    @ApiModelProperty("微信号")
    private String vcWeixin;

    /** 抖音号 */
    @Excel(name = "抖音号")
    @ApiModelProperty("抖音号")
    private String numDy;

    /** 性别 */
    @Excel(name = "性别")
    @ApiModelProperty("性别")
    private Integer numSex;

    /** 联系人角色 */
    @Excel(name = "联系人角色")
    @ApiModelProperty("联系人角色")
    private String vcContactRole;

    /** 排序 1 置顶 */
    @Excel(name = "排序 1 置顶")
    @ApiModelProperty("排序 1 置顶")
    private Long numSort;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreateUserid;

    /** 最后修改人 */
    @Excel(name = "最后修改人")
    @ApiModelProperty("最后修改人")
    private Long numLastUpdUserid;


    @ApiModelProperty("线索标签 1.自建线索 2.分配线索 3.领取线索")
    private Long numTab;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreateTime;

    /** 最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("最后修改时间")
    private Date datLastUpd;

    private Long numStatus;

    private String uuId;

    public String getUuId() {
        return uuId;
    }

    public void setUuId(String uuId) {
        this.uuId = uuId;
    }

    public Long getNumStatus() {
        return numStatus;
    }

    public void setNumStatus(Long numStatus) {
        this.numStatus = numStatus;
    }

    public Long getNumTab() {
        return numTab;
    }

    public void setNumTab(Long numTab) {
        this.numTab = numTab;
    }
    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setClueId(Long clueId)
    {
        this.clueId = clueId;
    }

    public Long getClueId()
    {
        return clueId;
    }
    public void setVcName(String vcName)
    {
        this.vcName = vcName;
    }

    public String getVcName()
    {
        return vcName;
    }
    public void setVcPhone(String vcPhone)
    {
        this.vcPhone = vcPhone;
    }

    public String getVcPhone()
    {
        return vcPhone;
    }
    public void setLocationId(Long locationId)
    {
        this.locationId = locationId;
    }

    public Long getLocationId()
    {
        return locationId;
    }
    public void setVcEmail(String vcEmail)
    {
        this.vcEmail = vcEmail;
    }

    public String getVcEmail()
    {
        return vcEmail;
    }
    public void setVcQq(String vcQq)
    {
        this.vcQq = vcQq;
    }

    public String getVcQq()
    {
        return vcQq;
    }
    public void setVcWeixin(String vcWeixin)
    {
        this.vcWeixin = vcWeixin;
    }

    public String getVcWeixin()
    {
        return vcWeixin;
    }
    public void setNumDy(String numDy)
    {
        this.numDy = numDy;
    }

    public String getNumDy()
    {
        return numDy;
    }
    public void setNumSex(Integer numSex)
    {
        this.numSex = numSex;
    }

    public Integer getNumSex()
    {
        return numSex;
    }
    public void setVcContactRole(String vcContactRole)
    {
        this.vcContactRole = vcContactRole;
    }

    public String getVcContactRole()
    {
        return vcContactRole;
    }
    public void setNumSort(Long numSort)
    {
        this.numSort = numSort;
    }

    public Long getNumSort()
    {
        return numSort;
    }
    public void setNumCreateUserid(Long numCreateUserid)
    {
        this.numCreateUserid = numCreateUserid;
    }

    public Long getNumCreateUserid()
    {
        return numCreateUserid;
    }
    public void setNumLastUpdUserid(Long numLastUpdUserid)
    {
        this.numLastUpdUserid = numLastUpdUserid;
    }

    public Long getNumLastUpdUserid()
    {
        return numLastUpdUserid;
    }
    public void setDatCreateTime(Date datCreateTime)
    {
        this.datCreateTime = datCreateTime;
    }

    public Date getDatCreateTime()
    {
        return datCreateTime;
    }
    public void setDatLastUpd(Date datLastUpd)
    {
        this.datLastUpd = datLastUpd;
    }

    public Date getDatLastUpd()
    {
        return datLastUpd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clueId", getClueId())
            .append("vcName", getVcName())
            .append("vcPhone", getVcPhone())
            .append("locationId", getLocationId())
            .append("vcEmail", getVcEmail())
            .append("vcQq", getVcQq())
            .append("vcWeixin", getVcWeixin())
            .append("numDy", getNumDy())
            .append("numSex", getNumSex())
            .append("vcContactRole", getVcContactRole())
            .append("numSort", getNumSort())
            .append("numCreateUserid", getNumCreateUserid())
            .append("numLastUpdUserid", getNumLastUpdUserid())
            .append("datCreateTime", getDatCreateTime())
            .append("datLastUpd", getDatLastUpd())
                .append("numTab",getNumTab())
                .append("numStatus",getNumStatus())
            .toString();
    }
}
