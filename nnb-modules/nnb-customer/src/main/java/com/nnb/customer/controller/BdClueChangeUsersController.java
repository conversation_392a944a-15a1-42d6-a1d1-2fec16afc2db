package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.customer.model.BdClueChangeUsersDto;
import com.nnb.customer.model.BdClueChangeUsersVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdClueChangeUsers;
import com.nnb.customer.service.IBdClueChangeUsersService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 投放流转配置Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdClueChangeUsers")
@Api(tags = "BdClueChangeUsersController", description = "投放流转配置")
public class BdClueChangeUsersController extends BaseController
{
    @Autowired
    private IBdClueChangeUsersService bdClueChangeUsersService;

    /**
     * 查询投放流转配置列表
     */
    @ApiOperation(value = "查询投放流转配置列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueChangeUsersVo.class)})
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:list")
    @GetMapping("/list")
    public TableDataInfo list(BdClueChangeUsers bdClueChangeUsers)
    {
        startPage();
        List<BdClueChangeUsersVo> list = bdClueChangeUsersService.selectBdClueChangeUsersVoList(bdClueChangeUsers);
        return getDataTable(list);
    }

    /**
     * 导出投放流转配置列表
     */
    @ApiOperation(value = " 导出投放流转配置列表")
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:export")
    //@Log(title = "投放流转配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdClueChangeUsers bdClueChangeUsers) throws IOException
    {
        List<BdClueChangeUsers> list = bdClueChangeUsersService.selectBdClueChangeUsersList(bdClueChangeUsers);
        ExcelUtil<BdClueChangeUsers> util = new ExcelUtil<BdClueChangeUsers>(BdClueChangeUsers.class);
        util.exportExcel(response, list, "投放流转配置数据");
    }

    /**
     * 获取投放流转配置详细信息
     */
    @ApiOperation(value = "获取投放流转配置详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdClueChangeUsersVo.class)})
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="投放流转配置id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(bdClueChangeUsersService.selectBdClueChangeUsersVoById(id));
    }

    /**
     * 新增投放流转配置
     */
    @ApiOperation(value = "新增投放流转配置")
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:add")
    //@Log(title = "投放流转配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody BdClueChangeUsersDto dto)
    {
        return toAjax(bdClueChangeUsersService.insertBdClueChangeUsersDto(dto));
    }

    /**
     * 修改投放流转配置
     */
    @ApiOperation(value = "修改投放流转配置")
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:edit")
    //@Log(title = "投放流转配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdClueChangeUsersDto dto)
    {
        return toAjax(bdClueChangeUsersService.updateBdClueChangeUsersDto(dto));
    }

    /**
     * 删除投放流转配置
     */
    @ApiOperation(value = "删除投放流转配置")
    //@PreAuthorize(hasPermi = "customer:BdClueChangeUsers:remove")
    //@Log(title = "投放流转配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(bdClueChangeUsersService.deleteBdClueChangeUsersByIds(ids));
    }
}
