package com.nnb.customer.domain.nichepool;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商机池检索第二层")
public class NichePoolSecondParam {
    @ApiModelProperty("条件类型二 1：所有，2：任意")
    private Integer filterSecond;

    @ApiModelProperty("筛选类型 1：单个，2：条件组")
    private Integer conditionType;

    private List<NichePoolThirdParam> conditionTwo;

}
