package com.nnb.customer.domain.bdCall;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 回调失败实体
 * @Date: 2024-10-21
 * @Version: 1.0
 */

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AiCallbackFailed implements Serializable {

    @JsonProperty("count")
    private Integer count;
    @JsonProperty("data")
    private List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO implements Serializable{
        @JsonProperty("bill")
        private Integer bill;
        @JsonProperty("duration")
        private Integer duration;
        @JsonProperty("callid")
        private String callId;
        @JsonProperty("calldate")
        private String callDate;
        @JsonProperty("number")
        private String number;
        @JsonProperty("numberid")
        private String numberId;
        @JsonProperty("customer_id")
        private String customerId;
        @JsonProperty("status")
        private Integer status;
        @JsonProperty("status_str")
        private String statusStr;
        @JsonProperty("user_id")
        private String userId;
        @JsonProperty("type")
        private Integer type;
        @JsonProperty("number_data")
        private NumberDataDTO numberData;
        @JsonProperty("group")
        private GroupDTO group;
        @JsonProperty("task")
        private TaskDTO task;
        @JsonProperty("user")
        private UserDTO user;
        @JsonProperty("customer_data")
        private CustomerDataDTO customerData;

        @NoArgsConstructor
        @Data
        public static class NumberDataDTO implements Serializable{
            @JsonProperty("number")
            private String number;
            @JsonProperty("province")
            private String province;
            @JsonProperty("city")
            private String city;
            @JsonProperty("operator")
            private String operator;
        }

        @NoArgsConstructor
        @Data
        public static class GroupDTO implements Serializable{
            @JsonProperty("id")
            private Integer id;
            @JsonProperty("name")
            private String name;
        }

        @NoArgsConstructor
        @Data
        public static class TaskDTO implements Serializable{
            @JsonProperty("id")
            private String id;
            @JsonProperty("name")
            private String name;
        }

        @NoArgsConstructor
        @Data
        public static class UserDTO implements Serializable{
            @JsonProperty("id")
            private String id;
            @JsonProperty("name")
            private String name;
        }

        @NoArgsConstructor
        @Data
        public static class CustomerDataDTO implements Serializable{
            @JsonProperty("name")
            private String name;
            @JsonProperty("email")
            private String email;
            @JsonProperty("company")
            private Object company;
            @JsonProperty("extra")
            private String extra;
        }
    }
}

/* {
        *     "count": 1, //通话失败个数
        *     "data": [
        *         {
        *             "bill": 0,
        *             "duration": 0,
        *             "callid": "fefe20f2-5a42-4571-8f0f-61a289c5dbac", //软电话拨打的一个唯一id
        *             "calldate": "2024-01-11 11:27:17", //呼叫时间
        *             "number": "13667679151", //号码
        *             "numberid": "7f2ff491-586f-4bc3-aae7-f74becb11609",
        *             "customer_id": "85f835cd-04a9-417d-9bea-63e411f89ba1",
        *             "status": 2,
        *             "status_str": "运营商拦截",
        *             "user_id": "073d8501-b6a3-4869-a87e-71807bfd4fb8", //用户id
        *             "type": 4, //用户类型  4-主账号 8-子账号
        *             "number_data": {
        *                 "number": "13667679151",
        *                 "province": "重庆",
        *                 "city": "重庆",
        *                 "operator": "移动"
        *             },
        *             "group": {
        *                 "id": 9303, //话术组id
        *                 "name": "测试" //话术昵称
        *             },
        *             "task": {
        *                 "id": "d51b9480-8a73-4f66-8dfa-c0e4a6fc108b", //任务id
        *                 "name": "2024-01-09（11:40）测试0104" //任务昵称
        *             },
        *             "user": {
        *                 "id": "40863af8-db4a-41c6-a269-eb5bc386d97f", //账号id
        *                 "name": "官方系统测试账号", //账号昵称
        *             },
        *             "customer_data": {
        *                 "name": "ai_2410478",
        *                 "email": "<EMAIL>",
        *                 "company": null,
        *                 "extra": "good"
        *             },
        *         }
        *     ]
        * }

 */
