package com.nnb.customer.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.customer.domain.BdAiTasks;
import com.nnb.customer.service.IBdAiTasksService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * ai外呼任务Controller
 * 
 * <AUTHOR>
 * @date 2022-02-18
 */
@RestController
@RequestMapping("/BdAiTasks")
@Api(tags = "BdAiTasksController", description = "ai外呼任务")
public class BdAiTasksController extends BaseController
{
    @Autowired
    private IBdAiTasksService bdAiTasksService;

    /**
     * 查询ai外呼任务列表
     */
    @ApiOperation(value = "查询ai外呼任务列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdAiTasks.class)})
    @PreAuthorize(hasPermi = "customer:BdAiTasks:list")
    @GetMapping("/list")
    public TableDataInfo list(BdAiTasks bdAiTasks)
    {
        startPage();
        List<BdAiTasks> list = bdAiTasksService.selectBdAiTasksList(bdAiTasks);
        return getDataTable(list);
    }

    /**
     * 导出ai外呼任务列表
     */
    @ApiOperation(value = "导出ai外呼任务列表")
    @PreAuthorize(hasPermi = "customer:BdAiTasks:export")
    //@Log(title = "ai外呼任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BdAiTasks bdAiTasks) throws IOException
    {
        List<BdAiTasks> list = bdAiTasksService.selectBdAiTasksList(bdAiTasks);
        ExcelUtil<BdAiTasks> util = new ExcelUtil<BdAiTasks>(BdAiTasks.class);
        util.exportExcel(response, list, "ai外呼任务数据");
    }

    /**
     * 获取ai外呼任务详细信息
     */
    @ApiOperation(value = "获取ai外呼任务详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = BdAiTasks.class)})
    @PreAuthorize(hasPermi = "customer:BdAiTasks:query")
    @GetMapping(value = "/{numId}")
    public AjaxResult getInfo(@ApiParam(name="numId",value="ai外呼任务id") @PathVariable("numId") Long numId)
    {
        return AjaxResult.success(bdAiTasksService.selectBdAiTasksByNumId(numId));
    }

    /**
     * 新增ai外呼任务
     */
    @ApiOperation(value = "新增ai外呼任务")
    @PreAuthorize(hasPermi = "customer:BdAiTasks:add")
    //@Log(title = "ai外呼任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BdAiTasks bdAiTasks)
    {
        return toAjax(bdAiTasksService.insertBdAiTasks(bdAiTasks));
    }

    /**
     * 修改ai外呼任务
     */
    @ApiOperation(value = "修改ai外呼任务")
    @PreAuthorize(hasPermi = "customer:BdAiTasks:edit")
    //@Log(title = "ai外呼任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BdAiTasks bdAiTasks)
    {
        return toAjax(bdAiTasksService.updateBdAiTasks(bdAiTasks));
    }

    /**
     * 删除ai外呼任务
     */
    @ApiOperation(value = "删除ai外呼任务")
    @PreAuthorize(hasPermi = "customer:BdAiTasks:remove")
    //@Log(title = "ai外呼任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{numIds}")
    public AjaxResult remove(@PathVariable Long[] numIds)
    {
        return toAjax(bdAiTasksService.deleteBdAiTasksByNumIds(numIds));
    }
}
