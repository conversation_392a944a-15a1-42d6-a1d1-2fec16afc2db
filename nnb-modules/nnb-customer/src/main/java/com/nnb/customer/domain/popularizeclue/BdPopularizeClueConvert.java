package com.nnb.customer.domain.popularizeclue;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 推广线索-转化对象 bd_popularize_clue_convert
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
@ApiModel(value="BdPopularizeClueConvert",description="推广线索-转化对象")
public class BdPopularizeClueConvert extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;

    /** 线索ID */
    @Excel(name = "线索ID")
    @ApiModelProperty("线索ID")
    private Long clueId;

    /** 来源ID */
    @Excel(name = "来源ID")
    @ApiModelProperty("来源ID")
    private Long adClueSourceId;

    /** 账户ID */
    @Excel(name = "账户ID")
    @ApiModelProperty("账户ID")
    private Long advertiserInfoId;

    /** 联系方式 */
    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String vcPhone;

    @ApiModelProperty("qq")
    private String vcQq;

    @ApiModelProperty("微信")
    private String vcWeixin;

    /** 删除标记；0：未删除，1：删除 */
    @ApiModelProperty("删除标记；0：未删除，1：删除")
    private Integer delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setClueId(Long clueId) 
    {
        this.clueId = clueId;
    }

    public Long getClueId() 
    {
        return clueId;
    }
    public void setAdClueSourceId(Long adClueSourceId) 
    {
        this.adClueSourceId = adClueSourceId;
    }

    public Long getAdClueSourceId() 
    {
        return adClueSourceId;
    }
    public void setAdvertiserInfoId(Long advertiserInfoId) 
    {
        this.advertiserInfoId = advertiserInfoId;
    }

    public Long getAdvertiserInfoId() 
    {
        return advertiserInfoId;
    }
    public void setVcPhone(String vcPhone) 
    {
        this.vcPhone = vcPhone;
    }

    public String getVcPhone() 
    {
        return vcPhone;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public String getVcQq() {
        return vcQq;
    }

    public void setVcQq(String vcQq) {
        this.vcQq = vcQq;
    }

    public String getVcWeixin() {
        return vcWeixin;
    }

    public void setVcWeixin(String vcWeixin) {
        this.vcWeixin = vcWeixin;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clueId", getClueId())
            .append("adClueSourceId", getAdClueSourceId())
            .append("advertiserInfoId", getAdvertiserInfoId())
            .append("vcPhone", getVcPhone())
            .append("createTime", getCreateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
