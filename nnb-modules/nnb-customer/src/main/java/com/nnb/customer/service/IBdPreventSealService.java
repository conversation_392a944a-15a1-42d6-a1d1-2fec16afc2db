package com.nnb.customer.service;

import java.util.List;
import java.util.Map;

import com.nnb.customer.domain.BdOutbound;
import com.nnb.customer.domain.BdPreventSeal;
import com.nnb.customer.domain.SealPhone;

/**
 * 手机防封号记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-14
 */
public interface IBdPreventSealService 
{
    /**
     * 查询手机防封号记录
     * 
     * @param numId 手机防封号记录主键
     * @return 手机防封号记录
     */
    public BdPreventSeal selectBdPreventSealByNumId(Long numId);

    /**
     * 查询手机防封号记录列表
     * 
     * @param bdPreventSeal 手机防封号记录
     * @return 手机防封号记录集合
     */
    public List<BdPreventSeal> selectBdPreventSealList(BdPreventSeal bdPreventSeal);

    /**
     * 新增手机防封号记录
     * 
     * @param bdPreventSeal 手机防封号记录
     * @return 结果
     */
    public int insertBdPreventSeal(BdPreventSeal bdPreventSeal);

    /**
     * 修改手机防封号记录
     * 
     * @param bdPreventSeal 手机防封号记录
     * @return 结果
     */
    public int updateBdPreventSeal(BdPreventSeal bdPreventSeal);

    /**
     * 批量删除手机防封号记录
     * 
     * @param numIds 需要删除的手机防封号记录主键集合
     * @return 结果
     */
    public int deleteBdPreventSealByNumIds(Long[] numIds);

    /**
     * 删除手机防封号记录信息
     * 
     * @param numId 手机防封号记录主键
     * @return 结果
     */
    public int deleteBdPreventSealByNumId(Long numId);

    /**
     * 校验电话是否解封
     * @param phone
     * @return
     */
    public Boolean checkBdPreventSealByPhone(String phone);


    /**
     * 校验电话是否触发封号
     * @param phone
     * @return
     */
    public Boolean verifyPhoneIsPreventSealByPhone(String phone);

    /**
     * 骚扰电话验证
     * @param phone
     * @return
     */
    int verifyPhoneIsCrankCallByPhone(String phone);


    Boolean checkInsertPhone(BdOutbound bdOutbound);

    void modifyRule(SealPhone sealPhone);

    List<SealPhone> selectPhoneRule();

}
