package com.nnb.customer.domain.bdCall;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-04-24
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BdCallBackParam implements Serializable {

    private List<BdCallBackParamDetail> list;
}
