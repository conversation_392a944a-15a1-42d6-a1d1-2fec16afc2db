package com.nnb.customer.mapper;

import com.nnb.system.api.domain.BdCustomerJob;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-12-04
 * @Version: 1.0
 */
@Repository
public interface BdCustomerJobMapper {

    /**
     * 查询北斗内部定时任务
     *
     * @param jobId 北斗内部定时任务主键
     * @return 北斗内部定时任务
     */
    BdCustomerJob selectBdCustomerJobByJobId(Long jobId);

    /**
     * 查询北斗内部定时任务列表
     *
     * @param bdCustomerJob 北斗内部定时任务
     * @return 北斗内部定时任务集合
     */
    List<BdCustomerJob> selectBdCustomerJobList(BdCustomerJob bdCustomerJob);

    /**
     * 新增北斗内部定时任务
     *
     * @param bdCustomerJob 北斗内部定时任务
     * @return 结果
     */
    int insertBdCustomerJob(BdCustomerJob bdCustomerJob);

    /**
     * 修改北斗内部定时任务
     *
     * @param bdCustomerJob 北斗内部定时任务
     * @return 结果
     */
    int updateBdCustomerJob(BdCustomerJob bdCustomerJob);

    /**
     * 删除北斗内部定时任务
     *
     * @param jobId 北斗内部定时任务主键
     * @return 结果
     */
    int deleteBdCustomerJobByJobId(Long jobId);

    /**
     * 批量删除北斗内部定时任务
     *
     * @param jobIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBdCustomerJobByJobIds(@Param("jobIds") List<Long> jobIds);

    SysDept selectSysDeptByDingId(@Param("dingTalkUserId") String dingTalkUserId);

    SysDept selectSysDeptByDeptId(@Param("deptId") Long deptId);

    SysUser selectDingUserIdByEmail(@Param("email") String email);

    void updateExecutionStatusByGroupAndClueId(@Param("group") String group, @Param("clueId") Long clueId, @Param("status") Integer status);

    void updateStatusByGroupAndClueId(@Param("group") String group, @Param("clueId") Long clueId, @Param("status") String status);

    SysUser selectUserByDingUserId(@Param("dingTalkUserId") String dingTalkUserId);

    @Select("select ding_user_id from sys_user where user_id = #{userId}")
    String selectDingUserIdByUserId(@Param("userId") Long userId);

    List<BdCustomerJob> selectUnexecuted();
}
