package com.nnb.customer.mapper;

import java.util.List;
import com.nnb.customer.domain.BdCallOutboundConfig;

/**
 * 外呼配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-06-23
 */
public interface BdCallOutboundConfigMapper 
{
    /**
     * 查询外呼配置
     * 
     * @param id 外呼配置主键
     * @return 外呼配置
     */
    public BdCallOutboundConfig selectBdCallOutboundConfigById(Long id);

    /**
     * 查询外呼配置列表
     * 
     * @param bdCallOutboundConfig 外呼配置
     * @return 外呼配置集合
     */
    public List<BdCallOutboundConfig> selectBdCallOutboundConfigList(BdCallOutboundConfig bdCallOutboundConfig);

    /**
     * 查询外呼配置列表
     *
     * @param bdCallOutboundConfig 外呼配置
     * @return 外呼配置集合
     */
    public Integer selectCountByPhone(BdCallOutboundConfig bdCallOutboundConfig);

    /**
     * 新增外呼配置
     * 
     * @param bdCallOutboundConfig 外呼配置
     * @return 结果
     */
    public int insertBdCallOutboundConfig(BdCallOutboundConfig bdCallOutboundConfig);

    /**
     * 修改外呼配置
     * 
     * @param bdCallOutboundConfig 外呼配置
     * @return 结果
     */
    public int updateBdCallOutboundConfig(BdCallOutboundConfig bdCallOutboundConfig);

    /**
     * 删除外呼配置
     * 
     * @param id 外呼配置主键
     * @return 结果
     */
    public int deleteBdCallOutboundConfigById(Long id);

    /**
     * 批量删除外呼配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBdCallOutboundConfigByIds(Long[] ids);
}
