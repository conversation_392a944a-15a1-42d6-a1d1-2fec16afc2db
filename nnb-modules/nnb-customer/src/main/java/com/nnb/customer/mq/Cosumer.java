package com.nnb.customer.mq;

import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

@Component
public class Cosumer {

    @RabbitHandler
    @RabbitListener(queuesToDeclare = @Queue("customer_direct_message_notice_queue"))
    public void process(String message) {
        System.out.println(message);
    }

}
