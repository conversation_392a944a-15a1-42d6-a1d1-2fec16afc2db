package com.nnb.customer.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 客户跟进记录对象 bd_customers_follow
 *
 * <AUTHOR>
 * @date 2022-02-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BdCustomersFollow",description="客户跟进记录对象")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BdCustomersFollow extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @Excel(name = "线索id")
    @ApiModelProperty("线索id")
    private Long clueId;

    @Excel(name = "跟进人id")
    @ApiModelProperty("跟进人id")
    private Long numUserId;

    @Excel(name = "下发部门id")
    @ApiModelProperty("下发部门id")
    private Long numDeptId;

    @ApiModelProperty("$column.columnComment")
    private Long userId;

    @ApiModelProperty("$column.columnComment")
    private Long crmCustomersId;

    @ApiModelProperty("客户历史手机号")
    private String historyPhone;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("下次沟通时间")
    private Date nextChattime;

    @ApiModelProperty("是否决策人 1是 2否")
    private Integer isDecision;

    @ApiModelProperty("客户首选注册区域，区域 register.id")
    private Integer topRegister;

    @ApiModelProperty("销售推荐注册区域，区域 register.id")
    private Integer recommendRegister;

    @ApiModelProperty("1crm_customers_id, 2 niche.id 商机")
    private Integer type;

    @ApiModelProperty("crm_main_users.id")
    private Long crmMainUserId;

    @ApiModelProperty("crm_users.id")
    private Long crmUsersId;

    @Excel(name = "客户表id")
    @ApiModelProperty("客户表id")
    private Long customersId;

    @Excel(name = "跟进内容")
    @ApiModelProperty("跟进内容")
    private String vcContent;

    @Excel(name = "是否掉保 1 私有沟通记录 2-其他操作记录;3-备注;7-TMI加入客保; 8-新建线索自动分配;9分配销售; 10自动申领线索;11手动申领;12普通销售申领库加入客保;13客保时限过期，已掉保;14:财税线索创建 15财税线索分配：16,财税线索加入客保;17:商机创建;18商机分配：19,商机提单 20 北斗跟进记录 21北斗操作记录 22老客户操作记录 23 老客户跟进记录")
    @ApiModelProperty(" 20 北斗跟进记录 21北斗操作记录 22老客户操作记录 23 老客户跟进记录 24 资质线索操作记录")
    private Long numStatus;

    @Excel(name = "意向度ID")
    @ApiModelProperty("意向度ID")
    private Long intentionId;

    @Excel(name = "状态客户状态 1报价已报/2服务认可/3未合作/4已合作/5合同已领取/6已归档/7已提单/8提单审核通过/")
    @ApiModelProperty("状态客户状态 1报价已报/2服务认可/3未合作/4已合作/5合同已领取/6已归档/7已提单/8提单审核通过/")
    private Integer numCStatus;

    @Excel(name = "跟进状态名字")
    @ApiModelProperty("跟进状态名字")
    private Long followStatusId;

    @Excel(name = "是否决策人 1是 2否")
    @ApiModelProperty("是否决策人 1是 2否")
    private Integer numIsDecision;

    @Excel(name = "行业")
    @ApiModelProperty("行业")
    private Long numTradesId;

    @Excel(name = "销售阶段1.初步接洽2.需求确定3.方案/报价4.谈判审核5.赢单6.输单")
    @ApiModelProperty("销售阶段1.初步接洽2.需求确定3.方案/报价4.谈判审核5.赢单6.输单")
    private Integer numSaleRank;

    @Excel(name = "输单原因")
    @ApiModelProperty("输单原因")
    private Long numWhyNoDeal;

    @Excel(name = "拨打状态1.待拨打2.未接通 .3联系中")
    @ApiModelProperty("拨打状态1.待拨打2.未接通 .3联系中")
    private Long numBadaStatus;

    @Excel(name = "线索联系人id")
    @ApiModelProperty("线索联系人id")
    private Long numContactId;

    @Excel(name = " 1.下发线索 2.移交 3.线索管理分配 4.放弃 5.加客保 6.变更跟进人 7.领取线索 8.拨打电话9.拉黑 10.销售阶段 11.下发至商机池12.商机池返回至线索中心 13.客保创建线索中心的线索 需改为客保14.资料上传自动分配 15.标记是否有效线索 16.批量从线索管理移交到线索中心,17.呼叫中心分配,18.意向度质检 19.批量移入公海  20.批量移入下沉池  21,外呼拨打记录 22.编辑信息,  23.发送短信")
    @ApiModelProperty(" 1.下发线索 2.移交 3.线索管理分配 4.放弃 5.加客保 6.变更跟进人 7.领取线索 8.拨打电话9.拉黑 10.销售阶段 11.下发至商机池12.商机池返回至线索中心 13.客保创建线索中心的线索 需改为客保14.资料上传自动分配 15.标记是否有效线索 16.批量从线索管理移交到线索中心,17.呼叫中心分配,18.意向度质检 19.批量移入公海  20.批量移入下沉池  21,外呼拨打记录 22.编辑信息,  23.发送短信")
    private Long numStatusLog;

    @Excel(name = "号码状态：0:未接通；1:关机；2:拒接；3:空号；4:停机；5:正常（接通）")
    @ApiModelProperty("号码状态：0:未接通；1:关机；2:拒接；3:空号；4:停机；5:正常（接通）")
    private Long numPhoneStatus;

    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreateUserid;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreateTime;

    @Excel(name = "最后修改人")
    @ApiModelProperty("最后修改人")
    private Long numLastUpdUserid;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("最后修改时间")
    private Date datLastUpd;

    @ApiModelProperty("是否已提醒 0否 1是")
    private Integer isRemind;

    @ApiModelProperty("企业Id")
    private Long enterpriseId;

    @ApiModelProperty("客户Id")
    private Long clientId;

    @ApiModelProperty("商机池Id")
    private Long nicheFlowConfId;

    @ApiModelProperty("首咨分数")
    private Integer firstConsultScore;

    @ApiModelProperty("线索报名金额")
    private BigDecimal registrationAmount;

    @ApiModelProperty("人工外呼意向度：0:无意向；1:低意向；2:高意向")
    private Integer artOutCallIntention;

    private String uuId;

}
