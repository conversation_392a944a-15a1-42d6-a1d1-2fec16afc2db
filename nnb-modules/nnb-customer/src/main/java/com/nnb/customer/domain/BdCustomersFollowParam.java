package com.nnb.customer.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BdCustomersFollowParam extends BdCustomersFollow{
    private static final long serialVersionUID = 7815847132714891578L;

    /** 创建时间 */
    @ApiModelProperty("创建时间开始")
    private String datCreateTimeBegin;
    /** 创建时间 */
    @ApiModelProperty("创建时间结束")
    private String datCreateTimeEnd;
}
