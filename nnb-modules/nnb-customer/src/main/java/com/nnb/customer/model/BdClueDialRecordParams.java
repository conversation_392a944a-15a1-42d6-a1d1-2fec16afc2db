package com.nnb.customer.model;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 北斗拨打电话记录参数
 *
 * <AUTHOR>
 * @date 2022-03-03
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BdClueDialRecordParams implements Serializable {
    // 联系人id
    private Long numContactId;

    //线索id
    private Long clueId;

    //电话
    private String numPhone;

    //客户id
    private Long customersId;

    // 拨打状态 1.待拨打2.未接通 .3联系中
    private Long numDialStatus;

    // 是否接通 1是 0否
    private Long numIsAnswer;

    // 拨打节点 1 外呼管理，2 线索管理，3客保管理，4线索精选，5呼叫中心
    private Long numNode;

    // uuid 录音标识
    private String vcCallUuid;

    // 拨打类型 1 呼出; 2呼入
    private Long numType;

    // 录音url
    private String vcContent;

    // 通话时长  单位是秒
    private BigDecimal numContentTime;

    /** 第三方记录信息 */
    private String vcOutsideMsg;

    //参数字符串key
    private String diykey;

    //参数字符串value
    private String diyvalue;

    /**
     * 录音文件
     */
    private MultipartFile file;
}
