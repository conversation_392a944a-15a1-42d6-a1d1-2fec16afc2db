package com.nnb.customer.model;

import com.nnb.customer.domain.BdOutbound;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="BdOutboundContactsVo",description="通话记录对象Vo")
public class BdOutboundContactsVo extends BdOutbound {
    private static final long serialVersionUID = -9002001475025993915L;

    /** 联系人名称 */
    @ApiModelProperty("联系人名称")
    private String contactsName;
    /** 通话时长 */
    @ApiModelProperty("通话时长")
    private String contentDate;
    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    private String createUserName;

    private Long  numClueId;

    private String numStatus;

    private String numIsFriend;
}
