<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdOutboundMapper">

    <resultMap type="com.nnb.customer.domain.BdOutbound" id="BdOutboundResult">
        <result property="id"    column="id"    />
        <result property="numPhone"    column="num_phone"    />
        <result property="numIsAnswer"    column="num_is_answer"    />
        <result property="numIsCreate"    column="num_is_create"    />
        <result property="numContentTime"    column="num_content_time"    />
        <result property="vcContent"    column="vc_content"    />
        <result property="vcCallUuid"    column="vc_call_uuid"    />
        <result property="vcMd5"    column="vc_md5"    />
        <result property="numNode"    column="num_node"    />
        <result property="numContactsId"    column="num_contacts_id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="numCustomerId"    column="num_customer_id"    />
        <result property="vcRemark"    column="vc_remark"    />
        <result property="numCallType"    column="num_call_type"    />
        <result property="numType"    column="num_type"    />
        <result property="vcOutsideMsg"    column="vc_outside_msg"    />
        <result property="numDeptId"    column="num_dept_id"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="numUpdatedBy"    column="num_updated_by"    />
        <result property="datCreatedAt"    column="dat_created_at"    />
        <result property="datUpdatedAt"    column="dat_updated_at"    />
        <result property="crmCustomersMainIdOld"    column="crm_customers_main_id_old"    />
        <result property="enterpriseId"    column="enterprise_id"    />
        <result property="clientId"    column="client_id"    />
        <result property="nicheFlowConfId"    column="niche_flow_conf_id"    />
        <result property="enterpriseDominant"    column="enterprise_dominant"    />
        <result property="content"    column="content"    />
        <result property="llmContent"    column="llm_content"    />
        <result property="callId"    column="call_id"    />
        <result property="callUserId"    column="call_user_id"    />
    </resultMap>

    <sql id="selectBdOutboundVo">
        select id, num_phone, num_is_answer, num_is_create, num_content_time, vc_content, vc_call_uuid, vc_md5, num_node, num_contacts_id, clue_id, num_customer_id, vc_remark, num_call_type, num_type, vc_outside_msg, num_dept_id, num_created_by, num_updated_by, dat_created_at, dat_updated_at, crm_customers_main_id_old, enterprise_id, client_id, niche_flow_conf_id, enterprise_dominant, content, llm_content, call_id, call_user_id from bd_outbound
    </sql>

    <select id="selectBdOutboundList" parameterType="com.nnb.customer.domain.BdOutbound" resultMap="BdOutboundResult">
        <include refid="selectBdOutboundVo"/>
        <where>
            <if test="numPhone != null  and numPhone != ''"> and num_phone = #{numPhone}</if>
            <if test="numIsAnswer != null "> and num_is_answer = #{numIsAnswer}</if>
            <if test="numIsCreate != null "> and num_is_create = #{numIsCreate}</if>
            <if test="numContentTime != null "> and num_content_time = #{numContentTime}</if>
            <if test="vcContent != null  and vcContent != ''"> and vc_content = #{vcContent}</if>
            <if test="vcCallUuid != null  and vcCallUuid != ''"> and vc_call_uuid = #{vcCallUuid}</if>
            <if test="vcMd5 != null  and vcMd5 != ''"> and vc_md5 = #{vcMd5}</if>
            <if test="numNode != null "> and num_node = #{numNode}</if>
            <if test="numContactsId != null "> and num_contacts_id = #{numContactsId}</if>
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="numCustomerId != null "> and num_customer_id = #{numCustomerId}</if>
            <if test="vcRemark != null  and vcRemark != ''"> and vc_remark = #{vcRemark}</if>
            <if test="numCallType != null "> and num_call_type = #{numCallType}</if>
            <if test="numType != null "> and num_type = #{numType}</if>
            <if test="vcOutsideMsg != null  and vcOutsideMsg != ''"> and vc_outside_msg = #{vcOutsideMsg}</if>
            <if test="numDeptId != null "> and num_dept_id = #{numDeptId}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null "> and num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAt != null "> and dat_created_at = #{datCreatedAt}</if>
            <if test="datUpdatedAt != null "> and dat_updated_at = #{datUpdatedAt}</if>
            <if test="crmCustomersMainIdOld != null "> and crm_customers_main_id_old = #{crmCustomersMainIdOld}</if>
            <if test="enterpriseId != null "> and enterprise_id = #{enterpriseId}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="nicheFlowConfId != null "> and niche_flow_conf_id = #{nicheFlowConfId}</if>
            <if test="enterpriseDominant != null "> and enterprise_dominant = #{enterpriseDominant}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="llmContent != null  and llmContent != ''"> and llm_content = #{llmContent}</if>
            <if test="callId != null "> and call_id = #{callId}</if>
            <if test="callUserId != null  and callUserId != ''"> and call_user_id = #{callUserId}</if>
        </where>
        ORDER BY dat_created_at DESC
    </select>

    <select id="selectBdOutboundById" parameterType="Long" resultMap="BdOutboundResult">
        <include refid="selectBdOutboundVo"/>
        where id = #{id}
    </select>
    <select id="selectBdOutboundVoList" parameterType="com.nnb.customer.model.BdOutboundDto"
            resultType="com.nnb.customer.model.BdOutboundContactsVo">
        select bo.id id,
        CASE bcd.num_tab
        when 1 then bo.num_phone
        when 2 then insert(bo.num_phone,4,4,"****")
        when 3 then insert(bo.num_phone,4,4,"****")
        else bo.num_phone
        END AS 'vcPhone',
        case  bo.num_node
        when 5 then insert(bo.num_phone,4,4,"****")
        when 2 then insert(bo.num_phone,4,4,"****")
        when 3  then insert(bo.num_phone,4,4,"****")
        when 4 then insert(bo.num_phone,4,4,"****")
        when 1 then bo.num_phone
        else bo.num_phone
        end as 'numPhone',

        bo.num_is_answer numIsAnswer,
        bo.num_is_create numIsCreate,
        bo.num_content_time numContentTime,
        bo.vc_content vcContent,
        bo.vc_call_uuid vcCallUuid,
        bo.vc_md5 vcMd5,
        bo.num_node numNode,
        bo.num_contacts_id numContactsId,
        bo.clue_id clueId,
        bo.num_customer_id numCustomerId,
        bo.vc_remark vcRemark,
        bo.num_call_type,
        bo.num_type numType,
        bo.vc_outside_msg vcOutsideMsg,
        bo.num_dept_id numDeptId,
        bo.num_created_by numCreatedBy,
        bo.num_updated_by numUpdatedBy,
        bo.dat_created_at datCreatedAt,
        bo.dat_updated_at datUpdatedAt,
        bcc.vc_name contactsName,
        su.nick_name createUserName
        from bd_outbound bo
        left join bd_clue_contacts bcc on bo.clue_id = bcc.clue_id
        left join sys_user su on bo.num_created_by = su.user_id
        left join bd_clue_detail bcd on bcd.clue_id=bo.clue_id
        <where>
            <if test="numTab !=null">bcd.num_tab= #{numTab}</if>
            <if test="vcPhone != null  and vcPhone != ''">and (bo.num_phone = #{vcPhone} or bo.num_phone = #{cipherTextPhone})</if>
            <if test="numIsAnswer != null ">and bo.num_is_answer = #{numIsAnswer}</if>
            <if test="numIsCreate != null ">and bo.num_is_create = #{numIsCreate}</if>
            <if test="numContentTime != null ">and bo.num_content_time = #{numContentTime}</if>
            <if test="vcContent != null  and vcContent != ''">and bo.vc_content = #{vcContent}</if>
            <if test="vcCallUuid != null  and vcCallUuid != ''">and bo.vc_call_uuid = #{vcCallUuid}</if>
            <if test="vcMd5 != null  and vcMd5 != ''">and bo.vc_md5 = #{vcMd5}</if>
            <if test="numNode != null ">and bo.num_node = #{numNode}</if>
            <if test="numContactsId != null ">and bo.num_contacts_id = #{numContactsId}</if>
            <if test="clueId != null ">and bo.clue_id = #{clueId}</if>
            <if test="numCustomerId != null ">and bo.num_customer_id = #{numCustomerId}</if>
            <if test="vcRemark != null  and vcRemark != ''">and bo.vc_remark = #{vcRemark}</if>
            <if test="numType != null ">and bo.num_type = #{numType}</if>
            <if test="numDeptId != null ">and bo.num_dept_id = #{numDeptId}</if>
            <if test="numCreatedBy != null ">and bo.num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null ">and bo.num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAtBegin != null and datCreatedAtEnd != null  ">and
            (bo.dat_created_at &gt;= #{datCreatedAtBegin} and bo.dat_created_at &lt;= #{datCreatedAtEnd})
            </if>
            <if test="numContentTimeBegin != null ">and bo.num_content_time &gt;= #{numContentTimeBegin}</if>
            <if test="numContentTimeEnd != null ">and bo.num_content_time &lt;= #{numContentTimeEnd}</if>
            <if test="deptId != null ">and su.dept_id = #{deptId}</if>
        </where>
        <if test="queryNum != null and querySize != null">
            order by bo.id desc
            limit #{queryNum}, #{querySize}
        </if>
--         order by bo.dat_created_at desc
    </select>

    <select id="selectBdOutboundVosByEnterpriseId" parameterType="com.nnb.customer.model.BdOutboundDto" resultType="com.nnb.customer.model.BdOutboundContactsVo">
        select
        bo.id id,
        bo.num_phone as 'numPhone',
        bo.vc_content vcContent,
        ec.contactName contactsName,
        bo.dat_created_at as datCreatedAt
        from bd_outbound bo
        left join erp_client ec on bo.client_id = ec.id
        where bo.enterprise_id = #{enterpriseId}
    </select>

    <insert id="insertBdOutbound" parameterType="com.nnb.customer.domain.BdOutbound" useGeneratedKeys="true" keyProperty="id">
        insert into bd_outbound
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numPhone != null">num_phone,</if>
            <if test="numIsAnswer != null">num_is_answer,</if>
            <if test="numIsCreate != null">num_is_create,</if>
            <if test="numContentTime != null">num_content_time,</if>
            <if test="vcContent != null">vc_content,</if>
            <if test="vcCallUuid != null">vc_call_uuid,</if>
            <if test="vcMd5 != null">vc_md5,</if>
            <if test="numNode != null">num_node,</if>
            <if test="numContactsId != null">num_contacts_id,</if>
            <if test="clueId != null">clue_id,</if>
            <if test="numCustomerId != null">num_customer_id,</if>
            <if test="vcRemark != null">vc_remark,</if>
            <if test="numCallType != null">num_call_type,</if>
            <if test="numType != null">num_type,</if>
            <if test="vcOutsideMsg != null">vc_outside_msg,</if>
            <if test="numDeptId != null">num_dept_id,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="numUpdatedBy != null">num_updated_by,</if>
            <if test="datCreatedAt != null">dat_created_at,</if>
            <if test="datUpdatedAt != null">dat_updated_at,</if>
            <if test="crmCustomersMainIdOld != null">crm_customers_main_id_old,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="clientId != null">client_id,</if>
            <if test="nicheFlowConfId != null">niche_flow_conf_id,</if>
            <if test="enterpriseDominant != null">enterprise_dominant,</if>
            <if test="content != null">content,</if>
            <if test="llmContent != null">llm_content,</if>
            <if test="callId != null">call_id,</if>
            <if test="callUserId != null and callUserId != ''">call_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numPhone != null">#{numPhone},</if>
            <if test="numIsAnswer != null">#{numIsAnswer},</if>
            <if test="numIsCreate != null">#{numIsCreate},</if>
            <if test="numContentTime != null">#{numContentTime},</if>
            <if test="vcContent != null">#{vcContent},</if>
            <if test="vcCallUuid != null">#{vcCallUuid},</if>
            <if test="vcMd5 != null">#{vcMd5},</if>
            <if test="numNode != null">#{numNode},</if>
            <if test="numContactsId != null">#{numContactsId},</if>
            <if test="clueId != null">#{clueId},</if>
            <if test="numCustomerId != null">#{numCustomerId},</if>
            <if test="vcRemark != null">#{vcRemark},</if>
            <if test="numCallType != null">#{numCallType},</if>
            <if test="numType != null">#{numType},</if>
            <if test="vcOutsideMsg != null">#{vcOutsideMsg},</if>
            <if test="numDeptId != null">#{numDeptId},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="numUpdatedBy != null">#{numUpdatedBy},</if>
            <if test="datCreatedAt != null">#{datCreatedAt},</if>
            <if test="datUpdatedAt != null">#{datUpdatedAt},</if>
            <if test="crmCustomersMainIdOld != null">#{crmCustomersMainIdOld},</if>
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="nicheFlowConfId != null">#{nicheFlowConfId},</if>
            <if test="enterpriseDominant != null">#{enterpriseDominant},</if>
            <if test="content != null">#{content},</if>
            <if test="llmContent != null">#{llmContent},</if>
            <if test="callId != null">#{callId},</if>
            <if test="callUserId != null and callUserId != ''">#{callUserId},</if>
        </trim>
    </insert>

    <update id="updateBdOutbound" parameterType="com.nnb.customer.domain.BdOutbound">
        update bd_outbound
        <trim prefix="SET" suffixOverrides=",">
            <if test="numPhone != null">num_phone = #{numPhone},</if>
            <if test="numIsAnswer != null">num_is_answer = #{numIsAnswer},</if>
            <if test="numIsCreate != null">num_is_create = #{numIsCreate},</if>
            <if test="numContentTime != null">num_content_time = #{numContentTime},</if>
            <if test="vcContent != null">vc_content = #{vcContent},</if>
            <if test="vcCallUuid != null">vc_call_uuid = #{vcCallUuid},</if>
            <if test="vcMd5 != null">vc_md5 = #{vcMd5},</if>
            <if test="numNode != null">num_node = #{numNode},</if>
            <if test="numContactsId != null">num_contacts_id = #{numContactsId},</if>
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="numCustomerId != null">num_customer_id = #{numCustomerId},</if>
            <if test="vcRemark != null">vc_remark = #{vcRemark},</if>
            <if test="numCallType != null">num_call_type = #{numCallType},</if>
            <if test="numType != null">num_type = #{numType},</if>
            <if test="vcOutsideMsg != null">vc_outside_msg = #{vcOutsideMsg},</if>
            <if test="numDeptId != null">num_dept_id = #{numDeptId},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datCreatedAt != null">dat_created_at = #{datCreatedAt},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
            <if test="crmCustomersMainIdOld != null">crm_customers_main_id_old = #{crmCustomersMainIdOld},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="nicheFlowConfId != null">niche_flow_conf_id = #{nicheFlowConfId},</if>
            <if test="enterpriseDominant != null">enterprise_dominant = #{enterpriseDominant},</if>
            <if test="content != null">content = #{content},</if>
            <if test="llmContent != null">llm_content = #{llmContent},</if>
            <if test="callId != null">call_id = #{callId},</if>
            <if test="callUserId != null and callUserId != ''">call_user_id = #{callUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdOutboundById" parameterType="Long">
        delete
        from bd_outbound
        where id = #{id}
    </delete>

    <delete id="deleteBdOutboundByIds" parameterType="String">
        delete from bd_outbound where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectBdOutbounds" parameterType="com.nnb.customer.domain.BdStatementVo" resultMap="BdOutboundResult">
        SELECT
        GROUP_CONCAT(distinct clue_id),
        num_created_by,
        count(num_is_answer)
        FROM bd_outbound
        <where>
            <if test="numIsAnswer !=null">and num_is_answer=#{numIsAnswer}</if>
            <if test="numType !=null">and num_type=1</if>
            <if test="numCallType!=null">and num_call_type = 3</if>
            <if test="numNode!=null !=null">and num_node = 5</if>
        </where>
        GROUP BY IFNULL(num_created_by,UUID())
        limit #{queryNum}, #{querySize};

    </select>

    <select id="selectBdOutboundsMaxByClueIds" parameterType="String" resultMap="BdOutboundResult">
        SELECT
        max( id ),
        num_content_time,
        vc_content,
        clue_id
        FROM
        bd_outbound
        WHERE
        clue_id in
        <foreach item="id" collection="clueIds.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY clue_id ;
    </select>
    <select id="userCallDurationStatistics" resultType="com.nnb.customer.domain.vo.outbound.BdOutboundStaticsVo">
        select
        bo.dat_created_at AS datCreatedAt,
        su.nick_name AS nickName,
        sd.dept_name AS deptName,
        <if test="statisticsType != null">
            GROUP_CONCAT(bo.num_is_answer) AS numIsAnswerStr,
            GROUP_CONCAT(bo.num_content_time) AS numContentTimeStr,
        </if>
        bo.num_is_answer AS numIsAnswer
        FROM bd_outbound bo
        LEFT JOIN sys_user su ON bo.num_created_by = su.user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        <where>
            1 = 1
            <if test="userId != null">
                AND bo.num_created_by = #{userId}
            </if>
            <if test="statisticsStartTime != null and statisticsStartTime != '' and statisticsEndTime != null and statisticsEndTime != ''">
                AND (bo.dat_created_at &gt;= #{statisticsStartTime} and bo.dat_created_at &lt;= #{statisticsEndTime})
            </if>
            <if test="deptId != null and deptId.size > 0">
                AND sd.dept_id in
                <foreach collection="deptId" item="one" open="(" separator="," close=")">
                    #{one}
                </foreach>
            </if>
            <if test="isJob != null and isJob == 0">
                AND su.status = 1
            </if>
            <if test="isJob != null and isJob == 1">
                AND su.status = 0
            </if>
        </where>
        ${params.dataScope}
        <if test="statisticsType != null and statisticsType == 1">
            GROUP BY bo.num_created_by
        </if>
        <if test="statisticsType != null and statisticsType == 2">
            GROUP BY bo.num_created_by,date_format(bo.dat_created_at,'%Y-%m-%d')
        </if>
    </select>

    <select id="countSelectBdOutboundVoList" resultType="java.lang.Long">
        select count(bo.id)
        from bd_outbound bo
        <if test="
            deptId != null
        ">
            left join sys_user su on bo.num_created_by = su.user_id
        </if>
        <if test="
            numTab !=null
        ">
            left join bd_clue_detail bcd on bcd.clue_id=bo.clue_id
        </if>
        <where>
            <if test="numTab !=null">bcd.num_tab= #{numTab}</if>
            <if test="numPhone != null  and numPhone != ''">and bo.num_phone = #{numPhone}</if>
            <if test="numIsAnswer != null ">and bo.num_is_answer = #{numIsAnswer}</if>
            <if test="numIsCreate != null ">and bo.num_is_create = #{numIsCreate}</if>
            <if test="numContentTime != null ">and bo.num_content_time = #{numContentTime}</if>
            <if test="vcContent != null  and vcContent != ''">and bo.vc_content = #{vcContent}</if>
            <if test="vcCallUuid != null  and vcCallUuid != ''">and bo.vc_call_uuid = #{vcCallUuid}</if>
            <if test="vcMd5 != null  and vcMd5 != ''">and bo.vc_md5 = #{vcMd5}</if>
            <if test="numNode != null ">and bo.num_node = #{numNode}</if>
            <if test="numContactsId != null ">and bo.num_contacts_id = #{numContactsId}</if>
            <if test="clueId != null ">and bo.clue_id = #{clueId}</if>
            <if test="numCustomerId != null ">and bo.num_customer_id = #{numCustomerId}</if>
            <if test="vcRemark != null  and vcRemark != ''">and bo.vc_remark = #{vcRemark}</if>
            <if test="numType != null ">and bo.num_type = #{numType}</if>
            <if test="numDeptId != null ">and bo.num_dept_id = #{numDeptId}</if>
            <if test="numCreatedBy != null ">and bo.num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null ">and bo.num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAtBegin != null and datCreatedAtEnd != null  ">and (bo.dat_created_at between
                #{datCreatedAtBegin} and date_format(#{datCreatedAtEnd},'%Y-%m-%d 23:59:59'))
            </if>
            <if test="numContentTimeBegin != null ">and bo.num_content_time &gt;= #{numContentTimeBegin}</if>
            <if test="numContentTimeEnd != null ">and bo.num_content_time &lt;= #{numContentTimeEnd}</if>
            <if test="deptId != null ">and su.dept_id = #{deptId}</if>
        </where>
    </select>
    <sql id="bdOutboundWhere">
        <where>
            1=1
            <if test="enterpriseDominant != null">
                AND (bo.enterprise_dominant = #{enterpriseDominant} or bo.enterprise_dominant = 0)
            </if>
            <if test="numTab !=null">bcd.num_tab= #{numTab}</if>
            <if test="vcPhone != null  and vcPhone != ''">and (bo.num_phone = #{vcPhone} or bo.num_phone = #{cipherTextPhone})</if>
            <if test="numIsAnswer != null ">and bo.num_is_answer = #{numIsAnswer}</if>
            <if test="numIsCreate != null ">and bo.num_is_create = #{numIsCreate}</if>
            <if test="numContentTime != null ">and bo.num_content_time = #{numContentTime}</if>
            <if test="vcContent != null  and vcContent != ''">and bo.vc_content = #{vcContent}</if>
            <if test="vcCallUuid != null  and vcCallUuid != ''">and bo.vc_call_uuid = #{vcCallUuid}</if>
            <if test="vcMd5 != null  and vcMd5 != ''">and bo.vc_md5 = #{vcMd5}</if>
            <if test="numNode != null ">and bo.num_node = #{numNode}</if>
            <if test="numContactsId != null ">and bo.num_contacts_id = #{numContactsId}</if>
            <if test="clueId != null ">and bo.clue_id = #{clueId}</if>
            <if test="numCustomerId != null ">and bo.num_customer_id = #{numCustomerId}</if>
            <if test="vcRemark != null  and vcRemark != ''">and bo.vc_remark = #{vcRemark}</if>
            <if test="numType != null ">and bo.num_type = #{numType}</if>
            <if test="numDeptId != null ">and bo.num_dept_id = #{numDeptId}</if>
            <if test="numCreatedBy != null ">and bo.num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null ">and bo.num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAtBegin != null and datCreatedAtEnd != null and datCreatedAtBegin != '' and datCreatedAtEnd != ''">
            and (bo.dat_created_at &gt;= #{datCreatedAtBegin} and bo.dat_created_at &lt;= #{datCreatedAtEnd})
            </if>
            <if test="numContentTimeBegin != null ">and bo.num_content_time &gt;= #{numContentTimeBegin}</if>
            <if test="numContentTimeEnd != null ">and bo.num_content_time &lt;= #{numContentTimeEnd}</if>
            <if test="deptId != null ">and su.dept_id = #{deptId}</if>
        </where>
    </sql>

    <sql id="bdOutboundSql">
        select bo.id id,
--         CASE bcd.num_tab
--         when 1 then bo.num_phone
--         when 2 then insert(bo.num_phone,4,4,"****")
--         when 3 then insert(bo.num_phone,4,4,"****")
--         else bo.num_phone
--         END AS 'vcPhone',
        bo.num_phone as 'numPhone',
        bo.num_is_answer numIsAnswer,
        bo.num_is_create numIsCreate,
        bo.num_content_time numContentTime,
        bo.vc_content vcContent,
        bo.vc_call_uuid vcCallUuid,
        bo.vc_md5 vcMd5,
        bo.num_node numNode,
        bo.num_contacts_id numContactsId,
        bo.clue_id clueId,
        bo.num_customer_id numCustomerId,
        bo.vc_remark vcRemark,
        bo.num_call_type,
        bo.num_type numType,
        bo.vc_outside_msg vcOutsideMsg,
        bo.num_dept_id numDeptId,
        bo.num_created_by numCreatedBy,
        bo.num_updated_by numUpdatedBy,
        bo.dat_created_at datCreatedAt,
        bo.dat_updated_at datUpdatedAt,
        su.nick_name createUserName
        from bd_outbound bo
    </sql>
    <select id="countQueryBdOutboundVoDeptList" resultType="java.lang.Long">
        select count(bo.id)
        from bd_outbound bo
        left join sys_user su on bo.num_created_by = su.user_id
        LEFT JOIN sys_dept fsd on su.dept_id = fsd.dept_id
<!--        <if test="-->
<!--            deptId != null-->
<!--        ">-->
<!--            left join sys_user su on bo.num_created_by = su.user_id-->
<!--        </if>-->
        <if test="
            numTab !=null
        ">
            left join bd_clue_detail bcd on bcd.clue_id=bo.clue_id
        </if>
        <include refid="bdOutboundWhere"/>
        ${params.dataScope}
    </select>
    <select id="queryBdOutboundVoDeptList" resultType="com.nnb.customer.model.BdOutboundContactsVo">
        <include refid="bdOutboundSql"/>
        left join sys_user su on bo.num_created_by = su.user_id
        LEFT JOIN sys_dept fsd on su.dept_id = fsd.dept_id
        <include refid="bdOutboundWhere"/>
        ${params.dataScope}
        <if test="queryNum != null and querySize != null">
            order by bo.id desc
            limit #{queryNum}, #{querySize}
        </if>
    </select>
    <select id="countQueryBdOutboundVoPerList" resultType="java.lang.Long">
        select count(bo.id)
        from bd_outbound bo
        left join sys_user su on bo.num_created_by = su.user_id
        LEFT JOIN sys_dept sd on su.dept_id = sd.dept_id
<!--        <if test="-->
<!--            deptId != null-->
<!--        ">-->
<!--            left join sys_user su on bo.num_created_by = su.user_id-->
<!--        </if>-->
        <if test="
            numTab !=null
        ">
            left join bd_clue_detail bcd on bcd.clue_id=bo.clue_id
        </if>
        <include refid="bdOutboundWhere"/>
        ${params.dataScope}
    </select>
    <select id="queryBdOutboundVoPerList" resultType="com.nnb.customer.model.BdOutboundContactsVo">
        <include refid="bdOutboundSql"/>
        left join sys_user su on bo.num_created_by = su.user_id
        LEFT JOIN sys_dept sd on su.dept_id = sd.dept_id
        <include refid="bdOutboundWhere"/>
        ${params.dataScope}
        <if test="queryNum != null and querySize != null">
            order by bo.id desc
            limit #{queryNum}, #{querySize}
        </if>
    </select>
    <select id="selectUserTagsByClueIdList" resultType="com.nnb.customer.model.BdOutboundContactsVo">
        select bc.id as numClueId, bc.num_status as numStatus, bcs.num_is_friend as numIsFriend
        from bd_clue bc
        left join bd_clue_status bcs on bc.id = bcs.clue_id
        where bc.id in  (
        <foreach collection="clueIdList" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="getClueDialCount" resultType="com.nnb.customer.domain.BdOutbound">
        select
            id,
            num_is_answer as numIsAnswer
        from bd_outbound bo where clue_id = #{clueId}
        <if test="numIsAnswer != null">
            and num_is_answer = #{numIsAnswer}
        </if>
    </select>

    <select id="selectTodayOutboundByClueIds" resultType="java.lang.Long">
        select clue_id from bd_outbound
        where dat_created_at &gt;= #{startTime} and dat_created_at &lt;= #{endTime}
        and clue_id in (
        <foreach collection="idList" item="clueId" separator=",">
            #{clueId}
        </foreach>
        )
    </select>

    <select id="selectTodayBdOutboundWithCallId" resultMap="BdOutboundResult">
        SELECT id, call_id, call_user_id
        FROM bd_outbound
        WHERE call_id != 0
            AND dat_created_at &gt;= #{startTime} AND dat_created_at &lt;= #{endTime}
            AND num_content_time &gt;= 10
            AND content IS NULL
    </select>

</mapper>
