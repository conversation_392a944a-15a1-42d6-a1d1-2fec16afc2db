<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdAiTaskClueMapper">
    
    <resultMap type="BdAiTaskClue" id="BdAiTaskClueResult">
        <result property="numId"    column="num_id"    />
        <result property="numAiTaskId"    column="num_ai_task_id"    />
        <result property="numClueId"    column="num_clue_id"    />
    </resultMap>

    <sql id="selectBdAiTaskClueVo">
        select num_id, num_ai_task_id, num_clue_id from bd_ai_task_clue
    </sql>

    <select id="selectBdAiTaskClueList" parameterType="BdAiTaskClue" resultMap="BdAiTaskClueResult">
        <include refid="selectBdAiTaskClueVo"/>
        <where>  
            <if test="numAiTaskId != null "> and num_ai_task_id = #{numAiTaskId}</if>
            <if test="numClueId != null "> and num_clue_id = #{numClueId}</if>
        </where>
    </select>
    
    <select id="selectBdAiTaskClueByNumId" parameterType="Long" resultMap="BdAiTaskClueResult">
        <include refid="selectBdAiTaskClueVo"/>
        where num_id = #{numId}
    </select>
        
    <insert id="insertBdAiTaskClue" parameterType="BdAiTaskClue" useGeneratedKeys="true" keyProperty="numId">
        insert into bd_ai_task_clue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numAiTaskId != null">num_ai_task_id,</if>
            <if test="numClueId != null">num_clue_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numAiTaskId != null">#{numAiTaskId},</if>
            <if test="numClueId != null">#{numClueId},</if>
         </trim>
    </insert>

    <update id="updateBdAiTaskClue" parameterType="BdAiTaskClue">
        update bd_ai_task_clue
        <trim prefix="SET" suffixOverrides=",">
            <if test="numAiTaskId != null">num_ai_task_id = #{numAiTaskId},</if>
            <if test="numClueId != null">num_clue_id = #{numClueId},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <delete id="deleteBdAiTaskClueByNumId" parameterType="Long">
        delete from bd_ai_task_clue where num_id = #{numId}
    </delete>

    <delete id="deleteBdAiTaskClueByNumIds" parameterType="String">
        delete from bd_ai_task_clue where num_id in 
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>
</mapper>