<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdClueGuestSrcMapper">
    
    <resultMap type="BdClueGuestSrc" id="BdClueGuestSrcResult">
        <result property="id"    column="id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="guestSrcId"    column="guest_src_id"    />
        <result property="numCreateUserid"    column="num_create_userid"    />
        <result property="datCreateTime"    column="dat_create_time"    />
    </resultMap>

    <sql id="selectBdClueGuestSrcVo">
        select id, clue_id, guest_src_id, num_create_userid, dat_create_time from bd_clue_guest_src
    </sql>

    <select id="selectBdClueGuestSrcList" parameterType="BdClueGuestSrc" resultMap="BdClueGuestSrcResult">
        <include refid="selectBdClueGuestSrcVo"/>
        <where>  
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="guestSrcId != null "> and guest_src_id = #{guestSrcId}</if>
            <if test="numCreateUserid != null "> and num_create_userid = #{numCreateUserid}</if>
            <if test="datCreateTime != null "> and dat_create_time = #{datCreateTime}</if>
        </where>
    </select>
    
    <select id="selectBdClueGuestSrcById" parameterType="Long" resultMap="BdClueGuestSrcResult">
        <include refid="selectBdClueGuestSrcVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBdClueGuestSrc" parameterType="BdClueGuestSrc" useGeneratedKeys="true" keyProperty="id">
        insert into bd_clue_guest_src
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="guestSrcId != null">guest_src_id,</if>
            <if test="numCreateUserid != null">num_create_userid,</if>
            <if test="datCreateTime != null">dat_create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="guestSrcId != null">#{guestSrcId},</if>
            <if test="numCreateUserid != null">#{numCreateUserid},</if>
            <if test="datCreateTime != null">#{datCreateTime},</if>
         </trim>
    </insert>

    <update id="updateBdClueGuestSrc" parameterType="BdClueGuestSrc">
        update bd_clue_guest_src
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="guestSrcId != null">guest_src_id = #{guestSrcId},</if>
            <if test="numCreateUserid != null">num_create_userid = #{numCreateUserid},</if>
            <if test="datCreateTime != null">dat_create_time = #{datCreateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdClueGuestSrcById" parameterType="Long">
        delete from bd_clue_guest_src where id = #{id}
    </delete>

    <delete id="deleteBdClueGuestSrcByIds" parameterType="String">
        delete from bd_clue_guest_src where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>