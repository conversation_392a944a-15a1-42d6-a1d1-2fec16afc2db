<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.LocationMapper">
    
    <resultMap type="Location" id="LocationResult">
        <result property="id"    column="id"    />
        <result property="phone"    column="phone"    />
        <result property="provinceId"    column="province_id"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
    </resultMap>

    <sql id="selectLocationVo">
        select id, phone, province_id, province, city from location
    </sql>

    <select id="selectLocationList" parameterType="Location" resultMap="LocationResult">
        <include refid="selectLocationVo"/>
        <where>  
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="provinceId != null "> and province_id = #{provinceId}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
        </where>
    </select>
    
    <select id="selectLocationById" parameterType="Long" resultMap="LocationResult">
        <include refid="selectLocationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLocation" parameterType="Location" useGeneratedKeys="true" keyProperty="id">
        insert into location
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null">phone,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null">#{phone},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
         </trim>
    </insert>

    <update id="updateLocation" parameterType="Location">
        update location
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null">phone = #{phone},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLocationById" parameterType="Long">
        delete from location where id = #{id}
    </delete>

    <delete id="deleteLocationByIds" parameterType="String">
        delete from location where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>