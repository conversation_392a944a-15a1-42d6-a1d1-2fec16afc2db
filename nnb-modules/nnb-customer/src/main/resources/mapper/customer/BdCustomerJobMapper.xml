<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdCustomerJobMapper">

    <resultMap type="com.nnb.system.api.domain.BdCustomerJob" id="BdCustomerJobResult">
        <result property="jobId"    column="job_id"    />
        <result property="jobName"    column="job_name"    />
        <result property="jobGroup"    column="job_group"    />
        <result property="invokeTarget"    column="invoke_target"    />
        <result property="cronExpression"    column="cron_expression"    />
        <result property="misfirePolicy"    column="misfire_policy"    />
        <result property="concurrent"    column="concurrent"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="clueId"    column="clue_id"    />
        <result property="bdCustomersFollowId"    column="bd_customers_follow_id"    />
        <result property="executionStatus"    column="execution_status"    />
    </resultMap>

    <sql id="selectBdCustomerJobVo">
        select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark, clue_id, bd_customers_follow_id, execution_status from bd_customer_job
    </sql>

    <select id="selectBdCustomerJobList" parameterType="com.nnb.system.api.domain.BdCustomerJob" resultMap="BdCustomerJobResult">
        <include refid="selectBdCustomerJobVo"/>
        <where>
            <if test="invokeTarget != null  and invokeTarget != ''"> and invoke_target = #{invokeTarget}</if>
            <if test="cronExpression != null  and cronExpression != ''"> and cron_expression = #{cronExpression}</if>
            <if test="misfirePolicy != null  and misfirePolicy != ''"> and misfire_policy = #{misfirePolicy}</if>
            <if test="concurrent != null  and concurrent != ''"> and concurrent = #{concurrent}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="bdCustomersFollowId != null "> and bd_customers_follow_id = #{bdCustomersFollowId}</if>
            <if test="executionStatus != null "> and execution_status = #{executionStatus}</if>
            <if test="clueIdList != null and clueIdList.size() >  0">
                and clue_id in (
                    <foreach collection="clueIdList" item="id" separator=",">
                        #{id}
                    </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectBdCustomerJobByJobId" parameterType="Long" resultMap="BdCustomerJobResult">
        <include refid="selectBdCustomerJobVo"/>
        where job_id = #{jobId}
    </select>

    <insert id="insertBdCustomerJob" parameterType="com.nnb.system.api.domain.BdCustomerJob" useGeneratedKeys="true" keyProperty="jobId">
        insert into bd_customer_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobName != null">job_name,</if>
            <if test="jobGroup != null">job_group,</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
            <if test="cronExpression != null">cron_expression,</if>
            <if test="misfirePolicy != null">misfire_policy,</if>
            <if test="concurrent != null">concurrent,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="clueId != null">clue_id,</if>
            <if test="bdCustomersFollowId != null">bd_customers_follow_id,</if>
            <if test="executionStatus != null">execution_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobName != null">#{jobName},</if>
            <if test="jobGroup != null">#{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
            <if test="cronExpression != null">#{cronExpression},</if>
            <if test="misfirePolicy != null">#{misfirePolicy},</if>
            <if test="concurrent != null">#{concurrent},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="clueId != null">#{clueId},</if>
            <if test="bdCustomersFollowId != null">#{bdCustomersFollowId},</if>
            <if test="executionStatus != null">#{executionStatus},</if>
        </trim>
    </insert>

    <update id="updateBdCustomerJob" parameterType="com.nnb.system.api.domain.BdCustomerJob">
        update bd_customer_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="jobGroup != null">job_group = #{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null">concurrent = #{concurrent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="bdCustomersFollowId != null">bd_customers_follow_id = #{bdCustomersFollowId},</if>
            <if test="executionStatus != null">execution_status = #{executionStatus},</if>
        </trim>
        where job_id = #{jobId}
    </update>

    <delete id="deleteBdCustomerJobByJobId" parameterType="Long">
        delete from bd_customer_job where job_id = #{jobId}
    </delete>

    <delete id="deleteBdCustomerJobByJobIds" parameterType="String">
        delete from bd_customer_job where job_id in (
            <foreach collection="jobIds" item="jobId" separator=",">
                #{jobId}
            </foreach>
        )
    </delete>

    <select id="selectSysDeptByDingId" resultType="com.nnb.system.api.domain.SysDept">
        select sd.dept_id   as deptId,
               sd.parent_id as parentId,
               sd.leader    as leader,
               sd.ancestors as ancestors
        from sys_user su
                 left join sys_dept sd on su.dept_id = sd.dept_id
        where su.ding_user_id = #{dingTalkUserId}
          and su.status = 0
        order by su.user_id desc
        limit 1
    </select>

    <select id="selectSysDeptByDeptId" resultType="com.nnb.system.api.domain.SysDept">
        select sd.dept_id   as deptId,
               sd.parent_id as parentId,
               sd.leader    as leader
        from sys_dept sd
        where sd.dept_id = #{deptId}
    </select>

    <select id="selectDingUserIdByEmail" resultType="com.nnb.system.api.domain.SysUser">
        select user_id as userId, ding_user_id as dingUserId
        from sys_user where email = #{email}
    </select>

    <select id="selectUserByDingUserId" resultType="com.nnb.system.api.domain.SysUser">
        select su.user_id   as userId,
               sd.dept_id   as deptId,
               sd.ancestors as ancestors
        from sys_user su
                 left join sys_dept sd on su.dept_id = sd.dept_id
        where su.ding_user_id = #{dingTalkUserId}
          and su.status = 0
        order by su.user_id desc
        limit 1
    </select>

    <update id="updateExecutionStatusByGroupAndClueId">
        update bd_customer_job
        set execution_status = #{status}
        where job_group = #{group} and clue_id = #{clueId}
    </update>

    <update id="updateStatusByGroupAndClueId">
        update bd_customer_job
        set status = #{status}
        where job_group = #{group} and clue_id = #{clueId}
    </update>

    <select id="selectUnexecuted" resultType="com.nnb.system.api.domain.BdCustomerJob">
        <include refid="selectBdCustomerJobVo"/>
        where status = 0 and job_id &gt;= 14903 and job_id &lt;= 15015
    </select>
</mapper>
