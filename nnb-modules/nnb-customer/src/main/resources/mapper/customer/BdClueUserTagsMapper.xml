<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdClueUserTagsMapper">

    <resultMap type="BdClueUserTags" id="BdClueUserTagsResult">
        <result property="numId"    column="num_id"    />
        <result property="numClueId"    column="num_clue_id"    />
        <result property="numUserTagId"    column="num_user_tag_id"    />
        <result property="userTags"    column="user_tags"    />
        <result property="userTagsIds"    column="user_tags_ids"    />
    </resultMap>
    <sql id="selectBdClueUserTagsVo">
        select num_id, num_clue_id, num_user_tag_id from bd_clue_user_tags
    </sql>

    <select id="selectBdClueUserTagsList" parameterType="BdClueUserTags" resultMap="BdClueUserTagsResult">
        <include refid="selectBdClueUserTagsVo"/>
        <where>
            <if test="numClueId != null "> and num_clue_id = #{numClueId}</if>
            <if test="numUserTagId != null "> and num_user_tag_id = #{numUserTagId}</if>
        </where>
    </select>

    <select id="selectBdClueUserTagsByNumId" parameterType="Long" resultMap="BdClueUserTagsResult">
        <include refid="selectBdClueUserTagsVo"/>
        where num_id = #{numId}
    </select>

    <select id="selectBdClueUserTagsListByClueIdList" resultMap="BdClueUserTagsResult">
        <include refid="selectBdClueUserTagsVo"/>
        where num_clue_id in (
            <foreach collection="bdClueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>

    <insert id="insertBdClueUserTags" parameterType="BdClueUserTags" useGeneratedKeys="true" keyProperty="numId">
        insert into bd_clue_user_tags
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numClueId != null">num_clue_id,</if>
            <if test="numUserTagId != null">num_user_tag_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numClueId != null">#{numClueId},</if>
            <if test="numUserTagId != null">#{numUserTagId},</if>
         </trim>
    </insert>
    <insert id="insertBdClueRoleTags" parameterType="BdClueRoleTage" useGeneratedKeys="true" keyProperty="numId">
        insert into bd_clue_role_tags
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numClueId != null">num_clue_id,</if>
            <if test="numRoleTagId != null">num_role_tag_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numClueId != null">#{numClueId},</if>
            <if test="numRoleTagId != null">#{numRoleTagId},</if>
        </trim>
    </insert>

    <update id="updateBdClueUserTags" parameterType="BdClueUserTags">
        update bd_clue_user_tags
        <trim prefix="SET" suffixOverrides=",">
            <if test="numClueId != null">num_clue_id = #{numClueId},</if>
            <if test="numUserTagId != null">num_user_tag_id = #{numUserTagId},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <delete id="deleteBdClueUserTagsByNumId" parameterType="Long">
        delete from bd_clue_user_tags where num_id = #{numId}
    </delete>

    <delete id="deleteBdClueUserTagsByNumIds" parameterType="String">
        delete from bd_clue_user_tags where num_id in
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>

    <delete id="deleteBdClueUserTagsByNumIdList" parameterType="Long">
        delete from bd_clue_user_tags where num_id in (
            <foreach collection="numIds" item="numId" separator=",">
                #{numId}
            </foreach>
        )
    </delete>

    <delete id="deleteBdClueUserTagsByClueId" parameterType="long" >
        delete from bd_clue_user_tags where num_clue_id in
        <foreach item="clueId" collection="clueIds" open="(" separator="," close=")">
            #{clueId}
        </foreach>
    </delete>

    <delete id="deleteBdClueRoleTagsByClueId"  parameterType="long">
        delete from bd_clue_role_tags where num_clue_id in
        <foreach item="clueId" collection="clueIds" open="(" separator="," close=")">
            #{clueId}
        </foreach>
    </delete>

    <select id="selectBdClueUserTagsListByIds" parameterType="String" resultMap="BdClueUserTagsResult">
        select num_clue_id from bd_clue_user_tags where num_user_tag_id in
        <foreach item="id" collection="ids.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectBdClueUserTagsListByClueIds" parameterType="String" resultMap="BdClueUserTagsResult">
        SELECT
        bcut.num_clue_id,
        group_concat(bcf.vc_name SEPARATOR ',') user_tags,
        group_concat(bcf.num_id SEPARATOR ',') user_tags_ids
        FROM
        bd_clue_user_tags bcut
        LEFT JOIN bd_user_tags bcf ON bcut.num_user_tag_id = bcf.num_id
        AND bcf.status = 1
        WHERE
        bcut.num_clue_id in
        <foreach item="id" collection="ids.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        bcut.num_clue_id
    </select>
    <select id="selectBdClueTagsEs" resultType="com.nnb.customer.domain.es.ESBdClueUserTags">
        select bcut.num_id          as numId,
               bcut.num_user_tag_id as numUserTagId,
               but.vc_name          as tagName,
               bcut.create_time     as createTime,
               bcut.num_clue_id     as numClueId
        from bd_clue_user_tags bcut
        left join bd_user_tags but on bcut.num_user_tag_id = but.num_id
        where bcut.num_clue_id in (
            <foreach collection="bdClueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>
    <select id="selectBdClueUserTagsListByIdList">
        delete from bd_clue_user_tags where num_id in (
            <foreach collection="idList" item="numId" separator=",">
                #{numId}
            </foreach>
        )
    </select>
</mapper>
