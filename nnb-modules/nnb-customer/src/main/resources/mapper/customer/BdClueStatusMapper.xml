<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdClueStatusMapper">

    <resultMap type="BdClueStatus" id="BdClueStatusResult">
        <result property="id"    column="id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="numFollowStatus"    column="num_follow_status"    />
        <result property="numIsPhone"    column="num_is_phone"    />
        <result property="numIsConnect"    column="num_is_connect"    />
        <result property="numIsFriend"    column="num_is_friend"    />
        <result property="numIsCrm"    column="num_is_crm"    />
        <result property="followUserId"    column="follow_user_id"    />
        <result property="datFollowTime"    column="dat_follow_time"    />
        <result property="numKbStatus"    column="num_kb_status"    />
        <result property="kbUserId"    column="kb_user_id"    />
        <result property="datKbTime"    column="dat_kb_time"    />
        <result property="lastFollowUserId"    column="last_follow_user_id"    />
        <result property="datLastFollowTime"    column="dat_last_follow_time"    />
        <result property="lastFollowDeptId"    column="last_follow_dept_id"    />
        <result property="numIssue"    column="num_issue"    />
        <result property="datIssueTime"    column="dat_issue_time"    />
        <result property="issueDeptId"    column="issue_dept_id"    />
        <result property="numDistributionStatus"    column="num_distribution_status"    />
        <result property="distributionUserId"    column="distribution_user_id"    />
        <result property="datDistributionTime"    column="dat_distribution_time"    />
        <result property="numLastAddCrmNode"    column="num_last_add_crm_node"    />
        <result property="numIsOppCall"    column="num_is_opp_call"    />
        <result property="numOrderStatus"    column="num_order_status"    />
        <result property="datMaxCallTime"    column="dat_max_call_time"    />
        <result property="datMaxCallTimeDate"    column="dat_max_call_time_date"    />
    </resultMap>

    <sql id="selectBdClueStatusVo">
        select id, clue_id, num_follow_status, num_is_phone, num_is_connect, num_is_friend, num_is_crm, follow_user_id, dat_follow_time, num_kb_status, kb_user_id, dat_kb_time, last_follow_user_id, dat_last_follow_time,last_follow_dept_id, num_issue, dat_issue_time, issue_dept_id, num_distribution_status, distribution_user_id, dat_distribution_time, num_last_add_crm_node, num_is_opp_call, num_order_status, dat_max_call_time, dat_max_call_time_date from bd_clue_status
    </sql>

    <select id="selectBdClueStatusList" parameterType="BdClueStatus" resultMap="BdClueStatusResult">
        <include refid="selectBdClueStatusVo"/>
        <where>
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="numFollowStatus != null "> and num_follow_status = #{numFollowStatus}</if>
            <if test="numIsPhone != null "> and num_is_phone = #{numIsPhone}</if>
            <if test="numIsConnect != null "> and num_is_connect = #{numIsConnect}</if>
            <if test="numIsFriend != null "> and num_is_friend = #{numIsFriend}</if>
            <if test="numIsCrm != null "> and num_is_crm = #{numIsCrm}</if>
            <if test="followUserId != null "> and follow_user_id = #{followUserId}</if>
            <if test="datFollowTime != null "> and dat_follow_time = #{datFollowTime}</if>
            <if test="numKbStatus != null "> and num_kb_status = #{numKbStatus}</if>
            <if test="kbUserId != null "> and kb_user_id = #{kbUserId}</if>
            <if test="datKbTime != null "> and dat_kb_time = #{datKbTime}</if>
            <if test="lastFollowUserId != null "> and last_follow_user_id = #{lastFollowUserId}</if>
            <if test="datLastFollowTime != null "> and dat_last_follow_time = #{datLastFollowTime}</if>
            <if test="lastFollowDeptId != null "> and last_follow_dept_id = #{lastFollowDeptId}</if>
            <if test="numIssue != null "> and num_issue = #{numIssue}</if>
            <if test="datIssueTime != null "> and dat_issue_time = #{datIssueTime}</if>
            <if test="issueDeptId != null "> and issue_dept_id = #{issueDeptId}</if>
            <if test="numDistributionStatus != null "> and num_distribution_status = #{numDistributionStatus}</if>
            <if test="distributionUserId != null "> and distribution_user_id = #{distributionUserId}</if>
            <if test="datDistributionTime != null "> and dat_distribution_time = #{datDistributionTime}</if>
            <if test="numLastAddCrmNode != null "> and num_last_add_crm_node = #{numLastAddCrmNode}</if>
            <if test="numIsOppCall != null "> and num_is_opp_call = #{numIsOppCall}</if>
            <if test="numOrderStatus != null "> and num_order_status = #{numOrderStatus}</if>
            <if test="datMaxCallTime != null "> and dat_max_call_time = #{datMaxCallTime}</if>
            <if test="datMaxCallTimeDate != null "> and dat_max_call_time_date = #{datMaxCallTimeDate}</if>
        </where>
    </select>

    <select id="selectBdClueStatusById" parameterType="Long" resultMap="BdClueStatusResult">
        <include refid="selectBdClueStatusVo"/>
        where id = #{id}
    </select>

    <insert id="insertBdClueStatus" parameterType="BdClueStatus" useGeneratedKeys="true" keyProperty="id">
        insert into bd_clue_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="numFollowStatus != null">num_follow_status,</if>
            <if test="numIsPhone != null">num_is_phone,</if>
            <if test="numIsConnect != null">num_is_connect,</if>
            <if test="numIsFriend != null">num_is_friend,</if>
            <if test="numIsCrm != null">num_is_crm,</if>
            <if test="followUserId != null">follow_user_id,</if>
            <if test="datFollowTime != null">dat_follow_time,</if>
            <if test="numKbStatus != null">num_kb_status,</if>
            <if test="kbUserId != null">kb_user_id,</if>
            <if test="datKbTime != null">dat_kb_time,</if>
            <if test="lastFollowUserId != null">last_follow_user_id,</if>
            <if test="datLastFollowTime != null">dat_last_follow_time,</if>
            <if test="lastFollowDeptId != null">last_follow_dept_id,</if>
            <if test="numIssue != null">num_issue,</if>
            <if test="datIssueTime != null">dat_issue_time,</if>
            <if test="issueDeptId != null">issue_dept_id,</if>
            <if test="numDistributionStatus != null">num_distribution_status,</if>
            <if test="distributionUserId != null">distribution_user_id,</if>
            <if test="datDistributionTime != null">dat_distribution_time,</if>
            <if test="numLastAddCrmNode != null">num_last_add_crm_node,</if>
            <if test="numIsOppCall != null">num_is_opp_call,</if>
            <if test="numOrderStatus != null">num_order_status,</if>
            <if test="datMaxCallTime != null">dat_max_call_time,</if>
            <if test="datMaxCallTimeDate != null">dat_max_call_time_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="numFollowStatus != null">#{numFollowStatus},</if>
            <if test="numIsPhone != null">#{numIsPhone},</if>
            <if test="numIsConnect != null">#{numIsConnect},</if>
            <if test="numIsFriend != null">#{numIsFriend},</if>
            <if test="numIsCrm != null">#{numIsCrm},</if>
            <if test="followUserId != null">#{followUserId},</if>
            <if test="datFollowTime != null">#{datFollowTime},</if>
            <if test="numKbStatus != null">#{numKbStatus},</if>
            <if test="kbUserId != null">#{kbUserId},</if>
            <if test="datKbTime != null">#{datKbTime},</if>
            <if test="lastFollowUserId != null">#{lastFollowUserId},</if>
            <if test="datLastFollowTime != null">#{datLastFollowTime},</if>
            <if test="lastFollowDeptId != null">#{lastFollowDeptId},</if>
            <if test="numIssue != null">#{numIssue},</if>
            <if test="datIssueTime != null">#{datIssueTime},</if>
            <if test="issueDeptId != null">#{issueDeptId},</if>
            <if test="numDistributionStatus != null">#{numDistributionStatus},</if>
            <if test="distributionUserId != null">#{distributionUserId},</if>
            <if test="datDistributionTime != null">#{datDistributionTime},</if>
            <if test="numLastAddCrmNode != null">#{numLastAddCrmNode},</if>
            <if test="numIsOppCall != null">#{numIsOppCall},</if>
            <if test="numOrderStatus != null">#{numOrderStatus},</if>
            <if test="datMaxCallTime != null">#{datMaxCallTime},</if>
            <if test="datMaxCallTimeDate != null">#{datMaxCallTimeDate},</if>
         </trim>
    </insert>
    <insert id="replaceBdClueStatus" parameterType="BdClueStatus">
        replace into bd_clue_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="numFollowStatus != null">num_follow_status,</if>
            <if test="numIsPhone != null">num_is_phone,</if>
            <if test="numIsConnect != null">num_is_connect,</if>
            <if test="numIsFriend != null">num_is_friend,</if>
            <if test="numIsCrm != null">num_is_crm,</if>
            <if test="followUserId != null">follow_user_id,</if>
            <if test="datFollowTime != null">dat_follow_time,</if>
            <if test="numKbStatus != null">num_kb_status,</if>
            <if test="kbUserId != null">kb_user_id,</if>
            <if test="datKbTime != null">dat_kb_time,</if>
            <if test="lastFollowUserId != null">last_follow_user_id,</if>
            <if test="datLastFollowTime != null">dat_last_follow_time,</if>
            <if test="lastFollowDeptId != null">last_follow_dept_id,</if>
            <if test="numIssue != null">num_issue,</if>
            <if test="datIssueTime != null">dat_issue_time,</if>
            <if test="issueDeptId != null">issue_dept_id,</if>
            <if test="numDistributionStatus != null">num_distribution_status,</if>
            <if test="distributionUserId != null">distribution_user_id,</if>
            <if test="datDistributionTime != null">dat_distribution_time,</if>
            <if test="numLastAddCrmNode != null">num_last_add_crm_node,</if>
            <if test="numIsOppCall != null">num_is_opp_call,</if>
            <if test="numOrderStatus != null">num_order_status,</if>
            <if test="datMaxCallTime != null">dat_max_call_time,</if>
            <if test="datMaxCallTimeDate != null">dat_max_call_time_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="numFollowStatus != null">#{numFollowStatus},</if>
            <if test="numIsPhone != null">#{numIsPhone},</if>
            <if test="numIsConnect != null">#{numIsConnect},</if>
            <if test="numIsFriend != null">#{numIsFriend},</if>
            <if test="numIsCrm != null">#{numIsCrm},</if>
            <if test="followUserId != null">#{followUserId},</if>
            <if test="datFollowTime != null">#{datFollowTime},</if>
            <if test="numKbStatus != null">#{numKbStatus},</if>
            <if test="kbUserId != null">#{kbUserId},</if>
            <if test="datKbTime != null">#{datKbTime},</if>
            <if test="lastFollowUserId != null">#{lastFollowUserId},</if>
            <if test="datLastFollowTime != null">#{datLastFollowTime},</if>
            <if test="lastFollowDeptId != null">#{lastFollowDeptId},</if>
            <if test="numIssue != null">#{numIssue},</if>
            <if test="datIssueTime != null">#{datIssueTime},</if>
            <if test="issueDeptId != null">#{issueDeptId},</if>
            <if test="numDistributionStatus != null">#{numDistributionStatus},</if>
            <if test="distributionUserId != null">#{distributionUserId},</if>
            <if test="datDistributionTime != null">#{datDistributionTime},</if>
            <if test="numLastAddCrmNode != null">#{numLastAddCrmNode},</if>
            <if test="numIsOppCall != null">#{numIsOppCall},</if>
            <if test="numOrderStatus != null">#{numOrderStatus},</if>
            <if test="datMaxCallTime != null">#{datMaxCallTime},</if>
            <if test="datMaxCallTimeDate != null">#{datMaxCallTimeDate},</if>
        </trim>
    </insert>

    <update id="updateBdClueStatus" parameterType="BdClueStatus">
        update bd_clue_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="numFollowStatus != null">num_follow_status = #{numFollowStatus},</if>
            <if test="numIsPhone != null">num_is_phone = #{numIsPhone},</if>
            <if test="numIsConnect != null">num_is_connect = #{numIsConnect},</if>
            <if test="numIsFriend != null">num_is_friend = #{numIsFriend},</if>
            <if test="numIsCrm != null">num_is_crm = #{numIsCrm},</if>
            <if test="followUserId != null">follow_user_id = #{followUserId},</if>
            <if test="datFollowTime != null">dat_follow_time = #{datFollowTime},</if>
            <if test="numKbStatus != null">num_kb_status = #{numKbStatus},</if>
            <if test="kbUserId != null">kb_user_id = #{kbUserId},</if>
            <if test="datKbTime != null">dat_kb_time = #{datKbTime},</if>
            <if test="lastFollowUserId != null">last_follow_user_id = #{lastFollowUserId},</if>
            <if test="datLastFollowTime != null">dat_last_follow_time = #{datLastFollowTime},</if>
            <if test="lastFollowDeptId != null">last_follow_dept_id = #{lastFollowDeptId},</if>
            <if test="numIssue != null">num_issue = #{numIssue},</if>
            <if test="datIssueTime != null">dat_issue_time = #{datIssueTime},</if>
            <if test="issueDeptId != null">issue_dept_id = #{issueDeptId},</if>
            <if test="numDistributionStatus != null">num_distribution_status = #{numDistributionStatus},</if>
            <if test="distributionUserId != null">distribution_user_id = #{distributionUserId},</if>
            <if test="datDistributionTime != null">dat_distribution_time = #{datDistributionTime},</if>
            <if test="numLastAddCrmNode != null">num_last_add_crm_node = #{numLastAddCrmNode},</if>
            <if test="numIsOppCall != null">num_is_opp_call = #{numIsOppCall},</if>
            <if test="numOrderStatus != null">num_order_status = #{numOrderStatus},</if>
            <if test="datMaxCallTime != null">dat_max_call_time = #{datMaxCallTime},</if>
            <if test="datMaxCallTimeDate != null">dat_max_call_time_date = #{datMaxCallTimeDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdClueStatusById" parameterType="Long">
        delete from bd_clue_status where id = #{id}
    </delete>

    <delete id="deleteBdClueStatusByIds" parameterType="String">
        delete from bd_clue_status where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectInfoByClueId" parameterType="Long" resultMap="BdClueStatusResult">
        select * from bd_clue_status where clue_id = #{clueId}
    </select>

    <select id="selectInfoByClueIdList" parameterType="Long" resultMap="BdClueStatusResult">
        select id, clue_id, num_follow_status, follow_user_id, dat_follow_time, num_kb_status, kb_user_id, dat_kb_time, last_follow_user_id, dat_last_follow_time, num_issue, dat_issue_time, issue_dept_id, num_distribution_status, distribution_user_id, dat_distribution_time, num_last_add_crm_node, launch_follow_question, dat_max_call_time_date, num_is_opp_call, dat_max_call_time, num_is_phone, num_is_connect, num_is_friend, num_is_crm, last_follow_dept_id, num_order_status
        from bd_clue_status where clue_id in (
            <foreach collection="clueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>

    <update id="updateBdClueStatusByClueId" parameterType="BdClueStatus">
        update bd_clue_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="numFollowStatus != null">num_follow_status = #{numFollowStatus},</if>
            <if test="numIsPhone != null">num_is_phone = #{numIsPhone},</if>
            <if test="numIsConnect != null">num_is_connect = #{numIsConnect},</if>
            <if test="numIsFriend != null">num_is_friend = #{numIsFriend},</if>
            <if test="numIsCrm != null">num_is_crm = #{numIsCrm},</if>
            <if test="followUserId != null">follow_user_id = #{followUserId},</if>
            <if test="datFollowTime != null">dat_follow_time = #{datFollowTime},</if>
            <if test="numKbStatus != null">num_kb_status = #{numKbStatus},</if>
            <if test="kbUserId != null">kb_user_id = #{kbUserId},</if>
            <if test="datKbTime != null">dat_kb_time = #{datKbTime},</if>
            <if test="lastFollowUserId != null">last_follow_user_id = #{lastFollowUserId},</if>
            <if test="datLastFollowTime != null">dat_last_follow_time = #{datLastFollowTime},</if>
            <if test="lastFollowDeptId != null">last_follow_dept_id = #{lastFollowDeptId},</if>
            <if test="numIssue != null">num_issue = #{numIssue},</if>
            <if test="datIssueTime != null">dat_issue_time = #{datIssueTime},</if>
            <if test="issueDeptId != null">issue_dept_id = #{issueDeptId},</if>
            <if test="numDistributionStatus != null">num_distribution_status = #{numDistributionStatus},</if>
            <if test="distributionUserId != null">distribution_user_id = #{distributionUserId},</if>
            <if test="datDistributionTime != null">dat_distribution_time = #{datDistributionTime},</if>
            <if test="numLastAddCrmNode != null">num_last_add_crm_node = #{numLastAddCrmNode},</if>
            <if test="numIsOppCall != null">num_is_opp_call = #{numIsOppCall},</if>
            <if test="numOrderStatus != null">num_order_status = #{numOrderStatus},</if>
            <if test="datMaxCallTime != null">dat_max_call_time = #{datMaxCallTime},</if>
            <if test="datMaxCallTimeDate != null">dat_max_call_time_date = #{datMaxCallTimeDate},</if>
        </trim>
        where clue_id = #{clueId}
    </update>

    <resultMap type="com.nnb.customer.domain.es.ESBdClueStatus" id="BdClueStatusResultEs">
        <result property="clueId"    column="clue_id"    />
        <result property="bdClueStatusNumFollowStatus"    column="num_follow_status"    />
        <result property="bdClueStatusFollowUserId"    column="follow_user_id"    />
        <result property="bdClueStatusDatFollowTime"    column="dat_follow_time"    />
        <result property="bdClueStatusNumKbStatus"    column="num_kb_status"    />
        <result property="bdClueStatusKbUserId"    column="kb_user_id"    />
        <result property="bdClueStatusDatKbTime"    column="dat_kb_time"    />
        <result property="bdClueStatusLastFollowUserId"    column="last_follow_user_id"    />
        <result property="bdClueStatusDatLastFollowTime"    column="dat_last_follow_time"    />
        <result property="bdClueStatusNumIssue"    column="num_issue"    />
        <result property="bdClueStatusDatIssueTime"    column="dat_issue_time"    />
        <result property="bdClueStatusIssueDeptId"    column="issue_dept_id"    />
        <result property="bdClueStatusNumDistributionStatus"    column="num_distribution_status"    />
        <result property="bdClueStatusDistributionUserId"    column="distribution_user_id"    />
        <result property="bdClueStatusDatDistributionTime"    column="dat_distribution_time"    />
        <result property="bdClueStatusNumLastAddCrmNode"    column="num_last_add_crm_node"    />
        <result property="bdClueStatusDatMaxCallTimeDate"    column="dat_max_call_time_date"    />
        <result property="bdClueStatusNumIsOppCall"    column="num_is_opp_call"    />
        <result property="bdClueStatusDatMaxCallTime"    column="dat_max_call_time"    />
        <result property="bdClueStatusNumIsPhone"    column="num_is_phone"    />
        <result property="bdClueStatusNumIsConnect"    column="num_is_connect"    />
        <result property="bdClueStatusNumIsFriend"    column="num_is_friend"    />
        <result property="bdClueStatusNumIsCrm"    column="num_is_crm"    />
        <result property="bdClueStatusLastFollowDeptId"    column="last_follow_dept_id"    />
        <result property="bdClueStatusNumOrderStatus"    column="num_order_status"    />
    </resultMap>

    <sql id="selectBdClueStatusVoEs">
        select clue_id, num_follow_status, follow_user_id, dat_follow_time, num_kb_status, kb_user_id, dat_kb_time, last_follow_user_id, dat_last_follow_time, num_issue, dat_issue_time, issue_dept_id, num_distribution_status, distribution_user_id, dat_distribution_time, num_last_add_crm_node, launch_follow_question, dat_max_call_time_date, num_is_opp_call, dat_max_call_time, num_is_phone, num_is_connect, num_is_friend, num_is_crm, last_follow_dept_id, num_order_status from bd_clue_status
    </sql>

    <select id="selectByClueIdListEs" resultMap="BdClueStatusResultEs">
        <include refid="selectBdClueStatusVoEs"/>
        where clue_id in (
            <foreach collection="bdClueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>

</mapper>
