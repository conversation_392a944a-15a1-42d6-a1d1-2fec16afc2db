<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdNicheGuestSrcsMapper">
    
    <resultMap type="BdNicheGuestSrcs" id="BdNicheGuestSrcsResult">
        <result property="numNicheId"    column="num_niche_id"    />
        <result property="numGuestSrcsId"    column="num_guest_srcs_id"    />
    </resultMap>

    <sql id="selectBdNicheGuestSrcsVo">
        select num_niche_id, num_guest_srcs_id from bd_niche_guest_srcs
    </sql>

    <select id="selectBdNicheGuestSrcsList" parameterType="BdNicheGuestSrcs" resultMap="BdNicheGuestSrcsResult">
        <include refid="selectBdNicheGuestSrcsVo"/>
        <where>  
            <if test="numNicheId != null "> and num_niche_id = #{numNicheId}</if>
            <if test="numGuestSrcsId != null "> and num_guest_srcs_id = #{numGuestSrcsId}</if>
        </where>
    </select>
    
    <select id="selectBdNicheGuestSrcsByNumNicheId" parameterType="Long" resultMap="BdNicheGuestSrcsResult">
        <include refid="selectBdNicheGuestSrcsVo"/>
        where num_niche_id = #{numNicheId}
    </select>
    <select id="countNicheByNumGuestSrcs"  parameterType="Long" resultType="java.lang.Integer">
        select count(1)
        from bd_niche_flow_conf nfc
                 left join bd_niche_guest_srcs ngs on nfc.num_id = ngs.num_niche_id
        where ngs.num_guest_srcs_id in
        <foreach item="guestSrcsId" collection="guestSrcsIds" open="(" separator="," close=")">
            #{guestSrcsId}
        </foreach>
        <if test="numId != null "> and nfc.num_id != #{numId}</if>
    </select>

    <insert id="insertBdNicheGuestSrcs" parameterType="BdNicheGuestSrcs">
        insert into bd_niche_guest_srcs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numNicheId != null">num_niche_id,</if>
            <if test="numGuestSrcsId != null">num_guest_srcs_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numNicheId != null">#{numNicheId},</if>
            <if test="numGuestSrcsId != null">#{numGuestSrcsId},</if>
         </trim>
    </insert>

    <update id="updateBdNicheGuestSrcs" parameterType="BdNicheGuestSrcs">
        update bd_niche_guest_srcs
        <trim prefix="SET" suffixOverrides=",">
            <if test="numGuestSrcsId != null">num_guest_srcs_id = #{numGuestSrcsId},</if>
        </trim>
        where num_niche_id = #{numNicheId}
    </update>

    <delete id="deleteBdNicheGuestSrcsByNumNicheId" parameterType="Long">
        delete from bd_niche_guest_srcs where num_niche_id = #{numNicheId}
    </delete>

    <delete id="deleteBdNicheGuestSrcsByNumNicheIds" parameterType="String">
        delete from bd_niche_guest_srcs where num_niche_id in 
        <foreach item="numNicheId" collection="array" open="(" separator="," close=")">
            #{numNicheId}
        </foreach>
    </delete>
</mapper>