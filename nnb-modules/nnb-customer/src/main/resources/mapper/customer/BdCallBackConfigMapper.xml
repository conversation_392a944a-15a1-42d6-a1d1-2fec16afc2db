<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdCallBackConfigMapper">

    <resultMap type="BdCallBackConfig" id="BdCallBackConfigResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="phone"    column="phone"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="city"    column="city"    />
        <result property="ccid"    column="ccid"    />
    </resultMap>

    <sql id="selectBdCallBackConfigVo">
        select id, user_id, phone, status, created_by, updated_by, created_at, updated_at from bd_call_back_config
    </sql>

    <select id="selectBdCallBackConfigList" parameterType="BdCallBackConfig" resultMap="BdCallBackConfigResult">
        <include refid="selectBdCallBackConfigVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="ccid != null  and ccid != ''"> and ccid = #{ccid}</if>
        </where>
    </select>

    <select id="selectBdCallBackConfigById" parameterType="Long" resultMap="BdCallBackConfigResult">
        <include refid="selectBdCallBackConfigVo"/>
        where id = #{id}
    </select>
    <select id="selectBdCallBackConfigVoList"  parameterType="BdCallBackConfig"
            resultType="com.nnb.customer.model.BdCallBackConfigVo">
         select bc.id,
                bc.user_id,
                bc.phone,
                bc.status,
                bc.created_by,
                bc.updated_by,
                bc.created_at,
                bc.updated_at,
                su.nick_name userName,
                sd.dept_name,
                cdr.title city,
                bc.ccid,
                bc.city cityId
        from bd_call_back_config bc
                left join sys_user su on bc.user_id = su.user_id
                left join sys_dept sd on su.dept_id = sd.dept_id
                left join com_dict_region cdr on bc.city = cdr.id
        <where>
            <if test="userId != null "> and bc.user_id = #{userId}</if>
            <if test="phone != null  and phone != ''"> and bc.phone = #{phone}</if>
            <if test="status != null  and status != ''"> and bc.status = #{status}</if>
            <if test="city != null  and city != ''"> and bc.city = #{city}</if>
            <if test="ccid != null  and ccid != ''"> and bc.ccid like concat('%', #{ccid}, '%')</if>
        </where>

    </select>

    <insert id="insertBdCallBackConfig" parameterType="BdCallBackConfig" useGeneratedKeys="true" keyProperty="id">
        insert into bd_call_back_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="city != null">city,</if>
            <if test="ccid != null">ccid,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="city != null">#{city},</if>
            <if test="ccid != null">#{ccid},</if>
        </trim>
    </insert>

    <update id="updateBdCallBackConfig" parameterType="BdCallBackConfig">
        update bd_call_back_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="cityId != null">city = #{cityId},</if>
            <if test="ccid != null">ccid = #{ccid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdCallBackConfigById" parameterType="Long">
        delete from bd_call_back_config where id = #{id}
    </delete>

    <delete id="deleteBdCallBackConfigByIds" parameterType="String">
        delete from bd_call_back_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>