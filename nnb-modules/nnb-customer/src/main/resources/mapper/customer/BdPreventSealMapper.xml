<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdPreventSealMapper">
    
    <resultMap type="BdPreventSeal" id="BdPreventSealResult">
        <result property="numId"    column="num_id"    />
        <result property="vcPhone"    column="vc_phone"    />
        <result property="datUnsealDate"    column="dat_unseal_date"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="numUpdatedBy"    column="num_updated_by"    />
        <result property="datCreatedAt"    column="dat_created_at"    />
        <result property="datUpdatedAt"    column="dat_updated_at"    />
    </resultMap>

    <resultMap type="BdOutbound" id="BdOutboundResult">
        <result property="id" column="id"/>
        <result property="numPhone" column="num_phone"/>
        <result property="numIsAnswer" column="num_is_answer"/>
        <result property="numIsCreate" column="num_is_create"/>
        <result property="numContentTime" column="num_content_time"/>
        <result property="vcContent" column="vc_content"/>
        <result property="vcCallUuid" column="vc_call_uuid"/>
        <result property="vcMd5" column="vc_md5"/>
        <result property="numNode" column="num_node"/>
        <result property="numContactsId" column="num_contacts_id"/>
        <result property="clueId" column="clue_id"/>
        <result property="numCustomerId" column="num_customer_id"/>
        <result property="vcRemark" column="vc_remark"/>
        <result property="numCallType" column="num_call_type"/>
        <result property="numType" column="num_type"/>
        <result property="vcOutsideMsg" column="vc_outside_msg"/>
        <result property="numDeptId" column="num_dept_id"/>
        <result property="numCreatedBy" column="num_created_by"/>
        <result property="numUpdatedBy" column="num_updated_by"/>
        <result property="datCreatedAt" column="dat_created_at"/>
        <result property="datUpdatedAt" column="dat_updated_at"/>
    </resultMap>

    <sql id="selectBdOutboundVo">
        select id,
               num_phone,
               num_is_answer,
               num_is_create,
               num_content_time,
               vc_content,
               vc_call_uuid,
               vc_md5,
               num_node,
               num_contacts_id,
               clue_id,
               num_customer_id,
               vc_remark,
               num_call_type,
               num_type,
               vc_outside_msg,
               num_dept_id,
               num_created_by,
               num_updated_by,
               dat_created_at,
               dat_updated_at
        from bd_outbound
    </sql>

    <sql id="selectBdPreventSealVo">
        select num_id, vc_phone, dat_unseal_date, num_created_by, num_updated_by, dat_created_at, dat_updated_at from bd_prevent_seal
    </sql>

    <select id="selectBdPreventSealList" parameterType="BdPreventSeal" resultMap="BdPreventSealResult">
        <include refid="selectBdPreventSealVo"/>
        <where>  
            <if test="vcPhone != null  and vcPhone != ''"> and vc_phone = #{vcPhone}</if>
            <if test="datUnsealDate != null "> and dat_unseal_date = #{datUnsealDate}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null "> and num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAt != null "> and dat_created_at = #{datCreatedAt}</if>
            <if test="datUpdatedAt != null "> and dat_updated_at = #{datUpdatedAt}</if>
        </where>
    </select>
    
    <select id="selectBdPreventSealByNumId" parameterType="Long" resultMap="BdPreventSealResult">
        <include refid="selectBdPreventSealVo"/>
        where num_id = #{numId}
    </select>
    <select id="selectSealByphone" resultType="com.nnb.customer.domain.BdPreventSeal">
        select num_id, vc_phone, dat_unseal_date, num_created_by, num_updated_by, dat_created_at, dat_updated_at from bd_prevent_seal
        where vc_phone in
        <foreach item="vcPhone" collection="vcPhones" open="(" separator="," close=")">
            #{vcPhone}
        </foreach>
    </select>
    <select id="checkBdPreventSealByPhone" resultType="java.util.Date">
        select dat_unseal_date
        from bd_prevent_seal
        where vc_phone = #{phone}
    </select>

    <select id="selectBdOutboundList" resultMap="BdOutboundResult">
        <include refid="selectBdOutboundVo"/>
        where num_phone = #{numPhone}
    </select>
    <select id="selectBdPreventSealByPhone" resultMap="BdPreventSealResult">
        <include refid="selectBdPreventSealVo"/>
        where vc_phone = #{phone}
    </select>

    <insert id="insertBdPreventSeal" parameterType="BdPreventSeal" useGeneratedKeys="true" keyProperty="numId">
        insert into bd_prevent_seal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vcPhone != null">vc_phone,</if>
            <if test="datUnsealDate != null">dat_unseal_date,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="numUpdatedBy != null">num_updated_by,</if>
            <if test="datCreatedAt != null">dat_created_at,</if>
            <if test="datUpdatedAt != null">dat_updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vcPhone != null">#{vcPhone},</if>
            <if test="datUnsealDate != null">#{datUnsealDate},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="numUpdatedBy != null">#{numUpdatedBy},</if>
            <if test="datCreatedAt != null">#{datCreatedAt},</if>
            <if test="datUpdatedAt != null">#{datUpdatedAt},</if>
         </trim>
    </insert>

    <update id="updateBdPreventSeal" parameterType="BdPreventSeal">
        update bd_prevent_seal
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcPhone != null">vc_phone = #{vcPhone},</if>
            <if test="datUnsealDate != null">dat_unseal_date = #{datUnsealDate},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datCreatedAt != null">dat_created_at = #{datCreatedAt},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <update id="updateBdPreventSealByPhone" parameterType="BdPreventSeal">
        update bd_prevent_seal
        <trim prefix="SET" suffixOverrides=",">
            <if test="datUnsealDate != null">dat_unseal_date = #{datUnsealDate},</if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
        </trim>
        where vc_phone = #{vcPhone}
    </update>

    <delete id="deleteBdPreventSealByNumId" parameterType="Long">
        delete from bd_prevent_seal where num_id = #{numId}
    </delete>

    <delete id="deleteBdPreventSealByNumIds" parameterType="String">
        delete from bd_prevent_seal where num_id in 
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>
</mapper>