<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdNeedProductMapper">

    <resultMap type="BdNeedProduct" id="BdNeedProductResult">
        <result property="id"    column="id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="configurationsId"    column="configurations_id"    />
        <result property="needProduct"    column="need_product"    />
    </resultMap>

    <sql id="selectBdNeedProductVo">
        select id, clue_id, configurations_id from bd_need_product
    </sql>

    <select id="selectBdNeedProductList" parameterType="BdNeedProduct" resultMap="BdNeedProductResult">
        <include refid="selectBdNeedProductVo"/>
        <where>
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="configurationsId != null "> and configurations_id = #{configurationsId}</if>
        </where>
    </select>

    <select id="selectBdNeedProductById" parameterType="Long" resultMap="BdNeedProductResult">
        <include refid="selectBdNeedProductVo"/>
        where id = #{id}
    </select>

    <select id="selectBdNeedProductByConfigurationsId" parameterType="Long" resultMap="BdNeedProductResult">
        <include refid="selectBdNeedProductVo"/>
        where configurations_id = #{configurationsId}
    </select>

    <insert id="insertBdNeedProduct" parameterType="BdNeedProduct" useGeneratedKeys="true" keyProperty="id">
        insert into bd_need_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="configurationsId != null">configurations_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="configurationsId != null">#{configurationsId},</if>
         </trim>
    </insert>

    <update id="updateBdNeedProduct" parameterType="BdNeedProduct">
        update bd_need_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="configurationsId != null">configurations_id = #{configurationsId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdNeedProductById" parameterType="Long">
        delete from bd_need_product where id = #{id}
    </delete>

    <delete id="deleteBdNeedProductByIds" parameterType="String">
        delete from bd_need_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBdNeedProductByClueId"  parameterType="Long">
        delete from bd_need_product where clue_id = #{clueId}
    </delete>

    <select id="getProductByClueId" resultType="com.nnb.customer.domain.vo.clue.BdNeedProductVO">
        SELECT
            bnp.clue_id,
            group_concat( bcf.vc_name SEPARATOR ',' ) need_product
        FROM
            bd_need_product bnp
        LEFT JOIN bd_configurations bcf ON bnp.configurations_id = bcf.id
        AND bcf.num_op_type = 18
        where bnp.clue_id = #{clueId}
    </select>

    <select id="getProductByClueIdList" resultType="com.nnb.customer.domain.vo.clue.BdNeedProductVO">
        SELECT
            bnp.clue_id,
            group_concat( bcf.vc_name SEPARATOR ',' ) need_product
        FROM
            bd_need_product bnp
        LEFT JOIN bd_configurations bcf ON bnp.configurations_id = bcf.id
        AND bcf.num_op_type = 18
        where bnp.clue_id in (
        <foreach collection="clueIdList" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectBdNeedProductListGroupByClueIds" parameterType="String" resultMap="BdNeedProductResult">
        SELECT
        bnp.clue_id,
        group_concat(bcf.vc_name SEPARATOR ',') need_product
        FROM
        bd_need_product bnp
        LEFT JOIN bd_configurations bcf ON bnp.configurations_id = bcf.id
        AND bcf.num_op_type = 18
        WHERE
        clue_id in
        <foreach item="id" collection="ids.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        bnp.clue_id
    </select>
    <select id="selectByClueIdListEs" resultType="com.nnb.customer.domain.es.ESBdNeedProduct">
        select clue_id           as clueId,
               configurations_id as configurationsId
        from bd_need_product
        where clue_id in (
            <foreach collection="bdClueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>
</mapper>
