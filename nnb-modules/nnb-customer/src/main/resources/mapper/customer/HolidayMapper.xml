<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.HolidayMapper">
    
    <resultMap type="Holiday" id="HolidayResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="day"    column="day"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="numUpdatedBy"    column="num_updated_by"    />
        <result property="datCreatedAt"    column="dat_created_at"    />
        <result property="datUpdatedAt"    column="dat_updated_at"    />
    </resultMap>

    <sql id="selectHolidayVo">
        select id, type, day, num_created_by, num_updated_by, dat_created_at, dat_updated_at from holiday
    </sql>

    <select id="selectHolidayList" parameterType="Holiday" resultMap="HolidayResult">
        <include refid="selectHolidayVo"/>
        <where>  
            <if test="type != null "> and type = #{type}</if>
            <if test="day != null "> and date = #{day}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null "> and num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAt != null "> and dat_created_at = #{datCreatedAt}</if>
            <if test="datUpdatedAt != null "> and dat_updated_at = #{datUpdatedAt}</if>
        </where>
    </select>
    
    <select id="selectHolidayById" parameterType="Long" resultMap="HolidayResult">
        <include refid="selectHolidayVo"/>
        where id = #{id}
    </select>

    <select id="selectHolidayByDay" resultType="java.util.Date">
        select max(h.day)
        from (
                 select id, type, day, created_at, updated_at, created_by, updated_by
                 from holiday
                 where type = 3
                   and day > date_format(#{beginDate}, '%Y-%m-%d')
                 limit #{day}
             ) h
    </select>

    <insert id="insertHoliday" parameterType="Holiday" useGeneratedKeys="true" keyProperty="id">
        insert into holiday
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="day != null">day,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="numUpdatedBy != null">num_updated_by,</if>
            <if test="datCreatedAt != null">dat_created_at,</if>
            <if test="datUpdatedAt != null">dat_updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="day != null">#{day},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="numUpdatedBy != null">#{numUpdatedBy},</if>
            <if test="datCreatedAt != null">#{datCreatedAt},</if>
            <if test="datUpdatedAt != null">#{datUpdatedAt},</if>
         </trim>
    </insert>

    <update id="updateHoliday" parameterType="Holiday">
        update holiday
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="day != null">day = #{day},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datCreatedAt != null">dat_created_at = #{datCreatedAt},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHolidayById" parameterType="Long">
        delete from holiday where id = #{id}
    </delete>

    <delete id="deleteHolidayByIds" parameterType="String">
        delete from holiday where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCountByDay" resultType="Long">
        SELECT count( 1 ) FROM holiday WHERE type = 3 AND DAY BETWEEN #{begin} AND #{end}
    </select>
</mapper>