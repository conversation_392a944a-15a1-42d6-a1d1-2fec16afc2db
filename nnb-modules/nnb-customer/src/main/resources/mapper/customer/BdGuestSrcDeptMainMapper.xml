<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdGuestSrcDeptMainMapper">
    
    <resultMap type="BdGuestSrcDeptMain" id="BdGuestSrcDeptMainResult">
        <result property="id"    column="id"    />
        <result property="numStatus"    column="num_status"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="numUpdatedBy"    column="num_updated_by"    />
        <result property="datCreatedAt"    column="dat_created_at"    />
        <result property="datUpdatedAt"    column="dat_updated_at"    />
    </resultMap>

    <sql id="selectBdGuestSrcDeptMainVo">
        select id, num_status, num_created_by, num_updated_by, dat_created_at, dat_updated_at from bd_guest_src_dept_main
    </sql>

    <select id="selectBdGuestSrcDeptMainList" parameterType="BdGuestSrcDeptMain" resultMap="BdGuestSrcDeptMainResult">
        <include refid="selectBdGuestSrcDeptMainVo"/>
        <where>
            <if test="numStatus != null "> and num_status = #{numStatus}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="numUpdatedBy != null "> and num_updated_by = #{numUpdatedBy}</if>
            <if test="datCreatedAt != null "> and dat_created_at = #{datCreatedAt}</if>
            <if test="datUpdatedAt != null "> and dat_updated_at = #{datUpdatedAt}</if>
        </where>
    </select>
    
    <select id="selectBdGuestSrcDeptMainById" parameterType="Long" resultMap="BdGuestSrcDeptMainResult">
        <include refid="selectBdGuestSrcDeptMainVo"/>
        where id = #{id}
    </select>

    <select id="selectBdGuestSrcDeptMainListVo" resultType="com.nnb.customer.model.BdGuestSrcDeptMainListVo"  parameterType="com.nnb.customer.domain.BdGuestSrcDeptVice">
        select main.id                             id,
               group_concat(distinct sd.dept_name) deptName,
               group_concat(distinct ps.vc_name)   pGuestSrcsName,
               group_concat(distinct s.vc_name)    guestSrcsName,
               main.num_status                     numStatus,
               main.dat_created_at                 datCreatedAt
        from bd_guest_src_dept_main main
                 left join bd_guest_src_dept_vice vice on main.id = vice.main_id
                 left join bd_guest_srcs s on vice.guest_srcs_id = s.id
                 left join bd_guest_srcs ps on s.p_id = ps.id
                 left join sys_dept sd on vice.dept_id = sd.dept_id
        <where>
            <if test="guestSrcsId != null "> and vice.guest_srcs_id =#{guestSrcsId}</if>
            <if test="deptId != null "> and vice.dept_id = #{deptId}</if>
        </where>
        group by main.id

    </select>

    <insert id="insertBdGuestSrcDeptMain" parameterType="BdGuestSrcDeptMain" useGeneratedKeys="true" keyProperty="id">
        insert into bd_guest_src_dept_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numStatus != null">num_status,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="numUpdatedBy != null">num_updated_by,</if>
            <if test="datCreatedAt != null">dat_created_at,</if>
            <if test="datUpdatedAt != null">dat_updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numStatus != null">#{numStatus},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="numUpdatedBy != null">#{numUpdatedBy},</if>
            <if test="datCreatedAt != null">#{datCreatedAt},</if>
            <if test="datUpdatedAt != null">#{datUpdatedAt},</if>
         </trim>
    </insert>

    <update id="updateBdGuestSrcDeptMain" parameterType="BdGuestSrcDeptMain">
        update bd_guest_src_dept_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datCreatedAt != null">dat_created_at = #{datCreatedAt},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdGuestSrcDeptMainById" parameterType="Long">
        delete from bd_guest_src_dept_main where id = #{id}
    </delete>

    <delete id="deleteBdGuestSrcDeptMainByIds" parameterType="String">
        delete from bd_guest_src_dept_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>