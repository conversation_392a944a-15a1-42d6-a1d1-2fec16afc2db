<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdCustomerMapper">
    
    <resultMap type="BdCustomer" id="BdCustomerResult">
        <result property="id"    column="id"    />
        <result property="numStatus"    column="num_status"    />
        <result property="numCreateUserid"    column="num_create_userid"    />
        <result property="datCreateTime"    column="dat_create_time"    />
        <result property="numLastUpdUserid"    column="num_last_upd_userid"    />
        <result property="datLastUpd"    column="dat_last_upd"    />
    </resultMap>

    <sql id="selectBdCustomerVo">
        select id, num_status, num_create_userid, dat_create_time, num_last_upd_userid, dat_last_upd from bd_customer
    </sql>

    <select id="selectBdCustomerList" parameterType="BdCustomer" resultMap="BdCustomerResult">
        <include refid="selectBdCustomerVo"/>
        <where>  
            <if test="numStatus != null "> and num_status = #{numStatus}</if>
            <if test="numCreateUserid != null "> and num_create_userid = #{numCreateUserid}</if>
            <if test="datCreateTime != null "> and dat_create_time = #{datCreateTime}</if>
            <if test="numLastUpdUserid != null "> and num_last_upd_userid = #{numLastUpdUserid}</if>
            <if test="datLastUpd != null "> and dat_last_upd = #{datLastUpd}</if>
        </where>
    </select>
    
    <select id="selectBdCustomerById" parameterType="Long" resultMap="BdCustomerResult">
        <include refid="selectBdCustomerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBdCustomer" parameterType="BdCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into bd_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numStatus != null">num_status,</if>
            <if test="numCreateUserid != null">num_create_userid,</if>
            <if test="datCreateTime != null">dat_create_time,</if>
            <if test="numLastUpdUserid != null">num_last_upd_userid,</if>
            <if test="datLastUpd != null">dat_last_upd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numStatus != null">#{numStatus},</if>
            <if test="numCreateUserid != null">#{numCreateUserid},</if>
            <if test="datCreateTime != null">#{datCreateTime},</if>
            <if test="numLastUpdUserid != null">#{numLastUpdUserid},</if>
            <if test="datLastUpd != null">#{datLastUpd},</if>
         </trim>
    </insert>

    <update id="updateBdCustomer" parameterType="BdCustomer">
        update bd_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="numCreateUserid != null">num_create_userid = #{numCreateUserid},</if>
            <if test="datCreateTime != null">dat_create_time = #{datCreateTime},</if>
            <if test="numLastUpdUserid != null">num_last_upd_userid = #{numLastUpdUserid},</if>
            <if test="datLastUpd != null">dat_last_upd = #{datLastUpd},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdCustomerById" parameterType="Long">
        delete from bd_customer where id = #{id}
    </delete>

    <delete id="deleteBdCustomerByIds" parameterType="String">
        delete from bd_customer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>