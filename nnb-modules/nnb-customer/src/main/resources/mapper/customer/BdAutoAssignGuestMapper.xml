<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdAutoAssignGuestMapper">
    
    <resultMap type="BdAutoAssignGuest" id="BdAutoAssignGuestResult">
        <result property="autoAssignId"    column="auto_assign_id"    />
        <result property="guestSrcstId"    column="guest_srcst_id"    />
    </resultMap>

    <sql id="selectBdAutoAssignGuestVo">
        select auto_assign_id, guest_srcst_id from bd_auto_assign_guest
    </sql>

    <select id="selectBdAutoAssignGuestList" parameterType="BdAutoAssignGuest" resultMap="BdAutoAssignGuestResult">
        <include refid="selectBdAutoAssignGuestVo"/>
        <where>  
            <if test="autoAssignId != null "> and auto_assign_id = #{autoAssignId}</if>
            <if test="guestSrcstId != null "> and guest_srcst_id = #{guestSrcstId}</if>
        </where>
    </select>
    
    <select id="selectBdAutoAssignGuestByAutoAssignId" parameterType="Long" resultMap="BdAutoAssignGuestResult">
        <include refid="selectBdAutoAssignGuestVo"/>
        where auto_assign_id = #{autoAssignId}
    </select>
        
    <insert id="insertBdAutoAssignGuest" parameterType="BdAutoAssignGuest">
        insert into bd_auto_assign_guest
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="autoAssignId != null">auto_assign_id,</if>
            <if test="guestSrcstId != null">guest_srcst_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="autoAssignId != null">#{autoAssignId},</if>
            <if test="guestSrcstId != null">#{guestSrcstId},</if>
         </trim>
    </insert>

    <update id="updateBdAutoAssignGuest" parameterType="BdAutoAssignGuest">
        update bd_auto_assign_guest
        <trim prefix="SET" suffixOverrides=",">
            <if test="guestSrcstId != null">guest_srcst_id = #{guestSrcstId},</if>
        </trim>
        where auto_assign_id = #{autoAssignId}
    </update>

    <delete id="deleteBdAutoAssignGuestByAutoAssignId" parameterType="Long">
        delete from bd_auto_assign_guest where auto_assign_id = #{autoAssignId}
    </delete>

    <delete id="deleteBdAutoAssignGuestByAutoAssignIds" parameterType="String">
        delete from bd_auto_assign_guest where auto_assign_id in 
        <foreach item="autoAssignId" collection="array" open="(" separator="," close=")">
            #{autoAssignId}
        </foreach>
    </delete>
</mapper>