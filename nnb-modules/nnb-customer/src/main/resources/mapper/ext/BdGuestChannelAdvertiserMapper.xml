<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.popularclue.BdGuestChannelAdvertiserMapper">
    
    <resultMap type="BdGuestChannelAdvertiser" id="BdGuestChannelAdvertiserResult">
        <result property="id"    column="id"    />
        <result property="guestSrcId"    column="guest_src_id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="advertiserInfoId"    column="advertiser_info_id"    />
        <result property="enterpriseDominant"    column="enterprise_dominant"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBdGuestChannelAdvertiserVo">
        select id, guest_src_id, channel_id, advertiser_info_id, enterprise_dominant, create_time from bd_guest_channel_advertiser
    </sql>

    <select id="selectBdGuestChannelAdvertiserList" parameterType="BdGuestChannelAdvertiser" resultMap="BdGuestChannelAdvertiserResult">
        <include refid="selectBdGuestChannelAdvertiserVo"/>
        <where>  
            <if test="guestSrcId != null "> and guest_src_id = #{guestSrcId}</if>
            <if test="channelId != null "> and channel_id = #{channelId}</if>
            <if test="advertiserInfoId != null "> and advertiser_info_id = #{advertiserInfoId}</if>
            <if test="enterpriseDominant != null "> and enterprise_dominant = #{enterpriseDominant}</if>
        </where>
    </select>
    
    <select id="selectBdGuestChannelAdvertiserById" parameterType="Long" resultMap="BdGuestChannelAdvertiserResult">
        <include refid="selectBdGuestChannelAdvertiserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBdGuestChannelAdvertiser" parameterType="BdGuestChannelAdvertiser">
        insert into bd_guest_channel_advertiser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="guestSrcId != null">guest_src_id,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="advertiserInfoId != null">advertiser_info_id,</if>
            <if test="enterpriseDominant != null">enterprise_dominant,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="guestSrcId != null">#{guestSrcId},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="advertiserInfoId != null">#{advertiserInfoId},</if>
            <if test="enterpriseDominant != null">#{enterpriseDominant},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBdGuestChannelAdvertiser" parameterType="BdGuestChannelAdvertiser">
        update bd_guest_channel_advertiser
        <trim prefix="SET" suffixOverrides=",">
            <if test="guestSrcId != null">guest_src_id = #{guestSrcId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="advertiserInfoId != null">advertiser_info_id = #{advertiserInfoId},</if>
            <if test="enterpriseDominant != null">enterprise_dominant = #{enterpriseDominant},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdGuestChannelAdvertiserById" parameterType="Long">
        delete from bd_guest_channel_advertiser where id = #{id}
    </delete>

    <delete id="deleteBdGuestChannelAdvertiserByIds" parameterType="String">
        delete from bd_guest_channel_advertiser where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>