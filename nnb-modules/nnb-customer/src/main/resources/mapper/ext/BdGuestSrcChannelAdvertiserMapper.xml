<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.BdGuestSrcChannelAdvertiserMapper">
    
    <resultMap type="BdGuestSrcChannelAdvertiser" id="BdGuestSrcChannelAdvertiserResult">
        <result property="id"    column="id"    />
        <result property="guestSrcId"    column="guest_src_id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="advertiserInfoId"    column="advertiser_info_id"    />
        <result property="enterpriseDominant"    column="enterprise_dominant"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBdGuestSrcChannelAdvertiserVo">
        select id, guest_src_id, channel_id, advertiser_info_id, enterprise_dominant, create_user, update_user, create_time, update_time, del_flag from bd_guest_src_channel_advertiser
    </sql>

    <select id="selectBdGuestSrcChannelAdvertiserList" parameterType="BdGuestSrcChannelAdvertiser" resultMap="BdGuestSrcChannelAdvertiserResult">
        <include refid="selectBdGuestSrcChannelAdvertiserVo"/>
        <where>  
            <if test="guestSrcId != null "> and guest_src_id = #{guestSrcId}</if>
            <if test="channelId != null "> and channel_id = #{channelId}</if>
            <if test="advertiserInfoId != null "> and advertiser_info_id = #{advertiserInfoId}</if>
            <if test="enterpriseDominant != null "> and enterprise_dominant = #{enterpriseDominant}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectBdGuestSrcChannelAdvertiserById" parameterType="Long" resultMap="BdGuestSrcChannelAdvertiserResult">
        <include refid="selectBdGuestSrcChannelAdvertiserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBdGuestSrcChannelAdvertiser" parameterType="BdGuestSrcChannelAdvertiser" useGeneratedKeys="true" keyProperty="id">
        insert into bd_guest_src_channel_advertiser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guestSrcId != null">guest_src_id,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="advertiserInfoId != null">advertiser_info_id,</if>
            <if test="enterpriseDominant != null">enterprise_dominant,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guestSrcId != null">#{guestSrcId},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="advertiserInfoId != null">#{advertiserInfoId},</if>
            <if test="enterpriseDominant != null">#{enterpriseDominant},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateBdGuestSrcChannelAdvertiser" parameterType="BdGuestSrcChannelAdvertiser">
        update bd_guest_src_channel_advertiser
        <trim prefix="SET" suffixOverrides=",">
            <if test="guestSrcId != null">guest_src_id = #{guestSrcId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="advertiserInfoId != null">advertiser_info_id = #{advertiserInfoId},</if>
            <if test="enterpriseDominant != null">enterprise_dominant = #{enterpriseDominant},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdGuestSrcChannelAdvertiserById" parameterType="Long">
        delete from bd_guest_src_channel_advertiser where id = #{id}
    </delete>

    <delete id="deleteBdGuestSrcChannelAdvertiserByIds" parameterType="String">
        delete from bd_guest_src_channel_advertiser where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>