<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.customer.mapper.popularclue.BdPopularizeClueConvertMapper">

    <resultMap type="BdPopularizeClueConvert" id="BdPopularizeClueConvertResult">
        <result property="id"    column="id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="adClueSourceId"    column="ad_clue_source_id"    />
        <result property="advertiserInfoId"    column="advertiser_info_id"    />
        <result property="vcPhone"    column="vc_phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBdPopularizeClueConvertVo">
        select id, clue_id, ad_clue_source_id, advertiser_info_id, vc_phone, create_time, del_flag from bd_popularize_clue_convert
    </sql>

    <select id="selectBdPopularizeClueConvertList" parameterType="BdPopularizeClueConvert" resultMap="BdPopularizeClueConvertResult">
        <include refid="selectBdPopularizeClueConvertVo"/>
        <where>
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="adClueSourceId != null "> and ad_clue_source_id = #{adClueSourceId}</if>
            <if test="advertiserInfoId != null "> and advertiser_info_id = #{advertiserInfoId}</if>
            <if test="vcPhone != null  and vcPhone != ''"> and vc_phone = #{vcPhone}</if>
            <if test="vcQq != null  and vcQq != ''"> and vc_qq = #{vcQq}</if>
            <if test="vcWeixin != null  and vcWeixin != ''"> and vc_weixin = #{vcWeixin}</if>
        </where>
    </select>

    <select id="selectBdPopularizeClueConvertById" parameterType="Long" resultMap="BdPopularizeClueConvertResult">
        <include refid="selectBdPopularizeClueConvertVo"/>
        where id = #{id}
    </select>

    <insert id="insertBdPopularizeClueConvert" parameterType="BdPopularizeClueConvert" useGeneratedKeys="true" keyProperty="id">
        insert into bd_popularize_clue_convert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="adClueSourceId != null">ad_clue_source_id,</if>
            <if test="advertiserInfoId != null">advertiser_info_id,</if>
            <if test="vcPhone != null">vc_phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="vcQq != null">vc_qq,</if>
            <if test="vcWeixin != null">vc_weixin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="adClueSourceId != null">#{adClueSourceId},</if>
            <if test="advertiserInfoId != null">#{advertiserInfoId},</if>
            <if test="vcPhone != null">#{vcPhone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="vcQq != null">#{vcQq},</if>
            <if test="vcWeixin != null">#{vcWeixin},</if>
         </trim>
    </insert>

    <update id="updateBdPopularizeClueConvert" parameterType="BdPopularizeClueConvert">
        update bd_popularize_clue_convert
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="adClueSourceId != null">ad_clue_source_id = #{adClueSourceId},</if>
            <if test="advertiserInfoId != null">advertiser_info_id = #{advertiserInfoId},</if>
            <if test="vcPhone != null">vc_phone = #{vcPhone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBdPopularizeClueConvertById" parameterType="Long">
        delete from bd_popularize_clue_convert where id = #{id}
    </delete>

    <delete id="deleteBdPopularizeClueConvertByIds" parameterType="String">
        delete from bd_popularize_clue_convert where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByClueIdListEs" resultType="com.nnb.customer.domain.es.ESBdPopularizeClueConvert">
        select id                 as id,
               clue_id            as clueId,
               ad_clue_source_id  as adClueSourceId,
               advertiser_info_id as advertiserInfoId,
               vc_phone           as vcPhone,
               vc_qq              as vcQq,
               vc_weixin          as vcWeixin,
               create_time        as createTime,
               del_flag           as delFlag
        from bd_popularize_clue_convert
        where clue_id in (
            <foreach collection="bdClueIdList" item="clueId" separator=",">
                #{clueId}
            </foreach>
        )
    </select>
</mapper>
