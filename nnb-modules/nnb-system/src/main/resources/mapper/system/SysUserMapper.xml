<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="onlineMainId" column="online_main_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="userLevel" column="user_level"/>
        <result property="userUpdateTime" column="userUpdateTime"/>
        <result property="userCreateTime" column="userCreateTime"/>
        <result property="resetTime" column="reset_time"/>
        <result property="crmEnterpriseId" column="crm_enterprise_id"/>
        <result property="entryTime" column="entry_time"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="daysOnTheJob" column="days_on_the_job"/>
        <result property="dingUserId" column="ding_user_id"/>
        <result property="isLeader" column="is_leader"/>
        <result property="xcxPhone" column="xcx_phone"/>
        <result property="xcxEnterpriseDominant" column="xcx_enterprise_dominant"/>
        <result property="callUserId" column="call_user_id"/>
        <result property="weChatId" column="weChat_id"/>

        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="cityId" column="city_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="enterpriseDominant" column="enterprise_dominant"/>
    </resultMap>

    <resultMap id="RoleResult" type="SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.call_user_id, u.weChat_id
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status, d.ancestors,d.enterprise_dominant,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,
        d.city_id, u.office_phone,u.reset_time,d.crm_enterprise_id,u.entry_time,u.leave_time,u.days_on_the_job, u.ding_user_id, u.is_leader, u.xcx_phone, u.xcx_enterprise_dominant, u.online_main_id
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.update_time, u.create_time as
        userCreateTime, u.update_time as userUpdateTime, u.remark, d.dept_name, d.leader, u.user_level, u.call_user_id, u.ding_user_id from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <if test="xcxPhone != null and xcxPhone != ''">
            AND u.xcx_phone = #{xcxPhone}
        </if>
        <if test="personDeptId != null and personDeptId != 0">
            AND (u.dept_id = #{personDeptId})
        </if>
        <if test="userLevelList != null">
            and u.user_level in
            <foreach collection="userLevelList" item="ulevel" open="(" separator="," close=")">
                #{ulevel}
            </foreach>
        </if>
        <if test="callUserId != null and callUserId != ''">
            AND u.call_user_id = #{callUserId}
        </if>
        <if test="dingUserId != null and dingUserId != ''">
            AND u.ding_user_id = #{dingUserId}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by user_id desc
    </select>

    <select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id = #{roleId}
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
        and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
        ur.role_id = #{roleId})
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
        select count(1) from sys_user where user_name = #{userName} limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, email from sys_user where email = #{email} limit 1
    </select>

    <select id="selectUserListByDeptId" parameterType="long" resultType="com.nnb.system.api.domain.SysUser">
        select user_id userId,
               nick_name nickName
        from sys_user
        where del_flag = 0 and status = 0
          and dept_id = #{deptId}
    </select>

    <select id="selectSysUserByName" resultType="com.nnb.system.api.domain.SysUser">
        select u.user_id userId, u.dept_id deptId, u.user_name userName, u.nick_name nickName, u.password password
        from sys_user u
        where u.nick_name = #{nickName}
        <if test="userId != null">
            and u.user_id != #{userId}
        </if>
    </select>

    <select id="selectSysUserByUserName" resultType="com.nnb.system.api.domain.SysUser">
        select u.user_id userId, u.dept_id deptId, u.user_name userName, u.nick_name nickName, u.password password
        from sys_user u
        where u.user_name = #{userName}
        <if test="userId != null">
            and u.user_id != #{userId}
        </if>
    </select>

    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="oldUserId != null and oldUserId != 0">old_user_id,</if>
        <if test="entryTime != null">entry_time,</if>
        <if test="companyId != null">company_id,</if>
        <if test="isLeader != null">is_leader,</if>
        <if test="callUserId != null and callUserId != ''">call_user_id,</if>
        <if test="dingUserId != null and dingUserId != ''">ding_user_id,</if>
        <if test="weChatId != null and weChatId != ''">weChat_id,</if>
        <if test="onlineMainId != null and onlineMainId != ''">online_main_id,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="deptId != null and deptId != ''">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="oldUserId != null and oldUserId != ''">#{oldUserId},</if>
        <if test="entryTime != null">#{entryTime},</if>
        <if test="companyId != null">#{companyId},</if>
        <if test="isLeader != null">#{isLeader},</if>
        <if test="callUserId != null and callUserId != ''">#{callUserId},</if>
        <if test="dingUserId != null and dingUserId != ''">#{dingUserId},</if>
        <if test="weChatId != null and weChatId != ''">#{weChat_id},</if>
        <if test="onlineMainId != null and onlineMainId != ''">#{onlineMainId},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="SysUser">
        update sys_user
        <set>
            <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="userLevel != null and userLevel != ''">user_level = #{userLevel},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="daysOnTheJob != null">days_on_the_job = #{daysOnTheJob},</if>
            <if test="isLeader != null">is_leader = #{isLeader},</if>
            <if test="xcxPhone != null">xcx_phone = #{xcxPhone},</if>
            <if test="xcxEnterpriseDominant != null">xcx_enterprise_dominant = #{xcxEnterpriseDominant},</if>
            <if test="callUserId != null and callUserId != ''">call_user_id = #{callUserId},</if>
            <if test="dingUserId != null and dingUserId != ''">ding_user_id = #{dingUserId},</if>
            <if test="weChatId != null and weChatId != ''">weChat_id = #{dingUserId},</if>
            <if test="onlineMainId != null and onlineMainId != ''">online_main_id = #{onlineMainId},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="SysUser">
        update sys_user set status = #{status} where user_id = #{userId}
    </update>

    <update id="updateUserByCrm" parameterType="SysUser">
        update sys_user set dept_id = #{deptId} where old_user_id = #{oldUserId}
    </update>

    <update id="updateUserAvatar" parameterType="SysUser">
        update sys_user set avatar = #{avatar} where user_name = #{userName}
    </update>

    <update id="resetUserPwd" parameterType="SysUser">
        update sys_user set password = #{password}, reset_time = sysdate() where user_id = #{userId}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        update sys_user set del_flag = '2' where user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="Long">
        update sys_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="getUserListByIds" parameterType="String" resultMap="SysUserResult">
        -- select user_id, dept_id, user_name, nick_name from sys_user
        <include refid="selectUserVo"/>
        WHERE
        u.user_id in
        <foreach item="id" collection="userIds.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getLegworkDept" resultType="com.nnb.system.domain.vo.SysUserVo">
        select u.user_id as userId,
        u.dept_id as deptId,
        sd.dept_name as deptName,
        u.user_name as userName,
        u.nick_name as nickName,
        u.is_assign as isAssign,
        u.user_num as userNum
        from sys_user u
        left join sys_dept sd on u.dept_id = sd.dept_id
        <where>
            u.dept_id in (76, 98)
            and u.del_flag = 0 and u.status = 0
            <if test="name != null and name != ''">
                AND u.nick_name = #{name}
            </if>
            <if test="isAssign != null">
                AND u.is_assign = #{isAssign}
            </if>
        </where>
    </select>

    <select id="getBJLegworkDept" resultType="com.nnb.system.domain.vo.SysUserVo">
        select u.user_id as userId,
        u.dept_id as deptId,
        u.user_name as userName,
        u.nick_name as nickName,
        u.is_assign as isAssign,
        u.user_num as userNum
        from sys_user u
        <where>
            dept_id in (76)
            <if test="name != null and name != ''">
                AND u.nick_name = #{name}
            </if>
            <if test="isAssign != null">
                AND u.is_assign = #{isAssign}
            </if>
        </where>
    </select>
    <select id="selectUserListLevel" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.update_time, u.create_time as
        userCreateTime, u.update_time as userUpdateTime, u.remark, d.dept_name, d.leader, u.user_level from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0' and u.status != 1
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <if test="userLevelList != null">
            and u.user_level in
            <foreach collection="userLevelList" item="ulevel" open="(" separator="," close=")">
                #{ulevel}
            </foreach>
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <update id="setAssign">
        update sys_user
        set is_assign = #{isAssign}
        where user_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="getUserListByDeptList" resultType="com.nnb.system.api.domain.SysUser">
        select user_id userId,
               nick_name nickName
        from sys_user
        where status = 0 and dept_id IN
        <foreach collection="deptList" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="selectUserByDingUserId" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.ding_user_id = #{dingUserId}
    </select>

    <select id="selectUserByWeComUserId" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.weChat_id = #{weComUserId}
    </select>

    <insert id="insertExportLog" parameterType="ExportLog" useGeneratedKeys="true" keyProperty="id">
        insert into export_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createdTime != null">created_time,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="exportData != null">export_data,</if>
            <if test="exportResult != null">export_result,</if>
            <if test="exportMoudle != null">export_moudle,</if>
            <if test="exportPage != null">export_page,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createdTime != null">#{createdTime},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="exportData != null">#{exportData},</if>
            <if test="exportResult != null">#{exportResult},</if>
            <if test="exportMoudle != null">#{exportMoudle},</if>
            <if test="exportPage != null">#{exportPage},</if>
        </trim>
    </insert>
</mapper>
