package com.nnb.third;

import cn.hutool.core.util.StrUtil;

import com.nnb.third.domain.weComDTO.WeComRecord;
import com.nnb.third.mapper.WeComRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.StringJoiner;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-14
 * @Version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = NnbThirdApplication.class)
public class WeComeTest {

    private WeComRecordMapper weComRecordMapper;

    @Test
    public void getGetGroupMessages() {
        //模版 姓名 + 时间 + 消息（）+ 换行符
        String msgTemplate = "%s %s 【%s】 ";
        List<WeComRecord> weComRecords = weComRecordMapper.selectWeComRecordList(
                new WeComRecord().setRoomId("wrkL9zGQAAwiMA9fRS3bO5FAAvLcepzg")
        );
        StringJoiner joiner = new StringJoiner(System.lineSeparator());
        weComRecords.stream().sorted(Comparator.comparing(WeComRecord::getMsgTime))
                .forEach(en -> joiner.add(
                                String.format(
                                        msgTemplate,
                                        en.getFromUser(),
                                        getDate(Long.parseLong(en.getMsgTime())),
                                        StrUtil.isNotBlank(en.getContent()) ? en.getContent() : ""
                                )
                        )
                );
        log.info(joiner.toString());
    }

    private String getDate(long time) {
        Instant instant = Instant.ofEpochMilli(time);
        // 转换为北京时间（UTC+8）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.of("Asia/Shanghai"));
        return formatter.format(instant);
    }

    @Autowired
    private void setWeComRecordMapper(WeComRecordMapper weComRecordMapper) {
        this.weComRecordMapper = weComRecordMapper;
    }
}
