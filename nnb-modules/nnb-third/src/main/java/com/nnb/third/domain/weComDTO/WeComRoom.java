package com.nnb.third.domain.weComDTO;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-06-25
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class WeComRoom implements Serializable {

    private Long id;

    @ApiModelProperty("群聊消息的群id")
    private String roomId;

    @ApiModelProperty("群名")
    private String roomName;

    @ApiModelProperty("群主ID")
    private String roomOwner;

    @ApiModelProperty("成员列表")
    private String userList;

    @ApiModelProperty("创建时间")
    private Integer createTime;

    @ApiModelProperty("群信息")
    private String roomDetail;

    @ApiModelProperty("企业类型：后企：2，牛牛帮：1")
    private Integer corpType;
}
