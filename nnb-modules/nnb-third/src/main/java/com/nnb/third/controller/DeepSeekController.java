package com.nnb.third.controller;

import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.third.domain.searchDTO.ChatWithWeComDTO;
import com.nnb.third.service.DeepSeekService;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionResponse;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-10
 * @Version: 1.0
 */
@Api(tags = "deepSeekController", value = "deepSeek-AI模型控制器")
@RestController
@RequestMapping("/deepSeek")
@RequiredArgsConstructor
public class DeepSeekController {

    private DeepSeekService deepSeekService;

    // sse 流式返回
    @GetMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatCompletionResponse> chat(String prompt) {
        return deepSeekService.chat(prompt);
    }

    // sse 流式返回
    @PostMapping(value = "/chatWithWeCom", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatCompletionResponse> chatWithWeCom(@RequestBody ChatWithWeComDTO chatWithWeComDTO) {
        return deepSeekService.chatWithWeCom(chatWithWeComDTO);
    }

    // sse 流式返回
    @GetMapping(value = "/chatWithWeComGet", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatCompletionResponse> chatWithWeComGet(@RequestParam String roomId,
                                                         @RequestParam String cueWord,
                                                         @RequestParam String beginTime,
                                                         @RequestParam String endTime,
                                                         @RequestParam int reasoning) {
        ChatWithWeComDTO chatWithWeComDTO = new ChatWithWeComDTO(roomId, cueWord, beginTime, endTime, reasoning);
        return deepSeekService.chatWithWeCom(chatWithWeComDTO);
    }

    @GetMapping(value = "/chatWithWeComGetFinal")
    public AjaxResult chatWithWeComGetFina(@RequestParam String roomId,
                                           @RequestParam String cueWord,
                                           @RequestParam String beginTime,
                                           @RequestParam String endTime,
                                           @RequestParam int reasoning) {
        ChatWithWeComDTO chatWithWeComDTO = new ChatWithWeComDTO(roomId, cueWord, beginTime, endTime, reasoning);
        return AjaxResult.success(deepSeekService.chatWithWeComGetFina(chatWithWeComDTO));
    }

    @GetMapping(value = "/analyzeChatHistory")
    public AjaxResult analyzeChatHistory() {
        return AjaxResult.success(deepSeekService.analyzeChatHistory());
    }

    @Autowired
    private void setDeepSeekService(DeepSeekService deepSeekService) {
        this.deepSeekService = deepSeekService;
    }
}
