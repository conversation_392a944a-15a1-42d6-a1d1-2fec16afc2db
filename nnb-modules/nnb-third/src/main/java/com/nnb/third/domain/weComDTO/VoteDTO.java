package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 投票消息
 * @Date: 2025-03-31
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoteDTO extends MessageDTO implements Serializable {

    /**
     * voteItem:
     * msgtype	collect。String类型
     * room_name	填表消息所在的群名称。String类型
     * creator	创建者在群中的名字。String类型
     * create_time	创建的时间。String类型
     * title	表名。String类型
     * details	表内容。json数组类型
     * id	表项id。Uint64类型
     * ques	表项名称。String类型
     * type	表项类型，有Text(文本),Number(数字),Date(日期),Time(时间)。String类型
     */

    @ApiModelProperty("投票主题。String类型")
    @JsonProperty("votetitle")
    private String voteTitle;

    @ApiModelProperty("名片所有者所在的公司名称。String类型")
    @JsonProperty("voteitem")
    private String voteItem;

    @ApiModelProperty("投票类型.101发起投票、102参与投票。Uint32类型")
    @JsonProperty("votetype")
    private Integer voteType;

    @ApiModelProperty("投票id，方便将参与投票消息与发起投票消息进行前后对照。String类型")
    @JsonProperty("voteid")
    private String voteId;
}
