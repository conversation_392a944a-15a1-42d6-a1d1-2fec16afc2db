package com.nnb.third.service;

import com.nnb.third.domain.weComDTO.WeComRoomEnterprise;
import com.nnb.third.domain.weComDTO.WeComRoomEnterpriseDTO;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-06-20
 * @Version: 1.0
 */
public interface WeComRoomEnterpriseService {

    /**
     * 查询企业微信企业信息关联
     *
     * @param id 企业微信企业信息关联主键
     * @return 企业微信企业信息关联
     */
    public WeComRoomEnterprise selectWeComRoomEnterpriseById(Long id);

    /**
     * 查询企业微信企业信息关联列表
     *
     * @param weComRoomEnterprise 企业微信企业信息关联
     * @return 企业微信企业信息关联集合
     */
    public List<WeComRoomEnterpriseDTO> selectWeComRoomEnterpriseList(WeComRoomEnterprise weComRoomEnterprise);

    /**
     * 新增企业微信企业信息关联
     *
     * @param weComRoomEnterprise 企业微信企业信息关联
     * @return 结果
     */
    public int insertWeComRoomEnterprise(WeComRoomEnterprise weComRoomEnterprise);

    /**
     * 修改企业微信企业信息关联
     *
     * @param weComRoomEnterprise 企业微信企业信息关联
     * @return 结果
     */
    public int updateWeComRoomEnterprise(WeComRoomEnterprise weComRoomEnterprise);

    /**
     * 批量删除企业微信企业信息关联
     *
     * @param ids 需要删除的企业微信企业信息关联主键集合
     * @return 结果
     */
    public int deleteWeComRoomEnterpriseByIds(List<Long> ids);

    /**
     * 删除企业微信企业信息关联信息
     *
     * @param id 企业微信企业信息关联主键
     * @return 结果
     */
    public int deleteWeComRoomEnterpriseById(Long id);

    /**
     * 切换企业
     * @param weComRoomEnterprise weComRoomEnterprise
     * @return int
     */
    public int handoff(WeComRoomEnterprise weComRoomEnterprise);
}
