package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 会议邀请消息
 * @Date: 2025-04-02
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MeetingDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("会议主题。String类型")
    @JsonProperty("topic")
    private String topic;

    @ApiModelProperty("会议开始时间。Utc时间")
    @JsonProperty("starttime")
    private String meetingStartTime;

    @ApiModelProperty("会议结束时间。Utc时间。如为快速会议则该字段为空")
    @JsonProperty("endtime")
    private String meetingEndTime;

    @ApiModelProperty("会议地址。String类型。如为快速会议则该字段为空")
    @JsonProperty("address")
    private String meetingAddress;

    @ApiModelProperty("会议备注。String类型。如为快速会议则该字段为空")
    @JsonProperty("remarks")
    private String meetingRemarks;

    @ApiModelProperty("会议id。方便将发起、处理消息进行对照。uint64类型")
    @JsonProperty("meetingid")
    private Integer meetingId;
}
