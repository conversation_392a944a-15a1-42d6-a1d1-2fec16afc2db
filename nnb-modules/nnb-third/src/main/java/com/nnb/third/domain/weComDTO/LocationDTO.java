package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 位置消息
 * @Date: 2025-03-31
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LocationDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("经度，单位double")
    @JsonProperty("longitude")
    private String longitude;

    @ApiModelProperty("纬度，单位double")
    @JsonProperty("latitude")
    private String latitude;

    @ApiModelProperty("地址信息。String类型")
    @JsonProperty("address")
    private String address;

    @ApiModelProperty("缩放比例。Uint32类型")
    @JsonProperty("zoom")
    private String zoom;

    @ApiModelProperty("位置信息的title。String类型")
    @JsonProperty("title")
    private String title;
}
