package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 音视频通话
 * @Date: 2025-04-02
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoipTextDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("通话时长，单位秒，uint32类型")
    @JsonProperty("callduration")
    private Integer callDuration;

    @ApiModelProperty("通话类型；1; //单人视频通话｜2; //单人语音通话｜3; //多人视频通话｜4; //多人语音通话")
    @JsonProperty("invitetype")
    private Integer inviteType;
}
