package com.nnb.third.service;

import com.common.api.model.DingMessageDTO;
import com.common.api.model.weCom.WeComApp;
import com.nnb.third.domain.searchDTO.GetRoomSearch;
import com.nnb.third.domain.weComDTO.GroupChatRes;
import com.nnb.third.domain.weComDTO.WeComRoomDTO;

import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-03-24
 * @Version: 1.0
 */
public interface WeComService {

    /**
     * 获取会话内容
     *
     * @return Boolean
     */
    Boolean retrieveSessionContent();

    /**
     * 单类型消息入库测试
     *
     * @param msgJson 消息json
     * @return Boolean
     */
    Boolean contentInsertDBTest(String msgJson);

    /**
     * 获取所有群id
     *
     * @return List<WeComRoomDTO>
     */
    List<WeComRoomDTO> getRoomIds(GetRoomSearch getRoomSearch);

    /**
     * 获取accessToken
     *
     * @param chatId   群id
     * @param corpType 企业类型
     * @param appName
     * @return GroupChatRes
     */
    GroupChatRes getGroupChatName(String chatId, Integer corpType, String appName);

    /**
     * 获取所有群信息
     *
     * @return Boolean
     */
    Boolean getAllCustomerGroupChat();

    /**
     * 获取所有的WeComApps
     *
     * @return List<WeComProperties.weComApp>
     */
    List<WeComApp> getWeComApps();

    /**
     * 获取企业 JS-SDK
     *
     * @param corpType
     * @param appName
     * @param url
     * @return
     */
    Map<String, Object> getJsSdk(Integer corpType, String appName, String url);

    /**
     * 获取应用 JS-SDK
     *
     * @param corpType
     * @param appName
     * @param url
     * @return
     */
    Map<String, Object> getAppJsSdk(Integer corpType, String appName, String url);

    /**
     * 发送丁丁服务告警消息
     * @param dingMessageDTO
     * @return
     */
    Boolean sendWarmDingMessage(DingMessageDTO dingMessageDTO);
}
