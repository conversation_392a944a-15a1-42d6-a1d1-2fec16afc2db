package com.nnb.third.domain.weComDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-06-20
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfo implements Serializable {

    private String accountTime;
    private String accountName;
    private String accountStatus;
    private Long enterpriseId;
}
