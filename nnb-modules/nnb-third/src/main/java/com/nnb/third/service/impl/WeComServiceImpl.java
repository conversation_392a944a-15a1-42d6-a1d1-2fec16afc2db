package com.nnb.third.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.common.api.config.WeComConfig;
import com.common.api.model.DingMessageDTO;
import com.common.api.model.weCom.WeComApp;
import com.common.api.service.DingService;
import com.common.api.service.WeComApiService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnb.common.core.constant.CacheConstants;
import com.nnb.common.core.exception.ServiceException;
import com.common.api.properties.WeComProperties;
import com.nnb.common.redis.service.RedisService;
import com.nnb.third.constant.DingMessageConstants;
import com.nnb.third.constant.WeComConstants;
import com.nnb.third.converter.WeComFactory;
import com.nnb.third.domain.searchDTO.GetRoomSearch;
import com.nnb.third.domain.weComDTO.*;
import com.common.api.enums.CorpTypeEnum;
import com.nnb.third.mapper.WeComAppConfigMapper;
import com.nnb.third.mapper.WeComRecordMapper;
import com.nnb.third.mapper.WeComRoomMapper;
import com.nnb.third.service.OssService;
import com.nnb.third.service.WeComService;
import com.nnb.third.utils.MybatisBatchUtils;
import com.nnb.third.utils.PullFilesUtil;
import com.nnb.third.utils.RSAEncryptUtil;
import com.nnb.third.utils.WeComPoolUtil;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-03-24
 * @Version: 1.0
 */
@Slf4j
@Service
public class WeComServiceImpl implements WeComService {

    private OssService ossService;

    private WeComConfig weComConfig;

    private WeComFactory weComFactory;

    private MybatisBatchUtils mybatisBatchUtils;

    private WeComRecordMapper weComRecordMapper;

    private WeComProperties weComProperties;

    @Resource
    private WeComAppConfigMapper weComAppConfigMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private WeComApiService weComApiService;

    @Resource
    private WeComRoomMapper weComRoomMapper;

    @Resource
    private DingService dingService;

    @Override
    public Boolean retrieveSessionContent() {

        long sdk = Finance.NewSdk();
        for (CorpTypeEnum anEnum : CorpTypeEnum.values()) {
            int seq = 0;
            //查询 上次查询到到 seq
            Integer seqDB = weComRecordMapper.selectSeqMax(anEnum.getCorpType());
            if (ObjectUtil.isNotEmpty(seqDB)) {
                seq = seqDB;
            }
            long ret = 0;
            ret = Finance.Init(sdk,
                    weComConfig.getCorpId(anEnum.getCorpType()),
                    weComConfig.getSecretKey(anEnum.getCorpType(), WeComConstants.RECORD_APP)
            );
            if (ret != 0) {
                log.error("init sdk err ret {}", ret);
                continue;
            }
            List<ChatDataRes> list = new ArrayList<>();
            int limit = 500;
            boolean flag = true;
            while (flag) {
                //拉取会话存档
                //每次使用GetChatData拉取存档前需要调用NewSlice获取一个slice，在使用完slice中数据后，还需要调用FreeSlice释放。
                long slice = Finance.NewSlice();
                ret = Finance.GetChatData(sdk, seq, limit, null, null, 10, slice);
                if (ret != 0) {
                    Finance.FreeSlice(slice);
                    log.error("getChatData ret {}", ret);
                    continue;
                }
                log.info("getChatData :{}", Finance.GetContentFromSlice(slice));
                //解析
                ChatDataRes chatDataRes;
                try {
                    chatDataRes = new ObjectMapper().readValue(Finance.GetContentFromSlice(slice), ChatDataRes.class);
                    if (chatDataRes.getErrCode() != 0) {
                        log.error("企业微信获取会话内容接口响应失败code：{}, msg：{}", chatDataRes.getErrCode(), chatDataRes.getErrMsg());
                        continue;
                    }
                } catch (JsonProcessingException e) {
                    log.error("ChatDataRes Json反序列化失败", e);
                    continue;
                }
                Finance.FreeSlice(slice);
                //判定集合是否为空
                if (CollUtil.isNotEmpty(chatDataRes.getChatDataList())) {
                    list.add(chatDataRes);
                    seq = chatDataRes.getChatDataList().get(chatDataRes.getChatDataList().size() - 1).getSeq();
                } else {
                    flag = false;
                }
            }
            List<ChatDataDTO> collect = list.stream()
                    .map(ChatDataRes::getChatDataList).flatMap(Collection::stream).collect(Collectors.toList());
            //解密会话存档内容
            List<WeComRecord> weComRecordList = new ArrayList<>();
            LocalDateTime insertTime = LocalDateTime.now();
            for (ChatDataDTO chatData : collect) {
                //每次使用DecryptData解密会话存档前需要调用NewSlice获取一个slice，在使用完slice中数据后，还需要调用FreeSlice释放。
                long msg = Finance.NewSlice();
                try {
                    //此处需要用户先用rsa私钥解密encrypt_random_key后，作为encrypt_key参数传入sdk来解密encrypt_chat_msg获取会话存档明文。
                    String message = RSAEncryptUtil.decryptRSA(
                            chatData.getEncryptRandomKey(), weComConfig.getPrivateKey(anEnum.getCorpType())
                    );
                    ret = Finance.DecryptData(sdk, message, chatData.getEncryptChatMsg(), msg);
                    if (ret != 0) {
                        log.error("decryptData err ret {}", ret);
                        Finance.FreeSlice(msg);
                        continue;
                    }
                    String plaintext = Finance.GetContentFromSlice(msg);
                    log.info("decrypt ret:{} , msg:{}", ret, plaintext);
                    Finance.FreeSlice(msg);

                    MessageDTO messageDTO = new ObjectMapper().readValue(plaintext, MessageDTO.class);
                    messageDTO.setToUser(JSONUtil.toJsonStr(messageDTO.getToUserList()));
                    messageDTO.setSdk(sdk);
                    //转换
                    WeComRecord weComRecord = weComFactory.getWeComRecord(messageDTO, plaintext, chatData);
                    weComRecord.setInsertTime(insertTime);
                    weComRecord.setCorpType(anEnum.getCorpType());
                    weComRecord.setPlaintext(plaintext);
                    weComRecordList.add(weComRecord);
                } catch (Exception e) {
                    log.error("解密会话存档内容有误：seq：{}，error：{}", chatData.getSeq(), e.getMessage());
                }
            }
            if (CollUtil.isNotEmpty(weComRecordList)) {
                //保存
                mybatisBatchUtils.batchUpdateOrInsert(weComRecordList, WeComRecordMapper.class,
                        (en, mapper) -> mapper.insertWeComRecord(en)
                );
                // 子线程写入群id
                writesTheGroupId(anEnum, weComRecordList);
            }
            //上传媒体文件
            //uploadMediaFiles(weComRecordList, sdk);
        }
        Finance.DestroySdk(sdk);
        return Boolean.TRUE;
    }

    private void writesTheGroupId(CorpTypeEnum anEnum, List<WeComRecord> weComRecordList) {
        WeComPoolUtil.getPool().execute(() -> {
            //更新群id we_come_room
            List<WeComRoom> weComRoomList = weComRecordList.stream().map(WeComRecord::getRoomId)
                    .filter(StrUtil::isNotBlank).filter(roomId -> !"0".equals(roomId))
                    .distinct()
                    .map(roomId -> new WeComRoom().setRoomId(roomId).setCorpType(anEnum.getCorpType()))
                    .collect(Collectors.toList());
            mybatisBatchUtils.batchUpdateOrInsert(weComRoomList, WeComRoomMapper.class,
                    (en, mapper) -> mapper.insertWeComRoomIgnore(en)
            );
        });
    }

    private void uploadMediaFiles(List<WeComRecord> weComRecordList, long sdk) {
        for (WeComRecord weComRecord : weComRecordList) {
            //拉取媒体，混合消息单独处理
            if (!WeComConstants.MIXED.equals(weComRecord.getMsgType())) {
                File file = PullFilesUtil.pullMediaFiles(sdk, weComRecord.getMsgType(), weComRecord.getPlaintext());
                if (ObjectUtil.isNotEmpty(file)) {
                    //上传
                    MultipartFile multipartFile = PullFilesUtil.fileToMultipartFile(file);
                    OssFileForUploadVO upload = ossService.upload(multipartFile, WeComConstants.MODULE_NAME);
                    //放入上传路径
                    weComRecord.setFileUrl(JSONUtil.toJsonStr(Collections.singletonList(upload.getWrapFilePath())));
                }
            } else {
                MixedItemDTO mixedItemDTO = new MixedItemDTO();
                try {
                    mixedItemDTO = new ObjectMapper().readValue(weComRecord.getMixed(), MixedItemDTO.class);
                } catch (Exception e) {
                    log.error("MixedDTO反序列化失败：", e);
                }
                List<String> urlList = new ArrayList<>();
                for (MixedItemDTO.ItemDTO itemDTO : mixedItemDTO.getItem()) {
                    //去除最后一个和第一个字符
                    String dataJson = itemDTO.getContent().substring(1, itemDTO.getContent().length() - 1);
                    File file = PullFilesUtil.pullMediaFiles(sdk, itemDTO.getType(), dataJson);
                    if (ObjectUtil.isNotEmpty(file)){
                        //上传
                        MultipartFile multipartFile = PullFilesUtil.fileToMultipartFile(file);
                        OssFileForUploadVO upload = ossService.upload(multipartFile, WeComConstants.MODULE_NAME);
                        //放入上传路径
                        urlList.add(upload.getWrapFilePath());
                    }
                }
                if (CollUtil.isNotEmpty(urlList)){
                    weComRecord.setFileUrl(JSONUtil.toJsonStr(urlList));
                }
            }
        }
        //更新
        List<WeComRecord> urlList = weComRecordList.stream()
                .filter(en -> StrUtil.isNotBlank(en.getFileUrl())).collect(Collectors.toList());
        mybatisBatchUtils.batchUpdateOrInsert(urlList, WeComRecordMapper.class,
                (en, mapper) -> mapper.updateWeComRecordUrl(en)
        );
    }

    @Override
    public Boolean contentInsertDBTest(String msgJson) {
        try {
            long sdk = Finance.NewSdk();
            MessageDTO messageDTO = new ObjectMapper().readValue(msgJson, MessageDTO.class);
            messageDTO.setSdk(sdk);
            //转换
            WeComRecord weComRecord = weComFactory.getWeComRecord(messageDTO, msgJson, new ChatDataDTO());
            //拉取媒体，混合消息单独处理
            weComRecord.setInsertTime(LocalDateTime.now());
            if (!WeComConstants.MIXED.equals(messageDTO.getMsgType())) {
                File file = PullFilesUtil.pullMediaFiles(sdk, messageDTO.getMsgType(), msgJson);
                if (ObjectUtil.isNotEmpty(file)) {
                    weComRecord.setInsertTime(LocalDateTime.now());
                    //上传
                    MultipartFile multipartFile = PullFilesUtil.fileToMultipartFile(file);
                    OssFileForUploadVO upload = ossService.upload(multipartFile, WeComConstants.MODULE_NAME);
                    //放入上传路径
                    weComRecord.setFileUrl(JSONUtil.toJsonStr(Collections.singletonList(upload.getWrapFilePath())));
                }
            }
            weComRecordMapper.insertWeComRecord(weComRecord);
        } catch (Exception e) {
            log.error("contentInsertDBTest err ", e);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<WeComRoomDTO> getRoomIds(GetRoomSearch getRoomSearch) {
        if (StrUtil.isNotBlank(getRoomSearch.getSearchKey())){
            return weComRecordMapper.selectEnterpriseByKeyWord(getRoomSearch.getSearchKey());
        } else {
            List<String> roomIds = weComRecordMapper.selectRoomIds();
            //查询企业名称
            List<WeComRoomDTO> list = weComRecordMapper.selectEnterpriseNameByRoomIds(roomIds);
            return roomIds.stream().map(roomId -> {
                WeComRoomDTO weComRoomDTO = new WeComRoomDTO().setRoomId(roomId);
                if (CollUtil.isNotEmpty(list)){
                    list.stream().filter(en -> roomId.equals(en.getRoomId()))
                            .findAny()
                            .ifPresent(en -> weComRoomDTO.setCompanyName(en.getCompanyName()));
                }
                return weComRoomDTO;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public GroupChatRes getGroupChatName(String chatId, Integer corpType, String appName) {
        String accessToken = weComApiService.getAccessToken(corpType, appName);
        //构建参数
        GroupChatReq groupChatReq = new GroupChatReq().setChatId(chatId);
        Map<String, Object> mapJson = BeanUtil.beanToMap(groupChatReq, true, true);
        //请求
        String groupChatUrl = String.format(weComProperties.getUrl().getGetGroupChatByRoomId(), accessToken);
        String postJson = HttpUtil.post(groupChatUrl, JSONUtil.toJsonStr(mapJson));
        //请求返回解析
        GroupChatRes groupChatRes;
        try {
            groupChatRes = new ObjectMapper().readValue(postJson, GroupChatRes.class);
            if (groupChatRes.getErrCode() != 0) {
                throw new ServiceException(
                        StrUtil.format("企业微信-类型【{}】,获取groupChatMsg失败：{}",
                                corpType, groupChatRes.getErrMsg()
                        )
                );
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("GroupChatRes Json反序列化失败" + e.getMessage());
        }
        return groupChatRes;
    }

    @Override
    public Boolean getAllCustomerGroupChat() {
        //扫描
        List<WeComRoom> list = weComRoomMapper.selectWeComJsonMsgIsNull();
        if (CollUtil.isNotEmpty(list)){
            list.forEach(en -> {
                try {
                    GroupChatRes res = getGroupChatName(en.getRoomId(), en.getCorpType(), "group_chat");
                    List<String> userIdList = res.getGroupChat().getMemberList().stream()
                            .map(GroupChatRes.GroupChatDTO.MemberListDTO::getUserId)
                            .collect(Collectors.toList());
                    en.setUserList(JSONUtil.toJsonStr(userIdList))
                            .setRoomName(res.getGroupChat().getName())
                            .setRoomOwner(res.getGroupChat().getOwner())
                            .setCreateTime(res.getGroupChat().getCreateTime())
                            .setRoomDetail(JSONUtil.toJsonStr(res));
                } catch (Exception e) {
                    log.error("getAllCustomerGroupChat err ", e);
                }
            });
        }
        mybatisBatchUtils.batchUpdateOrInsert(list, WeComRoomMapper.class,
                (en, mapper) -> mapper.updateWeComRoom(en)
        );
        return Boolean.TRUE;
    }

    @Override
    public List<WeComApp> getWeComApps() {
        // 从数据库加载
        List<WeComApp> cachedConfig = weComAppConfigMapper.selectWeComAppConfigList(new WeComApp());
        if (CollUtil.isNotEmpty(cachedConfig)) {
            // 先删除
            redisService.deleteObject(CacheConstants.WE_COM_APPS_KEY);
            // 缓存配置
            redisService.setCacheObject(CacheConstants.WE_COM_APPS_KEY, cachedConfig);
            return cachedConfig;
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, Object> getJsSdk(Integer corpType, String appName, String url) {
        return weComApiService.getJsSdkSign(corpType, appName, url);
    }

    @Override
    public Map<String, Object> getAppJsSdk(Integer corpType, String appName, String url) {
        return weComApiService.getAppJsSdkSign(corpType, appName, url);
    }

    @Override
    public Boolean sendWarmDingMessage(DingMessageDTO dingMessageDTO) {
        dingMessageDTO.setTitle(DingMessageConstants.SERVICE_WARMING_TITLE)
                        .setContent(
                                String.format(DingMessageConstants.SERVICE_WARMING, dingMessageDTO.getContent())
                        );
        log.info("erp发送钉钉消息入参为{}", JSONUtil.toJsonStr(dingMessageDTO));
        dingService.sendDingMessageToUser(dingMessageDTO);
        return Boolean.TRUE;
    }

    @Autowired
    private void setOssService(OssService ossService) {
        this.ossService = ossService;
    }

    @Autowired
    private void setWeComConfig(WeComConfig weComConfig) {
        this.weComConfig = weComConfig;
    }

    @Autowired
    private void setWeComFactory(WeComFactory weComFactory) {
        this.weComFactory = weComFactory;
    }

    @Autowired
    private void setMybatisBatchUtils(MybatisBatchUtils mybatisBatchUtils) {
        this.mybatisBatchUtils = mybatisBatchUtils;
    }

    @Autowired
    private void setWeComRecordMapper(WeComRecordMapper weComRecordMapper) {
        this.weComRecordMapper = weComRecordMapper;
    }

    @Autowired
    private void setWeComPropertiesConfig(WeComProperties weComProperties) {
        this.weComProperties = weComProperties;
    }
}
