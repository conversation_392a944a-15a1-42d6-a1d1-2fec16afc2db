package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 文件消息
 * @Date: 2025-03-31
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FileDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("文件名称。String类型")
    @JsonProperty("filename")
    private String fileName;

    @ApiModelProperty("文件类型后缀。String类型")
    @JsonProperty("fileext")
    private String fileExt;
}
