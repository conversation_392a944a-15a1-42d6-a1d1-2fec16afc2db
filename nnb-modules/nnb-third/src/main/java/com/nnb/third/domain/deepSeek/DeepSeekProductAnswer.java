package com.nnb.third.domain.deepSeek;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-06-27
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DeepSeekProductAnswer implements Serializable {

    @ApiModelProperty("群id")
    private String roomId;

    @ApiModelProperty("主要意向产品")
    private String mainIntentionProduct;

    @ApiModelProperty("关键词匹配")
    private String keywordMatching;

    @ApiModelProperty("意向度评分")
    private Integer intentionRating;

    @ApiModelProperty("是否有意向")
    private String isIntention;

    @ApiModelProperty("AI分析结果文本")
    private String aiAnalysisResult;
}
