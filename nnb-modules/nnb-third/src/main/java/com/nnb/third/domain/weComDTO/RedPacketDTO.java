package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Chen-xy
 * @Description: 红包消息
 * @Date: 2025-04-02
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedPacketDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("红包消息类型。1 普通红包、2 拼手气群红包、3 激励群红包。Uint32类型")
    @JsonProperty("type")
    private Integer redPacketType;

    @ApiModelProperty("红包祝福语。String类型")
    @JsonProperty("wish")
    private String redPacketWish;

    @ApiModelProperty("红包总个数。Uint32类型")
    @JsonProperty("totalcnt")
    private Integer totalCnt;

    @ApiModelProperty("红包总金额。Uint32类型，单位为分。")
    @JsonProperty("totalamount")
    private BigDecimal totalAmount;
}
