package com.nnb.third.domain.weComDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: <PERSON>-xy
 * @Description:
 * @Date: 2025-04-18
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class WeComRoomDTO {

    private String id;
    private String roomId;
    private String romeName;
    private String companyName;
}
