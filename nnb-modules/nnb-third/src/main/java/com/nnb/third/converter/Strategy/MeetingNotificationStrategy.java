package com.nnb.third.converter.Strategy;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnb.third.constant.WeComConstants;
import com.nnb.third.converter.WeComStrategy;
import com.nnb.third.domain.weComDTO.MeetingNotificationDTO;
import com.nnb.third.domain.weComDTO.MessageDTO;
import com.nnb.third.domain.weComDTO.WeComRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-02
 * @Version: 1.0
 */
@Slf4j
@Component
public class MeetingNotificationStrategy implements WeComStrategy<MessageDTO, String, List<WeComRecord>> {

    @Override
    public boolean supports(String type) {
        return WeComConstants.MEETING_NOTIFICATION.equalsIgnoreCase(type);
    }

    @Override
    public MeetingNotificationDTO getEntity(MessageDTO messageDTO, String JsonData) {
        Map<String, Object> bean = JSONUtil.toBean(JsonData, new TypeReference<Map<String, Object>>() {
        }, true);
        MeetingNotificationDTO meetingNotificationDTO = new MeetingNotificationDTO();
        try {
            meetingNotificationDTO = new ObjectMapper().readValue(
                    bean.get("info").toString(), MeetingNotificationDTO.class
            );
        } catch (Exception e) {
            log.error("MeetingNotificationDTO反序列化失败：", e);
        }
        //copy全属性
        BeanUtils.copyProperties(messageDTO, meetingNotificationDTO);
        return meetingNotificationDTO;
    }

    @Override
    public List<WeComRecord> getInsertList(String s) {
        return Collections.emptyList();
    }
}
