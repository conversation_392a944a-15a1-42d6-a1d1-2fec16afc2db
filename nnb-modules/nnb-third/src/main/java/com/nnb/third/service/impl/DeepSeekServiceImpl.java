package com.nnb.third.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.nnb.third.constant.DateFormatConstants;
import com.nnb.third.constant.WeComConstants;
import com.nnb.third.domain.deepSeek.DeepSeekProductAnswer;
import com.nnb.third.domain.resDTO.ChatResultDTO;
import com.nnb.third.domain.searchDTO.ChatWithWeComDTO;
import com.nnb.third.domain.weComDTO.WeComRecord;
import com.nnb.third.domain.weComDTO.WeComRoom;
import com.nnb.third.mapper.WeComRecordMapper;
import com.nnb.third.mapper.WeComRoomMapper;
import com.nnb.third.service.DeepSeekService;
import com.nnb.third.utils.JsonExtractor;
import com.nnb.third.utils.MybatisBatchUtils;
import io.github.pigmesh.ai.deepseek.core.DeepSeekClient;
import io.github.pigmesh.ai.deepseek.core.chat.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-14
 * @Version: 1.0
 */
@Slf4j
@Service
public class DeepSeekServiceImpl implements DeepSeekService {

    @Value("classpath:/prompts/product.pt")
    private org.springframework.core.io.Resource productResource;

    @Resource
    private DeepSeekClient deepSeekClient;

    @Resource
    private WeComRoomMapper weComRoomMapper;

    @Resource
    private WeComRecordMapper weComRecordMapper;

    @Resource
    private MybatisBatchUtils mybatisBatchUtils;

    @Override
    public Flux<ChatCompletionResponse> chat(String prompt) {
        return deepSeekClient.chatFluxCompletion(prompt);
    }

    @Override
    public Flux<ChatCompletionResponse> chatWithWeCom(ChatWithWeComDTO chatWithWeComDTO) {

        StringJoiner joiner = getChatMsg(chatWithWeComDTO);
        ChatCompletionRequest request = getRequest(chatWithWeComDTO, joiner);
        return deepSeekClient.chatFluxCompletion(request);
    }

    @Override
    public ChatResultDTO chatWithWeComGetFina(ChatWithWeComDTO chatWithWeComDTO) {
        StringJoiner joiner = getChatMsg(chatWithWeComDTO);
        ChatCompletionRequest request = getRequest(chatWithWeComDTO, joiner);
        ChatCompletionResponse response = deepSeekClient.chatCompletion(request).execute();
        AssistantMessage message = response.choices().get(0).message();
        return new ChatResultDTO(message.reasoningContent(), message.content());
    }

    @SuppressWarnings("all")
    @Override
    public Boolean analyzeChatHistory() {

        //List<DeepSeekProductAnswer> saveList = new ArrayList<>();
        List<WeComRoom> weComRoomList = weComRoomMapper.selectWeComRoomList(new WeComRoom());
        for (WeComRoom weComRoom : weComRoomList) {
            try {
                String productMessage = StreamUtils.copyToString(productResource.getInputStream(), StandardCharsets.UTF_8);
                ChatWithWeComDTO chatWithWeComDTO = new ChatWithWeComDTO(
                        weComRoom.getRoomId(), productMessage, null, null, 0
                );
                StringJoiner joiner = getChatMsg(chatWithWeComDTO);
                ChatCompletionRequest request = getRequest(chatWithWeComDTO, joiner);
                ChatCompletionResponse response = deepSeekClient.chatCompletion(request).execute();
                AssistantMessage message = response.choices().get(0).message();
                //提取json
                DeepSeekProductAnswer answer = JsonExtractor.extractJson(message.content(), DeepSeekProductAnswer.class)
                        .setRoomId(weComRoom.getRoomId())
                        .setAiAnalysisResult(message.content());
                //saveList.add(answer);
                weComRoomMapper.insertCustomerIntention(answer);
            } catch (Exception e) {
                log.error("analyzeChatHistory err ", e);
            }
        }
        //if (CollUtil.isNotEmpty(saveList)) {
        //    mybatisBatchUtils.batchUpdateOrInsert(saveList, WeComRoomMapper.class,
        //            (en, mapper) -> mapper.insertCustomerIntention(en)
        //    );
        //}
        return Boolean.TRUE;
    }

    private @NotNull ChatCompletionRequest getRequest(ChatWithWeComDTO chatWithWeComDTO, StringJoiner joiner) {
        ChatCompletionModel deepSeekModel = ChatCompletionModel.DEEPSEEK_CHAT;
        if (ChatWithWeComDTO.ReasoningEnum.YES.getValue() == chatWithWeComDTO.getReasoning()) {
            deepSeekModel = ChatCompletionModel.DEEPSEEK_REASONER;
        }
        return ChatCompletionRequest.builder()
                .model(deepSeekModel)
                .addUserMessage(joiner + chatWithWeComDTO.getCueWord())
                .build();
    }

    private @NotNull StringJoiner getChatMsg(ChatWithWeComDTO chatWithWeComDTO) {
        //查询
        if (ObjectUtil.isNotEmpty(chatWithWeComDTO.getBeginTime())) {
            chatWithWeComDTO.setBeginTimeNum(
                    getDateNum(chatWithWeComDTO.getBeginTime() + DateFormatConstants.TIME_BEGIN)
            );
        }
        if (ObjectUtil.isNotEmpty(chatWithWeComDTO.getEndTime())) {
            chatWithWeComDTO.setEndTimeNum(
                    getDateNum(chatWithWeComDTO.getEndTime() + DateFormatConstants.TIME_END)
            );
        }
        List<WeComRecord> weComRecords = weComRecordMapper.selectWeComRecordListWithBeginOrEnd(chatWithWeComDTO);
        //拼接
        StringJoiner joiner = new StringJoiner(System.lineSeparator());
        weComRecords.stream().sorted(Comparator.comparing(WeComRecord::getMsgTime))
                .forEach(en -> joiner.add(
                                String.format(
                                        WeComConstants.MSG_TEMPLATE, en.getFromUser(),
                                        getDate(Long.parseLong(en.getMsgTime())),
                                        StrUtil.isNotBlank(en.getContent()) ? en.getContent() : ""
                                )
                        )
                );
        return joiner;
    }

    private String getDate(long time) {
        if (ObjectUtil.isEmpty(time)) {
            return null;
        }
        Instant instant = Instant.ofEpochMilli(time);
        // 转换为北京时间（UTC+8）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateFormatConstants.TIME_FORMAT)
                .withZone(ZoneId.of(DateFormatConstants.TIME_ZONE));
        return formatter.format(instant);
    }

    private long getDateNum(String time) {
        // 替换空格为"T"，适配 ISO 8601 格式
        String input = time.replace(" ", "T");
        // 解析为 UTC 时间
        LocalDateTime utcTime = LocalDateTime.parse(input);

        // 转换为13位毫秒时间戳（UTC） // 明确指定 UTC 时区
        return utcTime.toInstant(ZoneOffset.UTC).toEpochMilli();
    }
}
