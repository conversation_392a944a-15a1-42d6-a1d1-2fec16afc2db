package com.nnb.third.converter.Strategy;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.nnb.third.constant.WeComConstants;
import com.nnb.third.converter.WeComStrategy;
import com.nnb.third.domain.weComDTO.MessageDTO;
import com.nnb.third.domain.weComDTO.TextDTO;
import com.nnb.third.domain.weComDTO.WeComRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-03-31
 * @Version: 1.0
 */
@Component
public class TextStrategy implements WeComStrategy<MessageDTO, String, List<WeComRecord>> {

    @Override
    public boolean supports(String type) {
        return WeComConstants.TEXT.equalsIgnoreCase(type);
    }

    @Override
    public TextDTO getEntity(MessageDTO messageDTO, String JsonData) {
        Map<String, Object> bean = JSONUtil.toBean(JsonData, new TypeReference<Map<String, Object>>() {
        }, true);
        TextDTO textDTO = JSONUtil.toBean(bean.get(messageDTO.getMsgType()).toString(), TextDTO.class);
        BeanUtils.copyProperties(messageDTO, textDTO);
        return textDTO;
    }

    @Override
    public List<WeComRecord> getInsertList(String s) {
        return null;
    }
}
