package com.nnb.third.domain.weComDTO;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 文本消息
 * @Date: 2025-03-31
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TextDTO extends MessageDTO implements Serializable {

    @ApiModelProperty("消息内容。String类型")
    @JsonProperty("content")
    private String content;
}
