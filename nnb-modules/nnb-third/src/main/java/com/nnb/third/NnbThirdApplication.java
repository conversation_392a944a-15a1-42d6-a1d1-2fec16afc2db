package com.nnb.third;

import com.nnb.common.security.annotation.EnableCustomConfig;
import com.nnb.common.security.annotation.EnableRyFeignClients;
import com.nnb.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


/**
 * 第三方服务
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
public class NnbThirdApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(NnbThirdApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  third服务模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "                    _       \n" +
                "                   | |      \n" +
                " _ __ _   _ __ _   | |__    \n" +
                "| '__  | | '__  |  | '_ \\   \n" +
                "| |  | | | |  | | || |_) |  \n" +
                "|_|  |_| |_|  |_|  |_.__/   \n" +
                "                            \n" +
                "                            \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
