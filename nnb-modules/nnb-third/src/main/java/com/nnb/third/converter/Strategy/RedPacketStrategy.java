package com.nnb.third.converter.Strategy;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nnb.third.constant.WeComConstants;

import com.nnb.third.converter.WeComStrategy;
import com.nnb.third.domain.weComDTO.MessageDTO;
import com.nnb.third.domain.weComDTO.RedPacketDTO;
import com.nnb.third.domain.weComDTO.WeComRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-04-02
 * @Version: 1.0
 */
@Slf4j
@Component
public class RedPacketStrategy implements WeComStrategy<MessageDTO, String, List<WeComRecord>> {
    @Override
    public boolean supports(String type) {
        return WeComConstants.RED_PACKET.equalsIgnoreCase(type);
    }

    @Override
    public RedPacketDTO getEntity(MessageDTO messageDTO, String JsonData) {
        Map<String, Object> bean = JSONUtil.toBean(JsonData, new TypeReference<Map<String, Object>>() {
        }, true);
        RedPacketDTO redPacketDTO = new RedPacketDTO();
        try {
            redPacketDTO = new ObjectMapper().readValue(
                    bean.get(messageDTO.getMsgType()).toString(), RedPacketDTO.class
            );
        } catch (Exception e) {
            log.error("RedPacketDTO反序列化失败：", e);
        }
        //copy全属性
        BeanUtils.copyProperties(messageDTO, redPacketDTO);
        return redPacketDTO;
    }

    @Override
    public List<WeComRecord> getInsertList(String s) {
        return Collections.emptyList();
    }
}
