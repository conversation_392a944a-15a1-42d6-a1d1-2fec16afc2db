package com.nnb.third.controller;

import cn.hutool.core.util.RandomUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.third.domain.searchDTO.GetRoomSearch;
import com.nnb.third.domain.weComDTO.WeComRoomDTO;
import com.nnb.third.service.WeComService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-03-24
 * @Version: 1.0
 */
@Api(tags = "WeComController", value = "企业微信控制器")
@RestController
@RequestMapping("/weCom")
@RequiredArgsConstructor
public class WeComController extends BaseController {

    private WeComService weComService;

    @ApiOperation(value = "获取会话内容")
    @GetMapping(value = "/retrieveSessionContent")
    public R<Boolean> retrieveSessionContent() {
        return R.ok(weComService.retrieveSessionContent());
    }

    @ApiOperation(value = "单类型消息入库")
    @GetMapping(value = "/contentInsertDBTest")
    public AjaxResult contentInsertDBTest(@RequestParam String msgJson) {
        return AjaxResult.success(weComService.contentInsertDBTest(msgJson));
    }

    @ApiOperation(value = "根据群id获取群名称")
    @GetMapping(value = "/getGroupChatName")
    public AjaxResult getGroupChatName(@RequestParam String chatId, @RequestParam Integer corpType) {
        return AjaxResult.success(weComService.getGroupChatName(chatId, corpType));
    }

    @ApiOperation(value = "获取所有群id")
    @GetMapping(value = "/getRoomIds")
    public TableDataInfo getRoomIds(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                    @RequestParam String searchKey) {
        GetRoomSearch getRoomSearch = new GetRoomSearch(searchKey, pageNum, pageSize);
        Page<Object> objects = PageHelper.startPage(pageNum, pageSize, null);
        List<WeComRoomDTO> list = weComService.getRoomIds(getRoomSearch);
        list.forEach(en -> en.setId(RandomUtil.randomString(8)));
        return getDataTableAndTotal(list, objects.getTotal());
    }

    @ApiOperation(value = "获取所有企业微信应用")
    @GetMapping(value = "/getWeComApps")
    public AjaxResult getWeComApps() {
        return AjaxResult.success(weComService.getWeComApps());
    }

    @ApiOperation(value = "获取企业 JS-SDK")
    @GetMapping(value = "/get-js-sdk")
    public AjaxResult getJsSdk(@RequestParam Integer corpType,
                               @RequestParam String appName,
                               @RequestParam String url) {
        return AjaxResult.success(weComService.getJsSdk(corpType, appName, url));
    }

    @ApiOperation(value = "获取应用 JS-SDK")
    @GetMapping(value = "/get-app-js-sdk")
    public AjaxResult getAppJsSdk(@RequestParam Integer corpType,
                                  @RequestParam String appName,
                                  @RequestParam String url) {
        return AjaxResult.success(weComService.getAppJsSdk(corpType, appName, url));
    }

    @Autowired
    private void setWeComService(WeComService weComService) {
        this.weComService = weComService;
    }
}
