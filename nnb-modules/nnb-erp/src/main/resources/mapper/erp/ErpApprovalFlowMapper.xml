<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpApprovalFlowMapper">
    
    <resultMap type="ErpApprovalFlow" id="ErpApprovalFlowResult">
        <result property="id"    column="id"    />
        <result property="numApprovalConfigId"    column="num_approval_config_id"    />
        <result property="numSort"    column="num_sort"    />
        <result property="numRoleId"    column="num_role_id"    />
        <result property="numStatus"    column="num_status"    />
        <result property="datCreatedAt"    column="dat_created_at"    />
        <result property="datCreatedBy"    column="dat_created_by"    />
        <result property="datUpdatedAt"    column="dat_updated_at"    />
        <result property="datUpdatedBy"    column="dat_updated_by"    />
    </resultMap>

    <sql id="selectErpApprovalFlowVo">
        select id, num_approval_config_id, num_sort, num_role_id, num_status, dat_created_at, dat_created_by, dat_updated_at, dat_updated_by from erp_approval_flow
    </sql>

    <select id="selectErpApprovalFlowList" parameterType="ErpApprovalFlow" resultMap="ErpApprovalFlowResult">
        <include refid="selectErpApprovalFlowVo"/>
        <where>  
            <if test="numApprovalConfigId != null "> and num_approval_config_id = #{numApprovalConfigId}</if>
            <if test="numSort != null "> and num_sort = #{numSort}</if>
            <if test="numRoleId != null "> and num_role_id = #{numRoleId}</if>
            <if test="numStatus != null "> and num_status = #{numStatus}</if>
            <if test="datCreatedAt != null "> and dat_created_at = #{datCreatedAt}</if>
            <if test="datCreatedBy != null "> and dat_created_by = #{datCreatedBy}</if>
            <if test="datUpdatedAt != null "> and dat_updated_at = #{datUpdatedAt}</if>
            <if test="datUpdatedBy != null "> and dat_updated_by = #{datUpdatedBy}</if>
        </where>
    </select>
    
    <select id="selectErpApprovalFlowById" parameterType="Long" resultMap="ErpApprovalFlowResult">
        <include refid="selectErpApprovalFlowVo"/>
        where id = #{id}
    </select>

    <select id="selectErpApprovalFlowVoList" parameterType="ErpApprovalFlow" resultType="com.nnb.erp.domain.vo.ErpApprovalFlowVo">

        select af.id,
            af.num_approval_config_id,
            af.num_sort,
            af.num_role_id,
            af.num_status,
            af.dat_created_at,
            af.dat_created_by,
            af.dat_updated_at,
            af.dat_updated_by,
            sr.role_key
        from erp_approval_flow af
        left join sys_role sr on af.num_role_id = sr.role_id
        <where>
            <if test="numApprovalConfigId != null "> and af.num_approval_config_id = #{numApprovalConfigId}</if>
            <if test="numSort != null "> and af.num_sort = #{numSort}</if>
            <if test="numRoleId != null "> and af.num_role_id = #{numRoleId}</if>
            <if test="numStatus != null "> and af.num_status = #{numStatus}</if>
        </where>
    </select>

    <insert id="insertErpApprovalFlow" parameterType="ErpApprovalFlow" useGeneratedKeys="true" keyProperty="id">
        insert into erp_approval_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numApprovalConfigId != null">num_approval_config_id,</if>
            <if test="numSort != null">num_sort,</if>
            <if test="numRoleId != null">num_role_id,</if>
            <if test="numStatus != null">num_status,</if>
            <if test="datCreatedAt != null">dat_created_at,</if>
            <if test="datCreatedBy != null">dat_created_by,</if>
            <if test="datUpdatedAt != null">dat_updated_at,</if>
            <if test="datUpdatedBy != null">dat_updated_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numApprovalConfigId != null">#{numApprovalConfigId},</if>
            <if test="numSort != null">#{numSort},</if>
            <if test="numRoleId != null">#{numRoleId},</if>
            <if test="numStatus != null">#{numStatus},</if>
            <if test="datCreatedAt != null">#{datCreatedAt},</if>
            <if test="datCreatedBy != null">#{datCreatedBy},</if>
            <if test="datUpdatedAt != null">#{datUpdatedAt},</if>
            <if test="datUpdatedBy != null">#{datUpdatedBy},</if>
         </trim>
    </insert>

    <update id="updateErpApprovalFlow" parameterType="ErpApprovalFlow">
        update erp_approval_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="numApprovalConfigId != null">num_approval_config_id = #{numApprovalConfigId},</if>
            <if test="numSort != null">num_sort = #{numSort},</if>
            <if test="numRoleId != null">num_role_id = #{numRoleId},</if>
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="datCreatedAt != null">dat_created_at = #{datCreatedAt},</if>
            <if test="datCreatedBy != null">dat_created_by = #{datCreatedBy},</if>
            <if test="datUpdatedAt != null">dat_updated_at = #{datUpdatedAt},</if>
            <if test="datUpdatedBy != null">dat_updated_by = #{datUpdatedBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpApprovalFlowById" parameterType="Long">
        delete from erp_approval_flow where id = #{id}
    </delete>

    <delete id="deleteErpApprovalFlowByIds" parameterType="String">
        delete from erp_approval_flow where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteErpApprovalFlowByConfigId" parameterType="Long">
        delete from erp_approval_flow where num_approval_config_id = #{configId}
    </delete>

    <!-- 通过配置标识与审核步骤获取审核流程标识。 -->
    <select id="getFlowIdByConfigIdAndSort" resultType="Long">
        SELECT eaf.id
        FROM erp_approval_config eac
        LEFT JOIN erp_approval_flow eaf ON eaf.num_approval_config_id = eac.id AND eaf.num_sort = #{sort}
        WHERE eac.id = #{configId} AND eac.num_status = 1;
    </select>

</mapper>