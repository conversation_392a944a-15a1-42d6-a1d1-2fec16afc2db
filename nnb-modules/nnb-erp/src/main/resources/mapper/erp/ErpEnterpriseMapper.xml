<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpEnterpriseMapper">

    <resultMap type="ErpEnterprise" id="ErpEnterpriseResult">
        <result property="id" column="id"/>
        <result property="vcCompanyName" column="vc_company_name"/>
        <result property="vcHistoryName" column="vc_history_name"/>
        <result property="numCorporatePropertyId" column="num_corporate_property_id"/>
        <result property="renewStatus"    column="renew_status"    />
        <result property="level"    column="level"    />
        <result property="kpLimit"    column="kp_limit"    />
        <result property="dataHandoverStatus"    column="data_handover_status"    />
    </resultMap>

    <sql id="selectErpEnterpriseVo">
        select id, vc_company_name, vc_history_name, num_corporate_property_id,renew_status, level, kp_limit, data_handover_status from erp_enterprise
    </sql>

    <select id="selectErpEnterpriseList" parameterType="com.nnb.erp.domain.vo.client.ErpEnterpriseDto"
            resultType="com.nnb.erp.domain.vo.client.ErpClientListResultVo">
        SELECT ec.id,
               ec.num_enterprise_id,
               ec.num_personal_id,
               ec.num_clue_id,
               ec.num_status,
               ec.num_city_id,
               ec.num_type,
               ec.vc_remark,
               ec.num_created_by,
               ec.dat_signing_datecreated_time,
               ec.num_updated_by,
               ec.dat_signing_dateupdated_time,
               ee.num_corporate_property_id,
               ee.vc_company_name,
               ee.vc_history_name,
               eptd.vc_tax_name,
               ec.contactName,
               ec.contactPhone,
               GROUP_CONCAT(DISTINCT ssm.account_user_id order by ssm.id desc) accountUserIdStr,
               GROUP_CONCAT(DISTINCT ssm.increment_user_id order by ssm.id desc) incrementUserIdStr,
               GROUP_CONCAT(DISTINCT epal.label) activitiesLabelStr
        FROM erp_client ec
        LEFT JOIN erp_enterprise ee ON ec.num_enterprise_id=ee.id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id=eptd.num_tax_id
        LEFT JOIN s_service_main ssm on ssm.client_id = ec.id
        LEFT JOIN erp_enterprise_activities_label eeal on eeal.enterprise_id = ee.id
        LEFT JOIN erp_promotional_activities_label epal on epal.id = eeal.activities_label
        <!-- 自定义权限 -->
        <if test="erpEnterprise.dataScope == 2 or erpEnterprise.dataScope == 3 or erpEnterprise.dataScope == 4">
            left join sys_user su1 on su1.user_id = ssm.seller_id
            left join sys_dept sd1 on sd1.dept_id = su1.dept_id

            left join sys_user su2 on su2.user_id = ssm.server_before_user_id
            left join sys_dept sd2 on sd2.dept_id = su2.dept_id

            left join sys_user su5 on su5.user_id = ssm.server_after_user_id
            left join sys_dept sd5 on sd5.dept_id = su5.dept_id

            left join sys_user su3 on su3.user_id = ssm.account_user_id
            left join sys_dept sd3 on sd3.dept_id = su3.dept_id

            left join sys_user su4 on su4.user_id = ssm.increment_user_id
            left join sys_dept sd4 on sd4.dept_id = su4.dept_id
        </if>

        <where>
            <if test="erpEnterprise.vcCompanyName != null  and erpEnterprise.vcCompanyName != ''">and ee.vc_company_name
                like concat('%', #{erpEnterprise.vcCompanyName}, '%')
            </if>
            <if test="erpEnterprise.vcHistoryName != null  and erpEnterprise.vcHistoryName != ''">and ee.vc_history_name
                like concat('%', #{erpEnterprise.vcHistoryName}, '%')
            </if>
            <if test="erpEnterprise.numCorporatePropertyId != null ">and ee.num_corporate_property_id =
                #{erpEnterprise.numCorporatePropertyId}
            </if>
            <if test="erpEnterprise.datSigningDatecreatedTimeStart != null and erpEnterprise.datSigningDatecreatedTimeEnd != null and erpEnterprise.datSigningDatecreatedTimeStart != '' and erpEnterprise.datSigningDatecreatedTimeEnd != ''">
                and ec.dat_signing_datecreated_time &gt;= #{erpEnterprise.datSigningDatecreatedTimeStart}
                and ec.dat_signing_datecreated_time &lt;= #{erpEnterprise.datSigningDatecreatedTimeEnd}
            </if>
            <if test="erpEnterprise.numUserId !=null">and eo.user_id=#{erpEnterprise.numUserId}</if>
            <if test="erpEnterprise.clientId">and ec.id=#{erpEnterprise.clientId}</if>
            <if test="erpEnterprise.cityId != null">
                and ec.num_city_id = #{erpEnterprise.cityId}
            </if>
            <if test="erpEnterprise.serverUserId !=null">and ssm.server_before_user_id=#{erpEnterprise.serverUserId}</if>
            <if test="erpEnterprise.serverUserId !=null">and ssm.server_after_user_id=#{erpEnterprise.serverUserId}</if>
            <if test="erpEnterprise.accountId!=null">and ssm.account_user_id=#{erpEnterprise.accountId}</if>
            <if test="erpEnterprise.incrementUserId!=null">and ssm.increment_user_id=#{erpEnterprise.incrementUserId}</if>
            <if test="erpEnterprise.saleId!=null">and ssm.seller_id=#{erpEnterprise.saleId}</if>
            and ec.num_type = 1
            <!-- 自定义权限 -->
            <if test="erpEnterprise.dataScope == 2">
                and (
                (sd1.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = #{erpEnterprise.roleId} )) or
                (sd2.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = #{erpEnterprise.roleId} )) or
                (sd3.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = #{erpEnterprise.roleId} )) or
                (sd4.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = #{erpEnterprise.roleId} )) or
                (sd5.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = #{erpEnterprise.roleId} ))
                )
            </if>
            <!-- 部门权限 -->
            <if test="erpEnterprise.dataScope == 3">
                and (
                (sd1.dept_id = #{erpEnterprise.deptId}) or
                (sd2.dept_id = #{erpEnterprise.deptId}) or
                (sd3.dept_id = #{erpEnterprise.deptId}) or
                (sd4.dept_id = #{erpEnterprise.deptId}) or
                (sd5.dept_id = #{erpEnterprise.deptId})
                )
            </if>
            <!-- 部门及一下数据 -->
            <if test="erpEnterprise.dataScope == 4">
                and (
                (sd1.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = #{erpEnterprise.deptId} or find_in_set(
                #{erpEnterprise.deptId} , ancestors ))) or
                (sd2.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = #{erpEnterprise.deptId} or find_in_set(
                #{erpEnterprise.deptId} , ancestors ))) or
                (sd3.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = #{erpEnterprise.deptId} or find_in_set(
                #{erpEnterprise.deptId} , ancestors ))) or
                (sd4.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = #{erpEnterprise.deptId} or find_in_set(
                #{erpEnterprise.deptId} , ancestors ))) or
                (sd5.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = #{erpEnterprise.deptId} or find_in_set(
                #{erpEnterprise.deptId} , ancestors )))
                )
            </if>
            <!-- 本人 -->
            <if test="erpEnterprise.dataScope == 5">
                and (
                ssm.seller_id = #{erpEnterprise.sysUserId} or ssm.server_before_user_id = #{erpEnterprise.sysUserId} or
                ssm.server_after_user_id = #{erpEnterprise.sysUserId} or ssm.account_user_id = #{erpEnterprise.sysUserId} or ssm.increment_user_id = #{erpEnterprise.sysUserId}
                )
            </if>
            <if test="erpEnterprise.numUserIdList != null and erpEnterprise.numUserIdList.size >0">
                and ssm.seller_id in
                <foreach collection="erpEnterprise.numUserIdList" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
            </if>
            <if test="erpEnterprise.serverUserIdList != null and erpEnterprise.serverUserIdList.size >0">
                and(
                ssm.server_before_user_id in
                <foreach collection="erpEnterprise.serverUserIdList" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
                or
                ssm.server_after_user_id in
                <foreach collection="erpEnterprise.serverUserIdList" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
                )

            </if>
            <if test="erpEnterprise.accountIdList != null and erpEnterprise.accountIdList.size >0">
                and
                ssm.account_user_id in
                <foreach collection="erpEnterprise.accountIdList" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
            </if>
            <if test="erpEnterprise.incrementUserIdList != null and erpEnterprise.incrementUserIdList.size >0">
                and
                ssm.increment_user_id in
                <foreach collection="erpEnterprise.incrementUserIdList" item="user" open="(" separator="," close=")">
                    #{user}
                </foreach>
            </if>
        </where>
        group by ec.num_enterprise_id
    </select>

    <select id="selectErpEnterpriseById" parameterType="Long" resultMap="ErpEnterpriseResult">
        <include refid="selectErpEnterpriseVo"/>
        where id = #{id}
    </select>

    <insert id="insertErpEnterprise" parameterType="ErpEnterprise" useGeneratedKeys="true" keyProperty="id">
        insert into erp_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vcCompanyName != null">vc_company_name,</if>
            <if test="vcHistoryName != null">vc_history_name,</if>
            <if test="numCorporatePropertyId != null">num_corporate_property_id,</if>
            <if test="renewStatus != null">renew_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vcCompanyName != null">#{vcCompanyName},</if>
            <if test="vcHistoryName != null">#{vcHistoryName},</if>
            <if test="numCorporatePropertyId != null">#{numCorporatePropertyId},</if>
            <if test="renewStatus != null">#{renewStatus},</if>
        </trim>
    </insert>

    <update id="updateErpEnterprise" parameterType="ErpEnterprise">
        update erp_enterprise
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcCompanyName != null">vc_company_name = #{vcCompanyName},</if>
            <if test="vcHistoryName != null">vc_history_name = #{vcHistoryName},</if>
            <if test="numCorporatePropertyId != null">num_corporate_property_id = #{numCorporatePropertyId},</if>
            <if test="renewStatus != null">renew_status = #{renewStatus},</if>
            <if test="dataHandoverStatus != null">data_handover_status = #{dataHandoverStatus},</if>
            <if test="dataHandoverDate != null">data_handover_date = #{dataHandoverDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpEnterpriseById" parameterType="Long">
        delete from erp_enterprise where id = #{id}
    </delete>

    <delete id="deleteErpEnterpriseByIds" parameterType="String">
        delete from erp_enterprise where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取指定企业名称的记录条数。 -->
    <select id="countEnterpriseByName" resultType="Integer">
        SELECT COUNT(ee.id) FROM erp_enterprise ee WHERE ee.vc_company_name = #{companyName}
    </select>
    <select id="selectUser" resultType="com.nnb.system.api.domain.SysUser">
        select * from sys_user where dept_id=#{aLong}

    </select>
    <select id="selectErpEnterpriseLists" resultType="com.nnb.erp.domain.ErpEnterprise">
        select id, vc_company_name, vc_history_name, num_corporate_property_id from erp_enterprise
        <where>
            <if test="id!=null">and id =#{id}</if>
            <if test="vcCompanyName!=null">and vc_company_name LIKE CONCAT('%', #{vcCompanyName}, '%')</if>
        </where>
    </select>

    <select id="selectErpEnterpriseListByName" resultType="com.nnb.erp.domain.ErpEnterprise">
        select id, vc_company_name, vc_history_name, num_corporate_property_id from erp_enterprise
        <where>
             vc_company_name = #{vcCompanyName}
        </where>
    </select>

    <select id="companyList" parameterType="com.nnb.erp.domain.vo.client.ErpEnterpriseDto" resultType="com.nnb.erp.domain.vo.client.ErpClientListResultVo">
        SELECT ec.id,ec.num_enterprise_id,ec.num_personal_id,ec.num_clue_id,ec.num_status,ec.num_city_id,
        ec.num_type,ec.vc_remark,ec.num_created_by,ec.dat_signing_datecreated_time,ec.num_updated_by,
        ec.dat_signing_dateupdated_time,ee.num_corporate_property_id,ee.vc_company_name,ee.vc_history_name,
        bc.vc_customer_name,bc.vc_phone,com.title AS city_name,eptd.vc_tax_name,
        ec.contactName,ec.contactPhone,
        GROUP_CONCAT(DISTINCT ssm.account_user_id order by ssm.id desc) accountUserIdStr,
        GROUP_CONCAT(DISTINCT ssm.increment_user_id order by ssm.id desc) incrementUserIdStr
        FROM erp_client ec
        LEFT JOIN erp_enterprise ee ON ec.num_enterprise_id=ee.id
        LEFT JOIN bd_clue bc ON ec.num_clue_id=bc.id
        LEFT JOIN com_dict_region com ON ec.num_city_id=com.id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id=eptd.num_tax_id
        LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
        LEFT JOIN s_service_main ssm on ssm.client_id = ec.id
        <where>
            <if test="erpEnterprise.vcCompanyName == null or erpEnterprise.vcCompanyName == ''">
                and 1 = 0
            </if>
            <if test="erpEnterprise.vcCompanyName != null  and erpEnterprise.vcCompanyName != ''">
                and ee.vc_company_name = #{erpEnterprise.vcCompanyName}
            </if>
        </where>
        group by ec.num_enterprise_id
    </select>


    <select id="selectOrderIdsByCompanyName" resultType="java.lang.Integer">
        select eo.id
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        where ee.vc_company_name like CONCAT('%', #{enterpriseName}, '%');
    </select>

    <select id="selectEnIdByCompanyName" resultType="java.lang.Integer">
        select ee.id
        from erp_enterprise ee
        where ee.vc_company_name like CONCAT('%', #{enterpriseName}, '%');
    </select>

    <select id="selectOrderIdsByLegalName" resultType="java.lang.Integer">
        select eo.id
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
                 LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id and eeu.num_user_type = 1
        where eeu.vc_name like CONCAT('%', #{legalPersonName}, '%');
    </select>

    <select id="selectEnIdByLegalName" resultType="java.lang.Integer">
        select ee.id
        from  erp_enterprise ee
                 LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
                 LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id and eeu.num_user_type = 1
        where eeu.vc_name like CONCAT('%', #{legalPersonName}, '%');
    </select>

    <select id="selectOrderIdsByContactName" resultType="java.lang.Integer">
        select eo.id
        from erp_orders eo
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        where ec.contactName like CONCAT('%', #{contactName}, '%');
    </select>

    <select id="selectEnIdByContactName" resultType="java.lang.Integer">
        select ec.num_enterprise_id
        from erp_client ec
        where ec.contactName like CONCAT('%', #{contactName}, '%');
    </select>

    <select id="selectOrderIdsByCityId" resultType="java.lang.Integer">
        select ec.num_enterprise_id
        from erp_client ec
        where ec.num_city_id = #{clientCityId}
    </select>
    <select id="selectOrderIdsByTaxId" resultType="java.lang.Integer">
        select eo.id
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        where eptd.num_tax_id = #{corporatePropertyId}
    </select>

    <select id="selectEnIdByTaxId" resultType="java.lang.Integer">
        select ee.id
        from erp_enterprise ee
                 LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        where eptd.num_tax_id = #{corporatePropertyId}
    </select>

    <select id="selectOrderIdsByContract" resultType="java.lang.Integer">
        select eo.id
        from erp_orders eo
                left JOIN erp_contract et on et.id = eo.num_contract_id
        where et.num_contract_main_id = #{contract} and eo.num_contract_id != 0
        union all
        select eo.id
        from erp_orders eo
        where eo.contract_subject = #{contract}
    </select>
    <select id="selectCompanyNameByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id as orderId,   -- 订单号
               ee.vc_company_name as enterpriseName -- 企业名称
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectCompanyNameByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
                ee.id as enterpriseId,
                ee.vc_company_name as enterpriseName -- 企业名称
        from erp_enterprise ee
        where ee.id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectCompanyNameByClientIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
                ec.id              as clientId,
                ee.vc_company_name as enterpriseName -- 企业名称
        from erp_enterprise ee
        left join erp_client ec on ee.id = ec.num_enterprise_id
        where ec.id in (
        <foreach collection="clientIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectCityNameByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id     as orderId,
               cdr.title as clientCityName -- 城市
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectCityNameByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
            ec.num_enterprise_id     as enterpriseId,
            cdr.title as clientCityName -- 城市
        from erp_client ec
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        where ec.num_enterprise_id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectCityNameByClientIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
        ec.id     as clientId,
        cdr.title as clientCityName -- 城市
        from erp_client ec
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        where ec.id in (
        <foreach collection="clientIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectLegalPersonByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id       as orderId,
               eeu.vc_name as legalPersonName -- 企业法人
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
                 LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id and eeu.num_user_type = 1
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectLegalPersonByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select ee.id       as enterpriseId,
        eeu.vc_name as legalPersonName -- 企业法人
        from erp_enterprise ee
        LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id and eeu.num_user_type = 1
        where ee.id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectContactByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id          as orderId,
               ec.contactName as contactName, -- 企业联系人
               ec.contactPhone as contactPhone -- 企业联系人
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectContactByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
        ec.num_enterprise_id     as enterpriseId,
        ec.contactName as contactName, -- 企业联系人
        ec.contactPhone as contactPhone -- 企业联系人
        from erp_client ec
        where ec.num_enterprise_id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectAccountContactByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select
        ec.num_enterprise_id     as enterpriseId,
        ec.contactName as accountContactName, -- 企业联系人
        ec.contactPhone as accountcontactPhone -- 企业联系人
        from erp_client ec
        where
        label = 1
        and ec.num_enterprise_id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectTaxByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id                                         as orderId,
               group_concat(eptd.vc_tax_name SEPARATOR '，') as corporatePropertyName -- 企业性质
        from erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectTaxByEnIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select ee.id                                         as enterpriseId,
        group_concat(eptd.vc_tax_name SEPARATOR '，') as corporatePropertyName -- 企业性质
        from erp_enterprise ee
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        where ee.id in (
        <foreach collection="enIds" item="id" separator=",">
            #{id}
        </foreach>
        )
        group by ee.id
    </select>

    <select id="selectTimeByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select eo.id                                                as orderId,
               DATE_FORMAT(eed.dat_begin_bookkeep_time, '%Y-%m-%d') AS bizBookkeepMonthBegin,  -- 记账开始月
               DATE_FORMAT(eed.dat_end_bookkeep_time, '%Y-%m-%d')   AS bizBookkeepMonthEnd    -- 记账截至月
        from  erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
        where eo.id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        group by eo.id
    </select>

    <select id="selectErpEnterpriseListByIds" resultMap="ErpEnterpriseResult">
        select id  , vc_company_name  from erp_enterprise
        WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectContactByEnIdsFromOrder" resultType="com.nnb.erp.domain.vo.ErpClientForOrderDetailVO">
        SELECT eo.id                                  AS `orderId`,
               bc.vc_customer_name                    AS 'clueCustomerName',
               bc.vc_phone                            AS 'clueCustomerPhone',
               ec.contactName                         AS 'contactName',
               ec.contactPhone                        AS 'contactPhone',
               eo.commit_order_type                   AS 'commitOrderType'
        FROM erp_orders eo
                 LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                 LEFT JOIn erp_enterprise ee ON ee.id = ec.num_enterprise_id
                 LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        WHERE eo.id in (
            <foreach collection="orderIdList" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>

    <select id="selectCityNameByCityIds" resultType="java.util.Map">
        select id, title as `name`
        from com_dict_region
        where id in (
        <foreach collection="cityIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
    <select id="selectClientIdByCompanyName" resultType="java.lang.Integer">
        select ec.id
        from erp_enterprise ee
        left join erp_client ec on ee.id = ec.num_enterprise_id
        where ee.vc_company_name like CONCAT('%', #{enterpriseName}, '%');
    </select>
    <select id="selectClientIdByContactName" resultType="java.lang.Integer">
        select ec.id
        from erp_client ec
        where ec.contactName like CONCAT('%', #{contactName}, '%');
    </select>
    <select id="selectOrderIdByAmount" resultType="java.lang.Integer">
        select id
        from erp_orders
        <where>
            <if test="realAmountStart != null">
                and num_pay_price &gt;= #{realAmountStart}
            </if>
            <if test="realAmountEnd != null">
                and num_pay_price &lt;= #{realAmountEnd}
            </if>
        </where>
    </select>

    <select id="selectErpEnterpriseListLimit" resultMap="ErpEnterpriseResult">
        <include refid="selectErpEnterpriseVo"/>
        <where>
            <if test="vcCompanyName!=null">and vc_company_name LIKE CONCAT('%', #{companyName}, '%')</if>
        </where>
        limit 10
    </select>

</mapper>
