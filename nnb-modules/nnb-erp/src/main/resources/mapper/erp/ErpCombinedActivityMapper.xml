<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCombinedActivityMapper">

    <resultMap type="ErpCombinedActivity" id="ErpCombinedActivityResult">
        <result property="id" column="id"/>
        <result property="vcName" column="vc_name"/>
        <result property="numStatus" column="num_status"/>
        <result property="numAreaId" column="num_area_id"/>
        <result property="numChannel" column="num_channel"/>
        <result property="numPrice" column="num_price"/>
        <result property="numType" column="num_type"/>
        <result property="vcCombinedProduct" column="vc_combined_product"/>
        <result property="vcComplimentaryProduct" column="vc_complimentary_product"/>
        <result property="datStartTime" column="dat_start_time"/>
        <result property="datEndTime" column="dat_end_time"/>
        <result property="vcRemark" column="vc_remark"/>
        <result property="datCreatedTime" column="dat_created_time"/>
        <result property="numCreatedBy" column="num_created_by"/>
        <result property="datUpdatedTime" column="dat_updated_time"/>
        <result property="datUpdatedBy" column="dat_updated_by"/>
        <result property="status" column="status"/>
        <result property="channel" column="channel"/>
        <result property="type" column="type"/>
        <result property="cityName" column="city_name"/>
    </resultMap>

    <sql id="selectErpCombinedActivityVo">
        select id,
               vc_name,
               num_status,
               num_area_id,
               num_channel,
               num_price,
               num_type,
               vc_combined_product,
               vc_complimentary_product,
               dat_start_time,
               dat_end_time,
               vc_remark,
               dat_created_time,
               num_created_by,
               dat_updated_time,
               dat_updated_by,
               CONCAT(dat_start_time, "至", dat_end_time) as valiTime
        from erp_combined_activity
    </sql>
    <sql id="selectErpCombinedActivityVoList">
        SELECT distinct (eca.id),
               eca.vc_name,
                   eca.num_status,
               eca.num_area_id,
               CASE
                   eca.num_channel
                   WHEN 0 THEN
                       "全部"
                   WHEN 1 THEN
                       "外部使用"
                   WHEN 2 THEN
                       "内部使用"
                   END   AS numChannel,
               eca.num_price,
               CASE
                   eca.num_type
                   WHEN 0 THEN
                       "组合"
                   ELSE "赠送"
                   END   AS numType,
               eca.vc_combined_product,
               eca.vc_complimentary_product,
               eca.dat_start_time,
               eca.dat_end_time,
               eca.vc_remark,
               eca.dat_created_time,
               eca.num_created_by,
               eca.dat_updated_time,
               eca.dat_updated_by,
            <!-- com.title   AS city_name,-->

               com.title  as numAreaName,
               CONCAT(eca.dat_start_time, "至", eca.dat_end_time) as valiTime

        FROM erp_combined_activity AS eca
                 LEFT JOIN com_dict_region AS com ON eca.num_area_id = com.id
                 LEFT JOIN erp_combined_activity_product ac on ac.num_combined_activity_id = eca.id
                 LEFT JOIN erp_product_region AS co ON eca.num_area_id = co.num_area_id


    </sql>

    <select id="selectErpCombinedActivityList" parameterType="ErpCombinedActivity"
            resultMap="ErpCombinedActivityResult">
        <include refid="selectErpCombinedActivityVoList"/>
        <where>
            <if test="vcName != null  and vcName != ''">and eca.vc_name like concat('%', #{vcName}, '%')</if>
            <if test="numStatus != null ">and eca.num_status = #{numStatus}</if>
            <if test="numAreaId != null ">and eca.num_area_id = #{numAreaId}</if>
            <if test="numChannel != null ">and eca.num_channel = #{numChannel}</if>
            <if test="numPrice != null ">and eca.num_price = #{numPrice}</if>
            <if test="numType != null ">and eca.num_type = #{numType}</if>
            <if test="vcCombinedProduct != null  and vcCombinedProduct != ''">and eca.vc_combined_product =
                #{vcCombinedProduct}
            </if>
            <if test="vcComplimentaryProduct != null  and vcComplimentaryProduct != ''">and eca.vc_complimentary_product
                = #{vcComplimentaryProduct}
            </if>
            <if test="datStartTime != null ">and eca.dat_start_time = #{datStartTime}</if>
            <if test="datEndTime != null ">and eca.dat_end_time = #{datEndTime}</if>
            <if test="vcRemark != null  and vcRemark != ''">and eca.vc_remark = #{vcRemark}</if>
            <if test="datCreatedTime != null ">and eca.dat_created_time = #{datCreatedTime}</if>
            <if test="numCreatedBy != null ">and eca.num_created_by = #{numCreatedBy}</if>
            <if test="datUpdatedTime != null ">and eca.dat_updated_time = #{datUpdatedTime}</if>
            <if test="datUpdatedBy != null ">and eca.dat_updated_by = #{datUpdatedBy}</if>
        </where>
        ORDER BY eca.id DESC
    </select>

    <select id="selectErpCombinedActivityById" parameterType="Long" resultMap="ErpCombinedActivityResult">
        <include refid="selectErpCombinedActivityVo"/>
        where id = #{id}
    </select>




    <select id="selectlistProductPriceCount" resultType="java.math.BigDecimal">

            SELECT  sum(ac.num_price) as productPriceCount  FROM erp_combined_activity AS eca LEFT JOIN
                                                                 erp_combined_activity_product ac on ac.num_combined_activity_id=eca.id
            where eca.id=#{id}

    </select>
    <select id="selectCombinedActivityId" resultType="com.nnb.erp.domain.ErpCombinedActivity">

            select cs.id,cs.dat_end_time
            from erp_combined_activity cs
            where  cs.num_status = 1

    </select>

    <select id="selectlistCity" resultType="com.nnb.erp.domain.dto.ErpProductRegionDto">
            select vc_area_name as numAreaName, num_area_id as numAreaId
            from erp_product_region;

    </select>
    <select id="selectlistDept" resultType="com.nnb.erp.domain.ErpCombineActivityDept">
            select dept_id As numDeptId, dept_name as numDept from sys_dept
            <where>
                <if test="numDept != null ">and dept_name like concat('%', #{numDept}, '%')</if>
            </where>
    </select>
    <select id="selectErpCombinedActivity" resultType="com.nnb.erp.domain.CombunedActivityDto">
        select detail.num_product_id        as numProductId,
               class.vc_classification_name as vcClassificationName,
               type.vc_type_name            as vcTypeName,
               name.vc_product_name         as vcProductName,
               ec.num_price             as numPrice,
               ec.num_activity_price        as numActivityPrice,
               es.vc_service_name           as vcServiceName
        from erp_product_detail detail
                 left join erp_product_configuration configuration on configuration.product_id = detail.num_product_id
                 left join erp_product_name name on detail.num_name_id = name.num_name_id
                 left join erp_product_type type on name.num_type_id = type.num_type_id
                 left join erp_product_classification class on type.num_classification_id = class.num_classification_id
                 LEFT JOIN  erp_combined_activity_product ec on ec.num_product_id=detail.num_product_id
                 LEFT JOIN erp_product_service es on es.num_service_id=detail.num_service_id
                 where ec.num_combined_activity_id=#{id}
                 AND (configuration.product_price = ec.num_price or configuration.discount_amount = ec.num_price)
    </select>
    <select id="selectErpCombinedActivityDept" resultType="com.nnb.erp.domain.ErpActivityDept">
        select num_dept_id as numDeptId,sd.dept_name as numDept from erp_activity_dept ed
            LEFT JOIN sys_dept sd on sd.dept_id=ed.num_dept_id
where ed.num_combined_activity_id=#{id}
    </select>
    <select id="selectActivityCity" resultType="java.lang.String">
            SELECT DISTINCT
                com.vc_area_name
            FROM
                erp_combined_activity_product ca
                    LEFT JOIN erp_combined_activity eca ON ca.num_combined_activity_id = eca.id
                    LEFT JOIN erp_product_region AS com ON com.num_region_dict_id =  eca.num_area_id

            WHERE eca.id=#{id}

    </select>
    <select id="selectErpCombinedActivitys" resultType="com.nnb.erp.domain.CombunedActivityDto">
        select detail.num_product_id        as numProductId,
               class.vc_classification_name as vcClassificationName,
               type.vc_type_name            as vcTypeName,
               name.vc_product_name         as vcProductName,
               detail.num_price             as numPrice,
               ec.num_activity_price        as numActivityPrice
        from erp_product_detail detail
                 left join erp_product_name name on detail.num_name_id = name.num_name_id
                 left join erp_product_type type on name.num_type_id = type.num_type_id
                 left join erp_product_classification class on type.num_classification_id = class.num_classification_id
                 LEFT JOIN  erp_combined_activity_product ec on ec.num_product_id=detail.num_product_id
        where ec.num_combined_activity_id=#{id}
    </select>
    <select id="activityForConfirmOrderVOList" resultType="com.nnb.erp.domain.ErpCombinedActivity">
    select vc_name,num_area_id,num_status from erp_combined_activity
        WHERE id IN (
        <foreach collection="activityIdList" item="activityId" separator=",">
            #{activityId}
        </foreach>
            )
    </select>
    <select id="selectErpCombined" resultType="com.nnb.erp.domain.ErpCombinedActivity">
        <include refid="selectErpCombinedActivityVo"/>
        where id = #{id}
    </select>
    <select id="selectErpCombinedActivityDto" resultType="com.nnb.erp.domain.CombunedActivityDto">
        select detail.num_product_id        as numProductId,
               class.vc_classification_name as vcClassificationName,
               type.vc_type_name            as vcTypeName,
               name.vc_product_name         as vcProductName,
               configuration.product_price             as numPrice,
               ec.num_activity_price        as numActivityPrice
        from erp_product_detail detail
                 left join erp_product_configuration configuration on configuration.product_id = detail.num_product_id
                 left join erp_product_name name on detail.num_name_id = name.num_name_id
                 left join erp_product_type type on name.num_type_id = type.num_type_id
                 left join erp_product_classification class on type.num_classification_id = class.num_classification_id
                 LEFT JOIN  erp_combined_activity_product ec on ec.num_product_id=detail.num_product_id
        <where>
            <if test="id!=null">and ec.num_combined_activity_id=#{id}</if>
            <if test="deptId!=null">and FIND_IN_SET(#{deptId}, configuration.dept_id)</if>
        </where>
    </select>


    <insert id="insertErpCombinedActivity" parameterType="ErpCombinedActivity" useGeneratedKeys="true" keyProperty="id">
        insert into erp_combined_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vcName != null">vc_name,</if>
            <if test="numStatus != null">num_status,</if>
            <if test="numAreaId != null">num_area_id,</if>
            <if test="numChannel != null">num_channel,</if>
            <if test="numPrice != null">num_price,</if>
            <if test="numType != null">num_type,</if>
            <if test="vcCombinedProduct != null">vc_combined_product,</if>
            <if test="vcComplimentaryProduct != null">vc_complimentary_product,</if>
            <if test="datStartTime != null">dat_start_time,</if>
            <if test="datEndTime != null">dat_end_time,</if>
            <if test="vcRemark != null">vc_remark,</if>
            <if test="datCreatedTime != null">dat_created_time,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="datUpdatedTime != null">dat_updated_time,</if>
            <if test="datUpdatedBy != null">dat_updated_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vcName != null">#{vcName},</if>
            <if test="numStatus != null">#{numStatus},</if>
            <if test="numAreaId != null">#{numAreaId},</if>
            <if test="numChannel != null">#{numChannel},</if>
            <if test="numPrice != null">#{numPrice},</if>
            <if test="numType != null">#{numType},</if>
            <if test="vcCombinedProduct != null">#{vcCombinedProduct},</if>
            <if test="vcComplimentaryProduct != null">#{vcComplimentaryProduct},</if>
            <if test="datStartTime != null">#{datStartTime},</if>
            <if test="datEndTime != null">#{datEndTime},</if>
            <if test="vcRemark != null">#{vcRemark},</if>
            <if test="datCreatedTime != null">#{datCreatedTime},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="datUpdatedTime != null">#{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">#{datUpdatedBy},</if>
        </trim>
    </insert>


    <insert id="insertErpCombinDept" useGeneratedKeys="true" keyProperty="id">
        insert into erp_activity_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId !=null">num_combined_activity_id,</if>
            <if test="numDeptId !=null">num_dept_id,</if>
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId !=null">#{numCombinedActivityId},</if>
            <if test="numDeptId !=null"> #{numDeptId}, </if>
        </trim>
    </insert>


        <insert id="insertActivityDept" useGeneratedKeys="true" keyProperty="id">
            insert into erp_activity_dept
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="numCombinedActivityId !=null">num_combined_activity_id,</if>
                <if test="numDeptId !=null">num_dept_id,</if>
            </trim>

            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="numCombinedActivityId !=null">#{numCombinedActivityId},</if>
                <if test="numDeptId !=null">#{numDeptId},</if>
            </trim>
        </insert>
    <insert id="insertErpCombinedAProduct" useGeneratedKeys="true" keyProperty="id">
        insert into erp_combined_activity_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId !=null">num_combined_activity_id,</if>
            <if test="numProductId !=null">num_product_id,</if>
            <if test="vcProductName !=null">vc_product_name, </if>
            <if test="numType !=null">num_type,</if>
            <if test="numPrice !=null">num_price,</if>
            <if test="numActivityPrice !=null">num_activity_price,</if>
            <if test="numProductCount !=null">num_product_count,</if>
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId !=null">#{numCombinedActivityId},</if>
            <if test="numProductId !=null">#{numProductId},</if>
            <if test="vcProductName !=null">#{vcProductName}, </if>
            <if test="numType !=null">#{numType},</if>
            <if test="numPrice !=null">#{numPrice},</if>
            <if test="numActivityPrice !=null">#{numActivityPrice},</if>
            <if test="numProductCount !=null">#{numProductCount},</if>
        </trim>
    </insert>


    <update id="updateErpCombinedActivity" parameterType="ErpCombinedActivity">
        update erp_combined_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcName != null">vc_name = #{vcName},</if>
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="numAreaId != null">num_area_id = #{numAreaId},</if>
            <if test="numChannel != null">num_channel = #{numChannel},</if>
            <if test="numPrice != null">num_price = #{numPrice},</if>
            <if test="numType != null">num_type = #{numType},</if>
            <if test="vcCombinedProduct != null">vc_combined_product = #{vcCombinedProduct},</if>
            <if test="vcComplimentaryProduct != null">vc_complimentary_product = #{vcComplimentaryProduct},</if>
            <if test="datStartTime != null">dat_start_time = #{datStartTime},</if>
            <if test="datEndTime != null">dat_end_time = #{datEndTime},</if>
            <if test="vcRemark != null">vc_remark = #{vcRemark},</if>
            <if test="datCreatedTime != null">dat_created_time = #{datCreatedTime},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datUpdatedTime != null">dat_updated_time = #{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">dat_updated_by = #{datUpdatedBy},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateErpDept">
        update erp_combined_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="numDeptId !=null">num_dept_id=#{numDeptId}</if>
            <if test="numCombinedActivityId !=null">num_combined_activity_id=#{numCombinedActivityId}</if>
        </trim>
    </update>
    <update id="updateErpCombinedDept">
        update  erp_activity_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="numDeptId !=null">num_dept_id=#{numDeptId},</if>
            <if test="numCombinedActivityId !=null">num_combined_activity_id=#{numCombinedActivityId},</if>
        </trim>
    </update>
    <update id="updateErpCombinedAProduct">
        update erp_combined_activity_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="numCombinedActivityId !=null">num_combined_activity_id=#{numCombinedActivityId},</if>
            <if test="numProductId !=null">num_product_id=#{numProductId},</if>
            <if test="vcProductName !=null">vc_product_name=#{vcProductName}, </if>
            <if test="numType !=null">num_type=#{numType},</if>
            <if test="numPrice !=null">num_price=#{numPrice},</if>
            <if test="numActivityPrice !=null">num_activity_price=#{numActivityPrice},</if>
            <if test="numProductCount !=null">num_product_count=#{numProductCount},</if>
        </trim>

    </update>
    <update id="updateErpComStatus" parameterType="com.nnb.erp.domain.ErpCombinedActivity">
        update erp_combined_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcName != null">vc_name = #{vcName},</if>
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="numAreaId != null">num_area_id = #{numAreaId},</if>
            <if test="numChannel != null">num_channel = #{numChannel},</if>
            <if test="numPrice != null">num_price = #{numPrice},</if>
            <if test="numType != null">num_type = #{numType},</if>
            <if test="vcCombinedProduct != null">vc_combined_product = #{vcCombinedProduct},</if>
            <if test="vcComplimentaryProduct != null">vc_complimentary_product = #{vcComplimentaryProduct},</if>
            <if test="datStartTime != null">dat_start_time = #{datStartTime},</if>
            <if test="datEndTime != null">dat_end_time = #{datEndTime},</if>
            <if test="vcRemark != null">vc_remark = #{vcRemark},</if>
            <if test="datCreatedTime != null">dat_created_time = #{datCreatedTime},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datUpdatedTime != null">dat_updated_time = #{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">dat_updated_by = #{datUpdatedBy},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteErpCombinedActivityById" parameterType="Long">
        delete
        from erp_combined_activity ea   where ea.id = #{id}
    </delete>

    <delete id="deleteErpCombinedActivityByIds" parameterType="String">
        delete from erp_combined_activity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteErpCombinedDept" parameterType="Long">
        delete from erp_activity_dept where num_combined_activity_id =#{id}

    </delete>
    <delete id="deleteErpCombinedActivityProduct">
        DELETE  FROM erp_combined_activity_product WHERE num_combined_activity_id=#{id}
    </delete>

</mapper>
