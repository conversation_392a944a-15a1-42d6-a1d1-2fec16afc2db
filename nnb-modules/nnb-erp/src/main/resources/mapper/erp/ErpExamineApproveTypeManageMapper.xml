<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpExamineApproveTypeManageMapper">
    
    <resultMap type="ErpExamineApproveTypeManage" id="ErpExamineApproveTypeManageResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentId"    column="parent_id"    />
        <result property="memo"    column="memo"    />
        <result property="typeLevel"    column="type_level"    />
        <result property="needPrint"    column="need_print"    />
        <result property="needPay"    column="need_pay"    />
        <result property="budgetId"    column="budget_id"    />
    </resultMap>

    <sql id="selectErpExamineApproveTypeManageVo">
        select id, name, status, created_user, created_time, update_user, update_time, parent_id, memo, type_level, need_print, need_pay, budget_id from erp_examine_approve_type_manage
    </sql>

    <select id="selectErpExamineApproveTypeManageList" parameterType="ErpExamineApproveTypeManage" resultMap="ErpExamineApproveTypeManageResult">
        <include refid="selectErpExamineApproveTypeManageVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createdUser != null "> and created_user = #{createdUser}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="typeLevel != null "> and type_level = #{typeLevel}</if>
            <if test="needPrint != null "> and need_print = #{needPrint}</if>
            <if test="needPay != null "> and need_pay = #{needPay}</if>
        </where>
    </select>
    
    <select id="selectErpExamineApproveTypeManageById" parameterType="Long" resultMap="ErpExamineApproveTypeManageResult">
        <include refid="selectErpExamineApproveTypeManageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpExamineApproveTypeManage" parameterType="ErpExamineApproveTypeManage" useGeneratedKeys="true" keyProperty="id">
        insert into erp_examine_approve_type_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="status != null">status,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="memo != null">memo,</if>
            <if test="typeLevel != null">type_level,</if>
            <if test="needPrint != null">need_print,</if>
            <if test="needPay != null">need_pay,</if>
            <if test="budgetId != null">budget_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="memo != null">#{memo},</if>
            <if test="typeLevel != null">#{typeLevel},</if>
            <if test="needPrint != null">#{needPrint},</if>
            <if test="needPay != null">#{needPay},</if>
            <if test="budgetId != null">#{budgetId},</if>
         </trim>
    </insert>

    <update id="updateErpExamineApproveTypeManage" parameterType="ErpExamineApproveTypeManage">
        update erp_examine_approve_type_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="typeLevel != null">type_level = #{typeLevel},</if>
            <if test="needPrint != null">need_print = #{needPrint},</if>
            <if test="needPay != null">need_pay = #{needPay},</if>
            budget_id = #{budgetId},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpExamineApproveTypeManageById" parameterType="Long">
        delete from erp_examine_approve_type_manage where id = #{id}
    </delete>

    <delete id="deleteErpExamineApproveTypeManageByIds" parameterType="String">
        delete from erp_examine_approve_type_manage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>