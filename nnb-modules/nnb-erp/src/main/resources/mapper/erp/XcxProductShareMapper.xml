<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.XcxProductShareMapper">
    
    <resultMap type="XcxProductShare" id="XcxProductShareResult">
        <result property="id"    column="id"    />
        <result property="openId"    column="open_id"    />
        <result property="createdTime"    column="created_time"    />
        <result property="content"    column="content"    />
        <result property="encryption"    column="encryption"    />
    </resultMap>

    <sql id="selectXcxProductShareVo">
        select id, open_id, created_time, content, encryption from xcx_product_share
    </sql>

    <select id="selectXcxProductShareList" parameterType="XcxProductShare" resultMap="XcxProductShareResult">
        <include refid="selectXcxProductShareVo"/>
        <where>  
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="encryption != null  and encryption != ''"> and encryption = #{encryption}</if>
        </where>
    </select>
    
    <select id="selectXcxProductShareById" parameterType="Long" resultMap="XcxProductShareResult">
        <include refid="selectXcxProductShareVo"/>
        where id = #{id}
    </select>

    <select id="getByEncryption" resultMap="XcxProductShareResult">
        <include refid="selectXcxProductShareVo"/>
        where encryption = #{encryption}
    </select>
        
    <insert id="insertXcxProductShare" parameterType="XcxProductShare" useGeneratedKeys="true" keyProperty="id">
        insert into xcx_product_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">open_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="content != null">content,</if>
            <if test="encryption != null">encryption,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">#{openId},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="content != null">#{content},</if>
            <if test="encryption != null">#{encryption},</if>
         </trim>
    </insert>

    <update id="updateXcxProductShare" parameterType="XcxProductShare">
        update xcx_product_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="content != null">content = #{content},</if>
            <if test="encryption != null">encryption = #{encryption},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXcxProductShareById" parameterType="Long">
        delete from xcx_product_share where id = #{id}
    </delete>

    <delete id="deleteXcxProductShareByIds" parameterType="String">
        delete from xcx_product_share where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>