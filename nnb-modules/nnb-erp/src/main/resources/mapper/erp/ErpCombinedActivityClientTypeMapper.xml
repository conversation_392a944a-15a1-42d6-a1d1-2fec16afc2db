<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCombinedActivityClientTypeMapper">
    
    <resultMap type="ErpCombinedActivityClientType" id="ErpCombinedActivityClientTypeResult">
        <result property="id"    column="id"    />
        <result property="numCombinedActivity"    column="num_combined_activity"    />
        <result property="numMemberType"    column="num_member_type"    />
    </resultMap>

    <sql id="selectErpCombinedActivityClientTypeVo">
        select id, num_combined_activity, num_member_type from erp_combined_activity_client_type
    </sql>

    <select id="selectErpCombinedActivityClientTypeList" parameterType="ErpCombinedActivityClientType" resultMap="ErpCombinedActivityClientTypeResult">
        <include refid="selectErpCombinedActivityClientTypeVo"/>
        <where>  
            <if test="numCombinedActivity != null "> and num_combined_activity = #{numCombinedActivity}</if>
            <if test="numMemberType != null "> and num_member_type = #{numMemberType}</if>
        </where>
    </select>
    
    <select id="selectErpCombinedActivityClientTypeById" parameterType="Long" resultMap="ErpCombinedActivityClientTypeResult">
        <include refid="selectErpCombinedActivityClientTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpCombinedActivityClientType" parameterType="ErpCombinedActivityClientType" useGeneratedKeys="true" keyProperty="id">
        insert into erp_combined_activity_client_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivity != null">num_combined_activity,</if>
            <if test="numMemberType != null">num_member_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivity != null">#{numCombinedActivity},</if>
            <if test="numMemberType != null">#{numMemberType},</if>
         </trim>
    </insert>

    <update id="updateErpCombinedActivityClientType" parameterType="ErpCombinedActivityClientType">
        update erp_combined_activity_client_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="numCombinedActivity != null">num_combined_activity = #{numCombinedActivity},</if>
            <if test="numMemberType != null">num_member_type = #{numMemberType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpCombinedActivityClientTypeById" parameterType="Long">
        delete from erp_combined_activity_client_type where id = #{id}
    </delete>

    <delete id="deleteErpCombinedActivityClientTypeByIds" parameterType="String">
        delete from erp_combined_activity_client_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>