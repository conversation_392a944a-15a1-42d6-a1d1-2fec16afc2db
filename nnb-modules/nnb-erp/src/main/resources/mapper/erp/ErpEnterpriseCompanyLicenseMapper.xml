<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpEnterpriseCompanyLicenseMapper">
    
    <resultMap type="ErpEnterpriseCompanyLicense" id="ErpEnterpriseCompanyLicenseResult">
        <result property="numId"    column="num_id"    />
        <result property="numErpEnterpriseCompanyId"    column="num_erp_enterprise_company_id"    />
        <result property="vcImgAddress"    column="vc_img_address"    />
    </resultMap>

    <sql id="selectErpEnterpriseCompanyLicenseVo">
        select num_id, num_erp_enterprise_company_id, vc_img_address from erp_enterprise_company_license
    </sql>

    <select id="selectErpBizCompanyLicenseList" parameterType="ErpEnterpriseCompanyLicense" resultMap="ErpEnterpriseCompanyLicenseResult">
        <include refid="selectErpEnterpriseCompanyLicenseVo"/>
        <where>  
            <if test="numErpEnterpriseCompanyId != null "> and num_erp_enterprise_company_id = #{numErpEnterpriseCompanyId}</if>
            <if test="vcImgAddress != null  and vcImgAddress != ''"> and vc_img_address = #{vcImgAddress}</if>
        </where>
    </select>

    <select id="selectErpBizCompanyLicenseByNumIdList" parameterType="ErpEnterpriseCompanyLicense" resultMap="ErpEnterpriseCompanyLicenseResult">
        <include refid="selectErpEnterpriseCompanyLicenseVo"/>
        where num_erp_enterprise_company_id in (
        <foreach collection="idList" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
    
    <select id="selectErpBizCompanyLicenseByNumId" parameterType="Long" resultMap="ErpEnterpriseCompanyLicenseResult">
        <include refid="selectErpEnterpriseCompanyLicenseVo"/>
        where num_id = #{numId}
    </select>
        
    <insert id="insertErpBizCompanyLicense" parameterType="ErpEnterpriseCompanyLicense" useGeneratedKeys="true" keyProperty="numId">
        insert into erp_enterprise_company_license
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numErpEnterpriseCompanyId != null">num_erp_enterprise_company_id,</if>
            <if test="vcImgAddress != null">vc_img_address,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numErpEnterpriseCompanyId != null">#{numErpEnterpriseCompanyId},</if>
            <if test="vcImgAddress != null">#{vcImgAddress},</if>
         </trim>
    </insert>

    <update id="updateErpBizCompanyLicense" parameterType="ErpEnterpriseCompanyLicense">
        update erp_enterprise_company_license
        <trim prefix="SET" suffixOverrides=",">
            <if test="numErpEnterpriseCompanyId != null">num_erp_enterprise_company_id = #{numErpEnterpriseCompanyId},</if>
            <if test="vcImgAddress != null">vc_img_address = #{vcImgAddress},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <delete id="deleteErpBizCompanyLicenseByNumId" parameterType="Long">
        delete from erp_enterprise_company_license where num_id = #{numId}
    </delete>

    <delete id="deleteErpBizCompanyLicenseByNumIds" parameterType="String">
        delete from erp_enterprise_company_license where num_id in
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>
    <delete id="deleteErpBizCompanyLicenseByInfoId" parameterType="Long">
        delete
        from erp_enterprise_company_license
        where num_erp_enterprise_company_id in (select num_id from erp_enterprise_company where num_erp_enterprise_id = #{infoId})
    </delete>
</mapper>