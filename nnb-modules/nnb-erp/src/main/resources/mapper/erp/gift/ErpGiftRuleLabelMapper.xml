<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.gift.ErpGiftRuleLabelMapper">
    
    <resultMap type="ErpGiftRuleLabel" id="ErpGiftRuleLabelResult">
        <result property="id"    column="id"    />
        <result property="labelName"    column="label_name"    />
        <result property="couponStatus"    column="coupon_status"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectErpGiftRuleLabelVo">
        select id, label_name, coupon_status, create_user, update_user, create_time, update_time from erp_gift_rule_label
    </sql>

    <select id="selectErpGiftRuleLabelList" parameterType="ErpGiftRuleLabel" resultMap="ErpGiftRuleLabelResult">
        <include refid="selectErpGiftRuleLabelVo"/>
        <where>  
            <if test="labelName != null  and labelName != ''"> and label_name like concat('%', #{labelName}, '%')</if>
            <if test="couponStatus != null "> and coupon_status = #{couponStatus}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectErpGiftRuleLabelById" parameterType="Long" resultMap="ErpGiftRuleLabelResult">
        <include refid="selectErpGiftRuleLabelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpGiftRuleLabel" parameterType="ErpGiftRuleLabel" useGeneratedKeys="true" keyProperty="id">
        insert into erp_gift_rule_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="labelName != null">label_name,</if>
            <if test="couponStatus != null">coupon_status,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="labelName != null">#{labelName},</if>
            <if test="couponStatus != null">#{couponStatus},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateErpGiftRuleLabel" parameterType="ErpGiftRuleLabel">
        update erp_gift_rule_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="labelName != null">label_name = #{labelName},</if>
            <if test="couponStatus != null">coupon_status = #{couponStatus},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpGiftRuleLabelById" parameterType="Long">
        delete from erp_gift_rule_label where id = #{id}
    </delete>

    <delete id="deleteErpGiftRuleLabelByIds" parameterType="String">
        delete from erp_gift_rule_label where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>