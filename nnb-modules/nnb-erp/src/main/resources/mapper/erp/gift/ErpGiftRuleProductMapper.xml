<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.gift.ErpGiftRuleProductMapper">
    
    <resultMap type="ErpGiftRuleProduct" id="ErpGiftRuleProductResult">
        <result property="id"    column="id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="productId"    column="product_id"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectErpGiftRuleProductVo">
        select id, rule_id, product_id, create_user, update_user, create_time, update_time from erp_gift_rule_product
    </sql>

    <select id="selectErpGiftRuleProductList" parameterType="ErpGiftRuleProduct" resultMap="ErpGiftRuleProductResult">
        <include refid="selectErpGiftRuleProductVo"/>
        <where>  
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectErpGiftRuleProductById" parameterType="Long" resultMap="ErpGiftRuleProductResult">
        <include refid="selectErpGiftRuleProductVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpGiftRuleProduct" parameterType="ErpGiftRuleProduct" useGeneratedKeys="true" keyProperty="id">
        insert into erp_gift_rule_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateErpGiftRuleProduct" parameterType="ErpGiftRuleProduct">
        update erp_gift_rule_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpGiftRuleProductById" parameterType="Long">
        delete from erp_gift_rule_product where id = #{id}
    </delete>

    <delete id="deleteErpGiftRuleProductByIds" parameterType="String">
        delete from erp_gift_rule_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteErpGiftRuleProductByRuleId" parameterType="Long">
        delete from erp_gift_rule_product where rule_id = #{ruleId}
    </delete>

    <select id="selectRuleIdByProductStr" resultType="java.lang.Long">
        select egrp.rule_id from erp_gift_rule_product egrp
        left join erp_gift_rule egr on egr.id = egrp.rule_id
        where egr.invert = 0 and egr.rule_status = 1 and FIND_IN_SET(#{deptId},egr.dept_id) and egr.minimum_spending_amount <![CDATA[<=]]> #{price}
          and ${productFindStr}
    </select>

    <select id="selectRuleIdByProductInvertStr" resultType="java.lang.Long">
        select egrp.rule_id from erp_gift_rule_product egrp
                                     left join erp_gift_rule egr on egr.id = egrp.rule_id
        where egr.invert = 1 and egr.rule_status = 1 and FIND_IN_SET(#{deptId},egr.dept_id) and egr.minimum_spending_amount <![CDATA[<=]]> #{price}
          and ${productFindStr}
    </select>

    <select id="selectByRuleIds" resultMap="ErpGiftRuleProductResult">
        <include refid="selectErpGiftRuleProductVo"/>
        WHERE rule_id IN
        <foreach collection="ruleIdList" item="ruleId" separator="," open="(" close=")">
            #{ruleId}
        </foreach>
        ORDER BY rule_id ASC
    </select>
</mapper>