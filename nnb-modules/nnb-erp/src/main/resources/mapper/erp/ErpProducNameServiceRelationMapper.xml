<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpProducNameServiceRelationMapper">

    <resultMap type="ErpProducNameServiceRelation" id="ErpProducNameServiceRelationResult">
        <result property="numId"    column="num_id"    />
        <result property="numNameId"    column="num_name_id"    />
        <result property="numServiceId"    column="num_service_id"    />
    </resultMap>

    <sql id="selectErpProducNameServiceRelationVo">
        select num_id, num_name_id, num_service_id from erp_product_name_service_relation
    </sql>

    <insert id="insterRelation" parameterType="ErpProducNameServiceRelation">
        insert into erp_product_name_service_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numNameId != null">num_name_id,</if>
            <if test="numServiceId != null">num_service_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numNameId != null">#{numNameId},</if>
            <if test="numServiceId != null">#{numServiceId},</if>
        </trim>
    </insert>

    <insert id="addRelation" parameterType="ErpProducNameServiceRelation">
        insert into erp_product_name_service_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numNameId != null">num_name_id,</if>
            <if test="numServiceId != null">num_service_id,</if>
            <if test="numProductId != null">num_product_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numNameId != null">#{numNameId},</if>
            <if test="numServiceId != null">#{numServiceId},</if>
            <if test="numProductId != null">#{numProductId},</if>
        </trim>
    </insert>

    <delete id="delRelation">
        delete from erp_product_name_service_relation where num_name_id = #{numNameId} and num_service_id = #{numServiceId}
    </delete>

    <delete id="deleteRelation">
        delete from erp_product_name_service_relation where num_name_id = #{numNameId} and num_service_id = #{numServiceId} and num_product_id = #{numProductId}
    </delete>

</mapper>