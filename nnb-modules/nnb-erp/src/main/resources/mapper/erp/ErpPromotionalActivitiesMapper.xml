<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpPromotionalActivitiesMapper">

    <resultMap type="ErpPromotionalActivities" id="ErpPromotionalActivitiesResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="labelId"    column="label_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="deptIds"    column="dept_ids"    />
        <result property="maxCount"    column="max_count"    />
        <result property="fullPrice"    column="full_price"    />
        <result property="givePrice"    column="give_price"    />
        <result property="selfGoldType"    column="self_gold_type"    />
        <result property="giveGoldType"    column="give_gold_type"    />
        <result property="otherShare"    column="other_share"    />
        <result property="minPrice"    column="min_price"    />
        <result property="useType"    column="use_type"    />
        <result property="useProportion"    column="use_proportion"    />
        <result property="useEndTime"    column="use_end_time"    />
        <result property="remindDays"    column="remind_days"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="prestore"    column="prestore"    />
    </resultMap>

    <sql id="selectErpPromotionalActivitiesVo">
        select id, name, label_id, start_time, end_time, dept_ids, max_count, full_price, give_price, self_gold_type, give_gold_type, other_share, min_price, use_type, use_proportion, use_end_time, remind_days, created_user, created_time, update_user, update_time, prestore from erp_promotional_activities
    </sql>

    <select id="selectErpPromotionalActivitiesList" parameterType="com.nnb.erp.domain.dto.ErpPromotionalActivitiesDto" resultType="com.nnb.erp.domain.vo.ErpPromotionalActivitiesVo">
        select
               epa.id as id,
               epa.name as name, label_id,
               epa.start_time as startTime,
               epa.end_time as endTime,
               epa.dept_ids as deptIds,
               epa.max_count as maxCount,
               epa.full_price as fullPrice,
               epa.give_price as givePrice,
               epal.label as labelName,
               su.nick_name as createdUserName,
               epa.created_time as createdTime,
               epa.status as status
        from erp_promotional_activities epa
        left join erp_promotional_activities_label epal on epal.id = epa.label_id
        left join sys_user su on su.user_id = epa.created_user
        <where>
            <if test="name != null  and name != ''"> and epa.name like concat('%', #{name}, '%')</if>
            <if test="status != null"> and epa.status = #{status}</if>
            <if test="activityStatus != null and activityStatus == 1"> and epa.start_time <![CDATA[>=]]> #{currentDate}</if>
            <if test="activityStatus != null and activityStatus == 2"> and epa.start_time <![CDATA[<=]]> #{currentDate} and epa.end_time <![CDATA[>=]]> #{currentDate} </if>
            <if test="activityStatus != null and activityStatus == 3"> and epa.end_time <![CDATA[<=]]> #{currentDate}</if>
        </where>
        ${sqlDeptStr}
        ORDER BY epa.id DESC
    </select>

    <select id="selectErpPromotionalActivitiesById" parameterType="Integer" resultMap="ErpPromotionalActivitiesResult">
        <include refid="selectErpPromotionalActivitiesVo"/>
        where id = #{id}
    </select>

    <insert id="insertErpPromotionalActivities" parameterType="ErpPromotionalActivities" useGeneratedKeys="true" keyProperty="id">
        insert into erp_promotional_activities
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="labelId != null">label_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="deptIds != null">dept_ids,</if>
            <if test="maxCount != null">max_count,</if>
            <if test="fullPrice != null">full_price,</if>
            <if test="givePrice != null">give_price,</if>
            <if test="selfGoldType != null">self_gold_type,</if>
            <if test="giveGoldType != null">give_gold_type,</if>
            <if test="otherShare != null">other_share,</if>
            <if test="minPrice != null">min_price,</if>
            <if test="useType != null">use_type,</if>
            <if test="useProportion != null">use_proportion,</if>
            <if test="useEndTime != null">use_end_time,</if>
            <if test="remindDays != null">remind_days,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="prestore != null">prestore,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="labelId != null">#{labelId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="deptIds != null">#{deptIds},</if>
            <if test="maxCount != null">#{maxCount},</if>
            <if test="fullPrice != null">#{fullPrice},</if>
            <if test="givePrice != null">#{givePrice},</if>
            <if test="selfGoldType != null">#{selfGoldType},</if>
            <if test="giveGoldType != null">#{giveGoldType},</if>
            <if test="otherShare != null">#{otherShare},</if>
            <if test="minPrice != null">#{minPrice},</if>
            <if test="useType != null">#{useType},</if>
            <if test="useProportion != null">#{useProportion},</if>
            <if test="useEndTime != null">#{useEndTime},</if>
            <if test="remindDays != null">#{remindDays},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="prestore != null">#{prestore},</if>
         </trim>
    </insert>

    <update id="updateErpPromotionalActivities" parameterType="ErpPromotionalActivities">
        update erp_promotional_activities
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="labelId != null">label_id = #{labelId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="deptIds != null">dept_ids = #{deptIds},</if>
            <if test="maxCount != null">max_count = #{maxCount},</if>
            <if test="fullPrice != null">full_price = #{fullPrice},</if>
            <if test="givePrice != null">give_price = #{givePrice},</if>
            <if test="selfGoldType != null">self_gold_type = #{selfGoldType},</if>
            <if test="giveGoldType != null">give_gold_type = #{giveGoldType},</if>
            <if test="otherShare != null">other_share = #{otherShare},</if>
            <if test="minPrice != null">min_price = #{minPrice},</if>
            <if test="useType != null">use_type = #{useType},</if>
            <if test="useProportion != null">use_proportion = #{useProportion},</if>
            <if test="useEndTime != null">use_end_time = #{useEndTime},</if>
            <if test="remindDays != null">remind_days = #{remindDays},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="prestore != null">prestore = #{prestore},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpPromotionalActivitiesById" parameterType="Integer">
        delete from erp_promotional_activities where id = #{id}
    </delete>

    <delete id="deleteErpPromotionalActivitiesByIds" parameterType="String">
        delete from erp_promotional_activities where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectShouldStopActivities" resultType="com.nnb.erp.domain.ErpPromotionalActivities">
        <include refid="selectErpPromotionalActivitiesVo"/>
        where end_time &lt; now() and status = 1;
    </select>

    <select id="selectErpPayRecordId" resultType="java.lang.Long">
        SELECT etv.pay_record_id
        FROM erp_transaction_voucher etv
                 LEFT JOIN erp_order_pay_record eopr ON eopr.id = etv.pay_record_id
        WHERE etv.type = 2
          AND eopr.reported_or_not = 1
          AND etv.activitie_id = #{id};
    </select>

</mapper>
