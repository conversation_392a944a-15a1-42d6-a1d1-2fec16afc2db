<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpLicenseDemandMapper">
    
    <resultMap type="ErpLicenseDemand" id="ErpLicenseDemandResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="area"    column="area"    />
        <result property="taxType"    column="tax_type"    />
        <result property="addressType"    column="address_type"    />
        <result property="remarks"    column="remarks"    />
        <result property="licenseId"    column="license_id"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdDate"    column="created_date"    />
        <result property="matchStatus"    column="match_status"    />
        <result property="matchDate"    column="match_date"    />
        <result property="saleStatus"    column="sale_status"    />
        <result property="licenseYear"    column="license_year"    />
        <result property="associationChange"    column="association_change"    />
        <result property="haveBank"    column="have_bank"    />
        <result property="haveSocialSecurity"    column="have_social_security"    />
        <result property="qualificationRequirement"    column="qualification_requirement"    />
        <result property="customerBudget"    column="customer_budget"    />
        <result property="payPrice"    column="pay_price"    />

    </resultMap>


    <sql id="selectErpLicenseDemandVo">
        select id, type, area, tax_type, address_type, remarks, license_id, created_user, created_date, match_status, match_date, sale_status, license_year, association_change, have_bank, have_social_security, qualification_requirement, customer_budget, payPrice from erp_license_demand
    </sql>

    <select id="selectErpLicenseDemandList" parameterType="ErpLicenseDemand" resultMap="ErpLicenseDemandResult">
        <include refid="selectErpLicenseDemandVo"/>
        <where>  
            <if test="type != null "> and type = #{type}</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="taxType != null "> and tax_type = #{taxType}</if>
            <if test="addressType != null "> and address_type = #{addressType}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="licenseId != null "> and license_id = #{licenseId}</if>
            <if test="createdUser != null "> and created_user = #{createdUser}</if>
            <if test="createdDate != null "> and created_date = #{createdDate}</if>
            <if test="matchStatus != null "> and match_status = #{matchStatus}</if>
            <if test="matchDate != null "> and match_date = #{matchDate}</if>
        </where>
    </select>

    <select id="selectLicenseDemandList" parameterType="com.nnb.erp.domain.dto.ErpLicenseDemandDto" resultType="com.nnb.erp.domain.vo.ErpLicenseDemandVo">
        select
            eld.*,
            el.number as licenseNumber,
            elt.name as typeName,
            eptd.vc_tax_name as taxName,
            ebsat.name as addressTypeName,
            su.nick_name as createdUserName,
            CASE eld.match_status
                WHEN 1 THEN '未匹配'
                WHEN 2 THEN '已匹配'
                WHEN 3 THEN '搁置'
            END AS 'matchStatusName',
            CASE eld.sale_status
                WHEN 1 THEN '已售卖'
                WHEN 2 THEN '未售卖'
            END AS 'saleStatusName',
            CASE el.status
                WHEN 1 THEN '待售'
                WHEN 2 THEN '已提单'
                WHEN 3 THEN '已售'
                WHEN 4 THEN '无'
                WHEN 5 THEN '提交待审核'
                WHEN 6 THEN '待审核'
                WHEN 7 THEN '审核未通过'
            END AS 'licenseStatusName',
            CASE eld.association_change
                WHEN 1 THEN '无'
                WHEN 2 THEN '关联变更少'
                WHEN 3 THEN '零关联变更'
            END AS 'associationChangeName',
            CASE eld.have_bank
                WHEN 1 THEN '有'
                WHEN 2 THEN '无'
            END AS 'haveBankName',
            CASE eld.have_social_security
                WHEN 1 THEN '有'
                WHEN 2 THEN '无'
            END AS 'haveSocialSecurityName'

        from erp_license_demand eld
        LEFT JOIN erp_license el on el.id = eld.license_id
        LEFT JOIN erp_license_type elt on eld.type = elt.id
        LEFT JOIN erp_product_tax_dict eptd on eld.tax_type = eptd.num_tax_id
        LEFT JOIN erp_biz_service_address_type ebsat on ebsat.id = eld.address_type
        LEFT JOIN sys_user su on su.user_id = eld.created_user
        LEFT JOIN sys_dept sd on sd.dept_id = su.dept_id
        <where>
            <if test="createdDateBegin != null "> and eld.created_date <![CDATA[>=]]> #{createdDateBegin}</if>
            <if test="createdDateEnd != null "> and eld.created_date <![CDATA[<=]]> #{createdDateEnd}</if>
            <if test="saleStatus != null "> and eld.sale_status = #{saleStatus}</if>
            <if test="licenseStatus != null "> and el.status = #{licenseStatus}</if>
            <if test="matchStatus != null "> and eld.match_status = #{matchStatus}</if>
            <if test="createdUser != null "> and eld.created_user = #{createdUser}</if>
            <if test="licenseNumber != null "> and el.number = #{licenseNumber}</if>
            <if test="areaIdList != null and areaIdList.size > 0">
                and eld.area in
                <foreach item="area" collection="areaIdList" open="(" separator="," close=")">
                    #{area}
                </foreach>
            </if>
            <if test="typeIdList != null and typeIdList.size > 0">
                and eld.type in
                <foreach item="type" collection="typeIdList" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
        </where>
        ${params.dataScope}
        order by eld.id desc
    </select>
    
    <select id="selectErpLicenseDemandById" parameterType="Long" resultMap="ErpLicenseDemandResult">
        <include refid="selectErpLicenseDemandVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpLicenseDemand" parameterType="ErpLicenseDemand" useGeneratedKeys="true" keyProperty="id">
        insert into erp_license_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="area != null">area,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="addressType != null">address_type,</if>
            <if test="remarks != null">remarks,</if>
            <if test="licenseId != null">license_id,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdDate != null">created_date,</if>
            <if test="matchStatus != null">match_status,</if>
            <if test="matchDate != null">match_date,</if>
            <if test="saleStatus != null">sale_status,</if>
            <if test="licenseYear != null">license_year,</if>
            <if test="associationChange != null">association_change,</if>
            <if test="haveBank != null">have_bank,</if>
            <if test="haveSocialSecurity != null">have_social_security,</if>
            <if test="qualificationRequirement != null">qualification_requirement,</if>
            <if test="customerBudget != null">customer_budget,</if>
            <if test="payPrice != null">pay_price,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="area != null">#{area},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="addressType != null">#{addressType},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="licenseId != null">#{licenseId},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdDate != null">#{createdDate},</if>
            <if test="matchStatus != null">#{matchStatus},</if>
            <if test="matchDate != null">#{matchDate},</if>
            <if test="saleStatus != null">#{saleStatus},</if>
            <if test="licenseYear != null">#{licenseYear},</if>
            <if test="associationChange != null">#{associationChange},</if>
            <if test="haveBank != null">#{haveBank},</if>
            <if test="haveSocialSecurity != null">#{haveSocialSecurity},</if>
            <if test="qualificationRequirement != null">#{qualificationRequirement},</if>
            <if test="customerBudget != null">#{customerBudget},</if>
            <if test="payPrice != null">#{payPrice},</if>
         </trim>
    </insert>

    <update id="updateErpLicenseDemand" parameterType="ErpLicenseDemand">
        update erp_license_demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="area != null">area = #{area},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="addressType != null">address_type = #{addressType},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="licenseId != null">license_id = #{licenseId},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdDate != null">created_date = #{createdDate},</if>
            <if test="matchStatus != null">match_status = #{matchStatus},</if>
            <if test="matchDate != null">match_date = #{matchDate},</if>
            <if test="saleStatus != null">sale_status = #{saleStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpLicenseDemandById" parameterType="Long">
        delete from erp_license_demand where id = #{id}
    </delete>

    <delete id="deleteErpLicenseDemandByIds" parameterType="String">
        delete from erp_license_demand where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>