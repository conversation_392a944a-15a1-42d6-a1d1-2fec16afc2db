<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.SConfigServiceTypeMapper">
    
    <resultMap type="SConfigServiceType" id="SConfigServiceTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="serviceCatalogueId"    column="service_catalogue_id"    />
        <result property="lqPointStatus"    column="lq_point_status"    />
        <result property="lqNextPointStatus"    column="lq_next_point_status"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptIdSh"    column="dept_id_sh"    />
        <result property="internalType"    column="internal_type"    />
    </resultMap>

    <sql id="selectSConfigServiceTypeVo">
        select id, name, status, dept_id, dept_id_sh, service_catalogue_id, lq_point_status, lq_next_point_status, internal_type from s_config_service_type
    </sql>

    <select id="selectSConfigServiceTypeList" parameterType="SConfigServiceType" resultType="com.nnb.erp.domain.vo.service.SConfigServiceTypeVo">
        select st.id,
        st.name,
        st.status,
        st.dept_id,
        st.dept_id_sh,
        st.service_catalogue_id,
        sc.name as serviceCatalogueName
        from s_config_service_type st left join s_config_service_catalogue sc on st.service_catalogue_id = sc.id
        <where>  
            <if test="name != null  and name != ''"> and st.name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and st.status = #{status}</if>
            <if test="serviceCatalogueId != null "> and st.service_catalogue_id = #{serviceCatalogueId}</if>
            <if test="internalType != null "> and st.internal_type = #{internalType}</if>
        </where>
    </select>
    
    <select id="selectSConfigServiceTypeById" parameterType="Long" resultMap="SConfigServiceTypeResult">
        <include refid="selectSConfigServiceTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSConfigServiceType" parameterType="SConfigServiceType" useGeneratedKeys="true" keyProperty="id">
        insert into s_config_service_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="serviceCatalogueId != null">service_catalogue_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptIdSh != null">dept_id_sh,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="serviceCatalogueId != null">#{serviceCatalogueId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptIdSh != null">#{serviceCatalogueId},</if>
         </trim>
    </insert>

    <update id="updateSConfigServiceType" parameterType="SConfigServiceType">
        update s_config_service_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="serviceCatalogueId != null">service_catalogue_id = #{serviceCatalogueId},</if>
            <if test="deptIdSh != null">dept_id_sh = #{deptIdSh},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSConfigServiceTypeById" parameterType="Long">
        delete from s_config_service_type where id = #{id}
    </delete>

    <delete id="deleteSConfigServiceTypeByIds" parameterType="String">
        delete from s_config_service_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectSConfigServiceTypeByIds" parameterType="String" resultMap="SConfigServiceTypeResult">
        SELECT
        id,
        lq_point_status,
        name,
        lq_next_point_status
        FROM
        s_config_service_type
        WHERE
        id in
        <foreach item="id" collection="ids.split(',')" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectServiceTypeIdsByWorkPlatFormId" resultType="java.lang.String">
        SELECT group_concat(scst.id SEPARATOR ',')
        FROM s_config_service_catalogue scsc
            LEFT JOIN s_config_service_type scst ON scst.service_catalogue_id = scsc.id
        WHERE scsc.work_platform_id = #{workPlatformId}
        <if test="serviceCatalogue != null and serviceCatalogue != -1"> and scst.service_catalogue_id = #{serviceCatalogue} </if>
    </select>



    <select id="selectServiceTypeIdsByCatalogueIdList" resultType="java.lang.String">
        SELECT group_concat(scst.id SEPARATOR ',')
        FROM s_config_service_catalogue scsc
        LEFT JOIN s_config_service_type scst ON scst.service_catalogue_id = scsc.id
        WHERE scst.service_catalogue_id in
            <foreach item="serviceCatalogueId" collection="serviceCatalogueIdList" open="(" separator="," close=")">
                #{serviceCatalogueId}
            </foreach>
    </select>

</mapper>