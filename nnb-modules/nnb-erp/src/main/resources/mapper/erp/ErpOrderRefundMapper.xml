<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpOrderRefundMapper">

    <resultMap type="com.nnb.erp.domain.ErpOrderRefund" id="ErpOrderRefundResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="signUserId"    column="sign_user_id"    />
        <result property="signUserName"    column="sign_user_name"    />
        <result property="refundType"    column="refund_type"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="orderStatusBefore"    column="order_status_before"    />
        <result property="createTime"    column="create_time"    />
        <result property="refundAttachment"    column="refund_attachment"    />
        <result property="refundPayFee"    column="refund_pay_fee"    />
        <result property="refundPayDate"    column="refund_pay_date"    />
    </resultMap>

    <resultMap type="com.nnb.erp.domain.ErpOrderRefund" id="ErpOrderRefundResultAndDetail">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="signUserId"    column="sign_user_id"    />
        <result property="signUserName"    column="sign_user_name"    />
        <result property="refundType"    column="refund_type"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="orderStatusBefore"    column="order_status_before"    />
        <result property="createTime"    column="create_time"    />
        <collection property="erpOrderRefundDetailList" ofType="com.nnb.erp.domain.ErpOrderRefundDetail"
                    javaType="java.util.ArrayList" column="id"
                    select="com.nnb.erp.mapper.ErpOrderRefundDetailMapper.selectErpOrderRefundDetailByRefundId"/>
    </resultMap>

    <sql id="selectErpOrderRefundVo">
        select id, order_id, order_num, sign_user_id, sign_user_name, refund_type, refund_amount, refund_reason, create_user_id, create_user_name, order_status_before, create_time, refund_account, account_name, opening_bank, refund_reason_select, refund_attachment, refund_pay_fee, refund_pay_date from erp_order_refund
    </sql>

    <select id="selectErpOrderRefundList" parameterType="ErpOrderRefund" resultMap="ErpOrderRefundResult">
        <include refid="selectErpOrderRefundVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNum != null  and orderNum != ''"> and order_num = #{orderNum}</if>
            <if test="signUserId != null "> and sign_user_id = #{signUserId}</if>
            <if test="signUserName != null  and signUserName != ''"> and sign_user_name like concat('%', #{signUserName}, '%')</if>
            <if test="refundType != null "> and refund_type = #{refundType}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="refundReason != null  and refundReason != ''"> and refund_reason = #{refundReason}</if>
            <if test="createUserId != null "> and create_user_id = #{createUserId}</if>
            <if test="createUserName != null  and createUserName != ''"> and create_user_name like concat('%', #{createUserName}, '%')</if>
            <if test="orderStatusBefore != null "> and order_status_before = #{orderStatusBefore}</if>
        </where>
    </select>

    <select id="selectErpOrderRefundById" parameterType="Long" resultMap="ErpOrderRefundResult">
        <include refid="selectErpOrderRefundVo"/>
        where id = #{id}
    </select>

    <select id="selectErpOrderRefundByOrderId" parameterType="Long" resultMap="ErpOrderRefundResultAndDetail">
        <include refid="selectErpOrderRefundVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectErpOrderRefundByIdList" parameterType="Long" resultMap="ErpOrderRefundResult">
        <include refid="selectErpOrderRefundVo"/>
        where id in (
            <foreach collection="idList" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>

    <insert id="insertErpOrderRefund" parameterType="ErpOrderRefund" useGeneratedKeys="true" keyProperty="id">
        insert into erp_order_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNum != null and orderNum != ''">order_num,</if>
            <if test="signUserId != null">sign_user_id,</if>
            <if test="signUserName != null and signUserName != ''">sign_user_name,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundReason != null and refundReason != ''">refund_reason,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createUserName != null and createUserName != ''">create_user_name,</if>
            <if test="orderStatusBefore != null">order_status_before,</if>
            <if test="createTime != null">create_time,</if>
            <if test="refundAccount != null">refund_account,</if>
            <if test="accountName != null">account_name,</if>
            <if test="openingBank != null">opening_bank,</if>
            <if test="refundReasonSelect != null">refund_reason_select,</if>
            <if test="refundAttachment != null">refund_attachment,</if>
            <if test="refundPayFee != null">refund_pay_fee,</if>
            <if test="refundPayDate != null">refund_pay_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
            <if test="signUserId != null">#{signUserId},</if>
            <if test="signUserName != null and signUserName != ''">#{signUserName},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundReason != null and refundReason != ''">#{refundReason},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createUserName != null and createUserName != ''">#{createUserName},</if>
            <if test="orderStatusBefore != null">#{orderStatusBefore},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="refundAccount != null">#{refundAccount},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="openingBank != null">#{openingBank},</if>
            <if test="refundReasonSelect != null">#{refundReasonSelect},</if>
            <if test="refundAttachment != null">#{refundAttachment},</if>
            <if test="refundPayFee != null">#{refundPayFee},</if>
            <if test="refundPayDate != null">#{refundPayDate},</if>
        </trim>
    </insert>

    <update id="updateErpOrderRefund" parameterType="ErpOrderRefund">
        update erp_order_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="signUserId != null">sign_user_id = #{signUserId},</if>
            <if test="signUserName != null and signUserName != ''">sign_user_name = #{signUserName},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundReason != null and refundReason != ''">refund_reason = #{refundReason},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="createUserName != null and createUserName != ''">create_user_name = #{createUserName},</if>
            <if test="orderStatusBefore != null">order_status_before = #{orderStatusBefore},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="refundAccount != null">refund_account = #{refundAccount},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="refundReasonSelect != null">refund_reason_select = #{refundReasonSelect},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpOrderRefundById" parameterType="Long">
        delete from erp_order_refund where id = #{id}
    </delete>

    <delete id="deleteErpOrderRefundByIds" parameterType="String">
        delete from erp_order_refund where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </delete>


    <select id="getRefundReason" resultType="com.nnb.erp.domain.ErpOrderRefundReason">
        select * from erp_order_refund_reason
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>
</mapper>
