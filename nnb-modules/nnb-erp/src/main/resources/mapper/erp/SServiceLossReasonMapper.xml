<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.SServiceLossReasonMapper">
    
    <resultMap type="SServiceLossReason" id="SServiceLossReasonResult">
        <result property="id"    column="id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="accountLsId"    column="account_ls_id"    />
    </resultMap>

    <sql id="selectSServiceLossReasonVo">
        select id, service_id, account_ls_id from s_service_loss_reason
    </sql>

    <select id="selectSServiceLossReasonList" parameterType="SServiceLossReason" resultMap="SServiceLossReasonResult">
        <include refid="selectSServiceLossReasonVo"/>
        <where>  
            <if test="serviceId != null "> and service_id = #{serviceId}</if>
            <if test="accountLsId != null "> and account_ls_id = #{accountLsId}</if>
        </where>
    </select>
    
    <select id="selectSServiceLossReasonById" parameterType="Long" resultMap="SServiceLossReasonResult">
        <include refid="selectSServiceLossReasonVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSServiceLossReason" parameterType="SServiceLossReason" useGeneratedKeys="true" keyProperty="id">
        insert into s_service_loss_reason
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">service_id,</if>
            <if test="accountLsId != null">account_ls_id,</if>
            <if test="lsDate != null">loss_date,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">#{serviceId},</if>
            <if test="accountLsId != null">#{accountLsId},</if>
            <if test="lsDate != null">#{lsDate},</if>
            <if test="enterpriseId != null">#{enterpriseId},</if>
         </trim>
    </insert>

    <update id="updateSServiceLossReason" parameterType="SServiceLossReason">
        update s_service_loss_reason
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="accountLsId != null">account_ls_id = #{accountLsId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSServiceLossReasonById" parameterType="Long">
        delete from s_service_loss_reason where id = #{id}
    </delete>

    <delete id="deleteSServiceLossReasonByIds" parameterType="String">
        delete from s_service_loss_reason where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>