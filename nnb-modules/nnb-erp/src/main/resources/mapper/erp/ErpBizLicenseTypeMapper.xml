<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpBizLicenseTypeMapper">
    
    <resultMap type="ErpBizLicenseType" id="ErpBizLicenseTypeResult">
        <result property="numId"    column="num_id"    />
        <result property="vcLicenseName"    column="vc_license_name"    />
        <result property="vcRemarks"    column="vc_remarks"    />
        <result property="numIsEffective"    column="num_is_effective"    />
        <result property="datCreatedTime"    column="dat_created_time"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="datUpdatedTime"    column="dat_updated_time"    />
        <result property="datUpdatedBy"    column="dat_updated_by"    />
    </resultMap>

    <sql id="selectErpBizLicenseTypeVo">
        select num_id, vc_license_name, vc_remarks, num_is_effective, dat_created_time, num_created_by, dat_updated_time, dat_updated_by from erp_biz_license_type
    </sql>

    <select id="selectErpBizLicenseTypeList" parameterType="ErpBizLicenseType" resultMap="ErpBizLicenseTypeResult">
        <include refid="selectErpBizLicenseTypeVo"/>
        <where>  
            <if test="vcLicenseName != null  and vcLicenseName != ''"> and vc_license_name like concat('%', #{vcLicenseName}, '%')</if>
            <if test="vcRemarks != null  and vcRemarks != ''"> and vc_remarks = #{vcRemarks}</if>
            <if test="numIsEffective != null "> and num_is_effective = #{numIsEffective}</if>
            <if test="datCreatedTime != null "> and dat_created_time = #{datCreatedTime}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="datUpdatedTime != null "> and dat_updated_time = #{datUpdatedTime}</if>
            <if test="datUpdatedBy != null "> and dat_updated_by = #{datUpdatedBy}</if>
        </where>
    </select>
    
    <select id="selectErpBizLicenseTypeByNumId" parameterType="Long" resultMap="ErpBizLicenseTypeResult">
        <include refid="selectErpBizLicenseTypeVo"/>
        where num_id = #{numId}
    </select>
        
    <insert id="insertErpBizLicenseType" parameterType="ErpBizLicenseType" useGeneratedKeys="true" keyProperty="numId">
        insert into erp_biz_license_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vcLicenseName != null">vc_license_name,</if>
            <if test="vcRemarks != null">vc_remarks,</if>
            <if test="numIsEffective != null">num_is_effective,</if>
            <if test="datCreatedTime != null">dat_created_time,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="datUpdatedTime != null">dat_updated_time,</if>
            <if test="datUpdatedBy != null">dat_updated_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vcLicenseName != null">#{vcLicenseName},</if>
            <if test="vcRemarks != null">#{vcRemarks},</if>
            <if test="numIsEffective != null">#{numIsEffective},</if>
            <if test="datCreatedTime != null">#{datCreatedTime},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="datUpdatedTime != null">#{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">#{datUpdatedBy},</if>
         </trim>
    </insert>

    <update id="updateErpBizLicenseType" parameterType="ErpBizLicenseType">
        update erp_biz_license_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcLicenseName != null">vc_license_name = #{vcLicenseName},</if>
            <if test="vcRemarks != null">vc_remarks = #{vcRemarks},</if>
            <if test="numIsEffective != null">num_is_effective = #{numIsEffective},</if>
            <if test="datCreatedTime != null">dat_created_time = #{datCreatedTime},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datUpdatedTime != null">dat_updated_time = #{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">dat_updated_by = #{datUpdatedBy},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <delete id="deleteErpBizLicenseTypeByNumId" parameterType="Long">
        delete from erp_biz_license_type where num_id = #{numId}
    </delete>

    <delete id="deleteErpBizLicenseTypeByNumIds" parameterType="String">
        delete from erp_biz_license_type where num_id in 
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>
</mapper>