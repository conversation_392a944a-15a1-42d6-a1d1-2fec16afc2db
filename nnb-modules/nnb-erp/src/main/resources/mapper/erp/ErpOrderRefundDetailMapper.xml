<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpOrderRefundDetailMapper">

    <resultMap type="com.nnb.erp.domain.ErpOrderRefundDetail" id="ErpOrderRefundDetailResult">
        <result property="id"    column="id"    />
        <result property="orderRefundId"    column="order_refund_id"    />
        <result property="serviceOrdersId"    column="service_orders_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productNameId"    column="product_name_id"    />
        <result property="productName"    column="product_name"    />
        <result property="serviceOrderTypeId"    column="service_order_type_id"    />
        <result property="serviceOrderType"    column="service_order_type"    />
        <result property="customServiceTypeId"    column="custom_service_type_id"    />
        <result property="customServiceType"    column="custom_service_type"    />
        <result property="taxTypeId"    column="tax_type_id"    />
        <result property="taxType"    column="tax_type"    />
        <result property="unit"    column="unit"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="price"    column="price"    />
        <result property="quantity"    column="quantity"    />
        <result property="couponName"    column="coupon_name"    />
        <result property="discountAmount"    column="discount_amount"    />
        <result property="channelAmount"    column="channel_amount"    />
        <result property="receivableAmount"    column="receivable_amount"    />
        <result property="paidAmount"    column="paid_amount"    />
        <result property="tailAmount"    column="tail_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundType"    column="refund_type"    />
        <result property="voidStatus"    column="void_status"    />
        <result property="voidStatusBefore"    column="void_status_before"    />
        <result property="serviceStatusBefore"    column="service_status_before"    />
    </resultMap>

    <sql id="selectErpOrderRefundDetailVo">
        select id, order_refund_id, service_orders_id, product_id, product_name_id, product_name, service_order_type_id, service_order_type, custom_service_type_id, custom_service_type, tax_type_id, tax_type, unit, city_id, city_name, price, quantity, coupon_name, discount_amount, channel_amount, receivable_amount, paid_amount, tail_amount, refund_amount, refund_type, void_status, void_status_before, service_status_before from erp_order_refund_detail
    </sql>

    <select id="selectErpOrderRefundDetailList" parameterType="ErpOrderRefundDetail" resultMap="ErpOrderRefundDetailResult">
        <include refid="selectErpOrderRefundDetailVo"/>
        <where>
            <if test="orderRefundId != null "> and order_refund_id = #{orderRefundId}</if>
            <if test="serviceOrdersId != null "> and service_orders_id = #{serviceOrdersId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productNameId != null "> and product_name_id = #{productNameId}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="serviceOrderTypeId != null "> and service_order_type_id = #{serviceOrderTypeId}</if>
            <if test="serviceOrderType != null  and serviceOrderType != ''"> and service_order_type = #{serviceOrderType}</if>
            <if test="customServiceTypeId != null "> and custom_service_type_id = #{customServiceTypeId}</if>
            <if test="customServiceType != null  and customServiceType != ''"> and custom_service_type = #{customServiceType}</if>
            <if test="taxTypeId != null  and taxTypeId != ''"> and tax_type_id = #{taxTypeId}</if>
            <if test="taxType != null  and taxType != ''"> and tax_type = #{taxType}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="couponName != null  and couponName != ''"> and coupon_name like concat('%', #{couponName}, '%')</if>
            <if test="discountAmount != null "> and discount_amount = #{discountAmount}</if>
            <if test="channelAmount != null "> and channel_amount = #{channelAmount}</if>
            <if test="receivableAmount != null "> and receivable_amount = #{receivableAmount}</if>
            <if test="paidAmount != null "> and paid_amount = #{paidAmount}</if>
            <if test="tailAmount != null "> and tail_amount = #{tailAmount}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="refundType != null "> and refund_type = #{refundType}</if>
            <if test="voidStatus != null "> and void_status = #{voidStatus}</if>
            <if test="voidStatusBefore != null "> and void_status_before = #{voidStatusBefore}</if>
            <if test="serviceStatusBefore != null "> and service_status_before = #{serviceStatusBefore}</if>
        </where>
    </select>

    <select id="selectErpOrderRefundDetailById" parameterType="Long" resultMap="ErpOrderRefundDetailResult">
        <include refid="selectErpOrderRefundDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectErpOrderRefundDetailByRefundId" parameterType="Long" resultMap="ErpOrderRefundDetailResult">
        <include refid="selectErpOrderRefundDetailVo"/>
        where order_refund_id = #{orderRefundId}
    </select>

    <insert id="insertErpOrderRefundDetail" parameterType="ErpOrderRefundDetail" useGeneratedKeys="true" keyProperty="id">
        insert into erp_order_refund_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderRefundId != null">order_refund_id,</if>
            <if test="serviceOrdersId != null">service_orders_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productNameId != null">product_name_id,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="serviceOrderTypeId != null">service_order_type_id,</if>
            <if test="serviceOrderType != null and serviceOrderType != ''">service_order_type,</if>
            <if test="customServiceTypeId != null">custom_service_type_id,</if>
            <if test="customServiceType != null and customServiceType != ''">custom_service_type,</if>
            <if test="taxTypeId != null and taxTypeId != ''">tax_type_id,</if>
            <if test="taxType != null and taxType != ''">tax_type,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="cityId != null and cityId != ''">city_id,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="price != null">price,</if>
            <if test="quantity != null">quantity,</if>
            <if test="couponName != null and couponName != ''">coupon_name,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="channelAmount != null">channel_amount,</if>
            <if test="receivableAmount != null">receivable_amount,</if>
            <if test="paidAmount != null">paid_amount,</if>
            <if test="tailAmount != null">tail_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="voidStatus != null">void_status,</if>
            <if test="voidStatusBefore != null">void_status_before,</if>
            <if test="serviceStatusBefore != null">service_status_before,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderRefundId != null">#{orderRefundId},</if>
            <if test="serviceOrdersId != null">#{serviceOrdersId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productNameId != null">#{productNameId},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="serviceOrderTypeId != null">#{serviceOrderTypeId},</if>
            <if test="serviceOrderType != null and serviceOrderType != ''">#{serviceOrderType},</if>
            <if test="customServiceTypeId != null">#{customServiceTypeId},</if>
            <if test="customServiceType != null and customServiceType != ''">#{customServiceType},</if>
            <if test="taxTypeId != null and taxTypeId != ''">#{taxTypeId},</if>
            <if test="taxType != null and taxType != ''">#{taxType},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="cityId != null and cityId != ''">#{cityId},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="price != null">#{price},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="couponName != null and couponName != ''">#{couponName},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="channelAmount != null">#{channelAmount},</if>
            <if test="receivableAmount != null">#{receivableAmount},</if>
            <if test="paidAmount != null">#{paidAmount},</if>
            <if test="tailAmount != null">#{tailAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="voidStatus != null">#{voidStatus},</if>
            <if test="voidStatusBefore != null">#{voidStatusBefore},</if>
            <if test="serviceStatusBefore != null">#{serviceStatusBefore},</if>
        </trim>
    </insert>

    <update id="updateErpOrderRefundDetail" parameterType="ErpOrderRefundDetail">
        update erp_order_refund_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderRefundId != null">order_refund_id = #{orderRefundId},</if>
            <if test="serviceOrdersId != null">service_orders_id = #{serviceOrdersId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productNameId != null">product_name_id = #{productNameId},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="serviceOrderTypeId != null">service_order_type_id = #{serviceOrderTypeId},</if>
            <if test="serviceOrderType != null and serviceOrderType != ''">service_order_type = #{serviceOrderType},</if>
            <if test="customServiceTypeId != null">custom_service_type_id = #{customServiceTypeId},</if>
            <if test="customServiceType != null and customServiceType != ''">custom_service_type = #{customServiceType},</if>
            <if test="taxTypeId != null and taxTypeId != ''">tax_type_id = #{taxTypeId},</if>
            <if test="taxType != null and taxType != ''">tax_type = #{taxType},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="cityId != null and cityId != ''">city_id = #{cityId},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="price != null">price = #{price},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="couponName != null and couponName != ''">coupon_name = #{couponName},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="channelAmount != null">channel_amount = #{channelAmount},</if>
            <if test="receivableAmount != null">receivable_amount = #{receivableAmount},</if>
            <if test="paidAmount != null">paid_amount = #{paidAmount},</if>
            <if test="tailAmount != null">tail_amount = #{tailAmount},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="voidStatus != null">void_status = #{voidStatus},</if>
            <if test="voidStatusBefore != null">void_status_before = #{voidStatusBefore},</if>
            <if test="serviceStatusBefore != null">service_status_before = #{serviceStatusBefore},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpOrderRefundDetailById" parameterType="Long">
        delete from erp_order_refund_detail where id = #{id}
    </delete>

    <delete id="deleteErpOrderRefundDetailByIds" parameterType="String">
        delete from erp_order_refund_detail where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </delete>
</mapper>
