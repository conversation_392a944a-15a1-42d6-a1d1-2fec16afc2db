<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCustomerIntentionProductMapper">
    
    <resultMap type="ErpCustomerIntentionProduct" id="ErpCustomerIntentionProductResult">
        <result property="id"    column="id"    />
        <result property="intentionId"    column="intention_id"    />
        <result property="productTypeId"    column="product_type_id"    />
    </resultMap>

    <sql id="selectErpCustomerIntentionProductVo">
        select id, intention_id, product_type_id from erp_customer_intention_product
    </sql>

    <select id="selectErpCustomerIntentionProductList" parameterType="ErpCustomerIntentionProduct" resultMap="ErpCustomerIntentionProductResult">
        <include refid="selectErpCustomerIntentionProductVo"/>
        <where>  
            <if test="intentionId != null "> and intention_id = #{intentionId}</if>
            <if test="productTypeId != null "> and product_type_id = #{productTypeId}</if>
        </where>
    </select>
    
    <select id="selectErpCustomerIntentionProductById" parameterType="Long" resultMap="ErpCustomerIntentionProductResult">
        <include refid="selectErpCustomerIntentionProductVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpCustomerIntentionProduct" parameterType="ErpCustomerIntentionProduct" useGeneratedKeys="true" keyProperty="id">
        insert into erp_customer_intention_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="intentionId != null">intention_id,</if>
            <if test="productTypeId != null">product_type_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="intentionId != null">#{intentionId},</if>
            <if test="productTypeId != null">#{productTypeId},</if>
         </trim>
    </insert>

    <update id="updateErpCustomerIntentionProduct" parameterType="ErpCustomerIntentionProduct">
        update erp_customer_intention_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="intentionId != null">intention_id = #{intentionId},</if>
            <if test="productTypeId != null">product_type_id = #{productTypeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpCustomerIntentionProductById" parameterType="Long">
        delete from erp_customer_intention_product where id = #{id}
    </delete>

    <delete id="deleteErpCustomerIntentionProductByIds" parameterType="String">
        delete from erp_customer_intention_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>