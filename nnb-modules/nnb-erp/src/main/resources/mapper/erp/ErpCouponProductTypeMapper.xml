<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCouponProductTypeMapper">
    
    <resultMap type="ErpCouponProductType" id="ErpCouponProductTypeResult">
        <result property="id"    column="id"    />
        <result property="numCouponId"    column="num_coupon_id"    />
        <result property="numProductTypeId"    column="num_product_type_id"    />
        <result property="vcProductTypeName"    column="vc_product_type_name"    />
    </resultMap>

    <sql id="selectErpCouponProductTypeVo">
        select id, num_coupon_id, num_product_type_id, vc_product_type_name from erp_coupon_product_type
    </sql>

    <select id="selectErpCouponProductTypeList" parameterType="ErpCouponProductType" resultMap="ErpCouponProductTypeResult">
        <include refid="selectErpCouponProductTypeVo"/>
        <where>  
            <if test="numCouponId != null "> and num_coupon_id = #{numCouponId}</if>
            <if test="numProductTypeId != null "> and num_product_type_id = #{numProductTypeId}</if>
            <if test="vcProductTypeName != null  and vcProductTypeName != ''"> and vc_product_type_name like concat('%', #{vcProductTypeName}, '%')</if>
        </where>
    </select>
    
    <select id="selectErpCouponProductTypeById" parameterType="Long" resultMap="ErpCouponProductTypeResult">
        <include refid="selectErpCouponProductTypeVo"/>
        where id = #{id}
    </select>

    <!--根据优惠券id查询关系-->
    <select id="selectErpCouponProductTypeByCouponId" parameterType="Long" resultType="long">
        select num_product_type_id from erp_coupon_product_type
        where num_coupon_id = #{couponId}
    </select>
        
    <insert id="insertErpCouponProductType" parameterType="ErpCouponProductType" useGeneratedKeys="true" keyProperty="id">
        insert into erp_coupon_product_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numCouponId != null">num_coupon_id,</if>
            <if test="numProductTypeId != null">num_product_type_id,</if>
            <if test="vcProductTypeName != null">vc_product_type_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numCouponId != null">#{numCouponId},</if>
            <if test="numProductTypeId != null">#{numProductTypeId},</if>
            <if test="vcProductTypeName != null">#{vcProductTypeName},</if>
         </trim>
    </insert>

    <update id="updateErpCouponProductType" parameterType="ErpCouponProductType">
        update erp_coupon_product_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="numCouponId != null">num_coupon_id = #{numCouponId},</if>
            <if test="numProductTypeId != null">num_product_type_id = #{numProductTypeId},</if>
            <if test="vcProductTypeName != null">vc_product_type_name = #{vcProductTypeName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpCouponProductTypeById" parameterType="Long">
        delete from erp_coupon_product_type where id = #{id}
    </delete>

    <delete id="deleteErpCouponProductTypeByIds" parameterType="String">
        delete from erp_coupon_product_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteErpCouponProductTypeByCouponId" parameterType="Long">
        delete from erp_coupon_product_type where num_coupon_id = #{couponId}
    </delete>
</mapper>