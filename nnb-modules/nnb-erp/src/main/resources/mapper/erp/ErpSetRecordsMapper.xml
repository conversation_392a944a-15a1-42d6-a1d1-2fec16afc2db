<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpSetRecordsMapper">
    
    <resultMap type="ErpSetRecords" id="ErpSetRecordsResult">
        <result property="configId"    column="config_id"    />
        <result property="sendMessage"    column="send_message"    />
    </resultMap>

    <sql id="selectErpSetRecordsVo">
        select config_id, send_message from erp_set_records
    </sql>

    <select id="selectErpSetRecordsList" parameterType="ErpSetRecords" resultMap="ErpSetRecordsResult">
        <include refid="selectErpSetRecordsVo"/>
        <where>  
            <if test="sendMessage != null "> and send_message = #{sendMessage}</if>
        </where>
    </select>
    
    <select id="selectErpSetRecordsByConfigId" parameterType="Long" resultMap="ErpSetRecordsResult">
        <include refid="selectErpSetRecordsVo"/>
        where config_id = #{configId}
    </select>
        
    <insert id="insertErpSetRecords" parameterType="ErpSetRecords">
        insert into erp_set_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configId != null">config_id,</if>
            <if test="sendMessage != null">send_message,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configId != null">#{configId},</if>
            <if test="sendMessage != null">#{sendMessage},</if>
         </trim>
    </insert>

    <update id="updateErpSetRecords" parameterType="ErpSetRecords">
        update erp_set_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="sendMessage != null">send_message = #{sendMessage},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteErpSetRecordsByConfigId" parameterType="Long">
        delete from erp_set_records where config_id = #{configId}
    </delete>

    <delete id="deleteErpSetRecordsByConfigIds" parameterType="String">
        delete from erp_set_records where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
</mapper>