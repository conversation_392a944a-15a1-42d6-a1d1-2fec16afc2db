<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpAccountVisitMapper">

    <resultMap type="ErpAccountVisit" id="ErpAccountVisitResult">
        <result property="id"    column="id"    />
        <result property="enterpriseId"    column="enterprise_id"    />
        <result property="accountUserId"    column="account_user_id"    />
        <result property="visitDate"    column="visit_date"    />
        <result property="visitType"    column="visit_type"    />
        <result property="visitArea"    column="visit_area"    />
        <result property="visitReason"    column="visit_reason"    />
        <result property="visitReasonDetail"    column="visit_reason_detail"    />
        <result property="visitUserName"    column="visit_user_name"    />
        <result property="togetherAccountUserId"    column="together_account_user_id"    />
        <result property="intention"    column="intention"    />
        <result property="visitAddress"    column="visit_address"    />
        <result property="customerDemand"    column="customer_demand"    />
        <result property="customerFeedback"    column="customer_feedback"    />
        <result property="finalResult"    column="final_result"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdDate"    column="created_date"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateDate"    column="update_date"    />
        <result property="photo"    column="photo"    />
        <result property="intentionLevel"    column="intention_level"    />
        <result property="visitContentRecord"    column="visit_content_record"    />
        <result property="taskId"    column="task_id"    />
        <result property="visitWay"    column="visit_way"    />
        <result property="businessModel"    column="business_model"    />
        <result property="noOrderReason"    column="no_order_reason"    />
        <result property="otherHarvest"    column="other_harvest"    />
    </resultMap>



    <sql id="selectErpAccountVisitVo">
        select id, enterprise_id, account_user_id, visit_date, visit_type, visit_area, visit_reason, visit_reason_detail, visit_user_name, together_account_user_id, intention, visit_address, customer_demand, customer_feedback, final_result, created_user, created_date, update_user, update_date, photo, intention_level, visit_content_record, task_id, visit_way, business_model, no_order_reason, other_harvest from erp_account_visit
    </sql>

    <select id="selectErpAccountVisitList" parameterType="ErpAccountVisit" resultMap="ErpAccountVisitResult">
        <include refid="selectErpAccountVisitVo"/>
        <where>
            <if test="enterpriseId != null "> and enterprise_id = #{enterpriseId}</if>
            <if test="accountUserId != null "> and account_user_id = #{accountUserId}</if>
            <if test="visitDate != null "> and visit_date = #{visitDate}</if>
            <if test="visitType != null "> and visit_type = #{visitType}</if>
            <if test="visitArea != null "> and visit_area = #{visitArea}</if>
            <if test="visitReason != null "> and visit_reason = #{visitReason}</if>
            <if test="visitReasonDetail != null  and visitReasonDetail != ''"> and visit_reason_detail = #{visitReasonDetail}</if>
            <if test="visitUserName != null  and visitUserName != ''"> and visit_user_name like concat('%', #{visitUserName}, '%')</if>
            <if test="togetherAccountUserId != null "> and together_account_user_id = #{togetherAccountUserId}</if>
            <if test="intention != null "> and intention = #{intention}</if>
            <if test="visitAddress != null  and visitAddress != ''"> and visit_address = #{visitAddress}</if>
            <if test="customerDemand != null  and customerDemand != ''"> and customer_demand = #{customerDemand}</if>
            <if test="customerFeedback != null  and customerFeedback != ''"> and customer_feedback = #{customerFeedback}</if>
            <if test="finalResult != null  and finalResult != ''"> and final_result = #{finalResult}</if>
            <if test="createdUser != null "> and created_user = #{createdUser}</if>
            <if test="createdDate != null "> and created_date = #{createdDate}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
        </where>
        ORDER BY visitDate DESC
    </select>

    <select id="selectErpAccountVisitListByTaskIds" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        <include refid="selectErpAccountVisitVo"/>
        where task_id in (
            <foreach collection="taskIds" item="taskId" separator=",">
                #{taskId}
            </foreach>
        )
    </select>

    <select id="getList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        select
            eav.*,
            su.nick_name as accountUser,
            sd.dept_name as accountDept,
            ee.vc_company_name,
            eptd.vc_tax_name,
            GROUP_CONCAT(err.id) as returnIds,
            GROUP_CONCAT(DISTINCT eo.vc_order_number) AS orderNumbers,
            GROUP_CONCAT(DISTINCT eavtrt.name) AS taxRiskStr,
            eavi.name AS intentionLevelStr,
            GROUP_CONCAT(DISTINCT together.nick_name) AS togetherAccountUserNames,
            CASE eav.visit_way WHEN 2 THEN '合规拜访' ELSE '日常拜访' END AS visitWayStr
        from erp_account_visit eav
        LEFT JOIN erp_orders eo ON ( eo.visit_id = eav.id AND eo.num_valid_status = 0 AND eo.num_create_order_examine_status = 5 )
        LEFT JOIN erp_retainage_return err ON ( err.num_order_id = eo.id AND err.num_status = 1 )
        left join erp_enterprise ee on ee.id = eav.enterprise_id
        left join erp_product_tax_dict eptd on eptd.num_tax_id = ee.num_corporate_property_id
        LEFT JOIN erp_account_visit_product eavp ON eavp.account_visit = eav.id
        LEFT JOIN erp_product_type ept ON ept.num_type_id = eavp.product_id
        left join sys_user su on su.user_id = eav.account_user_id
        left join sys_dept sd on sd.dept_id = su.dept_id
        left join erp_account_visit_tax_risk eavtr on eavtr.visit_id = eav.id
        left join erp_account_visit_tax_risk_type eavtrt on eavtrt.id = eavtr.type_id
        left join erp_account_visit_intention eavi on eavi.id = eav.intention_level
        left join sys_user together on FIND_IN_SET(together.user_id,eav.together_account_user_id)
        <where>
            <if test="visitDateBegin != null "> and eav.visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
            <if test="visitDateEnd != null "> and eav.visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
            <if test="feeDateBegin != null "> and err.dat_finance_collection_time <![CDATA[>=]]> #{feeDateBegin}</if>
            <if test="feeDateEnd != null "> and err.dat_finance_collection_time <![CDATA[<=]]> #{feeDateEnd}</if>
            <if test="accountUserId != null "> and eav.account_user_id = #{accountUserId}</if>
            <if test="intention != null "> and eav.intention = #{intention}</if>
            <if test="visitType != null "> and eav.visit_type = #{visitType}</if>
            <if test="togetherAccountUserId != null "> and FIND_IN_SET(#{togetherAccountUserId},eav.together_account_user_id)</if>
            <if test="enterpriseName != null  and enterpriseName != ''"> and ee.vc_company_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="productTypeIdList != null and productTypeIdList.size > 0">
                and eavp.product_id in
                <foreach item="productTypeId" collection="productTypeIdList" open="(" separator="," close=")">
                    #{productTypeId}
                </foreach>
            </if>
            <if test="accountDeptIdList != null and accountDeptIdList.size > 0">
                and sd.dept_id in
                <foreach item="accountDeptId" collection="accountDeptIdList" open="(" separator="," close=")">
                    #{accountDeptId}
                </foreach>
            </if>
            <if test="taxRiskList != null and taxRiskList.size > 0">
                and eavtr.type_id in
                <foreach item="taxRiskId" collection="taxRiskList" open="(" separator="," close=")">
                    #{taxRiskId}
                </foreach>
            </if>
            <if test="intentionLevelList != null and intentionLevelList.size > 0">
                and eav.intention_level in
                <foreach item="intentionLevel" collection="intentionLevelList" open="(" separator="," close=")">
                    #{intentionLevel}
                </foreach>
            </if>
            <if test="visitWayList != null and visitWayList.size > 0">
                and eav.visit_way in
                <foreach item="visitWay" collection="visitWayList" open="(" separator="," close=")">
                    #{visitWay}
                </foreach>
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        <if test="type == 1 "> GROUP BY eav.id</if>
        <if test="type == 2 "> GROUP BY eav.id,eo.id</if>
        ORDER BY eav.visit_date DESC,eav.id DESC
    </select>

    <select id="statisticAnalysis" resultType="com.nnb.erp.domain.vo.AccountVisitStatisticAnalysis">
        SELECT
            a.*,
            IFNULL(ROUND( ROUND( a.dealCount / a.intentionCount, 2 ) * 100, 0 ),0) AS intentionDealRage
        FROM
            (
                SELECT
                    su.nick_name AS accountUserName,
                    sd.dept_name AS accountUserDeptName,
                    COUNT( DISTINCT eav.id ) AS visitCount,
                    SUM( CASE WHEN eav.visit_type = 1 THEN 1 ELSE 0 END ) AS typeCount1,
                    SUM( CASE WHEN eav.visit_type = 2 THEN 1 ELSE 0 END ) AS typeCount2,
                    SUM( CASE WHEN eav.visit_type = 3 THEN 1 ELSE 0 END ) AS typeCount3,
                    intention.intentionCount AS intentionCount,
                    deal.dealCount,
                    IFNULL( ROUND( ROUND( deal.dealCount/ COUNT( DISTINCT eav.id ), 2 ) * 100, 0 ), 0 ) AS mainDealRage
                FROM
                    erp_account_visit eav
                    LEFT JOIN sys_user su ON su.user_id = eav.account_user_id
                    LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
                    LEFT JOIN (
                        SELECT
                            account_user_id as accountUserId,
                            COUNT( DISTINCT enterprise_id ) as intentionCount
                        FROM
                            erp_account_visit
                        WHERE
                            intention = 1
                            <if test="visitDateBegin != null "> and visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
                            <if test="visitDateEnd != null "> and visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
                        GROUP BY
                            account_user_id
                    ) intention ON intention.accountUserId = eav.account_user_id
                    LEFT JOIN (
                        SELECT
                            COUNT( DISTINCT ee.id ) AS dealCount,
                            eav.account_user_id AS accountUserId
                        FROM
                            erp_account_visit eav
                                LEFT JOIN erp_orders eo ON eav.id = eo.visit_id
                                LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
                                LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
                                LEFT JOIN erp_order_performance eop ON eop.order_id = eo.id
                                LEFT JOIN sys_user pSu ON pSu.user_id = eop.user_id
                                LEFT JOIN sys_dept pSd ON pSd.dept_id = pSu.dept_id
                                LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
                                LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
                        WHERE
                            eo.num_valid_status = 0
                              AND eo.num_create_order_examine_status = 5
                              AND eo.visit_id IS NOT NULL
                              AND
                                IF
                                    (
                                            FIND_IN_SET( 83, sd.ancestors )
                                            OR sd.dept_id = 83,
                                            FIND_IN_SET( 83, sd.ancestors )
                                                OR sd.dept_id = 83,
                                            ( FIND_IN_SET( 83, pSd.ancestors ) OR pSd.dept_id = 83 )
                                                AND eop.`status` = 1
                                    )

                              <if test="visitDateBegin != null "> and eav.visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
                              <if test="visitDateEnd != null "> and eav.visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
                            GROUP BY
                                eav.account_user_id
                        ) deal ON deal.accountUserId = eav.account_user_id
                        <where>
                            <if test="accountUserId != null "> and eav.account_user_id = #{accountUserId}</if>
                            <if test="accountDeptIdList != null and accountDeptIdList.size > 0">
                                and sd.dept_id in
                                <foreach item="accountDeptId" collection="accountDeptIdList" open="(" separator="," close=")">
                                    #{accountDeptId}
                                </foreach>
                            </if>
                            <if test="visitDateBegin != null "> and eav.visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
                            <if test="visitDateEnd != null "> and eav.visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
                        </where>
                GROUP BY
                    eav.account_user_id
            ) a ORDER BY a.mainDealRage DESC
    </select>

    <select id="getCooperateList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        SELECT
            eav.id AS id,
            GROUP_CONCAT( DISTINCT ept.vc_type_name ) AS cooperateProductStr,
            MIN( eo.dat_signing_date ) AS cooperateDate
        FROM
            erp_account_visit eav
        LEFT JOIN erp_orders eo ON eo.visit_id = eav.id
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id
        LEFT JOIN erp_product_type ept ON epn.num_type_id = ept.num_type_id
        WHERE
            eo.num_valid_status = 0
            AND eo.num_create_order_examine_status = 5
            AND eso.num_is_deprecated = 0
            AND eso.num_status = 1
            AND eav.id IN
            <foreach item="visitId" collection="visitIdList" open="(" separator="," close=")">
                #{visitId}
            </foreach>
        GROUP BY
            eav.id
    </select>

    <select id="getGroupMonthFeeList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        SELECT
            eo.visit_id as visitId,
            err.visit_after_months as afterMonth,
            SUM( etvfi.fee ) as monthFee,
            sd.ancestors as ancestors,
            sd.dept_id as deptId,
            eo.id as orderId
        FROM
            erp_orders eo
        LEFT JOIN erp_retainage_return err ON err.num_order_id = eo.id
        LEFT JOIN erp_transaction_voucher_follow etvf ON FIND_IN_SET( etvf.id, err.follow_ids )
        LEFT JOIN erp_transaction_voucher etv ON etv.id = etvf.transaction_voucher
        LEFT JOIN erp_transaction_voucher_follow_info etvfi ON etvfi.follow_id = etvf.id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        WHERE
            eo.num_valid_status = 0
            AND eo.num_create_order_examine_status = 5
            AND etvfi.`status` = 2
            AND etvf.type = 1
            AND etvf.`status` = 2
            AND etv.type IN ( 1, 2 )
            AND err.num_status = 1
            AND err.visit_after_months > 0
            and eo.visit_id in
            <foreach item="visitId" collection="visitIdList" open="(" separator="," close=")">
                #{visitId}
            </foreach>
        GROUP BY eo.visit_id, eo.id, err.visit_after_months
    </select>

    <select id="getGroupMonthPerformanceFeeList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        SELECT
            eo.id as orderId,
            SUM(eopi.fee) as monthFee,
            err.visit_after_months as afterMonth
        FROM
            erp_orders eo
                LEFT JOIN erp_retainage_return err ON err.num_order_id = eo.id
                LEFT JOIN erp_retainage_return_detail errd ON errd.num_retainage_return_id = err.id
                LEFT JOIN erp_order_performance_info eopi ON eopi.return_detail_id = errd.id
                LEFT JOIN sys_user su ON su.user_id = eopi.user_id
                LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        WHERE
            eo.id IN
            <foreach item="orderId" collection="orderIdList" open="(" separator="," close=")">
                #{orderId}
            </foreach>
          AND err.num_status = 1
          AND err.visit_after_months > 0
          AND ( FIND_IN_SET( 83, sd.ancestors ) OR sd.dept_id = 83 )
        GROUP BY eo.id , err.visit_after_months
    </select>

    <select id="selectErpAccountVisitById" parameterType="Long" resultMap="ErpAccountVisitResult">
        <include refid="selectErpAccountVisitVo"/>
        where id = #{id}
    </select>

    <insert id="insertErpAccountVisit" parameterType="ErpAccountVisit" useGeneratedKeys="true" keyProperty="id">
        insert into erp_account_visit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="accountUserId != null">account_user_id,</if>
            <if test="visitDate != null">visit_date,</if>
            <if test="visitType != null">visit_type,</if>
            <if test="visitArea != null">visit_area,</if>
            <if test="visitReason != null">visit_reason,</if>
            <if test="visitReasonDetail != null">visit_reason_detail,</if>
            <if test="visitUserName != null">visit_user_name,</if>
            <if test="togetherAccountUserId != null">together_account_user_id,</if>
            <if test="intention != null">intention,</if>
            <if test="visitAddress != null">visit_address,</if>
            <if test="customerDemand != null">customer_demand,</if>
            <if test="customerFeedback != null">customer_feedback,</if>
            <if test="finalResult != null">final_result,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdDate != null">created_date,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="photo != null">photo,</if>
            <if test="intentionLevel != null">intention_level,</if>
            <if test="visitContentRecord != null and visitContentRecord != ''">visit_content_record,</if>
            <if test="taskId != null">task_id,</if>
            <if test="visitWay != null">visit_way,</if>
            <if test="businessModel != null">business_model,</if>
            <if test="noOrderReason != null">no_order_reason,</if>
            <if test="otherHarvest != null">other_harvest,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="accountUserId != null">#{accountUserId},</if>
            <if test="visitDate != null">#{visitDate},</if>
            <if test="visitType != null">#{visitType},</if>
            <if test="visitArea != null">#{visitArea},</if>
            <if test="visitReason != null">#{visitReason},</if>
            <if test="visitReasonDetail != null">#{visitReasonDetail},</if>
            <if test="visitUserName != null">#{visitUserName},</if>
            <if test="togetherAccountUserId != null">#{togetherAccountUserId},</if>
            <if test="intention != null">#{intention},</if>
            <if test="visitAddress != null">#{visitAddress},</if>
            <if test="customerDemand != null">#{customerDemand},</if>
            <if test="customerFeedback != null">#{customerFeedback},</if>
            <if test="finalResult != null">#{finalResult},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdDate != null">#{createdDate},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="photo != null">#{photo},</if>
            <if test="intentionLevel != null">#{intentionLevel},</if>
            <if test="visitContentRecord != null and visitContentRecord != ''">#{visitContentRecord},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="visitWay != null">#{visitWay},</if>
            <if test="businessModel != null">#{businessModel},</if>
            <if test="noOrderReason != null">#{noOrderReason},</if>
            <if test="otherHarvest != null">#{otherHarvest},</if>
         </trim>
    </insert>

    <update id="updateErpAccountVisit" parameterType="ErpAccountVisit">
        update erp_account_visit
        <trim prefix="SET" suffixOverrides=",">
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="accountUserId != null">account_user_id = #{accountUserId},</if>
            <if test="visitDate != null">visit_date = #{visitDate},</if>
            <if test="visitType != null">visit_type = #{visitType},</if>
            <if test="visitArea != null">visit_area = #{visitArea},</if>
            <if test="visitReason != null">visit_reason = #{visitReason},</if>
            <if test="visitReasonDetail != null">visit_reason_detail = #{visitReasonDetail},</if>
            <if test="visitUserName != null">visit_user_name = #{visitUserName},</if>
            <if test="togetherAccountUserId != null">together_account_user_id = #{togetherAccountUserId},</if>
            <if test="intention != null">intention = #{intention},</if>
            <if test="visitAddress != null">visit_address = #{visitAddress},</if>
            <if test="customerDemand != null">customer_demand = #{customerDemand},</if>
            <if test="customerFeedback != null">customer_feedback = #{customerFeedback},</if>
            <if test="finalResult != null">final_result = #{finalResult},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="visitWay != null">visit_way = #{visitWay},</if>
            <if test="businessModel != null">business_model = #{businessModel},</if>
            <if test="noOrderReason != null">no_order_reason = #{noOrderReason},</if>
            <if test="otherHarvest != null">other_harvest = #{otherHarvest},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="intentionLevel != null">intention_level = #{intentionLevel},</if>
            <if test="visitContentRecord != null and visitContentRecord != ''">visit_content_record = #{visitContentRecord},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpAccountVisitById" parameterType="Long">
        delete from erp_account_visit where id = #{id}
    </delete>

    <delete id="deleteErpAccountVisitByIds" parameterType="String">
        delete from erp_account_visit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getOrderListByVisitId" resultType="com.nnb.erp.domain.vo.AccountVisitOrder">
        SELECT
            eso.id AS serviceOrderId,
            eo.vc_order_number,
            eo.dat_signing_date,
            su.nick_name AS signUserName,
            sd.dept_name AS signUserDeptName,
            sd.ancestors,
            sd.dept_id,
            eps.vc_service_name AS productName,
            eso.num_total_price AS totalPrice,
            MIN( eo.dat_signing_date ) AS cooperateDate,
            IFNULL( SUM( etvfi.fee ), 0 ) AS payPrice
        FROM
            erp_orders eo
        LEFT JOIN erp_retainage_return err ON ( err.num_order_id = eo.id AND err.num_status = 1 )
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_service eps ON epd.num_service_id = eps.num_service_id
        LEFT JOIN erp_transaction_voucher_follow etvf ON FIND_IN_SET( etvf.id, err.follow_ids )
        LEFT JOIN erp_transaction_voucher etv ON etv.id = etvf.transaction_voucher
        LEFT JOIN erp_transaction_voucher_follow_info etvfi ON ( etvfi.follow_id = etvf.id AND etvfi.product_id = eso.num_product_id )
        WHERE
        eo.visit_id = #{id}
        AND eso.num_is_deprecated = 0
        AND eso.num_status = 1
        AND etvf.type = 1
        AND etvf.`status` = 2
        AND etv.type IN ( 1, 2 )
        AND etvfi.`status` = 2
        <if test="feeDateBegin != null "> and err.dat_finance_collection_time <![CDATA[>=]]> #{feeDateBegin}</if>
        <if test="feeDateEnd != null "> and err.dat_finance_collection_time <![CDATA[<=]]> #{feeDateEnd}</if>
        GROUP BY
            eo.id,eso.num_product_id
    </select>




    <select id="getIntentionProductList" resultType="com.nnb.erp.domain.vo.AccountVisitProduct">
        SELECT
            ept.vc_type_name AS productName,
            a.productCount AS productCount
        FROM
            (
                SELECT
                       COUNT( eavp.product_id ) AS productCount,
                       eavp.product_id
                FROM erp_account_visit_product eavp
                LEFT JOIN erp_account_visit eav on eav.id = eavp.account_visit
                LEFT JOIN sys_user su ON su.user_id = eav.account_user_id
                <where>
                    <if test="accountUserId != null "> and eav.account_user_id = #{accountUserId}</if>
                    <if test="accountDeptIdList != null and accountDeptIdList.size > 0">
                        and su.dept_id in
                        <foreach item="accountDeptId" collection="accountDeptIdList" open="(" separator="," close=")">
                            #{accountDeptId}
                        </foreach>
                    </if>
                    <if test="visitDateBegin != null "> and eav.visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
                    <if test="visitDateEnd != null "> and eav.visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
                </where>

                GROUP BY eavp.product_id ) a
            LEFT JOIN erp_product_type ept ON a.product_id = ept.num_type_id
        ORDER BY
            a.productCount DESC
    </select>

    <select id="getDealProductList" resultType="com.nnb.erp.domain.vo.AccountVisitProduct">
        SELECT
            ept.vc_type_name AS productName,
            a.productCount AS productCount,
            a.payPrice AS payPrice
        FROM
            (
            SELECT
                epn.num_type_id AS typeId,
                sum( eso.num_pay_price ) AS payPrice,
                COUNT( eso.num_product_id ) AS productCount
            FROM
                erp_orders eo
                LEFT JOIN erp_service_orders eso ON eo.id = eso.num_order_id
                LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
                LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id
                LEFT JOIN erp_account_visit eav ON eav.id = eo.visit_id
                LEFT JOIN sys_user su ON su.user_id = eav.account_user_id
                <if test="type == 1 ">
                LEFT JOIN erp_transaction_voucher_follow etvf ON etvf.order_id = eo.id
                LEFT JOIN erp_transaction_voucher_follow_info etvfi ON etvfi.follow_id = etvf.id
                LEFT JOIN erp_transaction_voucher etv ON etv.id = etvf.transaction_voucher
                </if>
            WHERE
                eo.num_valid_status = 0
                AND eso.num_is_deprecated = 0
                AND eso.num_status = 1
                AND eo.visit_id IS NOT NULL
                <if test="type == 1 ">
                AND etvf.type IN (1,2)
                AND etvfi.`status` = 2
                </if>
                <if test="accountUserId != null "> and eav.account_user_id = #{accountUserId}</if>
                <if test="accountDeptIdList != null and accountDeptIdList.size > 0">
                    and su.dept_id in
                    <foreach item="accountDeptId" collection="accountDeptIdList" open="(" separator="," close=")">
                        #{accountDeptId}
                    </foreach>
                </if>
                <if test="visitDateBegin != null "> and eav.visit_date <![CDATA[>=]]> #{visitDateBegin}</if>
                <if test="visitDateEnd != null "> and eav.visit_date <![CDATA[<=]]> #{visitDateEnd}</if>
            GROUP BY
                epn.num_type_id
            ) a
            LEFT JOIN erp_product_type ept ON ept.num_type_id = a.typeId
        <if test="type == 1 "> ORDER BY a.payPrice DESC </if>
        <if test="type == 2 "> ORDER BY a.productCount DESC </if>
    </select>

    <select id="getIntentionProductByVisitIdList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        SELECT
            eavp.account_visit as visitId,
            GROUP_CONCAT( DISTINCT ept.vc_type_name ) AS intentionProductStr
        FROM
            erp_account_visit_product eavp
        LEFT JOIN erp_product_type ept ON ept.num_type_id = eavp.product_id
        WHERE
            eavp.account_visit IN
            <foreach item="visitId" collection="visitIdList" open="(" separator="," close=")">
                #{visitId}
            </foreach>
        GROUP BY
            eavp.account_visit
    </select>

    <update id="updateOrderVisitId">
        update erp_orders set visit_id = #{visitId}
        where id IN
        <foreach item="orderId" collection="orderIdList" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <select id="selectFeeByReturnIdList" resultType="com.nnb.erp.domain.vo.ErpAccountVisitVo">
        SELECT
            err.id as id,
            IFNULL(SUM( etvf.fee ),0) AS fee
        FROM
        erp_retainage_return err
        LEFT JOIN erp_transaction_voucher_follow etvf ON FIND_IN_SET( etvf.id, err.follow_ids )
        LEFT JOIN erp_transaction_voucher etv ON etv.id = etvf.transaction_voucher
        LEFT JOIN erp_transaction_voucher_follow_info etvfi ON etvfi.follow_id = etvf.id
        WHERE
            etvf.type = 1
            AND etvf.`status` = 2
            AND etv.type IN ( 1, 2 )
            AND etvfi.`status` = 2
            AND err.id IN
            <foreach item="returnId" collection="returnIdList" open="(" separator="," close=")">
                #{returnId}
            </foreach>
        GROUP BY err.id
    </select>

    <select id="selectProductList" resultType="com.nnb.erp.domain.ErpAccountVisitProduct">
        select id, account_visit as accountVisitId, product_id as productId
        from erp_account_visit_product
        where account_visit in (
            <foreach collection="ids" item="accountVisitId" separator=",">
                #{accountVisitId}
            </foreach>
        )
    </select>

    <select id="selectTaxRiskList" resultType="com.nnb.erp.domain.ErpAccountVisitTaxRisk">
        select * from erp_account_visit_tax_risk
        where visit_id in (
        <foreach collection="ids" item="visitId" separator=",">
            #{visitId}
        </foreach>
        )
    </select>

    <update id="updateErpAccountVisitRecord" parameterType="ErpAccountVisit">
        update erp_account_visit
        <trim prefix="SET" suffixOverrides=",">
            <if test="visitContentRecord != null and visitContentRecord != ''">
                visit_content_record = #{visitContentRecord},
            </if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectAccountByEnterpriseId" resultType="com.nnb.erp.domain.vo.service.SServiceVo">
        SELECT
            ssm.num_enterprise_id as numEnterpriseId,
            su.nick_name as accountUserName,
            sd.dept_name as accountUserDept
        FROM
            s_service_main ssm
        LEFT JOIN sys_user su ON su.user_id = ssm.account_user_id
        LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
        WHERE
            ssm.id IN (
                SELECT MAX( id ) FROM s_service_main
                WHERE num_enterprise_id in (
                <foreach collection="enterpriseIdList" item="enterpriseId" separator=",">
                    #{enterpriseId}
                </foreach>
                )
                AND service_status NOT IN ( 8, 9, 10 ) GROUP BY num_enterprise_id
            )

    </select>
</mapper>
