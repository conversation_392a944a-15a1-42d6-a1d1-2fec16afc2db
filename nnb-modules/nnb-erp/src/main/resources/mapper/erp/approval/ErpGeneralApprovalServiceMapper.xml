<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.approval.ErpGeneralApprovalServiceMapper">
    
    <resultMap type="ErpGeneralApprovalService" id="ErpGeneralApprovalServiceResult">
        <result property="id"    column="id"    />
        <result property="approveId"    column="approve_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="reason"    column="reason"    />
        <result property="money"    column="money"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="payeeName"    column="payee_name"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="attachment"    column="attachment"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectErpGeneralApprovalServiceVo">
        select id, approve_id, dept_id, reason, money, payment_time, payee_name, bank_account, opening_bank, attachment, remark, create_time, update_time, create_user, update_user, del_flag from erp_general_approval_service
    </sql>

    <select id="selectErpGeneralApprovalServiceList" parameterType="ErpGeneralApprovalService" resultMap="ErpGeneralApprovalServiceResult">
        <include refid="selectErpGeneralApprovalServiceVo"/>
        <where>  
            <if test="approveId != null "> and approve_id = #{approveId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="paymentTime != null "> and payment_time = #{paymentTime}</if>
            <if test="payeeName != null  and payeeName != ''"> and payee_name like concat('%', #{payeeName}, '%')</if>
            <if test="bankAccount != null  and bankAccount != ''"> and bank_account = #{bankAccount}</if>
            <if test="openingBank != null  and openingBank != ''"> and opening_bank = #{openingBank}</if>
            <if test="attachment != null  and attachment != ''"> and attachment = #{attachment}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectErpGeneralApprovalServiceById" parameterType="Long" resultMap="ErpGeneralApprovalServiceResult">
        <include refid="selectErpGeneralApprovalServiceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpGeneralApprovalService" parameterType="ErpGeneralApprovalService" useGeneratedKeys="true" keyProperty="id">
        insert into erp_general_approval_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="approveId != null">approve_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="reason != null">reason,</if>
            <if test="money != null">money,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="payeeName != null">payee_name,</if>
            <if test="bankAccount != null">bank_account,</if>
            <if test="openingBank != null">opening_bank,</if>
            <if test="attachment != null">attachment,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="approveId != null">#{approveId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="reason != null">#{reason},</if>
            <if test="money != null">#{money},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="payeeName != null">#{payeeName},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
            <if test="openingBank != null">#{openingBank},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateErpGeneralApprovalService" parameterType="ErpGeneralApprovalService">
        update erp_general_approval_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="approveId != null">approve_id = #{approveId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="money != null">money = #{money},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="payeeName != null">payee_name = #{payeeName},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
            <if test="openingBank != null">opening_bank = #{openingBank},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpGeneralApprovalServiceById" parameterType="Long">
        delete from erp_general_approval_service where id = #{id}
    </delete>

    <delete id="deleteErpGeneralApprovalServiceByIds" parameterType="String">
        delete from erp_general_approval_service where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>