<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.approval.ErpNewApprovalUserMapper">
    
    <resultMap type="ErpNewApprovalUser" id="ErpNewApprovalUserResult">
        <result property="id"    column="id"    />
        <result property="approvalType"    column="approval_type"    />
        <result property="approvalNodeId"    column="approval_node_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectErpNewApprovalUserVo">
        select id, approval_type, approval_node_id, dept_id, user_id, city_id, sort, create_time, update_time, create_user, update_user, del_flag from erp_new_approval_user
    </sql>

    <select id="selectErpNewApprovalUserList" parameterType="ErpNewApprovalUser" resultMap="ErpNewApprovalUserResult">
        <include refid="selectErpNewApprovalUserVo"/>
        <where>  
            <if test="approvalType != null "> and approval_type = #{approvalType}</if>
            <if test="approvalNodeId != null "> and approval_node_id = #{approvalNodeId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectErpNewApprovalUserById" parameterType="Long" resultMap="ErpNewApprovalUserResult">
        <include refid="selectErpNewApprovalUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpNewApprovalUser" parameterType="ErpNewApprovalUser" useGeneratedKeys="true" keyProperty="id">
        insert into erp_new_approval_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="approvalType != null">approval_type,</if>
            <if test="approvalNodeId != null">approval_node_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="approvalType != null">#{approvalType},</if>
            <if test="approvalNodeId != null">#{approvalNodeId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateErpNewApprovalUser" parameterType="ErpNewApprovalUser">
        update erp_new_approval_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="approvalType != null">approval_type = #{approvalType},</if>
            <if test="approvalNodeId != null">approval_node_id = #{approvalNodeId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpNewApprovalUserById" parameterType="Long">
        delete from erp_new_approval_user where id = #{id}
    </delete>

    <delete id="deleteErpNewApprovalUserByIds" parameterType="String">
        delete from erp_new_approval_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectErpNewApprovalUser" parameterType="ErpNewApprovalUser" resultMap="ErpNewApprovalUserResult">
        <include refid="selectErpNewApprovalUserVo"/>
        <where>
            <if test="approvalType != null "> and approval_type = #{approvalType}</if>
            <if test="approvalNodeId != null "> and approval_node_id = #{approvalNodeId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="cityId != null">and city_id = #{cityId}</if>
        </where>
    </select>
</mapper>