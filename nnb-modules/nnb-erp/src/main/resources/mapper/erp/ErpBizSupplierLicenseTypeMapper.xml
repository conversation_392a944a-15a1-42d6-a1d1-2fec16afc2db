<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpBizSupplierLicenseTypeMapper">
    
    <resultMap type="ErpBizSupplierLicenseType" id="ErpBizSupplierLicenseTypeResult">
        <result property="id"    column="id"    />
        <result property="numBizSupplierInfoId"    column="num_biz_supplier_info_id"    />
        <result property="numBizLicenseTypeId"    column="num_biz_license_type_id"    />
    </resultMap>

    <sql id="selectErpBizSupplierLicenseTypeVo">
        select id, num_biz_supplier_info_id, num_biz_license_type_id from erp_biz_supplier_license_type
    </sql>

    <select id="selectErpBizSupplierLicenseTypeList" parameterType="ErpBizSupplierLicenseType" resultMap="ErpBizSupplierLicenseTypeResult">
        <include refid="selectErpBizSupplierLicenseTypeVo"/>
        <where>  
            <if test="numBizSupplierInfoId != null "> and num_biz_supplier_info_id = #{numBizSupplierInfoId}</if>
            <if test="numBizLicenseTypeId != null "> and num_biz_license_type_id = #{numBizLicenseTypeId}</if>
        </where>
    </select>
    
    <select id="selectErpBizSupplierLicenseTypeById" parameterType="Long" resultMap="ErpBizSupplierLicenseTypeResult">
        <include refid="selectErpBizSupplierLicenseTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpBizSupplierLicenseType" parameterType="ErpBizSupplierLicenseType" useGeneratedKeys="true" keyProperty="id">
        insert into erp_biz_supplier_license_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numBizSupplierInfoId != null">num_biz_supplier_info_id,</if>
            <if test="numBizLicenseTypeId != null">num_biz_license_type_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numBizSupplierInfoId != null">#{numBizSupplierInfoId},</if>
            <if test="numBizLicenseTypeId != null">#{numBizLicenseTypeId},</if>
         </trim>
    </insert>

    <update id="updateErpBizSupplierLicenseType" parameterType="ErpBizSupplierLicenseType">
        update erp_biz_supplier_license_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="numBizSupplierInfoId != null">num_biz_supplier_info_id = #{numBizSupplierInfoId},</if>
            <if test="numBizLicenseTypeId != null">num_biz_license_type_id = #{numBizLicenseTypeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpBizSupplierLicenseTypeById" parameterType="Long">
        delete from erp_biz_supplier_license_type where id = #{id}
    </delete>

    <delete id="deleteErpBizSupplierLicenseTypeByIds" parameterType="String">
        delete from erp_biz_supplier_license_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>