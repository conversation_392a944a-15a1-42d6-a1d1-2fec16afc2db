<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpOrderCountMapper">

    <select id="countPayPriceOrLastPrice" resultType="java.util.Map">
        select
        IFNULL(SUM(num_pay_price), 0)  as 'payPrice',
        IFNULL(SUM(num_last_price), 0) as 'lastPrice'
        from erp_orders eo
        <if test="orderCountDto.deptIds.size > 0">
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        </if>
        where eo.num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>
    <select id="countRefundPrice" resultType="java.util.Map">
        select
        IFNULL(SUM(num_refund_price), 0)  as 'refundPrice'
        from erp_service_order_refund esor
        left join sys_user su on su.user_id = esor.num_created_by
        left join sys_dept sd on sd.dept_id = su.dept_id
        where esor.num_status = 1
        and esor.dat_signing_datecreated_time &gt;= #{orderCountDto.beginTime}
        and esor.dat_signing_datecreated_time &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and esor.num_created_by = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>
    <select id="countServiceMain" resultType="java.lang.Integer">
        select count(a) as 'num'
        from (
        select GROUP_CONCAT(DISTINCT (ssm.service_status) SEPARATOR ',') a
        from erp_orders eo
        left join s_service_main ssm on ssm.order_id = eo.id
        <if test="orderCountDto.deptIds.size > 0">
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        </if>
        where eo.num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
        group by ssm.order_id) temp
        where
        <if test="status == 3">
            temp.a = #{status}
        </if>
        <if test="status == 4">
            temp.a = #{status} or find_in_set(#{status}, temp.a)
        </if>
    </select>
    <select id="orderChartCount" resultType="java.util.Map">
        select
        IFNULL(SUM(num_pay_price), 0)  as 'payPrice',
        IFNULL(SUM(num_last_price), 0) as 'lastPrice',
        DATE_FORMAT(dat_signing_date, '%Y-%m-%d') 'time'
        from erp_orders eo
        <if test="orderCountDto.deptIds.size > 0">
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        </if>
        where eo.num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
        group by eo.dat_signing_date
        order by eo.dat_signing_date
    </select>
    <select id="hotProductCount" resultType="com.nnb.erp.domain.vo.HotProductVo">
        select
        eso.num_product_id as productId,
        esoi.vc_product_type as type,
        esoi.vc_product_name as name,
        esoi.vc_unit as unit,
        SUBSTRING(esoi.vc_area,1,3) as city,
        esoi.num_price as price,
        count(num_product_id) as num
        from erp_orders eo
        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_service_orders_info esoi on esoi.num_service_orders = eso.id
        <if test="orderCountDto.deptIds.size > 0">
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        </if>
        where num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
        group by eso.num_product_id
        order by count(num_product_id) desc
        limit 10;
    </select>
    <select id="rankingCountDept" resultType="com.nnb.erp.domain.vo.RankingCountVo">
        select
        IFNULL(SUM(eo.num_pay_price), 0)  as 'price',
        sd.dept_name as 'name'
        from erp_orders eo
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        where num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
        group by sd.dept_id
        order by SUM(num_pay_price) desc
        limit 10;
    </select>
    <select id="rankingCountUser" resultType="com.nnb.erp.domain.vo.RankingCountVo">
        select
        IFNULL(SUM(eo.num_pay_price), 0)  as 'price',
        su.nick_name as 'name'
        from erp_orders eo
            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
        where num_create_order_examine_status = 5
        and eo.dat_signing_date &gt;= #{orderCountDto.beginTime}
        and eo.dat_signing_date &lt;= #{orderCountDto.endTime}
        <if test="orderCountDto.userId != null and orderCountDto.userId != ''">
            and eo.num_user_id = #{orderCountDto.userId}
        </if>
        <if test="orderCountDto.deptIds.size > 0">
            and sd.dept_id in (
            <foreach collection="orderCountDto.deptIds" item="id" separator=",">
                #{id}
            </foreach>
            )
        </if>
        group by su.user_id
        order by SUM(num_pay_price) desc
        limit 10
    </select>
</mapper>
