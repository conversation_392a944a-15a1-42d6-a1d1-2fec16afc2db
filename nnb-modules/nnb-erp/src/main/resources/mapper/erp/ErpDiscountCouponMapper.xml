<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpDiscountCouponMapper">

    <resultMap type="com.nnb.erp.domain.ErpDiscountCoupon" id="ErpDiscountCouponResult">
        <result property="id"    column="id"    />
        <result property="discountCouponAmountId"    column="discount_coupon_amount_id"    />
        <result property="discountAmount"    column="discount_amount"    />
        <result property="belongUserId"    column="belong_user_id"    />
        <result property="status"    column="status"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="numType"    column="num_type"    />
        <result property="minPrice"    column="min_price"    />
        <result property="xcxPhone"    column="xcx_phone"    />
        <result property="validityPeriod"    column="validity_period"    />
        <result property="actStartTime"    column="act_start_time"    />
        <result property="actEndTime"    column="act_end_time"    />
        <result property="remark"    column="remark"    />
        <result property="xcxCouponConfigId"    column="xcx_coupon_config_id"    />
    </resultMap>

    <sql id="selectErpDiscountCouponVo">
        select id, discount_coupon_amount_id, discount_amount, belong_user_id, status, create_user, update_user, create_time, update_time,num_product_id,clue_id,clientId,num_type,min_price,xcx_phone, validity_period, act_start_time, act_end_time, remark, xcx_coupon_config_id from erp_discount_coupon
    </sql>

    <select id="selectErpDiscountCouponList" parameterType="ErpDiscountCoupon" resultMap="ErpDiscountCouponResult">
        <include refid="selectErpDiscountCouponVo"/>
        <where>
            <if test="discountCouponAmountId != null "> and discount_coupon_amount_id = #{discountCouponAmountId}</if>
            <if test="discountAmount != null "> and discount_amount = #{discountAmount}</if>
            <if test="belongUserId != null "> and belong_user_id = #{belongUserId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
            <if test="numProductId != null">and num_product_id = #{numProductId}</if>
            <if test="clueId != null">and clue_id = #{clueId}</if>
            <if test="clientId != null">and clientId = #{clientId}</if>
            <if test="numType != null">and num_type = #{numType}</if>
            <if test="minPrice != null">and min_price = #{minPrice}</if>
        </where>
    </select>

    <select id="selectDiscountCouponList" parameterType="ErpDiscountCoupon" resultMap="ErpDiscountCouponResult">
        <include refid="selectErpDiscountCouponVo"/>
        <where>
            <if test="discountCouponAmountId != null "> and discount_coupon_amount_id = #{discountCouponAmountId}</if>
            <if test="discountAmount != null "> and discount_amount = #{discountAmount}</if>
            <if test="belongUserId != null "> and belong_user_id = #{belongUserId}</if>
            <if test="interfaceType != null and interfaceType == 0">
                and status in(0, 1)
             </if>
            <if test="interfaceType != null and interfaceType == 1">
                and status = 0
            </if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
            <if test="numProductId != null">and num_product_id = #{numProductId}</if>
            <if test="clueId != null">and clue_id = #{clueId}</if>
            <if test="clientId != null">and clientId = #{clientId}</if>
            <if test="numType != null">and num_type = #{numType}</if>
            <if test="minPrice != null">and min_price = #{minPrice}</if>
            <if test="xcxPhone != null and xcxPhone != ''">and xcx_phone = #{xcxPhone}</if>
            <if test="xcxCouponConfigId != null">and xcx_coupon_config_id = #{xcxCouponConfigId}</if>
        </where>
    </select>


    <select id="selectErpDiscountCouponVoList"  parameterType="ErpDiscountCoupon" resultType="com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponVo">
        select edc.id, discount_amount discountAmount, edc.status, su.nick_name as belongUser, sd.dept_name as dept,
        edc.create_time,
        edc.num_product_id as numProductId,
        edc.clue_id as clueId,
        edc.clientId as clientId,
        su1.nick_name as createName,
        epn.vc_product_name as vcProductName,
        ee.vc_company_name as vcCompanyName
        from erp_discount_coupon edc
        left join sys_user su on su.user_id = edc.belong_user_id
        left join sys_dept sd on sd.dept_id = su.dept_id
        left join sys_user su1 on su1.user_id = edc.create_user
        LEFT JOIN erp_product_detail epd  on epd.num_product_id=edc.num_product_id
        LEFT JOIN erp_product_name  epn on epn.num_name_id=epd.num_name_id
        LEFT JOIN erp_client ec on ec.id=edc.clientId
        LEFT JOIN erp_enterprise ee on ee.id=num_enterprise_id
        <where>
            <if test="discountCouponAmountId != null "> and edc.discount_coupon_amount_id = #{discountCouponAmountId}</if>
            <if test="discountAmount != null "> and edc.discount_amount = #{discountAmount}</if>
<!--             <if test="belongUserId != null "> and belong_user_id = #{belongUserId}</if> -->
            <if test="status != null"> and edc.status = #{status}</if>
<!--             <if test="createUser != null "> and create_user = #{createUser}</if> -->
            <if test="updateUser != null "> and edc.update_user = #{updateUser}</if>
            <if test="belongUserId != null">
                and (edc.belong_user_id = #{belongUserId} or edc.create_user = #{belongUserId})
            </if>
            <if test="belongUser!=null ">and edc.belong_user_id=#{belongUser}</if>
            <if test="createUser!=null">and edc.create_user = #{createUser}</if>
            <if test="clientId !=null ">and edc.clientId=#{clientId}</if>
            <if test="clueId!=null ">and edc.clue_id=#{clueId}</if>
            <if test="numProductId !=null">and edc.num_product_id=#{numProductId} </if>
        </where>
        order By edc.create_time desc
    </select>

    <select id="selectErpDiscountCouponById" parameterType="Long" resultMap="ErpDiscountCouponResult">
        <include refid="selectErpDiscountCouponVo"/>
        where id = #{id}
    </select>

    <select id="selectErpDiscountCouponByIds" parameterType="Long" resultMap="ErpDiscountCouponResult">
        <include refid="selectErpDiscountCouponVo"/>
        where id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
    <select id="selectCompanyCouponList" resultType="com.nnb.erp.domain.CompanyCoupon">
        select edc.id,
               ee.vc_company_name as companyName,
               edc.discount_amount as discountAmount,
               edc.belong_user_id as belongUserId,
               edc.status as status,
               edc.clientId as clientId,
               edc.num_type as numType,
               edc.min_price as minPrice
        from erp_discount_coupon edc
            left join erp_client ec on ec.id = edc.clientId
            left join erp_enterprise ee on ee.id = ec.num_enterprise_id
        <where>
            edc.num_type = 2
            <if test="companyCoupon.companyName != null and companyCoupon.companyName != ''">
             and ee.vc_company_name like concat('%', #{companyCoupon.companyName}, '%')
            </if>
            <if test="companyCoupon.status != null"> and edc.status = #{companyCoupon.status}</if>
        </where>
        order by edc.create_time desc
    </select>

    <select id="couponUsage" resultType="com.nnb.erp.domain.vo.CouponUsageVo">
        select
            eo.id                                               as orderId,
            eo.vc_order_number                                  as orderNum,
            eso.num_product_id                                  as productId,
            esoi.vc_product_name                                as productName,
            esoi.num_price                                      as originalPrice,
            eso.num_pay_price                                   as realPrice,
            edc.id                                              as discountId,
            edc.discount_amount                                 as discountPrice,
            group_concat(distinct (sd.dept_name) SEPARATOR ',') as sellerDeptName,
            group_concat(distinct (su.nick_name) SEPARATOR ',') as sellerUserName,
            edc.create_time                                     as createTime
        from erp_discount_coupon edc
              left join erp_service_orders eso on edc.id = eso.num_coupon_id
              left join erp_service_orders_info esoi on esoi.num_service_orders = eso.id
              left join erp_orders eo on eso.num_order_id = eo.id
              left join erp_order_payment_term eopt on eopt.num_order_id = eo.id
              left join sys_user su on su.user_id = eopt.num_payee
              left join sys_dept sd on sd.dept_id = su.dept_id
        where edc.num_type = 1
        and edc.status = 1
        <if test="couponUsageDto.productName != null and couponUsageDto.productName != ''">
            and esoi.vc_product_name like concat('%', #{couponUsageDto.productName} ,'%')
        </if>
        <if test="couponUsageDto.sellerId != null">
            and eopt.num_payee = #{couponUsageDto.sellerId}
        </if>
        <if test="couponUsageDto.sellerDeptId != null">
            and sd.dept_id IN
            (SELECT dept_id FROM sys_dept WHERE dept_id = #{couponUsageDto.sellerDeptId} or find_in_set(#{couponUsageDto.sellerDeptId}, ancestors))
        </if>
        group by edc.id, eo.vc_order_number, eso.num_product_id
        order BY edc.id desc
    </select>


    <insert id="insertErpDiscountCoupon" parameterType="ErpDiscountCoupon" useGeneratedKeys="true" keyProperty="id">
        insert into erp_discount_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountCouponAmountId != null">discount_coupon_amount_id,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="belongUserId != null">belong_user_id,</if>
            <if test="status != null">status,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="numProductId != null">num_product_id,</if>
            <if test="clueId != null">clue_id,</if>
            <if test="clientId!=null">clientId,</if>
            <if test="numType!=null">num_type,</if>
            <if test="minPrice!=null">min_price,</if>
            <if test="xcxPhone!=null">xcx_phone,</if>
            <if test="validityPeriod!=null">validity_period,</if>
            <if test="actStartTime!=null">act_start_time,</if>
            <if test="actEndTime!=null">act_end_time,</if>
            <if test="remark!=null">remark,</if>
            <if test="xcxCouponConfigId!=null">xcx_coupon_config_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountCouponAmountId != null">#{discountCouponAmountId},</if>
            <if test="discountAmount != null">#{discountAmount},</if>
            <if test="belongUserId != null">#{belongUserId},</if>
            <if test="status != null">#{status},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="numProductId != null">#{numProductId},</if>
            <if test="clueId != null">#{clueId},</if>
            <if test="clientId!=null">#{clientId},</if>
            <if test="numType!=null">#{numType},</if>
            <if test="minPrice!=null">#{minPrice},</if>
            <if test="xcxPhone!=null">#{xcxPhone},</if>
            <if test="validityPeriod!=null">#{validityPeriod},</if>
            <if test="actStartTime != null">#{actStartTime},</if>
            <if test="actEndTime != null">#{actEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="xcxCouponConfigId != null">#{xcxCouponConfigId},</if>
         </trim>
    </insert>

    <update id="updateErpDiscountCoupon" parameterType="ErpDiscountCoupon">
        update erp_discount_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="discountCouponAmountId != null">discount_coupon_amount_id = #{discountCouponAmountId},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="belongUserId != null">belong_user_id = #{belongUserId},</if>
            <if test="numProductId != null">num_product_id = #{numProductId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="numType != null">num_type = #{numType},</if>
            <if test="minPrice != null">min_price = #{minPrice},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpDiscountCouponById" parameterType="Long">
        delete from erp_discount_coupon where id = #{id}
    </delete>

    <delete id="deleteErpDiscountCouponByIds" parameterType="String">
        delete from erp_discount_coupon where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDCMessage" parameterType="Long" resultType="java.util.HashMap">
        SELECT
            edc.id as id,
            edc.discount_amount as amount,
            CASE
                edc.num_type
            WHEN 1 THEN
                "优惠券"
            WHEN 2 THEN
                eps.vc_service_name
            ELSE ""
            END AS serviceName
        FROM
            erp_discount_coupon edc
        LEFT JOIN erp_service_orders eso ON eso.id = edc.create_user
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_service eps ON eps.num_service_id = epd.num_service_id
        where edc.id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
</mapper>
