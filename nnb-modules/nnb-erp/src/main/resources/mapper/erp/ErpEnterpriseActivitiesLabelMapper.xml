<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpEnterpriseActivitiesLabelMapper">
    
    <resultMap type="ErpEnterpriseActivitiesLabel" id="ErpEnterpriseActivitiesLabelResult">
        <result property="id"    column="id"    />
        <result property="activitiesLabel"    column="activities_label"    />
        <result property="enterpriseId"    column="enterprise_id"    />
        <result property="orderId"    column="order_id"    />
    </resultMap>

    <sql id="selectErpEnterpriseActivitiesLabelVo">
        select id, activities_label, enterprise_id, order_id from erp_enterprise_activities_label
    </sql>

    <select id="selectErpEnterpriseActivitiesLabelList" parameterType="ErpEnterpriseActivitiesLabel" resultMap="ErpEnterpriseActivitiesLabelResult">
        <include refid="selectErpEnterpriseActivitiesLabelVo"/>
        <where>  
            <if test="activitiesLabel != null "> and activities_label = #{activitiesLabel}</if>
            <if test="enterpriseId != null "> and enterprise_id = #{enterpriseId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
        </where>
    </select>
    
    <select id="selectErpEnterpriseActivitiesLabelById" parameterType="Long" resultMap="ErpEnterpriseActivitiesLabelResult">
        <include refid="selectErpEnterpriseActivitiesLabelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpEnterpriseActivitiesLabel" parameterType="ErpEnterpriseActivitiesLabel" useGeneratedKeys="true" keyProperty="id">
        insert into erp_enterprise_activities_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activitiesLabel != null">activities_label,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="orderId != null">order_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activitiesLabel != null">#{activitiesLabel},</if>
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="orderId != null">#{orderId},</if>
         </trim>
    </insert>

    <update id="updateErpEnterpriseActivitiesLabel" parameterType="ErpEnterpriseActivitiesLabel">
        update erp_enterprise_activities_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="activitiesLabel != null">activities_label = #{activitiesLabel},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpEnterpriseActivitiesLabelById" parameterType="Long">
        delete from erp_enterprise_activities_label where id = #{id}
    </delete>

    <delete id="deleteErpEnterpriseActivitiesLabelByIds" parameterType="String">
        delete from erp_enterprise_activities_label where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>