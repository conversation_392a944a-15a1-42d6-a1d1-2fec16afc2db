<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.VisitBacksMapper">

    <!-- 获取会计回访客户列表。 -->
    <select id="getClientListForAccountantBacks" resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        SELECT eo.id AS 'orderId',
        ebmi.num_erp_service_orders_id AS 'serviceOrderId',
        ebmi.num_id AS 'bizId',
        ec.id AS 'clientId',
        ee.vc_company_name AS 'enterpriseName',
        cdr.title AS 'clientCityName',
        eeu.vc_name AS 'legalPersonName',
        ec.contactName AS 'contactName',
        ec.contactPhone AS 'contactPhone',
        eo.vc_order_number AS 'orderNumber',
        eptd.vc_tax_name AS 'corporatePropertyName',
        NULL AS 'bizAllotAccountantTime',
        DATE_FORMAT(ebmi.dat_begin_bookkeep_time, '%Y-%m-%d') AS 'bizBookkeepMonthBegin',
        DATE_FORMAT(ebmi.dat_end_bookkeep_time, '%Y-%m-%d') AS 'bizBookkeepMonthEnd',
        account_user.nick_name AS 'bizAccountingUserName',
        ebns.num_fiscal_tax_bookkeep AS 'bizStatus',
        DATE_FORMAT(vbl.createdTime, '%Y-%m-%d %H:%i:%s') AS 'backLastTime',
        vb.status AS 'backStatus'
        FROM erp_biz_main_info ebmi
        LEFT JOIN erp_biz_node_status ebns ON ebns.num_erp_biz_main_info_id = ebmi.num_id
        LEFT JOIN erp_service_orders eso ON eso.id = ebmi.num_erp_service_orders_id
        LEFT JOIN erp_orders eo ON eo.id = ebmi.num_order_id
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_detail eed ON eed.num_erp_enterprise_id = ee.id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        LEFT JOIN sys_user account_user ON account_user.user_id = ebmi.num_accounting_user_id
        LEFT JOIN visit_backs vb ON vb.serviceOrderId = eso.id
        LEFT JOIN visit_backs_last vbl ON vbl.serviceOrderId = eso.id
        <where>
            ebns.num_fiscal_tax_bookkeep IS NOT NULL AND ec.num_type = 1
            <if test="query.backTimeBegin != null">
                AND DATE_FORMAT(vb.createdtime, '%Y%m%d') &gt;= DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null">
                AND DATE_FORMAT(vb.createdtime, '%Y%m%d') &lt;= DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">
                AND ee.vc_company_name LIKE CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.legalPersonName != null and query.legalPersonName != ''">
                AND eeu.vc_name LIKE CONCAT('%', #{query.legalPersonName}, '%')
            </if>
            <if test="query.corporatePropertyId != null">
                AND ee.num_corporate_property_id = #{query.corporatePropertyId}
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                AND ec.contactName LIKE CONCAT('%', #{query.contactName}, '%')
            </if>
            <if test="query.clientCityId != null">
                AND ec.num_city_id = #{query.clientCityId}
            </if>
            <if test="query.orderNumber != null and query.orderNumber != ''">
                AND eo.vc_order_number = #{query.orderNumber}
            </if>
            <if test="query.bizStatus != null">
                AND ebns.num_fiscal_tax_bookkeep = #{query.bizStatus}
            </if>
            <if test="query.bizAccountingUserId != null">
                AND ebmi.num_accounting_user_id = #{query.bizAccountingUserId}
            </if>
            <if test="query.backStatus != null">
                AND vb.status = #{query.backStatus}
            </if>
            <if test="query.clientCityName != null">
                AND cdr.id = #{query.clientCityName}
            </if>
        </where>
    </select>

    <!-- 获取增值回访客户列表。 -->
    <select id="getClientListForAppreciationBacks"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAppreciationBacksListVO">
        SELECT eo.id AS 'orderId',
        eso.id AS 'serviceOrderId',
        ebmi.num_id AS 'bizId',
        ec.id AS 'clientId',
        ee.vc_company_name AS 'enterpriseName',
        cdr.title AS 'clientCityName',
        eed.vc_register_address AS 'enterpriseRegisterAddress',
        eeu.vc_name AS 'legalPersonName',
        eo.vc_order_number AS 'orderNumber',
        eptd.vc_tax_name AS 'corporatePropertyName',
        ec.contactName AS 'contactName',
        ec.contactPhone AS 'contactPhone',
        NULL AS 'businessName',
        IFNULL(ebmi.num_agency_type, 0) AS 'agencyType',
        NULL AS 'bizTurnIncrementTime',
        NULL AS 'bizBusinessSupportUserName',
        increment_user.nick_name AS 'incrementUserName',
        NULL AS 'bizTurnAccountingTime',
        account_user.nick_name AS 'bizAccountingUserName',
        ebns.num_fiscal_tax_appreciation AS 'bizStatus',
        DATE_FORMAT(vbl.createdTime, '%Y-%m-%d %H:%i:%s') AS 'backLastTime',
        vb.status AS 'backStatus'
        FROM erp_biz_main_info ebmi
        LEFT JOIN erp_biz_node_status ebns ON ebns.num_erp_biz_main_info_id = ebmi.num_id
        LEFT JOIN erp_service_orders eso ON eso.id = ebmi.num_erp_service_orders_id
        LEFT JOIN erp_orders eo ON eo.id = ebmi.num_order_id
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_detail eed ON eed.num_erp_enterprise_id = ee.id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_id = eed.num_legal_person_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        LEFT JOIN sys_user account_user ON account_user.user_id = ebmi.num_accounting_user_id
        LEFT JOIN sys_user increment_user ON increment_user.user_id = ebmi.num_increment_user_id
        LEFT JOIN visit_backs vb ON vb.serviceOrderId = eso.id
        LEFT JOIN visit_backs_last vbl ON vbl.serviceOrderId = eso.id
        LEFT JOIN erp_contract contract ON contract.id = eo.num_contract_id
        <where>
            ebns.num_fiscal_tax_bookkeep IS NOT NULL AND ec.num_type = 1
            <if test="query.backTimeBegin != null">AND DATE_FORMAT(vb.createdtime, '%Y%m%d') &gt;=
                DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null">AND DATE_FORMAT(vb.createdtime, '%Y%m%d') &lt;=
                DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">AND ee.vc_company_name LIKE
                CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.legalPersonName != null">AND eeu.vc_name LIKE CONCAT('%', #{query.legalPersonName}, '%')
            </if>
            <if test="query.orderNumber != null">AND eo.vc_order_number = #{query.orderNumber}</if>
            <if test="query.contractNumber != null">AND contract.vc_contract_number = #{query.contractNumber}</if>
            <if test="query.agencyType != null and query.agencyType != ''">AND ebmi.num_agency_type =
                #{query.agencyType}
            </if>
            <if test="query.agencyType != null and query.agencyType == 0">AND ebmi.num_agency_type IS NULL</if>
            <if test="query.bizStatus != null">AND ebns.num_fiscal_tax_appreciation = #{query.bizStatus}</if>
            <if test="query.clientCityId != null">AND ec.num_city_id = #{query.clientCityId}</if>
            <if test="query.corporatePropertyId != null">AND ee.num_corporate_property_id =
                #{query.corporatePropertyId}
            </if>
            <if test="query.bizIncrementUserId != null">AND ebmi.num_increment_user_id = #{query.bizIncrementUserId}
            </if>
            <if test="query.backStatus != null">AND vb.status = #{query.backStatus}</if>
            <if test="query.contactName != null and query.contactName != ''">AND ec.contactName LIKE CONCAT('%',
                #{query.contactName}, '%')
            </if>
        </where>
    </select>

    <!-- 保存回访基础信息。 -->
    <insert id="saveBack" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO visit_backs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.orderId != null">`orderId`,</if>
            <if test="entity.serviceOrderId != null">`serviceOrderId`,</if>
            <if test="entity.bizId != null">`bizId`,</if>
            <if test="entity.clientId != null">`clientId`,</if>
            <if test="entity.backType != null">`backType`,</if>
            <if test="entity.status != null">`status`,</if>
            <if test="entity.number != null and entity.number != ''">`number`,</if>
            <if test="entity.remark != null and entity.remark != ''">`remark`,</if>
            <if test="entity.createdBy != null">`createdBy`,</if>
            <if test="entity.createdTime != null">`createdTime`,</if>
            <if test="entity.updateBy != null">`updateBy`,</if>
            <if test="entity.updateTime != null">`updateTime`,</if>
            <if test="entity.enterpriseId != null">`enterprise_id`,</if>
            <if test="entity.serviceType != null">`service_type`,</if>
        </trim>
        VALUE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.orderId != null">#{entity.orderId},</if>
            <if test="entity.serviceOrderId != null">#{entity.serviceOrderId},</if>
            <if test="entity.bizId != null">#{entity.bizId},</if>
            <if test="entity.clientId != null">#{entity.clientId},</if>
            <if test="entity.backType != null">#{entity.backType},</if>
            <if test="entity.status != null">#{entity.status},</if>
            <if test="entity.number != null and entity.number != ''">#{entity.number},</if>
            <if test="entity.remark != null and entity.remark != ''">#{entity.remark},</if>
            <if test="entity.createdBy != null">#{entity.createdBy},</if>
            <if test="entity.createdTime != null">#{entity.createdTime},</if>
            <if test="entity.updateBy != null">#{entity.updateBy},</if>
            <if test="entity.updateTime != null">#{entity.updateTime},</if>
            <if test="entity.enterpriseId != null">#{entity.enterpriseId},</if>
            <if test="entity.serviceType != null">#{entity.serviceType},</if>
        </trim>
    </insert>

    <!-- 保存最近一次回访信息。 -->
    <insert id="saveBackLast" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO visit_backs_last
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.orderId != null">`orderId`,</if>
            <if test="entity.serviceOrderId != null">`serviceOrderId`,</if>
            <if test="entity.bizId != null">`bizId`,</if>
            <if test="entity.clientId != null">`clientId`,</if>
            <if test="entity.backType != null">`backType`,</if>
            <if test="entity.backId != null">`backId`,</if>
            <if test="entity.status != null">`status`,</if>
            <if test="entity.createdBy != null">`createdBy`,</if>
            <if test="entity.createdTime != null">`createdTime`,</if>
            <if test="entity.updateBy != null">`updateBy`,</if>
            <if test="entity.updateTime != null">`updateTime`,</if>
        </trim>
        VALUE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.orderId != null">#{entity.orderId},</if>
            <if test="entity.serviceOrderId != null">#{entity.serviceOrderId},</if>
            <if test="entity.bizId != null">#{entity.bizId},</if>
            <if test="entity.clientId != null">#{entity.clientId},</if>
            <if test="entity.backType != null">#{entity.backType},</if>
            <if test="entity.backId != null">#{entity.backId},</if>
            <if test="entity.status != null">#{entity.status},</if>
            <if test="entity.createdBy != null">#{entity.createdBy},</if>
            <if test="entity.createdTime != null">#{entity.createdTime},</if>
            <if test="entity.updateBy != null">#{entity.updateBy},</if>
            <if test="entity.updateTime != null">#{entity.updateTime},</if>
        </trim>
    </insert>

    <!-- 更新最近一次回访信息。 -->
    <update id="updateBackLast">
        UPDATE visit_backs_last SET
            `orderId` = #{entity.orderId},
            `serviceOrderId` = #{entity.serviceOrderId},
            `bizId` = #{entity.bizId},
            `clientId` = #{entity.clientId},
            `backType` = #{entity.backType},
            `backId` = #{entity.backId},
            `status` = #{entity.status},
            `updateBy` = #{entity.updateBy},
            `updateTime` = #{entity.updateTime}
        WHERE id = #{entity.id}
    </update>

    <!-- 获取指定服务单、指定回访类型是否有最近一次记录。 -->
    <select id="getBackListByCondition" resultType="Integer">
        SELECT id FROM visit_backs_last WHERE bizid = #{entity.bizId} AND backtype = #{entity.backType}
    </select>

    <!-- 保存回访问题信息。 -->
    <insert id="saveBackQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO visit_backs_question
        (
        `backId`,
        `questionId`,
        `answer`,
        `createdBy`,
        `createdTime`,
        `updateBy`,
        `updateTime`,
        `remark`
        )
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.backId},
            #{entity.questionId},
            #{entity.answer},
            #{entity.createdBy},
            #{entity.createdTime},
            #{entity.updateBy},
            #{entity.updateTime},
            #{entity.remark}
            )
        </foreach>
        ;
    </insert>

    <!-- 保存回访录音信息。 -->
    <insert id="saveBackRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO visit_backs_record
            (
             `backId`,
             `recordUrl`,
             `recordTime`,
             `status`,
             `createdBy`,
             `createdTime`,
             `updateBy`,
             `updateTime`,
             `service_main_id`,
             `enterprise_id`,
             `order_id`,
             `back_type`
             )
        VALUES
            (
             #{entity.backId},
             #{entity.recordUrl},
             #{entity.recordTime},
             #{entity.status},
             #{entity.createdBy},
             #{entity.createdTime},
             #{entity.updateBy},
             #{entity.updateTime},
             #{entity.serviceMainId},
             #{entity.enterpriseId},
             #{entity.orderId},
             #{entity.backType}
             );
    </insert>

    <!-- 获取外勤回访列表。 -->
    <select id="getTaskBacks" resultType="com.nnb.erp.domain.vo.backs.QueryForTaskBacksListVO">
        SELECT DATE_FORMAT(vb.createdTime, '%Y-%m-%d %H:%i:%s') AS 'createdTime',
               GROUP_CONCAT(vbt.description, ':', vbt.score)    AS 'scores',
               vb.remark                                        AS 'remark',
               su.nick_name                                     AS 'createdByName'
        FROM visit_backs vb
        LEFT JOIN sys_user su ON su.user_id = vb.createdBy
        LEFT JOIN visit_backs_task vbt ON vbt.backId = vb.id
        WHERE vb.bizId = #{taskId}
        GROUP BY vb.id
    </select>

    <!-- 销售回访 -->
    <select id="getSalesFollowUp" resultType="com.nnb.erp.domain.vo.backs.QueryForAppreciationBacksListVO">
        select eo.id as orderId,
        eo.vc_order_number as orderNumber,
        ee.vc_company_name as enterpriseName,
        cdr.title as clientCityName,
        eeu.vc_name as legalPersonName,
        eptd.vc_tax_name as corporatePropertyName,
        ec.contactName as contactName,
        eo.dat_signing_date as datSigningDate,
        sd.dept_name as deptName,
        su.nick_name as signatory,
        eo.status as orderStatus,
        vb.createdTime as backLastTime,
        vb.status as backStatus
        from erp_orders eo
        LEFT JOIN (SELECT a.id,
        a.orderId,
        a.status,
        a.createdTime
        FROM visit_backs a,
        (
        SELECT b.orderId AS orderId,
        max(b.createdTime) AS createdTime
        FROM visit_backs b
        GROUP BY b.orderId
        ) d
        WHERE d.orderId = a.orderId
        AND a.createdTime = d.createdTime
        ORDER BY a.orderId
        ) vb ON eo.id = vb.orderId
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        LEFT JOIN com_dict_region cdr ON cdr.id = eo.area_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <include refid="getSalesFollowUpWhere"/>
        <if test="query.queryNum != null and query.querySize != null">
            limit #{query.queryNum}, #{query.querySize}
        </if>
    </select>

    <select id="getClientListForAppreciationBacksTotal" resultType="java.lang.Long">
        select count(*)
        from erp_orders eo
        <if test="
            (query.backTimeBegin != null and query.backTimeBegin != '')
            or (query.backTimeEnd != null and query.backTimeEnd != '')
            or query.backStatus != null
        ">
            LEFT JOIN (SELECT a.id,
            a.orderId,
            a.status,
            a.createdTime
            FROM visit_backs a,
            (
            SELECT b.orderId AS orderId,
            max(b.createdTime) AS createdTime
            FROM visit_backs b
            GROUP BY b.orderId
            ) d
            WHERE d.orderId = a.orderId
            AND a.createdTime = d.createdTime
            ORDER BY a.orderId
            ) vb ON eo.id = vb.orderId
        </if>
        <if test="
            (query.enterpriseName != null and query.enterpriseName != '')
            or (query.corporatePropertyName != null and query.corporatePropertyName != '')
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        </if>
        <if test="
            query.contactName != null and query.contactName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        </if>
        <if test="
            query.legalPersonName != null and query.legalPersonName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
            LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id
        </if>
        <if test="
            query.signatory != null and query.signatory != ''
        ">
            LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        </if>
        <if test="query.contract != null and query.contract != ''">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <include refid="getSalesFollowUpWhere"/>
    </select>

    <sql id="getSalesFollowUpWhere">
        <where>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">AND
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">AND
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
            </if>
            <if test="query.backTimeBegin != null and query.backTimeBegin != ''">AND DATE_FORMAT(vb.createdTime,
                '%Y%m%d') &gt;= DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null and query.backTimeEnd != ''">AND DATE_FORMAT(vb.createdTime, '%Y%m%d')
                &lt;= DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">AND ee.vc_company_name LIKE
                CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.orderNumber != null">AND eo.vc_order_number = #{query.orderNumber}</if>
            <if test="query.contactName != null and query.contactName != ''">AND ec.contactName LIKE CONCAT('%',
                #{query.contactName}, '%')
            </if>
            <if test="query.legalPersonName != null and query.legalPersonName != ''">AND eeu.vc_name LIKE CONCAT('%',
                #{query.legalPersonName}, '%')
            </if>
            <if test="query.corporatePropertyName != null and query.corporatePropertyName != ''">AND
                ee.num_corporate_property_id = #{query.corporatePropertyName}
            </if>
            <if test="query.clientCityId != null">AND eo.area_id = #{query.clientCityId}</if>
            <if test="query.orderStatus != null">AND eo.status = #{query.orderStatus}</if>
            <if test="query.signatory != null and query.signatory != ''">AND su.nick_name LIKE CONCAT('%',
                #{query.signatory}, '%')
            </if>
            <if test="query.backStatus != null">AND vb.status = #{query.backStatus}</if>
            <if test="query.contract != null">AND eo.contract_subject = #{query.contract}</if>
        </where>
    </sql>

    <select id="getSoundRecording" resultType="com.nnb.erp.domain.vo.backs.FollowUpRecording">
        select vbr.id,
               vbr.backId,
               vbr.recordUrl,
               vbr.recordTime,
               vbr.status,
               su.nick_name createdBy,
               vbr.createdTime,
               vbr.updateBy,
               vbr.updateTime
        from visit_backs_record vbr
                 left join sys_user su on su.user_id = vbr.createdBy
        where vbr.service_main_id in (
        <foreach collection="mainIds" item="id" separator=",">
            #{id}
        </foreach>
        )
        order by vbr.createdTime desc
    </select>

    <select id="getReturnVisitRecord" resultType="com.nnb.erp.domain.vo.backs.ReturnVisitRecord">
        select vbq.createdTime,
               GROUP_CONCAT(conf.vc_name SEPARATOR ',') as question,
               GROUP_CONCAT(DISTINCT if(vbq.answer is null , null, vbq.answer) Separator ',') as answer,
               su.nick_name createdBy,
               vb.number as number,
               vb.remark as remark
        from visit_backs vb
                 left join visit_backs_question vbq on vb.id = vbq.backId
                 left join bd_configurations conf on vbq.questionId = conf.id
                 left join sys_user su on su.user_id = vbq.createdBy
        where vb.bizId in (
        <foreach collection="mainIds" item="id" separator=",">
            #{id}
        </foreach>
        ) and vbq.id is not null
        group by vb.id
        order by vbq.createdTime desc
    </select>

    <!-- 会计回访 -->
    <select id="getAccountingReturnVisit" resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select ssm.order_id as orderId,
        ssm.id as bizId,
        ee.vc_company_name as enterpriseName, -- 企业名称
        cdr.title as clientCityName, -- 城市
        eeu.vc_name as legalPersonName, -- 企业法人
        ec.contactName as contactName, -- 企业联系人
        eo.vc_order_number as orderNumber, -- 订单号
        eptd.vc_tax_name as corporatePropertyName, -- 企业性质
        ee.account_time_old as bizAllotAccountantTime, -- 分配会计时间
        DATE_FORMAT(eed.dat_begin_bookkeep_time, '%Y-%m-%d') AS bizBookkeepMonthBegin, -- 记账开始月
        DATE_FORMAT(eed.dat_end_bookkeep_time, '%Y-%m-%d') AS bizBookkeepMonthEnd, -- 记账截至月
        su.nick_name as bizAccountingUserName, -- 记账会计
        scsps.name as bizStatus, -- 记账状态
        vb.createdTime as backLastTime, -- 最近一次回访时间
        vb.status as backStatus -- 是否有效回访
        from s_service_main ssm
        left join erp_orders eo on ssm.order_id = eo.id
        LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
        LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        LEFT JOIN (SELECT a.id,
        a.orderId,
        a.status,
        a.createdTime
        FROM visit_backs a,
        (
        SELECT b.orderId AS orderId,
        max(b.createdTime) AS createdTime
        FROM visit_backs b
        GROUP BY b.orderId
        ) d
        WHERE d.orderId = a.orderId
        AND a.createdTime = d.createdTime
        ORDER BY a.orderId
        ) vb ON eo.id = vb.orderId
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id and eeu.num_user_type = 1
        LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
        LEFT JOIN sys_user su ON su.user_id = ssm.account_user_id
        LEFT JOIN com_dict_region cdr ON cdr.id = eo.area_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <include refid="getAccountingReturnVisitWhere"/>
        limit #{query.queryNum}, #{query.querySize}
    </select>

    <select id="getAccountingReturnVisitTotal" resultType="java.lang.Long">
        select count(*)
        from s_service_main ssm

<!--        <if test="-->
<!--            (query.orderNumber != null and query.orderNumber != '')-->
<!--            or query.clientCityId != null-->
<!--            or query.contract != null-->
<!--        ">-->
<!--            left join erp_orders eo on ebmi.num_order_id = eo.id-->
<!--        </if>-->
        <if test="
            (query.backTimeBegin != null and query.backTimeBegin != '')
            or (query.backTimeEnd != null and query.backTimeEnd != '')
            or query.backStatus != null
            or (query.orderNumber != null and query.orderNumber != '')
            or query.clientCityId != null
            or query.contract != null
        ">
            left join erp_orders eo on ssm.order_id = eo.id
            LEFT JOIN (SELECT a.id,
            a.orderId,
            a.status,
            a.createdTime
            FROM visit_backs a,
            (
            SELECT b.orderId AS orderId,
            max(b.createdTime) AS createdTime
            FROM visit_backs b
            GROUP BY b.orderId
            ) d
            WHERE d.orderId = a.orderId
            AND a.createdTime = d.createdTime
            ORDER BY a.orderId
            ) vb ON eo.id = vb.orderId
        </if>
        <if test="
            (query.enterpriseName != null and query.enterpriseName != '')
            or (query.corporatePropertyName != null and query.corporatePropertyName != '')
            or query.bizAllotAccountantBeginTime != null
            or query.bizAllotAccountantEndTime != null
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        </if>
        <if test="
            query.contactName != null and query.contactName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        </if>
        <if test="
            query.legalPersonName != null and query.legalPersonName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
            LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id
        </if>
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <if test="
            query.bizStatus != null
        ">
            LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
            LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        </if>
        <include refid="getAccountingReturnVisitWhere"/>
    </select>

    <sql id="getAccountingReturnVisitWhere">
        <where>
            ssm.service_catalogue = 5 or ssm.service_catalogue = 12 or ssm.service_catalogue = 21
            <if test="query.bizAllotAccountantBeginTime != null and query.bizAllotAccountantBeginTime != ''">AND
                DATE_FORMAT(ee.account_time_old, '%Y%m%d') &gt;= DATE_FORMAT(#{query.bizAllotAccountantBeginTime},
                '%Y%m%d')
            </if>
            <if test="query.bizAllotAccountantEndTime != null and query.bizAllotAccountantEndTime != ''">AND
                DATE_FORMAT(ee.account_time_old, '%Y%m%d') &lt;= DATE_FORMAT(#{query.bizAllotAccountantEndTime},
                '%Y%m%d')
            </if>
            <if test="query.backTimeBegin != null and query.backTimeBegin != ''">AND DATE_FORMAT(vb.createdTime,
                '%Y%m%d') &gt;= DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null and query.backTimeEnd != ''">AND DATE_FORMAT(vb.createdTime, '%Y%m%d')
                &lt;= DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">AND ee.vc_company_name LIKE
                CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.orderNumber != null and query.orderNumber != ''">AND eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.contactName != null and query.contactName != ''">AND ec.contactName LIKE CONCAT('%',
                #{query.contactName}, '%')
            </if>
            <if test="query.legalPersonName != null and query.legalPersonName != ''">AND eeu.vc_name LIKE CONCAT('%',
                #{query.legalPersonName}, '%')
            </if>
            <if test="query.corporatePropertyName != null and query.corporatePropertyName != ''">AND
                ee.num_corporate_property_id = #{query.corporatePropertyName}
            </if>
            <if test="query.clientCityId != null">AND eo.area_id = #{query.clientCityId}</if>
            <if test="query.backStatus != null">AND vb.status = #{query.backStatus}</if>
            <if test="query.contract != null">AND eo.contract_subject = #{query.contract}</if>
            <if test="query.bizStatus != null and query.bizStatus != ''">AND ssepr.point_status = #{query.bizStatus}
            </if>
            <if test="query.bizAccountingUserName != null and query.bizAccountingUserName != ''">AND ssm.account_ser_d =
                #{query.bizAccountingUserName}
            </if>
        </where>
    </sql>

    <!-- 保存外勤回访信息。 -->
    <insert id="saveTaskBack" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO visit_backs_task
        (
        `backId`,
        `description`,
        `score`
        )
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backId}, #{entity.description}, #{entity.score})
        </foreach>
    </insert>

    <!-- 增值回访 -->
    <select id="getIncrementReturnVisit" resultType="com.nnb.erp.domain.vo.backs.QueryIncrementReturnVisitVo">
        SELECT ssm.order_id AS 'orderId',
        ssm.id AS 'bizId',
        ee.vc_company_name AS 'enterpriseName', -- 企业名称
        cdr.title AS 'clientCityName', -- 城市
        eed.vc_register_address AS 'enterpriseRegisterAddress', -- 注册区域
        eeu.vc_name AS 'legalPersonName', -- 企业法人
        eo.vc_order_number AS 'orderNumber', -- 订单号
        eptd.vc_tax_name AS 'corporatePropertyName', -- 企业性质
        ec.contactName AS 'contactName', -- 企业联系人
        NULL AS 'businessName', -- 所属行业
        su.nick_name AS 'bizBusinessSupportUserName', -- 业支负责人
        eed.dat_transfer_accounting_time AS 'bizTurnAccountingTime', -- 转会计日期
        suk.nick_name AS 'bizAccountingUserName', -- 会计
        scsps.name AS 'bizStatus', -- 增值状态
        vb.createdTime as backLastTime, -- 最近一次回访时间
        vb.status as backStatus -- 是否有效回访
        from s_service_main ssm
        left join erp_orders eo on ssm.order_id = eo.id
        LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
        LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        LEFT JOIN (SELECT a.id,
        a.orderId,
        a.status,
        a.createdTime
        FROM visit_backs a,
        (
        SELECT b.orderId AS orderId,
        max(b.createdTime) AS createdTime
        FROM visit_backs b
        GROUP BY b.orderId
        ) d
        WHERE d.orderId = a.orderId
        AND a.createdTime = d.createdTime
        ORDER BY a.orderId
        ) vb ON eo.id = vb.orderId
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id and eeu.num_user_type = 1
        LEFT JOIN erp_enterprise_detail eed ON eed.num_erp_enterprise_id = ee.id
        LEFT JOIN sys_user su ON su.user_id = ssm.server_user_id
        LEFT JOIN sys_user suk ON suk.user_id = ssm.server_user_id
        LEFT JOIN com_dict_region cdr ON cdr.id = eo.area_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <include refid="getIncrementReturnVisitWhere"/>
        limit #{query.queryNum}, #{query.querySize}
    </select>

    <select id="getIncrementReturnVisitTotal" resultType="java.lang.Long">
        select count(*)
        from s_service_main ssm
        <if test="
            (query.orderNumber != null and query.orderNumber != '')
            or query.clientCityId != null
            or query.contract != null
        ">
            left join erp_orders eo on ebmi.num_order_id = eo.id
        </if>
        <if test="
            (query.backTimeBegin != null and query.backTimeBegin != '')
            or (query.backTimeEnd != null and query.backTimeEnd != '')
            or query.backStatus != null
        ">
            LEFT JOIN (SELECT a.id,
            a.orderId,
            a.status,
            a.createdTime
            FROM visit_backs a,
            (
            SELECT b.orderId AS orderId,
            max(b.createdTime) AS createdTime
            FROM visit_backs b
            GROUP BY b.orderId
            ) d
            WHERE d.orderId = a.orderId
            AND a.createdTime = d.createdTime
            ORDER BY a.orderId
            ) vb ON eo.id = vb.orderId
        </if>
        <if test="
            (query.enterpriseName != null and query.enterpriseName != '')
            or (query.corporatePropertyName != null and query.corporatePropertyName != '')
            or query.bizTurnIncrementBeginTime != null
            or query.bizTurnIncrementEndTime != null
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
            LEFT JOIN erp_enterprise_detail eed ON eed.num_erp_enterprise_id = ee.id
        </if>
        <if test="
            query.contactName != null and query.contactName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        </if>
        <if test="
            query.legalPersonName != null and query.legalPersonName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
            LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id and eeu.num_user_type = 1
        </if>
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <if test="
            query.bizStatus != null
        ">
            LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
            LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        </if>
        <include refid="getIncrementReturnVisitWhere"/>
    </select>

    <sql id="getIncrementReturnVisitWhere">
        <where>
            ssm.service_catalogue = 6
            <!--            <if test="query.bizTurnIncrementBeginTime != null and query.bizTurnIncrementBeginTime != ''">AND DATE_FORMAT(eed.dat_transfer_accounting_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.bizTurnIncrementBeginTime}, '%Y%m%d')</if>-->
            <!--            <if test="query.bizTurnIncrementEndTime != null and query.bizTurnIncrementEndTime != ''">AND DATE_FORMAT(eed.dat_transfer_accounting_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.bizTurnIncrementEndTime}, '%Y%m%d')</if>-->
            <if test="query.backTimeBegin != null and query.backTimeBegin != ''">AND DATE_FORMAT(vb.createdTime,
                '%Y%m%d') &gt;= DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null and query.backTimeEnd != ''">AND DATE_FORMAT(vb.createdTime, '%Y%m%d')
                &lt;= DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">AND ee.vc_company_name LIKE
                CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.orderNumber != null and query.orderNumber != ''">AND eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.contractNumber != null and query.contractNumber != ''">AND eo.contact_num =
                #{query.contractNumber}
            </if>
            <if test="query.legalPersonName != null and query.legalPersonName != ''">AND eeu.vc_name LIKE CONCAT('%',
                #{query.legalPersonName}, '%')
            </if>
            <if test="query.corporatePropertyName != null and query.corporatePropertyName != ''">AND
                ee.num_corporate_property_id = #{query.corporatePropertyName}
            </if>
            <if test="query.clientCityId != null">AND eo.area_id = #{query.clientCityId}</if>
            <if test="query.incrementUserName != null">AND ssm.increment_user_id = #{query.incrementUserName}</if>
            <if test="query.backStatus != null">AND vb.status = #{query.backStatus}</if>
            <if test="query.contract != null">AND eo.contract_subject = #{query.contract}</if>
            <if test="query.bizStatus != null and query.bizStatus != ''">AND ssepr.point_status = #{query.bizStatus}
            </if>
            <!-- 是否代办,增值人员,区域,转增值时间,所属行业-->
        </where>
    </sql>

    <!-- 业支 -->
    <select id="getIndustrialReturnVisit" resultType="com.nnb.erp.domain.vo.backs.QueryIndustrialReturnVisitVo">
        select ssm.order_id as orderId,
        ssm.id as bizId,
        eo.vc_order_number as orderNumber, -- 订单号
        ee.vc_company_name as enterpriseName, -- 企业名称
        cdr.title as clientCityName, -- 城市
        eeu.vc_name as legalPersonName, -- 企业法人
        eptd.vc_tax_name as corporatePropertyName, -- 企业性质
        ec.contactName as contactName, -- 企业联系人
        ssm.created_time as ofArrivalTime, -- 到达业支时间
        ssm.created_time AS serviceStartTime, -- 服务开始时间
        su.nick_name as industrialReturnName, -- 业支对接人
        scsps.name as bizStatus, -- 记账状态
        vb.createdTime as backLastTime, -- 最近一次回访时间
        vb.status as backStatus -- 是否有效回访
        from s_service_main ssm
        left join erp_orders eo on ssm.order_id = eo.id
        LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
        LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        LEFT JOIN (SELECT a.id,
        a.orderId,
        a.status,
        a.createdTime
        FROM visit_backs a,
        (
        SELECT b.orderId AS orderId,
        max(b.createdTime) AS createdTime
        FROM visit_backs b
        GROUP BY b.orderId
        ) d
        WHERE d.orderId = a.orderId
        AND a.createdTime = d.createdTime
        ORDER BY a.orderId
        ) vb ON eo.id = vb.orderId
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id and eeu.num_user_type = 1
        LEFT JOIN erp_enterprise_detail eed on eed.num_erp_enterprise_id = ee.id
        LEFT JOIN sys_user su ON su.user_id = ssm.server_user_id
        LEFT JOIN com_dict_region cdr ON cdr.id = eo.area_id
        LEFT JOIN erp_product_tax_dict eptd ON ee.num_corporate_property_id = eptd.num_tax_id
        LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <include refid="getIndustrialReturnVisitWhere"/>
        limit #{query.queryNum}, #{query.querySize}
    </select>

    <select id="getIndustrialReturnVisitTotal" resultType="java.lang.Long">
        select count(*)
        from s_service_main ssm

        <if test="
            (query.orderNumber != null and query.orderNumber != '')
            or query.clientCityId != null
            or query.contract != null
        ">
            left join erp_orders eo on ebmi.num_order_id = eo.id
        </if>
        <if test="
            (query.backTimeBegin != null and query.backTimeBegin != '')
            or (query.backTimeEnd != null and query.backTimeEnd != '')
            or query.backStatus != null
        ">
            LEFT JOIN (SELECT a.id,
            a.orderId,
            a.status,
            a.createdTime
            FROM visit_backs a,
            (
            SELECT b.orderId AS orderId,
            max(b.createdTime) AS createdTime
            FROM visit_backs b
            GROUP BY b.orderId
            ) d
            WHERE d.orderId = a.orderId
            AND a.createdTime = d.createdTime
            ORDER BY a.orderId
            ) vb ON eo.id = vb.orderId
        </if>
        <if test="
            (query.enterpriseName != null and query.enterpriseName != '')
            or (query.corporatePropertyName != null and query.corporatePropertyName != '')
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        </if>
        <if test="
            query.contactName != null and query.contactName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        </if>
        <if test="
            query.legalPersonName != null and query.legalPersonName != ''
        ">
            LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
            LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
            LEFT JOIN erp_enterprise_user eeu ON eeu.num_erp_enterprise_id = ee.id
        </if>
        <if test="query.contract != null">
            LEFT JOIN erp_contract_main ecm on ecm.id = eo.contract_subject
        </if>
        <if test="
            query.bizStatus != null
        ">
            LEFT JOIN s_service_end_point_record ssepr ON ssepr.order_id = ssm.id
            LEFT JOIN s_config_service_point_status scsps on scsps.id = ssepr.point_status
        </if>
        <if test="
            query.industrialReturnName != null and query.industrialReturnName != ''
        ">
            LEFT JOIN sys_user su ON su.user_id = ssm.server_user_id
        </if>
        <include refid="getIndustrialReturnVisitWhere"/>
    </select>

    <sql id="getIndustrialReturnVisitWhere">
        <where>
            ssm.service_catalogue = 1 or ssm.service_catalogue = 2 or ssm.service_catalogue = 3 or ssm.service_catalogue
            = 4
            <if test="query.ofArrivalStartTime != null and query.ofArrivalStartTime != ''">AND
                DATE_FORMAT(ssm.created_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.ofArrivalStartTime}, '%Y%m%d')
            </if>
            <if test="query.ofArrivalEndTime != null and query.ofArrivalEndTime != ''">AND DATE_FORMAT(ssm.created_time,
                '%Y%m%d') &lt;= DATE_FORMAT(#{query.ofArrivalEndTime}, '%Y%m%d')
            </if>
            <if test="query.backTimeBegin != null and query.backTimeBegin != ''">AND DATE_FORMAT(vb.createdTime,
                '%Y%m%d') &gt;= DATE_FORMAT(#{query.backTimeBegin}, '%Y%m%d')
            </if>
            <if test="query.backTimeEnd != null and query.backTimeEnd != ''">AND DATE_FORMAT(vb.createdTime, '%Y%m%d')
                &lt;= DATE_FORMAT(#{query.backTimeEnd}, '%Y%m%d')
            </if>
            <if test="query.enterpriseName != null and query.enterpriseName != ''">AND ee.vc_company_name LIKE
                CONCAT('%', #{query.enterpriseName}, '%')
            </if>
            <if test="query.orderNumber != null and query.orderNumber != ''">AND eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.contactName != null and query.contactName != ''">AND ec.contactName LIKE CONCAT('%',
                #{query.contactName}, '%')
            </if>
            <if test="query.legalPersonName != null and query.legalPersonName != ''">AND eeu.vc_name LIKE CONCAT('%',
                #{query.legalPersonName}, '%')
            </if>
            <if test="query.corporatePropertyName != null and query.corporatePropertyName != ''">AND
                ee.num_corporate_property_id = #{query.corporatePropertyName}
            </if>
            <if test="query.clientCityId != null">AND eo.area_id = #{query.clientCityId}</if>
            <if test="query.contract != null">AND eo.contract_subject = #{query.contract}</if>
            <if test="query.bizStatus != null and query.bizStatus != ''">AND ssepr.point_status = #{query.bizStatus}
            </if>
            <if test="query.industrialReturnName != null and query.industrialReturnName != ''">AND su.nick_name LIKE
                CONCAT('%', #{query.industrialReturnName}, '%')
            </if>
        </where>
    </sql>

    <select id="selectVisitBacksEntity" resultType="com.nnb.erp.domain.VisitBacksEntity">
            SELECT a.id,
                   a.orderId,
                   a.status,
                   a.createdTime
            FROM visit_backs a,
                 (
                     SELECT b.orderId          AS orderId,
                            max(b.createdTime) AS createdTime
                     FROM visit_backs b
                     GROUP BY b.orderId
                 ) d
            WHERE d.orderId = a.orderId
              AND a.createdTime = d.createdTime
              <if test="backTimeBegin != null and backTimeBegin != '' and backTimeEnd != null and backTimeEnd != ''">
                AND a.createdTime &gt;= #{backTimeBegin} and a.createdTime &lt;= #{backTimeEnd}
              </if>
              <if test="backStatus == 1">
                AND a.status = #{backStatus}
              </if>
              <if test="backStatus == 0">
                AND a.status = #{backStatus}
              </if>
            ORDER BY a.orderId
    </select>

    <select id="selectVisitBacks" resultType="com.nnb.erp.domain.VisitBacksEntity">
        SELECT
            a.id,
            a.bizId,
            a.orderId,
            a.status,
            a.createdTime,
            a.enterprise_id as enterpriseId
        FROM visit_backs a
        <where>
            a.id in (
            select max(id) as id
            from visit_backs
            <if test="type == 1">
                group by orderId
            </if>
            <if test="type == 5">
                group by enterprise_id
            </if>
            )
            <if test="backStatus != null">
                and a.status = #{backStatus}
            </if>
            <if test="backTimeBegin != null and backTimeBegin != '' and backTimeEnd != null and backTimeEnd != ''">
                AND a.createdTime &gt;= #{backTimeBegin} and a.createdTime &lt;= #{backTimeEnd}
            </if>
            AND a.backType = #{type}
        </where>
    </select>

    <select id="selectVisitBackByStatus" resultType="java.lang.Integer">
            SELECT a.id,
                   a.orderId,
                   a.status,
                   a.createdTime
            FROM visit_backs a,
                 (
                     SELECT b.orderId          AS orderId,
                            max(b.createdTime) AS createdTime
                     FROM visit_backs b
                     GROUP BY b.orderId
                 ) d
            WHERE d.orderId = a.orderId
              AND a.createdTime = d.createdTime
              AND a.status = #{backStatus}
            ORDER BY a.orderId
    </select>

    <sql id="accountColumn">
        select ssm.order_id          as orderId,
               ssm.num_enterprise_id as enterpriseId,
               ssm.id                as bizId,
               ssm.order_num         as orderNumber,
               ssm.account_user_id   as accountUserId,
               scsps.name            as bizStatus,
        DATE_FORMAT(ssm.ac_start, '%Y-%m-%d')       as bizBookkeepMonthBegin,
        DATE_FORMAT(ssm.ac_end, '%Y-%m-%d')         as bizBookkeepMonthEnd
        from s_service_main ssm
            LEFT JOIN s_config_service_point_status scsps on ssm.service_point_status = scsps.id
    </sql>

    <sql id="accountWhere">
        <if test="query.monthBeginStart != null and query.monthBeginStart != '' and query.monthBeginEnd != null and query.monthBeginEnd != ''">
            AND ssm.ac_start &gt;= #{query.monthBeginStart} and ssm.ac_start &lt;= #{query.monthBeginEnd}
        </if>
        <if test="query.orderNumber != null and query.orderNumber != ''">
            and ssm.order_num = #{query.orderNumber}
        </if>
        <if test="query.bizAccountingUser != null and query.bizAccountingUser != ''">
            and ssm.account_user_id = #{query.bizAccountingUser}
        </if>
        <if test="query.bizStatus != null and query.bizStatus != ''">
            and ssm.service_point_status = #{query.bizStatus}
        </if>
        <if test="query.clientCityId != null and query.clientCityId != ''">
            and ssm.city_id = #{query.clientCityId}
        </if>
        <if test="query.orderIdList.size > 0">
            and ssm.order_id in (
            <foreach collection="query.orderIdList" item="orderId" separator=",">
                #{orderId}
            </foreach>
            )
        </if>
        <if test="query.enterpriseIdList.size > 0">
            and ssm.num_enterprise_id in (
            <foreach collection="query.enterpriseIdList" item="enterpriseId" separator=",">
                #{enterpriseId}
            </foreach>
            )
        </if>
        <if test="query.sqlType == 1">
            and 1 = 0
        </if>
    </sql>

    <select id="getAccountingReturnVisitSplit"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        <include refid="accountColumn"/>
        <where>
            ssm.id IN
            (
                SELECT max(smids.id)
                FROM s_service_main smids
                where
                smids.service_catalogue = 22
                and smids.service_type = 10
                and smids.service_status != 8
                and smids.service_point = 64
                GROUP BY smids.num_enterprise_id
            )
            <include refid="accountWhere"/>
        </where>
        GROUP BY ssm.num_enterprise_id
        order by ssm.created_time desc
    </select>

    <select id="getAccountingReturnVisitSplitExport"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        <include refid="accountColumn"/>
        <where>
            ssm.service_catalogue = 22
            and ssm.service_type = 10
            and ssm.service_status != 8
            and ssm.service_point = 64
            <include refid="accountWhere"/>
        </where>
    </select>

    <!--     excel导入数据        -->
    <select id="selectAllservice_new" resultType="java.util.Map">
        select * from product_new_types
    </select>

    <select id="selectAllclassification" resultType="java.util.Map">
        select * from erp_product_classification where num_is_use = 1
    </select>

    <select id="selectAlltype" resultType="java.util.Map">
        select * from erp_product_type where num_is_use = 1
    </select>

    <select id="selectAllname" resultType="java.util.Map">
        select * from erp_product_name where num_is_use = 1
    </select>

    <select id="selectAllservice" resultType="java.util.Map">
        select * from erp_product_service
    </select>
    <select id="selectTaxNameList" resultType="java.util.Map">
        select num_tax_id,vc_tax_name from erp_product_tax_dict where vc_tax_name in (
        <foreach collection="taxNameList" item="tax" separator=",">
            #{tax}
        </foreach>
        )
    </select>
    <select id="selectAllunit" resultType="java.util.Map">
        select * from erp_product_unit_dict
    </select>
    <select id="selectUnit" resultType="java.util.Map">
        select * from erp_product_unit_dict
    </select>
    <select id="selectVisitTimeAndStatusByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        SELECT a.id          as backId,
               a.orderId     as orderId,
               a.createdTime as backLastTime, -- 最近一次回访时间
               a.status      as backStatus,   -- 是否有效回访
               a.number      as number,
               a.remark      as remark
        FROM visit_backs a,
             (SELECT b.orderId AS orderId, max(b.createdTime) AS createdTime
              FROM visit_backs b
              GROUP BY b.orderId) d
        WHERE d.orderId = a.orderId
          AND a.createdTime = d.createdTime
          AND a.orderId in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
            )
          AND a.backType = 1
        ORDER BY a.orderId
    </select>

    <select id="selectVisitTimeAndStatusByBizIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        SELECT a.id            as backId,
               a.bizId         as bizId,
               a.orderId       as orderId,
               a.createdTime   as backLastTime, -- 最近一次回访时间
               a.status        as backStatus,   -- 是否有效回访
               a.enterprise_id as enterpriseId
        FROM visit_backs a
        WHERE a.id in
              (
                  select max(id) as id
                  from visit_backs
                  where enterprise_id in
                  (
                    <foreach collection="enterpriseIds" item="id" separator=",">
                        #{id}
                    </foreach>
                  )
                  group by enterprise_id
              )
              and a.backType = 5
    </select>

    <select id="getReturnVisitRecordByOrderIds" resultType="com.nnb.erp.domain.vo.backs.ReturnVisitRecord">
        select vbq.createdTime,
               GROUP_CONCAT(conf.vc_name SEPARATOR ',') as question,
               GROUP_CONCAT(DISTINCT if(vbq.answer is null , null, vbq.answer) Separator ',') as answer,
               su.nick_name createdBy,
               vb.number as number,
               vb.remark as remark,
               vb.orderId as orderId
        from visit_backs vb
                 left join visit_backs_question vbq on vb.id = vbq.backId
                 left join bd_configurations conf on vbq.questionId = conf.id
                 left join sys_user su on su.user_id = vbq.createdBy
        where vb.orderId in (
        <foreach collection="orderIds" item="id" separator=",">
            #{id}
        </foreach>
        ) and vbq.id is not null
        group by vb.id
        order by vbq.createdTime desc
    </select>
    <select id="getReturnVisitRecordList" resultType="com.nnb.erp.domain.vo.backs.ReturnVisitRecord">
        select
        vb.createdTime,
        GROUP_CONCAT(conf.vc_name SEPARATOR '，') as question,
        GROUP_CONCAT(if(vbq.answer is null , null, vbq.answer) Separator '，') as answer,
        su.nick_name createdBy,
        vb.number as number,
        vb.remark as remark,
        vb.bizId as bizId,
        vb.status as status,
        vb.enterprise_id as enterpriseId
        from visit_backs vb
        left join visit_backs_question vbq on vb.id = vbq.backId
        left join bd_configurations conf on vbq.questionId = conf.id
        left join sys_user su on su.user_id = vbq.createdBy
        where vb.enterprise_id in (
            <foreach collection="enterpriseIds" item="id" separator=",">
                #{id}
            </foreach>
        )
        and vbq.id is not null
        and vb.createdTime &gt;= #{backTimeBegin}
        and vb.createdTime &lt;= #{backTimeEnd}
        group by vb.id
        order by vb.createdTime desc
    </select>
    <select id="selectSServiceMainIds" resultType="java.lang.Long">
        select id
        from s_service_main
        where num_enterprise_id = (
            select num_enterprise_id
            from s_service_main
            where id = #{mainId}
        )
    </select>

    <select id="getIncrementReturnVisitSplit"
            resultType="com.nnb.erp.domain.vo.backs.QueryIncrementReturnVisitVo">
        select
        ssm.id                    as bizId,
        ssm.order_id              as orderId,
        ssm.num_enterprise_id     as enterpriseId,
        ssm.order_num             as orderNumber,
        ssm.account_user_id       as accountUserId,
        ssm.server_before_user_id as serverBeforeUserId,
        ssm.server_after_user_id  as serverAfterUserId,
        scsps.name                as bizStatus
        from s_service_main ssm
        LEFT JOIN s_config_service_point_status scsps on ssm.service_point_status = scsps.id
        <where>
            ssm.id IN (SELECT max(smids.id) FROM s_service_main smids
            where
            smids.service_catalogue = 6
            and smids.service_type = 9
            and smids.service_point = 56
            and smids.service_point_status = 189
            GROUP BY smids.num_enterprise_id)
            <if test="query.orderNumber != null and query.orderNumber != ''">
                and ssm.order_num = #{query.orderNumber}
            </if>
            <if test="query.incrementUserName != null">
                and ssm.increment_user_id = #{query.incrementUserId}
            </if>
            <if test="query.bizStatus != null and query.bizStatus != ''">
                and ssm.service_point_status = #{query.bizStatus}
            </if>
            <if test="query.clientCityId != null and query.clientCityId != ''">
                and ssm.city_id = #{query.clientCityId}
            </if>
            <if test="query.areaId != null and query.areaId != ''">
                and ssm.region_id = #{query.areaId}
            </if>
            <if test="query.bizTurnIncrementBeginTime != null and query.bizTurnIncrementBeginTime != ''">
                and ssm.to_increment_time &gt;= #{query.bizTurnIncrementBeginTime}
            </if>
            <if test="query.bizTurnIncrementEndTime != null and query.bizTurnIncrementEndTime != ''">
                and ssm.to_increment_time &lt;= #{query.bizTurnIncrementEndTime}
            </if>
            <if test="query.orderIdList.size > 0">
                and ssm.order_id in (
                <foreach collection="query.orderIdList" item="orderId" separator=",">
                    #{orderId}
                </foreach>
                )
            </if>
            <if test="query.idList.size > 0">
                and ssm.id in (
                <foreach collection="query.idList" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="query.enterpriseIdList.size > 0">
                and ssm.num_enterprise_id in (
                <foreach collection="query.enterpriseIdList" item="enterpriseId" separator=",">
                    #{enterpriseId}
                </foreach>
                )
            </if>
            <if test="query.sqlType == 1">
                and 1 = 0
            </if>
        </where>
        GROUP BY ssm.num_enterprise_id
        order by ssm.created_time desc
    </select>
    <select id="selectUserMsg" resultType="com.nnb.erp.domain.vo.backs.UserMsg">
        select user_id   as userId,
               nick_name as userName
        from sys_user
        where user_id in (
        <foreach collection="userIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
    <select id="getReturnVisitRecordByEnterpriseId" resultType="com.nnb.erp.domain.vo.backs.ReturnVisitRecord">
        select
        vb.Id as backId,
        vb.createdTime,
        GROUP_CONCAT(conf.vc_name SEPARATOR ',') as question,
        GROUP_CONCAT(DISTINCT if(vbq.answer is null , null, vbq.answer) Separator ',') as answer,
        su.nick_name createdBy,
        vb.number as number,
        vb.remark as remark
        from visit_backs vb
        left join visit_backs_question vbq on vb.id = vbq.backId
        left join bd_configurations conf on vbq.questionId = conf.id
        left join sys_user su on su.user_id = vbq.createdBy
        where
         vb.enterprise_id = #{enterpriseId}
         and vb.backType = 5
         and vbq.id is not null
        group by vb.id
        order by vb.createdTime desc
    </select>
    <select id="getReturnVisitRecordByOrderId" resultType="com.nnb.erp.domain.vo.backs.ReturnVisitRecord">
        select
        vb.orderId,
        vb.Id as backId,
        vb.createdTime,
        GROUP_CONCAT(conf.vc_name SEPARATOR ',') as question,
        GROUP_CONCAT(if(vbq.answer is null , null, vbq.answer) Separator ',') as answer,
        GROUP_CONCAT(vbq.remark SEPARATOR ' ') as questionRemark,
        su.nick_name createdBy,
        vb.number as number,
        vb.remark as remark
        from visit_backs vb
        left join visit_backs_question vbq on vb.id = vbq.backId
        left join bd_configurations conf on vbq.questionId = conf.id
        left join sys_user su on su.user_id = vbq.createdBy
        where
         vb.orderId = #{orderId}
         and vb.backType = 1
         and vbq.id is not null
        group by vb.id
        order by vb.createdTime desc
    </select>
    <select id="getSoundRecordingByEnterpriseId" resultType="com.nnb.erp.domain.vo.backs.FollowUpRecording">
        select vbr.id,
        vbr.backId,
        vbr.recordUrl,
        vbr.recordTime,
        vbr.status,
        su.nick_name createdBy,
        vbr.createdTime,
        vbr.updateBy,
        vbr.updateTime
        from visit_backs_record vbr
        left join sys_user su on su.user_id = vbr.createdBy
        where vbr.enterprise_id = #{enterpriseId} and (back_type = 5 or back_type is null)
        order by vbr.createdTime desc
    </select>
    <select id="getSoundRecordingByOrderId" resultType="com.nnb.erp.domain.vo.backs.FollowUpRecording">
        select vbr.id,
        vbr.backId,
        vbr.recordUrl,
        vbr.recordTime,
        vbr.status,
        su.nick_name createdBy,
        vbr.createdTime,
        vbr.updateBy,
        vbr.updateTime
        from visit_backs_record vbr
        left join sys_user su on su.user_id = vbr.createdBy
        where vbr.order_id = #{orderId} and back_type = 1
        order by vbr.createdTime desc
    </select>
    <select id="getSalesFollowUpList" resultType="com.nnb.erp.domain.vo.backs.QueryForAppreciationBacksListVO">
        select eo.id                            as orderId,
               eo.vc_order_number               as orderNumber,
               eo.dat_signing_date              as datSigningDate,
               eo.dat_signing_datecreated_time  as auditTime,
               su.nick_name                     as signatory,
               sd.dept_name                     as deptName,
               eo.num_total_price               as totalPrice,
               eo.num_pay_price                 as payPrice,
               eo.num_client_id                 as clientId,
               sd.ancestors                     as ancestors,
               (case
                   when eo.num_valid_status = 2 then '作废'
                   when (eo.num_valid_status = 3 or eo.num_valid_status = 4) then '退款'
                   when (eo.num_status = 5 or eo.num_status = 6) then '退款中'
                   else '审核通过' end)          as orderStatus
        from erp_orders eo
               left join sys_user su on eo.num_user_id = su.user_id
               left join sys_dept sd on su.dept_id = sd.dept_id
        <where>
            eo.num_create_order_examine_status = 5
            <if test="query.realDecimalStart != null">
                and eo.num_pay_price &gt;= #{query.realDecimalStart}
            </if>
            <if test="query.realDecimalEnd != null">
                and eo.num_pay_price &lt;= #{query.realDecimalEnd}
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND eo.dat_signing_date &gt;= #{query.datSigningDateBegin}
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND eo.dat_signing_date &lt;= #{query.datSigningDateEnd}
            </if>
            <if test="query.auditDateBegin != null and query.auditDateBegin != ''">
                and eo.dat_signing_datecreated_time &gt;= #{query.auditDateBegin}
            </if>
            <if test="query.auditDateEnd != null and query.auditDateEnd != ''">
                and eo.dat_signing_datecreated_time &lt;= #{query.auditDateEnd}
            </if>
            <if test="query.orderNumber != null and query.orderNumber != ''">
                and eo.vc_order_number = #{query.orderNumber}
            </if>
            <if test="query.orderStatus == 1">
                and eo.num_create_order_examine_status = 5
            </if>
            <if test="query.orderStatus == 2">
                and eo.num_valid_status = 2
            </if>
            <if test="query.orderStatus == 3">
                and eo.num_valid_status = 3
            </if>
            <if test="query.orderStatus == 4">
                and (eo.num_status = 5 or eo.num_status = 6)
            </if>
            <if test="query.signId != null and query.signId != ''">
                and eo.num_user_id = #{query.signId}
            </if>
            <if test="query.orderIdList.size > 0">
                and eo.id in (
                <foreach collection="query.orderIdList" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="query.clientIdList.size > 0">
                and eo.num_client_id in (
                <foreach collection="query.clientIdList" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size() > 0">
                and sd.dept_id in (
                <foreach collection="query.deptIdList" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="query.backStatus == 3">
                and eo.id not in (select orderId from visit_backs where backType = 1)
            </if>
            <if test="query.sqlType == 1">
                and 1 = 0
            </if>
        </where>
        order by eo.dat_signing_datecreated_time desc
    </select>
    <select id="selectOrderIdsByAuditOperateDate" resultType="java.lang.Integer">
        select num_order_id
        from erp_order_operating_record
        where id in (
            select min(id) as id
            from erp_order_operating_record
            where num_operation_type = 6
            group by num_order_id
        )
        and dat_created_time &gt;= #{auditOperateDateBegin}
        and dat_created_time &lt;= #{auditOperateDateEnd}
    </select>
    <select id="selectAuditOperateTimeByOrderIds"
            resultType="com.nnb.erp.domain.vo.backs.QueryForAppreciationBacksListVO">
        select num_order_id as orderId,
                dat_created_time as auditOperateTime
        from erp_order_operating_record
        where id in (
            select min(id) as id
            from erp_order_operating_record
            where num_operation_type = 6
            group by num_order_id
        )
        and num_order_id in (
        <foreach collection="orderIds" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
    <select id="selectFirstDept" resultType="com.nnb.system.api.domain.SysDept">
        select dept_id as deptId, dept_name as deptName
        from sys_dept where parent_id = 0 and status = 0
    </select>
    <select id="selectAmountByOrderIdList" resultType="com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO">
        select id as orderId, num_pay_price as payPrice
        from erp_orders
        where id in (
            <foreach collection="orderIds" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>

    <insert id="insertService_new" parameterType="com.nnb.erp.controller.ExcelImport"  useGeneratedKeys="true" keyProperty="id">
        insert into product_new_types (name)
        values (#{excelImport.name})
    </insert>
    <insert id="insertClassificationId" parameterType="com.nnb.erp.controller.ExcelImport"  useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_classification (vc_classification_name, is_old_data)
        values (#{excelImport.name},3)
    </insert>
    <insert id="insertTypeId" parameterType="com.nnb.erp.controller.ExcelImport"  useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_type (vc_type_name, num_classification_id,  is_old_data)
        values (#{excelImport.name},#{excelImport.otherId},3)
    </insert>
    <insert id="insertNameId" parameterType="com.nnb.erp.controller.ExcelImport"  useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_name (vc_product_name, num_type_id, is_old_data)
        values (#{excelImport.name},#{excelImport.otherId},3)
    </insert>
    <insert id="insertServiceId" parameterType="com.nnb.erp.controller.ExcelImport"  useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_service (vc_service_name, is_old_data)
        values (#{excelImport.name},3)
    </insert>

    <insert id="insertTax" parameterType="com.nnb.erp.controller.ExcelImport" useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_tax_relation (num_product_id, num_tax_id, is_old_data)
        values (#{excelImport.productId},#{excelImport.otherId},3)
    </insert>
    <insert id="insertCity" parameterType="com.nnb.erp.controller.ExcelImport" useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_area_relation (num_product_id, num_area_id, is_old_data)
        values (#{excelImport.productId},#{excelImport.otherId},3)
    </insert>
    <insert id="insertTixi" parameterType="com.nnb.erp.controller.ExcelImport" useGeneratedKeys="true" keyProperty="id">
        insert into erp_product_configuration
        (product_id, configuration_status, product_price, create_user, update_user, create_time, update_time, dept_id)
        values (#{excelImport.productId},1,#{excelImport.price},1,1,NOW(),NOW(),#{excelImport.deptId})
    </insert>

    <update id="updateTypeId">
        update erp_product_type set num_classification_id = #{excelImport.otherId} where num_type_id = #{excelImport.id}
    </update>
    <update id="updateNameId">
        update erp_product_name set num_type_id = #{excelImport.otherId} where num_name_id = #{excelImport.id}
    </update>

</mapper>
