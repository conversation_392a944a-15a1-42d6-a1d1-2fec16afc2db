<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpOrdersMapper">

    <resultMap type="com.nnb.erp.domain.ErpOrders" id="ErpOrdersResult">
        <result property="id" column="id"/>
        <result property="vcOrderNumber" column="vc_order_number"/>
        <result property="numSource" column="num_source"/>
        <result property="numContractId" column="num_contract_id"/>
        <result property="numClueId" column="num_clue_id"/>
        <result property="numClientId" column="num_client_id"/>
        <result property="numPayee" column="num_payee"/>
        <result property="numUserId" column="num_user_id"/>
        <result property="numTotalPrice" column="num_total_price"/>
        <result property="numPayPrice" column="num_pay_price"/>
        <result property="numLastPrice" column="num_last_price"/>
        <result property="numRefundPrice" column="num_refund_price"/>
        <result property="numDiscountAmount" column="num_discount_amount"/>
        <result property="datSigningDate" column="dat_signing_date"/>
        <result property="numPaymentStatus" column="num_payment_status"/>
        <result property="numStatus" column="num_status"/>
        <result property="numValidStatus" column="num_valid_status"/>
        <result property="numPoints" column="num_points"/>
        <result property="numGrowthValue" column="num_growth_value"/>
        <result property="numBusinessApprovalId" column="num_business_approval_id"/>
        <result property="datSigningDatecollectMoneyTime" column="dat_signing_datecollect_money_time"/>
        <result property="datFinanceCollectionTime" column="dat_finance_collection_time"/>
        <result property="numCreatedBy" column="num_created_by"/>
        <result property="datSigningDatecreatedTime" column="dat_signing_datecreated_time"/>
        <result property="numUpdatedBy" column="num_updated_by"/>
        <result property="datSigningDateupdatedTime" column="dat_signing_dateupdated_time"/>
        <result property="numCustomerType" column="num_customer_type"/>
        <result property="numPaymentTerm" column="num_payment_term"/>
        <result property="vcPhone" column="vc_phone"/>
        <result property="vcRemark" column="vc_remark"/>
        <result property="isElectronicContract" column="is_electronic_contract"/>
        <result property="numCreateOrderExamineStatus" column="num_create_order_examine_status"/>
        <result property="numModifyOrderExamineStatus" column="num_modify_order_examine_status"/>
        <result property="acStart" column="ac_start"/>
        <result property="acEnd" column="ac_end"/>
        <result property="commitOrderType" column="commit_order_type"/>
        <result property="licenseNumber" column="license_number"/>
        <result property="isReturn" column="is_return"/>
        <result property="numClientIdNew" column="num_client_id_new"/>
        <result property="numContractIdNew" column="num_contract_id_new"/>
        <result property="isChangeBlankContract" column="is_change_blank_contract"/>
        <result property="isPaperChangeOnline" column="is_paper_change_online"/>
        <result property="isOnlineChangePaper" column="is_online_change_paper"/>
        <result property="isElectronicContractNew" column="is_electronic_contract_new"/>
        <result property="isElectronicContractNew" column="is_electronic_contract_new"/>
        <result property="companyName" column="companyName"/>
        <result property="isCancelOrderGift" column="is_cancel_order_gift"/>
        <result property="visitId" column="visit_id"/>
        <result property="contractSubject" column="contract_subject"/>


    </resultMap>

    <sql id="selectErpOrdersVo">
        select id, vc_order_number, num_source, num_contract_id, num_clue_id, num_client_id, num_payee,
               num_user_id, num_total_price, num_pay_price, num_last_price, num_refund_price, num_discount_amount,
               dat_signing_date, num_payment_status, num_status, num_valid_status, num_points, num_growth_value, num_business_approval_id,
               dat_signing_datecollect_money_time,dat_finance_collection_time , num_created_by, dat_signing_datecreated_time,
               num_updated_by, dat_signing_dateupdated_time, num_customer_type, num_payment_term, vc_phone, is_electronic_contract,num_create_order_examine_status,
               num_modify_order_examine_status,num_cancel_order_examine_status,num_refund_examine_status,ac_start,ac_end,
               commit_order_type,license_number, is_return, num_client_id_new, num_contract_id_new, is_change_blank_contract, is_paper_change_online,is_online_change_paper,
               is_electronic_contract_new,is_cancel_order_gift,visit_id,contract_subject, customer_intention_id
        from erp_orders
    </sql>

    <select id="selectErpOrdersList" parameterType="ErpOrders" resultMap="ErpOrdersResult">
        <include refid="selectErpOrdersVo"/>
        <where>
            <if test="vcOrderNumber != null  and vcOrderNumber != ''">and vc_order_number = #{vcOrderNumber}</if>
            <if test="numSource != null ">and num_source = #{numSource}</if>
            <if test="numContractId != null ">and num_contract_id = #{numContractId}</if>
            <if test="numClueId != null  and numClueId != ''">and num_clue_id = #{numClueId}</if>
            <if test="numClientId != null ">and num_client_id = #{numClientId}</if>
            <if test="numPayee != null ">and num_payee = #{numPayee}</if>
            <if test="numUserId != null ">and num_user_id = #{numUserId}</if>
            <if test="numTotalPrice != null ">and num_total_price = #{numTotalPrice}</if>
            <if test="numPayPrice != null ">and num_pay_price = #{numPayPrice}</if>
            <if test="numLastPrice != null ">and num_last_price = #{numLastPrice}</if>
            <if test="numDiscountAmount != null ">and num_discount_amount = #{numDiscountAmount}</if>
            <if test="datSigningDate != null ">and dat_signing_date = #{datSigningDate}</if>
            <if test="numPaymentStatus != null ">and num_payment_status = #{numPaymentStatus}</if>
            <if test="numStatus != null ">and num_status = #{numStatus}</if>
            <if test="numPoints != null ">and num_points = #{numPoints}</if>
            <if test="numGrowthValue != null ">and num_growth_value = #{numGrowthValue}</if>
            <if test="numBusinessApprovalId != null ">and num_business_approval_id = #{numBusinessApprovalId}</if>
            <if test="datSigningDatecollectMoneyTime != null ">and dat_signing_datecollect_money_time =
                #{datSigningDatecollectMoneyTime}
            </if>
            <if test="datFinanceCollectionTime != null ">and dat_finance_collection_time = #{datFinanceCollectionTime}
            </if>
            <if test="numCreatedBy != null ">and num_created_by = #{numCreatedBy}</if>
            <if test="datSigningDatecreatedTime != null ">and dat_signing_datecreated_time =
                #{datSigningDatecreatedTime}
            </if>
            <if test="numUpdatedBy != null ">and num_updated_by = #{numUpdatedBy}</if>
            <if test="datSigningDateupdatedTime != null ">and dat_signing_dateupdated_time =
                #{datSigningDateupdatedTime}
            </if>
            <if test="numCustomerType != null ">and num_customer_type = #{numCustomerType}</if>
            <if test="numPaymentTerm != null ">and num_payment_term = #{numPaymentTerm}</if>
            <if test="vcPhone != null  and vcPhone != ''">and vc_phone = #{vcPhone}</if>
            <if test="cipherId != null and cipherId != ''">and cipher_id = #{cipherId}</if>
        </where>
    </select>

    <select id="selectErpOrdersById" parameterType="Long" resultMap="ErpOrdersResult">
        <include refid="selectErpOrdersVo"/>
        where id = #{id}
    </select>

    <select id="selectOrderByOrderNumber" resultType="com.nnb.erp.domain.ErpOrders">
        <include refid="selectErpOrdersVo"/>
        where vc_order_number = #{orderNumber}
    </select>

    <select id="selectErpOrdersByIdList" parameterType="Long" resultMap="ErpOrdersResult">
        select eo.id, eo.vc_order_number, eo.num_source, eo.num_contract_id, eo.num_clue_id, eo.num_client_id,
        eo.num_payee, eo.num_user_id, eo.num_total_price, eo.num_pay_price, eo.num_last_price, eo.num_discount_amount,
        eo.dat_signing_date, eo.num_payment_status, eo.num_status, eo.num_points, eo.num_growth_value,
        eo.num_business_approval_id, eo.dat_signing_datecollect_money_time, eo.dat_finance_collection_time,
        eo.num_created_by, eo.dat_signing_datecreated_time, eo.num_updated_by, eo.dat_signing_dateupdated_time,
        eo.num_customer_type, eo.num_payment_term, eo.vc_phone, eo.is_electronic_contract,
        eo.num_create_order_examine_status, eo.num_modify_order_examine_status, eo.num_cancel_order_examine_status,
        eo.num_refund_examine_status, eo.ac_start,ac_end, eo.commit_order_type, eo.license_number, eo.is_return,
        eo.num_client_id_new, eo.num_contract_id_new, eo.is_change_blank_contract, eo.is_paper_change_online,
        eo.is_online_change_paper, eo.is_electronic_contract_new, IFNULL(ee.vc_company_name, ep.vc_name) AS 'companyName'
        from erp_orders eo
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        where eo.id in (
            <foreach collection="idList" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>
    <select id="selectErpOrdersBycipherId" parameterType="String" resultMap="ErpOrdersResult">
        <include refid="selectErpOrdersVo"/>
        where cipher_id = #{cipherId}
    </select>

    <insert id="insertErpOrders" parameterType="ErpOrders" useGeneratedKeys="true" keyProperty="id">
        insert into erp_orders
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactNum !=null">contact_num,</if>
            <if test="vcOrderNumber != null">vc_order_number,</if>
            <if test="numSource != null">num_source,</if>
            <if test="areaId != null">area_id,</if>
            <if test="corporateProperty != null">corporate_property,</if>
            <if test="numContractId != null">num_contract_id,</if>
            <if test="numClueId != null">num_clue_id,</if>
            <if test="numClientId != null">num_client_id,</if>
            <if test="numPayee != null">num_payee,</if>
            <if test="numUserId != null">num_user_id,</if>
            <if test="numTotalPrice != null">num_total_price,</if>
            <if test="numPayPrice != null">num_pay_price,</if>
            <if test="numLastPrice != null">num_last_price,</if>
            <if test="numDiscountAmount != null">num_discount_amount,</if>
            <if test="datSigningDate != null">dat_signing_date,</if>
            <if test="numPaymentStatus != null">num_payment_status,</if>
            <if test="numStatus != null">num_status,</if>
            <if test="numValidStatus != null">num_valid_status,</if>
            <if test="numRetainageStatus != null">num_retainage_status,</if>
            <if test="numBizStatus != null">num_biz_status,</if>
            <if test="numCreateOrderExamineStatus != null">num_create_order_examine_status,</if>
            <if test="numModifyOrderExamineStatus != null">num_modify_order_examine_status,</if>
            <if test="numCancelOrderExamineStatus != null">num_cancel_order_examine_status,</if>
            <if test="numRefundExamineStatus != null">num_refund_examine_status,</if>
            <if test="numPoints != null">num_points,</if>
            <if test="numGrowthValue != null">num_growth_value,</if>
            <if test="numBusinessApprovalId != null">num_business_approval_id,</if>
            <if test="datSigningDatecollectMoneyTime != null">dat_signing_datecollect_money_time,</if>
            <if test="datFinanceCollectionTime != null">dat_finance_collection_time,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="datSigningDatecreatedTime != null">dat_signing_datecreated_time,</if>
            <if test="numUpdatedBy != null">num_updated_by,</if>
            <if test="datSigningDateupdatedTime != null">dat_signing_dateupdated_time,</if>
            <if test="numCustomerType != null">num_customer_type,</if>
            <if test="numPaymentTerm != null">num_payment_term,</if>
            <if test="vcPhone != null">vc_phone,</if>
            <if test="vcRemark != null">vc_remark,</if>
            <if test="contractSubject != null">contract_subject,</if>
            <if test="onlineContractPdf != null">vc_online_contract_pdf,</if>
            <if test="isElectronicContract != null">is_electronic_contract,</if>
            <if test="cipherId != null">cipher_id,</if>
            <if test="acStart!=null">ac_start,</if>
            <if test="acEnd!=null">ac_end,</if>
            <if test="commitOrderType!=null">commit_order_type,</if>
            <if test="licenseNumber!=null">license_number,</if>
            <if test="numContractIdNew!=null">num_contract_id_new,</if>
            <if test="gsRemark!=null">gs_remark,</if>
            <if test="openId!=null">open_id,</if>
            <if test="isChangeBlankContract!=null">is_change_blank_contract,</if>
            <if test="isPaperChangeOnline!=null">is_paper_change_online,</if>
            <if test="isOnlineChangePaper!=null">is_online_change_paper,</if>
            <if test="isElectronicContractNew!=null">is_electronic_contract_new,</if>
            <if test="nicheFlowConfId!=null">niche_flow_conf_id,</if>
            <if test="visitId!=null">visit_id,</if>
            <if test="customerIntentionId!=null">customer_intention_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactNum !=null">#{contactNum},</if>
            <if test="vcOrderNumber != null">#{vcOrderNumber},</if>
            <if test="numSource != null">#{numSource},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="corporateProperty != null">#{corporateProperty},</if>
            <if test="numContractId != null">#{numContractId},</if>
            <if test="numClueId != null">#{numClueId},</if>
            <if test="numClientId != null">#{numClientId},</if>
            <if test="numPayee != null">#{numPayee},</if>
            <if test="numUserId != null">#{numUserId},</if>
            <if test="numTotalPrice != null">#{numTotalPrice},</if>
            <if test="numPayPrice != null">#{numPayPrice},</if>
            <if test="numLastPrice != null">#{numLastPrice},</if>
            <if test="numDiscountAmount != null">#{numDiscountAmount},</if>
            <if test="datSigningDate != null">#{datSigningDate},</if>
            <if test="numPaymentStatus != null">#{numPaymentStatus},</if>
            <if test="numStatus != null">#{numStatus},</if>
            <if test="numValidStatus != null">#{numValidStatus},</if>
            <if test="numRetainageStatus != null">#{numRetainageStatus},</if>
            <if test="numBizStatus != null">#{numBizStatus},</if>
            <if test="numCreateOrderExamineStatus != null">#{numCreateOrderExamineStatus},</if>
            <if test="numModifyOrderExamineStatus != null">#{numModifyOrderExamineStatus},</if>
            <if test="numCancelOrderExamineStatus != null">#{numCancelOrderExamineStatus},</if>
            <if test="numRefundExamineStatus != null">#{numRefundExamineStatus},</if>
            <if test="numPoints != null">#{numPoints},</if>
            <if test="numGrowthValue != null">#{numGrowthValue},</if>
            <if test="numBusinessApprovalId != null">#{numBusinessApprovalId},</if>
            <if test="datSigningDatecollectMoneyTime != null">#{datSigningDatecollectMoneyTime},</if>
            <if test="datFinanceCollectionTime != null">#{datFinanceCollectionTime},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="datSigningDatecreatedTime != null">#{datSigningDatecreatedTime},</if>
            <if test="numUpdatedBy != null">#{numUpdatedBy},</if>
            <if test="datSigningDateupdatedTime != null">#{datSigningDateupdatedTime},</if>
            <if test="numCustomerType != null">#{numCustomerType},</if>
            <if test="numPaymentTerm != null">#{numPaymentTerm},</if>
            <if test="vcPhone != null">#{vcPhone},</if>
            <if test="vcRemark != null">#{vcRemark},</if>
            <if test="contractSubject != null">#{contractSubject},</if>
            <if test="onlineContractPdf != null">#{onlineContractPdf},</if>
            <if test="isElectronicContract != null">#{isElectronicContract},</if>
            <if test="cipherId != null">#{cipherId},</if>
            <if test="acStart!=null">#{acStart},</if>
            <if test="acEnd!=null">#{acEnd},</if>
            <if test="commitOrderType!=null">#{commitOrderType},</if>
            <if test="licenseNumber!=null">#{licenseNumber},</if>
            <if test="numContractIdNew!=null">#{numContractIdNew},</if>
            <if test="gsRemark!=null">#{gsRemark},</if>
            <if test="openId!=null">#{openId},</if>
            <if test="isChangeBlankContract!=null">#{isChangeBlankContract},</if>
            <if test="isPaperChangeOnline!=null">#{isPaperChangeOnline},</if>
            <if test="isOnlineChangePaper!=null">#{isOnlineChangePaper},</if>
            <if test="isElectronicContractNew!=null">#{isElectronicContractNew},</if>
            <if test="nicheFlowConfId!=null">#{nicheFlowConfId},</if>
            <if test="visitId!=null">#{visitId},</if>
            <if test="customerIntentionId!=null">#{customerIntentionId},</if>

        </trim>
    </insert>

    <update id="updateErpOrders" parameterType="ErpOrders">
        update erp_orders
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcOrderNumber != null">vc_order_number = #{vcOrderNumber},</if>
            <if test="numSource != null">num_source = #{numSource},</if>
            <if test="numContractId != null">num_contract_id = #{numContractId},</if>
            <if test="numClueId != null">num_clue_id = #{numClueId},</if>
            <if test="numClientId != null">num_client_id = #{numClientId},</if>
            <if test="numClientIdNew != null">num_client_id_new = #{numClientIdNew},</if>
            <if test="numPayee != null">num_payee = #{numPayee},</if>
            <if test="numUserId != null">num_user_id = #{numUserId},</if>
            <if test="numTotalPrice != null">num_total_price = #{numTotalPrice},</if>
            <if test="numPayPrice != null">num_pay_price = #{numPayPrice},</if>
            <if test="numLastPrice != null">num_last_price = #{numLastPrice},</if>
            <if test="numDiscountAmount != null">num_discount_amount = #{numDiscountAmount},</if>
            <if test="datSigningDate != null">dat_signing_date = #{datSigningDate},</if>
            <if test="numPaymentStatus != null">num_payment_status = #{numPaymentStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="numStatus != null">num_status = #{numStatus},</if>
            <if test="numValidStatus != null">num_valid_status = #{numValidStatus},</if>
            <if test="numRetainageStatus != null">num_retainage_status = #{numRetainageStatus},</if>
            <if test="numBizStatus != null">num_biz_status = #{numBizStatus},</if>
            <if test="numCreateOrderExamineStatus != null">num_create_order_examine_status =
                #{numCreateOrderExamineStatus},
            </if>
            <if test="numModifyOrderExamineStatus != null">num_modify_order_examine_status =
                #{numModifyOrderExamineStatus},
            </if>
            <if test="numCancelOrderExamineStatus != null">num_cancel_order_examine_status =
                #{numCancelOrderExamineStatus},
            </if>
            <if test="numRefundExamineStatus != null">num_refund_examine_status = #{numRefundExamineStatus},</if>
            <if test="numPoints != null">num_points = #{numPoints},</if>
            <if test="numGrowthValue != null">num_growth_value = #{numGrowthValue},</if>
            <if test="numBusinessApprovalId != null">num_business_approval_id = #{numBusinessApprovalId},</if>
            <if test="datSigningDatecollectMoneyTime != null">dat_signing_datecollect_money_time =
                #{datSigningDatecollectMoneyTime},
            </if>
            <if test="datFinanceCollectionTime != null">dat_finance_collection_time = #{datFinanceCollectionTime},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datSigningDatecreatedTime != null">dat_signing_datecreated_time = #{datSigningDatecreatedTime},
            </if>
            <if test="numUpdatedBy != null">num_updated_by = #{numUpdatedBy},</if>
            <if test="datSigningDateupdatedTime != null">dat_signing_dateupdated_time = #{datSigningDateupdatedTime},
            </if>
            <if test="numCustomerType != null">num_customer_type = #{numCustomerType},</if>
            <if test="numPaymentTerm != null">num_payment_term = #{numPaymentTerm},</if>
            <if test="vcPhone != null">vc_phone = #{vcPhone},</if>
            <if test="vcRemark != null">vc_remark = #{vcRemark},</if>
            <if test="contractSubject != null">contract_subject = #{contractSubject},</if>
            <if test="onlineContractPdf != null">vc_online_contract_pdf = #{onlineContractPdf},</if>
            <if test="isReturn!=null">is_return=#{isReturn},</if>
            <if test="isElectronicContract!=null">is_electronic_contract=#{isElectronicContract},</if>
            <if test="numContractIdNew !=null">num_contract_id_new=#{numContractIdNew},</if>
            <if test="contactNum !=null">contact_num=#{contactNum},</if>
            <if test="checkTheContractTime !=null">check_the_contract_time=#{checkTheContractTime},</if>
            <if test="isChangeBlankContract !=null">is_change_blank_contract=#{isChangeBlankContract},</if>
            <if test="releaseContract !=null and releaseContract == 1">contact_num = null,num_contract_id = null,</if>
            <if test="isPaperChangeOnline !=null">is_paper_change_online=#{isPaperChangeOnline},</if>
            <if test="isOnlineChangePaper !=null">is_online_change_paper=#{isOnlineChangePaper},</if>
            <if test="releaseEleContractUrl !=null and releaseEleContractUrl == 1">vc_online_contract_pdf = null,</if>
            <if test="isElectronicContractNew !=null">is_electronic_contract_new = #{isElectronicContractNew},</if>
            <if test="deleteEleContactUrl != null">ele_contact_url = null,</if>
            <if test="createArriveFinance !=null">create_arrive_finance = #{createArriveFinance},</if>
            <if test="createApproveFinance !=null">create_approve_finance = #{createApproveFinance},</if>
            <if test="isCancelOrderGift != null">is_cancel_order_gift = #{isCancelOrderGift},</if>
            <if test="visitId != null">visit_id = #{visitId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpOrdersById" parameterType="Long">
        delete from erp_orders where id = #{id}
    </delete>

    <delete id="deleteErpOrdersByIds" parameterType="String">
        delete from erp_orders where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <resultMap type="com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO" id="ErpOrdersAndServiceOrderInfoResult">
        <result property="orderId" column="orderId"/>
        <result property="clientId" column="clientId"/>
        <result property="orderNumber" column="orderNumber"/>
        <result property="orderStatusType" column="orderStatusType"/>
        <result property="userName" column="userName"/>
        <result property="deptName" column="deptName"/>
        <result property="totalPrice" column="totalPrice"/>
        <result property="payPrice" column="payPrice"/>
        <result property="lastPrice" column="lastPrice"/>
        <result property="signingDate" column="signingDate"/>
        <result property="allPrice" column="allPrice"/>
        <result property="vcContractNumber" column="contractNumber"/>
        <result property="numValidStatus" column="numValidStatus"/>
        <result property="numCreateOrderExamineStatus" column="numCreateOrderExamineStatus"/>
        <result property="numModifyOrderExamineStatus" column="numModifyOrderExamineStatus"/>
        <result property="numCancelOrderExamineStatus" column="numCancelOrderExamineStatus"/>
        <result property="numRefundExamineStatus" column="numRefundExamineStatus"/>
        <result property="vcOnlineContractPdf" column="vcOnlineContractPdf"/>
        <result property="isElectronicContract" column="isElectronicContract"/>
        <collection property="serviceOrders" ofType="com.nnb.erp.domain.vo.ErpProductForOrderDetailVO">
            <result property="productId" column="productId"/>
            <result property="productName" column="productName"/>
            <result property="serviceName" column="serviceName"/>
            <result property="taxName" column="taxName"/>
            <result property="unit" column="unit"/>
            <result property="productCount" column="productCount"/>
            <result property="region" column="region"/>
            <result property="unitPrice" column="unitPrice"/>
            <result property="couponId" column="couponId"/>
            <result property="couponName" column="couponName"/>
            <result property="combinedId" column="combinedId"/>
            <result property="combinedName" column="combinedName"/>
            <result property="productPreferential" column="productPreferential"/>
            <result property="channelFee" column="channelFee"/>
            <result property="couponPrice" column="couponPrice"/>
            <result property="totalPrice" column="totalPrice"/>
            <result property="payPrice" column="payPrice"/>
            <result property="lastPrice" column="lastPrice"/>
        </collection>

    </resultMap>
    <!--根据客户id获取订单-->
    <select id="selectErpOrdersListByClientId" parameterType="long" resultMap="ErpOrdersAndServiceOrderInfoResult">
        SELECT
        eo.id AS 'orderId',
        eo.num_create_order_examine_status AS numCreateOrderExamineStatus,
        eo.num_modify_order_examine_status AS numModifyOrderExamineStatus,
        eo.num_cancel_order_examine_status AS numCancelOrderExamineStatus,
        eo.num_refund_examine_status AS numRefundExamineStatus,
        eo.num_client_id AS 'clientId',
        eo.vc_order_number AS 'orderNumber',
        ec.vc_contract_number as contractNumber,
        eo.num_status AS 'orderStatusType',
        su.nick_name AS 'userName',
        sd.dept_name AS 'deptName',
        eo.num_total_price + eo.num_discount_amount AS 'allPrice',
        eo.num_total_price AS 'totalPrice',
        eo.num_pay_price AS 'payPrice',
        eo.num_last_price AS 'lastPrice',
        eo.num_discount_amount AS 'discountAmount',
        eo.dat_signing_date AS 'signingDate',
        eo.num_refund_price as sumRefundPrice,
        eo.num_valid_status as numValidStatus,
        eso.num_product_count as productCount,
        eso.num_total_price as totalPrice,
        eso.num_pay_price as payPrice,
        eso.num_last_price as lastPrice,
        eso.num_coupon_price as couponPrice,
        eso.num_product_preferential as productPreferential,
        eso.num_channel_fee as channelFee,
        esoi.`vc_product_name` as productName,
        esoi.`vc_corporate_property` as corporateProperty,
        esoi.`num_price` as numPrice,
        esoi.`vc_product_type` as productType,
        esoi.`vc_unit` as unit,
        esoi.`vc_area` as region,
        eo.vc_online_contract_pdf AS vcOnlineContractPdf,
        eo.is_electronic_contract as 'isElectronicContract'
        FROM
        erp_orders eo
        LEFT JOIN erp_contract ec ON eo.num_contract_id = ec.id
        LEFT JOIN sys_user su ON eo.num_user_id = su.user_id
        LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
        LEFT JOIN erp_service_orders eso ON eo.id = eso.num_order_id
        LEFT JOIN erp_service_orders_info esoi ON eso.id = esoi.num_service_orders
        <where>
            <if test="status != null and status.length != 0">
                and eo.num_status IN(1,2,3,4)

            </if>
            <if test="ids != null and ids.length != 0">
                and eo.num_client_id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="numValidStatus != null and numValidStatus.length != 0">
                and eo.num_valid_status IN(0, 1, 3)
            </if>
        </where>

    </select>



    <!--根据客户id获取订单-->
    <select id="selectOrderListByClientId" parameterType="long" resultType="com.nnb.erp.domain.enterprise.ErpOrderDetail">
        SELECT
        eo.id AS 'orderId',
        eo.num_client_id AS 'clientId',
        eo.vc_order_number AS 'vcOrderNumber',
        eo.contact_num as contractNumber,
        case eo.num_status
        when 1 then '待审核'
        when 2 then '待服务'
        when 3 then '服务中'
        when 4 then '已完成'
        end as 'numStatus',
        su.nick_name AS 'signer',
        eo.num_total_price AS 'numTotalPrice',
        eo.num_pay_price AS 'payPrice',
        eo.num_last_price AS 'lastPrice',
        eo.num_discount_amount AS 'numDiscountAmount',
        eo.dat_signing_date AS 'datSigningDate',
        eo.is_electronic_contract as 'isElectronicContract'
        FROM
        erp_orders eo
        left join sys_user su on eo.num_user_id = su.user_id
        <where>
<!--
            <if test="ids != null and ids.length != 0">
-->
                eo.num_client_id in
                <foreach item="id" collection="clientIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
<!--
            </if>
-->
        </where>

    </select>

    <!-- 获取订单列表，用于订单管理列表展示。 -->
    <select id="getOrderListForOm" resultType="com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO">
        SELECT eo.id AS 'orderId',
        eo.vc_order_number AS 'orderNumber',
        eo.num_source AS 'orderSourceType',
        IF(eo.num_source = 0, 'CRM', '小程序') AS 'orderSourceName',
        eo.num_client_id AS 'clientId',
        IFNULL(ee.vc_company_name, ep.vc_name) AS 'clientName',
        bc.vc_customer_name AS 'clueCustomerName',
        u.dept_id AS 'deptId',
        sd.dept_name AS 'deptName',
        u.user_id AS 'userId',
        u.nick_name AS 'userName',
        DATE_FORMAT(eo.dat_signing_date, '%Y-%m-%d') AS 'signingDate',
        eo.num_discount_amount AS 'discountAmount',
        eo.num_total_price AS 'totalPrice',
        eo.num_pay_price AS 'payPrice',
        eo.num_last_price AS 'lastPrice',
        DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y-%m-%d %H:%i:%s') AS 'createTime',
        eo.vc_remark AS 'remark',
        eo.contract_subject AS contractSubject,
        eo.vc_phone AS vcPhone,
        eo.contact_num as 'vcContractNumber',
        eo.vc_online_contract_pdf AS vcOnlineContractPdf,
        eo.is_electronic_contract AS isElectronicContract,
        bc.vc_phone AS clueCustomerPhone,
        eo.cipher_id AS cipherId,
        eo.num_create_order_examine_status AS numCreateOrderExamineStatus,
        eo.num_modify_order_examine_status AS numModifyOrderExamineStatus,
        eo.num_cancel_order_examine_status AS numCancelOrderExamineStatus,
        eo.num_refund_examine_status AS numRefundExamineStatus,
        eo.status AS status,
        eo.num_valid_status AS numValidStatus,
        eo.commit_order_type AS commitOrderType,
        ec.contactName AS 'contactName',
        ec.contactPhone AS commitOrderType,
        ee.id AS enterpriseId,
        bc.id AS clueId,
        eo.is_return as isReturn,
        DATE_FORMAT(eo.dat_finance_collection_time, '%Y-%m-%d') AS 'datFinanceCollectionTime',
        eo.num_refund_price as sumRefundPrice
        FROM erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        <if test="query.numNameIdList!=null and query.numNameIdList.size > 0">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1=1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"
            <if test="query.orderStatusType eq 1 and query.orderStatusType !=''">
                AND (
                eo.num_create_order_examine_status IN (1, 3)
                )
            </if>
            <if test="query.orderStatusType eq 2 and query.orderStatusType !=''">
                AND (
                eo.num_modify_order_examine_status IN (1, 3)
                )
            </if>
            <if test="query.orderStatusType eq 3 and query.orderStatusType !=''">
                AND (
                eo.num_refund_examine_status = 3 or eo.num_valid_status = 5 or eo.num_valid_status = 6
                )
            </if>
            <if test="query.orderStatusType eq 4 and query.orderStatusType !=''">
                AND (
                eo.num_cancel_order_examine_status IN (3)
                )
            </if>
            <if test="query.orderStatusType eq 5 and query.orderStatusType !=''">
                and (eo.num_create_order_examine_status = 5
                or eo.num_modify_order_examine_status = 5
                or eo.num_refund_examine_status =5
                or eo.num_cancel_order_examine_status = 5
                and eo.num_valid_status != 2
                and eo.num_cancel_order_examine_status!=3
                and eo.num_refund_examine_status!=3
                and eo.num_valid_status != 3
                )
            </if>
            <if test="query.orderStatusType eq 6 and query.orderStatusType !=''">
                AND (
                eo.num_payment_status = 0
                )
            </if>
            <if test="query.orderStatusType eq 7 and query.orderStatusType !='' ">
                AND (
                eo.num_payment_status = 1
                )
            </if>
            <if test="query.orderStatusType eq 8 and query.orderStatusType !=''">
                AND (
                eo.num_valid_status = 1
                )
            </if>
            <if test="query.orderStatusType eq 9 and query.orderStatusType !=''">
                AND (
                eo.num_create_order_examine_status IN (2, 4)
                OR eo.num_modify_order_examine_status IN (2, 4)
                OR eo.num_refund_examine_status IN (4)
                OR eo.num_cancel_order_examine_status IN (4)
                )
            </if>
            <if test="query.orderStatusType eq 10 and query.orderStatusType !=''">
                AND (
                eo.num_valid_status = 3 or num_valid_status = 4
                )
            </if>
            <if test="query.orderStatusType eq 11 and query.orderStatusType !=''">
                AND (
                eo.num_valid_status = 2
                )
            </if>
            <if test="query.orderStatusType eq 12 and query.orderStatusType !=''">
                AND (
                eo.num_create_order_examine_status = 10
                )
            </if>
            <if test="query.orderStatusType eq 13 and query.orderStatusType !=''">
                AND (
                eo.num_create_order_examine_status = 11
                )
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.orderId !=null">and eo.id =
                #{query.orderId}
            </if>
            <if test="query.licenseNumber !=null and query.licenseNumber !='' ">and eo.license_number =
                #{query.licenseNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>
            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.numNameIdList!=null and query.numNameIdList.size > 0">
                and name.num_name_id in
                <foreach collection="query.numNameIdList" item="numNameId" open="(" separator="," close=")">
                    #{numNameId}
                </foreach>
            </if>

            <!--            <if test="query.keyword != null and query.keyword != ''">-->
            <!--                AND (-->
            <!--                    eo.vc_order_number = #{query.keyword}-->
            <!--                    OR-->
            <!--                    bc.vc_customer_name LIKE CONCAT('%', #{query.keyword}, '%')-->
            <!--                    OR-->
            <!--                    ee.vc_company_name LIKE CONCAT('%', #{query.keyword}, '%')-->
            <!--                    OR-->
            <!--                    ep.vc_name LIKE CONCAT('%', #{query.keyword}, '%')-->
            <!--                )-->
            <!--            </if>-->
            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,257,258))
            </if>
            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ORDER BY eo.dat_signing_datecreated_time DESC
    </select>

    <!-- 获取指定订单内、服务单的全部已审核退款。 -->
    <select id="getSumRefundPriceByOrderId" resultType="BigDecimal">
        SELECT IFNULL(SUM(esor.num_refund_price), 0) AS 'sumRefundPrice'
        FROM erp_orders eo
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
        LEFT JOIN erp_service_order_refund esor ON esor.num_service_order_id = eso.id AND esor.num_status = 1
        WHERE eo.id = #{orderId};
    </select>

    <!-- 获取订单各个状态的统计数据。 -->
    <select id="getOrderStatusCount" parameterType="com.nnb.common.core.web.domain.BaseEntity"
            resultType="com.nnb.erp.domain.vo.ErpOrderStatusCountVO">
        SELECT COUNT(IF(num_create_order_examine_status IN (1, 3), TRUE, NULL)) AS 'auditing',
        COUNT(IF(num_modify_order_examine_status IN (1, 3), TRUE, NULL)) AS 'editAuditing',
        COUNT(IF(num_refund_examine_status IN (3), TRUE, NULL)) AS 'refundAuditing',
        COUNT(IF(num_cancel_order_examine_status IN (3), TRUE, NULL)) AS 'deprecatedAuditing',
        COUNT(IF(num_create_order_examine_status IN (5) OR num_modify_order_examine_status IN (5) OR
        num_refund_examine_status IN (5) OR num_cancel_order_examine_status IN (5), TRUE, NULL)) AS 'auditPass',
        COUNT(IF(num_payment_status = 0, TRUE, NULL)) AS 'paying',
        COUNT(IF(num_payment_status = 1, TRUE, NULL)) AS 'payed',
        COUNT(IF(num_valid_status = 1, TRUE, NULL)) AS 'canceled',
        COUNT(IF(num_create_order_examine_status IN (2, 4) OR num_modify_order_examine_status IN (2, 4) OR
        num_refund_examine_status IN (4) OR num_cancel_order_examine_status IN (4), TRUE, NULL)) AS 'rejected',
        COUNT(IF(num_valid_status = 3, TRUE, NULL)) AS 'refund',
        COUNT(IF(num_valid_status = 2, TRUE, NULL)) AS 'deprecated',
        COUNT(IF(num_create_order_examine_status IN (10), TRUE, NULL)) AS 'customerSignNot',
        COUNT(IF(num_create_order_examine_status IN (11), TRUE, NULL)) AS 'customerRejected'
        FROM erp_orders eo
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN sys_user u ON u.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            <if test="params.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="params.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>
            <!-- 数据范围过滤 -->
            <if test="params.dataRange == null or params.dataRange == ''">
                ${params.dataScope}
            </if>
        </where>
    </select>

    <!-- 获取指定订单回款记录。 -->
    <resultMap id="RetainageMap" type="com.nnb.erp.domain.vo.ErpRetainageForOmListVO">
        <result property="retainageId" column="retainageId"/>
        <result property="discounts" column="discounts"/>
        <result property="collectionPrice" column="collectionPrice"/>
        <result property="paymentTermType" column="paymentTermType"/>
        <result property="paymentTermName" column="paymentTermName"/>
        <result property="createId" column="createId"/>
        <result property="createName" column="createName"/>
        <result property="createTime" column="createTime"/>
        <result property="financeCollectionTime" column="financeCollectionTime"/>
        <result property="auditStatusType" column="auditStatusType"/>
        <result property="auditUser" column="auditUser"/>
        <result property="auditName" column="auditName"/>
        <result property="auditTime" column="auditTime"/>
        <result property="termId" column="termId"/>
        <collection property="payments" ofType="com.nnb.erp.domain.vo.ErpPaymentForOmListVO">
            <result property="paymentId" column="paymentId"/>
            <result property="payeeId" column="payeeId"/>
            <result property="payeeName" column="payeeName"/>
            <result property="collectionTime" column="collectionTime"/>
            <collection property="terms" ofType="com.nnb.erp.domain.vo.ErpPaymentTermForOmListVO">
                <result property="termId" column="termId"/>
                <result property="money" column="money"/>
                <result property="paymentType" column="paymentType"/>
                <result property="paymentUrl" column="paymentUrl"/>
            </collection>
        </collection>
    </resultMap>
    <select id="getRetainageByOrderId" resultMap="RetainageMap">
        SELECT err.id                                                    AS 'retainageId',
               err.num_discounts                                         AS 'discounts',
               eopti.num_money                                            AS 'collectionPrice',
               err.num_created_by                                        AS 'createId',
               t_create_user.nick_name                                   AS 'createName',
               DATE_FORMAT(err.dat_signing_datecreated_time, '%Y-%m-%d') AS 'createTime',
               DATE_FORMAT(err.dat_finance_collection_time, '%Y-%m-%d')  AS 'financeCollectionTime',
               err.num_status                                            AS 'auditStatusType',
               err.num_updated_by                                        AS 'auditUser',
               t_audit_user.nick_name                                    AS 'auditName',
               DATE_FORMAT(err.dat_signing_dateupdated_time, '%Y-%m-%d %H:%i:%s') AS 'auditTime',
               eopt.id                                                   AS 'paymentId',
               eopt.num_payee                                            AS 'payeeId',
               t_payee.nick_name                                         AS 'payeeName',
               DATE_FORMAT(eopt.dat_collection_time, '%Y-%m-%d')         AS 'collectionTime',
               eopti.id                                                  AS 'termId',
               eopti.num_money                                           AS 'money',
               eopti.num_payment_type                                    AS 'paymentType',
               eopti.vc_payment_url                                      AS 'paymentUrl'
        FROM erp_retainage_return err
        LEFT JOIN erp_orders eo ON eo.id = err.num_order_id
        LEFT JOIN erp_order_payment_term eopt ON eopt.num_retainage_id = err.id
        LEFT JOIN erp_order_payment_term_info eopti ON eopti.term_id = eopt.id
        LEFT JOIN sys_user t_create_user ON t_create_user.user_id = err.num_created_by
        LEFT JOIN sys_user t_payee ON t_payee.user_id = eopt.num_payee
        LEFT JOIN sys_user t_audit_user ON t_audit_user.user_id = err.num_updated_by
        WHERE err.num_order_id = #{orderId} and err.num_type = 2
    </select>

    <!-- 获取客户、订单信息，用于订单详情。 -->
    <resultMap id="ClientAndOrderForOrderDetailMap" type="com.nnb.erp.domain.vo.ErpOrderDetailForOmVO">
        <association property="client" javaType="com.nnb.erp.domain.vo.ErpClientForOrderDetailVO">
            <result property="clientId" column="clientId"/>
            <result property="clientType" column="clientType"/>
            <result property="clientName" column="clientName"/>
            <result property="historyName" column="historyName"/>
            <result property="clueId" column="clueId"/>
            <result property="clueCustomerId" column="clueCustomerId"/>
            <result property="clueCustomerName" column="clueCustomerName"/>
            <result property="clueCustomerPhone" column="clueCustomerPhone"/>
            <result property="corporatePropertyId" column="corporatePropertyId"/>
            <result property="corporatePropertyName" column="corporatePropertyName"/>
            <result property="clientCityId" column="clientCityId"/>
            <result property="clientCityName" column="clientCityName"/>
        </association>
        <association property="order" javaType="com.nnb.erp.domain.vo.ErpOrderForOrderDetailVO">
            <result property="orderId" column="orderId"/>
            <result property="userId" column="userId"/>
            <result property="userName" column="userName"/>
            <result property="signingDate" column="signingDate"/>
            <result property="orderNumber" column="orderNumber"/>
            <result property="orderSourceType" column="orderSourceType"/>
            <result property="orderSourceName" column="orderSourceName"/>
            <result property="contractId" column="contractId"/>
            <result property="contractNumber" column="contractNumber"/>
            <result property="orderStatusType" column="orderStatusType"/>
            <result property="discountAmount" column="discountAmount"/>
            <result property="totalPrice" column="totalPrice"/>
            <result property="payPrice" column="payPrice"/>
            <result property="lastPrice" column="lastPrice"/>
            <result property="refundPrice" column="refundPrice"/>
        </association>
    </resultMap>
    <select id="getClientAndOrderInfoForOrderDetail" resultMap="ClientAndOrderForOrderDetailMap">
        SELECT ec.id                                        AS 'clientId',
               ec.num_type                                  AS 'clientType',
               IFNULL(ee.vc_company_name, ep.vc_name)       AS 'clientName',
               IFNULL(ee.vc_history_name, '')               AS 'historyName',
               ec.num_clue_id                               AS 'clueId',
               bc.customer_id                               AS 'clueCustomerId',
               bc.vc_company_name                           AS 'clueCustomerName',
               bc.vc_phone                                  AS 'clueCustomerPhone',
               ee.num_corporate_property_id                 AS 'corporatePropertyId',
               eptd.vc_tax_name                             AS 'corporatePropertyName',
               ec.num_city_id                               AS 'clientCityId',
               cdr.title                                    AS 'clientCityName',
               eo.id                                        AS 'orderId',
               eo.num_user_id                               AS 'userId',
               su.nick_name                                 AS 'userName',
               DATE_FORMAT(eo.dat_signing_date, '%Y-%m-%d') AS 'signingDate',
               eo.vc_order_number                           AS 'orderNumber',
               eo.num_source                                AS 'orderSourceType',
               IF(eo.num_source = 0, 'CRM', '小程序')        AS 'orderSourceName',
               eo.num_contract_id                           AS 'contractId',
               ec2.vc_contract_number                       AS 'contractNumber',
               eo.num_status                                AS 'orderStatusType',
               IFNULL(eo.num_discount_amount, 0)            AS 'discountAmount',
               IFNULL(eo.num_total_price, 0)                AS 'totalPrice',
               IFNULL(eo.num_pay_price, 0)                  AS 'payPrice',
               IFNULL(eo.num_last_price, 0)                 AS 'lastPrice',
               IFNULL(SUM(esor.num_refund_price), 0)        AS 'refundPrice'
        FROM erp_orders eo
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIn erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN bd_clue bc ON bc.id = ec.num_clue_id
        LEFT JOIN erp_product_tax_dict eptd ON eptd.num_tax_id = ee.num_corporate_property_id
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        LEFT JOIN erp_contract ec2 ON ec2.id = eo.num_contract_id
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
        LEFT JOIN erp_service_order_refund esor ON esor.num_service_order_id = eso.id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        WHERE eo.id = #{orderId}
        GROUP BY eo.id ORDER BY NULL;
    </select>

    <!-- 获取客户信息，用于订单详情。 -->
    <select id="getClientForOrderDetailByOrderId" resultType="com.nnb.erp.domain.vo.ErpClientForOrderDetailVO">
        SELECT ec.id                                        AS 'clientId',
               ec.num_type                                  AS 'clientType',
               IFNULL(ee.vc_company_name, ep.vc_name)       AS 'clientName',
               IFNULL(ee.vc_history_name, '')               AS 'historyName',
               eo.num_clue_id                               AS 'clueId',
               bc.customer_id                              AS 'clueCustomerId',
               bc.vc_customer_name                         AS 'clueCustomerName',
               bc.vc_phone                                 AS 'clueCustomerPhone',
               ee.num_corporate_property_id                 AS 'corporatePropertyId',
               eptd.vc_tax_name                             AS 'corporatePropertyName',
               ec.num_city_id                               AS 'clientCityId',
               cdr.title                                    AS 'clientCityName',
               ec.contactName                               AS 'contactName',
               ec.contactPhone                              AS 'contactPhone',
               eo.commit_order_type                         AS 'commitOrderType',
               ee.id                                        AS 'numEnterpriseId'
        FROM erp_orders eo
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIn erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_product_tax_dict eptd ON eptd.num_tax_id = ee.num_corporate_property_id
        LEFT JOIN com_dict_region cdr ON cdr.id = ec.num_city_id
        WHERE eo.id = #{orderId};
    </select>

    <!-- 获取订单基本信息，用于订单详情。 -->
    <select id="getOrderForOrderDetailByOrderId" resultType="com.nnb.erp.domain.vo.ErpOrderForOrderDetailVO">
        SELECT eo.id                                        AS 'orderId',
               eo.num_client_id                             AS 'numClientId',
               eo.num_clue_id                               AS 'numClueId',
               eo.license_number                            AS 'licenseNumber',
               eo.num_user_id                               AS 'userId',
               eo.vc_phone                                  AS 'phone',
               eo.num_payment_term                          AS 'paymentTermType',
               IF(eo.num_payment_term = 1, '线上支付', '线下支付')  AS 'paymentTermName',
               su.nick_name                                 AS 'userName',
               DATE_FORMAT(eo.dat_signing_date, '%Y-%m-%d') AS 'signingDate',
               eo.vc_order_number                           AS 'orderNumber',
               eo.num_source                                AS 'orderSourceType',
               IF(eo.num_source = 0, 'CRM', '小程序')        AS 'orderSourceName',
               eo.num_contract_id                           AS 'contractId',
               ec2.vc_contract_number                       AS 'contractNumber',
               eo.num_status                                AS 'orderStatusType',
               eo.num_valid_status                          AS 'orderValidStatusType',
               eo.num_retainage_status                      AS 'orderRetainageStatusType',
               CASE eo.num_retainage_status WHEN 1 THEN '有尾款' WHEN 2 THEN '无尾款' END AS 'orderRetainageStatusName',
               eo.num_biz_status                            AS 'orderBizStatusType',
               eo.num_create_order_examine_status           AS 'orderCreateExamineStatusType',
               eo.num_modify_order_examine_status           AS 'orderModifyExamineStatusType',
               eo.num_cancel_order_examine_status           AS 'orderCancelExamineStatusType',
               eo.num_refund_examine_status                 AS 'orderRefundExamineStatusType',
               IFNULL(eo.num_discount_amount, 0)            AS 'discountAmount',
               IFNULL(eo.num_total_price, 0)                AS 'totalPrice',
               IFNULL(eo.num_pay_price, 0)                  AS 'payPrice',
               IFNULL(eo.num_last_price, 0)                 AS 'lastPrice',
               IFNULL(SUM(esor.num_refund_price), 0)        AS 'refundPrice',
               eo.vc_remark                                 AS 'remark',
               eo.contract_subject                          as onlineContracts,
               eo.ac_start                                  as acStart,
               eo.ac_end                                    as acEnd,
               eo.cipher_id                                 AS cipherId,
               eo.dat_finance_collection_time               as financeTime,
               eo.vc_online_contract_pdf                    as vcOnlineContractPdf,
               eo.is_electronic_contract                    as isElectronicContract,
               eo.commit_order_type                         as commitOrderType,
               eo.contract_subject                          as contractSubject,
               eo.is_return                                 as isReturn,
               su1.nick_name                                AS numCreatedBy,
               eo.ele_contact_url                           AS eleContactUrl,
               eo.xcx_url_link                              AS xcxUrlLink,
               eo.xcx_qr_code                               AS xcxQrCode,
               eo.check_the_contract_time                   AS checkTheContractTime,
               eo.is_electronic_contract_new                AS isElectronicContractNew,
               eo.num_valid_status                          AS numValidStatus,
               eo.dat_signing_datecreated_time              AS datSigningDatecreatedTime
        FROM erp_orders eo
        LEFT JOIN erp_contract ec2 ON ec2.id = eo.num_contract_id
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
        LEFT JOIN erp_service_order_refund esor ON esor.num_service_order_id = eso.id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_user su1 ON su1.user_id = eo.num_created_by
        WHERE eo.id = #{orderId}
        GROUP BY eo.id ORDER BY NULL;
    </select>

    <!-- 获取产品信息，用于订单详情。 -->
    <select id="getProductsForOrderDetail" resultType="com.nnb.erp.domain.vo.ErpProductForOrderDetailVO">
        SELECT eso.id                       AS 'serviceOrderId',
               eso.num_product_id           AS 'productId',
               eso.gt_kp                    AS 'gtKp',
               eso.kp_enterprise            AS 'kpEnterprise',
               esoi.vc_product_name         AS 'productName',
               ept.num_classification_id    AS 'classificationId',
               epd.num_service_id           AS 'serviceType',
--             esoi.vc_product_type         AS 'serviceName',
               scst.name                    AS 'serviceName',
--             eptr.num_tax_id              AS 'taxType',
               esoi.vc_corporate_property   AS 'taxName',
               esoi.vc_unit                 AS 'unit',
               eso.num_product_count        AS 'productCount',
               esoi.vc_area                 AS 'region',
               esoi.num_price               AS 'unitPrice',
               eso.num_coupon_id            AS 'couponId',
               ec.vc_name                   AS 'couponName',
               eso.num_combined_activity_id AS 'combinedId',
               eca.vc_name                  AS 'combinedName',
               eso.num_product_preferential AS 'productPreferential',
               eso.num_channel_fee          AS 'channelFee',
               eso.num_coupon_price         AS 'couponPrice',
               eso.num_total_price          AS 'totalPrice',
               eso.num_pay_price            AS 'payPrice',
               eso.num_last_price           AS 'lastPrice',
               eso.num_is_deprecated        AS 'isDeprecated',
               esd.vc_service_name          as 'vcServiceName',
               ed.commit_order_type         as commitOrderType,
               epn.num_name_id              as numNameId,
               epn.num_type_id              as typeId,
               epd.service_type_id          as serviceTypeId,
               epd.early_warning            AS 'earlyWarning',
               epd.early_warning_url        AS 'earlyWarningUrl',
               epd.finance_classification_id          as financeClassificationId,
               eso.license_number           as licenseNumber,
               eso.no_contract_selected_status  as noContractSelectedStatus,
               epd.service_type_id             as detailServiceTypeId,
               ssqe.service_main_id             as extensionServiceId,
               ssm.qualifications_extension_date             as qualificationsExtensionDate,
               ssm.annual_inspection_date             as annualInspectionDate,
               epd.qualifications_extension             as qualificationsExtension,
               epd.annual_inspection             as annualInspection,
               DATE_FORMAT(eso.ac_start, '%Y-%m-%d')  as acStart,
               DATE_FORMAT(eso.ac_end, '%Y-%m-%d')  as acEnd

        FROM erp_service_orders eso
        LEFT JOIN erp_service_orders_info esoi ON eso.id = esoi.num_service_orders
        LEFT JOIN erp_coupon ec ON ec.id = eso.num_coupon_id
        LEFT JOIN erp_combined_activity eca ON eca.id = eso.num_combined_activity_id
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN s_config_service_type scst on scst.id=epd.service_type_id
        LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id AND epn.num_is_use = 1
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        LEFT JOIN erp_product_service esd on  esd.num_service_id = epd.num_service_id
        LEFT JOIN s_service_qualifications_extension ssqe on (ssqe.service_order_id = eso.id and (ssqe.status = 1 || ssqe.status = 2))
        LEFT JOIN s_service_main ssm on ssm.id = ssqe.service_main_id
#         LEFT JOIN erp_product_tax_relation eptr ON eptr.num_product_id = epd.num_product_id
        Left join erp_orders  ed on ed.id =eso.num_order_id
        WHERE eso.num_order_id = #{orderId} and eso.num_status = 1
          /*AND eso.num_is_deprecated IN (0, 1);*/
    </select>

    <!-- 获取指定订单提交订单时的收款记录。 -->
    <resultMap id="PaymentMap" type="com.nnb.erp.domain.vo.ErpPaymentForOmListVO">
        <result property="paymentId" column="paymentId"/>
        <result property="payeeId" column="payeeId"/>
        <result property="numStatus" column="numStatus"/>
        <result property="payeeName" column="payeeName"/>
        <result property="collectionTime" column="collectionTime"/>
        <collection property="terms" ofType="com.nnb.erp.domain.vo.ErpPaymentTermForOmListVO">
            <result property="termId" column="termId"/>
            <result property="money" column="money"/>
            <result property="paymentType" column="paymentType"/>
            <result property="paymentUrl" column="paymentUrl"/>
        </collection>
    </resultMap>
    <select id="getCommitPaymentsByOrderId" resultMap="PaymentMap">
        SELECT eopt.id                                           AS 'paymentId',
               eopt.num_payee                                    AS 'payeeId',
               eopt.num_status                                    AS 'numStatus',
               t_payee.nick_name                                 AS 'payeeName',
               DATE_FORMAT(eopt.dat_collection_time, '%Y-%m-%d') AS 'collectionTime',
               eopti.id                                          AS 'termId',
               eopti.num_money                                   AS 'money',
               eopti.num_payment_type                            AS 'paymentType',
               eopti.vc_payment_url                              AS 'paymentUrl'
        FROM erp_order_payment_term eopt
        LEFT JOIN erp_order_payment_term_info eopti ON eopti.term_id = eopt.id
        LEFT JOIN sys_user t_payee ON t_payee.user_id = eopt.num_payee
        WHERE eopt.num_order_id = #{orderId}
    </select>

    <!-- 根据尾款标识查询回款&服务单详情。 -->
    <select id="getRetainageDetailByRetainageId"
            resultType="com.nnb.erp.domain.vo.ErpRetainageReturnDetailForOrderListVO">
        SELECT errd.id                      AS 'detailId',
               errd.num_retainage_return_id AS 'retainageId',
               errd.num_service_order_id    AS 'serviceOrderId',
               errd.num_discounts           AS 'discounts',
               errd.num_collection_price    AS 'collectionPrice'
        FROM erp_retainage_return_detail errd
        WHERE errd.num_retainage_return_id = #{retainageId}
    </select>

    <!-- 获取指定订单状态。 -->
    <select id="getOrderStatus" resultType="com.nnb.erp.domain.vo.ErpOrderStatusVO">
        SELECT num_status                      AS 'orderStatusType',
               num_valid_status                AS 'validStatusType',
               num_retainage_status            AS 'retainageStatusType',
               num_biz_status                  AS 'bizStatusType',
               num_create_order_examine_status AS 'createOrderExamineStatusType',
               num_modify_order_examine_status AS 'modifyOrderExamineStatusType',
               num_cancel_order_examine_status AS 'cancelOrderExamineStatusType',
               num_refund_examine_status       AS 'refundExamineStatusType',
               status                          AS status
        FROM erp_orders eo
        WHERE eo.id = #{orderId}
    </select>

    <!-- 修改订单支付状态、实收金额、尾款金额。 -->
    <update id="payOrderForMobile">
        UPDATE erp_orders
        SET num_payment_status = 1,
            num_pay_price = num_total_price,
            num_last_price = 0,
            dat_signing_dateupdated_time = NOW()
        WHERE id = #{orderId}
    </update>

    <!-- 获取指定订单内的所有服务订单标识。 -->
    <select id="getServiceOrderIdByOrderId" resultType="Long">
        SELECT id
        FROM erp_service_orders
        WHERE num_order_id = #{orderId}
    </select>

    <!-- 查询订单审核列表。 -->
    <select id="getOrderExamineList" resultType="com.nnb.erp.domain.vo.ErpOrderInfoForExamineVO">
        SELECT eo.id AS 'orderId',
        DATE_FORMAT(eo.create_arrive_finance, '%Y-%m-%d %H:%i:%s') AS 'createArriveFinance',
        DATE_FORMAT(eo.dat_signing_datecollect_money_time, '%Y-%m-%d %H:%i:%s') AS 'createApproveFinance',
        eo.vc_order_number AS 'orderNumber',
        eo.num_source AS 'orderSourceType',
        CASE eo.num_source WHEN 0 THEN 'CRM' WHEN 1 THEN '小程序' END AS 'orderSourceName',
        IFNULL(ee.vc_company_name, ep.vc_name) AS 'clientName',
        bc.vc_customer_name AS 'clueCustomerName',
        sd.dept_id AS 'deptId',
        sd.dept_name AS 'deptName',
        eo.num_user_id AS 'userId',
        su.nick_name AS 'userName',
        DATE_FORMAT(eo.dat_signing_date, '%Y-%m-%d') AS 'signingDate',
        eo.num_discount_amount AS 'discountAmount',
        eo.num_total_price AS 'totalPrice',
        eo.num_pay_price AS 'payPrice',
        eo.num_last_price AS 'lastPrice',
        DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y-%m-%d %H:%i:%s') AS 'createTime',
        eo.vc_remark AS 'remark',
        eo.ac_start as 'acStart',
        eo.ac_end as 'acEnd',
        eo.contact_num as vcContractNumber,
        eo.commit_order_type                   AS 'commitOrderType',
        eo.num_clue_id                         AS 'clueId',
        ec.contactName AS 'clientContactName',
        ena.id as 'newApprovalId',
        DATE_FORMAT(ena.create_time, '%Y-%m-%d %H:%i:%s') as approvalTime
        FROM erp_orders eo
        LEFT JOIN erp_new_approval ena ON ena.other_id = eo.id
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id and eso.num_is_deprecated = 0 and eso.num_status = 1
        LEFT JOIN erp_product_detail pd on eso.num_product_id = pd.num_product_id
        LEFT JOIN erp_product_name epn on epn.num_name_id = pd.num_name_id
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        LEFT JOIN erp_product_classification epc ON epc.num_classification_id = ept.num_classification_id
        left join erp_product_service eps on eps.num_service_id = pd.num_service_id
        left join erp_retainage_return err on err.num_order_id = eo.id
        <where>

            <if test="query.examineType eq 1">
                <if test="query.examineRole eq 1">
                    AND eo.num_create_order_examine_status IN (1)
                </if>
                <if test="query.examineRole eq 3">
                    AND eo.num_create_order_examine_status IN (9)
                </if>
                <if test="query.examineRole eq 2">
                    AND eo.num_create_order_examine_status IN (3)
                </if>
                AND(ena.type IS NULL OR  ena.type =  1 OR ena.type =  2)
            </if>
            <if test="query.examineType eq 2">
                <if test="query.examineRole eq 1">
                    AND eo.num_modify_order_examine_status IN (1)
                </if>
                <if test="query.examineRole eq 3">
                    AND eo.num_modify_order_examine_status IN (9)
                </if>
                <if test="query.examineRole eq 4">
                    AND eo.num_modify_order_examine_status IN (12)
                </if>
                <if test="query.examineRole eq 2">
                    AND eo.num_modify_order_examine_status IN (3)
                </if>
                AND(ena.type IS NULL OR  ena.type =  3)
            </if>
            <if test="query.examineType eq 3">
                <if test="query.examineRole eq 1">
                    AND eo.num_refund_examine_status IN (1)
                </if>
                <if test="query.examineRole eq 2">
                    AND eo.num_refund_examine_status IN (3)
                </if>
                <if test="query.examineRole eq 4">
                    AND eo.num_refund_examine_status IN (12)
                </if>
                AND(ena.type IS NULL OR  ena.type =  4)
            </if>
            <if test="query.examineType eq 4">
                <if test="query.examineRole eq 1">
                    AND eo.num_cancel_order_examine_status IN (1)
                </if>
                <if test="query.examineRole eq 2">
                    AND eo.num_cancel_order_examine_status IN (3)
                </if>
                <if test="query.examineRole eq 4">
                    AND eo.num_cancel_order_examine_status IN (12)
                </if>
                AND(ena.type IS NULL OR  ena.type =  5)
            </if>
            AND(ena.`status`IS NULL OR ena.`status` in (0, 1))
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                eo.vc_order_number = #{query.keyword}
                OR ec.contactName LIKE CONCAT('%', #{query.keyword}, '%')
                OR ee.vc_company_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR ep.vc_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR su.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
                )
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND (
                eps.vc_service_name LIKE CONCAT('%', #{query.productName}, '%')
                or epn.vc_product_name LIKE CONCAT('%', #{query.productName}, '%')
                )
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
            </if>
            <if test="query.deptId != null">
                AND sd.dept_id = #{query.deptId}
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size() > 0">
                AND sd.dept_id  in (
                <foreach collection="query.deptIdList" item="query.deptId" separator=",">
                    #{query.deptId}
                </foreach>
                )
            </if>
            <if test="query.isElectronicContract != null and query.isElectronicContract != ''">
                AND eo.is_electronic_contract = #{query.isElectronicContract}
            </if>
            <if test="query.collectionTimeBegin != null "> and err.num_status = 0 and err.num_type = 1 and err.dat_finance_collection_time <![CDATA[>=]]> #{query.collectionTimeBegin}</if>
            <if test="query.collectionTimeEnd != null ">   and err.num_status = 0 and err.num_type = 1 and err.dat_finance_collection_time <![CDATA[<=]]> #{query.collectionTimeEnd}</if>
            <if test="query.isElectronicContractNew != null and query.isElectronicContractNew != ''">
                AND IF(ISNULL(eo.is_electronic_contract_new), eo.is_electronic_contract = #{query.isElectronicContractNew}, eo.is_electronic_contract_new = #{query.isElectronicContractNew})
            </if>
            <!-- 数据范围过滤 -->
            ${query.params.dataScope}
            GROUP BY eo.id
        </where>
    </select>

    <!-- 更新指定订单的实收金额、尾款金额，用于退款。 -->
    <update id="updatePayPriceForRefund">
        UPDATE erp_orders
        SET num_pay_price = num_pay_price - #{refundPrice},
            num_last_price = num_last_price + #{refundPrice},
            num_updated_by = #{userId},
            dat_signing_dateupdated_time = NOW()
        WHERE id = #{orderId}
    </update>

    <!-- 维护订单信息，通过查询统计各个关联表来维护订单金额。 -->
    <update id="maintainOrderInfo">
        UPDATE
            erp_orders eo,
            (SELECT IFNULL(SUM(num_total_price), 0)  AS 'sumTotal',
                    IFNULL(SUM(num_pay_price), 0)    AS 'sumPay',
                    IFNULL(SUM(num_last_price), 0)   AS 'sumLast',
                    IFNULL(SUM(num_coupon_price), 0) AS 'sumCoupon',
                    IFNULL(SUM(num_points), 0)       AS 'sumPoints'
             FROM erp_service_orders eso
             WHERE num_order_id = #{orderId} AND num_is_deprecated IN (0, 2, 4, 5, 6) and num_status = 1
             ) t1,
             (SELECT IFNULL(SUM(errd.num_discounts), 0) AS 'sumDiscounts'
              FROM erp_retainage_return err
                       LEFT JOIN erp_retainage_return_detail errd ON errd.num_retainage_return_id = err.id
                       LEFT JOIN erp_service_orders eso ON eso.id = errd.num_service_order_id
              WHERE err.num_order_id = #{orderId} AND err.num_status = 1 AND eso.num_is_deprecated IN (0, 2, 4, 5, 6) and eso.num_status = 1
              ) t2
        SET eo.num_total_price = t1.sumTotal,
            eo.num_pay_price = t1.sumPay,
            eo.num_last_price = t1.sumLast,
            eo.num_discount_amount = t1.sumCoupon + t2.sumDiscounts,
            eo.num_points = t1.sumPoints
        WHERE
            eo.id = #{orderId}
    </update>

    <!-- 获取指定订单内的产品类型。 -->
    <select id="getServiceOrderTypeByOrderId" resultType="Long">
        SELECT epn.num_type_id
        FROM erp_service_orders eso
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id
        WHERE eso.num_order_id = #{orderId} AND eso.num_is_deprecated = 0
    </select>

    <!-- 获取指定订单的电子合同主体。 -->
    <select id="getContractSubject" resultType="Integer">
        SELECT contract_subject FROM erp_orders WHERE id = #{orderId};
    </select>
    <select id="getOnlieContractByOrderId" resultType="com.nnb.erp.domain.vo.ErpContractVvo">
        select id as 'contractId',showNumber as 'contractNumber' from tb_online_contract where orderId= #{orderId} and status=1;
    </select>

    <select id="getContractByOrderId" resultType="com.nnb.erp.domain.vo.ErpContractVvo">
    select id as 'contractId',vc_contract_number as 'contractNumber' from  erp_contract where orderId= #{orderId};
    </select>
    <select id="getOnlieContractByOrderIdList" resultType="com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO">
        select id as 'contractId',showNumber as 'vcContractNumber',orderId as 'orderId' from tb_online_contract
        <where>
            <if test="orderIdList != null and orderIdList.size > 0">
                orderId in
                <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
            <if test="vcContractNumber !=null and vcContractNumber !=''">and showNumber  LIKE CONCAT('%',#{vcContractNumber},'%')  </if>
            and status=1
        </where>
    </select>
    <select id="getServiceApprovalList" resultType="com.nnb.erp.domain.vo.ErpOrderInfoForExamineVO">
        SELECT eo.id                                                      AS 'orderId',
        eo.vc_order_number                                                AS 'orderNumber',
        eo.num_source                                                     AS 'orderSourceType',
        CASE eo.num_source WHEN 0 THEN 'CRM' WHEN 1 THEN '小程序' END      AS 'orderSourceName',
        IFNULL(ee.vc_company_name, ep.vc_name)                            AS 'clientName',
        ec.contactName                                                    AS 'clueCustomerName',
        sd.dept_id                                                        AS 'deptId',
        sd.dept_name                                                      AS 'deptName',
        eo.num_user_id                                                    AS 'userId',
        su.nick_name                                                      AS 'userName',
        DATE_FORMAT(eo.dat_signing_date, '%Y-%m-%d')                      AS 'signingDate',
        eo.num_discount_amount                                            AS 'discountAmount',
        eo.num_total_price                                                AS 'totalPrice',
        eo.num_pay_price                                                  AS 'payPrice',
        eo.num_last_price                                                 AS 'lastPrice',
        DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y-%m-%d %H:%i:%s') AS 'createTime',
        eo.vc_remark                                                      AS 'remark',
        eo.ac_start                                                       as 'acStart',
        eo.ac_end                                                         as 'acEnd',
        eo.contact_num                                                    as vcContractNumber,
        <if test="query.examineRole eq 4">
            espa.id                                                       as approvalId,
        </if>
        ena.id                                                        as 'newApprovalId'
        FROM erp_orders eo
        LEFT JOIN erp_new_approval ena on ena.other_id = eo.id
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
        <if test="query.examineRole eq 4">
            LEFT JOIN erp_service_person_approval espa on eo.id = espa.order_id
            LEFT JOIN sys_user asu on asu.user_id = espa.user_id
            LEFT JOIN sys_dept asd ON asd.dept_id = asu.dept_id
        </if>
        <where>
            espa.status = 0
        <if test="query.examineType eq 1">
            <if test="query.examineRole eq 1">
                AND eo.num_create_order_examine_status IN (1)
            </if>
            <if test="query.examineRole eq 3">
                AND eo.num_create_order_examine_status IN (9)
            </if>
            <if test="query.examineRole eq 2">
                AND eo.num_create_order_examine_status IN (3)
            </if>
        </if>
        <if test="query.examineType eq 2">
            <if test="query.examineRole eq 1">
                AND eo.num_modify_order_examine_status IN (1)
            </if>
            <if test="query.examineRole eq 3">
                AND eo.num_modify_order_examine_status IN (9)
            </if>
            <if test="query.examineRole eq 4">
                AND eo.num_modify_order_examine_status IN (12)
                AND espa.type = 1
                AND espa.status = 0
            </if>
            <if test="query.examineRole eq 2">
                AND eo.num_modify_order_examine_status IN (3)
            </if>
            AND(ena.type IS NULL OR  ena.type =  3)
        </if>
        <if test="query.examineType eq 3">
            <if test="query.examineRole eq 1">
                AND eo.num_refund_examine_status IN (1)
            </if>
            <if test="query.examineRole eq 2">
                AND eo.num_refund_examine_status IN (3)
            </if>
            <if test="query.examineRole eq 4">
                AND eo.num_refund_examine_status IN (12)
                AND espa.type = 3
                AND espa.status = 0
            </if>
            AND(ena.type IS NULL OR  ena.type =  4)
        </if>
        <if test="query.examineType eq 4">
            <if test="query.examineRole eq 1">
                AND eo.num_cancel_order_examine_status IN (1)
            </if>
            <if test="query.examineRole eq 2">
                AND eo.num_cancel_order_examine_status IN (3)
            </if>
            <if test="query.examineRole eq 4">
                AND eo.num_cancel_order_examine_status IN (12)
                AND espa.type = 2
                AND espa.status = 0
            </if>
            AND(ena.type IS NULL OR  ena.type =  5)
        </if>
            AND(ena.`status`IS NULL OR ena.`status` in (0, 1))
            <if test="query.keyword != null and query.keyword != ''">
            AND (
            eo.vc_order_number = #{query.keyword}
            OR ec.contactName   LIKE CONCAT('%', #{query.keyword}, '%')
            OR ee.vc_company_name LIKE CONCAT('%', #{query.keyword}, '%')
            OR ep.vc_name LIKE CONCAT('%', #{query.keyword}, '%')
            OR su.nick_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        <if test="query.createTimeBegin != null">
            AND DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin}, '%Y%m%d')
        </if>
        <if test="query.createTimeEnd != null">
            AND DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd}, '%Y%m%d')
        </if>
        </where>
        ${query.params.dataScope}
    </select>
    <select id="selectPaymentInfoList" resultType="com.nnb.erp.domain.vo.PaymentInfoVo">
        select  eopr.id,
                eopr.billNo,
                eopr.orderId,
                eopr.userId,
                eopr.clueId,
                eopr.fee,
                epc.name as payCompanyId,
                eopr.payUrl,
                eopr.payImg,
                eopr.payValidTime,
                eopr.createdTime,
                eopr.payStatus,
                eopr.payTime,
                eopr.tradeId,
                eopr.qrCodeId,
                eopr.clientId,
                eopr.remark,
                su.nick_name as userName
        from erp_order_pay_record eopr
        left join erp_pay_company epc on eopr.payCompanyId = epc.id
        left join sys_user su on eopr.userId = su.user_id
        <where>
            <if test="paymentInfo.id != null">
                and id = #{paymentInfo.id}
            </if>
            <if test="paymentInfo.billNo != null and paymentInfo.billNo != ''">
                and billNo = #{paymentInfo.billNo}
            </if>
            <if test="paymentInfo.orderId != null and paymentInfo.orderId != ''">
                and orderId = #{paymentInfo.orderId}
            </if>
            <if test="paymentInfo.userId != null">
                and userId = #{paymentInfo.userId}
            </if>
            <if test="paymentInfo.clueId != null">
                and clueId = #{paymentInfo.clueId}
            </if>
            <if test="paymentInfo.fee != null">
                and fee = #{paymentInfo.fee}
            </if>
            <if test="paymentInfo.payCompanyId != null">
                and payCompanyId = #{paymentInfo.payCompanyId}
            </if>
            <if test="paymentInfo.payUrl != null and paymentInfo.payUrl != ''">
                and payUrl = #{paymentInfo.payUrl}
            </if>
            <if test="paymentInfo.payImg != null and paymentInfo.payImg != ''">
                and payImg = #{paymentInfo.payImg}
            </if>
            <if test="paymentInfo.payValidTime != null">
                and payValidTime = #{paymentInfo.payValidTime}
            </if>
            <if test="paymentInfo.createdTime != null">
                and createdTime = #{paymentInfo.createdTime}
            </if>
            <if test="paymentInfo.payStatus != null">
                and payStatus = #{paymentInfo.payStatus}
            </if>
            <if test="paymentInfo.payTime != null">
                and payTime = #{paymentInfo.payTime}
            </if>
            <if test="paymentInfo.tradeId != null and paymentInfo.tradeId != ''">
                and tradeId = #{paymentInfo.tradeId}
            </if>
            <if test="paymentInfo.qrCodeId != null and paymentInfo.qrCodeId != ''">
                and qrCodeId = #{paymentInfo.qrCodeId}
            </if>
            <if test="paymentInfo.clientId != null and paymentInfo.clientId != ''">
                and clientId = #{paymentInfo.clientId}
            </if>
            <if test="paymentInfo.qrCodeId != null and paymentInfo.qrCodeId != ''">
                and remark = #{paymentInfo.remark}
            </if>
        </where>
    </select>
    <select id="selectSumServiceOrdersPrice" resultType="java.util.Map">
        SELECT IFNULL(SUM(num_total_price), 0)  AS 'sumTotal',
                    IFNULL(SUM(num_pay_price), 0)    AS 'sumPay',
                    IFNULL(SUM(num_last_price), 0)   AS 'sumLast',
                    IFNULL(SUM(num_coupon_price), 0) AS 'sumCoupon',
                    IFNULL(SUM(num_points), 0)       AS 'sumPoints'
             FROM erp_service_orders eso
             WHERE num_order_id = #{orderId}
              AND num_is_deprecated IN (0, 2, 4, 5, 6)
    </select>
    <select id="selectRetainageReturnPrice" resultType="java.util.Map">
        SELECT IFNULL(SUM(errd.num_discounts), 0) AS 'sumDiscounts'
            FROM erp_retainage_return err
                     LEFT JOIN erp_retainage_return_detail errd ON errd.num_retainage_return_id = err.id
                     LEFT JOIN erp_service_orders eso ON eso.id = errd.num_service_order_id
            WHERE err.num_order_id = #{orderId}
             AND err.num_status = 1 AND eso.num_is_deprecated IN (0, 2, 4, 5, 6)
    </select>
    <update id="updateErpOrdersByCipherId">
        update erp_orders
        set ele_contact_url = #{eleContactUrl}
        where cipher_id = #{cipherId}
    </update>
    <update id="updateXcxMsgByCipherId">
        update erp_orders
        set xcx_url_link = #{urlLink}, xcx_qr_code = #{qrCode}
        where cipher_id = #{cipherId}
    </update>

    <select id="getSalesListingCount" resultType="java.lang.Long">
        SELECT 	sum(id)

        from
        (
        (SELECT
        count(eo.id) AS id
        from erp_orders eo
        -- left join erp_order_payment_term eopt on eopt.num_order_id = eo.id
        -- left join erp_order_payment_term_info eopti on eopt.id = eopti.term_id
        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id
        left join s_service_main ssm on eo.id = ssm.order_id and ssm.product_id = eso.num_product_id
        left join sys_user su1 on su1.user_id = eo.num_user_id
        left join sys_dept sd1 on sd1.dept_id = su1.dept_id
        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id

        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and errd.num_status = 1
        and errd.payType = 1
        and eso.num_status != 0
        <if test="datSigningDateStart != null and datSigningDateEnd != null">
            and eo.dat_signing_date &gt;= #{datSigningDateStart}
            and eo.dat_signing_date &lt;= #{datSigningDateEnd}
        </if>
        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and eo.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and eo.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>
        <!--<if test="datCollectionTimeStart != null and datCollectionTimeEnd !=null">
            and eopt.dat_collection_time &gt;= #{datCollectionTimeEnd}
            and eopt.dat_collection_time &lt;= #{datCollectionTimeEnd}
        </if>-->
        <if test="deptIds != null and deptIds.size > 0">
            and sd1.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="orderNum != null and orderNum != ''">
            and eo.vc_order_number = #{orderNum}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and ee.vc_company_name like concat('%',
            #{enterpriseName}, '%')
        </if>
        ${params.dataScope}
        )
        union all
        (
        SELECT
        count(eo.id) AS id
        from erp_orders eo
        -- left join erp_order_payment_term eopt on eopt.num_order_id = eo.id
        -- left join erp_order_payment_term_info eopti on eopt.id = eopti.term_id
        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id
        left join s_service_main ssm on eo.id = ssm.order_id and ssm.product_id = eso.num_product_id
        left join sys_user su1 on su1.user_id = eo.num_user_id
        left join sys_dept sd1 on sd1.dept_id = su1.dept_id
        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id


        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and errd.num_status = 1
        AND (err.num_status = 1 or err.num_status is null)
        and errd.payType = 2
        and eso.num_status != 0
        <if test="datSigningDateStart != null and datSigningDateEnd != null">
            and eo.dat_signing_date &gt;= #{datSigningDateStart}
            and eo.dat_signing_date &lt;= #{datSigningDateEnd}
        </if>
        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and err.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and err.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>
        <!--<if test="datCollectionTimeStart != null and datCollectionTimeEnd !=null">
            and eopt.dat_collection_time &gt;= #{datCollectionTimeEnd}
            and eopt.dat_collection_time &lt;= #{datCollectionTimeEnd}
        </if>-->
        <if test="deptIds != null and deptIds.size > 0">
            and sd1.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="orderNum != null and orderNum != ''">
            and eo.vc_order_number = #{orderNum}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and ee.vc_company_name like concat('%',
            #{enterpriseName}, '%')
        </if>
        ${params.dataScope}
        )
        )
        as temp

    </select>

    <select id="getSalesListing" resultType="com.nnb.erp.domain.vo.report.SalesListingVo">
        (SELECT
        eo.id as orderId,
        eo.is_electronic_contract as isElectronicContract,
        eo.contact_num as contractNumber,
        eo.vc_order_number as vcOrderNumber,
        ee.vc_company_name as enterpriseName,
        ec.contactName as enterpriseContactName,
        errd.num_collection_price as numCollectionPrice,
        ( CASE WHEN eo.num_status = 1 THEN '待审核' WHEN eo.num_status = 2 THEN '待服务' WHEN eo.num_status = 3 THEN
        '服务中' WHEN eo.num_status= 4 THEN '已完成' END ) as '订单状态',
        ( CASE WHEN eo.num_create_order_examine_status = 5 THEN '财务审核通过' WHEN eo.num_create_order_examine_status =
        4 THEN '财务审核驳回' WHEN eo.num_create_order_examine_status = 3 THEN '财务待审核' WHEN
        eo.num_create_order_examine_status= 2 THEN '经理审核驳回' WHEN eo.num_create_order_examine_status= 1 THEN
        '待经理审核' WHEN eo.num_create_order_examine_status= 0 THEN '未参与' WHEN eo.num_create_order_examine_status=
        10 THEN '待客户签约' WHEN eo.num_create_order_examine_status= 11 THEN '客户驳回' END ) as '提单审核状态',
        ( CASE WHEN eo.num_valid_status = 0 THEN '有效' WHEN eo.num_valid_status = 1 THEN '已撤销' WHEN
        eo.num_valid_status = 2 THEN '已作废' WHEN eo.num_valid_status= 3 THEN '已退款' END ) as '作废退款状态',
        (CASE WHEN eo.num_valid_status = 3 THEN '是' ELSE '否' END) as isRefund,
        ssr.created_time as sServiceMainCreateTime,
        cdr.title as city,
        eo.dat_signing_date as datSigningDate,
        su1.nick_name as signer,
        sd1.dept_name as signDeptName,
        sd1.dept_id as deptId,
        esoi.vc_corporate_property as vcCorporateProperty,
        esoi.vc_area as vcArea,
        epc.vc_classification_name as vcClassificationName,
        epn.vc_product_name as vcProductName,
        eso.num_product_count as numProductCount,
        ept.vc_type_name as '产品类型',
        eps.vc_service_name as vcServiceName,
        CASE
        WHEN edc.num_type = 1 THEN '优惠卷'
        WHEN edc.num_type = 2 AND edc.discount_amount = 600 THEN '预存399抵600'
        WHEN edc.num_type = 2 AND edc.discount_amount = 900 THEN '预存618抵900'
        WHEN edc.num_type = 2 AND edc.discount_amount = 1000 THEN '预存799记账费抵1000'
        WHEN edc.num_type = 2 AND edc.discount_amount = 1500 THEN '预存1111记账费抵1500'
        WHEN edc.num_type = 2 AND edc.discount_amount = 1200 THEN '预存818抵1200'
        ELSE '预存抵扣券'
        END
        as discountType,
        edc.discount_amount as discountAmount,
        eso.num_total_price as productNumTotalPrice,
        eso.num_pay_price as productPayPrice,
        eso.num_last_price as productNumLastPrice,
        CASE
        WHEN eso.num_is_deprecated = 0 THEN '正常'
        WHEN eso.num_is_deprecated = 1 THEN '退费'
        WHEN eso.num_is_deprecated = 2 THEN '退费待审核'
        WHEN eso.num_is_deprecated = 3 THEN '作废'
        WHEN eso.num_is_deprecated = 4 THEN '作废待审核'
        WHEN eso.num_is_deprecated = 5 THEN '部分退费'
        WHEN eso.num_is_deprecated = 5 THEN '部分退费待审核'
        ELSE ''
        END
        as productStatus,
        eo.num_total_price as orderNumTotalPrice,
        null as lastPriceNumStatus,
        eo.num_last_price as numLastPrice,
        eo.dat_finance_collection_time as datFinanceCollectionTime,
        bgs.vc_name as guestVcName,
        ebsat.name as addressType,
        eo.dat_signing_datecreated_time as orderCreateTime,
        ssm.ac_start as acStart,
        ssm.ac_end as acEnd,
        eo.vc_remark as payeeRemark,
        ssm.to_increment_time as toIncrementTime,
        CASE eo.sign
        WHEN 1 THEN
        '首单'
        ELSE
        ''
        END
        as firstOrder,
        1 as type,
        err.id as numRetainageId,
        ssm.id as serviceMainId,
        epd.num_product_id as numProductId,
        eso.num_coupon_price as numCouponPrice,
        eo.num_total_price + eo.num_discount_amount as orderTotalPrice,
        case ssm.service_status
        when 4 then '搁置'
        end as shelveStatus,
        scst.name as serviceTypeName,
        ssm.user_id as serviceUserId,
        ssm.service_point as servicePoint,
        ssm.service_point_status as servicePointStatus,
        eo.license_number as licenseNumber


        from erp_orders eo
        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id
        left join s_service_main ssm on eo.id = ssm.order_id and ssm.product_id = eso.num_product_id
        left join (
        select max(created_time) as created_time,
        service_id
        from s_service_record
        ) ssr on ssr.service_id = ssm.id
        left join sys_user su1 on su1.user_id = eo.num_user_id
        left join sys_dept sd1 on sd1.dept_id = su1.dept_id
        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id
        LEFT JOIN com_dict_region as cdr on cdr.id = ec.num_city_id
        LEFT JOIN erp_product_detail epd on epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_service eps on eps.num_service_id=epd.num_service_id
        LEFT JOIN erp_product_name epn on epn.num_name_id = epd.num_name_id
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        LEFT JOIN erp_product_classification epc ON epc.num_classification_id = ept.num_classification_id
        left join erp_service_orders_info esoi on eso.id = esoi.num_service_orders
        LEFT JOIN erp_discount_coupon edc on eso.num_coupon_id = edc.id
        left join bd_clue bc on eo.num_clue_id = bc.id
        left join bd_guest_srcs bgs on bgs.id = bc.guest_srcs_id
        left join erp_biz_service_address_type ebsat on ebsat.id = epd.address_type
        left join s_config_service_type scst on scst.id = epd.service_type_id

        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and errd.num_status = 1
        and errd.payType = 1
        and eso.num_status != 0
        <if test="datSigningDateStart != null and datSigningDateEnd != null">
            and eo.dat_signing_date &gt;= #{datSigningDateStart}
            and eo.dat_signing_date &lt;= #{datSigningDateEnd}
        </if>
        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and eo.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and eo.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>
        <!--<if test="datCollectionTimeStart != null and datCollectionTimeEnd !=null">
            and eopt.dat_collection_time &gt;= #{datCollectionTimeEnd}
            and eopt.dat_collection_time &lt;= #{datCollectionTimeEnd}
        </if>-->
        <if test="deptIds != null and deptIds.size > 0">
            and sd1.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="orderNum != null and orderNum != ''">
            and eo.vc_order_number = #{orderNum}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and ee.vc_company_name like concat('%',
            #{enterpriseName}, '%')
        </if>
        ${params.dataScope}
        )
        union all
        (
        SELECT
        eo.id as orderId,
        eo.is_electronic_contract as isElectronicContract,
        eo.contact_num as contractNumber,
        eo.vc_order_number as 'vcOrderNumber',
        ee.vc_company_name as enterpriseName,
        ec.contactName as enterpriseContactName,

        errd.num_collection_price as numCollectionPrice,
        ( CASE WHEN eo.num_status = 1 THEN '待审核' WHEN eo.num_status = 2 THEN '待服务' WHEN eo.num_status = 3 THEN
        '服务中' WHEN eo.num_status= 4 THEN '已完成' END ) as '订单状态',
        ( CASE WHEN eo.num_create_order_examine_status = 5 THEN '财务审核通过' WHEN eo.num_create_order_examine_status =
        4 THEN '财务审核驳回' WHEN eo.num_create_order_examine_status = 3 THEN '财务待审核' WHEN
        eo.num_create_order_examine_status= 2 THEN '经理审核驳回' WHEN eo.num_create_order_examine_status= 1 THEN
        '待经理审核' WHEN eo.num_create_order_examine_status= 0 THEN '未参与' WHEN eo.num_create_order_examine_status=
        10 THEN '待客户签约' WHEN eo.num_create_order_examine_status= 11 THEN '客户驳回' END ) as '提单审核状态',
        ( CASE WHEN eo.num_valid_status = 0 THEN '有效' WHEN eo.num_valid_status = 1 THEN '已撤销' WHEN
        eo.num_valid_status = 2 THEN '已作废' WHEN eo.num_valid_status= 3 THEN '已退款' END ) as '作废退款状态',
        (CASE WHEN eo.num_valid_status = 3 THEN '是' ELSE '否' END) as isRefund,
        ssr.created_time as sServiceMainCreateTime,
        cdr.title as city,
        eo.dat_signing_date as datSigningDate,
        su1.nick_name as signer,
        sd1.dept_name as signDeptName,
        sd1.dept_id as deptId,
        esoi.vc_corporate_property as vcCorporateProperty,
        esoi.vc_area as vcArea,
        epc.vc_classification_name as vcClassificationName,
        epn.vc_product_name as vcProductName,
        eso.num_product_count as numProductCount,
        ept.vc_type_name as '产品类型',
        eps.vc_service_name as vcServiceName,
        CASE
        WHEN edc.num_type = 1 THEN '优惠卷'
        WHEN edc.num_type = 2 AND edc.discount_amount = 600 THEN '预存399抵600'
        WHEN edc.num_type = 2 AND edc.discount_amount = 900 THEN '预存618抵900'
        WHEN edc.num_type = 2 AND edc.discount_amount = 1000 THEN '预存799记账费抵1000'
        WHEN edc.num_type = 2 AND edc.discount_amount = 1500 THEN '预存1111记账费抵1500'
        ELSE ''
        END
        as discountType,
        edc.discount_amount as discountAmount,
        eso.num_total_price as productNumTotalPrice,
        eso.num_pay_price as productPayPrice,
        eso.num_last_price as productNumLastPrice,
        CASE
        WHEN eso.num_is_deprecated = 0 THEN '正常'
        WHEN eso.num_is_deprecated = 1 THEN '退费'
        WHEN eso.num_is_deprecated = 2 THEN '退费待审核'
        WHEN eso.num_is_deprecated = 3 THEN '作废'
        WHEN eso.num_is_deprecated = 4 THEN '作废待审核'
        WHEN eso.num_is_deprecated = 5 THEN '部分退费'
        WHEN eso.num_is_deprecated = 5 THEN '部分退费待审核'
        ELSE ''
        END
        as productStatus,
        eo.num_total_price as orderNumTotalPrice,
        CASE
        WHEN err.num_status = 0 THEN '待审核'
        WHEN err.num_status = 1 THEN '审核通过'
        WHEN err.num_status = 2 THEN '驳回'
        WHEN err.num_status = 3 THEN '撤消'
        WHEN err.num_status = 4 THEN '删除'
        WHEN err.num_status = 5 THEN '作废'
        ELSE ''
        END
        as lastPriceNumStatus,
        eo.num_last_price as numLastPrice,
        err.dat_finance_collection_time as datFinanceCollectionTime,
        bgs.vc_name as guestVcName,
        ebsat.name as addressType,
        eo.dat_signing_datecreated_time as orderCreateTime,
        ssm.ac_start as acStart,
        ssm.ac_end as acEnd,
        "无" as payeeRemark,
        ssm.to_increment_time as toIncrementTime,
        CASE eo.sign
        WHEN 1 THEN
        '首单'
        ELSE
        ''
        END
        as firstOrder,
        2 as type,
        err.id as numRetainageId,
        ssm.id as serviceMainId,
        epd.num_product_id as numProductId,
        eso.num_coupon_price as numCouponPrice,
        eo.num_total_price + eo.num_discount_amount as orderTotalPrice,
        case ssm.service_status
        when 4 then '搁置'
        end as shelveStatus,
        scst.name as serviceTypeName,
        ssm.user_id as serviceUserId,
        ssm.service_point as servicePoint,
        ssm.service_point_status as servicePointStatus,
        eo.license_number as licenseNumber





        from erp_orders eo

        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id
        left join s_service_main ssm on eo.id = ssm.order_id and ssm.product_id = eso.num_product_id
        left join (
        select max(created_time) as created_time,
        service_id
        from s_service_record
        ) ssr on ssr.service_id = ssm.id

        left join sys_user su1 on su1.user_id = eo.num_user_id
        left join sys_dept sd1 on sd1.dept_id = su1.dept_id
        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id
        LEFT JOIN com_dict_region as cdr on cdr.id = ec.num_city_id
        LEFT JOIN erp_product_detail epd on epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_service eps on eps.num_service_id=epd.num_service_id
        LEFT JOIN erp_product_name epn on epn.num_name_id = epd.num_name_id
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        LEFT JOIN erp_product_classification epc ON epc.num_classification_id = ept.num_classification_id
        left join erp_service_orders_info esoi on eso.id = esoi.num_service_orders
        LEFT JOIN erp_discount_coupon edc on eso.num_coupon_id = edc.id
        left join bd_clue bc on eo.num_clue_id = bc.id
        left join bd_guest_srcs bgs on bgs.id = bc.guest_srcs_id
        left join erp_biz_service_address_type ebsat on ebsat.id = epd.address_type
        left join s_config_service_type scst on scst.id = epd.service_type_id

        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and errd.num_status = 1
        AND (err.num_status = 1 or err.num_status is null)
        and errd.payType = 2
        and eso.num_status != 0
        <if test="datSigningDateStart != null and datSigningDateEnd != null">
            and eo.dat_signing_date &gt;= #{datSigningDateStart}
            and eo.dat_signing_date &lt;= #{datSigningDateEnd}
        </if>
        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and err.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and err.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>
        <!--<if test="datCollectionTimeStart != null and datCollectionTimeEnd !=null">
            and eopt.dat_collection_time &gt;= #{datCollectionTimeEnd}
            and eopt.dat_collection_time &lt;= #{datCollectionTimeEnd}
        </if>-->
        <if test="deptIds != null and deptIds.size > 0">
            and sd1.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="orderNum != null and orderNum != ''">
            and eo.vc_order_number = #{orderNum}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and ee.vc_company_name like concat('%',
            #{enterpriseName}, '%')
        </if>
        ${params.dataScope}
        )
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum}, #{pageSize}
        </if>

    </select>
    <select id="getRetainageByOrderIds" resultType="com.nnb.erp.domain.vo.ErpRetainageForOmListVO">
        SELECT err.id                                                    AS 'retainageId',
                err.num_discounts                                         AS 'discounts',
                eopti.num_money                                            AS 'collectionPrice',
                err.num_created_by                                        AS 'createId',
                t_create_user.nick_name                                   AS 'createName',
                DATE_FORMAT(err.dat_signing_datecreated_time, '%Y-%m-%d') AS 'createTime',
                DATE_FORMAT(err.dat_finance_collection_time, '%Y-%m-%d')  AS 'financeCollectionTime',
                err.num_status                                            AS 'auditStatusType',
                err.num_updated_by                                        AS 'auditUser',
                t_audit_user.nick_name                                    AS 'auditName',
                DATE_FORMAT(err.dat_signing_dateupdated_time, '%Y-%m-%d %H:%i:%s') AS 'auditTime',
                eopt.id                                                   AS 'paymentId',
                eopt.num_payee                                            AS 'payeeId',
                t_payee.nick_name                                         AS 'payeeName',
                DATE_FORMAT(eopt.dat_collection_time, '%Y-%m-%d')         AS 'collectionTime',
                eopti.id                                                  AS 'termId',
                eopti.num_money                                           AS 'money',
                eopti.num_payment_type                                    AS 'paymentType',
                eopti.vc_payment_url                                      AS 'paymentUrl',
                err.num_order_id                                          as 'numOrderId'
        FROM erp_retainage_return err
                 LEFT JOIN erp_orders eo ON eo.id = err.num_order_id
                 LEFT JOIN erp_order_payment_term eopt ON eopt.num_retainage_id = err.id
                 LEFT JOIN erp_order_payment_term_info eopti ON eopti.term_id = eopt.id
                 LEFT JOIN sys_user t_create_user ON t_create_user.user_id = err.num_created_by
                 LEFT JOIN sys_user t_payee ON t_payee.user_id = eopt.num_payee
                 LEFT JOIN sys_user t_audit_user ON t_audit_user.user_id = err.num_updated_by
        WHERE     err.num_order_id in
        <foreach item="orderId" collection="orderList" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
    <select id="getOrderStatuList" resultType="com.nnb.erp.domain.vo.ErpOrderStatusVO">
        SELECT num_status                      AS 'orderStatusType',
                num_valid_status                AS 'validStatusType',
                num_retainage_status            AS 'retainageStatusType',
                num_biz_status                  AS 'bizStatusType',
                num_create_order_examine_status AS 'createOrderExamineStatusType',
                num_modify_order_examine_status AS 'modifyOrderExamineStatusType',
                num_cancel_order_examine_status AS 'cancelOrderExamineStatusType',
                num_refund_examine_status       AS 'refundExamineStatusType',
                status                          AS status,
                id                              as orderId
        FROM erp_orders eo
        WHERE eo.id in
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
    <select id="getOrderListForOmCount" resultType="com.nnb.erp.domain.vo.ErpOrderStatusCountVO">
        select
        temp1.`auditing`,
        temp2.`editAuditing`,
        temp3.`refundAuditing`,
        temp4.`deprecatedAuditing`,
        temp5.`auditPass`,
        temp6.`paying`,
        temp7.`payed`,
        temp8.`canceled`,
        temp9.`rejected`,
        temp10.`refund`,
        temp11.`deprecated`,
        temp12.`customerSignNot`,
        temp13.`customerRejected`
        from
        (
        select COUNT(eo.id) as `auditing`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        <if test="query.numNameIdList!=null and query.numNameIdList.size > 0">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"
            AND (
            eo.
            num_create_order_examine_status IN (1, 3)
            )

            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.numNameIdList!=null and query.numNameIdList.size > 0">
                and name.num_name_id in
                <foreach collection="query.numNameIdList" item="numNameId" open="(" separator="," close=")">
                    #{numNameId}
                </foreach>
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null ">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>

        </where> ) temp1
        left join (
        SELECT
        COUNT(eo.id) as `editAuditing`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1=1

            AND (
            eo.num_modify_order_examine_status IN (1, 3)
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>


            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp2 on temp1.id=temp2.id
        left join (
        select COUNT(eo.id) as `refundAuditing`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_refund_examine_status =3 or eo.num_valid_status = 5 or eo.num_valid_status = 6
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp3 on temp3.id=temp1.id
        left join(
        select COUNT(eo.id) as `deprecatedAuditing`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_cancel_order_examine_status IN (3)
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp4 on temp4.id=temp1.id
        left join(
        select COUNT(eo.id) as `auditPass`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            and (eo.num_create_order_examine_status = 5
            or eo.num_modify_order_examine_status = 5
            or eo.num_refund_examine_status =5
            or eo.num_cancel_order_examine_status = 5
            and eo.num_valid_status != 2
            and eo.num_cancel_order_examine_status!=3
            and eo.num_refund_examine_status!=3
            and eo.num_valid_status != 3
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>

            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp5 on temp5.id=temp1.id
        left join(
        select COUNT(eo.id) as `paying`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_payment_status = 0
            )

            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp6 on temp6.id=temp1.id
        left join(
        select COUNT(eo.id) as `payed`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_payment_status = 1
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))

            </if>
            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp7 on temp7.id=temp1.id
        left join(
        select COUNT(eo.id) as `canceled`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_valid_status = 1
            )

            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp8 on temp8.id=temp1.id
        left join(
        select COUNT(eo.id) as `rejected`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_create_order_examine_status IN (2, 4)
            OR eo.num_modify_order_examine_status IN (2, 4)
            OR eo.num_refund_examine_status IN (4)
            OR eo.num_cancel_order_examine_status IN (4)
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp9 on temp9.id=temp1.id
        left join(
        select COUNT(eo.id) as `refund`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_valid_status = 3 or num_valid_status = 4
            )

            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>
            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp10 on temp10.id=temp1.id
        left join(
        select COUNT(eo.id) as `deprecated`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_valid_status = 2
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))

            </if>
            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp11 on temp11.id=temp1.id
        left join(
        select COUNT(eo.id) as `customerSignNot`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_create_order_examine_status = 10
            )

            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>

            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp12 on temp12.id=temp1.id
        left join(
        select COUNT(eo.id) as `customerRejected`,1 as id
        FROM
        erp_orders eo
        <if test="query.numNameId!=null and query.numNameId!=''">
            LEFT JOIN erp_service_orders es on eo.id=es.num_order_id
            LEFT JOIN erp_product_detail detail on es.num_product_id=detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
        </if>
        LEFT JOIN erp_client ec ON eo.num_client_id = ec.id
        LEFT JOIN bd_clue bc ON bc.id = eo.num_clue_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_personal ep ON ep.id = ec.num_personal_id
        LEFT JOIN sys_user u ON eo.num_user_id = u.user_id
        LEFT JOIN sys_dept sd ON u.dept_id = sd.dept_id
        <where>
            1 = 1
            -- "订单状态：1-待审核，2-待服务，3-服务中，4-已完成。"

            AND (
            eo.num_create_order_examine_status = 11
            )


            <if test="query.numNameId!=null and query.numNameId!=''">
                and name.num_name_id=#{query.numNameId}
            </if>
            <if test="query.orderNumber !=null and query.orderNumber !='' ">and eo.vc_order_number =
                #{query.orderNumber}
            </if>
            <if test="query.clueCustomerName !=null and query.clueCustomerName !='' ">and ec.contactName LIKE
                CONCAT('%', #{query.clueCustomerName}, '%')
            </if>
            <if test="query.clueCustomerPhone !=null and query.clueCustomerPhone !='' ">and ec.contactPhone
                =#{query.clueCustomerPhone}
            </if>
            <if test="query.clientName !=null and query.clientName !='' ">and ee.vc_company_name LIKE CONCAT('%',
                #{query.clientName}, '%')
            </if>
            <if test="query.userName !=null and query.userName !='' ">and u.nick_name LIKE
                CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.vcContractNumber !=null and query.vcContractNumber!=''">and eo.contact_num LIKE
                CONCAT('%',#{query.vcContractNumber},'%')
            </if>

            <if test="query.orderSourceType != null and query.orderSourceType != ''">
                AND (
                eo.num_source = #{query.orderSourceType}
                )
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND (
                u.dept_id = #{query.deptId}
                )
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size > 0">
                AND u.dept_id in
                <foreach item="deptId" collection="query.deptIdList" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != ''">
                AND (
                u.user_id = #{query.userId}
                )
            </if>
            <if test="query.isHaveLastPrice != null">
                <if test="query.isHaveLastPrice eq 0">
                    AND (
                    eo.num_retainage_status = 2
                    )
                </if>
                <if test="query.isHaveLastPrice eq 1">
                    AND (
                    eo.num_retainage_status = 1
                    )
                </if>
            </if>
            <if test="query.createTimeBegin != null and query.createTimeBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y%m%d')
                )
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_datecreated_time, '%Y%m%d') &lt;= DATE_FORMAT(#{query.createTimeEnd},
                '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateBegin != null and query.datSigningDateBegin != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &gt;= DATE_FORMAT(#{query.datSigningDateBegin}, '%Y%m%d')
                )
            </if>
            <if test="query.datSigningDateEnd != null and query.datSigningDateEnd != ''">
                AND (
                DATE_FORMAT(eo.dat_signing_date, '%Y%m%d') &lt;= DATE_FORMAT(#{query.datSigningDateEnd}, '%Y%m%d')
                )
            </if>
            <if test="query.dataRange == 2">
                AND sd.city_id = 64 and sd.dept_id not in (200,245,246,50,193,235,254,255)
            </if>
            <if test="query.dataRange == 3">
                AND (sd.city_id = 136 or sd.dept_id in (200,245,246,50,193,235,254,255,211,230,231,232,233,248,253,258))
            </if>

            <!-- 数据范围过滤 -->
            <if test="query.dataRange == null or query.dataRange == ''">
                ${query.params.dataScope}
            </if>
        </where>
        ) temp13 on temp13.id=temp1.id
    </select>

    <select id="selectLicenseSalesBoardVo" resultType="com.nnb.erp.domain.vo.qzd.LicenseSalesBoardVo">
        (SELECT
        eo.id as orderId,
        eo.is_electronic_contract as isElectronicContract,
        eo.contact_num as contractNumber,
        eo.vc_order_number as vcOrderNumber,
        ee.vc_company_name as enterpriseName,
        eo.dat_signing_date as datSigningDate,

        errd.num_collection_price as numCollectionPrice,

        cdr.title as city,


        edc.discount_amount as discountAmount,
        eso.num_total_price as productNumTotalPrice,
        eso.num_pay_price as productPayPrice,
        eso.num_last_price as productNumLastPrice,

        eo.dat_finance_collection_time as datFinanceCollectionTime,

        eso.num_coupon_price as numCouponPrice,
        bc.id clueId,
        err.id as numRetainageId,
        1 as type,
        bgs.vc_name as guestVcName,
        el.number as licenceNumber




        from erp_orders eo
        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id

        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id
        LEFT JOIN com_dict_region as cdr on cdr.id = ec.num_city_id
        LEFT JOIN erp_discount_coupon edc on eso.num_coupon_id = edc.id
        left join erp_license el on el.number = eo.license_number
        left join bd_clue bc on eo.num_clue_id = bc.id
        left join bd_guest_srcs bgs on bgs.id = bc.guest_srcs_id

        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and eso.num_product_id = 3413
        and errd.num_status = 1
        and errd.payType = 1
        and eso.num_status != 0

        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and eo.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and eo.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>

        )
        union all
        (
        SELECT
        eo.id as orderId,
        eo.is_electronic_contract as isElectronicContract,
        eo.contact_num as contractNumber,
        eo.vc_order_number as 'vcOrderNumber',
        ee.vc_company_name as enterpriseName,
        eo.dat_signing_date as datSigningDate,


        errd.num_collection_price as numCollectionPrice,

        cdr.title as city,

        edc.discount_amount as discountAmount,
        eso.num_total_price as productNumTotalPrice,
        eso.num_pay_price as productPayPrice,
        eso.num_last_price as productNumLastPrice,

        err.dat_finance_collection_time as datFinanceCollectionTime,

        eso.num_coupon_price as numCouponPrice,
        bc.id clueId,
        err.id as numRetainageId,
        1 as type,
        bgs.vc_name as guestVcName,
        el.number as licenceNumber





        from erp_orders eo

        left join erp_service_orders eso on eso.num_order_id = eo.id
        left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
        left join erp_retainage_return err on err.id = errd.num_retainage_return_id

        left join erp_client ec on ec.id = eo.num_client_id
        left join erp_enterprise ee on ec.num_enterprise_id = ee.id
        LEFT JOIN com_dict_region as cdr on cdr.id = ec.num_city_id

        LEFT JOIN erp_discount_coupon edc on eso.num_coupon_id = edc.id
        left join erp_license el on el.number = eo.license_number
        left join bd_clue bc on eo.num_clue_id = bc.id
        left join bd_guest_srcs bgs on bgs.id = bc.guest_srcs_id


        where
        (
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 0 and  eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_refund_examine_status = 5 and eo.num_valid_status not in(1, 2))
        or
        (eo.num_create_order_examine_status = 5 and eo.num_modify_order_examine_status = 5 and eo.num_refund_examine_status = 5  and eo.num_valid_status not in(1, 2))
        )
        and eso.num_product_id = 3413
        and errd.num_status = 1
        AND (err.num_status = 1 or err.num_status is null)
        and errd.payType = 2
        and eso.num_status != 0

        <if test="datFinanceCollectionTimeStart != null and datFinanceCollectionTimeEnd !=null">
            and err.dat_finance_collection_time &gt;= #{datFinanceCollectionTimeStart}
            and err.dat_finance_collection_time &lt;= #{datFinanceCollectionTimeEnd}
        </if>
        )
    </select>
    <select id="collectionReport" resultType="com.nnb.erp.domain.vo.CollectionReportVo">
            select
            eo.id as orderId,
            eo.is_electronic_contract as isElectronicContract,
            err.num_payee as payeeUserId,
            GROUP_CONCAT(DISTINCT errd.id) as retainageReturnDetailIds,
            IFNULL(esoi.num_price, 0) as productPrice,
            eso.num_coupon_id as numCouponId,
            eso.num_combined_activity_id as numCombinedActivityId,
            eo.num_discount_amount as orderDiscountAmount,
            err.follow_ids as followIds,
            eso.num_product_id as productId,
            eso.num_total_price as productNumPayPrice,
            eso.num_last_price as productNumLastPrice,
            scsps.is_end as pointStatusIsEnd,
            ssm.type_before_zz as smTypeBeforeZz,
            ssm.service_type as smServiceType,
            eso.id as serviceOrderId,
            epd.service_type_id as pdServiceType,
            eso.gt_kp as gtKp,
            epd.invoice_cost as invoiceCost,
            eo.vc_order_number as vcOrderNumber,
            eo.contact_num AS contractNumber,
            ssm.id as serviceMainId,
            cdr.title as city,
            eo.dat_signing_date as datSigningDate,
            eo.num_user_id as numUserId,
            su.nick_name as signer,
            sd.dept_name as signDeptName,
            ee.vc_company_name as enterpriseName,
            ec.contactName as enterpriseContactName,
            eptd.vc_tax_name as vcCorporateProperty,
            epc.vc_classification_name as vcClassificationName,
            eps.vc_service_name as vcServiceName,
            epn.vc_product_name as vcProductName,
            ecst.name as serviceTypeName,
            IFNULL(eso.num_product_count, 0) as numProductCount,
            eso.num_coupon_price as discountAmount,
            eo.num_total_price as orderTotalPrice,
            SUM(errd.num_collection_price) as collectionPrice,
            eo.num_last_price as numLastPrice,
            CASE
            WHEN eso.num_is_deprecated = 0 THEN '正常'
            WHEN eso.num_is_deprecated = 1 THEN '退费'
            WHEN eso.num_is_deprecated = 2 THEN '退费待审核'
            WHEN eso.num_is_deprecated = 3 THEN '作废'
            WHEN eso.num_is_deprecated = 4 THEN '作废待审核'
            WHEN eso.num_is_deprecated = 5 THEN '部分退费'
            WHEN eso.num_is_deprecated = 6 THEN '部分退费待审核'
            WHEN eso.num_is_deprecated = 7 THEN '修改新增待审核'
            ELSE '' END as productStatus,
            2 as isFinish,
            max(err.dat_finance_collection_time) as datFinanceCollectionTime,
            eo.dat_signing_datecreated_time as orderCreateTime,
            eo.vc_remark as payeeRemark,
            ssm.to_increment_time as toIncrementTime,
            "否" as isRefund,
            eso.num_refund_price as refoundPrice,
            scsts.name as serviceTypeStatusName,
            sss.name as shelveStatus,
            ssm.user_id as serviceUserId,
            scsp.name as servicePointName,
            scsps.name as servicePointStatusName,
            ssm.ac_start as acStart,
            ssm.ac_end as acEnd,
            ssm.months as months
            from erp_service_orders eso
            left join erp_retainage_return_detail errd on errd.num_service_order_id = eso.id
            left join erp_retainage_return err on err.id = errd.num_retainage_return_id
            left join erp_service_orders_info esoi on esoi.num_service_orders = eso.id
            left join erp_orders eo on eso.num_order_id = eo.id
            left join s_service_main ssm on (ssm.order_id = eso.num_order_id and ssm.product_id = eso.num_product_id and ssm.give_product = 2)
            left join erp_client ec on ec.id = eo.num_client_id
            left join com_dict_region cdr on cdr.id = ec.num_city_id
            left join erp_enterprise ee on ee.id = ec.num_enterprise_id
            left join erp_product_tax_dict eptd on eptd.num_tax_id = ee.num_corporate_property_id
            left join erp_product_detail epd on epd.num_product_id = eso.num_product_id
            left join erp_product_service eps on eps.num_service_id = epd.num_service_id
            left join erp_product_name epn on epn.num_name_id = epd.num_name_id
            left join erp_product_type ept on ept.num_type_id = epn.num_type_id
            left join erp_product_classification epc on epc.num_classification_id = ept.num_classification_id
            left join s_config_service_type_status scsts on scsts.id = ssm.service_status
            left join s_config_service_type ecst on ecst.id = epd.service_type_id
            left join s_config_service_point_status scsps on scsps.id = ssm.service_point_status
            left join s_config_service_point scsp on scsp.id = ssm.service_point
            left join s_service_shelve sss on sss.id = ssm.shelve_id



            left join sys_user su on su.user_id = eo.num_user_id
            left join sys_dept sd on sd.dept_id = su.dept_id
            <where>
                and eo.num_create_order_examine_status = 5 and err.num_status = 1 and eo.num_valid_status in (0,4,6) and ssm.internal_service_from = 0
                <if test="orderNumber != null and orderNumber != ''"> and eo.vc_order_number = #{orderNumber}</if>
                <if test="deptIdList != null and deptIdList.size > 0">
                    and sd.dept_id in
                    <foreach item="deptId" collection="deptIdList" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="enterpriseName != null and enterpriseName != ''"> and ee.vc_company_name like concat('%', #{enterpriseName}, '%')</if>
                <if test="signDateBegin != null "> and eo.dat_signing_date <![CDATA[>=]]> #{signDateBegin}</if>
                <if test="signDateEnd != null "> and eo.dat_signing_date <![CDATA[<=]]> #{signDateEnd}</if>
                <if test="orderCreatedDateBegin != null "> and eo.dat_signing_datecreated_time <![CDATA[>=]]> #{orderCreatedDateBegin}</if>
                <if test="orderCreatedDateEnd != null "> and eo.dat_signing_datecreated_time <![CDATA[<=]]> #{orderCreatedDateEnd}</if>
                <if test="payDateBegin != null "> and err.dat_finance_collection_time <![CDATA[>=]]> #{payDateBegin}</if>
                <if test="payDateEnd != null "> and err.dat_finance_collection_time <![CDATA[<=]]> #{payDateEnd}</if>
                ${params.dataScope}
                group by eso.id,err.num_payee,DATE_FORMAT(err.dat_finance_collection_time, '%Y-%m')
                order by eo.id desc
            </where>



    </select>

    <select id="selectErpOrdersByIds" resultMap="ErpOrdersResult">
        <include refid="selectErpOrdersVo"/>
        where id in (
            <foreach collection="orderIds" item="orderId" separator=",">
                #{orderId}
            </foreach>
        )
    </select>

    <select id="selectOrderByOrderNumberList" resultType="com.nnb.erp.domain.ErpOrders">
        <include refid="selectErpOrdersVo"/>
        where vc_order_number in (
            <foreach collection="orderNumberList" item="orderNumber" separator=",">
                #{orderNumber}
            </foreach>
        )
    </select>

</mapper>
