<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.CourseInfoMapper">

    <!-- 在线课程配置列表。 -->
    <select id="getCourseInfoPageForConfig" resultType="com.nnb.erp.domain.vo.course.CourseInfoPageForConfigVO">
        SELECT tci.`id`                                                                  AS 'id',
                tci.`courseName`                                                         AS 'courseName',
                CONCAT('[', tct.`firstId`, IF(tct.`secondId` IS NOT NULL, CONCAT(',', tct.`secondId`), ''),
                IF(tct.`thirdId` IS NOT NULL, CONCAT(',', tct.`thirdId`), ''), ']')      AS 'typeIdArrStr',
                tci.`typeId`                                                             AS 'typeId',
                CONCAT(tct.`firstName`, IF(tct.`secondName` IS NOT NULL, CONCAT('/', tct.`secondName`), ''),
                IF(tct.`thirdName` IS NOT NULL, CONCAT('/', tct.`thirdName`), ''))       AS 'courseTypeName',
                tci.`courseCover`                                                        AS 'courseCover',
                tci.`courseLink`                                                         AS 'courseLink',
                tci.`teacherName`                                                        AS 'teacherName',
                tci.`teacherAvatar`                                                      AS 'teacherAvatar',
                tci.`teacherIntroduction`                                                AS 'teacherIntroduction',
                tci.`qrcodeCourse`                                                       AS 'qrcodeCourse',
                tci.`qrcodeExam`                                                         AS 'qrcodeExam',
                tci.`qrcodeFeedback`                                                     AS 'qrcodeFeedback',
                DATE_FORMAT(tci.`createdTime`, '%Y-%m-%d %H:%i:%s')                      AS 'createdTime',
                tci.`status`                                                             AS 'status',
                t1.roleIds                                                               AS 'roleId',
                t1.roleNames                                                             AS 'roleNames'
        FROM tb_course_info tci
        LEFT JOIN tb_course_type tct ON tct.`id` = tci.`typeId`
        LEFT JOIN (
        SELECT tcp.`courseId`, GROUP_CONCAT(sr.`role_name`) AS 'roleNames', CONCAT(GROUP_CONCAT(tcp.`roleId`)) AS 'roleIds'            FROM tb_course_permission tcp
        INNER JOIN sys_role sr ON sr.`role_id` = tcp.`roleId`
            <where>
                <if test="query.roleName != null and query.roleName != ''">
                    AND sr.`role_name` LIKE CONCAT('%', #{query.roleName}, '%')
                </if>
            </where>
            GROUP BY tcp.`courseId`
        ) t1 ON t1.`courseId` = tci.`id`
        <where>
            <if test="query.courseName != null and query.courseName != ''">
                AND tci.`courseName` LIKE CONCAT('%', #{query.courseName}, '%')
            </if>
            <if test="query.status != null">
                AND tci.`status` = #{query.status}
            </if>
            <if test="query.courseTypes != null">
                AND (
                    tct.`firstId` IN
                        <foreach collection="query.courseTypes" item="courseType" open="(" close=")" separator=",">
                            #{courseType}
                        </foreach>
                        OR
                    tct.`secondId` IN
                        <foreach collection="query.courseTypes" item="courseType" open="(" close=")" separator=",">
                            #{courseType}
                        </foreach>
                        OR
                    tct.`thirdId` IN
                        <foreach collection="query.courseTypes" item="courseType" open="(" close=")" separator=",">
                            #{courseType}
                        </foreach>
                )
            </if>
        </where>
    </select>

    <!-- 添加实体。 -->
    <insert id="insertEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_course_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">`courseName`,</if>
            <if test="typeId != null and typeId != ''">`typeId`,</if>
            <if test="courseCover != null and courseCover != ''">`courseCover`,</if>
            <if test="courseLink != null and courseLink != ''">`courseLink`,</if>
            <if test="teacherName != null and teacherName != ''">`teacherName`,</if>
            <if test="teacherAvatar != null and teacherAvatar != ''">`teacherAvatar`,</if>
            <if test="teacherIntroduction != null and teacherIntroduction != ''">`teacherIntroduction`,</if>
            <if test="qrcodeCourse != null and qrcodeCourse != ''">`qrcodeCourse`,</if>
            <if test="qrcodeExam != null and qrcodeExam != ''">`qrcodeExam`,</if>
            <if test="qrcodeFeedback != null and qrcodeFeedback != ''">`qrcodeFeedback`,</if>
            <if test="status != null and status != ''">`status`,</if>
            <if test="createdBy != null and createdBy != ''">`createdBy`,</if>
            <if test="createdTime != null">`createdTime`,</if>
            <if test="roleIds !=null">roleIds,</if>
        </trim>
        <trim prefix="VALUE (" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="typeId != null and typeId != ''">#{typeId},</if>
            <if test="courseCover != null and courseCover != ''">#{courseCover},</if>
            <if test="courseLink != null and courseLink != ''">#{courseLink},</if>
            <if test="teacherName != null and teacherName != ''">#{teacherName},</if>
            <if test="teacherAvatar != null and teacherAvatar != ''">#{teacherAvatar},</if>
            <if test="teacherIntroduction != null and teacherIntroduction != ''">#{teacherIntroduction},</if>
            <if test="qrcodeCourse != null and qrcodeCourse != ''">#{qrcodeCourse},</if>
            <if test="qrcodeExam != null and qrcodeExam != ''">#{qrcodeExam},</if>
            <if test="qrcodeFeedback != null and qrcodeFeedback != ''">#{qrcodeFeedback},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="roleIds !=null">#{roleIds},</if>
        </trim>
    </insert>

    <!-- 修改实体。 -->
    <update id="updateEntity">
        UPDATE tb_course_info SET
        <trim suffixOverrides=",">
            <if test="courseName != null and courseName != ''">`courseName` = #{courseName},</if>
            <if test="typeId != null and typeId != ''">`typeId` = #{typeId},</if>
            <if test="courseCover != null and courseCover != ''">`courseCover` = #{courseCover},</if>
            <if test="courseLink != null and courseLink != ''">`courseLink` = #{courseLink},</if>
            <if test="teacherName != null and teacherName != ''">`teacherName` = #{teacherName},</if>
            <if test="teacherAvatar != null and teacherAvatar != ''">`teacherAvatar` = #{teacherAvatar},</if>
            <if test="teacherIntroduction != null and teacherIntroduction != ''">`teacherIntroduction` = #{teacherIntroduction},</if>
            <if test="qrcodeCourse != null and qrcodeCourse != ''">`qrcodeCourse` = #{qrcodeCourse},</if>
            <if test="qrcodeExam != null and qrcodeExam != ''">`qrcodeExam` = #{qrcodeExam},</if>
            <if test="qrcodeFeedback != null and qrcodeFeedback != ''">`qrcodeFeedback` = #{qrcodeFeedback},</if>
            <if test="status != null and status != ''">`status` = #{status},</if>
            <if test="updateBy != null and updateBy != ''">`updateBy` = #{updateBy},</if>
            <if test="updateTime != null">`updateTime` = #{updateTime},</if>
            <if test="roleIds !=null">roleIds=#{roleIds},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 更改指定课程状态。 -->
    <update id="changeStatus">
        UPDATE tb_course_info SET `status` = 1 - `status` WHERE `id` = #{courseId}
    </update>

    <!-- 根据权限获取课程列表。 -->
    <select id="getCourseInfoPageForUser" resultType="com.nnb.erp.domain.vo.course.CourseInfoForUserVO">
        SELECT tci.`id`          AS 'courseId',
               tci.`courseName`  AS 'courseName',
               tci.`courseCover` AS 'courseCover'
        FROM tb_course_info tci
        INNER JOIN tb_course_type tct ON tct.`id` = tci.`typeId`
        INNER JOIN tb_course_permission tcp ON tcp.`courseId` = tci.id
        WHERE   tcp.`roleId` IN
        <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
          AND tci.`status` = 1
        <if test="query.courseType != null">
            AND (
                tct.`firstId` = #{query.courseType} OR
                tct.`secondId` = #{query.courseType} OR
                tct.`thirdId` = #{query.courseType}
            )
        </if>
        <if test="query.courseName != null and query.courseName != ''">
            AND tci.`courseName` IS NOT NULL
            AND tci.`courseName` LIKE CONCAT('%', #{query.courseName}, '%')
        </if>
        GROUP BY tci.id ORDER BY tci.id
    </select>

    <!-- 根据权限获取课程详情。 -->
    <select id="getCourseInfoForUser" resultType="com.nnb.erp.domain.vo.course.CourseInfoForUserVO">
        SELECT tci.`id`                                                                  AS 'courseId',
               tci.`courseName`                                                          AS 'courseName',
               CONCAT(tct.`firstName`, IF(tct.`secondName` IS NOT NULL, CONCAT('  >  ', tct.`secondName`), ''),
                      IF(tct.`thirdName` IS NOT NULL, CONCAT('  >  ', tct.`thirdName`), '')) AS 'courseTypeName',
               tci.`courseCover`                                                         AS 'courseCover',
               tci.`courseLink`                                                          AS 'courseLink',
               tci.`teacherName`                                                         AS 'teacherName',
               tci.`teacherAvatar`                                                       AS 'teacherAvatar',
               tci.`teacherIntroduction`                                                 AS 'teacherIntroduction',
               tci.`qrcodeCourse`                                                        AS 'qrcodeCourse',
               tci.`qrcodeExam`                                                          AS 'qrcodeExam',
               tci.`qrcodeFeedback`                                                      AS 'qrcodeFeedback',
               tci.`createdTime`                                                         AS 'createdTime'
        FROM tb_course_info tci
        INNER JOIN tb_course_type tct ON tct.`id` = tci.`typeId`
        INNER JOIN tb_course_permission tcp ON tcp.`courseId` = tcp.`courseId`
        WHERE tci.`id` AND
        tcp.`roleId` IN
        <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
          AND tci.`status` = 1 AND tci.`id` = #{courseId}
        GROUP BY tci.`id`
    </select>

</mapper>
