<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpTransactionVoucherMapper">
    
    <resultMap type="ErpTransactionVoucher" id="ErpTransactionVoucherResult">
        <result property="id"    column="id"    />
        <result property="payRecordId"    column="pay_record_id"    />
        <result property="type"    column="type"    />
        <result property="activitieId"    column="activitie_id"    />
        <result property="allFee"    column="all_fee"    />
        <result property="balance"    column="balance"    />
        <result property="balanceUse"    column="balance_use"    />
        <result property="waitEntry"    column="wait_entry"    />
        <result property="waitDeduction"    column="wait_deduction"    />
        <result property="endTime"    column="end_time"    />
        <result property="operateUser"    column="operate_user"    />
        <result property="opetateTime"    column="opetate_time"    />
        <result property="subtractMemo"    column="subtract_memo"    />
    </resultMap>

    <sql id="selectErpTransactionVoucherVo">
        select id, pay_record_id, type, activitie_id, all_fee, balance, balance_use, wait_entry, wait_deduction, end_time, operate_user, opetate_time, subtract_memo from erp_transaction_voucher
    </sql>

    <select id="selectErpTransactionVoucherList" parameterType="ErpTransactionVoucher" resultMap="ErpTransactionVoucherResult">
        <include refid="selectErpTransactionVoucherVo"/>
        <where>  
            <if test="payRecordId != null "> and pay_record_id = #{payRecordId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="activitieId != null "> and activitie_id = #{activitieId}</if>
        </where>
    </select>
    
    <select id="selectErpTransactionVoucherById" parameterType="Integer" resultMap="ErpTransactionVoucherResult">
        <include refid="selectErpTransactionVoucherVo"/>
        where id = #{id}
    </select>

    <select id="selectErpTransactionVoucherByPayRecordId" parameterType="java.lang.Long" resultType="com.alibaba.fastjson.JSONObject">
        select etv.balance,
               etv.balance_use,
               etv.all_fee,
               etv.wait_entry,
               etv.wait_deduction,
               etv.type,
               epa.name,
               epa.other_share,
               etv.id,
               etv.activitie_id,
               CASE etv.call_back_status
                   WHEN 1 THEN "已回访"
                   ELSE "-"
                   END AS callBackStatusStr,
               CASE etv.call_back_result
                   WHEN 1 THEN "结果一致"
                   WHEN 2 THEN "结果异常"
                   WHEN 3 THEN "无人接听"
                   ELSE "-"
               END AS callBackResultStr,
               DATE_FORMAT(etv.call_back_date, '%Y-%m-%d %H:%i:%s') AS 'callBackDate',
               etv.call_back_memo as callBackMemo,
               etv.type as activitieType
        from erp_transaction_voucher etv
        left join erp_promotional_activities epa on epa.id = etv.activitie_id
        LEFT JOIN erp_order_pay_record eopr ON eopr.id = etv.pay_record_id
        where etv.pay_record_id = #{payRecordId} and eopr.reported_or_not = 1
    </select>
        
    <insert id="insertErpTransactionVoucher" parameterType="ErpTransactionVoucher" useGeneratedKeys="true" keyProperty="id">
        insert into erp_transaction_voucher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payRecordId != null">pay_record_id,</if>
            <if test="type != null">type,</if>
            <if test="activitieId != null">activitie_id,</if>
            <if test="allFee != null">all_fee,</if>
            <if test="balance != null">balance,</if>
            <if test="balanceUse != null">balance_use,</if>
            <if test="waitEntry != null">wait_entry,</if>
            <if test="waitDeduction != null">wait_deduction,</if>
            <if test="endTime != null">end_time,</if>
            <if test="operateUser != null">operate_user,</if>
            <if test="opetateTime != null">opetate_time,</if>
            <if test="zeroPay != null">zero_pay,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payRecordId != null">#{payRecordId},</if>
            <if test="type != null">#{type},</if>
            <if test="activitieId != null">#{activitieId},</if>
            <if test="allFee != null">#{allFee},</if>
            <if test="balance != null">#{balance},</if>
            <if test="balanceUse != null">#{balanceUse},</if>
            <if test="waitEntry != null">#{waitEntry},</if>
            <if test="waitDeduction != null">#{waitDeduction},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="operateUser != null">#{operateUser},</if>
            <if test="opetateTime != null">#{opetateTime},</if>
            <if test="zeroPay != null">#{zeroPay},</if>
         </trim>
    </insert>

    <update id="updateErpTransactionVoucher" parameterType="ErpTransactionVoucher">
        update erp_transaction_voucher
        <trim prefix="SET" suffixOverrides=",">
            <if test="payRecordId != null">pay_record_id = #{payRecordId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="activitieId != null">activitie_id = #{activitieId},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="balanceUse != null">balance_use = #{balanceUse},</if>
            <if test="waitEntry != null">wait_entry = #{waitEntry},</if>
            <if test="waitDeduction != null">wait_deduction = #{waitDeduction},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="operateUser != null">operate_user = #{operateUser},</if>
            <if test="opetateTime != null">opetate_time = #{opetateTime},</if>
            <if test="subtractMemo != null">subtract_memo = #{subtractMemo},</if>
            <if test="approveIn != null">approve_in = #{approveIn},</if>
            self_id = #{selfId}
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpTransactionVoucherById" parameterType="Integer">
        delete from erp_transaction_voucher where id = #{id}
    </delete>

    <delete id="deleteErpTransactionVoucherByIds" parameterType="String">
        delete from erp_transaction_voucher where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOrderCanUseList" resultType="java.lang.Integer">
        select
        eopr.id
        from erp_transaction_voucher etv
        left join erp_order_pay_record eopr on eopr.id = etv.pay_record_id
        <where>
            and eopr.reported_or_not = 1 and (etv.balance_use > 0 or etv.zero_pay = 1) and IF(etv.type = 3,etv.end_time &gt;= curdate(), ISNULL(etv.end_time))
            and ( ISNULL(etv.activitie_id)
                <if test="activitieIdList != null and activitieIdList.size() > 0">
                    or etv.activitie_id in
                    <foreach item="activitieId" collection="activitieIdList" open="(" separator="," close=")">
                        #{activitieId}
                    </foreach>
                </if>
            )
            <if test="userId != null "> and eopr.userId = #{userId}</if>
<!--            <if test="clueId != null "> and IF(eopr.payment_type = 0,eopr.clueId = #{clueId}, ISNULL(eopr.clueId))</if>-->
<!--            <if test="clientId != null and clientId != ''"> and IF(eopr.payment_type = 0,eopr.clientId = #{clientId}, ISNULL(eopr.clientId))</if>-->
        </where>
        GROUP BY eopr.id
    </select>

    <select id="selectByPayRecordIdList" resultType="com.nnb.erp.domain.vo.ErpTransactionVoucherVo">
        select
               etv.id,
               eopr.billNo,
               eopr.tradeId,
               eopr.document_number,
               eopr.clueId,
               eopr.clientId,
               eopr.client_name,
               eopr.payTime,
               eopr.payment_type,
               epa.name as activitieName,
               epa.other_share,
                etv.activitie_id,
               etv.pay_record_id,
               etv.all_fee,
               etv.balance,
               etv.balance_use,
               etv.wait_entry,
               etv.wait_deduction,
                etv.approve_in,
                etv.type,
               0 as needProduct,
               eopr.userId as operateUser,
                epc.name as payCompanyName,
        etv.zero_pay
        from erp_transaction_voucher etv
        left join erp_order_pay_record eopr on eopr.id = etv.pay_record_id
        left join erp_promotional_activities epa on epa.id = etv.activitie_id
        left join erp_pay_company epc on epc.id = eopr.payCompanyId
        <where>
            <if test="type != null "> and etv.type = #{type}</if>
            <if test="payRecordIdList != null and payRecordIdList.size() > 0">
                and etv.pay_record_id in
                <foreach item="payRecordId" collection="payRecordIdList" open="(" separator="," close=")">
                    #{payRecordId}
                </foreach>
            </if>
            <if test="activitieIdList != null and activitieIdList.size() > 0">
                and etv.activitie_id in
                <foreach item="activitieId" collection="activitieIdList" open="(" separator="," close=")">
                    #{activitieId}
                </foreach>
            </if>
        </where>
        ORDER BY eopr.payment_type,eopr.payTime ASC
    </select>
</mapper>