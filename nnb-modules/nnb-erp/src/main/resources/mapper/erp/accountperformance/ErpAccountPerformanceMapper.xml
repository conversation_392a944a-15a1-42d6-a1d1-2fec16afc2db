<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.accountpermance.ErpAccountPerformanceMapper">
    
    <resultMap type="ErpAccountPerformance" id="ErpAccountPerformanceResult">
        <result property="id"    column="id"    />
        <result property="monthStr"    column="month_str"    />
        <result property="regularRenewal"    column="regular_renewal"    />
        <result property="deptPersonName"    column="dept_person_name"    />
        <result property="effectiveHouseholds"    column="effective_households"    />
        <result property="everyman"    column="everyman"    />
        <result property="smallScale"    column="small_scale"    />
        <result property="renewTotal"    column="renew_total"    />
        <result property="renewed"    column="renewed"    />
        <result property="renewedDetail"    column="renewed_detail"    />
        <result property="toBeRenewed"    column="to_be_renewed"    />
        <result property="renewalRate"    column="renewal_rate"    />
        <result property="abnormalLoss"    column="abnormal_loss"    />
        <result property="abnormal"    column="abnormal"    />
        <result property="cancel"    column="cancel"    />
        <result property="transfer"    column="transfer"    />
        <result property="cityId"    column="city_id"    />
        <result property="type"    column="type"    />
        <result property="queryYearMonth"    column="query_year_month"    />
        <result property="deptId"    column="dept_id"    />
        <result property="year"    column="year"    />
        <result property="quarter"    column="quarter"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUser"    column="update_user"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectErpAccountPerformanceVo">
        select id, month_str, regular_renewal, dept_person_name, effective_households, everyman, small_scale, renew_total, renewed, renewed_detail, to_be_renewed, renewal_rate, abnormal_loss, abnormal, cancel, transfer, city_id, type, query_year_month, dept_id, year, quarter, user_id, create_time, create_user, update_time, update_user, del_flag from erp_account_performance
    </sql>

    <select id="selectErpAccountPerformanceList" parameterType="ErpAccountPerformance" resultMap="ErpAccountPerformanceResult">
        <include refid="selectErpAccountPerformanceVo"/>
        <where>  
            <if test="monthStr != null  and monthStr != ''"> and month_str = #{monthStr}</if>
            <if test="regularRenewal != null "> and regular_renewal = #{regularRenewal}</if>
            <if test="deptPersonName != null  and deptPersonName != ''"> and dept_person_name like concat('%', #{deptPersonName}, '%')</if>
            <if test="effectiveHouseholds != null "> and effective_households = #{effectiveHouseholds}</if>
            <if test="everyman != null "> and everyman = #{everyman}</if>
            <if test="smallScale != null "> and small_scale = #{smallScale}</if>
            <if test="renewTotal != null "> and renew_total = #{renewTotal}</if>
            <if test="renewed != null "> and renewed = #{renewed}</if>
            <if test="renewedDetail != null  and renewedDetail != ''"> and renewed_detail = #{renewedDetail}</if>
            <if test="toBeRenewed != null "> and to_be_renewed = #{toBeRenewed}</if>
            <if test="renewalRate != null  and renewalRate != ''"> and renewal_rate = #{renewalRate}</if>
            <if test="abnormalLoss != null "> and abnormal_loss = #{abnormalLoss}</if>
            <if test="abnormal != null "> and abnormal = #{abnormal}</if>
            <if test="cancel != null "> and cancel = #{cancel}</if>
            <if test="transfer != null "> and transfer = #{transfer}</if>
            <if test="cityId != null "> and city_id = #{cityId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="queryYearMonth != null "> and query_year_month = #{queryYearMonth}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="quarter != null  and quarter != ''"> and quarter = #{quarter}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>

    <select id="selectOldAccountPerformanceList" resultMap="ErpAccountPerformanceResult">
        <include refid="selectErpAccountPerformanceVo"/>
        <where>
            type = #{oldAccountPermanceType}
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="quarter != null  and quarter != ''"> and quarter = #{quarter}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="cityId != null "> and city_id = #{cityId}</if>
            <if test="monthStart != null "> and query_year_month &gt;= #{monthStart}</if>
            <if test="monthStart != null "> and query_year_month &lt;= #{monthEnd}</if>
        </where>
    </select>
    
    <select id="selectErpAccountPerformanceById" parameterType="Long" resultMap="ErpAccountPerformanceResult">
        <include refid="selectErpAccountPerformanceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpAccountPerformance" parameterType="ErpAccountPerformance" useGeneratedKeys="true" keyProperty="id">
        insert into erp_account_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthStr != null">month_str,</if>
            <if test="regularRenewal != null">regular_renewal,</if>
            <if test="deptPersonName != null">dept_person_name,</if>
            <if test="effectiveHouseholds != null">effective_households,</if>
            <if test="everyman != null">everyman,</if>
            <if test="smallScale != null">small_scale,</if>
            <if test="renewTotal != null">renew_total,</if>
            <if test="renewed != null">renewed,</if>
            <if test="renewedDetail != null">renewed_detail,</if>
            <if test="toBeRenewed != null">to_be_renewed,</if>
            <if test="renewalRate != null">renewal_rate,</if>
            <if test="abnormalLoss != null">abnormal_loss,</if>
            <if test="abnormal != null">abnormal,</if>
            <if test="cancel != null">cancel,</if>
            <if test="transfer != null">transfer,</if>
            <if test="cityId != null">city_id,</if>
            <if test="type != null">type,</if>
            <if test="queryYearMonth != null">query_year_month,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="year != null">year,</if>
            <if test="quarter != null">quarter,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthStr != null">#{monthStr},</if>
            <if test="regularRenewal != null">#{regularRenewal},</if>
            <if test="deptPersonName != null">#{deptPersonName},</if>
            <if test="effectiveHouseholds != null">#{effectiveHouseholds},</if>
            <if test="everyman != null">#{everyman},</if>
            <if test="smallScale != null">#{smallScale},</if>
            <if test="renewTotal != null">#{renewTotal},</if>
            <if test="renewed != null">#{renewed},</if>
            <if test="renewedDetail != null">#{renewedDetail},</if>
            <if test="toBeRenewed != null">#{toBeRenewed},</if>
            <if test="renewalRate != null">#{renewalRate},</if>
            <if test="abnormalLoss != null">#{abnormalLoss},</if>
            <if test="abnormal != null">#{abnormal},</if>
            <if test="cancel != null">#{cancel},</if>
            <if test="transfer != null">#{transfer},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="type != null">#{type},</if>
            <if test="queryYearMonth != null">#{queryYearMonth},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="year != null">#{year},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <insert id="insertErpAccountPerformanceList">
        insert into erp_account_performance
        (
        month_str,
        regular_renewal,
        dept_person_name,
        effective_households,
        everyman,
        small_scale,
        renew_total,
        renewed,
        renewed_detail,
        to_be_renewed,
        renewal_rate,
        abnormal_loss,
        abnormal,
        cancel,
        city_id,
        transfer,
        type,
        query_year_month,
        dept_id,
        year,
        quarter,
        user_id,
        create_user,
        update_user
        )
        values
        <foreach collection="erpAccountPerformanceList" item="item" index="index" separator=",">
          (
            #{item.monthStr},
            #{item.regularRenewal},
            #{item.deptPersonName},
            #{item.effectiveHouseholds},
            #{item.everyman},
            #{item.smallScale},
            #{item.renewTotal},
            #{item.renewed},
            #{item.renewedDetail},
            #{item.toBeRenewed},
            #{item.renewalRate},
            #{item.abnormalLoss},
            #{item.abnormal},
            #{item.cancel},
            #{item.cityId},
            #{item.transfer},
            #{item.type},
            #{item.queryYearMonth},
            #{item.deptId},
            #{item.year},
            #{item.quarter},
            #{item.userId},
            #{item.createUser},
            #{item.updateUser}
            )
        </foreach>
    </insert>

    <update id="updateErpAccountPerformance" parameterType="ErpAccountPerformance">
        update erp_account_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthStr != null">month_str = #{monthStr},</if>
            <if test="regularRenewal != null">regular_renewal = #{regularRenewal},</if>
            <if test="deptPersonName != null">dept_person_name = #{deptPersonName},</if>
            <if test="effectiveHouseholds != null">effective_households = #{effectiveHouseholds},</if>
            <if test="everyman != null">everyman = #{everyman},</if>
            <if test="smallScale != null">small_scale = #{smallScale},</if>
            <if test="renewTotal != null">renew_total = #{renewTotal},</if>
            <if test="renewed != null">renewed = #{renewed},</if>
            <if test="renewedDetail != null">renewed_detail = #{renewedDetail},</if>
            <if test="toBeRenewed != null">to_be_renewed = #{toBeRenewed},</if>
            <if test="renewalRate != null">renewal_rate = #{renewalRate},</if>
            <if test="abnormalLoss != null">abnormal_loss = #{abnormalLoss},</if>
            <if test="abnormal != null">abnormal = #{abnormal},</if>
            <if test="cancel != null">cancel = #{cancel},</if>
            <if test="transfer != null">transfer = #{transfer},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="yearMonth != null">year_month = #{yearMonth},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="year != null">year = #{year},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpAccountPerformanceById" parameterType="Long">
        delete from erp_account_performance where id = #{id}
    </delete>

    <delete id="deleteErpAccountPerformanceByIds" parameterType="String">
        delete from erp_account_performance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>