<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.OpZhuxiaoMapper">
    
    <resultMap type="OpZhuxiao" id="OpZhuxiaoResult">
        <result property="id"    column="id"    />
        <result property="opId"    column="op_id"    />
        <result property="gsZxStatus"    column="gs_zx_status"    />
        <result property="gsZxUser"    column="gs_zx_user"    />
        <result property="swZxStatus"    column="sw_zx_status"    />
        <result property="swZxUser"    column="sw_zx_user"    />
        <result property="yhZxStatus"    column="yh_zx_status"    />
        <result property="yhZxUser"    column="yh_zx_user"    />
        <result property="sbZxStatus"    column="sb_zx_status"    />
        <result property="sbZxUser"    column="sb_zx_user"    />
        <result property="gjjZxStatus"    column="gjj_zx_status"    />
        <result property="gjjZxUser"    column="gjj_zx_user"    />
        <result property="zxStatus"    column="zx_status"    />
        <result property="gsZxRemark"    column="gs_zx_remark"    />
        <result property="swZxRemark"    column="sw_zx_remark"    />
        <result property="yhZxRemark"    column="yh_zx_remark"    />
        <result property="sbZxRemark"    column="sb_zx_remark"    />
        <result property="gjjZxRemark"    column="gjj_zx_remark"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
    </resultMap>

    <sql id="selectOpZhuxiaoVo">
        select id, op_id, gs_zx_status, gs_zx_user, sw_zx_status, sw_zx_user, yh_zx_status, yh_zx_user, sb_zx_status, sb_zx_user, gjj_zx_status, gjj_zx_user, zx_status, gs_zx_remark, sw_zx_remark, yh_zx_remark, sb_zx_remark, gjj_zx_remark, created_by, updated_by from op_zhuxiao
    </sql>

    <select id="selectOpZhuxiaoList" parameterType="OpZhuxiao" resultMap="OpZhuxiaoResult">
        <include refid="selectOpZhuxiaoVo"/>
        <where>  
            <if test="opId != null "> and op_id = #{opId}</if>
            <if test="gsZxStatus != null "> and gs_zx_status = #{gsZxStatus}</if>
            <if test="gsZxUser != null "> and gs_zx_user = #{gsZxUser}</if>
            <if test="swZxStatus != null "> and sw_zx_status = #{swZxStatus}</if>
            <if test="swZxUser != null "> and sw_zx_user = #{swZxUser}</if>
            <if test="yhZxStatus != null "> and yh_zx_status = #{yhZxStatus}</if>
            <if test="yhZxUser != null "> and yh_zx_user = #{yhZxUser}</if>
            <if test="sbZxStatus != null "> and sb_zx_status = #{sbZxStatus}</if>
            <if test="sbZxUser != null "> and sb_zx_user = #{sbZxUser}</if>
            <if test="gjjZxStatus != null "> and gjj_zx_status = #{gjjZxStatus}</if>
            <if test="gjjZxUser != null "> and gjj_zx_user = #{gjjZxUser}</if>
            <if test="zxStatus != null "> and zx_status = #{zxStatus}</if>
            <if test="gsZxRemark != null  and gsZxRemark != ''"> and gs_zx_remark = #{gsZxRemark}</if>
            <if test="swZxRemark != null  and swZxRemark != ''"> and sw_zx_remark = #{swZxRemark}</if>
            <if test="yhZxRemark != null  and yhZxRemark != ''"> and yh_zx_remark = #{yhZxRemark}</if>
            <if test="sbZxRemark != null  and sbZxRemark != ''"> and sb_zx_remark = #{sbZxRemark}</if>
            <if test="gjjZxRemark != null  and gjjZxRemark != ''"> and gjj_zx_remark = #{gjjZxRemark}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectOpZhuxiaoById" parameterType="Long" resultMap="OpZhuxiaoResult">
        <include refid="selectOpZhuxiaoVo"/>
        where id = #{id}
    </select>

    <select id="selectOpZhuxiaoByOrderId" parameterType="Long" resultMap="OpZhuxiaoResult">
        <include refid="selectOpZhuxiaoVo"/>
        where op_id = #{opId}
    </select>
        
    <insert id="insertOpZhuxiao" parameterType="OpZhuxiao" useGeneratedKeys="true" keyProperty="id">
        insert into op_zhuxiao
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opId != null">op_id,</if>
            <if test="gsZxStatus != null">gs_zx_status,</if>
            <if test="gsZxUser != null">gs_zx_user,</if>
            <if test="swZxStatus != null">sw_zx_status,</if>
            <if test="swZxUser != null">sw_zx_user,</if>
            <if test="yhZxStatus != null">yh_zx_status,</if>
            <if test="yhZxUser != null">yh_zx_user,</if>
            <if test="sbZxStatus != null">sb_zx_status,</if>
            <if test="sbZxUser != null">sb_zx_user,</if>
            <if test="gjjZxStatus != null">gjj_zx_status,</if>
            <if test="gjjZxUser != null">gjj_zx_user,</if>
            <if test="zxStatus != null">zx_status,</if>
            <if test="gsZxRemark != null">gs_zx_remark,</if>
            <if test="swZxRemark != null">sw_zx_remark,</if>
            <if test="yhZxRemark != null">yh_zx_remark,</if>
            <if test="sbZxRemark != null">sb_zx_remark,</if>
            <if test="gjjZxRemark != null">gjj_zx_remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opId != null">#{opId},</if>
            <if test="gsZxStatus != null">#{gsZxStatus},</if>
            <if test="gsZxUser != null">#{gsZxUser},</if>
            <if test="swZxStatus != null">#{swZxStatus},</if>
            <if test="swZxUser != null">#{swZxUser},</if>
            <if test="yhZxStatus != null">#{yhZxStatus},</if>
            <if test="yhZxUser != null">#{yhZxUser},</if>
            <if test="sbZxStatus != null">#{sbZxStatus},</if>
            <if test="sbZxUser != null">#{sbZxUser},</if>
            <if test="gjjZxStatus != null">#{gjjZxStatus},</if>
            <if test="gjjZxUser != null">#{gjjZxUser},</if>
            <if test="zxStatus != null">#{zxStatus},</if>
            <if test="gsZxRemark != null">#{gsZxRemark},</if>
            <if test="swZxRemark != null">#{swZxRemark},</if>
            <if test="yhZxRemark != null">#{yhZxRemark},</if>
            <if test="sbZxRemark != null">#{sbZxRemark},</if>
            <if test="gjjZxRemark != null">#{gjjZxRemark},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
         </trim>
    </insert>

    <update id="updateOpZhuxiao" parameterType="OpZhuxiao">
        update op_zhuxiao
        <trim prefix="SET" suffixOverrides=",">
            <if test="opId != null">op_id = #{opId},</if>
            <if test="gsZxStatus != null">gs_zx_status = #{gsZxStatus},</if>
            <if test="gsZxUser != null">gs_zx_user = #{gsZxUser},</if>
            <if test="swZxStatus != null">sw_zx_status = #{swZxStatus},</if>
            <if test="swZxUser != null">sw_zx_user = #{swZxUser},</if>
            <if test="yhZxStatus != null">yh_zx_status = #{yhZxStatus},</if>
            <if test="yhZxUser != null">yh_zx_user = #{yhZxUser},</if>
            <if test="sbZxStatus != null">sb_zx_status = #{sbZxStatus},</if>
            <if test="sbZxUser != null">sb_zx_user = #{sbZxUser},</if>
            <if test="gjjZxStatus != null">gjj_zx_status = #{gjjZxStatus},</if>
            <if test="gjjZxUser != null">gjj_zx_user = #{gjjZxUser},</if>
            <if test="zxStatus != null">zx_status = #{zxStatus},</if>
            <if test="gsZxRemark != null">gs_zx_remark = #{gsZxRemark},</if>
            <if test="swZxRemark != null">sw_zx_remark = #{swZxRemark},</if>
            <if test="yhZxRemark != null">yh_zx_remark = #{yhZxRemark},</if>
            <if test="sbZxRemark != null">sb_zx_remark = #{sbZxRemark},</if>
            <if test="gjjZxRemark != null">gjj_zx_remark = #{gjjZxRemark},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpZhuxiaoById" parameterType="Long">
        delete from op_zhuxiao where id = #{id}
    </delete>

    <delete id="deleteOpZhuxiaoByIds" parameterType="String">
        delete from op_zhuxiao where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOpZhuxiaoListByOrderIds" parameterType="String" resultMap="OpZhuxiaoResult">
        <include refid="selectOpZhuxiaoVo"/>
        WHERE op_id in
        <foreach item="orderId" collection="orderIds.split(',')" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
</mapper>