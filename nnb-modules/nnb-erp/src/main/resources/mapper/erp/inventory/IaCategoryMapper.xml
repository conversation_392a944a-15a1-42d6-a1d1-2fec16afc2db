<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.inventory.IaCategoryMapper">

    <resultMap type="com.nnb.erp.domain.inventory.IaCategory" id="IaCategoryResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="level"    column="level"    />
        <result property="node"    column="node"    />
        <result property="parentId"    column="parent_id"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectIaCategoryVo">
        select id, company_id, code, name, level, node, parent_id, type from ia_category
    </sql>

    <select id="selectIaCategoryList" parameterType="IaCategory" resultMap="IaCategoryResult">
        <include refid="selectIaCategoryVo"/>
        <where>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="node != null "> and node = #{node}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>

    <select id="selectIaCategoryById" parameterType="Long" resultMap="IaCategoryResult">
        <include refid="selectIaCategoryVo"/>
        where id = #{id}
    </select>
    <select id="selectIaCategoryTreeList" resultType="com.nnb.erp.domain.inventory.IaCategory">
        <include refid="selectIaCategoryVo"/>
        where (`type` = #{iaCategory.type} and company_id = #{iaCategory.companyId}) or id = 1
    </select>
    <select id="selectChildList" resultMap="IaCategoryResult">
        <include refid="selectIaCategoryVo"/>
        where company_id = #{iaCategory.companyId}
            and `type` = #{iaCategory.type}
            and parent_id = #{iaCategory.parentId}
    </select>
    <select id="countByName" resultType="java.lang.Integer">
        select count(id) from ia_category
        where `type` = #{iaCategory.type}
            and company_id = #{iaCategory.companyId}
            and `name` = #{iaCategory.name}
    </select>

    <select id="selectIdsByName" resultType="java.lang.Long">
        select id from ia_category
        where `type` = #{iaCategory.type}
            and company_id = #{iaCategory.companyId}
            and `name` = #{iaCategory.name}
    </select>
    <select id="selectByNameList" resultMap="IaCategoryResult">
        <include refid="selectIaCategoryVo"/>
        where `type` = #{type}
            and company_id = #{companyId}
            and`name` in (
                <foreach collection="categoryNames" item="name" separator=",">
                    #{name}
                </foreach>
            )
    </select>

    <insert id="insertIaCategory" parameterType="IaCategory" useGeneratedKeys="true" keyProperty="id">
        insert into ia_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="level != null">level,</if>
            <if test="node != null">node,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="level != null">#{level},</if>
            <if test="node != null">#{node},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateIaCategory" parameterType="IaCategory">
        update ia_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="node != null">node = #{node},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIaCategoryById" parameterType="Long">
        delete from ia_category where id = #{id}
    </delete>

    <delete id="deleteIaCategoryByIds" parameterType="String">
        delete from ia_category where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </delete>
</mapper>
