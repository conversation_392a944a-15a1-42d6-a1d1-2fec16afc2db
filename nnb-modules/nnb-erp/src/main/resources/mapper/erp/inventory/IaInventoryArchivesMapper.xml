<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.inventory.IaInventoryArchivesMapper">

    <resultMap type="com.nnb.erp.domain.inventory.IaInventoryArchives" id="IaInventoryArchivesResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="iaCategoryId"    column="ia_category_id"    />
        <result property="iaCategoryCode"    column="ia_category_code"    />
        <result property="iaCategoryName"    column="ia_category_name"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="model"    column="model"    />
        <result property="iaUnitId"    column="ia_unit_id"    />
        <result property="iaUnitCode"    column="ia_unit_code"    />
        <result property="iaUnitName"    column="ia_unit_name"    />
        <result property="pricingMethod"    column="pricing_method"    />
        <result property="productId"    column="product_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="productImageUrl"    column="product_image_url"    />
    </resultMap>

    <sql id="selectIaInventoryArchivesVo">
        select id, company_id, ia_category_id, ia_category_code, ia_category_name, warehouse_id, warehouse_code, warehouse_name, code, name, model, ia_unit_id, ia_unit_code, ia_unit_name, pricing_method, product_id, create_time, product_image_url from ia_inventory_archives
    </sql>

    <select id="selectIaInventoryArchivesList" parameterType="IaInventoryArchives" resultMap="IaInventoryArchivesResult">
        <include refid="selectIaInventoryArchivesVo"/>
        <where>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="iaCategoryId != null "> and ia_category_id = #{iaCategoryId}</if>
            <if test="iaCategoryCode != null  and iaCategoryCode != ''"> and ia_category_code = #{iaCategoryCode}</if>
            <if test="iaCategoryName != null  and iaCategoryName != ''"> and ia_category_name like concat('%', #{iaCategoryName}, '%')</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="iaUnitId != null "> and ia_unit_id = #{iaUnitId}</if>
            <if test="iaUnitCode != null  and iaUnitCode != ''"> and ia_unit_code = #{iaUnitCode}</if>
            <if test="iaUnitName != null  and iaUnitName != ''"> and ia_unit_name like concat('%', #{iaUnitName}, '%')</if>
            <if test="pricingMethod != null "> and pricing_method = #{pricingMethod}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="keyWord != null and keyWord != ''">
                and (`code` like concat('%', #{keyWord}, '%') or `name` like concat('%', #{keyWord}, '%'))
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in (
                    <foreach collection="ids" item="inventoryId" separator=",">
                        #{inventoryId}
                    </foreach>
                )
            </if>
            <if test="iaCategoryIds != null and iaCategoryIds.size() > 0">
                and ia_category_id in (
                    <foreach collection="iaCategoryIds" item="iaCategoryId" separator=",">
                        #{iaCategoryId}
                    </foreach>
                )
            </if>
            <if test="productIds != null and productIds.size() > 0">
                and product_id in (
                <foreach collection="productIds" item="productId" separator=",">
                    #{productId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectIaInventoryArchivesById" parameterType="Long" resultMap="IaInventoryArchivesResult">
        <include refid="selectIaInventoryArchivesVo"/>
        where id = #{id}
    </select>

    <select id="selectIaInventoryArchivesByIds" parameterType="Long" resultMap="IaInventoryArchivesResult">
        <include refid="selectIaInventoryArchivesVo"/>
        where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>
    <select id="selectByIaCategoryId" resultType="com.nnb.erp.domain.inventory.IaInventoryArchives">
        <include refid="selectIaInventoryArchivesVo"/>
        where ia_category_id in (
            <foreach collection="iaCategoryIds" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>

    <select id="selectByIaCategoryIdAndKeyWord" resultType="com.nnb.erp.domain.inventory.IaInventoryArchives">
        <include refid="selectIaInventoryArchivesVo"/>
        <where>
            <if test="iaCategoryIds != null and iaCategoryIds.size() > 0">
                and ia_category_id in (
                <foreach collection="iaCategoryIds" item="id" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (
                    `code` like concat('%', #{keyWord}, '%')
                    or `name` like concat('%', #{keyWord}, '%')
                    or `model` like concat('%', #{keyWord}, '%')
                )
            </if>
        </where>
    </select>
    <select id="countByNameAndModel" resultType="java.lang.Integer">
        select count(id) from ia_inventory_archives
        where `name` = #{search.name}
            and model = #{search.model}
            and company_id = #{search.companyId}
    </select>
    <select id="selectIdsByName" resultType="java.lang.Long">
        select id from ia_inventory_archives
        where `name` = #{search.name}
            and model = #{search.model}
            and company_id = #{search.companyId}
    </select>

    <insert id="insertIaInventoryArchives" parameterType="IaInventoryArchives" useGeneratedKeys="true" keyProperty="id">
        insert into ia_inventory_archives
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="iaCategoryId != null">ia_category_id,</if>
            <if test="iaCategoryCode != null and iaCategoryCode != ''">ia_category_code,</if>
            <if test="iaCategoryName != null and iaCategoryName != ''">ia_category_name,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="model != null and model != ''">model,</if>
            <if test="iaUnitId != null">ia_unit_id,</if>
            <if test="iaUnitCode != null and iaUnitCode != ''">ia_unit_code,</if>
            <if test="iaUnitName != null and iaUnitName != ''">ia_unit_name,</if>
            <if test="pricingMethod != null">pricing_method,</if>
            <if test="productId != null">product_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="productImageUrl != null">product_image_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="iaCategoryId != null">#{iaCategoryId},</if>
            <if test="iaCategoryCode != null and iaCategoryCode != ''">#{iaCategoryCode},</if>
            <if test="iaCategoryName != null and iaCategoryName != ''">#{iaCategoryName},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="model != null and model != ''">#{model},</if>
            <if test="iaUnitId != null">#{iaUnitId},</if>
            <if test="iaUnitCode != null and iaUnitCode != ''">#{iaUnitCode},</if>
            <if test="iaUnitName != null and iaUnitName != ''">#{iaUnitName},</if>
            <if test="pricingMethod != null">#{pricingMethod},</if>
            <if test="productId != null">#{productId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="productImageUrl != null">#{productImageUrl},</if>
         </trim>
    </insert>

    <update id="updateIaInventoryArchives" parameterType="IaInventoryArchives">
        update ia_inventory_archives
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="iaCategoryId != null">ia_category_id = #{iaCategoryId},</if>
            <if test="iaCategoryCode != null and iaCategoryCode != ''">ia_category_code = #{iaCategoryCode},</if>
            <if test="iaCategoryName != null and iaCategoryName != ''">ia_category_name = #{iaCategoryName},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="model != null and model != ''">model = #{model},</if>
            <if test="iaUnitId != null">ia_unit_id = #{iaUnitId},</if>
            <if test="iaUnitCode != null and iaUnitCode != ''">ia_unit_code = #{iaUnitCode},</if>
            <if test="iaUnitName != null and iaUnitName != ''">ia_unit_name = #{iaUnitName},</if>
            <if test="pricingMethod != null">pricing_method = #{pricingMethod},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="productImageUrl != null">product_image_url = #{productImageUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCategoryId">
        update ia_inventory_archives
        set ia_category_id = #{iaCategoryId},
            ia_category_code = #{iaCategoryCode},
            ia_category_name = #{iaCategoryName}
        where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </update>

    <update id="updateCategoryName">
        update ia_inventory_archives
        set ia_category_name = #{iaCategoryName}
        where id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
    <update id="updateUnitName">
        update ia_inventory_archives
        set ia_unit_name = #{iaUnitName}
        where id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <delete id="deleteIaInventoryArchivesById" parameterType="Long">
        delete from ia_inventory_archives where id = #{id}
    </delete>

    <delete id="deleteIaInventoryArchivesByIds" parameterType="String">
        delete from ia_inventory_archives where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </delete>
</mapper>
