<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.inventory.IaInventoryWarehouseMapper">

    <resultMap type="com.nnb.erp.domain.inventory.IaInventoryWarehouse" id="IaInventoryWarehouseResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="inventoryId"    column="inventory_id"    />
        <result property="inventoryWarehouseIndex"    column="inventory_warehouse_index"    />
        <result property="inQty"    column="in_qty"    />
        <result property="aveMarketPrice"    column="ave_market_price"    />
        <result property="avePurchasePrice"    column="ave_purchase_price"    />
        <result property="sumInQty"    column="sum_in_qty"    />
    </resultMap>

    <sql id="selectIaInventoryWarehouseVo">
        select id, company_id, warehouse_id, inventory_id, inventory_warehouse_index, in_qty, ave_market_price, ave_purchase_price, sum_in_qty from ia_inventory_warehouse
    </sql>

    <select id="selectIaInventoryWarehouseList" parameterType="IaInventoryWarehouse" resultMap="IaInventoryWarehouseResult">
        <include refid="selectIaInventoryWarehouseVo"/>
        <where>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="inventoryId != null "> and inventory_id = #{inventoryId}</if>
            <if test="inventoryWarehouseIndex != null  and inventoryWarehouseIndex != ''"> and inventory_warehouse_index = #{inventoryWarehouseIndex}</if>
            <if test="inQty != null "> and in_qty = #{inQty}</if>
            <if test="inWareIndexList != null and inWareIndexList.size() > 0">
                and inventory_warehouse_index in (
                    <foreach collection="inWareIndexList" item="index" separator=",">
                        #{index}
                    </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectIaInventoryWarehouseById" parameterType="Long" resultMap="IaInventoryWarehouseResult">
        <include refid="selectIaInventoryWarehouseVo"/>
        where id = #{id}
    </select>

    <insert id="insertIaInventoryWarehouse" parameterType="IaInventoryWarehouse" useGeneratedKeys="true" keyProperty="id">
        insert into ia_inventory_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="inventoryId != null">inventory_id,</if>
            <if test="inventoryWarehouseIndex != null and inventoryWarehouseIndex != ''">inventory_warehouse_index,</if>
            <if test="inQty != null">in_qty,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="inventoryId != null">#{inventoryId},</if>
            <if test="inventoryWarehouseIndex != null and inventoryWarehouseIndex != ''">#{inventoryWarehouseIndex},</if>
            <if test="inQty != null">#{inQty},</if>
        </trim>
    </insert>

    <update id="updateIaInventoryWarehouse" parameterType="IaInventoryWarehouse">
        update ia_inventory_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="inventoryId != null">inventory_id = #{inventoryId},</if>
            <if test="inventoryWarehouseIndex != null and inventoryWarehouseIndex != ''">inventory_warehouse_index = #{inventoryWarehouseIndex},</if>
            <if test="inQty != null">in_qty = #{inQty},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIaInventoryWarehouseById" parameterType="Long">
        delete from ia_inventory_warehouse where id = #{id}
    </delete>

    <delete id="deleteIaInventoryWarehouseByIds" parameterType="String">
        delete from ia_inventory_warehouse where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </delete>

    <select id="selectIntervalList" parameterType="com.nnb.erp.domain.inventory.InventoryStockSearch" resultMap="IaInventoryWarehouseResult">
        <include refid="selectIaInventoryWarehouseVo"/>
        <where>
            <if test="warehouseId != null">
                and warehouse_id &gt;= #{warehouseId}
            </if>
            <if test="minMarketPrice != null">
                and ave_market_price &gt;= #{minMarketPrice}
            </if>
            <if test="maxMarketPrice != null">
                and ave_market_price &lt;= #{minMarketPrice}
            </if>
            <if test="minPurchasePrice != null">
                and ave_purchase_price &gt;= #{minPurchasePrice}
            </if>
            <if test="maxPurchasePrice != null">
                and ave_purchase_price &lt;= #{maxPurchasePrice}
            </if>
            <if test="minQuantity != null">
                and sum_in_qty &gt;= #{minQuantity}
            </if>
            <if test="maxQuantity != null">
                and sum_in_qty &lt;= #{maxQuantity}
            </if>
            <if test="warehouseId == null and showStock == 0">
                and sum_in_qty != 0
            </if>
            <if test="warehouseId != null and showStock == 0">
                and in_qty != 0
            </if>
        </where>
    </select>

    <update id="updatePriceAndSum" parameterType="IaInventoryWarehouse">
        update ia_inventory_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="aveMarketPrice != null">ave_market_price = #{aveMarketPrice},</if>
            <if test="avePurchasePrice != null">ave_purchase_price = #{avePurchasePrice},</if>
            <if test="sumInQty != null">sum_in_qty = #{sumInQty},</if>
        </trim>
        where inventory_id = #{inventoryId}
    </update>

    <update id="updateQtyByInventoryIdAndWarehouseId">
        update ia_inventory_warehouse
        set in_qty = #{inQty}
        where inventory_id =#{inventoryId} and warehouse_id = #{warehouseId}
    </update>

    <select id="countInventory" resultType="com.nnb.erp.domain.inventory.InventoryCount">
        select count(inventory_id) as inventoryNum,
               sum(sum_in_qty) as inventoryQtySum
        from (select inventory_id, sum_in_qty
              from ia_inventory_warehouse
              <where>
                  <if test="showStock == 0">
                      sum_in_qty != 0
                  </if>
                  <if test="showStock == 1">
                      1 = 1
                  </if>
              </where>
              group by inventory_id
        ) temp
    </select>

</mapper>
