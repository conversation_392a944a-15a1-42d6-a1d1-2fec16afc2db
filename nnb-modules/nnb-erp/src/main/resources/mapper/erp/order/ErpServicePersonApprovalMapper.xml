<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.order.ErpServicePersonApprovalMapper">
    
    <resultMap type="ErpServicePersonApproval" id="ErpServicePersonApprovalResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="orderId"    column="order_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createAt"    column="create_at"    />
        <result property="updateAt"    column="update_at"    />
        <result property="version"    column="version"    />
        <result property="type"    column="type"    />
        <result property="sort"    column="sort"    />
        <result property="approveId"    column="approve_id"    />
    </resultMap>

    <sql id="selectErpServicePersonApprovalVo">
        select id, user_id, status, order_id, create_by, update_by, create_at, update_at, version, sort, approve_id from erp_service_person_approval
    </sql>

    <select id="selectErpServicePersonApprovalList" parameterType="ErpServicePersonApproval" resultMap="ErpServicePersonApprovalResult">
        <include refid="selectErpServicePersonApprovalVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="createAt != null "> and create_at = #{createAt}</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            <if test="approveId != null "> and approve_id = #{approveId}</if>
        </where>
    </select>
    
    <select id="selectErpServicePersonApprovalById" parameterType="Long" resultMap="ErpServicePersonApprovalResult">
        <include refid="selectErpServicePersonApprovalVo"/>
        where id = #{id}
    </select>
    <select id="selectErpServicePersonApprovalListByOrderId" resultType="com.nnb.erp.domain.order.ErpServicePersonApproval">
        select id, user_id, status, type, order_id, create_by, update_by, create_at, update_at, MAX(version) as version from erp_service_person_approval
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="approveId != null "> and approve_id = #{approveId}</if>
        </where>
        group by version
        order by create_at DESC
    </select>

    <insert id="insertErpServicePersonApproval" parameterType="ErpServicePersonApproval">
        insert into erp_service_person_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="version != null">version,</if>
            <if test="sort != null">sort,</if>
            <if test="orderId != null">order_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createAt != null">create_at,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="approveId != null">approve_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="version != null">#{version},</if>
            <if test="sort != null">#{sort},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="approveId != null">#{approveId},</if>
         </trim>
    </insert>

    <update id="updateErpServicePersonApproval" parameterType="ErpServicePersonApproval">
        update erp_service_person_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateErpServicePersonApprovalByOrderId" parameterType="ErpServicePersonApproval">
        update erp_service_person_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where order_id = #{orderId} and version = #{version}
    </update>

    <delete id="deleteErpServicePersonApprovalById" parameterType="Long">
        delete from erp_service_person_approval where id = #{id}
    </delete>

    <delete id="deleteErpServicePersonApprovalByIds" parameterType="String">
        delete from erp_service_person_approval where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateApprovalByOrderIdAndStatus" parameterType="ErpServicePersonApproval">
        update erp_service_person_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where order_id = #{orderId} and status = 0
    </update>
</mapper>