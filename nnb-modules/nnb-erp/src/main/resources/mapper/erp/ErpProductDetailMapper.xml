<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpProductDetailMapper">

    <resultMap type="ErpProductDetail" id="ErpProductDetailResult">
        <result property="numProductId"    column="num_product_id"    />
        <result property="numNameId"    column="num_name_id"    />
        <result property="numServiceId"    column="num_service_id"    />
        <result property="numUnitId"    column="num_unit_id"    />
        <result property="numPrice"    column="num_price"    />
        <result property="numIsNeedContract"    column="num_is_need_contract"    />
        <result property="numIsAppletShow"    column="num_is_applet_show"    />
        <result property="numIsUp"    column="num_is_up"    />
        <result property="numIsCheck"    column="num_is_check"    />
        <result property="vcRemark"    column="vc_remark"    />
        <result property="numCreateUserid"    column="num_create_userid"    />
        <result property="datCreateTime"    column="dat_create_time"    />
        <result property="numLastUpdUserid"    column="num_last_upd_userid"    />
        <result property="datLastUpd"    column="dat_last_upd"    />
        <result property="serviceTypeId"    column="service_type_id"    />
        <result property="isOldData"    column="is_old_data"    />
        <result property="numProductIdOld"    column="num_product_id_old"    />
        <result property="isAddress"    column="is_address"    />
        <result property="isSend"    column="is_send"    />
        <result property="serviceMainAging"    column="service_main_aging"    />
        <result property="costDetailed"    column="cost_detailed"    />
        <result property="standardPrice"    column="standard_price"    />
        <result property="noContractNeededDeptId"    column="no_contract_needed_dept_id"    />
        <result property="invoiceCost"    column="invoice_cost"    />
        <result property="invoicePerformance"    column="invoice_performance"    />
        <result property="onlineId"    column="online_id"    />
        <result property="selfSupport"    column="self_support"    />
        <result property="approveInfoId"    column="approve_info_id"    />
        <result property="earlyWarning"    column="early_warning"    />
        <result property="earlyWarningUrl"    column="early_warning_url"    />
        <result property="costPrice"    column="cost_price"    />
    </resultMap>

    <sql id="selectErpProductDetailVo">
        select num_product_id, is_address, num_name_id, num_service_id, num_unit_id, num_price, num_is_need_contract, num_is_applet_show, num_is_up, num_is_check, vc_remark, num_create_userid, dat_create_time, num_last_upd_userid, dat_last_upd, service_type_id,
               is_send,service_main_aging,cost_detailed,standard_price,no_contract_needed_dept_id,invoice_cost,invoice_performance, online_id, self_support, approve_info_id, early_warning, early_warning_url, cost_price from erp_product_detail
    </sql>

    <select id="selectErpProductDetailList" parameterType="ErpProductDetail" resultMap="ErpProductDetailResult">
        <include refid="selectErpProductDetailVo"/>
        <where>
            <if test="numProductId != null">and num_product_id = #{numProductId}</if>
            <if test="numNameId != null "> and num_name_id = #{numNameId}</if>
            <if test="numServiceId != null "> and num_service_id = #{numServiceId}</if>
            <if test="numUnitId != null "> and num_unit_id = #{numUnitId}</if>
            <if test="numIsUp != null "> and num_is_up = #{numIsUp}</if>
            <if test="isAddress != null "> and is_address = #{isAddress}</if>
            <if test="isOldData != null "> and is_old_data = #{isOldData}</if>
        </where>
    </select>

    <select id="selectErpProductDetailByNumProductId" parameterType="Long" resultMap="ErpProductDetailResult">
        <include refid="selectErpProductDetailVo"/>
        where num_product_id = #{numProductId}
    </select>

    <insert id="insertErpProductDetail" parameterType="com.nnb.erp.domain.ErpProductDetail"  useGeneratedKeys="true" keyProperty="numProductId">
        insert into erp_product_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numProductId != null">num_product_id,</if>
            <if test="numNameId != null">num_name_id,</if>
            <if test="numServiceId != null">num_service_id,</if>
            <if test="numUnitId != null">num_unit_id,</if>
            <if test="numPrice != null">num_price,</if>
            <if test="numIsNeedContract != null">num_is_need_contract,</if>
            <if test="numIsAppletShow != null">num_is_applet_show,</if>
            <if test="numIsUp != null">num_is_up,</if>
            <if test="numIsCheck != null">num_is_check,</if>
            <if test="vcRemark != null">vc_remark,</if>
            <if test="numCreateUserid != null">num_create_userid,</if>
            <if test="datCreateTime != null">dat_create_time,</if>
            <if test="numLastUpdUserid != null">num_last_upd_userid,</if>
            <if test="datLastUpd != null">dat_last_upd,</if>
            <if test="serviceTypeId !=null">service_type_id,</if>
            <if test="isOldData !=null">is_old_data,</if>
            <if test="numProductIdOld !=null">num_product_id_old,</if>
            <if test="isAddress !=null">is_address,</if>
            <if test="costPrice !=null">cost_price,</if>
            <if test="isSend !=null">is_send,</if>
            <if test="serviceMainAging !=null">service_main_aging,</if>
            <if test="costDetailed !=null">cost_detailed,</if>
            <if test="standardPrice !=null">standard_price,</if>
            <if test="financeClassificationId !=null">finance_classification_id,</if>
            <if test="verbalTrick !=null">verbal_trick,</if>
            <if test="noContractNeededDeptId !=null">no_contract_needed_dept_id,</if>
            <if test="outCost !=null">out_cost,</if>
            <if test="kpProduct !=null">kp_product,</if>
            <if test="kpSupport !=null">kp_support,</if>
            <if test="onlineId !=null">online_id,</if>
            <if test="selfSupport !=null">self_support,</if>
            <if test="earlyWarning !=null">early_warning,</if>
            <if test="earlyWarningUrl !=null">early_warning_url,</if>
            <if test="approveInfoId !=null">approve_info_id,</if>
            <if test="qualificationsExtension !=null">qualifications_extension,</if>
            <if test="annualInspection !=null">annual_inspection,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numProductId != null">#{numProductId},</if>
            <if test="numNameId != null">#{numNameId},</if>
            <if test="numServiceId != null">#{numServiceId},</if>
            <if test="numUnitId != null">#{numUnitId},</if>
            <if test="numPrice != null">#{numPrice},</if>
            <if test="numIsNeedContract != null">#{numIsNeedContract},</if>
            <if test="numIsAppletShow != null">#{numIsAppletShow},</if>
            <if test="numIsUp != null">#{numIsUp},</if>
            <if test="numIsCheck != null">#{numIsCheck},</if>
            <if test="vcRemark != null">#{vcRemark},</if>
            <if test="numCreateUserid != null">#{numCreateUserid},</if>
            <if test="datCreateTime != null">#{datCreateTime},</if>
            <if test="numLastUpdUserid != null">#{numLastUpdUserid},</if>
            <if test="datLastUpd != null">#{datLastUpd},</if>
            <if test="serviceTypeId !=null">#{serviceTypeId},</if>
            <if test="isOldData != null">#{isOldData},</if>
            <if test="numProductIdOld != null">#{numProductIdOld},</if>
            <if test="isAddress != null">#{isAddress},</if>
            <if test="costPrice != null">#{costPrice},</if>
            <if test="isSend != null">#{isSend},</if>
            <if test="serviceMainAging != null">#{serviceMainAging},</if>
            <if test="costDetailed != null">#{costDetailed},</if>
            <if test="standardPrice != null">#{standardPrice},</if>
            <if test="financeClassificationId != null">#{financeClassificationId},</if>
            <if test="verbalTrick != null">#{verbalTrick},</if>
            <if test="noContractNeededDeptId != null">#{noContractNeededDeptId},</if>
            <if test="outCost != null">#{outCost},</if>
            <if test="kpProduct != null">#{kpProduct},</if>
            <if test="kpSupport != null">#{kpSupport},</if>
            <if test="onlineId != null">#{onlineId},</if>
            <if test="selfSupport != null">#{selfSupport},</if>
            <if test="earlyWarning != null">#{earlyWarning},</if>
            <if test="earlyWarningUrl != null">#{earlyWarningUrl},</if>
            <if test="approveInfoId != null">#{approveInfoId},</if>
            <if test="qualificationsExtension != null">#{qualificationsExtension},</if>
            <if test="annualInspection != null">#{annualInspection},</if>
        </trim>
    </insert>

    <update id="updateErpProductDetail" parameterType="com.nnb.erp.domain.ErpProductDetail" useGeneratedKeys="true" keyProperty="numProductId">
        update erp_product_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="numNameId != null">num_name_id = #{numNameId},</if>
            <if test="numServiceId != null">num_service_id = #{numServiceId},</if>
            <if test="numUnitId != null">num_unit_id = #{numUnitId},</if>
            <if test="numPrice != null">num_price = #{numPrice},</if>
            <if test="numIsNeedContract != null">num_is_need_contract = #{numIsNeedContract},</if>
            <if test="numIsAppletShow != null">num_is_applet_show = #{numIsAppletShow},</if>
            <if test="numIsUp != null">num_is_up = #{numIsUp},</if>
            <if test="numIsCheck != null">num_is_check = #{numIsCheck},</if>
            <if test="vcRemark != null">vc_remark = #{vcRemark},</if>
            <if test="numCreateUserid != null">num_create_userid = #{numCreateUserid},</if>
            <if test="datCreateTime != null">dat_create_time = #{datCreateTime},</if>
            <if test="numLastUpdUserid != null">num_last_upd_userid = #{numLastUpdUserid},</if>
            <if test="datLastUpd != null">dat_last_upd = #{datLastUpd},</if>
            <if test="isOldData != null">is_old_data = #{isOldData},</if>
            <if test="serviceTypeId != null">service_type_id = #{serviceTypeId},</if>
            <if test="numProductIdOld != null">num_product_id_old = #{numProductIdOld},</if>
            <if test="isAddress != null">is_address = #{isAddress},</if>
            <if test="costPrice != null">cost_price = #{costPrice},</if>
            <if test="isSend != null">is_send = #{isSend},</if>
            <if test="costDetailed != null">cost_detailed = #{costDetailed},</if>
            <if test="standardPrice != null">standard_price = #{standardPrice},</if>
            <if test="financeClassificationId != null">finance_classification_id = #{financeClassificationId},</if>
            <if test="verbalTrick != null">verbal_trick = #{verbalTrick},</if>
            <if test="noContractNeededDeptId != null">no_contract_needed_dept_id = #{noContractNeededDeptId},</if>
            <if test="outCost != null">out_cost = #{outCost},</if>
            <if test="kpProduct != null">kp_product = #{kpProduct},</if>
            <if test="kpSupport != null">kp_support = #{kpSupport},</if>
            <if test="onlineId != null">online_id = #{onlineId},</if>
            <if test="selfSupport != null">self_support = #{selfSupport},</if>
            <if test="earlyWarning != null">early_warning = #{earlyWarning},</if>
            <if test="earlyWarningUrl != null">early_warning_url = #{earlyWarningUrl},</if>
            <if test="approveInfoId != null">approve_info_id = #{approveInfoId},</if>
            <if test="qualificationsExtension != null">qualifications_extension = #{qualificationsExtension},</if>
            <if test="annualInspection != null">annual_inspection = #{annualInspection},</if>
            service_main_aging = #{serviceMainAging},
        </trim>
        where num_product_id = #{numProductId}
    </update>

    <delete id="deleteErpProductDetailByNumProductId" parameterType="Long">
        delete from erp_product_detail where num_product_id = #{numProductId}
    </delete>

    <delete id="deleteErpProductDetailByNumProductIds" parameterType="String">
        delete from erp_product_detail where num_product_id in
        <foreach item="numProductId" collection="array" open="(" separator="," close=")">
            #{numProductId}
        </foreach>
    </delete>


    <select id="selectErpProductList" parameterType="ErpProductDetailListDto" resultType="com.nnb.erp.domain.vo.ErpProductDetailListVo">
        select  detail.num_product_id             as numProductId,
                class.vc_classification_name      as vcClassificationName,
                class.num_classification_id       as numClassificationId,
                detail.approve_info_id            as approveInfoId,
                detail.num_name_id                as numNameId,
                type.num_type_id                  as typeId,
                type.vc_type_name                 as vcTypeName,
                name.vc_product_name              as vcProductName,
                service.name                      as serviceName,
                detail.service_type_id            as serviceTypeId,
                tax.vc_tax_names                  as vcTaxNames,
                util.vc_unit_name                 as vcUnitName,
                region.vc_area_names              as vcAreaNames,
                configuration.id                    as configurationId,
                configuration.product_price       as numPrice,
                configuration.activity_start_time as activityStartTime,
                configuration.activity_end_time   as activityEndTime,
                configuration.discount_amount     as discountAmount,
                detail.num_is_up                  as numIsUp,
                detail.num_is_check               as numIsCheck,
                detail.dat_create_time            as datCreateTime,
                detail.vc_remark                  as vcemark,
                eps.vc_service_name               as vcServiceName,
                IFNULL(detail.cost_price,0)       as costPrice,
                IFNULL(detail.out_cost,0)         as outCost,
                configuration.number_commission     as numberCommission,
                configuration.number_commission_type     as numberCommissionType,
                configuration.account_commission     as accountCommission,
                configuration.account_commission_type     as accountCommissionType,
                configuration.lead_custom_commission     as leadCustomCommission,
                configuration.lead_custom_commission_type     as leadCustomCommissionType,
                configuration.sale_commission     as saleCommission,
                configuration.sale_commission_type     as saleCommissionType,
                configuration.increment_commission     as incrementCommission,
                configuration.increment_commission_type     as incrementCommissionType,
                configuration.address_commission     as addressCommission,
                configuration.address_commission_type     as addressCommissionType
        from erp_product_detail detail
            left join erp_product_name name on detail.num_name_id = name.num_name_id
            left join erp_product_type type on name.num_type_id = type.num_type_id
            left join erp_product_classification class on type.num_classification_id = class.num_classification_id
            left join erp_product_service eps on detail.num_service_id = eps.num_service_id
            left join s_config_service_type service on detail.service_type_id = service.id
            left join erp_product_configuration configuration on configuration.product_id = detail.num_product_id
            left join (select r.num_product_id num_product_id, group_concat(distinct t.vc_tax_name) vc_tax_names
        from erp_product_tax_relation r
        left join erp_product_tax_dict t on r.num_tax_id = t.num_tax_id
        group by r.num_product_id) tax on detail.num_product_id = tax.num_product_id
        left join erp_product_unit_dict util on detail.num_unit_id = util.num_unit_id
        left join
        (select r.num_product_id num_product_id, group_concat(distinct a.region_names) vc_area_names
        from erp_product_area_relation r
        left join
        (select s.id, concat_ws('-', o.title, t.title, s.title) region_names
        from com_dict_region s
        left join com_dict_region t on s.parent_id = t.id
        left join com_dict_region o on t.parent_id = o.id
        where s.level = 3
        union all
        select t.id, concat_ws('-', o.title, t.title) region_names
        from  com_dict_region t
        left join com_dict_region o on t.parent_id = o.id
        where t.level = 2
        union all
        select o.id,  o.title region_names
        from  com_dict_region o
        where o.level = 1) a
        on r.num_area_id = a.id
        group by r.num_product_id) region on detail.num_product_id = region.num_product_id
--         left join erp_product_area_relation epar on detail.num_product_id = epar.num_product_id
--         left join com_dict_region cdr on epar.num_area_id = cdr.id
        <if test="(numAreaId != null and numAreaId != '') or (numAreaIdList != null and numAreaIdList.size() > 0)">
        inner join (
            select distinct par.num_product_id from erp_product_area_relation par
            left join com_dict_region cdr on par.num_area_id = cdr.id
            where
                <if test="numAreaId != null and numAreaId != ''">par.num_area_id = #{numAreaId} or cdr.parent_id = #{numAreaId}</if>
                <if test="numAreaIdList != null and numAreaIdList.size() > 0">
                    par.num_area_id in
                    <foreach item="areaId" collection="numAreaIdList" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                    or cdr.parent_id in
                    <foreach item="areaId" collection="numAreaIdList" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                </if>
            )
        pr on detail.num_product_id = pr.num_product_id
        </if>
        <if test="(numTaxId != null and numTaxId != '') or (numTaxIdList != null and numTaxIdList.size() > 0 )">
        inner join (
            select distinct num_product_id from erp_product_tax_relation where
            <if test="numTaxId != null and numTaxId != ''">num_tax_id = #{numTaxId}</if>
            <if test="numTaxIdList != null and numTaxIdList.size() > 0">
                num_tax_id in
                <foreach item="taxId" collection="numTaxIdList" open="(" separator="," close=")">
                    #{taxId}
                </foreach>
            </if>
            ) ptr on detail.num_product_id = ptr.num_product_id
        </if>
        <where>
            detail.is_old_data = 0
            <if test="numProductId != null "> and detail.num_product_id = #{numProductId}</if>
            <if test="vcProductName != null and vcProductName != ''"> and name.vc_product_name like concat('%', #{vcProductName}, '%')</if>
            <if test="numClassificationId != null "> and class.num_classification_id = #{numClassificationId}</if>
            <if test="numTypeId != null "> and type.num_type_id = #{numTypeId}</if>
            <if test="numNameId != null "> and name.num_name_id = #{numNameId}</if>
            <if test="numNameIdList != null and numNameIdList.size > 0">
                and name.num_name_id in
                <foreach item="nameId" collection="numNameIdList" open="(" separator="," close=")">
                    #{nameId}
                </foreach>
            </if>
            <if test="numUnitId != null "> and detail.num_unit_id = #{numUnitId}</if>
            <if test="numIsUp != null "> and detail.num_is_up = #{numIsUp}</if>
            <if test="numIsCheck != null "> and detail.num_is_check = #{numIsCheck}</if>
            <if test="vcServiceName !=null">and eps.vc_service_name like concat('%', #{vcServiceName}, '%')</if>
            <if test="numPrice !=null">and configuration.product_price=#{numPrice}</if>
            <if test="deptId !=null">and FIND_IN_SET(#{deptId}, configuration.dept_id)</if>
            <if test="numProductId !=null"> and detail.num_product_id=#{numProductId}</if>
            <if test="configurationAppletShow !=null"> and configuration.num_is_applet_show=#{configurationAppletShow}</if>

        </where>
    </select>

    <select id="selectErpProductListByNumber" parameterType="ErpProductDetailListDto" resultType="com.nnb.erp.domain.vo.ErpProductDetailListVo">
        select  detail.num_product_id             as numProductId,
        class.vc_classification_name      as vcClassificationName,
        detail.num_name_id                as numNameId,
        type.num_type_id                  as typeId,
        type.vc_type_name                 as vcTypeName,
        name.vc_product_name              as vcProductName,
        service.name                      as serviceName,
        detail.service_type_id            as serviceTypeId,
        tax.vc_tax_names                  as vcTaxNames,
        util.vc_unit_name                 as vcUnitName,
        region.vc_area_names              as vcAreaNames,
        elp.product_price       as numPrice,
        detail.num_is_up                  as numIsUp,
        detail.num_is_check               as numIsCheck,
        detail.dat_create_time            as datCreateTime,
        detail.vc_remark                  as vcemark,
        eps.vc_service_name               as vcServiceName,
        detail.cost_price                 as costPrice
        from erp_product_detail detail
        left join erp_product_name name on detail.num_name_id = name.num_name_id
        left join erp_product_type type on name.num_type_id = type.num_type_id
        left join erp_product_classification class on type.num_classification_id = class.num_classification_id
        left join erp_product_service eps on detail.num_service_id = eps.num_service_id
        left join s_config_service_type service on detail.service_type_id = service.id
        left join erp_license_product elp on elp.product_id = detail.num_product_id
        left join (select r.num_product_id num_product_id, group_concat(distinct t.vc_tax_name) vc_tax_names
        from erp_product_tax_relation r
        left join erp_product_tax_dict t on r.num_tax_id = t.num_tax_id
        group by r.num_product_id) tax on detail.num_product_id = tax.num_product_id
        left join erp_product_unit_dict util on detail.num_unit_id = util.num_unit_id
        left join
        (select r.num_product_id num_product_id, group_concat(distinct a.region_names) vc_area_names
        from erp_product_area_relation r
        left join
        (select s.id, concat_ws('-', o.title, t.title, s.title) region_names
        from com_dict_region s
        left join com_dict_region t on s.parent_id = t.id
        left join com_dict_region o on t.parent_id = o.id
        where s.level = 3
        union all
        select t.id, concat_ws('-', o.title, t.title) region_names
        from  com_dict_region t
        left join com_dict_region o on t.parent_id = o.id
        where t.level = 2
        union all
        select o.id,  o.title region_names
        from  com_dict_region o
        where o.level = 1) a
        on r.num_area_id = a.id
        group by r.num_product_id) region on detail.num_product_id = region.num_product_id
        <where>
            detail.is_old_data = 0
            <if test="numProductId != null "> and detail.num_product_id = #{numProductId}</if>
            <if test="vcProductName != null and vcProductName != ''"> and name.vc_product_name like concat('%', #{vcProductName}, '%')</if>
            <if test="numClassificationId != null "> and class.num_classification_id = #{numClassificationId}</if>
            <if test="numTypeId != null "> and type.num_type_id = #{numTypeId}</if>
            <if test="numNameId != null "> and name.num_name_id = #{numNameId}</if>
            <if test="numUnitId != null "> and detail.num_unit_id = #{numUnitId}</if>
            <if test="numIsUp != null "> and detail.num_is_up = #{numIsUp}</if>
            <if test="numIsCheck != null "> and detail.num_is_check = #{numIsCheck}</if>
            <if test="vcServiceName !=null">and eps.vc_service_name like concat('%', #{vcServiceName}, '%')</if>
            <if test="numPrice !=null">and configuration.product_price=#{numPrice}</if>
            <if test="deptId !=null">and FIND_IN_SET(#{deptId}, configuration.dept_id)</if>
            <if test="numProductId !=null"> and detail.num_product_id=#{numProductId}</if>
            <if test="licenseId !=null"> and elp.license_id=#{licenseId}</if>
            <if test="productIds != null">
                and detail.num_product_id IN
                <foreach item="item" index="index" collection="productIds"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectErpProductDetail" parameterType="Long" resultType="com.nnb.erp.domain.vo.ErpProductDetailVo">
        select detail.num_product_id as numProductId
             , class.num_classification_id as numClassificationId
             , class.vc_classification_name as vcClassificationName
             , type.num_type_id as numTypeId
             , type.vc_type_name as vcTypeName
             , name.num_name_id as numNameId
             , name.vc_product_name as vcProductName
             , service.num_service_id as numServiceId
             , service.vc_service_name as vcServiceName
             , st.name as serviceName
             , st.id  as  serviceId
--              , service.id as numServiceId
--              , service.name as vcServiceName
             , unit.num_unit_id as numUnitId
             , unit.vc_unit_name as vcUnitName
             , detail.num_price as numPrice
             , detail.num_is_need_contract as numIsNeedContract
             , detail.num_is_applet_show as numIsAppletShow
             , detail.vc_remark as vcemark,
               detail.cost_price as costPrice,
            detail.is_send as isSend,
            detail.service_main_aging as serviceMainAging,
            detail.is_address as isAddress,
            detail.cost_detailed as costDetailed,
            detail.standard_price as standardPrice,
            detail.finance_classification_id as financeClassificationId,
            detail.verbal_trick as verbalTrick,
            detail.no_contract_needed_dept_id as noContractNeededDeptId,
            detail.out_cost as outCost,
            detail.kp_product as kpProduct,
            detail.kp_support as kpSupport,
            detail.online_id as onlineId,
            detail.self_support as selfSupport,
            detail.early_warning as earlyWarning,
            detail.early_warning_url as earlyWarningUrl,
           detail.qualifications_extension as qualificationsExtension,
           detail.annual_inspection as annualInspection
        from erp_product_detail detail
         left join erp_product_name name on detail.num_name_id = name.num_name_id
         left join erp_product_type type on name.num_type_id = type.num_type_id
         left join erp_product_classification class on type.num_classification_id = class.num_classification_id
         left join erp_product_service service on detail.num_service_id = service.num_service_id
         left join s_config_service_type st on st.id=detail.service_type_id
--          left join s_config_service_type service on detail.service_type_id = service.id
         left join erp_product_unit_dict unit on detail.num_unit_id = unit.num_unit_id
        where detail.num_product_id = #{numProductId}
    </select>
    <select id="countErpDetailByDto" parameterType="ErpProductDetailDto"  resultType="java.lang.Integer" >
        select count(1)
        from erp_product_detail d
                 left join erp_product_tax_relation t on d.num_product_id = t.num_product_id
                 left join erp_product_area_relation a on d.num_product_id = a.num_product_id
                 left join erp_product_service eps on eps.num_service_id = d.num_service_id
                 left join erp_product_configuration epc on epc.product_id = d.num_product_id
        <where>
            d.is_old_data = 0
            <if test="numProductId != null "> and d.num_product_id != #{numProductId}</if>
            <if test="numNameId != null "> and d.num_name_id = #{numNameId}</if>
            <if test="numUnitId != null "> and d.num_unit_id = #{numUnitId}</if>
            <if test="vcServiceName != null "> and eps.vc_service_name = #{vcServiceName}</if>
            <if test="productPriceList != null and productPriceList.size > 0 and productPriceList != ''">
                AND epc.product_price IN (
                <foreach collection="productPriceList" item="price" separator=",">
                    #{price}
                </foreach>
                )
            </if>
            <if test="productTaxids != null">
                And t.num_tax_id IN
                <foreach item="item" index="index" collection="productTaxids"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="areaIds != null">
                And a.num_area_id IN
                <foreach item="item" index="index" collection="areaIds"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectErpProductByNumProductId" resultType="com.nnb.erp.domain.vo.ErpProductVo">
        select epd.num_product_id as numProductId,
               epn.vc_product_name as vcProductName
        from
            erp_product_detail epd left join erp_product_name epn on epd.num_name_id = epn.num_name_id
        where epd.num_product_id = #{numProductId}
    </select>
    <select id="selectCityByNameList" resultType="java.util.Map">
        select a.id,a.title from com_dict_region a
        left join com_dict_region b on a.parent_id = b.id
        left join com_dict_region c on b.parent_id = c.id
        where  c.title LIKE CONCAT('%',#{province},'%') and a.title in (
        <foreach collection="cityName" item="name" separator=",">
            #{name}
        </foreach>
        )
    </select>
    <select id="getVcServiceName" resultType="com.nnb.erp.domain.ErpProductService">
        SELECT num_service_id,vc_service_name from erp_product_service where vc_service_name  LIKE CONCAT('%',#{vcServiceName},'%')
    </select>
    <select id="selectErpProductDetailListById" resultType="com.nnb.erp.domain.ErpProductDetail">
        <include refid="selectErpProductDetailVo"/>
        where num_product_id in
        <foreach collection="list" item="id"  open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
