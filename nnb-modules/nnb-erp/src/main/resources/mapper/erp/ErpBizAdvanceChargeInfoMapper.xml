<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpBizAdvanceChargeInfoMapper">

    <resultMap type="ErpBizAdvanceChargeInfo" id="ErpBizAdvanceChargeInfoResult">
        <result property="numId" column="num_id"/>
        <result property="vcAdvanceChargeNo" column="vc_advance_charge_no"/>
        <result property="numSupplierId" column="num_supplier_id"/>
        <result property="numSupplierAmount" column="num_supplier_amount"/>
        <result property="numSupplierBandId" column="num_supplier_band_id"/>
        <result property="numApplyUserId" column="num_apply_user_id"/>
        <result property="numApplyDeptId" column="num_apply_dept_id"/>
        <result property="vcRemarks" column="vc_remarks"/>
        <result property="datCommitDat" column="dat_commit_dat"/>
        <result property="numApproveStatus" column="num_approve_status"/>
        <result property="numWriteOffStatus" column="num_write_off_status"/>
        <result property="numWriteOffAmount" column="num_write_off_amount"/>
        <result property="vcWriteOffReject" column="vc_write_off_reject"/>
        <result property="datCreatedTime" column="dat_created_time"/>
        <result property="numCreatedBy" column="num_created_by"/>
        <result property="datUpdatedTime" column="dat_updated_time"/>
        <result property="datUpdatedBy" column="dat_updated_by"/>
    </resultMap>

    <sql id="selectErpBizAdvanceChargeInfoVo">
        select num_id,
               vc_advance_charge_no,
               num_supplier_id,
               num_supplier_amount,
               num_supplier_band_id,
               num_apply_user_id,
               num_apply_dept_id,
               vc_remarks,
               dat_commit_dat,
               num_approve_status,
               num_write_off_status,
               num_write_off_amount,
               vc_write_off_reject,
               dat_created_time,
               num_created_by,
               dat_updated_time,
               dat_updated_by
        from erp_biz_advance_charge_info
    </sql>

    <select id="selectErpBizAdvanceChargeInfoList" parameterType="ErpBizAdvanceChargeInfo"
            resultMap="ErpBizAdvanceChargeInfoResult">
        <include refid="selectErpBizAdvanceChargeInfoVo"/>
        <where>
            <if test="vcAdvanceChargeNo != null  and vcAdvanceChargeNo != ''">and vc_advance_charge_no =
                #{vcAdvanceChargeNo}
            </if>
            <if test="numSupplierId != null ">and num_supplier_id = #{numSupplierId}</if>
            <if test="numSupplierAmount != null ">and num_supplier_amount = #{numSupplierAmount}</if>
            <if test="numSupplierBandId != null ">and num_supplier_band_id = #{numSupplierBandId}</if>
            <if test="numApplyUserId != null ">and num_apply_user_id = #{numApplyUserId}</if>
            <if test="numApplyDeptId != null ">and num_apply_dept_id = #{numApplyDeptId}</if>
            <if test="vcRemarks != null  and vcRemarks != ''">and vc_remarks = #{vcRemarks}</if>
            <if test="datCommitDat != null ">and dat_commit_dat = #{datCommitDat}</if>
            <if test="numApproveStatus != null ">and num_approve_status = #{numApproveStatus}</if>
            <if test="numWriteOffStatus != null ">and num_write_off_status = #{numWriteOffStatus}</if>
            <if test="numWriteOffAmount != null ">and num_write_off_amount = #{numWriteOffAmount}</if>
            <if test="datCreatedTime != null ">and dat_created_time = #{datCreatedTime}</if>
            <if test="numCreatedBy != null ">and num_created_by = #{numCreatedBy}</if>
            <if test="datUpdatedTime != null ">and dat_updated_time = #{datUpdatedTime}</if>
            <if test="datUpdatedBy != null ">and dat_updated_by = #{datUpdatedBy}</if>
        </where>
    </select>
    <select id="selectErpBizAdvanceChargeInfoListNew" parameterType="ErpBizAdvanceChargeInfo" resultType="com.nnb.erp.domain.vo.bizAdvanceChargeInfo.ErpBizAdvanceChargeInfoVo">
        SELECT
            baci.num_id,
            baci.vc_advance_charge_no,
            baci.num_supplier_id,
            baci.num_supplier_amount,
            baci.num_supplier_band_id,
            baci.num_apply_user_id,
            baci.num_apply_dept_id,
            baci.vc_remarks,
            baci.dat_commit_dat,
            CASE
            baci.num_approve_status
            WHEN 1 THEN
            "待审批"
            WHEN 2 THEN
            "已通过"
            WHEN 3 THEN
            "撤销"
            END AS approve_status,
            CASE
            baci.num_write_off_status
            WHEN 1 THEN
            "未核销"
            WHEN 2 THEN
            "已核销"
            END AS write_off_status,
            baci.num_write_off_amount,
            baci.dat_created_time,
            baci.num_created_by,
            baci.dat_updated_time,
            baci.dat_updated_by,
            bsi.vc_supplier_name,
            su.user_name,
            sd.dept_name
        FROM
            erp_biz_advance_charge_info baci
            LEFT JOIN erp_biz_supplier_info bsi ON baci.num_supplier_id = bsi.num_id
            LEFT JOIN sys_user su ON baci.num_apply_user_id = su.user_id
            LEFT JOIN sys_dept sd ON baci.num_apply_dept_id = sd.dept_id
        <where>
            <if test="vcAdvanceChargeNo != null  and vcAdvanceChargeNo != ''">and vc_advance_charge_no =
                #{vcAdvanceChargeNo}
            </if>
            <if test="numSupplierId != null ">and num_supplier_id = #{numSupplierId}</if>
            <if test="numSupplierAmount != null ">and num_supplier_amount = #{numSupplierAmount}</if>
            <if test="numSupplierBandId != null ">and num_supplier_band_id = #{numSupplierBandId}</if>
            <if test="numApplyUserId != null ">and num_apply_user_id = #{numApplyUserId}</if>
            <if test="numApplyDeptId != null ">and num_apply_dept_id = #{numApplyDeptId}</if>
            <if test="vcRemarks != null  and vcRemarks != ''">and vc_remarks = #{vcRemarks}</if>
            <if test="datCommitDat != null ">and dat_commit_dat = #{datCommitDat}</if>
            <if test="numApproveStatus != null ">and num_approve_status = #{numApproveStatus}</if>
            <if test="numWriteOffStatus != null ">and num_write_off_status = #{numWriteOffStatus}</if>
            <if test="numWriteOffAmount != null ">and num_write_off_amount = #{numWriteOffAmount}</if>
            <if test="datCreatedTime != null ">and dat_created_time = #{datCreatedTime}</if>
            <if test="numCreatedBy != null ">and num_created_by = #{numCreatedBy}</if>
            <if test="datUpdatedTime != null ">and dat_updated_time = #{datUpdatedTime}</if>
            <if test="datUpdatedBy != null ">and dat_updated_by = #{datUpdatedBy}</if>
        </where>
    </select>

    <select id="selectErpBizAdvanceChargeInfoByNumId" parameterType="Long" resultMap="ErpBizAdvanceChargeInfoResult">
        <include refid="selectErpBizAdvanceChargeInfoVo"/>
        where num_id = #{numId}
    </select>

    <insert id="insertErpBizAdvanceChargeInfo" parameterType="ErpBizAdvanceChargeInfo" useGeneratedKeys="true"
            keyProperty="numId">
        insert into erp_biz_advance_charge_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vcAdvanceChargeNo != null">vc_advance_charge_no,</if>
            <if test="numSupplierId != null">num_supplier_id,</if>
            <if test="numSupplierAmount != null">num_supplier_amount,</if>
            <if test="numSupplierBandId != null">num_supplier_band_id,</if>
            <if test="numApplyUserId != null">num_apply_user_id,</if>
            <if test="numApplyDeptId != null">num_apply_dept_id,</if>
            <if test="vcRemarks != null">vc_remarks,</if>
            <if test="datCommitDat != null">dat_commit_dat,</if>
            <if test="numApproveStatus != null">num_approve_status,</if>
            <if test="numWriteOffStatus != null">num_write_off_status,</if>
            <if test="numWriteOffAmount != null">num_write_off_amount,</if>
            <if test="vcWriteOffReject != null">vc_write_off_reject,</if>
            <if test="datCreatedTime != null">dat_created_time,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="datUpdatedTime != null">dat_updated_time,</if>
            <if test="datUpdatedBy != null">dat_updated_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vcAdvanceChargeNo != null">#{vcAdvanceChargeNo},</if>
            <if test="numSupplierId != null">#{numSupplierId},</if>
            <if test="numSupplierAmount != null">#{numSupplierAmount},</if>
            <if test="numSupplierBandId != null">#{numSupplierBandId},</if>
            <if test="numApplyUserId != null">#{numApplyUserId},</if>
            <if test="numApplyDeptId != null">#{numApplyDeptId},</if>
            <if test="vcRemarks != null">#{vcRemarks},</if>
            <if test="datCommitDat != null">#{datCommitDat},</if>
            <if test="numApproveStatus != null">#{numApproveStatus},</if>
            <if test="numWriteOffStatus != null">#{numWriteOffStatus},</if>
            <if test="numWriteOffAmount != null">#{numWriteOffAmount},</if>
            <if test="vcWriteOffReject != null">#{vcWriteOffReject},</if>
            <if test="datCreatedTime != null">#{datCreatedTime},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="datUpdatedTime != null">#{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">#{datUpdatedBy},</if>
        </trim>
    </insert>

    <update id="updateErpBizAdvanceChargeInfo" parameterType="ErpBizAdvanceChargeInfo">
        update erp_biz_advance_charge_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="vcAdvanceChargeNo != null">vc_advance_charge_no = #{vcAdvanceChargeNo},</if>
            <if test="numSupplierId != null">num_supplier_id = #{numSupplierId},</if>
            <if test="numSupplierAmount != null">num_supplier_amount = #{numSupplierAmount},</if>
            <if test="numSupplierBandId != null">num_supplier_band_id = #{numSupplierBandId},</if>
            <if test="numApplyUserId != null">num_apply_user_id = #{numApplyUserId},</if>
            <if test="numApplyDeptId != null">num_apply_dept_id = #{numApplyDeptId},</if>
            <if test="vcRemarks != null">vc_remarks = #{vcRemarks},</if>
            <if test="datCommitDat != null">dat_commit_dat = #{datCommitDat},</if>
            <if test="numApproveStatus != null">num_approve_status = #{numApproveStatus},</if>
            <if test="numWriteOffStatus != null">num_write_off_status = #{numWriteOffStatus},</if>
            <if test="numWriteOffAmount != null">num_write_off_amount = #{numWriteOffAmount},</if>
            <if test="vcWriteOffReject != null">vc_write_off_reject = #{vcWriteOffReject},</if>
            <if test="datCreatedTime != null">dat_created_time = #{datCreatedTime},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datUpdatedTime != null">dat_updated_time = #{datUpdatedTime},</if>
            <if test="datUpdatedBy != null">dat_updated_by = #{datUpdatedBy},</if>
        </trim>
        where num_id = #{numId}
    </update>

    <delete id="deleteErpBizAdvanceChargeInfoByNumId" parameterType="Long">
        delete
        from erp_biz_advance_charge_info
        where num_id = #{numId}
    </delete>

    <delete id="deleteErpBizAdvanceChargeInfoByNumIds" parameterType="String">
        delete from erp_biz_advance_charge_info where num_id in
        <foreach item="numId" collection="array" open="(" separator="," close=")">
            #{numId}
        </foreach>
    </delete>

    <select id="selectCountErpBizAdvanceChargeInfo" parameterType="ErpBizAdvanceChargeInfo"
            resultType="java.lang.Integer">
        select count(num_id) from erp_biz_advance_charge_info
        <where>
            <if test="vcAdvanceChargeNo != null  and vcAdvanceChargeNo != ''">and vc_advance_charge_no =
                #{vcAdvanceChargeNo}
            </if>
            <if test="numSupplierId != null ">and num_supplier_id = #{numSupplierId}</if>
            <if test="numSupplierAmount != null ">and num_supplier_amount = #{numSupplierAmount}</if>
            <if test="numSupplierBandId != null ">and num_supplier_band_id = #{numSupplierBandId}</if>
            <if test="numApplyUserId != null ">and num_apply_user_id = #{numApplyUserId}</if>
            <if test="numApplyDeptId != null ">and num_apply_dept_id = #{numApplyDeptId}</if>
            <if test="vcRemarks != null  and vcRemarks != ''">and vc_remarks = #{vcRemarks}</if>
            <if test="datCommitDat != null ">and dat_commit_dat = #{datCommitDat}</if>
            <if test="numApproveStatus != null ">and num_approve_status = #{numApproveStatus}</if>
            <if test="numWriteOffStatus != null ">and num_write_off_status = #{numWriteOffStatus}</if>
            <if test="numWriteOffAmount != null ">and num_write_off_amount = #{numWriteOffAmount}</if>
            <if test="datCreatedTime != null ">and dat_created_time = #{datCreatedTime}</if>
            <if test="numCreatedBy != null ">and num_created_by = #{numCreatedBy}</if>
            <if test="datUpdatedTime != null ">and dat_updated_time = #{datUpdatedTime}</if>
            <if test="datUpdatedBy != null ">and dat_updated_by = #{datUpdatedBy}</if>
        </where>
    </select>
</mapper>