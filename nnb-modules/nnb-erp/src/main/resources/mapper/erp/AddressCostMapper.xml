<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.AddressCostMapper">
    
    <resultMap type="AddressCost" id="AddressCostResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="registerAreaId"    column="register_area_id"    />
        <result property="addresstype"    column="addressType"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="historyHasprice"    column="history_hasPrice"    />
        <result property="historyNoprice"    column="history_noPrice"    />
        <result property="hasPrice"    column="has_price"    />
        <result property="noPrice"    column="no_price"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="typePrice"    column="type_price"    />
        <result property="inNumber"    column="in_number"    />
        <result property="outNumber"    column="out_number"    />
        <result property="type"    column="type"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="costTypeId"    column="cost_type_id"    />
        <result property="isOpen"    column="is_open"    />
    </resultMap>

    <sql id="selectAddressCostVo">
        select id, agent_id, register_area_id, addressType, start_time, end_time, detail_address, history_hasPrice, history_noPrice, has_price, no_price, status, created_by, updated_by, created_at, updated_at, type_price, in_number, out_number, type, total_number, cost_type_id, is_open from address_cost
    </sql>

    <select id="selectAddressCostList" parameterType="AddressCost" resultMap="AddressCostResult">
        <include refid="selectAddressCostVo"/>
        <where>  
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="registerAreaId != null "> and register_area_id = #{registerAreaId}</if>
            <if test="addresstype != null "> and addressType = #{addresstype}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="detailAddress != null  and detailAddress != ''"> and detail_address = #{detailAddress}</if>
            <if test="historyHasprice != null "> and history_hasPrice = #{historyHasprice}</if>
            <if test="historyNoprice != null "> and history_noPrice = #{historyNoprice}</if>
            <if test="hasPrice != null "> and has_price = #{hasPrice}</if>
            <if test="noPrice != null "> and no_price = #{noPrice}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            <if test="typePrice != null "> and type_price = #{typePrice}</if>
            <if test="inNumber != null "> and in_number = #{inNumber}</if>
            <if test="outNumber != null "> and out_number = #{outNumber}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="totalNumber != null "> and total_number = #{totalNumber}</if>
            <if test="costTypeId != null "> and cost_type_id = #{costTypeId}</if>
            <if test="isOpen != null "> and is_open = #{isOpen}</if>
        </where>
    </select>
    
    <select id="selectAddressCostById" parameterType="Long" resultMap="AddressCostResult">
        <include refid="selectAddressCostVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAddressCost" parameterType="AddressCost" useGeneratedKeys="true" keyProperty="id">
        insert into address_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="registerAreaId != null">register_area_id,</if>
            <if test="addresstype != null">addressType,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="historyHasprice != null">history_hasPrice,</if>
            <if test="historyNoprice != null">history_noPrice,</if>
            <if test="hasPrice != null">has_price,</if>
            <if test="noPrice != null">no_price,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="typePrice != null">type_price,</if>
            <if test="inNumber != null">in_number,</if>
            <if test="outNumber != null">out_number,</if>
            <if test="type != null">type,</if>
            <if test="totalNumber != null">total_number,</if>
            <if test="costTypeId != null">cost_type_id,</if>
            <if test="isOpen != null">is_open,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="registerAreaId != null">#{registerAreaId},</if>
            <if test="addresstype != null">#{addresstype},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="historyHasprice != null">#{historyHasprice},</if>
            <if test="historyNoprice != null">#{historyNoprice},</if>
            <if test="hasPrice != null">#{hasPrice},</if>
            <if test="noPrice != null">#{noPrice},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="typePrice != null">#{typePrice},</if>
            <if test="inNumber != null">#{inNumber},</if>
            <if test="outNumber != null">#{outNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="totalNumber != null">#{totalNumber},</if>
            <if test="costTypeId != null">#{costTypeId},</if>
            <if test="isOpen != null">#{isOpen},</if>
         </trim>
    </insert>

    <update id="updateAddressCost" parameterType="AddressCost">
        update address_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="registerAreaId != null">register_area_id = #{registerAreaId},</if>
            <if test="addresstype != null">addressType = #{addresstype},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="historyHasprice != null">history_hasPrice = #{historyHasprice},</if>
            <if test="historyNoprice != null">history_noPrice = #{historyNoprice},</if>
            <if test="hasPrice != null">has_price = #{hasPrice},</if>
            <if test="noPrice != null">no_price = #{noPrice},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="typePrice != null">type_price = #{typePrice},</if>
            <if test="inNumber != null">in_number = #{inNumber},</if>
            <if test="outNumber != null">out_number = #{outNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="costTypeId != null">cost_type_id = #{costTypeId},</if>
            <if test="isOpen != null">is_open = #{isOpen},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAddressCostById" parameterType="Long">
        delete from address_cost where id = #{id}
    </delete>

    <delete id="deleteAddressCostByIds" parameterType="String">
        delete from address_cost where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>