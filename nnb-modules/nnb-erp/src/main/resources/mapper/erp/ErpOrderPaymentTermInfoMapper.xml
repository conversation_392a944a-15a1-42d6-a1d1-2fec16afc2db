<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpOrderPaymentTermInfoMapper">

    <resultMap type="ErpOrderPaymentTermInfo" id="ErpOrderPaymentTermInfoResult">
        <result property="id"    column="id"    />
        <result property="numPaymentType"    column="num_payment_type"    />
        <result property="vcPaymentUrl"    column="vc_payment_url"    />
        <result property="numMoney"    column="num_money"    />
        <result property="termId"    column="term_id"    />
        <result property="numStatus"    column="num_status"    />
        <result property="datFinanceCollectionTime"    column="dat_finance_collection_time"    />
    </resultMap>

    <sql id="selectErpOrderPaymentTermInfoVo">
        select id, num_payment_type, vc_payment_url, num_money,dat_finance_collection_time from erp_order_payment_term_info
    </sql>

    <select id="selectErpOrderPaymentTermInfoList" parameterType="ErpOrderPaymentTermInfo" resultMap="ErpOrderPaymentTermInfoResult">
        <include refid="selectErpOrderPaymentTermInfoVo"/>
        <where>
            <if test="numPaymentType != null "> and num_payment_type = #{numPaymentType}</if>
            <if test="vcPaymentUrl != null  and vcPaymentUrl != ''"> and vc_payment_url = #{vcPaymentUrl}</if>
            <if test="numMoney != null "> and num_money = #{numMoney}</if>
            <if test="termId != null "> and term_id = #{termId}</if>
        </where>
    </select>

    <select id="selectErpOrderPaymentTermInfoById" parameterType="Long" resultMap="ErpOrderPaymentTermInfoResult">
        <include refid="selectErpOrderPaymentTermInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertErpOrderPaymentTermInfo" parameterType="ErpOrderPaymentTermInfo" useGeneratedKeys="true" keyProperty="id">
        insert into erp_order_payment_term_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="termId != null">term_id,</if>
            <if test="numPaymentType != null">num_payment_type,</if>
            <if test="vcPaymentUrl != null">vc_payment_url,</if>
            <if test="numMoney != null">num_money,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="termId != null">#{termId},</if>
            <if test="numPaymentType != null">#{numPaymentType},</if>
            <if test="vcPaymentUrl != null">#{vcPaymentUrl},</if>
            <if test="numMoney != null">#{numMoney},</if>
         </trim>
    </insert>

    <update id="updateErpOrderPaymentTermInfo" parameterType="ErpOrderPaymentTermInfo">
        update erp_order_payment_term_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="termId != null">term_id = #{termId},</if>
            <if test="numPaymentType != null">num_payment_type = #{numPaymentType},</if>
            <if test="vcPaymentUrl != null">vc_payment_url = #{vcPaymentUrl},</if>
            <if test="numMoney != null">num_money = #{numMoney},</if>
            <if test="financeTime!=null">dat_finance_collection_time=#{financeTime},</if>
            <if test="updatedBy!=null">updated_by=#{updatedBy},</if>
            <if test="updatedAt!=null and updatedAt != ''">updated_at=#{updatedAt},</if>
            <if test="numStatus!=null and numStatus != ''">num_status=#{numStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpOrderPaymentTermInfoById" parameterType="Long">
        delete from erp_order_payment_term_info where id = #{id}
    </delete>

    <delete id="deleteErpOrderPaymentTermInfoByIds" parameterType="String">
        delete from erp_order_payment_term_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getPaymentTermInfoVo" resultType="com.nnb.erp.domain.vo.PaymentTermInfoVo">
        select
            eopt.id,
            eopt.num_order_id,
            eopti.term_id,
            eopti.num_payment_type,
            eopti.vc_payment_url,
            eopti.num_money
            from erp_order_payment_term eopt
            left join erp_order_payment_term_info eopti on eopt.id = eopti.term_id
            where eopt.num_order_id = #{numOrderId}
    </select>
    <select id="selectErpOrderPaymentTermInfoByTermId" resultType="com.nnb.erp.domain.ErpOrderPaymentTermInfo">
        <include refid="selectErpOrderPaymentTermInfoVo"/>
        where term_id = #{termId}
    </select>
</mapper>
