<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpExamineApproveTypePersionMapper">
    
    <resultMap type="ErpExamineApproveTypePersion" id="ErpExamineApproveTypePersionResult">
        <result property="id"    column="id"    />
        <result property="approveType"    column="approve_type"    />
        <result property="deptIds"    column="dept_ids"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateTime"    column="update_time"    />
        <result property="memo"    column="memo"    />
        <result property="status"    column="status"    />
        <result property="followInfo"    column="follow_info"    />
    </resultMap>

    <sql id="selectErpExamineApproveTypePersionVo">
        select id, approve_type, dept_ids, created_user, created_time, update_user, update_time, memo, status, follow_info from erp_examine_approve_type_persion
    </sql>

    <select id="selectErpExamineApproveTypePersionList" parameterType="com.nnb.erp.domain.dto.ErpExamineApproveTypePersionDto" resultType="com.nnb.erp.domain.vo.ErpExamineApproveTypePersionVo">
        select
               eeatp.id,
               eeatp.approve_type,
               eeatm.name as approveName,
               eeatp.dept_ids,
               eeatp.user_ids,
               eeatp.change_type,
               eeatp.created_user,
               eeatp.created_time,
               eeatp.update_user,
               eeatp.update_time,
               eeatp.memo,
               eeatp.status,
               eeatp.follow_info,
               eeatp.sample
        from erp_examine_approve_type_persion eeatp
        LEFT JOIN erp_examine_approve_type_manage eeatm on eeatm.id = eeatp.approve_type
        <where>
            <if test="approveName != null  and approveName != ''"> and eeatm.name like concat('%', #{approveName}, '%')</if>
            <if test="id != null "> and eeatp.id = #{id}</if>
            <if test="approveType != null "> and eeatp.approve_type = #{approveType}</if>
            <if test="status != null "> and eeatp.status = #{status}</if>
            <if test="sample != null "> and eeatp.sample = #{sample}</if>
        </where>
    </select>
    
    <select id="selectErpExamineApproveTypePersionById" parameterType="Long" resultMap="ErpExamineApproveTypePersionResult">
        <include refid="selectErpExamineApproveTypePersionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpExamineApproveTypePersion" parameterType="ErpExamineApproveTypePersion" useGeneratedKeys="true" keyProperty="id">
        insert into erp_examine_approve_type_persion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="approveType != null">approve_type,</if>
            <if test="deptIds != null">dept_ids,</if>
            <if test="userIds != null">user_ids,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="memo != null">memo,</if>
            <if test="status != null">status,</if>
            <if test="followInfo != null and followInfo != ''">follow_info,</if>
            <if test="sample != null">sample,</if>
            <if test="changeType != null">change_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="approveType != null">#{approveType},</if>
            <if test="deptIds != null">#{deptIds},</if>
            <if test="userIds != null">#{userIds},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="memo != null">#{memo},</if>
            <if test="status != null">#{status},</if>
            <if test="followInfo != null and followInfo != ''">#{followInfo},</if>
            <if test="sample != null">#{sample},</if>
            <if test="changeType != null">#{changeType},</if>
         </trim>
    </insert>

    <update id="updateErpExamineApproveTypePersion" parameterType="ErpExamineApproveTypePersion">
        update erp_examine_approve_type_persion
        <trim prefix="SET" suffixOverrides=",">
            <if test="approveType != null">approve_type = #{approveType},</if>
            <if test="deptIds != null">dept_ids = #{deptIds},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="followInfo != null and followInfo != ''">follow_info = #{followInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpExamineApproveTypePersionById" parameterType="Long">
        delete from erp_examine_approve_type_persion where id = #{id}
    </delete>

    <delete id="deleteErpExamineApproveTypePersionByIds" parameterType="String">
        delete from erp_examine_approve_type_persion where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>