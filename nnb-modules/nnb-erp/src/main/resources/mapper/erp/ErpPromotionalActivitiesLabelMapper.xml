<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpPromotionalActivitiesLabelMapper">
    
    <resultMap type="ErpPromotionalActivitiesLabel" id="ErpPromotionalActivitiesLabelResult">
        <result property="id"    column="id"    />
        <result property="label"    column="label"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectErpPromotionalActivitiesLabelVo">
        select id, label, created_user, created_time, update_user, update_time from erp_promotional_activities_label
    </sql>

    <select id="selectErpPromotionalActivitiesLabelList" parameterType="ErpPromotionalActivitiesLabel" resultMap="ErpPromotionalActivitiesLabelResult">
        <include refid="selectErpPromotionalActivitiesLabelVo"/>
        <where>  
            <if test="label != null  and label != ''"> and label like concat('%', #{label}, '%')</if>
            <if test="createdUser != null "> and created_user = #{createdUser}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
        ORDER BY id DESC
    </select>
    
    <select id="selectErpPromotionalActivitiesLabelById" parameterType="Integer" resultMap="ErpPromotionalActivitiesLabelResult">
        <include refid="selectErpPromotionalActivitiesLabelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpPromotionalActivitiesLabel" parameterType="ErpPromotionalActivitiesLabel" useGeneratedKeys="true" keyProperty="id">
        insert into erp_promotional_activities_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="label != null">label,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="label != null">#{label},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateErpPromotionalActivitiesLabel" parameterType="ErpPromotionalActivitiesLabel">
        update erp_promotional_activities_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="label != null">label = #{label},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpPromotionalActivitiesLabelById" parameterType="Integer">
        delete from erp_promotional_activities_label where id = #{id}
    </delete>

    <delete id="deleteErpPromotionalActivitiesLabelByIds" parameterType="String">
        delete from erp_promotional_activities_label where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>