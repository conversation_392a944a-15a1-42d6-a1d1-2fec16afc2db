<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpContractRecordMapper">
    
    <resultMap type="ErpContractRecord" id="ErpContractRecordResult">
        <result property="id"    column="id"    />
        <result property="numContractId"    column="num_contract_id"    />
        <result property="vcContent"    column="vc_content"    />
        <result property="numCreatedBy"    column="num_created_by"    />
        <result property="datSigningDatecreatedTime"    column="dat_signing_datecreated_time"    />
    </resultMap>

    <sql id="selectErpContractRecordVo">
        select id, num_contract_id, vc_content, num_created_by, dat_signing_datecreated_time from erp_contract_record
    </sql>

    <select id="selectErpContractRecordList" parameterType="ErpContractRecord" resultMap="ErpContractRecordResult">
        <include refid="selectErpContractRecordVo"/>
        <where>  
            <if test="numContractId != null "> and num_contract_id = #{numContractId}</if>
            <if test="vcContent != null  and vcContent != ''"> and vc_content = #{vcContent}</if>
            <if test="numCreatedBy != null "> and num_created_by = #{numCreatedBy}</if>
            <if test="datSigningDatecreatedTime != null "> and dat_signing_datecreated_time = #{datSigningDatecreatedTime}</if>
        </where>
    </select>
    
    <select id="selectErpContractRecordById" parameterType="Long" resultMap="ErpContractRecordResult">
        <include refid="selectErpContractRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectErpContractRecordVoList"  parameterType="ErpContractRecord" resultType="com.nnb.erp.domain.vo.ErpContractRecordVo">

        select cr.id,
               cr.num_contract_id,
               cr.vc_content,
               cr.num_created_by,
               cr.dat_signing_datecreated_time,
               su.nick_name createdUserName
        from erp_contract_record cr
                 left join sys_user su on cr.num_created_by = su.user_id
        where cr.num_contract_id = #{numContractId}
        order by cr.dat_signing_datecreated_time asc
    </select>

    <insert id="insertErpContractRecord" parameterType="ErpContractRecord" useGeneratedKeys="true" keyProperty="id">
        insert into erp_contract_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numContractId != null">num_contract_id,</if>
            <if test="vcContent != null">vc_content,</if>
            <if test="numCreatedBy != null">num_created_by,</if>
            <if test="datSigningDatecreatedTime != null">dat_signing_datecreated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numContractId != null">#{numContractId},</if>
            <if test="vcContent != null">#{vcContent},</if>
            <if test="numCreatedBy != null">#{numCreatedBy},</if>
            <if test="datSigningDatecreatedTime != null">#{datSigningDatecreatedTime},</if>
         </trim>
    </insert>

    <update id="updateErpContractRecord" parameterType="ErpContractRecord">
        update erp_contract_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="numContractId != null">num_contract_id = #{numContractId},</if>
            <if test="vcContent != null">vc_content = #{vcContent},</if>
            <if test="numCreatedBy != null">num_created_by = #{numCreatedBy},</if>
            <if test="datSigningDatecreatedTime != null">dat_signing_datecreated_time = #{datSigningDatecreatedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpContractRecordById" parameterType="Long">
        delete from erp_contract_record where id = #{id}
    </delete>

    <delete id="deleteErpContractRecordByIds" parameterType="String">
        delete from erp_contract_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>