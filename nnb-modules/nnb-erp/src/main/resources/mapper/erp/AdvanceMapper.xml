<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.AdvanceMapper">
    
    <resultMap type="Advance" id="AdvanceResult">
        <result property="id"    column="id"    />
        <result property="number"    column="number"    />
        <result property="agentId"    column="agent_id"    />
        <result property="pay"    column="pay"    />
        <result property="deptId"    column="dept_id"    />
        <result property="bankId"    column="bank_id"    />
        <result property="status"    column="status"    />
        <result property="hxStatus"    column="hx_status"    />
        <result property="remark"    column="remark"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="passTime"    column="pass_time"    />
        <result property="confirmAmount"    column="confirm_amount"    />
        <result property="confirmDate"    column="confirm_date"    />
        <result property="confirmUser"    column="confirm_user"    />
    </resultMap>

    <sql id="selectAdvanceVo">
        select id, number, agent_id, pay, dept_id, bank_id, status, hx_status, remark, created_by, updated_by, created_at, updated_at, pass_time, confirm_amount, confirm_date, confirm_user from advance
    </sql>

    <select id="selectAdvanceList" parameterType="Advance" resultMap="AdvanceResult">
        <include refid="selectAdvanceVo"/>
        <where>  
            <if test="number != null  and number != ''"> and number = #{number}</if>
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="pay != null "> and pay = #{pay}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="bankId != null "> and bank_id = #{bankId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="hxStatus != null "> and hx_status = #{hxStatus}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            <if test="passTime != null "> and pass_time = #{passTime}</if>
            <if test="confirmAmount != null "> and confirm_amount = #{confirmAmount}</if>
            <if test="confirmDate != null "> and confirm_date = #{confirmDate}</if>
            <if test="confirmUser != null "> and confirm_user = #{confirmUser}</if>
        </where>
    </select>
    
    <select id="selectAdvanceById" parameterType="Integer" resultMap="AdvanceResult">
        <include refid="selectAdvanceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAdvance" parameterType="Advance" useGeneratedKeys="true" keyProperty="id">
        insert into advance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="number != null">number,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="pay != null">pay,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="bankId != null">bank_id,</if>
            <if test="status != null">status,</if>
            <if test="hxStatus != null">hx_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="passTime != null">pass_time,</if>
            <if test="confirmAmount != null">confirm_amount,</if>
            <if test="confirmDate != null">confirm_date,</if>
            <if test="confirmUser != null">confirm_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="number != null">#{number},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="pay != null">#{pay},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="bankId != null">#{bankId},</if>
            <if test="status != null">#{status},</if>
            <if test="hxStatus != null">#{hxStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="passTime != null">#{passTime},</if>
            <if test="confirmAmount != null">#{confirmAmount},</if>
            <if test="confirmDate != null">#{confirmDate},</if>
            <if test="confirmUser != null">#{confirmUser},</if>
         </trim>
    </insert>

    <update id="updateAdvance" parameterType="Advance">
        update advance
        <trim prefix="SET" suffixOverrides=",">
            <if test="number != null">number = #{number},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="pay != null">pay = #{pay},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="bankId != null">bank_id = #{bankId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hxStatus != null">hx_status = #{hxStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="passTime != null">pass_time = #{passTime},</if>
            <if test="confirmAmount != null">confirm_amount = #{confirmAmount},</if>
            <if test="confirmDate != null">confirm_date = #{confirmDate},</if>
            <if test="confirmUser != null">confirm_user = #{confirmUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdvanceById" parameterType="Integer">
        delete from advance where id = #{id}
    </delete>

    <delete id="deleteAdvanceByIds" parameterType="String">
        delete from advance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>