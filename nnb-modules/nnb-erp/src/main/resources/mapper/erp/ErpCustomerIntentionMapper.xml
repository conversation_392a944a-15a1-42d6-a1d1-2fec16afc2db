<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCustomerIntentionMapper">

    <resultMap type="ErpCustomerIntention" id="ErpCustomerIntentionResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="intentionSource"    column="intention_source"    />
        <result property="intentionDegree"    column="intention_degree"    />
        <result property="intentionConfigId"    column="intention_config_id"    />
        <result property="followStatus"    column="follow_status"    />
        <result property="dealDate"    column="deal_date"    />
        <result property="dealFee"    column="deal_fee"    />
        <result property="memo"    column="memo"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdTime"    column="created_time"    />
        <result property="accountUser"    column="account_user"    />
    </resultMap>

    <sql id="selectErpCustomerIntentionVo">
        select id, client_id, intention_source, intention_degree, intention_config_id, follow_status, deal_date, deal_fee, memo, created_user, created_time, account_user from erp_customer_intention
    </sql>

    <select id="selectErpCustomerIntentionList" resultType="com.nnb.erp.domain.vo.ErpCustomerIntentionVo">
        select
               eci.id,
               ee.vc_company_name as enterpriseName,
               ee.id as enterpriseId,
               ee.level as enterpriseLevel,
               ec.contactName as contactName,
               ec.contactPhone as contactPhone,
               cdr.title as cityName,
               GROUP_CONCAT(DISTINCT ecip.product_type_id) as productTypeIds,
               eci.follow_status,
               eci.deal_fee,
               eci.deal_date,
               eci.intention_degree,
               eci.created_time,
        su.nick_name as accountUserName
        from erp_customer_intention eci
        left join erp_customer_intention_product ecip on ecip.intention_id = eci.id
        left join erp_client ec on ec.id = eci.client_id
        left join erp_enterprise ee on ee.id = ec.num_enterprise_id
        left join com_dict_region cdr on cdr.id = ec.num_city_id
        left join sys_user su on su.user_id = eci.account_user
        left join sys_dept sd on sd.dept_id = su.dept_id
        <where>
            and 1
            <if test="id != null "> and eci.id = #{id}</if>
            <if test="enterpriseName != null and enterpriseName != ''"> and ee.vc_company_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="productTypeIdList != null and productTypeIdList.size > 0">
                and ecip.product_type_id in
                <foreach item="productTypeId" collection="productTypeIdList" open="(" separator="," close=")">
                    #{productTypeId}
                </foreach>
            </if>
            <if test="cityIdList != null and cityIdList.size > 0">
                and ec.num_city_id in
                <foreach item="cityId" collection="cityIdList" open="(" separator="," close=")">
                    #{cityId}
                </foreach>
            </if>
            <if test="intentionDegree != null "> and eci.intention_degree = #{intentionDegree}</if>
            <if test="followStatus != null "> and eci.follow_status = #{followStatus}</if>
            <if test="followStatusList != null and followStatusList.size > 0">
                and eci.follow_status in
                <foreach item="followStatus" collection="followStatusList" open="(" separator="," close=")">
                    #{followStatus}
                </foreach>
            </if>
            <if test="createdTimeBegin !=null">and eci.created_time <![CDATA[>=]]> #{createdTimeBegin}</if>
            <if test="createdTimeEnd !=null">and eci.created_time <![CDATA[<=]]> #{createdTimeEnd}</if>
        </where>
        ${params.dataScope}
        GROUP BY eci.id
        order by eci.created_time DESC, intention_degree ASC
    </select>

    <select id="getCustomerByName" resultType="com.nnb.erp.domain.vo.ErpCustomerIntentionVo">
        SELECT
            ee.vc_company_name as enterpriseName,
            ee.id as enterpriseId,
            ssm.account_user_id,
            su.nick_name as accountUserName
        FROM
            s_service_main ssm
        LEFT JOIN erp_enterprise ee ON ssm.num_enterprise_id = ee.id
        LEFT JOIN sys_user su ON su.user_id = ssm.account_user_id
        <where>
            and ssm.id IN (SELECT MAX(id) FROM s_service_main WHERE service_type = 10 AND service_status not in (8,9,10) GROUP BY num_enterprise_id)
            <if test="enterpriseName != null and enterpriseName != ''"> and ee.vc_company_name like concat('%', #{enterpriseName}, '%')</if>
        </where>
        limit 10
    </select>

    <select id="getUserIdByLogin" resultType="java.lang.Long">
        SELECT
            su.user_id
        FROM
        sys_user su
        LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
        where 1
        ${params.dataScope}
    </select>

    <select id="selectErpCustomerIntentionById" parameterType="Long" resultMap="ErpCustomerIntentionResult">
        <include refid="selectErpCustomerIntentionVo"/>
        where id = #{id}
    </select>

    <insert id="insertErpCustomerIntention" parameterType="ErpCustomerIntention" useGeneratedKeys="true" keyProperty="id">
        insert into erp_customer_intention
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientId != null">client_id,</if>
            <if test="intentionSource != null">intention_source,</if>
            <if test="intentionDegree != null">intention_degree,</if>
            <if test="intentionConfigId != null">intention_config_id,</if>
            <if test="followStatus != null">follow_status,</if>
            <if test="dealDate != null">deal_date,</if>
            <if test="dealFee != null">deal_fee,</if>
            <if test="memo != null">memo,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="accountUser != null">account_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientId != null">#{clientId},</if>
            <if test="intentionSource != null">#{intentionSource},</if>
            <if test="intentionDegree != null">#{intentionDegree},</if>
            <if test="intentionConfigId != null">#{intentionConfigId},</if>
            <if test="followStatus != null">#{followStatus},</if>
            <if test="dealDate != null">#{dealDate},</if>
            <if test="dealFee != null">#{dealFee},</if>
            <if test="memo != null">#{memo},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="accountUser != null">#{accountUser},</if>
         </trim>
    </insert>

    <update id="updateErpCustomerIntention" parameterType="ErpCustomerIntention">
        update erp_customer_intention
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="intentionSource != null">intention_source = #{intentionSource},</if>
            <if test="intentionDegree != null">intention_degree = #{intentionDegree},</if>
            <if test="intentionConfigId != null">intention_config_id = #{intentionConfigId},</if>
            <if test="followStatus != null">follow_status = #{followStatus},</if>
            <if test="dealDate != null">deal_date = #{dealDate},</if>
            <if test="dealFee != null">deal_fee = #{dealFee},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="accountUser != null">account_user = #{accountUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpCustomerIntentionById" parameterType="Long">
        delete from erp_customer_intention where id = #{id}
    </delete>

    <delete id="deleteErpCustomerIntentionByIds" parameterType="String">
        delete from erp_customer_intention where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>
