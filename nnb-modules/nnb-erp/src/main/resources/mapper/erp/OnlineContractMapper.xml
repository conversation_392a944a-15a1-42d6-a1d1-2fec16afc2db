<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.OnlineContractMapper">
    <update id="updateOnlineContract">
        update tb_online_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractNumber!=null">contractNumber=#{contractNumber},</if>
            <if test="contractIndex!=null">contractIndex=#{contractIndex,</if>
            <if test="showNumber!=null">showNumber={showNumber},</if>
            <if test="contractType!=null">contractName=#{contractName},</if>
            <if test="clientId!=null">clientId=#{clientId},</if>
            <if test="contractContent!=null">contractContent=#{contractContent},</if>
            <if test="createdBy!=null">createdBy=#{createdBy},</if>
            <if test="createdTime!=null">createdTime=#{createdTime},</if>
            <if test="updateBy!=null">updateBy=#{updateBy},</if>
            <if test="updateTime!=null">updateTime=#{updateTime},</if>
            <if test="status!=null">status=#{status},</if>
        </trim>
        where orderId=#{orderId}
    </update>

    <!-- 指定产品是否需要合同。 -->
    <select id="isNeedContract" resultType="Integer">
        SELECT IFNULL(num_is_need_contract, -1) FROM erp_product_detail WHERE num_product_id = #{productId}
    </select>

    <!-- 获取指定客户的记账服务单数量。 -->
    <select id="countClientBookKeepBiz" resultType="Integer">
        SELECT COUNT(ssm.id)
        from s_service_main ssm
        where ssm.service_type = 10
          and ssm.service_status in(1,2,3)
          and ssm.num_enterprise_id = #{numEnterpriseId}
          <if test="orderId != null">
              and ssm.order_id != #{orderId}
          </if>
    </select>

    <!-- 生成合同编号。 -->
    <select id="genContractNumber" resultType="String">
        SELECT
            CONCAT(
                'NNB-',
                DATE_FORMAT(NOW(), '%Y%m'),
                LPAD(COUNT(DISTINCT contractNumber) + 1, 6, 0)
            )
        FROM tb_online_contract;
    </select>

    <!-- 新增电子合同。 -->
    <insert id="saveContractBatch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_online_contract
            (
            `contractNumber`,
            `contractIndex`,
            `showNumber`,
            `contractType`,
            `contractName`,
            `clientId`,
            `orderId`,
            `contractContent`,
            `createdBy`,
            `createdTime`,
            `updateBy`,
            `updateTime`
            )
         VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.contractNumber},
                #{entity.contractIndex},
                #{entity.showNumber},
                #{entity.contractType},
                #{entity.contractName},
                #{entity.clientId},
                #{entity.orderId},
                #{entity.contractContent},
                #{entity.createdBy},
                #{entity.createdTime},
                #{entity.updateBy},
                #{entity.updateTime}
            )
        </foreach>

    </insert>

    <!-- 获取指定订单的全部电子合同。 -->
    <select id="getPdf" resultType="com.nnb.erp.domain.vo.onlineContract.CheckContractVO">
        SELECT
            t1.showNumber,
            t1.contractType,
            t2.contract_subject AS 'contractSubject',
            t1.contractName,
            t1.contractContent AS 'contractContentStr',
            t2.id AS orderId
        FROM tb_online_contract t1
        INNER JOIN erp_orders t2 ON t1.orderId = t2.id AND t2.cipher_id = #{orderId} AND t2.contract_subject IS NOT NULL and t1.status = 1
    </select>
    <select id="selectOnlineContracById" resultType="com.nnb.erp.domain.OnlineContractEntity">
        select  id,showNumber,contractType,contractName,contractContent,status, audit_status as auditStatus from tb_online_contract where orderId =#{orderId}
    </select>

    <select id="selectOnlineContracByOrderId" resultType="com.nnb.erp.domain.OnlineContractEntity">
        select id, contractNumber, showNumber, contractType, contractName, contractContent, status
        from tb_online_contract
        where orderId = #{orderId}
        group by contractNumber
        order by createdTime desc
    </select>

    <update id="updateOnlineContractByContractNumber">
        update tb_online_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="updateBy!=null">updateBy=#{updateBy},</if>
            <if test="updateTime!=null">updateTime=#{updateTime},</if>
            <if test="status!=null">status=#{status},</if>
            <if test="auditStatus!=null">audit_status=#{auditStatus},</if>
        </trim>
        where orderId = #{orderId} and audit_status = #{auditStatusQuery} and status = #{statusQuery}
    </update>

</mapper>
