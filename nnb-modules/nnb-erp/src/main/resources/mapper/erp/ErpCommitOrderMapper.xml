<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCommitOrderMapper">

    <!-- 组合优惠产品映射。 -->
    <resultMap id="combinationByJsonMap" type="com.nnb.erp.domain.vo.ErpCombinedForConfirmOrderVO">
        <result property="combinationId" column="combinationId"/>
        <result property="combinationName" column="combinationName"/>
        <collection property="productInfo" ofType="com.nnb.erp.domain.vo.ErpCombinedProductForConfirmOrderVO">
            <result property="productId" column="productId"/>
            <result property="productType" column="productType"/>
            <result property="productName" column="productName"/>
            <result property="unitPrice" column="unitPrice"/>
            <result property="discountedPrice" column="discountedPrice"/>
            <result property="discountCount" column="discountCount"/>
        </collection>
    </resultMap>
    <insert id="insterZhuxiao">
        insert into op_zhuxiao
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="opId!=null">op_id,</if>
            <if test="gsZxStatus!=null">gs_zx_status,</if>
            <if test="gsZxUser!=null">gs_zx_user,</if>
            <if test="swZxStatus!=null">sw_zx_status,</if>
            <if test="swZxUser!=null">sw_zx_user,</if>
            <if test="yhZxStatus!=null">yh_zx_status,</if>
            <if test="yhZxUser!=null">yh_zx_user,</if>
            <if test="sbZxStatus!=null">sb_zx_status,</if>
            <if test="sbZxUser!=null">sb_zx_user,</if>
            <if test="gjjZxStatus!=null">gjj_zx_status,</if>
            <if test="gjjZxUser!=null">gjj_zx_user,</if>
            <if test="zxStatus!=null">gs_zx_remark,</if>
            <if test="swZxRemark!=null">sw_zx_remark,</if>
            <if test="yhZxRemark!=null">yh_zx_remark,</if>
            <if test="sbZxRemark!=null">sb_zx_remark,</if>
            <if test="gjjZxRemark!=null">gjj_zx_remark,</if>
            <if test="createdBy!=null">created_by,</if>
            <if test="updatedBy!=null">updated_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="opId!=null">#{opId},</if>
            <if test="gsZxStatus!=null">#{gsZxStatus},</if>
            <if test="gsZxUser!=null">#{gsZxUser},</if>
            <if test="swZxStatus!=null">#{swZxStatus},</if>
            <if test="swZxUser!=null">#{swZxUser},</if>
            <if test="yhZxStatus!=null">#{yhZxStatus},</if>
            <if test="yhZxUser!=null">#{yhZxUser},</if>
            <if test="sbZxStatus!=null">#{sbZxStatus},</if>
            <if test="sbZxUser!=null">#{sbZxUser},</if>
            <if test="gjjZxStatus!=null">#{gjjZxStatus},</if>
            <if test="gjjZxUser!=null">#{gjjZxUser},</if>
            <if test="zxStatus!=null">#{zxStatus},</if>
            <if test="swZxRemark!=null">#{swZxRemark},</if>
            <if test="yhZxRemark!=null">#{yhZxRemark},</if>
            <if test="sbZxRemark!=null">#{sbZxRemark},</if>
            <if test="gjjZxRemark!=null">#{gjjZxRemark},</if>
            <if test="createdBy!=null">#{createdBy},</if>
            <if test="updatedBy!=null">#{updatedBy},</if>
        </trim>

    </insert>

    <!-- 获取待确认订单的产品基本信息。 -->
    <select id="getProductInfoByIds" resultType="com.nnb.erp.domain.vo.ErpProductForConfirmOrderVO">
        SELECT t1.*,
               GROUP_CONCAT(DISTINCT t2.col4) AS 'region',
               GROUP_CONCAT(DISTINCT t2.col9) AS 'belongCity'
        FROM (
        SELECT epd.num_product_id AS 'productId',
        epd.num_name_id as 'numNameId',
        epd.service_type_id as 'serviceTypeId',
        epd.num_is_need_contract as 'numIsNeedContract',
        ept.num_type_id AS 'typeId',
        ept.num_classification_id AS 'classificationId',
        epn.vc_product_name AS 'productName',
        epd.num_service_id AS 'serviceId',
        epd.billing_check_cost AS 'billingCheckCost',
        eps.vc_service_name AS 'vcServiceName',
        epd.early_warning AS 'earlyWarning',
        epd.early_warning_url AS 'earlyWarningUrl',
        epd.qualifications_extension AS 'qualificationsExtension',
        epd.annual_inspection AS 'annualInspection',
        service.name        as 'serviceName',
        GROUP_CONCAT(DISTINCT eptd.vc_tax_name) AS 'taxName',
        epud.vc_unit_name AS 'unit',
        configuration.product_price  as unitPrice,
        configuration.activity_start_time as activityStartTime,
        configuration.activity_end_time   as activityEndTime,
        configuration.discount_amount     as activityDiscountAmount,
        configuration.id                  as configurationId,
        GROUP_CONCAT(DISTINCT eptd.num_tax_id) AS 'taxIds',
        epd.num_is_up AS 'isUp',
        GROUP_CONCAT(DISTINCT epar.num_area_id) AS 'regionIds'
        FROM erp_product_detail epd
        LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id AND epn.num_is_use = 1
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        left join erp_product_configuration configuration on configuration.product_id = epd.num_product_id
        LEFT JOIN erp_product_service eps ON eps.num_service_id = epd.num_service_id AND eps.num_state = 1
        LEFT JOIN s_config_service_type service on epd.service_type_id = service.id AND service.status = 1
        LEFT JOIN erp_product_tax_relation eptr ON eptr.num_product_id = epd.num_product_id
        LEFT JOIN erp_product_tax_dict eptd ON eptr.num_tax_id = eptd.num_tax_id
        LEFT JOIN erp_product_unit_dict epud ON epud.num_unit_id = epd.num_unit_id AND epud.num_is_use = 1
        LEFT JOIN erp_product_area_relation epar ON epar.num_product_id = epd.num_product_id
        <where>
            and epd.num_product_id IN (
                <foreach collection="dto.productList" item="productId" separator=",">
                    #{productId}
                </foreach>
            )
        <if test="dto.deptId!=null">
            and FIND_IN_SET(#{dto.deptId}, configuration.dept_id)
        </if>
        <if test="dto.filterIsOldData == null || dto.filterIsOldData != 1">
            and epd.is_old_data = 0
        </if>
        </where>
        GROUP BY epd.num_product_id ORDER BY NULL
        ) t1
        LEFT JOIN (
        SELECT r1.title AS 'col1',
        r2.title AS 'col2',
        r3.title AS 'col3',
        CONCAT(IFNULL(r1.title, ''), IF(r2.title IS NOT NUll, CONCAT('-', r2.title), ''), IF(r3.title IS NOT NULL,
        CONCAT('-', r3.title), '')) AS 'col4',
        CONCAT(IFNULL(r1.title, ''), IF(r2.title IS NOT NUll, CONCAT('-', r2.title), '')) AS 'col5',
        CONCAT(IFNULL(r2.title, ''), IF(r3.title IS NOT NULL, CONCAT('-', r3.title), '')) AS 'col6',
        CONCAT(IFNULL(r1.title, ''), IF(r3.title IS NOT NULL, CONCAT('-', r3.title), '')) AS 'col7',
        r1.id AS 'col8',
        r2.id AS 'col9',
        IFNULL(r3.id, r2.id) AS 'col10'
        FROM com_dict_region r1
        LEFT JOIN com_dict_region r2 ON r1.id = r2.parent_id
        LEFT JOIN com_dict_region r3 ON r2.id = r3.parent_id
        WHERE r1.parent_id = 0
        ORDER BY r1.id ASC, r2.id ASC, r3.id ASC
        ) t2 ON FIND_IN_SET(t2.col10, t1.regionIds)
        GROUP BY t1.productId ORDER BY NULL;
    </select>

    <!-- 通过json获取组合信息。 -->
    <select id="getCombinationByJson" resultMap="combinationByJsonMap">
        SELECT eca.id AS 'combinationId', eca.vc_name AS 'combinationName', ecap.num_product_id AS 'productId', ecap.num_type AS 'productType', ecap.vc_product_name AS 'productName', ecap.num_price AS 'unitPrice', ecap.num_activity_price AS 'discountedPrice', ecap.num_product_count AS 'discountCount'
        FROM erp_combined_activity eca
                 LEFT JOIN erp_combined_activity_product ecap ON ecap.num_combined_activity_id = eca.id
        WHERE eca.vc_combined_product = #{combinationJson}
          AND eca.num_status = 1
          AND eca.num_channel = 2
          AND eca.num_type = 0
          AND NOW() BETWEEN eca.dat_start_time AND eca.dat_end_time;
    </select>

    <!-- 通过产品标识集合json文本获取组合信息。 -->
    <select id="getCombinationByIdList" resultMap="combinationByJsonMap">
        SELECT eca.id AS 'combinationId', eca.vc_name AS 'combinationName', ecap.num_product_id AS 'productId', ecap.num_type AS 'productType', ecap.vc_product_name AS 'productName', ecap.num_price AS 'unitPrice', ecap.num_activity_price AS 'discountedPrice', ecap.num_product_count AS 'discountCount'
        FROM (
                 SELECT t1.activity_id, GROUP_CONCAT(t1.db_product) AS 'db_products'
                 FROM (
                          SELECT ecap.num_combined_activity_id AS 'activity_id', GROUP_CONCAT(CONCAT(ecap.num_product_id, '-', ecap.num_product_count)) AS 'db_product'
                          FROM erp_combined_activity_product ecap
                          GROUP BY ecap.num_combined_activity_id, ecap.num_product_id
                          ORDER BY ecap.num_combined_activity_id, ecap.num_product_id
                      ) t1
                 GROUP BY t1.activity_id
                 ORDER BY NULL
             ) t2
                 INNER JOIN (
            SELECT GROUP_CONCAT(in_product) AS 'in_products'
            FROM (
                     SELECT CONCAT(temp.product_id, '-', COUNT(*)) AS 'in_product'
                     FROM JSON_TABLE(#{idList}, '$[*]' COLUMNS (product_id BIGINT(20) PATH '$')) temp
                     GROUP BY temp.product_id
                     ORDER BY temp.product_id
                 ) t3
        ) t4 ON t4.in_products = t2.db_products
                 INNER JOIN erp_combined_activity eca
                            ON eca.id = t2.activity_id AND eca.num_status = 1 AND eca.num_channel = 2 AND
                               eca.num_type = 0 AND NOW() BETWEEN eca.dat_start_time AND eca.dat_end_time
                 INNER JOIN erp_combined_activity_product ecap ON ecap.num_combined_activity_id = eca.id;
    </select>

    <!-- 获取指定产品可用优惠券，用于CRM确认订单。 -->
    <select id="getDiscountsForCrm" resultType="com.nnb.erp.domain.vo.ErpDiscountForConfirmOrderVO">
        SELECT ec.id AS 'discountId', ec.num_type AS 'discountType', ec.vc_name AS 'discountName', ec.num_discount AS 'numDiscount', ec.num_meet AS 'meetThreshold', ec.num_price AS 'meetPrice'
        FROM erp_coupon ec
                 LEFT JOIN erp_coupon_dept ecd ON ecd.num_coupon_id = ec.id
                 LEFT JOIN erp_coupon_product ecp ON ecp.num_coupon_id = ec.id
                 LEFT JOIN erp_coupon_product_type ecpt ON ecpt.num_coupon_id = ec.id
        WHERE ec.num_channel IN (0, 2)
          AND (
                ecd.num_dept_id IS NULL
                OR ecd.num_dept_id = #{deptId}
            )
          AND (
                ecp.num_product_id IS NULL
                OR ecp.num_product_id = #{productId}
            )
          AND (
                ecpt.num_product_type_id IS NULL
                OR ecpt.num_product_type_id = #{classificationId}
            )
          AND (
            NOW() BETWEEN ec.dat_start_time AND ec.dat_end_time
            )
          AND (
                    ec.num_type = 0
                OR (
                        ec.num_type = 1 AND ec.num_meet &lt;= #{sumPrice}
                        )
            )
          AND ec.num_status = 1
        GROUP BY ec.id
        ORDER BY NULL
        ;
    </select>


    <!-- 获取指定产品可用优惠券，用于小程序确认订单。 -->
    <select id="getDiscountsForXcx" resultType="com.nnb.erp.domain.vo.ErpDiscountForConfirmOrderVO">
        SELECT ec.id AS 'discountId', ec.num_type AS 'discountType', ec.vc_name AS 'discountName', ec.num_discount AS 'numDiscount', ec.num_meet AS 'meetThreshold', ec.num_price AS 'meetPrice'
        FROM erp_coupon_use_record ecur
                 INNER JOIN erp_coupon ec ON ec.id = ecur.num_coupon_id
                 LEFT JOIN erp_coupon_product ecp ON ecp.num_coupon_id = ec.id
                 LEFT JOIN erp_coupon_product_type ecpt ON ecpt.num_coupon_id = ec.id
        WHERE ecur.vc_phone = #{phone}
          AND ecur.num_status = 0
          AND ec.num_channel IN (0, 1)
          AND (
                ecp.num_product_id IS NULL
                OR ecp.num_product_id = #{productId}
            )
          AND (
                ecpt.num_product_type_id IS NULL
                OR ecpt.num_product_type_id = #{classificationId}
            )
          AND (
            NOW() BETWEEN ec.dat_start_time AND ec.dat_end_time
            )
          AND (
                    ec.num_type = 0
                OR (
                        ec.num_type = 1 AND ec.num_meet &lt;= #{sumPrice}
                        )
            )
          AND ec.num_status = 1
        GROUP BY ec.id
        ORDER BY NULL
        ;
    </select>

    <!-- 获取订单编号。 -->
    <select id="getOrderNumber" resultType="String">
        SELECT CONCAT(
                       DATE_FORMAT(NOW(), '%Y%m%d'),
                       LPAD(COUNT(*) + 1, 4, 0)
                   ) AS 'orderNUmber'
        FROM erp_orders
        WHERE DATE_FORMAT(NOW(), '%Y%m%d') = DATE_FORMAT(dat_signing_datecreated_time, '%Y%m%d');
    </select>

    <!-- 更新指定订单的收款人信息为最新。 -->
    <update id="updatePayeeForOrder">
        UPDATE
            erp_orders t1,
            (
            SELECT
            eopt.num_payee,
            eopt.dat_collection_time
            FROM erp_order_payment_term eopt
            LEFT JOIN erp_retainage_return err ON err.id = eopt.num_retainage_id
            WHERE
            eopt.num_order_id = #{orderId}
            OR
            (
            err.num_order_id = #{orderId}
            AND
            err.num_status IN (0, 1)
            )
            ORDER BY eopt.dat_collection_time DESC
            LIMIT 1
            ) t2
        SET t1.num_payee = t2.num_payee, t1.dat_signing_datecollect_money_time = t2.dat_collection_time
        WHERE t1.id = #{orderId}
    </update>

    <!-- 获取指定订单内、指定产品的废弃状态。 -->
    <select id="getProductForServiceOrder" resultType="com.nnb.erp.domain.vo.ErpProductForServiceOrdersVO">
        SELECT IFNULL(eso.num_is_deprecated, 0) AS 'deprecatedStatus', eso.id AS 'serviceOrderId', IFNULL(eso.num_total_price, 0) AS 'totalPrice', IFNULL(eso.num_pay_price, 0) AS 'payPrice', IFNULL(eso.num_last_price, 0) AS 'lastPrice', IFNULL(SUM(errd.num_discounts), 0) AS 'retainageDiscountPrice'
        FROM erp_service_orders eso
                 LEFT JOIN erp_retainage_return_detail errd ON errd.num_service_order_id = eso.id
        WHERE eso.num_order_id = #{orderId}
          AND eso.num_product_id = #{productId}
        GROUP BY eso.num_product_id, eso.num_is_deprecated, eso.id
        ORDER BY NULL
    </select>

    <!-- 更新指定订单的业务审核标识。 -->
    <update id="updateApprovalForOrder">
        UPDATE erp_orders
        SET num_business_approval_id = #{businessApprovalId}
        WHERE id = #{orderId}
    </update>

    <!-- 获取指定订单历史退款金额。 -->
    <select id="getSummaryRefundPriceByOrderId" resultType="BigDecimal">
        SELECT IFNULL(SUM(esor.num_refund_price), 0)
        FROM erp_orders eo
                 LEFT JOIN erp_service_orders eso ON eso.num_order_id = eo.id
                 LEFT JOIN erp_service_order_refund esor ON esor.num_service_order_id = eso.id
        WHERE eo.id = #{orderId}
    </select>
    <select id="getDiscountAmount" resultType="com.nnb.erp.domain.vo.ErpDiscountForConfirmOrderVO">
        SELECT SUM(ec.discount_amount) as discountPrice
        FROM erp_service_orders eso
                 LEFT JOIN erp_discount_coupon ec ON ec.num_product_id = eso.num_product_id
        WHERE eso.num_order_id = #{orderId}
          AND eso.num_product_id = #{productId}

    </select>
    <select id="getCombinationActivity" resultType="com.nnb.erp.domain.ErpCombinedActivity">
            select  DISTINCT(ea.id) as id,ea.vc_name as vcName,
            detail.num_product_id        as numProductId,
            class.vc_classification_name as vcClassificationName,
            type.vc_type_name            as vcTypeName,
            name.vc_product_name         as vcProductName,
            configuration.product_price   as numPrice,
            ec.num_activity_price        as numActivityPrice,
            er.vc_area_name             as numAreaName,
          CONCAT(ea.dat_start_time, "至", ea.dat_end_time) as valiTime
            from erp_product_detail detail
            left join erp_product_configuration configuration on configuration.product_id = detail.num_product_id
            left join erp_product_name name on detail.num_name_id = name.num_name_id
            left join erp_product_type type on name.num_type_id = type.num_type_id
            left join erp_product_classification class on type.num_classification_id = class.num_classification_id
            LEFT JOIN  erp_combined_activity_product ec on ec.num_product_id=detail.num_product_id
            LEFT JOIN erp_combined_activity ea ON ea.id = ec.num_combined_activity_id
            LEFT JOIN com_dict_region AS com ON ea.num_area_id = com.id
            LEFT JOIN erp_product_region er on er.num_region_dict_id=ea.num_area_id
            LEFT JOIN erp_activity_dept ead on ead.num_combined_activity_id=ea.id


            <where>
            and ea.num_status=1 and configuration.product_price=ec.num_price
            <if test="vcName !=null">and  ea.vc_name LIKE concat('%', #{vcName}, '%')</if>
            <if test="datStartTime != null and datEndTime!= null">
                and (ea.dat_start_time &gt;= #{datStartTimeString} and ea.dat_start_time &lt;= #{datEndTimeString})
            </if>
            <if test="numStatus !=null">and ea.num_status=1</if>
            <if test="id !=null ">and ea.id=#{id}</if>
            <if test="numAreaName!=null ">and er.num_area_name=#{numAreaName}</if>
            <if test="numAreaId !=null">and com.id=#{numAreaId}</if>
            <if test="deptId!=null">and FIND_IN_SET(#{deptId}, ead.num_dept_id)</if>
            </where>
    </select>
    <select id="getCombinationActivityByIds" resultType="com.nnb.erp.domain.vo.ErpProductForConfirmOrderVO">

        SELECT t1.*, GROUP_CONCAT(DISTINCT t2.col4) AS 'region', GROUP_CONCAT(DISTINCT t2.col9) AS 'belongCity'
        FROM (
                 SELECT epd.num_product_id AS 'productId',
                         er.num_price as 'numPrice',
                        er.num_activity_price as 'numActivityPrice',
                        ept.num_type_id AS 'typeId',
                         ept.num_classification_id AS 'classificationId',
                         epn.vc_product_name AS 'productName',
                         epd.num_service_id AS 'serviceId',
                         eps.vc_service_name AS 'serviceName',
                         GROUP_CONCAT(DISTINCT eptd.vc_tax_name) AS 'taxName',
                         epud.vc_unit_name AS 'unit',
                         epd.num_price AS 'unitPrice',
                         GROUP_CONCAT(DISTINCT eptd.num_tax_id) AS 'taxIds',
                         epd.num_is_up AS 'isUp',
                         GROUP_CONCAT(DISTINCT epar.num_area_id) AS 'regionIds'
                 FROM erp_product_detail epd
                          LEFT JOIN erp_product_name epn ON epn.num_name_id = epd.num_name_id AND epn.num_is_use = 1
                          LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
                          LEFT JOIN erp_product_service eps ON eps.num_service_id = epd.num_service_id AND eps.num_state = 1
                          LEFT JOIN erp_product_tax_relation eptr ON eptr.num_product_id = epd.num_product_id
                          LEFT JOIN erp_product_tax_dict eptd ON eptr.num_tax_id = eptd.num_tax_id
                          LEFT JOIN erp_product_unit_dict epud ON epud.num_unit_id = epd.num_unit_id AND epud.num_is_use = 1
                          LEFT JOIN erp_product_area_relation epar ON epar.num_product_id = epd.num_product_id
                          LEFT JOIN erp_combined_activity_product er on er.num_product_id=epar.num_product_id

                 WHERE er.num_combined_activity_id IN (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
                     )
                 GROUP BY epd.num_product_id ORDER BY NULL
             ) t1
                 LEFT JOIN (
            SELECT r1.title AS 'col1',
                    r2.title AS 'col2',
                    r3.title AS 'col3',
                    CONCAT(IFNULL(r1.title, ''), IF(r2.title IS NOT NUll, CONCAT('-', r2.title), ''), IF(r3.title IS NOT NULL,
                                                                                                         CONCAT('-', r3.title), '')) AS 'col4',
                    CONCAT(IFNULL(r1.title, ''), IF(r2.title IS NOT NUll, CONCAT('-', r2.title), '')) AS 'col5',
                    CONCAT(IFNULL(r2.title, ''), IF(r3.title IS NOT NULL, CONCAT('-', r3.title), '')) AS 'col6',
                    CONCAT(IFNULL(r1.title, ''), IF(r3.title IS NOT NULL, CONCAT('-', r3.title), '')) AS 'col7',
                    r1.id AS 'col8',
                    r2.id AS 'col9',
                    IFNULL(r3.id, r2.id) AS 'col10'
            FROM com_dict_region r1
                     LEFT JOIN com_dict_region r2 ON r1.id = r2.parent_id
                     LEFT JOIN com_dict_region r3 ON r2.id = r3.parent_id
            WHERE r1.parent_id = 0
            ORDER BY r1.id ASC, r2.id ASC, r3.id ASC
        ) t2 ON FIND_IN_SET(t2.col10, t1.regionIds)
        GROUP BY t1.productId ORDER BY NULL;
    </select>

    <select id="getContractNum" resultType="java.lang.String">
        select vc_contract_number  from erp_contract where id=#{contractId}
    </select>


</mapper>
