<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpWtdzKpMapper">
    
    <resultMap type="ErpWtdzKp" id="ErpWtdzKpResult">
        <result property="id"    column="id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="kpDate"    column="kp_date"    />
        <result property="kpCount"    column="kp_count"    />
        <result property="kpFee"    column="kp_fee"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyUser"    column="company_user"    />
        <result property="addressCost"    column="address_cost"    />
        <result property="createdUser"    column="created_user"    />
        <result property="createdDate"    column="created_date"    />
        <result property="updateUser"    column="update_user"    />
        <result property="updateDate"    column="update_date"    />
        <result property="settlement"    column="settlement"    />
        <result property="advance"    column="advance"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectErpWtdzKpVo">
        select id, service_id, kp_date, kp_count, kp_fee, company_name, company_user, address_cost, created_user, created_date, update_user, update_date, settlement, advance, status from erp_wtdz_kp
    </sql>

    <select id="selectErpWtdzKpList" parameterType="com.nnb.erp.domain.dto.ErpWtdzKpDto" resultType="com.nnb.erp.domain.vo.ErpWtdzKpVo">
        SELECT
            ewk.id,
            ewk.kp_date,
            ewk.kp_count,
            ewk.kp_fee,
            ewk.company_name,
            ewk.company_user,
            ebsa.agent_number AS agentNumber,
            su.nick_name AS createdUserName,
            ewk.created_date,
            ewk.advance,
            ewk.settlement,
            ewk.status,
            CASE
                ewk.settlement
                WHEN 2 THEN
                "否" ELSE "是"
            END AS settlementName,
            eo.vc_order_number AS orderNumber,
            eo.dat_signing_date as signDate,
            signSu.nick_name AS signUserName,
            ebsac.settlement_radio,
            ebsa.id as agentId
        FROM
            erp_wtdz_kp ewk
        LEFT JOIN erp_biz_service_address_cost ebsac ON ebsac.id = ewk.address_cost
        LEFT JOIN erp_biz_service_agent ebsa ON ebsa.id = ebsac.agent_id
        LEFT JOIN sys_user su ON su.user_id = ewk.created_user
        LEFT JOIN s_service_main ssm ON ssm.id = ewk.service_id
        LEFT JOIN erp_orders eo ON eo.id = ssm.order_id
        LEFT JOIN sys_user signSu ON signSu.user_id = eo.num_user_id
        <where>
            <if test="status != null "> and ewk.status = #{status}</if>
            <if test="settlement != null "> and ewk.settlement = #{settlement}</if>
            <if test="agentId != null "> and ebsa.id = #{agentId}</if>
            <if test="orderNumber != null and orderNumber != ''"> and eo.vc_order_number = #{orderNumber}</if>
            <if test="kpDate != null "> and ewk.kp_date = #{kpDate}</if>
            <if test="serviceId != null "> and ewk.service_id = #{serviceId}</if>
            <if test="idList != null and idList.size() > 0">
                and ewk.id in
                <foreach item="id" collection="idList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectErpWtdzKpById" parameterType="Long" resultMap="ErpWtdzKpResult">
        <include refid="selectErpWtdzKpVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpWtdzKp" parameterType="ErpWtdzKp" useGeneratedKeys="true" keyProperty="id">
        insert into erp_wtdz_kp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">service_id,</if>
            <if test="kpDate != null">kp_date,</if>
            <if test="kpCount != null">kp_count,</if>
            <if test="kpFee != null">kp_fee,</if>
            <if test="companyName != null">company_name,</if>
            <if test="companyUser != null">company_user,</if>
            <if test="addressCost != null">address_cost,</if>
            <if test="createdUser != null">created_user,</if>
            <if test="createdDate != null">created_date,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="settlement != null">settlement,</if>
            <if test="advance != null">advance,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">#{serviceId},</if>
            <if test="kpDate != null">#{kpDate},</if>
            <if test="kpCount != null">#{kpCount},</if>
            <if test="kpFee != null">#{kpFee},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyUser != null">#{companyUser},</if>
            <if test="addressCost != null">#{addressCost},</if>
            <if test="createdUser != null">#{createdUser},</if>
            <if test="createdDate != null">#{createdDate},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="settlement != null">#{settlement},</if>
            <if test="advance != null">#{advance},</if>
         </trim>
    </insert>

    <update id="updateErpWtdzKp" parameterType="ErpWtdzKp">
        update erp_wtdz_kp
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="kpDate != null">kp_date = #{kpDate},</if>
            <if test="kpCount != null">kp_count = #{kpCount},</if>
            <if test="kpFee != null">kp_fee = #{kpFee},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyUser != null">company_user = #{companyUser},</if>
            <if test="addressCost != null">address_cost = #{addressCost},</if>
            <if test="createdUser != null">created_user = #{createdUser},</if>
            <if test="createdDate != null">created_date = #{createdDate},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="settlement != null">settlement = #{settlement},</if>
            <if test="advance != null">advance = #{advance},</if>
            <if test="status != null">status = #{status},</if>
            <if test="settlementRadio != null">settlement_radio = #{settlementRadio},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpWtdzKpById" parameterType="Long">
        delete from erp_wtdz_kp where id = #{id}
    </delete>

    <delete id="deleteErpWtdzKpByIds" parameterType="String">
        delete from erp_wtdz_kp where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectJYInfoByIds" parameterType="Long" resultType="com.nnb.erp.domain.vo.ErpWtdzKpVo">
        SELECT
            ewk.id,
            ewk.settlement,
            ssm.product_id AS productId,
            ebsac.cost_name_id AS costNameId,
            ebsac.cooperate_status AS cooperateStatus,
            ebsac.settlement_radio AS settlementRadio,
            ebsac.agent_id AS agentId,
            ewk.status,
            ssm.order_num AS orderNumber
        FROM
        erp_wtdz_kp ewk
        LEFT JOIN erp_biz_service_address_cost ebsac ON ebsac.id = ewk.address_cost
        LEFT JOIN s_service_main ssm ON ssm.id = ewk.service_id
        <where>
            <if test="idList != null and idList.size() > 0">
                and ewk.id in
                <foreach item="id" collection="idList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getListByApproveId" parameterType="Long" resultType="com.nnb.erp.domain.vo.ErpWtdzKpExportVo">
        SELECT
            eo.vc_order_number AS orderNumber,
            eo.dat_signing_date as signDate,
            signSu.nick_name AS signUserName,
            ewk.company_name,
            ewk.company_user,
            eso.num_pay_price as collectionFee,
            eso.gt_kp as allKpFee,
            ewk.kp_fee,
            ebsa.agent_number AS agentNumber,
            ebsac.settlement_radio
        FROM  erp_examine_approve eea
        LEFT JOIN erp_examine_other_order_pay eeoop ON eea.other_id = eeoop.id
        LEFT JOIN erp_wtdz_kp ewk ON FIND_IN_SET(ewk.id, eeoop.wtdz_ids)
        LEFT JOIN s_service_main ssm ON ssm.id = ewk.service_id
        LEFT JOIN erp_orders eo ON eo.id = ssm.order_id
        LEFT JOIN sys_user signSu ON signSu.user_id = eo.num_user_id
        LEFT JOIN erp_service_orders eso ON (eso.num_order_id = ssm.order_id AND eso.num_product_id = ssm.product_id)
        LEFT JOIN erp_biz_service_address_cost ebsac ON ebsac.id = ewk.address_cost
        LEFT JOIN erp_biz_service_agent ebsa ON ebsa.id = ebsac.agent_id
        where eea.id = #{id}
    </select>

</mapper>