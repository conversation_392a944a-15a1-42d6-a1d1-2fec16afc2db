<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.product.ErpVerbalTrickRecordMapper">
    
    <resultMap type="ErpVerbalTrickRecord" id="ErpVerbalTrickRecordResult">
        <result property="id"    column="id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="nicheFlowConfId"    column="niche_flow_conf_id"    />
        <result property="operateType"    column="operate_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectErpVerbalTrickRecordVo">
        select id, clue_id, niche_flow_conf_id, operate_type, create_time, update_time, create_user, update_user, del_flag from erp_verbal_trick_record
    </sql>

    <select id="selectErpVerbalTrickRecordList" parameterType="ErpVerbalTrickRecord" resultMap="ErpVerbalTrickRecordResult">
        <include refid="selectErpVerbalTrickRecordVo"/>
        <where>  
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="nicheFlowConfId != null "> and niche_flow_conf_id = #{nicheFlowConfId}</if>
            <if test="operateType != null "> and operate_type = #{operateType}</if>
            <if test="createUser != null "> and create_user = #{createUser}</if>
            <if test="updateUser != null "> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectErpVerbalTrickRecordById" parameterType="Long" resultMap="ErpVerbalTrickRecordResult">
        <include refid="selectErpVerbalTrickRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpVerbalTrickRecord" parameterType="ErpVerbalTrickRecord" useGeneratedKeys="true" keyProperty="id">
        insert into erp_verbal_trick_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="nicheFlowConfId != null">niche_flow_conf_id,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="nicheFlowConfId != null">#{nicheFlowConfId},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateErpVerbalTrickRecord" parameterType="ErpVerbalTrickRecord">
        update erp_verbal_trick_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="nicheFlowConfId != null">niche_flow_conf_id = #{nicheFlowConfId},</if>
            <if test="operateType != null">operate_type = #{operateType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpVerbalTrickRecordById" parameterType="Long">
        delete from erp_verbal_trick_record where id = #{id}
    </delete>

    <delete id="deleteErpVerbalTrickRecordByIds" parameterType="String">
        delete from erp_verbal_trick_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>