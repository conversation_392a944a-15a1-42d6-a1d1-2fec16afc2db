<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nnb.erp.mapper.ErpCombinedActivityProductMapper">
    
    <resultMap type="ErpCombinedActivityProduct" id="ErpCombinedActivityProductResult">
        <result property="id"    column="id"    />
        <result property="numCombinedActivityId"    column="num_combined_activity_id"    />
        <result property="numProductId"    column="num_product_id"    />
        <result property="vcProductName"    column="vc_product_name"    />
        <result property="numType"    column="num_type"    />
        <result property="numPrice"    column="num_price"    />
        <result property="numActivityPrice"    column="num_activity_price"    />
        <result property="numProductCount"    column="num_product_count"    />
    </resultMap>

    <sql id="selectErpCombinedActivityProductVo">
        select id, num_combined_activity_id, num_product_id, vc_product_name, num_type, num_price, num_activity_price, num_product_count from erp_combined_activity_product
    </sql>

    <select id="selectErpCombinedActivityProductList" parameterType="ErpCombinedActivityProduct" resultMap="ErpCombinedActivityProductResult">
        <include refid="selectErpCombinedActivityProductVo"/>
        <where>  
            <if test="numCombinedActivityId != null "> and num_combined_activity_id = #{numCombinedActivityId}</if>
            <if test="numProductId != null "> and num_product_id = #{numProductId}</if>
            <if test="vcProductName != null  and vcProductName != ''"> and vc_product_name like concat('%', #{vcProductName}, '%')</if>
            <if test="numType != null "> and num_type = #{numType}</if>
            <if test="numPrice != null "> and num_price = #{numPrice}</if>
            <if test="numActivityPrice != null "> and num_activity_price = #{numActivityPrice}</if>
            <if test="numProductCount != null "> and num_product_count = #{numProductCount}</if>
        </where>
    </select>
    
    <select id="selectErpCombinedActivityProductById" parameterType="Long" resultMap="ErpCombinedActivityProductResult">
        <include refid="selectErpCombinedActivityProductVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErpCombinedActivityProduct" parameterType="ErpCombinedActivityProduct" useGeneratedKeys="true" keyProperty="id">
        insert into erp_combined_activity_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId != null">num_combined_activity_id,</if>
            <if test="numProductId != null">num_product_id,</if>
            <if test="vcProductName != null">vc_product_name,</if>
            <if test="numType != null">num_type,</if>
            <if test="numPrice != null">num_price,</if>
            <if test="numActivityPrice != null">num_activity_price,</if>
            <if test="numProductCount != null">num_product_count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="numCombinedActivityId != null">#{numCombinedActivityId},</if>
            <if test="numProductId != null">#{numProductId},</if>
            <if test="vcProductName != null">#{vcProductName},</if>
            <if test="numType != null">#{numType},</if>
            <if test="numPrice != null">#{numPrice},</if>
            <if test="numActivityPrice != null">#{numActivityPrice},</if>
            <if test="numProductCount != null">#{numProductCount},</if>
         </trim>
    </insert>

    <update id="updateErpCombinedActivityProduct" parameterType="ErpCombinedActivityProduct">
        update erp_combined_activity_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="numCombinedActivityId != null">num_combined_activity_id = #{numCombinedActivityId},</if>
            <if test="numProductId != null">num_product_id = #{numProductId},</if>
            <if test="vcProductName != null">vc_product_name = #{vcProductName},</if>
            <if test="numType != null">num_type = #{numType},</if>
            <if test="numPrice != null">num_price = #{numPrice},</if>
            <if test="numActivityPrice != null">num_activity_price = #{numActivityPrice},</if>
            <if test="numProductCount != null">num_product_count = #{numProductCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErpCombinedActivityProductById" parameterType="Long">
        delete from erp_combined_activity_product where id = #{id}
    </delete>

    <delete id="deleteErpCombinedActivityProductByIds" parameterType="String">
        delete from erp_combined_activity_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>