package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 收款方式，用于尾款回款，DTO。
 *
 * <AUTHOR>
 * @since 2022/4/1 18:19
 */
@Data
public class ErpPaymentForRetainageReturnDTO {

    /**
     * 收款人。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("收款人。")
    private Long payee;

    /**
     * 收款时间。
     */
    @ApiModelProperty("收款时间。")
    private Date collectionTime;

    /**
     * 收款详情。
     */
    @ApiModelProperty("收款详情。")
    private List<ErpPaymentTermForRetainageReturnDTO> terms;

}
