package com.nnb.erp.service.MiniProgram;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Chen-xy
 * @Description: 小程序接口
 * @Date: 2024-06-11
 * @Version: 1.0
 */
public interface MiniProgramService {


    void scheduledSubscriptionMsg(String strategyId);

    void scheduledPaymentReminder();

    void scheduledSendEmailFile();
}
