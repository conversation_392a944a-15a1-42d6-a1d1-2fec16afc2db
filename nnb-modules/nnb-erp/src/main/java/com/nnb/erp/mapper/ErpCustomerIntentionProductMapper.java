package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpCustomerIntentionProduct;

/**
 * 记账客户意向产品Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface ErpCustomerIntentionProductMapper 
{
    /**
     * 查询记账客户意向产品
     * 
     * @param id 记账客户意向产品主键
     * @return 记账客户意向产品
     */
    public ErpCustomerIntentionProduct selectErpCustomerIntentionProductById(Long id);

    /**
     * 查询记账客户意向产品列表
     * 
     * @param erpCustomerIntentionProduct 记账客户意向产品
     * @return 记账客户意向产品集合
     */
    public List<ErpCustomerIntentionProduct> selectErpCustomerIntentionProductList(ErpCustomerIntentionProduct erpCustomerIntentionProduct);

    /**
     * 新增记账客户意向产品
     * 
     * @param erpCustomerIntentionProduct 记账客户意向产品
     * @return 结果
     */
    public int insertErpCustomerIntentionProduct(ErpCustomerIntentionProduct erpCustomerIntentionProduct);

    /**
     * 修改记账客户意向产品
     * 
     * @param erpCustomerIntentionProduct 记账客户意向产品
     * @return 结果
     */
    public int updateErpCustomerIntentionProduct(ErpCustomerIntentionProduct erpCustomerIntentionProduct);

    /**
     * 删除记账客户意向产品
     * 
     * @param id 记账客户意向产品主键
     * @return 结果
     */
    public int deleteErpCustomerIntentionProductById(Long id);

    /**
     * 批量删除记账客户意向产品
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpCustomerIntentionProductByIds(Long[] ids);
}
