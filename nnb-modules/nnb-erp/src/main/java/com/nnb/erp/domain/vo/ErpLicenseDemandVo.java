package com.nnb.erp.domain.vo;

import com.nnb.erp.domain.ErpLicenseDemand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ErpLicenseDemandVo extends ErpLicenseDemand {


    @ApiModelProperty("执照编号")
    private String licenseNumber;

    @ApiModelProperty("行业类型")
    private String typeName;

    @ApiModelProperty("区域")
    private String areaName;

    @ApiModelProperty("纳税类型")
    private String taxName;

    @ApiModelProperty("地址类型")
    private String addressTypeName;

    @ApiModelProperty("申请人")
    private String createdUserName;

    @ApiModelProperty("匹配状态")
    private String matchStatusName;

    @ApiModelProperty("售卖状态")
    private String saleStatusName;

    @ApiModelProperty("执照状态")
    private String licenseStatusName;

    private String associationChangeName;

    @ApiModelProperty("执照状态")
    private String haveBankName;

    @ApiModelProperty("执照状态")
    private String haveSocialSecurityName;





}
