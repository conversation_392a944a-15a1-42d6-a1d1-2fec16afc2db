package com.nnb.erp.domain.vo.service;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 s_config_service
 * 
 * <AUTHOR>
 * @date 2022-08-30
 */
@ApiModel(value="SConfigService",description="【请填写功能名称】对象")
public class SConfigServicePersionVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("人员Id")
    private Long persionId;

    @ApiModelProperty("服务Id")
    private Long serviceId;

    @ApiModelProperty("城市id")
    private Long cityId;

    @ApiModelProperty("人员名称")
    private String persionName;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("城市名称")
    private String cityName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPersionId() {
        return persionId;
    }

    public void setPersionId(Long persionId) {
        this.persionId = persionId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getPersionName() {
        return persionName;
    }

    public void setPersionName(String persionName) {
        this.persionName = persionName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("persionid", getPersionId())
                .append("serviceid", getServiceId())
                .append("persionname", getCityId())
                .toString();
    }
}
