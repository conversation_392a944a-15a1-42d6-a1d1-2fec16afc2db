package com.nnb.erp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpLicenseDemand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ErpLicenseDemandDto extends ErpLicenseDemand {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("申请时间")
    private Date createdDateBegin;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("申请时间")
    private Date createdDateEnd;

    @ApiModelProperty("执照编号")
    private String licenseNumber;

    @ApiModelProperty("执照状态")
    private Long licenseStatus;

    private List<Long> areaIdList;
    private List<Long> typeIdList;
}
