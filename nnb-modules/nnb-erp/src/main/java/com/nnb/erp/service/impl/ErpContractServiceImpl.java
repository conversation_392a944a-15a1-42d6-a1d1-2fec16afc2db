package com.nnb.erp.service.impl;

import java.util.*;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.SpringUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.erp.constant.enums.OperateEnum;
import com.nnb.erp.constant.enums.OrderApprovalStatusEnum;
import com.nnb.erp.constant.enums.OrderInvalidStatusEnum;
import com.nnb.erp.domain.ErpContractMainType;
import com.nnb.erp.domain.ErpContractRecord;
import com.nnb.erp.domain.vo.ErpContractDto;
import com.nnb.erp.domain.vo.ErpContractVo;
import com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO;
import com.nnb.erp.mapper.ErpContractMainTypeMapper;
import com.nnb.erp.mapper.ErpContractRecordMapper;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpContractMapper;
import com.nnb.erp.domain.ErpContract;
import com.nnb.erp.service.IErpContractService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-12
 */
@Service
public class ErpContractServiceImpl implements IErpContractService {
    @Autowired
    private ErpContractMapper erpContractMapper;
    @Autowired
    private ErpContractMainTypeMapper erpContractMainTypeMapper;
    @Autowired
    private ErpContractRecordMapper erpContractRecordMapper;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询合同
     *
     * @param id 合同主键
     * @return 合同
     */
    @Override
    public ErpContract selectErpContractById(Long id) {
        return erpContractMapper.selectErpContractById(id);
    }

    /**
     * 查询合同列表
     *
     * @param erpContract 合同
     * @return 合同
     */
    @Override
    public List<ErpContract> selectErpContractList(ErpContract erpContract) {
        return erpContractMapper.selectErpContractList(erpContract);
    }

    /**
     * 新增合同
     *
     * @param erpContract 合同
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertErpContract(ErpContract erpContract) {
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();


        Integer numberWithUser2 =
                erpContractMapper.selectCountErpContract(ErpContract.builder()
                        .numStatus(2L)
                        .numCreatedBy(userId)
                        .build());
        Integer numberWithUser1 =
                erpContractMapper.selectCountErpContract(ErpContract.builder()
                        .numStatus(1L)
                        .numCreatedBy(userId)
                        .build());
        Integer numberWithUser7 =
                erpContractMapper.selectCountErpContract(ErpContract.builder()
                        .numStatus(7L)
                        .numCreatedBy(userId)
                        .build());
        if (numberWithUser1 + numberWithUser2 + numberWithUser7 >= 4) {
            throw new ServiceException("待使用状态的合同已超过四份");
        }


        ErpContractMainType mainType =
                erpContractMainTypeMapper.selectErpContractMainTypeById(erpContract.getNumContractMainTypeId());
        //合同头
        StringBuilder sb = new StringBuilder(mainType.getVcContractNumberHeader());
        Calendar instance = Calendar.getInstance();
        //年-月
        Integer year = instance.get(Calendar.YEAR);
        sb.append("-").append(year);
        int month = instance.get(Calendar.MONTH) + 1;
        sb.append(month < 10 ? "0" + month : month + "");
        //合同份数
        Integer count =
                erpContractMapper.selectCountErpContract(ErpContract.builder()
                        .numContractMainTypeId(erpContract.getNumContractMainTypeId())
                        .build());
        sb.append(String.format("%04d", count));
        erpContract.setNumStatus(1L);
        erpContract.setVcContractNumber(sb.toString());
        erpContract.setNumCreatedBy(userId);
        erpContract.setDatSigningDatecreatedTime(date);
        int i = erpContractMapper.insertErpContract(erpContract);
        //合同动态记录
        erpContractRecordMapper.insertErpContractRecord(ErpContractRecord
                .builder()
                .numContractId(erpContract.getId())
                .vcContent(OperateEnum.APPLY.getName())
                .numCreatedBy(userId)
                .datSigningDatecreatedTime(date)
                .build());

        return i;
    }

    /**
     * 修改合同
     *
     * @param erpContract 合同
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateErpContract(ErpContractDto erpContract) {
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();
        erpContract.setNumUpdatedBy(userId);
        erpContract.setDatSigningDateupdatedTime(date);
        int i = erpContractMapper.updateErpContract(erpContract);
        //合同操作
        String name = OperateEnum.getNameByStatus(erpContract.getNumStatus());
        //驳回备注
        if (erpContract.getNumStatus().equals(OperateEnum.REJECT.getStatus())
                || erpContract.getNumStatus().equals(OperateEnum.EXAMINE_REJECT.getStatus())) {
            name = name + erpContract.getVcReject();
        }

        //合同动态记录
        erpContractRecordMapper.insertErpContractRecord(ErpContractRecord
                .builder()
                .numContractId(erpContract.getId())
                .vcContent(name)
                .numCreatedBy(userId)
                .datSigningDatecreatedTime(date)
                .build());
        return i;
    }

    /**
     * 批量删除合同
     *
     * @param ids 需要删除的合同主键
     * @return 结果
     */
    @Override
    public int deleteErpContractByIds(Long[] ids) {
        return erpContractMapper.deleteErpContractByIds(ids);
    }

    /**
     * 删除合同信息
     *
     * @param id 合同主键
     * @return 结果
     */
    @Override
    public int deleteErpContractById(Long id) {
        return erpContractMapper.deleteErpContractById(id);
    }

    /**
     * 查询合同列表页面
     *
     * @param erpContract 合同
     * @return
     */
    @Override
    public List<ErpContractVo> selectErpContractVoList(ErpContractDto erpContract) {
        if (StringUtils.isNotEmpty(erpContract.getDatSigningDatecreatedTimeBegin())) {
            erpContract.setDatSigningDatecreatedTimeBegin(erpContract.getDatSigningDatecreatedTimeBegin() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(erpContract.getDatSigningDatecreatedTimeEnd())) {
            erpContract.setDatSigningDatecreatedTimeEnd(erpContract.getDatSigningDatecreatedTimeEnd() + " 23:59:59");
        }
        // 获取当前的用户
        LoginUser loginUser = tokenService.getLoginUser();
        List<ErpContractVo> result;
        if (erpContract.getStatusGrant() != 2) {
            //申请,经理审核
            Set<String> permissions = loginUser.getPermissions();
            //erp:contract:number  部门经理权限
            if (permissions.contains("*:*:*") || permissions.contains(ErpProductConstants.Jurisdiction.ERP_CONTRACT_NUMBER)) {
                result = SpringUtils.getBean(IErpContractService.class).selectErpContractVoListByLeader(erpContract);
            } else {
                result = SpringUtils.getBean(IErpContractService.class).selectErpContractVoListByUser(erpContract);
            }
        } else {
            //发放-不做数据权限控制，使用菜单权限
            result = erpContractMapper.selectErpContractVoList(erpContract);
        }
        List<Long> idList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            ErpContractVo vo = result.get(i);
            if (vo.getNumStatus().intValue() == 3) {
                idList.add(vo.getId());
            }
        }
        if (idList.size() > 0) {
            List<ErpOrderInfoForOmListVO> resList = erpContractMapper.getOrderListByContractIdList(idList);
            if (ObjectUtil.isNotEmpty(resList) && resList.size() > 0) {
                for (int i = 0; i < result.size(); i++) {
                    ErpContractVo vo = result.get(i);
                    if (vo.getNumStatus().intValue() == 3) {
                        for (int j = 0; j < resList.size(); j++) {
                            ErpOrderInfoForOmListVO val = resList.get(j);
                            if (vo.getId().intValue() == val.getOrderId().intValue()) {
                                vo.setVcOrderNumber(val.getOrderNumber());
                                String nameByType = null;

                                Integer numValidStatus = val.getNumValidStatus();
                                if ((Objects.nonNull(numValidStatus) && 0 != numValidStatus)) {
                                    nameByType = OrderInvalidStatusEnum.getNameByType(numValidStatus);
                                } else {
                                    if (Objects.nonNull(val.getNumModifyOrderExamineStatus()) && (0 != val.getNumModifyOrderExamineStatus())) {
                                        nameByType = OrderApprovalStatusEnum.getNameByType(val.getNumModifyOrderExamineStatus());
                                    } else if (Objects.nonNull(val.getNumCancelOrderExamineStatus()) && (0 != val.getNumCancelOrderExamineStatus())) {
                                        nameByType = OrderApprovalStatusEnum.getNameByType(val.getNumCancelOrderExamineStatus());
                                    } else {
                                        Integer numCreateOrderExamineStatus = val.getNumCreateOrderExamineStatus();
                                        nameByType = OrderApprovalStatusEnum.getNameByType(numCreateOrderExamineStatus);
                                    }
                                }
                                vo.setOrderAuditStatusStr(nameByType);
                            }
                        }
                    }
                }
            }
        }


        return result;
    }

    @Override
    public ErpContractVo selectErpContractVoById(Long id) {
        return erpContractMapper.selectErpContractVoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int substitution(List<Long> erpContractIds) {
        List<ErpContractVo> erpContracts = erpContractMapper.selectErpContractByids(erpContractIds);
        //置换申请人
        Long numCreatedBy = erpContracts.get(0).getNumCreatedBy();
        erpContracts.get(0).setNumCreatedBy(erpContracts.get(1).getNumCreatedBy());
        erpContracts.get(1).setNumCreatedBy(numCreatedBy);
        StringBuilder name = new StringBuilder("合同置换：");
        name.append(erpContracts.get(0).getUserName()).append("合同").append(erpContracts.get(0).getVcContractNumber())
                .append("置换").append(erpContracts.get(1).getUserName()).append("合同").append(erpContracts.get(1).getVcContractNumber());
        for (int i = 0; i < erpContracts.size(); i++) {
            ErpContract contract = erpContracts.get(i);
            erpContractMapper.updateErpContract(contract);

            //合同动态记录
            erpContractRecordMapper.insertErpContractRecord(ErpContractRecord
                    .builder()
                    .numContractId(contract.getId())
                    .vcContent(name.toString())
                    .numCreatedBy(SecurityUtils.getUserId())
                    .datSigningDatecreatedTime(DateUtil.date())
                    .build());
        }
        return 1;
    }

    @Override
    public List<ErpContract> selectlistByRole(ErpContract erpContract) {
        // 获取当前的用户
        List<ErpContract> result;
        LoginUser loginUser = tokenService.getLoginUser();
        Set<String> permissions = loginUser.getPermissions();
        //erp:contract:number  部门经理权限
        if (permissions.contains("*:*:*") || permissions.contains(ErpProductConstants.Jurisdiction.ERP_CONTRACT_NUMBER)) {
            result = SpringUtils.getBean(IErpContractService.class).selectlistByLeader(erpContract);
        } else {
            result = SpringUtils.getBean(IErpContractService.class).selectlistByUser(erpContract);
        }
        return result;
    }

    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpContract> selectlistByUser(ErpContract erpContract) {
        return erpContractMapper.selectlistByRole(erpContract);
    }

    @Override
    @DataScope(deptAlias = "sd")
    public List<ErpContract> selectlistByLeader(ErpContract erpContract) {
        return erpContractMapper.selectlistByRole(erpContract);
    }

    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpContractVo> selectErpContractVoListByUser(ErpContractDto erpContract) {
        return erpContractMapper.selectErpContractVoList(erpContract);
    }

    @Override
    @DataScope(deptAlias = "sd")
    public List<ErpContractVo> selectErpContractVoListByLeader(ErpContractDto erpContract) {
        return erpContractMapper.selectErpContractVoList(erpContract);
    }

    @Override
    public int updateContractStatusById(Long contractId, Long numStatus) {
        return erpContractMapper.updateErpContract(
                ErpContract.builder()
                        .id(contractId)
                        .numStatus(numStatus)
                        .datSigningDateupdatedTime(DateUtil.date())
                        .numUpdatedBy(SecurityUtils.getUserId())
                        .build());
    }

    @Override
    public int updateUser(ErpContract erpContract) {
        DateTime date = DateUtil.date();
        erpContract.setUpdateTime(date);
        erpContractMapper.updateErpContract(erpContract);
        //合同动态记录
        erpContractRecordMapper.insertErpContractRecord(ErpContractRecord
                .builder()
                .numContractId(erpContract.getId())
                .vcContent("修改合同申请人")
                .numCreatedBy(SecurityUtils.getUserId())
                .datSigningDatecreatedTime(date)
                .build());
        return 1;
    }

}
