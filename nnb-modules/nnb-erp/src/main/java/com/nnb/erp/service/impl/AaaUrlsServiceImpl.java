package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.AaaUrlsMapper;
import com.nnb.erp.domain.AaaUrls;
import com.nnb.erp.service.IAaaUrlsService;

/**
 * 3a资质对应的请求地址Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-16
 */
@Service
public class AaaUrlsServiceImpl implements IAaaUrlsService 
{
    @Autowired
    private AaaUrlsMapper aaaUrlsMapper;

    /**
     * 查询3a资质对应的请求地址
     * 
     * @param aaaQualifieId 3a资质对应的请求地址主键
     * @return 3a资质对应的请求地址
     */
    @Override
    public AaaUrls selectAaaUrlsByAaaQualifieId(Integer aaaQualifieId)
    {
        return aaaUrlsMapper.selectAaaUrlsByAaaQualifieId(aaaQualifieId);
    }

    /**
     * 查询3a资质对应的请求地址列表
     * 
     * @param aaaUrls 3a资质对应的请求地址
     * @return 3a资质对应的请求地址
     */
    @Override
    public List<AaaUrls> selectAaaUrlsList(AaaUrls aaaUrls)
    {
        return aaaUrlsMapper.selectAaaUrlsList(aaaUrls);
    }

    /**
     * 新增3a资质对应的请求地址
     * 
     * @param aaaUrls 3a资质对应的请求地址
     * @return 结果
     */
    @Override
    public int insertAaaUrls(AaaUrls aaaUrls)
    {
        return aaaUrlsMapper.insertAaaUrls(aaaUrls);
    }

    /**
     * 修改3a资质对应的请求地址
     * 
     * @param aaaUrls 3a资质对应的请求地址
     * @return 结果
     */
    @Override
    public int updateAaaUrls(AaaUrls aaaUrls)
    {
        return aaaUrlsMapper.updateAaaUrls(aaaUrls);
    }

    /**
     * 批量删除3a资质对应的请求地址
     * 
     * @param aaaQualifieIds 需要删除的3a资质对应的请求地址主键
     * @return 结果
     */
    @Override
    public int deleteAaaUrlsByAaaQualifieIds(Integer[] aaaQualifieIds)
    {
        return aaaUrlsMapper.deleteAaaUrlsByAaaQualifieIds(aaaQualifieIds);
    }

    /**
     * 删除3a资质对应的请求地址信息
     * 
     * @param aaaQualifieId 3a资质对应的请求地址主键
     * @return 结果
     */
    @Override
    public int deleteAaaUrlsByAaaQualifieId(Integer aaaQualifieId)
    {
        return aaaUrlsMapper.deleteAaaUrlsByAaaQualifieId(aaaQualifieId);
    }
}
