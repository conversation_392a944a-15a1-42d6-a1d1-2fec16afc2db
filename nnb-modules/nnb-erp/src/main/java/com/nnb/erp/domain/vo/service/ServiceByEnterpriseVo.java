package com.nnb.erp.domain.vo.service;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 【请填写功能名称】对象 s_service_main
 * 
 * <AUTHOR>
 * @date 2022-08-31
 */
@ApiModel(value="ServiceByEnterpriseVo",description="【请填写功能名称】对象")
public class ServiceByEnterpriseVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @Excel(name = "企业Id")
    @ApiModelProperty("企业Id")
    private Long numErpEnterpriseId;

    @Excel(name = "企业名称")
    @ApiModelProperty("企业名称")
    private String vcCompanyName;

    @Excel(name = "签约时间")
    @ApiModelProperty("签约时间")
    private Date signTime;

    @Excel(name = "到达时间")
    @ApiModelProperty("到达时间")
    private Date createdTime;

    @Excel(name = "税号")
    @ApiModelProperty("税号")
    private String vcDutyParagraph;

    @Excel(name = "纳税类型")
    @ApiModelProperty("纳税类型")
    private Long numCorporatePropertyId;

    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactName;

    @Excel(name = "记账会计")
    @ApiModelProperty("记账会计")
    private String accountName;

    @Excel(name = "状态")
    @ApiModelProperty("状态")
    private Long skStatus;

    @Excel(name = "税控开始日")
    @ApiModelProperty("税控开始日")
    private Date datTaxControlCustodyTime;

    @Excel(name = "税控截止日")
    @ApiModelProperty("税控截止日")
    private Date datTaxControlCustodyTimeEnd;


}
