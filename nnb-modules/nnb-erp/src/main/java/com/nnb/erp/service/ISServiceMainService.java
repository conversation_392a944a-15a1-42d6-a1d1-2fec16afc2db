package com.nnb.erp.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dto.QualificationsExtensionDto;
import com.nnb.erp.domain.dto.SaleStatisticsDto;
import com.nnb.erp.domain.dto.service.ErpAccountPerformanceDTO;
import com.nnb.erp.domain.dto.service.SServiceRecordDto;
import com.nnb.erp.domain.dto.service.ServiceByEnterpriseDto;
import com.nnb.erp.domain.dto.service.ServiceMainDto;
import com.nnb.erp.domain.vo.QualificationsExtensionVo;
import com.nnb.erp.domain.vo.SaleStatisticsVo;
import com.nnb.erp.domain.vo.service.SServiceVo;

import javax.servlet.http.HttpServletResponse;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
public interface ISServiceMainService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SServiceMain selectSServiceMainById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sServiceMain 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SServiceVo> selectSServiceMainList(ServiceMainDto sServiceMain);

    public List<SServiceVo> getEnterpriseServiceList(ServiceMainDto sServiceMain);

    public List<SServiceVo> getAddressEnterpriseList(ServiceMainDto sServiceMain);

    public List<SServiceVo> getEnterpriseServiceListById(ServiceMainDto sServiceMain);


    public List<SServiceVo> getServiceListByEnterpriseId(ServiceByEnterpriseDto serviceByEnterpriseDto);

    public List<ServiceMainCount> getKeepAcountTypeList();

    /**
     * 新增【请填写功能名称】
     *
     * @param sServiceMain 【请填写功能名称】
     * @return 结果
     */
    public int insertSServiceMain(SServiceMain sServiceMain);

    /**
     * 修改【请填写功能名称】
     *
     * @param sServiceMain 【请填写功能名称】
     * @return 结果
     */
    public int updateSServiceMain(SServiceMain sServiceMain);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSServiceMainByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSServiceMainById(Long id);

    public int operateService(SServiceRecordDto sServiceRecord);

    public int serviceBDBack(Long id);
    public int serviceToBD(Long id);
    public int serviceAfterBack(Long id);
    public int serviceToAfter(Long id);
    public int getService(Long id);

    /**
     * 地址成本结算
     * @param costSettlementDTO
     * @return
     */
    public int addCostSettlement(CostSettlementDTO costSettlementDTO);


    /**
     * 根据type获取id集合
     */
    List<Long> getCountIdsByIdList(String ids,int type);

    public List<Long> getDeptIdList(Long deptId);

    /**
     * 根据产品时效发送服务单邮件
     */
    public void sendServiceMainMail();


    List<ErpAccountPerformanceVo> getErpAccountPerformanceVoPerson(ErpAccountPerformanceDTO erpAccountPerformanceDTO);

    List<ErpAccountPerformanceVo> getErpAccountPerformanceVoDept(ErpAccountPerformanceDTO erpAccountPerformanceDTO);


    int renewalAccountUser(Long fromUser, Long toUser, Long serviceType);

    int updateAccountBySMIds(List<Long> ids, Long toUser, Long serviceType);

    int licenseIn(Long licenseId);

    SServiceInformation getSmInfomation(Long serviceId);

    int updateSmInfomation(SServiceInformation sServiceInformation);

    int serviceTypeBack(Long id);

    void serviceProductAgeing();

    void pointSendMessage(String mainId, Long pointId);

    int historyOperateAccount(Long mainId, Long userId);

    int isBDOperateUser(Long mainId, Long userId);

    int updateProductWithZC(JSONObject obj);

    int createInternalTypeService(JSONObject obj);

    List<InternalServiceStatistics> selectInternalServiceStatisticsList(InternalServiceStatistics internalServiceStatistics);

    List<SaleStatisticsVo> getSaleStatisticsListNew(SaleStatisticsDto saleStatisticsDto);

    List<AccountLossReason> getAccountLossReason();

    int sendEmailByLiZhiAccount(Long userId);

    List<ComDictRegion> getCity(List<Long> parentIdList);

    Boolean sendEmailAccount() throws IOException;

    int yearInspect(Long numEnterpriseId);

    int dataHandover(Long numEnterpriseId, Long dataHandoverStatus, String dataHandoverDate);

    /***
     * 获取合规账企业ID
     * @return
     */
    List<Long> hgzEnterprise();


    public int updateQualificationsExtension(SServiceMain sServiceMain);

    List<QualificationsExtensionVo> qualificationsExtensionList(QualificationsExtensionDto qualificationsExtensionDto);

    public int lsQualificationsExtension(SServiceMain sServiceMain);

    public int qualificationsExtensionLossTask();
}
