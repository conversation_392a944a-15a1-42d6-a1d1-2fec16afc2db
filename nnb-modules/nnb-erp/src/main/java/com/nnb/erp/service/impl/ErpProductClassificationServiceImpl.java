package com.nnb.erp.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.erp.domain.ErpProductName;
import com.nnb.erp.domain.ErpProductType;
import com.nnb.erp.domain.vo.ErpProductClassificationDictListVo;
import com.nnb.erp.domain.vo.ErpProductServiceVo;
import com.nnb.erp.service.IErpProductNameService;
import com.nnb.erp.service.IErpProductTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpProductClassificationMapper;
import com.nnb.erp.domain.ErpProductClassification;
import com.nnb.erp.service.IErpProductClassificationService;

/**
 * 产品分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class ErpProductClassificationServiceImpl implements IErpProductClassificationService 
{
    @Autowired
    private ErpProductClassificationMapper erpProductClassificationMapper;
    @Autowired
    private IErpProductTypeService typeService;
    @Autowired
    private IErpProductNameService nameService;

    /**
     * 查询产品分类
     * 
     * @param numClassificationId 产品分类主键
     * @return 产品分类
     */
    @Override
    public ErpProductClassification selectErpProductClassificationByNumClassificationId(Long numClassificationId)
    {
        return erpProductClassificationMapper.selectErpProductClassificationByNumClassificationId(numClassificationId);
    }

    /**
     * 查询产品分类列表
     * 
     * @param erpProductClassification 产品分类
     * @return 产品分类
     */
    @Override
    public List<ErpProductClassification> selectErpProductClassificationList(ErpProductClassification erpProductClassification)
    {
        return erpProductClassificationMapper.selectErpProductClassificationList(erpProductClassification);
    }

    /**
     * 新增产品分类
     * 
     * @param erpProductClassification 产品分类
     * @return 结果
     */
    @Override
    public int insertErpProductClassification(ErpProductClassification erpProductClassification)
    {
        DateTime date = DateUtil.date();
        Long userId = SecurityUtils.getUserId();
        erpProductClassification.setNumCreateUserid(ObjectUtil.isNotNull(userId) ? userId : 1L);
        erpProductClassification.setNumLastUpdUserid(ObjectUtil.isNotNull(userId) ? userId : 1L);
        erpProductClassification.setDatCreateTime(date);
        erpProductClassification.setDatLastUpd(date);
        return erpProductClassificationMapper.insertErpProductClassification(erpProductClassification);
    }

    /**
     * 修改产品分类
     * 
     * @param erpProductClassification 产品分类
     * @return 结果
     */
    @Override
    public int updateErpProductClassification(ErpProductClassification erpProductClassification)
    {
        DateTime date = DateUtil.date();
        Long userId = SecurityUtils.getUserId();
        erpProductClassification.setNumLastUpdUserid(userId);
        erpProductClassification.setDatLastUpd(date);
        return erpProductClassificationMapper.updateErpProductClassification(erpProductClassification);
    }

    /**
     * 批量删除产品分类
     * 
     * @param numClassificationIds 需要删除的产品分类主键
     * @return 结果
     */
    @Override
    public int deleteErpProductClassificationByNumClassificationIds(Long[] numClassificationIds)
    {
        return erpProductClassificationMapper.deleteErpProductClassificationByNumClassificationIds(numClassificationIds);
    }

    /**
     * 删除产品分类信息
     * 
     * @param numClassificationId 产品分类主键
     * @return 结果
     */
    @Override
    public int deleteErpProductClassificationByNumClassificationId(Long numClassificationId)
    {
        return erpProductClassificationMapper.deleteErpProductClassificationByNumClassificationId(numClassificationId);
    }

    @Override
    public int editState(Long numClassificationId) {
        DateTime date = DateUtil.date();
        Long userId = SecurityUtils.getUserId();
        ErpProductClassification erpProductClassification = erpProductClassificationMapper.selectErpProductClassificationByNumClassificationId(numClassificationId);
        if (ObjectUtil.isNull(erpProductClassification)){
            throw new ServiceException("产品分类不存在！");
        }
        erpProductClassification.setNumIsUse(Math.abs(erpProductClassification.getNumIsUse() -1));
        erpProductClassification.setNumLastUpdUserid(userId);
        erpProductClassification.setDatLastUpd(date);
        return erpProductClassificationMapper.updateErpProductClassification(erpProductClassification);
    }

    @Override
    public List<ErpProductClassificationDictListVo> listByState() {
        ErpProductClassification dict = ErpProductClassification.builder().numIsUse(ErpProductConstants.Y).build();
        List<ErpProductClassification> erpProductClassifications = erpProductClassificationMapper.selectErpProductClassificationList(dict);
        return erpProductClassifications.stream()
                .map(item -> ErpProductClassificationDictListVo.builder()
                        .numClassificationId(item.getNumClassificationId())
                        .vcClassificationName(item.getVcClassificationName())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ErpProductServiceVo> dictRegionList() {
        List<ErpProductServiceVo> vo = new ArrayList<>();
        //一级分类
        List<ErpProductClassification> classList =
                erpProductClassificationMapper.selectErpProductClassificationList(ErpProductClassification.builder().numIsUse(ErpProductConstants.Y).build());
        if (ObjectUtil.isNotEmpty(classList)){
            classList.forEach(classification ->{
                //二级类型
                List<ErpProductType> types =
                        typeService.selectErpProductTypeList(ErpProductType.builder().numClassificationId(classification.getNumClassificationId()).build());
                List<ErpProductServiceVo> typeList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(types)){
                    types.forEach(type ->{
                        //三级名称
                        List<ErpProductName> names =
                                nameService.selectErpProductNameList(ErpProductName.builder().numTypeId(type.getNumTypeId()).build());
                        List<ErpProductServiceVo> nameList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(names)){
                            nameList = names.stream().map(name ->
                                    ErpProductServiceVo.builder()
                                            .id(name.getNumNameId())
                                            .name(name.getVcProductName())
                                            .level(ErpProductConstants.Levels.NAME)
                                            .build()).collect(Collectors.toList());
                        }
                        ErpProductServiceVo typeLink = ErpProductServiceVo.builder()
                                .id(type.getNumTypeId())
                                .name(type.getVcTypeName())
                                .level(ErpProductConstants.Levels.TYPE)
                                .childrenList(nameList).build();
                        if (ObjectUtil.isNotEmpty(typeLink.getChildrenList())){
                            typeList.add(typeLink);
                        }
                    });
                }
                ErpProductServiceVo classLink =
                        ErpProductServiceVo.builder()
                        .id(classification.getNumClassificationId())
                        .name(classification.getVcClassificationName())
                        .level(ErpProductConstants.Levels.CLASSIFICATION)
                        .childrenList(typeList)
                        .build();
                if (ObjectUtil.isNotEmpty(classLink.getChildrenList())){
                    vo.add(classLink);
                }
            });
        }
        return vo;
    }

    @Override
    public List<ErpProductServiceVo> productClassDetailList() {
        List<ErpProductServiceVo> vo = new ArrayList<>();
        //一级分类
        List<ErpProductClassification> classList =
                erpProductClassificationMapper.selectErpProductClassificationList(ErpProductClassification.builder().numIsUse(ErpProductConstants.Y).build());
        if (ObjectUtil.isNotEmpty(classList)){
            classList.forEach(classification ->{
                //二级类型
                List<ErpProductType> types =
                        typeService.selectErpProductTypeList(ErpProductType.builder().numClassificationId(classification.getNumClassificationId()).build());
                List<ErpProductServiceVo> typeList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(types)){
                    types.forEach(type ->{
                        //三级名称
                        List<ErpProductName> names =
                                nameService.selectErpProductNameList(ErpProductName.builder().numTypeId(type.getNumTypeId()).build());
                        List<ErpProductServiceVo> nameList = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(names)){
                            nameList = names.stream().map(name ->
                                    ErpProductServiceVo.builder()
                                            .id(name.getNumNameId())
                                            .name(name.getVcProductName())
                                            .level(ErpProductConstants.Levels.NAME)
                                            .build()).collect(Collectors.toList());
                        }
                        ErpProductServiceVo typeLink = ErpProductServiceVo.builder()
                                .id(type.getNumTypeId())
                                .name(type.getVcTypeName())
                                .level(ErpProductConstants.Levels.TYPE)
                                .childrenList(nameList).build();
                        typeList.add(typeLink);
                    });
                }
                ErpProductServiceVo classLink =
                        ErpProductServiceVo.builder()
                                .id(classification.getNumClassificationId())
                                .name(classification.getVcClassificationName())
                                .level(ErpProductConstants.Levels.CLASSIFICATION)
                                .childrenList(typeList)
                                .build();
                vo.add(classLink);
            });
        }
        return vo;
    }

}
