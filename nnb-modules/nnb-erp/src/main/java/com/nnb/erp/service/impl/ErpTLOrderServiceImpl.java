package com.nnb.erp.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.nnb.erp.config.TlOpenApiPropertiesConfig;
import com.nnb.erp.domain.tLOrder.*;
import com.nnb.erp.mapper.ErpTLOrderMapper;
import com.nnb.erp.service.ErpTLOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-02-07
 * @Version: 1.0
 */
@Service
@Slf4j
public class ErpTLOrderServiceImpl implements ErpTLOrderService {

    @Autowired
    private TlOpenApiPropertiesConfig tlOpenApiPropertiesConfig;

    @Override
    public ConfirmPayRecordRes syncConfirmPayRecord(ConfirmPayRecordReq confirmPayRecordReq) {

        Long time = LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        String token = tlOpenApiPropertiesConfig.getSecret() + time;

        confirmPayRecordReq.setTime(time);
        confirmPayRecordReq.setCompanyId(tlOpenApiPropertiesConfig.getCompanyId());
        confirmPayRecordReq.setToken(MD5Utils.md5Hex(token, "utf-8"));

        String url = tlOpenApiPropertiesConfig.getPrefixAddress() + tlOpenApiPropertiesConfig.getSyncConfirmPayRecordUrl();

        log.info("螳螂支付确认请求接口url：{}，请求json：{}", url, JSONUtil.toJsonStr(confirmPayRecordReq));
        String post = HttpUtil.post(url, JSONUtil.toJsonStr(confirmPayRecordReq));
        log.info("螳螂支付确认接口返回：{}", post);

        return JSONUtil.toBean(post, ConfirmPayRecordRes.class);
    }

    @Override
    public InvalidOrderRes invalidOrder(InvalidOrderReq invalidOrderReq) {

        Long time = LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        String token = tlOpenApiPropertiesConfig.getSecret() + time;

        invalidOrderReq.setTime(time);
        invalidOrderReq.setCompanyId(tlOpenApiPropertiesConfig.getCompanyId());
        invalidOrderReq.setToken(MD5Utils.md5Hex(token, "utf-8"));

        String url = tlOpenApiPropertiesConfig.getPrefixAddress() + tlOpenApiPropertiesConfig.getInvalidOrderUrl();

        log.info("螳螂订单作废接口请求接口url：{}，请求json：{}", url, JSONUtil.toJsonStr(invalidOrderReq));
        String post = HttpUtil.post(url, JSONUtil.toJsonStr(invalidOrderReq));
        log.info("螳螂订单作废接口接口返回：{}", post);

        return JSONUtil.toBean(post, InvalidOrderRes.class);
    }

    @Override
    public RefundOrderRes refundOrder(RefundOrderReq refundOrderReq) {

        Long time = LocalDateTime.now().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        String token = tlOpenApiPropertiesConfig.getSecret() + time;

        refundOrderReq.setTime(time);
        refundOrderReq.setCompanyId(tlOpenApiPropertiesConfig.getCompanyId());
        refundOrderReq.setToken(MD5Utils.md5Hex(token, "utf-8"));

        String url = tlOpenApiPropertiesConfig.getPrefixAddress() + tlOpenApiPropertiesConfig.getRefundOrderUrl();

        log.info("螳螂订单退费接口请求接口url：{}，请求json：{}", url, JSONUtil.toJsonStr(refundOrderReq));
        String post = HttpUtil.post(url, JSONUtil.toJsonStr(refundOrderReq));
        log.info("螳螂订单退费接口接口返回：{}", post);

        return JSONUtil.toBean(post, RefundOrderRes.class);
    }
}
