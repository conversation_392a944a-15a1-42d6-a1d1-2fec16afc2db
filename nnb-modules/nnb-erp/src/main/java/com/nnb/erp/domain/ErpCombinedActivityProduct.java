package com.nnb.erp.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;
import org.apache.poi.hpsf.Decimal;

/**
 * 活动产品关系对象 erp_combined_activity_product
 * 
 * <AUTHOR>
 * @date 2022-03-16
 */
@ApiModel(value="ErpCombinedActivityProduct",description="活动产品关系对象")
public class ErpCombinedActivityProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** erp_combined_activity.id */
    @Excel(name = "erp_combined_activity.id")
    @ApiModelProperty("erp_combined_activity.id")
    private Long numCombinedActivityId;

    /** 产品id */
    @Excel(name = "产品id")
    @ApiModelProperty("产品id")
    private Long numProductId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    @ApiModelProperty("产品名称")
    private String vcProductName;

    /** 类型 0-&gt;主产品； 1-&gt; 赠送产品 */
    @Excel(name = "类型 0-&gt;主产品； 1-&gt; 赠送产品")
    @ApiModelProperty("类型 0-&gt;主产品； 1-&gt; 赠送产品")
    private Integer numType;

    @ApiModelProperty("分类名称")
    private  String vcClassificationName;
    /** 产品价格 */
    @Excel(name = "产品价格")
    @ApiModelProperty("产品价格")
    private BigDecimal numPrice;

    /** 活动价格 */
    @Excel(name = "活动价格")
    @ApiModelProperty("活动价格")
    private BigDecimal numActivityPrice;

    /** 产品数量 */
    @ApiModelProperty("产品数量")
    private Long numProductCount;

    public String getVcClassificationName() {
        return vcClassificationName;
    }

    public void setVcClassificationName(String vcClassificationName) {
        this.vcClassificationName = vcClassificationName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNumCombinedActivityId(Long numCombinedActivityId) 
    {
        this.numCombinedActivityId = numCombinedActivityId;
    }

    public Long getNumCombinedActivityId() 
    {
        return numCombinedActivityId;
    }
    public void setNumProductId(Long numProductId) 
    {
        this.numProductId = numProductId;
    }

    public Long getNumProductId() 
    {
        return numProductId;
    }
    public void setVcProductName(String vcProductName) 
    {
        this.vcProductName = vcProductName;
    }

    public String getVcProductName() 
    {
        return vcProductName;
    }
    public void setNumType(Integer numType) 
    {
        this.numType = numType;
    }

    public Integer getNumType() 
    {
        return numType;
    }
    public void setNumPrice(BigDecimal numPrice) 
    {
        this.numPrice = numPrice;
    }

    public BigDecimal getNumPrice() 
    {
        return numPrice;
    }
    public void setNumActivityPrice(BigDecimal numActivityPrice) 
    {
        this.numActivityPrice = numActivityPrice;
    }

    public BigDecimal getNumActivityPrice() 
    {
        return numActivityPrice;
    }
    public void setNumProductCount(Long numProductCount) 
    {
        this.numProductCount = numProductCount;
    }

    public Long getNumProductCount() 
    {
        return numProductCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("numCombinedActivityId", getNumCombinedActivityId())
            .append("numProductId", getNumProductId())
            .append("vcProductName", getVcProductName())
            .append("numType", getNumType())
            .append("numPrice", getNumPrice())
            .append("numActivityPrice", getNumActivityPrice())
            .append("numProductCount", getNumProductCount())
                .append("vcClassificationName",getVcClassificationName())
            .toString();
    }
}
