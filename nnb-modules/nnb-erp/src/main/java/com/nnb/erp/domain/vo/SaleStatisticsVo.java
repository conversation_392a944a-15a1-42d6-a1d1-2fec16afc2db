package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SaleStatisticsVo {

    @Excel(name = "serviceOrderId")
    private Long serviceOrderId;

    @Excel(name = "产品ID")
    private Long productId;

    @Excel(name = "订单编号")
    private String orderNum;

    @Excel(name = "合同编号")
    private String contractNum;

    @Excel(name = "城市")
    private String cityName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signDate;

    @Excel(name = "签约部门")
    private String signDeptName;

    @Excel(name = "签约人")
    private String signUserName;

    @Excel(name = "收款部门")
    private String collectionDeptName;

    @Excel(name = "收款人")
    private String collectionUserName;

    @Excel(name = "企业名称")
    private String enterpriseName;

    @Excel(name = "一级分类.1")
    private String productClassificationName;

    @Excel(name = "三级分类")
    private String productName;

    @Excel(name = "产品名称")
    private String productServiceName;

    @Excel(name = "服务类型")
    private String serviceTypeName;

    @Excel(name = "购买数量")
    private Integer productCount;

    @Excel(name = "产品价格")
    private BigDecimal productPrice;

    @Excel(name = "产品总价")
    private BigDecimal productPriceTotal;

    @Excel(name = "优惠类型")
    private String couponType;

    @Excel(name = "优惠金额")
    private BigDecimal couponPrice;

    @Excel(name = "赠金金额")
    private BigDecimal givePrice;

    @Excel(name = "渠道费")
    private BigDecimal channelFee;

    @Excel(name = "实际应收")
    private BigDecimal totalPrice;

    @Excel(name = "累计收款")
    private BigDecimal payPrice;

    @Excel(name = "尾款")
    private BigDecimal lastPrice;

    @Excel(name = "跟进人")
    private String serviceUserName;

    @Excel(name = "跟进部门")
    private String serviceUserDeptName;

    @Excel(name = "标准售价")
    private BigDecimal standardPrice;

    @Excel(name = "外采成本")
    private BigDecimal outCost;

    @Excel(name = "计件成本")
    private BigDecimal numberCommission;

    @Excel(name = "实际毛利")
    private BigDecimal actualGross;

    @Excel(name = "实际毛利率")
    private String actualGrossRate;

    @Excel(name = "标准毛利")
    private BigDecimal standardGross;

    @Excel(name = "标准毛利率")
    private String standardGrossRate;


    /***
     * 应收去赠金
     */
    private BigDecimal totalPriceNoGive;

    private Integer numberCommissionType;
    private Long couponId;
    private Long followUserId;
    private List<Long> collectUserList;
    private Integer isElectronicContract;
    private Long orderId;

}
