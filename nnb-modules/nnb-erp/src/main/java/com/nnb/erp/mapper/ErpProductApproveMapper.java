package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpProductApprove;

/**
 * 产品新增修改审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ErpProductApproveMapper 
{
    /**
     * 查询产品新增修改审批
     * 
     * @param id 产品新增修改审批主键
     * @return 产品新增修改审批
     */
    public ErpProductApprove selectErpProductApproveById(Long id);

    /**
     * 查询产品新增修改审批列表
     * 
     * @param erpProductApprove 产品新增修改审批
     * @return 产品新增修改审批集合
     */
    public List<ErpProductApprove> selectErpProductApproveList(ErpProductApprove erpProductApprove);

    /**
     * 新增产品新增修改审批
     * 
     * @param erpProductApprove 产品新增修改审批
     * @return 结果
     */
    public int insertErpProductApprove(ErpProductApprove erpProductApprove);

    /**
     * 修改产品新增修改审批
     * 
     * @param erpProductApprove 产品新增修改审批
     * @return 结果
     */
    public int updateErpProductApprove(ErpProductApprove erpProductApprove);

    /**
     * 删除产品新增修改审批
     * 
     * @param id 产品新增修改审批主键
     * @return 结果
     */
    public int deleteErpProductApproveById(Long id);

    /**
     * 批量删除产品新增修改审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpProductApproveByIds(Long[] ids);
}
