package com.nnb.erp.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ErpProductNameByTypeVo implements Serializable {
    private static final long serialVersionUID = -2397594253542638317L;

    /** ID */
    private Long numNameId;

    /** 产品类型 */
    private String vcProductName;
}
