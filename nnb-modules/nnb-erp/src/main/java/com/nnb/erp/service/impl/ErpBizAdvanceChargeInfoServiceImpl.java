package com.nnb.erp.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.erp.domain.ErpContract;
import com.nnb.erp.domain.vo.bizAdvanceChargeInfo.ErpBizAdvanceChargeInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpBizAdvanceChargeInfoMapper;
import com.nnb.erp.domain.ErpBizAdvanceChargeInfo;
import com.nnb.erp.service.IErpBizAdvanceChargeInfoService;

/**
 * 预付款信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@Service
public class ErpBizAdvanceChargeInfoServiceImpl implements IErpBizAdvanceChargeInfoService 
{
    @Autowired
    private ErpBizAdvanceChargeInfoMapper erpBizAdvanceChargeInfoMapper;

    /**
     * 查询预付款信息
     * 
     * @param numId 预付款信息主键
     * @return 预付款信息
     */
    @Override
    public ErpBizAdvanceChargeInfo selectErpBizAdvanceChargeInfoByNumId(Long numId)
    {
        return erpBizAdvanceChargeInfoMapper.selectErpBizAdvanceChargeInfoByNumId(numId);
    }

    /**
     * 查询预付款信息列表
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 预付款信息
     */
    @Override
    public List<ErpBizAdvanceChargeInfoVo> selectErpBizAdvanceChargeInfoList(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo)
    {
        return erpBizAdvanceChargeInfoMapper.selectErpBizAdvanceChargeInfoListNew(erpBizAdvanceChargeInfo);
    }

    /**
     * 新增预付款信息
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 结果
     */
    @Override
    public int insertErpBizAdvanceChargeInfo(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo)
    {
        // todo  调用redis自增生成单号
        Integer count = erpBizAdvanceChargeInfoMapper.selectCountErpBizAdvanceChargeInfo(erpBizAdvanceChargeInfo);
        erpBizAdvanceChargeInfo.setVcAdvanceChargeNo(DateUtil.format(DateUtil.parse(DateUtil.today()),"yyyyMMdd")+String.format("%04d",count+1));
        erpBizAdvanceChargeInfo.setNumCreatedBy(SecurityUtils.getUserId());
        return erpBizAdvanceChargeInfoMapper.insertErpBizAdvanceChargeInfo(erpBizAdvanceChargeInfo);
    }

    /**
     * 修改预付款信息
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 结果
     */
    @Override
    public int updateErpBizAdvanceChargeInfo(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo)
    {
        erpBizAdvanceChargeInfo.setDatUpdatedTime(DateUtil.date());
        erpBizAdvanceChargeInfo.setDatUpdatedBy(SecurityUtils.getUserId());
        return erpBizAdvanceChargeInfoMapper.updateErpBizAdvanceChargeInfo(erpBizAdvanceChargeInfo);
    }

    /**
     * 批量删除预付款信息
     * 
     * @param numIds 需要删除的预付款信息主键
     * @return 结果
     */
    @Override
    public int deleteErpBizAdvanceChargeInfoByNumIds(Long[] numIds)
    {
        return erpBizAdvanceChargeInfoMapper.deleteErpBizAdvanceChargeInfoByNumIds(numIds);
    }

    /**
     * 删除预付款信息信息
     * 
     * @param numId 预付款信息主键
     * @return 结果
     */
    @Override
    public int deleteErpBizAdvanceChargeInfoByNumId(Long numId)
    {
        return erpBizAdvanceChargeInfoMapper.deleteErpBizAdvanceChargeInfoByNumId(numId);
    }
}
