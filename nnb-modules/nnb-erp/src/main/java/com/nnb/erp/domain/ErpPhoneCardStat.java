package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 手机卡管理统计对象 erp_phone_card_stat
 * 
 * <AUTHOR>
 * @date 2021-11-30
 */
public class ErpPhoneCardStat extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 手机卡配置id */
    @Excel(name = "手机卡配置id")
    private Long configId;

    /** 手机卡当前用户id */
    @Excel(name = "手机卡当前用户id")
    private String staffId;

    /** 语音使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "语音使用日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 通话消耗时长 */
    @Excel(name = "通话消耗时长")
    private Long callConsume;

    /** 当日通话次数 */
    @Excel(name = "当日通话次数")
    private Long callNumDay;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }
    public void setStaffId(String staffId) 
    {
        this.staffId = staffId;
    }

    public String getStaffId() 
    {
        return staffId;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setCallConsume(Long callConsume) 
    {
        this.callConsume = callConsume;
    }

    public Long getCallConsume() 
    {
        return callConsume;
    }
    public void setCallNumDay(Long callNumDay) 
    {
        this.callNumDay = callNumDay;
    }

    public Long getCallNumDay() 
    {
        return callNumDay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("configId", getConfigId())
            .append("staffId", getStaffId())
            .append("startTime", getStartTime())
            .append("callConsume", getCallConsume())
            .append("callNumDay", getCallNumDay())
            .toString();
    }
}
