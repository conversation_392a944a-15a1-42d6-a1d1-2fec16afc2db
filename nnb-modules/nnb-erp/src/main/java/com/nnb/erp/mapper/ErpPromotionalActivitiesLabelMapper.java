package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpPromotionalActivitiesLabel;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface ErpPromotionalActivitiesLabelMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpPromotionalActivitiesLabel selectErpPromotionalActivitiesLabelById(Integer id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpPromotionalActivitiesLabel 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpPromotionalActivitiesLabel> selectErpPromotionalActivitiesLabelList(ErpPromotionalActivitiesLabel erpPromotionalActivitiesLabel);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpPromotionalActivitiesLabel 【请填写功能名称】
     * @return 结果
     */
    public int insertErpPromotionalActivitiesLabel(ErpPromotionalActivitiesLabel erpPromotionalActivitiesLabel);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpPromotionalActivitiesLabel 【请填写功能名称】
     * @return 结果
     */
    public int updateErpPromotionalActivitiesLabel(ErpPromotionalActivitiesLabel erpPromotionalActivitiesLabel);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesLabelById(Integer id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesLabelByIds(Integer[] ids);
}
