package com.nnb.erp.controller;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.chain.intention.entity.IntentionConfigSearch;
import com.nnb.erp.domain.intention.IntentionAccount;
import com.nnb.erp.domain.intention.IntentionConfig;
import com.nnb.erp.service.IIntentionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-11-13
 * @Version: 1.0
 */
@RestController
@RequestMapping("/intention-config")
@Api(tags = "IntentionConfigController", value = "客户意向配置")
public class IntentionConfigController extends BaseController {

    private IIntentionConfigService intentionConfigService;

    @ApiOperation(value = "查询客户意向配置列表")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody IntentionConfig intentionConfig) {
        Page<Object> objects = PageHelper.startPage(
                intentionConfig.getPageNo(), intentionConfig.getPageSize(), null
        );
        List<IntentionConfig> list = intentionConfigService.selectIntentionConfigList(intentionConfig);
        return getDataTableAndTotal(list, objects.getTotal());
    }

    @ApiOperation(value = "获取客户意向配置详细信息")
    @PostMapping("/getById")
    public AjaxResult getInfo(@RequestBody IntentionConfig intentionConfig) {
        return AjaxResult.success(intentionConfigService.selectIntentionConfigById(intentionConfig.getId()));
    }

    @ApiOperation(value = "新增客户意向配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IntentionConfig intentionConfig) {
        return toAjax(intentionConfigService.insertIntentionConfig(intentionConfig));
    }

    @ApiOperation(value = "修改客户意向配置")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody IntentionConfig intentionConfig) {
        return toAjax(intentionConfigService.updateIntentionConfig(intentionConfig));
    }

    @ApiOperation(value = "删除客户意向配置")
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody IntentionConfig intentionConfig) {
        return toAjax(intentionConfigService.deleteIntentionConfigByIds(intentionConfig.getIds()));
    }

    @ApiOperation(value = "获取意向客户客户配置列表")
    @PostMapping("/getAccountListByConfig")
    public TableDataInfo getAccountListByConfig(@RequestBody IntentionConfigSearch IntentionConfigSearch) {
        Map<String, Object> resultMap = intentionConfigService.getAccountListByConfig(IntentionConfigSearch);
        List<IntentionAccount> list = MapUtil.get(
                resultMap, "list", new TypeReference<List<IntentionAccount>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(resultMap, "total"));
    }

    @ApiOperation(value = "获取意向客户配置列表详情")
    @PostMapping("/getAccountConfigDetailList")
    public TableDataInfo getAccountListByConfig(@RequestBody IntentionConfig intentionConfig) {
        Map<String, Object> resultMap = intentionConfigService.getAccountConfigDetailList(intentionConfig);
        List<IntentionAccount> list = MapUtil.get(
                resultMap, "list", new TypeReference<List<IntentionAccount>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(resultMap, "total"));
    }

    @ApiOperation(value = "获取意向客户配置列表详情")
    @GetMapping("/countNumberFollow")
    public void countNumberFollow() {
        intentionConfigService.countNumberFollow();
    }

    @Autowired
    private void setIntentionConfigService(IIntentionConfigService intentionConfigService) {
        this.intentionConfigService = intentionConfigService;
    }
}
