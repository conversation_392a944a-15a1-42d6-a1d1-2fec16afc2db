package com.nnb.erp.domain.inventory;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Author: Chen-xy
 * @Description: 收发存明细
 * @Date: 2024-01-04
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaSummaryDetailRes extends IaSummaryRes implements Serializable {

    //单据日期
    private String orderDate;
    //单据类型
    private Integer busType;
    private String busTypeStr;
    //单据id
    private Long iaStorageId;
    //单据号
    private String orderNumber;
    //往来单位id
    private Long communicationId;
    //往来单位名称
    private String communicationName;
}
