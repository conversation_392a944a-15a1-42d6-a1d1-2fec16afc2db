package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpContractMainType;
import com.nnb.erp.service.IErpContractMainTypeService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 合同主体类型Controller
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@RestController
@RequestMapping("/ErpContractMainType")
@Api(tags = "ErpContractMainTypeController", description = "合同主体类型")
public class ErpContractMainTypeController extends BaseController
{
    @Autowired
    private IErpContractMainTypeService erpContractMainTypeService;

    /**
     * 查询合同主体类型列表
     */
    @ApiOperation(value = "查询合同主体类型列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpContractMainType.class)})
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpContractMainType erpContractMainType)
    {
        startPage();
        List<ErpContractMainType> list = erpContractMainTypeService.selectErpContractMainTypeList(erpContractMainType);
        return getDataTable(list);
    }

    /**
     * 导出合同主体类型列表
     */
    @ApiOperation(value = "导出合同主体类型列表")
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:export")
    //@Log(title = "合同主体类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpContractMainType erpContractMainType) throws IOException
    {
        List<ErpContractMainType> list = erpContractMainTypeService.selectErpContractMainTypeList(erpContractMainType);
        ExcelUtil<ErpContractMainType> util = new ExcelUtil<ErpContractMainType>(ErpContractMainType.class);
        util.exportExcel(response, list, "合同主体类型数据");
    }

    /**
     * 获取合同主体类型详细信息
     */
    @ApiOperation(value = "获取合同主体类型详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpContractMainType.class)})
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="合同主体类型id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpContractMainTypeService.selectErpContractMainTypeById(id));
    }

    /**
     * 新增合同主体类型
     */
    @ApiOperation(value = "新增合同主体类型")
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:add")
    //@Log(title = "合同主体类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpContractMainType erpContractMainType)
    {
        return toAjax(erpContractMainTypeService.insertErpContractMainType(erpContractMainType));
    }

    /**
     * 修改合同主体类型
     */
    @ApiOperation(value = "修改合同主体类型")
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:edit")
    //@Log(title = "合同主体类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpContractMainType erpContractMainType)
    {
        return toAjax(erpContractMainTypeService.updateErpContractMainType(erpContractMainType));
    }

    /**
     * 删除合同主体类型
     */
    @ApiOperation(value = "删除合同主体类型")
    //@PreAuthorize(hasPermi = "erp:ErpContractMainType:remove")
    //@Log(title = "合同主体类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpContractMainTypeService.deleteErpContractMainTypeByIds(ids));
    }

    /**
     * 查询合同主体类型(下拉框)
     */
    @ApiOperation(value = "查询合同主体类型(下拉框)")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpContractMainType.class)})
    @GetMapping("/listAll")
    public AjaxResult listAll(ErpContractMainType erpContractMainType)
    {
        List<ErpContractMainType> list = erpContractMainTypeService.selectErpContractMainTypeList(erpContractMainType);
        return AjaxResult.success(list);
    }



    @ApiOperation(value = "根据合同主体查询合同主体类型(下拉框)")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpContractMainType.class)})
    @GetMapping("/getErpContractMainTypeByMainId/{mainId}")
    public AjaxResult getErpContractMainTypeByMainId(@PathVariable("mainId") Long mainId){
        return AjaxResult.success(erpContractMainTypeService.getErpContractMainTypeByMainId(mainId));
    }
}
