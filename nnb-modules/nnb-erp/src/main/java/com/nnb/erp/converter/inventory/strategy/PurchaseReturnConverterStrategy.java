package com.nnb.erp.converter.inventory.strategy;

import com.nnb.erp.constant.InventoryConstants;
import com.nnb.erp.converter.inventory.IaConverterStrategy;
import com.nnb.erp.converter.inventory.config.IaConfig;
import com.nnb.erp.domain.inventory.IaStorage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: Chen-xy
 * @Description: 采购退货
 * @Date: 2024-04-11
 * @Version: 1.0
 */
@Component("PURCHASE_RETURN")
public class PurchaseReturnConverterStrategy implements IaConverterStrategy<Boolean, IaStorage> {

    @Autowired
    private IaConfig iaConfig;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean manualInputSave(IaStorage iaStorage) {
        //获取往来单位
        iaConfig.getIaCommunication(iaStorage);
        return iaConfig.basisInsert(iaStorage, InventoryConstants.PURCHASE_RECEIPT_NUM);
    }

    @Override
    public Boolean manualImportSave(IaStorage iaStorage) {
        return null;
    }
}
