package com.nnb.erp.mapper;

import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpBizServiceAddressCost;
import com.nnb.erp.domain.ErpBizServiceAddressCostExcel;
import com.nnb.erp.domain.dto.ErpBizServiceAddressCostDto;
import com.nnb.erp.domain.vo.ErpBizServiceAddressCostVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 地址成本配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-22
 */
public interface ErpBizServiceAddressCostMapper 
{
    /**
     * 查询地址成本配置
     * 
     * @param id 地址成本配置主键
     * @return 地址成本配置
     */
    public ErpBizServiceAddressCost selectErpBizServiceAddressCostById(Long id);

    /**
     * 查询地址成本配置列表
     * 
     * @param erpBizServiceAddressCost 地址成本配置
     * @return 地址成本配置集合
     */
    public List<ErpBizServiceAddressCost> selectErpBizServiceAddressCostList(ErpBizServiceAddressCost erpBizServiceAddressCost);

    public List<ErpBizServiceAddressCostVo> selectList(ErpBizServiceAddressCostDto erpBizServiceAddressCost);
    public List<ErpBizServiceAddressCostVo> listOrderBy(ErpBizServiceAddressCostDto erpBizServiceAddressCost);
    public List<ErpBizServiceAddressCostExcel> exportList(ErpBizServiceAddressCostDto erpBizServiceAddressCost);

    /**
     * 新增地址成本配置
     * 
     * @param erpBizServiceAddressCost 地址成本配置
     * @return 结果
     */
    public int insertErpBizServiceAddressCost(ErpBizServiceAddressCost erpBizServiceAddressCost);

    /**
     * 修改地址成本配置
     * 
     * @param erpBizServiceAddressCost 地址成本配置
     * @return 结果
     */
    public int updateErpBizServiceAddressCost(ErpBizServiceAddressCost erpBizServiceAddressCost);

    /**
     * 删除地址成本配置
     * 
     * @param id 地址成本配置主键
     * @return 结果
     */
    public int deleteErpBizServiceAddressCostById(Long id);

    /**
     * 批量删除地址成本配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizServiceAddressCostByIds(Long[] ids);

    List<ErpBizServiceAddressCost> selectErpBizServiceAddressCostByIds(@Param("idList") List<Long> idList);

    List<Map<String, Object>> selectProductAreaByProductIdList(@Param("productIdList") List<Long> productIdList);

    @Select("SELECT * FROM erp_biz_service_address_cost WHERE agent_id = #{agentId} AND cost_name_id = #{productId} AND cooperate_status = 1")
    ErpBizServiceAddressCost selectAddressCostByAgentAndProduct(@Param("agentId") Long agentId, @Param("productId") Long productId);
}
