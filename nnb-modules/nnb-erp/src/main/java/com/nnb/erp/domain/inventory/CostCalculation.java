package com.nnb.erp.domain.inventory;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Chen-xy
 * @Description: 成本计算返回实体
 * @Date: 2024-01-05
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostCalculation implements Serializable {

    //类型
    private Integer busType;
    private String busTypeStr;
    //数量
    private Integer number;
    //成本金额
    private BigDecimal costAmount;
}
