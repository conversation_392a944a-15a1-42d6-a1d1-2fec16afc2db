package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 订单操作记录对象 erp_order_operating_record
 * 
 * <AUTHOR>
 * @date 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ErpOrderOperatingRecord",description="订单操作记录对象")
public class ErpOrderOperatingRecord extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /**
     * 主键标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键标识。")
    private Long id;

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private Long numOrderId;

    /**
     * 操作类型。
     */
    @ApiModelProperty("操作类型。")
    private Integer numOperationType;

    /**
     * 操作内容。
     */
    @ApiModelProperty("操作内容。")
    private String vcOperationContent;

    /**
     * 创建人。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("创建人。")
    private Long numCreatedBy;

    /**
     * 创建时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间。")
    private Date datCreatedTime;

    /**
     * 更新人。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("更新人。")
    private Long numUpdatedBy;

    /**
     * 更新时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间。")
    private Date datUpdatedTime;

    private String imageList;

}
