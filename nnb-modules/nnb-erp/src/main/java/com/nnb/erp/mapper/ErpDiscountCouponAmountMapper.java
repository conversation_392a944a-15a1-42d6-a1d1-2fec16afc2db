package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpDiscountCouponAmount;

/**
 * 优惠券额度Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-12
 */
public interface ErpDiscountCouponAmountMapper 
{
    /**
     * 查询优惠券额度
     * 
     * @param id 优惠券额度主键
     * @return 优惠券额度
     */
    public ErpDiscountCouponAmount selectErpDiscountCouponAmountById(Long id);

    /**
     * 查询优惠券额度列表
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 优惠券额度集合
     */
    public List<ErpDiscountCouponAmount> selectErpDiscountCouponAmountList(ErpDiscountCouponAmount erpDiscountCouponAmount);

    /**
     * 新增优惠券额度
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 结果
     */
    public int insertErpDiscountCouponAmount(ErpDiscountCouponAmount erpDiscountCouponAmount);

    /**
     * 修改优惠券额度
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 结果
     */
    public int updateErpDiscountCouponAmount(ErpDiscountCouponAmount erpDiscountCouponAmount);

    /**
     * 删除优惠券额度
     * 
     * @param id 优惠券额度主键
     * @return 结果
     */
    public int deleteErpDiscountCouponAmountById(Long id);

    /**
     * 批量删除优惠券额度
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpDiscountCouponAmountByIds(Long[] ids);

    public List<ErpDiscountCouponAmount> getDiscountCouponAmountList(ErpDiscountCouponAmount erpDiscountCouponAmount);
}
