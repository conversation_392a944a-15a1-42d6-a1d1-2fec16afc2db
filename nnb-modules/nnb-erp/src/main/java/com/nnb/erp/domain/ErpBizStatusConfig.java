package com.nnb.erp.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 服务单状态配置对象 erp_biz_status_config
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@ApiModel(value="ErpBizStatusConfig",description="服务单状态配置对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErpBizStatusConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long numId;

    /** 菜单节点id */
    @Excel(name = "菜单节点id")
    @ApiModelProperty("菜单节点id")
    private Long numMenuId;

    /** 菜单节点名称 */
    @Excel(name = "菜单节点名称")
    @ApiModelProperty("菜单节点名称")
    private String vcMenuName;

    /** 流程节点id */
    @Excel(name = "流程节点id")
    @ApiModelProperty("流程节点id")
    private Long numFlowId;

    /** 状态id */
    @Excel(name = "状态id")
    @ApiModelProperty("状态id")
    private Long numStatusId;

    /** 状态名称 */
    @Excel(name = "状态名称")
    @ApiModelProperty("状态名称")
    private String vcStatusName;

    /** 是否初始状态 */
    @Excel(name = "是否初始状态")
    @ApiModelProperty("是否初始状态")
    private Long numIsBegin;

    /** 是否可选状态:0否，1是 */
    @Excel(name = "是否可选状态:0否，1是")
    @ApiModelProperty("是否可选状态:0否，1是")
    private Long numIsChoice;

    /** 状态类型: 1普通，2跳BD，3跳售后，4BD转回，5售后转回，6转增值服务单,7服务单完成状态，8节点完成状态（用于跳转下个节点）,9注册服务-出执照转增项 */
    @Excel(name = "状态类型: 1普通，2跳BD，3跳售后，4BD转回，5售后转回，6转增值服务单,7服务单完成状态，8节点完成状态（用于跳转下个节点）,9注册服务-出执照转增项")
    @ApiModelProperty("状态类型: 1普通，2跳BD，3跳售后，4BD转回，5售后转回，6转增值服务单,7服务单完成状态，8节点完成状态（用于跳转下个节点）,9注册服务-出执照转增项")
    private Long numStatusType;

    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setNumMenuId(Long numMenuId) 
    {
        this.numMenuId = numMenuId;
    }

    public Long getNumMenuId() 
    {
        return numMenuId;
    }
    public void setNumFlowId(Long numFlowId) 
    {
        this.numFlowId = numFlowId;
    }

    public Long getNumFlowId() 
    {
        return numFlowId;
    }
    public void setNumStatusId(Long numStatusId) 
    {
        this.numStatusId = numStatusId;
    }

    public Long getNumStatusId() 
    {
        return numStatusId;
    }
    public void setVcStatusName(String vcStatusName) 
    {
        this.vcStatusName = vcStatusName;
    }

    public String getVcStatusName() 
    {
        return vcStatusName;
    }
    public void setNumIsBegin(Long numIsBegin) 
    {
        this.numIsBegin = numIsBegin;
    }

    public Long getNumIsBegin() 
    {
        return numIsBegin;
    }
    public void setNumIsChoice(Long numIsChoice) 
    {
        this.numIsChoice = numIsChoice;
    }

    public Long getNumIsChoice() 
    {
        return numIsChoice;
    }

    public String getVcMenuName() {
        return vcMenuName;
    }

    public void setVcMenuName(String vcMenuName) {
        this.vcMenuName = vcMenuName;
    }

    public Long getNumStatusType() {
        return numStatusType;
    }

    public void setNumStatusType(Long numStatusType) {
        this.numStatusType = numStatusType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("numMenuId", getNumMenuId())
            .append("vcMenuName", getVcMenuName())
            .append("numFlowId", getNumFlowId())
            .append("numStatusId", getNumStatusId())
            .append("vcStatusName", getVcStatusName())
            .append("numIsBegin", getNumIsBegin())
            .append("numIsChoice", getNumIsChoice())
            .append("numStatusType", getNumStatusType())
            .toString();
    }
}
