package com.nnb.erp.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.map.MapUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.nacos.common.utils.StringUtils;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.erp.domain.vo.ErpProductConfigurationVo;
import com.nnb.erp.util.MybatisBatchUtils;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpProductConfigurationMapper;
import com.nnb.erp.domain.ErpProductConfiguration;
import com.nnb.erp.service.IErpProductConfigurationService;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 产品库配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class ErpProductConfigurationServiceImpl implements IErpProductConfigurationService
{
    @Resource
    private MybatisBatchUtils mybatisBatchUtils;

    @Autowired
    private ErpProductConfigurationMapper erpProductConfigurationMapper;

    /**
     * 查询产品库配置信息
     *
     * @param id 产品库配置信息主键
     * @return 产品库配置信息
     */
    @Override
    public ErpProductConfiguration selectErpProductConfigurationById(Long id)
    {
        return erpProductConfigurationMapper.selectErpProductConfigurationById(id);
    }

    @Override
    public List<ErpProductConfigurationVo> getProductConfigurationById(Long id) {
        ErpProductConfiguration erpProductConfigurationBy = new ErpProductConfiguration();
        erpProductConfigurationBy.setProductId(id);
        List<ErpProductConfiguration> erpProductConfigurations = erpProductConfigurationMapper.selectErpProductConfigurationList(erpProductConfigurationBy);
        BigDecimal outCost = erpProductConfigurationMapper.selectOutCostByProductId(id);
        List<ErpProductConfigurationVo> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(erpProductConfigurations)) {
            for (ErpProductConfiguration erpProductConfiguration : erpProductConfigurations) {
                ErpProductConfigurationVo erpProductConfigurationVo = new ErpProductConfigurationVo();
                BeanUtils.copyProperties(erpProductConfiguration, erpProductConfigurationVo);
                list.add(erpProductConfigurationVo);
                String deptId = erpProductConfiguration.getDeptId();
                if (StringUtils.isNotEmpty(deptId)) {
                    String[] split = deptId.split(",");
                    List<String> strings = Arrays.asList(split);
                    erpProductConfigurationVo.setDeptIds(strings);
                }
                BigDecimal allCost = new BigDecimal("0");
                allCost = outCost.add(
                                erpProductConfiguration.getNumberCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getNumberCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getNumberCommission())).divide(new BigDecimal("100")))
                        .add(
                                erpProductConfiguration.getAccountCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getAccountCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getAccountCommission())).divide(new BigDecimal("100")))
                        .add(
                                erpProductConfiguration.getLeadCustomCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getLeadCustomCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getLeadCustomCommission())).divide(new BigDecimal("100")))
                        .add(
                                erpProductConfiguration.getSaleCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getSaleCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getSaleCommission())).divide(new BigDecimal("100")))
                        .add(
                                erpProductConfiguration.getIncrementCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getIncrementCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getIncrementCommission())).divide(new BigDecimal("100")))
                        .add(
                                erpProductConfiguration.getAddressCommissionType() == 1 ? new BigDecimal(erpProductConfiguration.getAddressCommission()) :
                                        erpProductConfiguration.getProductPrice().multiply(new BigDecimal(erpProductConfiguration.getAddressCommission())).divide(new BigDecimal("100")));


                erpProductConfigurationVo.setAllCost(allCost);
            }
        }

        return list;
    }

    @Override
    public void importProductConfigExcel(MultipartFile file) throws IOException {
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
        List<Map<String, Object>> maps = reader.readAll();

        // 根据产品id和价格去重
        List<Map<String, Object>> deduplicatedMaps = new ArrayList<>(maps.stream()
                .collect(Collectors.toMap(
                        // 使用产品id和价格作为key进行去重
                        map -> {
                            Object productId = map.get("产品ID"); // 根据实际Excel列名调整
                            Object price = map.get("价格"); // 根据实际Excel列名调整
                            return productId + "_" + price; // 组合key
                        },
                        // value就是map本身
                        map -> map,
                        // 如果有重复key，保留第一个
                        (existing, replacement) -> existing
                ))
                .values());
        // 查询所有配置
        List<ErpProductConfiguration> list = erpProductConfigurationMapper.selectErpProductConfigurationList(
                new ErpProductConfiguration()
        );
        // deduplicatedMaps 匹配 更新
        list.forEach(config -> {
            deduplicatedMaps.stream().filter(m ->
                    MapUtil.getLong(m, "产品ID").equals(config.getProductId())
                            && MapUtil.get(m, "价格", BigDecimal.class).compareTo(config.getProductPrice()) == 0
            ).findAny().ifPresent(
                    m -> config.setGuidePrice(MapUtil.get(m, "指导价格", BigDecimal.class))
            );
        });
        //更新指导价格
        List<ErpProductConfiguration> updateList = list.stream().map(en -> {
            ErpProductConfiguration config = new ErpProductConfiguration();
            config.setId(en.getId());
            config.setGuidePrice(en.getGuidePrice());
            return config;
        }).collect(Collectors.toList());

        mybatisBatchUtils.batchUpdateOrInsert(updateList, ErpProductConfigurationMapper.class,
                (en, mapper) -> mapper.updateGuidePrice(en)
        );
    }

    /**
     * 查询产品库配置信息列表
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 产品库配置信息
     */
    @Override
    public List<ErpProductConfiguration> selectErpProductConfigurationList(ErpProductConfiguration erpProductConfiguration)
    {
        return erpProductConfigurationMapper.selectErpProductConfigurationList(erpProductConfiguration);
    }

    /**
     * 新增产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    @Override
    public int insertErpProductConfiguration(ErpProductConfiguration erpProductConfiguration)
    {
        erpProductConfiguration.setCreateTime(DateUtils.getNowDate());
        return erpProductConfigurationMapper.insertErpProductConfiguration(erpProductConfiguration);
    }

    /**
     * 修改产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    @Override
    public int updateErpProductConfiguration(ErpProductConfiguration erpProductConfiguration)
    {
        erpProductConfiguration.setUpdateTime(DateUtils.getNowDate());
        return erpProductConfigurationMapper.updateErpProductConfiguration(erpProductConfiguration);
    }

    /**
     * 批量删除产品库配置信息
     *
     * @param ids 需要删除的产品库配置信息主键
     * @return 结果
     */
    @Override
    public int deleteErpProductConfigurationByIds(Long[] ids)
    {
        return erpProductConfigurationMapper.deleteErpProductConfigurationByIds(ids);
    }

    /**
     * 删除产品库配置信息信息
     *
     * @param id 产品库配置信息主键
     * @return 结果
     */
    @Override
    public int deleteErpProductConfigurationById(Long id)
    {
        return erpProductConfigurationMapper.deleteErpProductConfigurationById(id);
    }
}
