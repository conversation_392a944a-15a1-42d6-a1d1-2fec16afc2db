package com.nnb.erp.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.erp.domain.ErpProductDetail;
import com.nnb.erp.domain.dto.ErpBizServiceAddressCostNameDto;
import com.nnb.erp.domain.vo.ErpBizServiceAddressCostNameVo;
import com.nnb.erp.mapper.ErpProductDetailMapper;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpBizServiceRegisterAreasMapper;
import com.nnb.erp.domain.ErpBizServiceRegisterAreas;
import com.nnb.erp.service.IErpBizServiceRegisterAreasService;

/**
 * 注册区域Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-06
 */
@Service
public class ErpBizServiceRegisterAreasServiceImpl implements IErpBizServiceRegisterAreasService 
{
    @Autowired
    private ErpBizServiceRegisterAreasMapper erpBizServiceRegisterAreasMapper;
    @Autowired
    private ErpProductDetailMapper erpProductDetailMapper;

    /**
     * 查询注册区域
     * 
     * @param id 注册区域主键
     * @return 注册区域
     */
    @Override
    public ErpBizServiceRegisterAreas selectErpBizServiceRegisterAreasById(Integer id)
    {
        return erpBizServiceRegisterAreasMapper.selectErpBizServiceRegisterAreasById(id);
    }

    /**
     * 查询注册区域列表
     * 
     * @param erpBizServiceRegisterAreas 注册区域
     * @return 注册区域
     */
    @Override
    public List<ErpBizServiceRegisterAreas> selectErpBizServiceRegisterAreasList(ErpBizServiceRegisterAreas erpBizServiceRegisterAreas)
    {
        return erpBizServiceRegisterAreasMapper.selectErpBizServiceRegisterAreasList(erpBizServiceRegisterAreas);
    }

    /**
     * 新增注册区域
     * 
     * @param erpBizServiceRegisterAreas 注册区域
     * @return 结果
     */
    @Override
    public int insertErpBizServiceRegisterAreas(ErpBizServiceRegisterAreas erpBizServiceRegisterAreas)
    {
        ErpBizServiceRegisterAreas registerAreas = new ErpBizServiceRegisterAreas();
        registerAreas.setName(erpBizServiceRegisterAreas.getName());
        List<ErpBizServiceRegisterAreas> manageList = erpBizServiceRegisterAreasMapper.selectErpBizServiceRegisterAreasList(registerAreas);
        for (int i = 0; i < manageList.size(); i++) {
            if (manageList.get(i).getName() == erpBizServiceRegisterAreas.getName()) {
                throw new ServiceException("该类型已存在");
            }
        }
        return erpBizServiceRegisterAreasMapper.insertErpBizServiceRegisterAreas(erpBizServiceRegisterAreas);
    }

    /**
     * 修改注册区域
     * 
     * @param erpBizServiceRegisterAreas 注册区域
     * @return 结果
     */
    @Override
    public int updateErpBizServiceRegisterAreas(ErpBizServiceRegisterAreas erpBizServiceRegisterAreas)
    {
        ErpBizServiceRegisterAreas registerAreas = new ErpBizServiceRegisterAreas();
        registerAreas.setName(erpBizServiceRegisterAreas.getName());
        List<ErpBizServiceRegisterAreas> manageList = erpBizServiceRegisterAreasMapper.selectErpBizServiceRegisterAreasList(registerAreas);
        for (int i = 0; i < manageList.size(); i++) {
            if (manageList.get(i).getName() == erpBizServiceRegisterAreas.getName()
                    && manageList.get(i).getId() != erpBizServiceRegisterAreas.getId()) {
                throw new ServiceException("该类型已存在");
            }
        }
//
        return erpBizServiceRegisterAreasMapper.updateErpBizServiceRegisterAreas(erpBizServiceRegisterAreas);
    }

    /**
     * 批量删除注册区域
     * 
     * @param ids 需要删除的注册区域主键
     * @return 结果
     */
    @Override
    public int deleteErpBizServiceRegisterAreasByIds(Integer[] ids)
    {
        return erpBizServiceRegisterAreasMapper.deleteErpBizServiceRegisterAreasByIds(ids);
    }

    /**
     * 删除注册区域信息
     * 
     * @param id 注册区域主键
     * @return 结果
     */
    @Override
    public int deleteErpBizServiceRegisterAreasById(Integer id)
    {
        return erpBizServiceRegisterAreasMapper.deleteErpBizServiceRegisterAreasById(id);
    }

    @Override
    public List<ErpBizServiceRegisterAreas> getAllByProductId(Long productId) {
        ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(productId);
        if (ObjectUtil.isNull(erpProductDetail)) {
            throw new ServiceException("产品错误");
        }
        if (ObjectUtil.isEmpty(erpProductDetail.getCostDetailed()) || erpProductDetail.getCostDetailed().equals("[]") || erpProductDetail.getCostDetailed().equals("null")) {
            throw new ServiceException("产品未配置成本明细");
        }
        JSONArray arr = JSONArray.fromObject(erpProductDetail.getCostDetailed());
        List<Long> list = new ArrayList<>();
        for (int i = 0; i < arr.size(); i++) {
            list.add(Long.parseLong(arr.getString(i)));
        }
        return erpBizServiceRegisterAreasMapper.selectErpBizServiceRegisterAreasByCostTypeIds(list);
    }
}
