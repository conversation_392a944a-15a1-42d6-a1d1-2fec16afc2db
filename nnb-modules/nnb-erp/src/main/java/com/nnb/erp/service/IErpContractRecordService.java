package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpContractRecord;
import com.nnb.erp.domain.vo.ErpContractRecordVo;

/**
 * 合同记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
public interface IErpContractRecordService 
{
    /**
     * 查询合同记录
     * 
     * @param id 合同记录主键
     * @return 合同记录
     */
    public ErpContractRecord selectErpContractRecordById(Long id);

    /**
     * 查询合同记录列表
     * 
     * @param erpContractRecord 合同记录
     * @return 合同记录集合
     */
    public List<ErpContractRecord> selectErpContractRecordList(ErpContractRecord erpContractRecord);

    /**
     * 新增合同记录
     * 
     * @param erpContractRecord 合同记录
     * @return 结果
     */
    public int insertErpContractRecord(ErpContractRecord erpContractRecord);

    /**
     * 修改合同记录
     * 
     * @param erpContractRecord 合同记录
     * @return 结果
     */
    public int updateErpContractRecord(ErpContractRecord erpContractRecord);

    /**
     * 批量删除合同记录
     * 
     * @param ids 需要删除的合同记录主键集合
     * @return 结果
     */
    public int deleteErpContractRecordByIds(Long[] ids);

    /**
     * 删除合同记录信息
     * 
     * @param id 合同记录主键
     * @return 结果
     */
    public int deleteErpContractRecordById(Long id);

    /**
     * 查询合同记录列表
     *
     * @param erpContractRecord 合同记录
     * @return 合同记录集合
     */
    public List<ErpContractRecordVo> selectErpContractRecordVoList(ErpContractRecord erpContractRecord);
}
