package com.nnb.erp.service;

import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpIncomeDetails;
import com.nnb.erp.domain.dto.ErpIncomeDetailsDto;
import com.nnb.erp.domain.vo.ErpIncomeDetailsVo;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
public interface IErpIncomeDetailsService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpIncomeDetails selectErpIncomeDetailsById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpIncomeDetails 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpIncomeDetailsVo> listWithOrderCreatedExamine(ErpIncomeDetailsDto erpIncomeDetails);

    public List<ErpIncomeDetailsVo> balanceListWithOrderId(ErpIncomeDetailsDto erpIncomeDetails);

    public int deleteBalanceWithOrder(ErpIncomeDetailsDto erpIncomeDetails);
    /**
     * 新增【请填写功能名称】
     * 
     * @param erpIncomeDetails 【请填写功能名称】
     * @return 结果
     */
    public int insertErpIncomeDetails(ErpIncomeDetails erpIncomeDetails);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpIncomeDetails 【请填写功能名称】
     * @return 结果
     */
    public int updateErpIncomeDetails(ErpIncomeDetails erpIncomeDetails);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteErpIncomeDetailsByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpIncomeDetailsById(Long id);

    /***
     * 根据银联回调插入收入明细表
     * @param map
     */
    public int addIncomeDetailsByChinaUmsCallBack(Map<String, Object> map);

    void examineOrderPass(Map<String, Object> map);
}
