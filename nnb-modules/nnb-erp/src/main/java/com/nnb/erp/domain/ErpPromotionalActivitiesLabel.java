package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_promotional_activities_label
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
@ApiModel(value="ErpPromotionalActivitiesLabel",description="【请填写功能名称】对象")
public class ErpPromotionalActivitiesLabel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Integer id;

    /** 标签名称 */
    @Excel(name = "标签名称")
    @ApiModelProperty("标签名称")
    private String label;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createdUser;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建人", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date createdTime;

    /** $column.columnComment */
    @Excel(name = "创建人")
    @ApiModelProperty("$column.columnComment")
    private Long updateUser;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setLabel(String label) 
    {
        this.label = label;
    }

    public String getLabel() 
    {
        return label;
    }
    public void setCreatedUser(Long createdUser)
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser()
    {
        return createdUser;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdateUser(Long updateUser)
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser()
    {
        return updateUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("label", getLabel())
            .append("createdUser", getCreatedUser())
            .append("createdTime", getCreatedTime())
            .append("updateUser", getUpdateUser())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
