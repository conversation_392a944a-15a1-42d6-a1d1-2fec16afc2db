package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 优惠券和产品分类关系对象 erp_coupon_product_type
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@ApiModel(value="ErpCouponProductType",description="优惠券和产品分类关系对象")
public class ErpCouponProductType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    @ApiModelProperty("优惠券id")
    private Long numCouponId;

    /** 产品分类id */
    @Excel(name = "产品分类id")
    @ApiModelProperty("产品分类id")
    private Long numProductTypeId;

    /** 产品类型名称（看如果产品分类改名，是否要看历史的，防止名字变化） */
    @Excel(name = "产品类型名称", readConverterExp = "看=如果产品分类改名，是否要看历史的，防止名字变化")
    @ApiModelProperty("产品类型名称（看如果产品分类改名，是否要看历史的，防止名字变化）")
    private String vcProductTypeName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNumCouponId(Long numCouponId) 
    {
        this.numCouponId = numCouponId;
    }

    public Long getNumCouponId() 
    {
        return numCouponId;
    }
    public void setNumProductTypeId(Long numProductTypeId) 
    {
        this.numProductTypeId = numProductTypeId;
    }

    public Long getNumProductTypeId() 
    {
        return numProductTypeId;
    }
    public void setVcProductTypeName(String vcProductTypeName) 
    {
        this.vcProductTypeName = vcProductTypeName;
    }

    public String getVcProductTypeName() 
    {
        return vcProductTypeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("numCouponId", getNumCouponId())
            .append("numProductTypeId", getNumProductTypeId())
            .append("vcProductTypeName", getVcProductTypeName())
            .toString();
    }
}
