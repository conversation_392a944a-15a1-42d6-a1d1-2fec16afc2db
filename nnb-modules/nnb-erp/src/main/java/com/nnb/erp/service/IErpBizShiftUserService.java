package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizShiftUser;
import com.nnb.erp.domain.vo.ErpBizShiftUserDto;
import com.nnb.erp.domain.vo.ErpBizShiftUserVo;

/**
 * 服务订单流转配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface IErpBizShiftUserService 
{
    /**
     * 查询服务订单流转配置
     * 
     * @param numId 服务订单流转配置主键
     * @return 服务订单流转配置
     */
    public ErpBizShiftUser selectErpBizShiftUserByNumId(Long numId);

    /**
     * 查询服务订单流转配置列表
     * 
     * @param erpBizShiftUser 服务订单流转配置
     * @return 服务订单流转配置集合
     */
    public List<ErpBizShiftUserVo> selectErpBizShiftUserList(ErpBizShiftUser erpBizShiftUser);

    /**
     * 新增服务订单流转配置
     * 
     * @param erpBizShiftUser 服务订单流转配置
     * @return 结果
     */
    public int insertErpBizShiftUser(ErpBizShiftUserDto erpBizShiftUser);

    /**
     * 修改服务订单流转配置
     * 
     * @param erpBizShiftUser 服务订单流转配置
     * @return 结果
     */
    public int updateErpBizShiftUser(ErpBizShiftUserDto erpBizShiftUser);

    /**
     * 批量删除服务订单流转配置
     * 
     * @param numIds 需要删除的服务订单流转配置主键集合
     * @return 结果
     */
    public int deleteErpBizShiftUserByNumIds(Long[] numIds);

    /**
     * 删除服务订单流转配置信息
     * 
     * @param numId 服务订单流转配置主键
     * @return 结果
     */
    public int deleteErpBizShiftUserByNumId(Long numId);
}
