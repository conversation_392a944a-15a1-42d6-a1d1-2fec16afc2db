package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_examine_other_order_pay_association
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@ApiModel(value="ErpExamineOtherOrderPayAssociation",description="【请填写功能名称】对象")
public class ErpExamineOtherOrderPayAssociation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** erp_examine_other_order_pay.id */
    @Excel(name = "erp_examine_other_order_pay.id")
    @ApiModelProperty("erp_examine_other_order_pay.id")
    private Long otherOrderPayId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String orderNum;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOtherOrderPayId(Long otherOrderPayId) 
    {
        this.otherOrderPayId = otherOrderPayId;
    }

    public Long getOtherOrderPayId() 
    {
        return otherOrderPayId;
    }
    public void setOrderNum(String orderNum) 
    {
        this.orderNum = orderNum;
    }

    public String getOrderNum() 
    {
        return orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("otherOrderPayId", getOtherOrderPayId())
            .append("orderNum", getOrderNum())
            .toString();
    }
}
