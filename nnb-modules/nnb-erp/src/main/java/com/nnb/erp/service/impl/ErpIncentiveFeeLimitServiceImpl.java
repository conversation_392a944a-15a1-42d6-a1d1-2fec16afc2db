package com.nnb.erp.service.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.erp.domain.dto.ErpIncentiveFeeLimitDto;
import com.nnb.erp.domain.vo.ErpIncentiveFeeLimitVo;
import com.nnb.system.api.domain.SysDept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpIncentiveFeeLimitMapper;
import com.nnb.erp.domain.ErpIncentiveFeeLimit;
import com.nnb.erp.service.IErpIncentiveFeeLimitService;

/**
 * 销售激励费Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-11
 */
@Service
public class ErpIncentiveFeeLimitServiceImpl implements IErpIncentiveFeeLimitService 
{
    @Autowired
    private ErpIncentiveFeeLimitMapper erpIncentiveFeeLimitMapper;

    @Value("${erp.incentiveFee.limitDept}")
    private String erpIncentiveFeeLimitDept;

    /**
     * 查询销售激励费
     * 
     * @param id 销售激励费主键
     * @return 销售激励费
     */
    @Override
    public ErpIncentiveFeeLimit selectErpIncentiveFeeLimitById(Long id)
    {
        return erpIncentiveFeeLimitMapper.selectErpIncentiveFeeLimitById(id);
    }

    /**
     * 查询销售激励费列表
     * 
     * @param erpIncentiveFeeLimit 销售激励费
     * @return 销售激励费
     */
    @Override
    public List<ErpIncentiveFeeLimitVo> selectErpIncentiveFeeLimitList(ErpIncentiveFeeLimit erpIncentiveFeeLimit)
    {
        List<ErpIncentiveFeeLimitVo> voList = getIncentiveFeeLimitDept();

        List<ErpIncentiveFeeLimit> list = new ArrayList<>();
        for (int i = 0; i < voList.size(); i++) {
            Long deptId = voList.get(i).getDeptId();
            erpIncentiveFeeLimit.setDeptId(deptId);
            list = erpIncentiveFeeLimitMapper.selectErpIncentiveFeeLimitList(erpIncentiveFeeLimit);

            if (ObjectUtil.isEmpty(list) || list.size() == 0) {
                Long year = erpIncentiveFeeLimit.getYear();
                for (int j = 1; j < 13; j++) {
                    ErpIncentiveFeeLimit limit = new ErpIncentiveFeeLimit();
                    limit.setFee(new BigDecimal("0"));
                    limit.setYear(year);
                    limit.setMonth(Long.parseLong(j+""));
                    limit.setDeptId(voList.get(i).getDeptId());
                    erpIncentiveFeeLimitMapper.insertErpIncentiveFeeLimit(limit);
                }

            }
        }


        erpIncentiveFeeLimit.setDeptId(null);
        list = erpIncentiveFeeLimitMapper.selectErpIncentiveFeeLimitList(erpIncentiveFeeLimit);
        for (int i = 0; i < voList.size(); i++) {
            ErpIncentiveFeeLimitVo vo = voList.get(i);

            for (int j = 0; j < list.size(); j++) {
                ErpIncentiveFeeLimit limit = list.get(j);
                limit.setFeeUpdate(0L);
                if (limit.getDeptId().intValue() == vo.getDeptId().intValue()) {
                    switch (limit.getMonth().intValue()) {
                        case 1:
                            vo.setMonth1(limit);
                            break;
                        case 2:
                            vo.setMonth2(limit);
                            break;
                        case 3:
                            vo.setMonth3(limit);
                            break;
                        case 4:
                            vo.setMonth4(limit);
                            break;
                        case 5:
                            vo.setMonth5(limit);
                            break;
                        case 6:
                            vo.setMonth6(limit);
                            break;
                        case 7:
                            vo.setMonth7(limit);
                            break;
                        case 8:
                            vo.setMonth8(limit);
                            break;
                        case 9:
                            vo.setMonth9(limit);
                            break;
                        case 10:
                            vo.setMonth10(limit);
                            break;
                        case 11:
                            vo.setMonth11(limit);
                            break;
                        case 12:
                            vo.setMonth12(limit);
                            break;
                    }
                }
            }
        }
        return voList;
    }

    /**
     * 新增销售激励费
     * 
     * @param erpIncentiveFeeLimit 销售激励费
     * @return 结果
     */
    @Override
    public int insertErpIncentiveFeeLimit(ErpIncentiveFeeLimit erpIncentiveFeeLimit)
    {
        return erpIncentiveFeeLimitMapper.insertErpIncentiveFeeLimit(erpIncentiveFeeLimit);
    }

    /**
     * 修改销售激励费
     * 
     * @param ErpIncentiveFeeLimitDto 销售激励费
     * @return 结果
     */
    @Override
    public List<ErpIncentiveFeeLimitVo> updateErpIncentiveFeeLimit(ErpIncentiveFeeLimitDto dto) throws Exception
    {
//        return erpIncentiveFeeLimitMapper.updateErpIncentiveFeeLimit(erpIncentiveFeeLimit);
        List<ErpIncentiveFeeLimit> updateList = new ArrayList<>();
        List<ErpIncentiveFeeLimitVo> voList = dto.getVoList();
        if (ObjectUtil.isEmpty(voList) || voList.size() == 0) {
            throw new ServiceException("数据错误");
        }
        boolean haveErr = false;
        for (int i = 0; i < voList.size(); i++) {
            ErpIncentiveFeeLimitVo vo = voList.get(i);

            for (Field field : ErpIncentiveFeeLimitVo.class.getDeclaredFields()) {
                if (field.getName().startsWith("month")) {
                    Object monthObj = field.get(vo);
                    if (ObjectUtil.isEmpty(monthObj)) {
                        continue;
                    }
                    ErpIncentiveFeeLimit month = JSONObject.parseObject(JSONObject.toJSONString(monthObj), ErpIncentiveFeeLimit.class);
                    if (month.getFeeUpdate().intValue() == 1) {
                        BigDecimal limitFee = erpIncentiveFeeLimitMapper.getFeeApprove(month.getDeptId(), month.getYear().toString() + (Integer.parseInt(month.getMonth().toString()) < 10 ? "-0" : "-") + month.getMonth().toString());
                        if (month.getFee().compareTo(limitFee) < 0) {
                            month.setMsg(vo.getDeptName()+month.getMonth()+"月激励金额最少"+limitFee+"元");
                            haveErr = true;
                        }
                        updateList.add(month);
                    }
                    field.set(vo, month);
                }
            }
        }
        if (!haveErr) {
            for (int i = 0; i < updateList.size(); i++) {
                erpIncentiveFeeLimitMapper.updateErpIncentiveFeeLimit(updateList.get(i));
            }
            return new ArrayList<>();
        }
        return voList;
    }

    /**
     * 批量删除销售激励费
     * 
     * @param ids 需要删除的销售激励费主键
     * @return 结果
     */
    @Override
    public int deleteErpIncentiveFeeLimitByIds(Long[] ids)
    {
        return erpIncentiveFeeLimitMapper.deleteErpIncentiveFeeLimitByIds(ids);
    }

    /**
     * 删除销售激励费信息
     * 
     * @param id 销售激励费主键
     * @return 结果
     */
    @Override
    public int deleteErpIncentiveFeeLimitById(Long id)
    {
        return erpIncentiveFeeLimitMapper.deleteErpIncentiveFeeLimitById(id);
    }

    @Override
    public List<ErpIncentiveFeeLimitVo> getIncentiveFeeLimitDept() {
        List<ErpIncentiveFeeLimitVo> list = new ArrayList<>();
        List<Long> deptIdList = Arrays.stream(erpIncentiveFeeLimitDept.split(",")).map(Long::valueOf).collect(Collectors.toList());
        for (int i = 0; i < deptIdList.size(); i++) {
            SysDept sysDept = erpIncentiveFeeLimitMapper.selectSysDeptById(deptIdList.get(i));
            List<Long> ancestors = Arrays.stream(sysDept.getAncestors().split(",")).map(Long::valueOf).collect(Collectors.toList());

            SysDept sysDeptFirst = erpIncentiveFeeLimitMapper.selectSysDeptById(ancestors.get(1));

            ErpIncentiveFeeLimitVo vo = new ErpIncentiveFeeLimitVo();
            vo.setDeptName(sysDept.getDeptName());
            vo.setFirstDeptName(sysDeptFirst.getDeptName());
            vo.setCityName(sysDept.getCityId().intValue() == 64 ? "北京" : "上海");
            vo.setDeptId(sysDept.getDeptId());
            list.add(vo);
        }
        return list;
    }

    @Override
    public JSONObject getHasApproveFee(ErpIncentiveFeeLimit erpIncentiveFeeLimit) {
        JSONObject returnObj = new JSONObject();
        BigDecimal limitFee = new BigDecimal("0");
        BigDecimal approveFee = new BigDecimal("0");
        BigDecimal hasFee = new BigDecimal("0");
        List<ErpIncentiveFeeLimit> list = erpIncentiveFeeLimitMapper.selectErpIncentiveFeeLimitList(erpIncentiveFeeLimit);
        if (ObjectUtil.isEmpty(list) || list.size() != 1) {
            throw new ServiceException("未配置销售激励费额度");
        }

        String yearMonth = Integer.parseInt(erpIncentiveFeeLimit.getMonth().toString()) < 10 ?
                erpIncentiveFeeLimit.getYear().toString()+"-0"+ erpIncentiveFeeLimit.getMonth().toString() :
                erpIncentiveFeeLimit.getYear().toString()+"-"+ erpIncentiveFeeLimit.getMonth().toString();

        approveFee = erpIncentiveFeeLimitMapper.getFeeApprove(erpIncentiveFeeLimit.getDeptId(),  yearMonth);

        limitFee = list.get(0).getFee();
        hasFee = list.get(0).getFee().subtract(approveFee);
        returnObj.put("limitFee", limitFee);
        returnObj.put("approveFee", approveFee);
        returnObj.put("hasFee", hasFee);
        return returnObj;
    }
}
