package com.nnb.erp.service.impl.gift;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.dto.gift.ErpGiftIssueRecordDTO;
import com.nnb.erp.domain.gift.ErpGift;
import com.nnb.erp.domain.gift.ErpGiftIssueRecord;
import com.nnb.erp.domain.gift.ErpGiftRule;
import com.nnb.erp.domain.gift.ErpGiftRuleLabel;
import com.nnb.erp.domain.vo.gift.ErpGiftIssueRecordVO;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.mapper.gift.ErpGiftRuleLabelMapper;
import com.nnb.erp.mapper.gift.ErpGiftRuleMapper;
import com.nnb.erp.service.gift.IErpGiftIssueRecordService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 赠品发放记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
@Service
public class ErpGiftIssueRecordServiceImpl implements IErpGiftIssueRecordService
{
    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;

    @Autowired
    private ErpGiftRuleLabelMapper erpGiftRuleLabelMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Value("${erp.gift.pool.admin.roleId}")
    private Long giftPoolAdminRoleId;

    @Autowired
    private ErpGiftRuleMapper erpGiftRuleMapper;

    /**
     * 查询赠品发放记录
     * 
     * @param id 赠品发放记录主键
     * @return 赠品发放记录
     */
    @Override
    public ErpGiftIssueRecord selectErpGiftIssueRecordById(Long id)
    {
        return erpGiftIssueRecordMapper.selectErpGiftIssueRecordById(id);
    }

    /**
     * 查询赠品发放记录列表
     * 
     * @param erpGiftIssueRecord 赠品发放记录
     * @return 赠品发放记录
     */
    @Override
    public List<ErpGiftIssueRecord> selectErpGiftIssueRecordList(ErpGiftIssueRecord erpGiftIssueRecord)
    {
        return erpGiftIssueRecordMapper.selectErpGiftIssueRecordList(erpGiftIssueRecord);
    }

    /**
     * 新增赠品发放记录
     * 
     * @param erpGiftIssueRecord 赠品发放记录
     * @return 结果
     */
    @Override
    public int insertErpGiftIssueRecord(ErpGiftIssueRecord erpGiftIssueRecord)
    {
        erpGiftIssueRecord.setCreateTime(DateUtils.getNowDate());
        return erpGiftIssueRecordMapper.insertErpGiftIssueRecord(erpGiftIssueRecord);
    }

    /**
     * 修改赠品发放记录
     * 
     * @param erpGiftIssueRecord 赠品发放记录
     * @return 结果
     */
    @Override
    public int updateErpGiftIssueRecord(ErpGiftIssueRecord erpGiftIssueRecord)
    {
        erpGiftIssueRecord.setUpdateTime(DateUtils.getNowDate());
        return erpGiftIssueRecordMapper.updateErpGiftIssueRecord(erpGiftIssueRecord);
    }

    /**
     * 批量删除赠品发放记录
     * 
     * @param ids 需要删除的赠品发放记录主键
     * @return 结果
     */
    @Override
    public int deleteErpGiftIssueRecordByIds(Long[] ids)
    {
        return erpGiftIssueRecordMapper.deleteErpGiftIssueRecordByIds(ids);
    }

    /**
     * 删除赠品发放记录信息
     * 
     * @param id 赠品发放记录主键
     * @return 结果
     */
    @Override
    public int deleteErpGiftIssueRecordById(Long id)
    {
        return erpGiftIssueRecordMapper.deleteErpGiftIssueRecordById(id);
    }

    @Override
    public List<ErpGiftIssueRecordVO> getIssueRecordList(ErpGiftIssueRecordDTO erpGiftIssueRecordDTO) {
        List<ErpGiftIssueRecordVO> issueRecordList = erpGiftIssueRecordMapper.getIssueRecordList(erpGiftIssueRecordDTO);
        issueRecordList.forEach(item -> {
            String labelId = item.getLabelId();
            if (StringUtils.isNotEmpty(labelId)) {
                StringBuffer stringBufferLabelName = new StringBuffer();
                String[] split = labelId.split(",");
                for (String s : split) {
                    ErpGiftRuleLabel erpGiftRuleLabel = erpGiftRuleLabelMapper.selectErpGiftRuleLabelById(Long.valueOf(s));
                    stringBufferLabelName.append(Objects.nonNull(erpGiftRuleLabel) ? erpGiftRuleLabel.getLabelName() + " " : null);
                }
                item.setLabelName(String.valueOf(stringBufferLabelName));
            }
            Long deptId = item.getDeptId();
            String labelIdStr = item.getLabelId();
            StringBuffer minimumSpendingAmountSb = new StringBuffer();
            StringBuffer productIdSb = new StringBuffer();
            if (StringUtils.isNotEmpty(labelIdStr)) {
                for (String labelStr : labelIdStr.split(",")) {
                    List<ErpGiftRule> erpGiftRuleList = erpGiftIssueRecordMapper.getErpGiftRuleList(Long.valueOf(labelStr), deptId);
                    if (CollectionUtils.isNotEmpty(erpGiftRuleList)) {
                        for (ErpGiftRule erpGiftRule : erpGiftRuleList) {
                            minimumSpendingAmountSb.append(erpGiftRule.getMinimumSpendingAmount() + ",");
                            if (3 == erpGiftRule.getApplicableProductType() || 2 == erpGiftRule.getApplicableProductType()) {
                                productIdSb.append(erpGiftRule.getProductId() + ",");
                            } else if (1 == erpGiftRule.getApplicableProductType()) {
                                productIdSb.append("全部通用,");
                            }
                        }
                    }
                }
            }

            item.setMinimumSpendingAmount(String.valueOf(minimumSpendingAmountSb));
            item.setApplyProductId(String.valueOf(productIdSb));
        });
        return issueRecordList;
    }

    @Override
    public List<ErpGiftIssueRecordVO> getIssueGiftPool(ErpGiftIssueRecordDTO erpGiftIssueRecordDTO) {
        LoginUser loginUser = tokenService.getLoginUser();
        List<String> roleIdList = loginUser.getRoles().stream().map(item -> item).collect(Collectors.toList());
        erpGiftIssueRecordDTO.setIsGiftPoolAdmin(0);
        if (CollectionUtils.isNotEmpty(roleIdList) && roleIdList.contains(giftPoolAdminRoleId) && 1L == loginUser.getUserid()) {
            if (Objects.nonNull(loginUser)) {
                erpGiftIssueRecordDTO.setIsGiftPoolAdmin(1);
                String ancestors = loginUser.getSysUser().getDept().getAncestors();
                Long deptId = loginUser.getSysUser().getDeptId();
                if (StringUtils.isNotEmpty(ancestors)) {
                    String[] split = ancestors.split(",");
                    int length = split.length;
                    if (length <= 2) {
                        //获取部门及其子部门
                        R<List<SysDept>> childrenByPid = remoteUserService.getDeptChildrenByPid(deptId, SecurityConstants.INNER);
                        if (200 == childrenByPid.getCode() && CollectionUtils.isNotEmpty(childrenByPid.getData())) {
                            List<Long> deptIdList = childrenByPid.getData().stream().map(SysDept::getDeptId).collect(Collectors.toList());
                            erpGiftIssueRecordDTO.setSubDeptIdList(deptIdList);
                        }
                    } else if (length == 3) {
                        erpGiftIssueRecordDTO.setSubDeptIdList(Arrays.asList(deptId));
                    } else if (length > 3) {
                        R<List<SysDept>> childrenByPid = remoteUserService.getDeptChildrenByPid(deptId, SecurityConstants.INNER);
                        if (200 == childrenByPid.getCode() && CollectionUtils.isNotEmpty(childrenByPid.getData())) {
                            Long thirdDeptId = childrenByPid.getData().get(4).getDeptId();
                            erpGiftIssueRecordDTO.setSubDeptIdList(Arrays.asList(thirdDeptId));
                        }
                    }
                }
            }
        }
        List<ErpGiftIssueRecordVO> issueGiftPool = erpGiftIssueRecordMapper.getIssueGiftPool(erpGiftIssueRecordDTO);
        issueGiftPool.forEach(item -> {
            StringBuffer noticeSb = new StringBuffer();
            StringBuffer labelSb = new StringBuffer();
            StringBuffer productSb = new StringBuffer();
            String labelId = item.getLabelId();
            Long deptId = item.getDeptId();
            if (StringUtils.isNotEmpty(labelId)) {
                String[] labelIdList = labelId.split(",");
                for (int j = 0; j < labelIdList.length; j ++) {
                    String label = labelIdList[j];

                    ErpGiftRuleLabel erpGiftRuleLabel = erpGiftRuleLabelMapper.selectErpGiftRuleLabelById(Long.valueOf(label));
                    List<ErpGiftRule> erpGiftByLabelAndDeptId = erpGiftRuleMapper.getErpGiftByLabelAndDeptId(Long.valueOf(label), deptId);
                    for (int i = 0; i < erpGiftByLabelAndDeptId.size(); i++) {
                        ErpGiftRule erpGiftRule = erpGiftByLabelAndDeptId.get(i);
                        if (erpGiftRule.getApplicableProductType().intValue() == 1) {
                            productSb.append(" 规则"+(j+1)+"：通用产品/");
                        }
                        if (erpGiftRule.getApplicableProductType().intValue() == 2) {
                            productSb.append(" 规则"+(j+1)+"：指定产品："+erpGiftRule.getProductId()+"/");
                        }
                        if (erpGiftRule.getApplicableProductType().intValue() == 3) {
                            productSb.append(" 规则"+(j+1)+"：组合产品："+erpGiftRule.getProductId()+"/");
                        }
                        noticeSb.append(" 规则"+(j+1)+"："+erpGiftRule.getNoticeForUse() + "/");
                    }
                    labelSb.append(Objects.nonNull(erpGiftRuleLabel) ? erpGiftRuleLabel.getLabelName() + "/" : null);
                }
            }
            item.setNoticeForUse(String.valueOf(noticeSb));
            item.setLabelName(String.valueOf(labelSb));
            item.setApplyProductId(String.valueOf(productSb));
        });
        return issueGiftPool;
    }



}
