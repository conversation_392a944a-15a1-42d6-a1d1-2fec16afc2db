package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.OrderInfo;

/**
 * 订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
public interface IOrderInfoService 
{
    /**
     * 查询订单信息
     * 
     * @param numOrderId 订单信息主键
     * @return 订单信息
     */
    public OrderInfo selectOrderInfoByNumOrderId(Long numOrderId);

    /**
     * 查询订单信息列表
     * 
     * @param orderInfo 订单信息
     * @return 订单信息集合
     */
    public List<OrderInfo> selectOrderInfoList(OrderInfo orderInfo);

    /**
     * 新增订单信息
     * 
     * @param orderInfo 订单信息
     * @return 结果
     */
    public int insertOrderInfo(OrderInfo orderInfo);

    /**
     * 修改订单信息
     * 
     * @param orderInfo 订单信息
     * @return 结果
     */
    public int updateOrderInfo(OrderInfo orderInfo);

    /**
     * 批量删除订单信息
     * 
     * @param numOrderIds 需要删除的订单信息主键集合
     * @return 结果
     */
    public int deleteOrderInfoByNumOrderIds(Long[] numOrderIds);

    /**
     * 删除订单信息信息
     * 
     * @param numOrderId 订单信息主键
     * @return 结果
     */
    public int deleteOrderInfoByNumOrderId(Long numOrderId);
}
