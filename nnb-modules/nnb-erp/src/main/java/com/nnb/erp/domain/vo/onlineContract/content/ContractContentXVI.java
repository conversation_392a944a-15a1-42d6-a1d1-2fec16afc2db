package com.nnb.erp.domain.vo.onlineContract.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/7/13 13:25
 */
@Data
@ApiModel(value = "ContractContentXVI", description = "上海企苗服务协议")
public class ContractContentXVI {

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 负责人姓名
     */
    private String principalName;

    /**
     * 甲方联系电话
     */
    private String firstContactPhone;

    /**
     * 甲方住址
     */
    private String firstAddress;

    /**
     * 乙方
     */
    private String secondParty;
    /**
     * 销售姓名
     */
    private String sellerName;
    /**
     * 乙方联系电话
     */
    private String secondtContactPhone;
    /**
     * 乙方住址
     */
    private String secondAddress;

    /**
     * 应收大写
     */
    private String sumServiceChageCapital;
    /**
     * 应收小写
     */
    private BigDecimal sumServiceChageLowerCase;
    /**
     * 实收大写
     */
    private String payerMoneyCapital;
    /**
     * 实收小写
     */
    private BigDecimal payerMoneyLowerCase;

    /**
     * 尾款大写
     */
    private String balancePaymentCapital;
    /**
     * 尾款小写
     */
    private BigDecimal balancePaymentLowerCase;
    /**
     * 甲方签章
     */
    private String firstPartySignature;
    /**
     * 甲方授权代表
     */
    private String firstPartyRepresentative;
    /**
     * 甲方签订日期
     */
    private Date fristPartyDate;
    /**
     * 乙方签章
     */
    private String secondPartySignature;
    /**
     * 乙方授权代表
     */
    private String secondPartyRepresentative;
    /**
     * 乙方签订日期
     */
    private Date secondPartyDate;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 合同其它详情信息
     */
    private Object contractDetailObject;








}
