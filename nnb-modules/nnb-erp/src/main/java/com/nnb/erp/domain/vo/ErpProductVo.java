package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品管理对象 erp_product_detail
 *
 * <AUTHOR>
 * @date 2021-12-27
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpProductVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Excel(name = "ID")
    private Long numProductId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String vcProductName;


}
