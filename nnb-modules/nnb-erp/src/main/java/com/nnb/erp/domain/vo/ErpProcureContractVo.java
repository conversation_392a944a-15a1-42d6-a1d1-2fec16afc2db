package com.nnb.erp.domain.vo;

import com.nnb.erp.domain.ErpProcureContract;
import com.nnb.erp.domain.vo.approval.ErpExamineApprovalFlowVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpProcureContractVo extends ErpProcureContract {

    private String createdUserName;
    private String createdUserDept;
    private String contractSourceName;
    private String statusName;
    private String contractTypeName;
    private String agentNumber;
    private String fileUserName;
    private String cancelUserName;

    private List<ErpExamineApprovalFlowVO> followInfo;
    private List<ErpExamineApprovalFlowVO> recordInfo;
}
