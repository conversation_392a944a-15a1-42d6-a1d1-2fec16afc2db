package com.nnb.erp.domain.vo.onlineContract.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/7/13 13:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractContentV", description = "注销代理（注销服务）")
public class ContractContentV extends contractBaseEntity{

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 代表人姓名
     */
    private String firstPrincipalName;

    /**
     * 甲方联系电话
     */
    private String firstContactPhone;

    /**
     * 乙方
     */
    private String secondParty;
    /**
     * 乙方代表人姓名
     */
    private String secondPrincipalName;
    /**
     * 乙方联系电话
     */
    private String secondtContactPhone;

    /**
     * 注销金额大写
     */
    private String cancelAmountCapital;

    /**
     * 注销金额小写
     */
    private BigDecimal cancelAmountLowerCase;


    /**
     * 合同其它详情信息
     */
    private Object contractDetailObject;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
}
