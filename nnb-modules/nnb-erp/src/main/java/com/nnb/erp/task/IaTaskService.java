package com.nnb.erp.task;

import com.nnb.erp.service.inventory.IaOccurAmountService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: Chen-xy
 * @Description: 存货核算定时任务
 * @Date: 2025-06-09
 * @Version: 1.0
 */
@Slf4j
@Component
public class IaTaskService {

    @Resource
    private IaOccurAmountService iaOccurAmountService;

    /**
     * 定时统计库存数据
     * @return Boolean
     */
    @XxlJob("inventory-stock")
    public Boolean inventoryStockTask(){
        return iaOccurAmountService.inventoryStockTask();
    }

    /**
     * 定时修正库存数量
     * @return Boolean
     */
    @XxlJob("fix-inventory-qty")
    public Boolean fixInventoryQty(){
        return iaOccurAmountService.fixInventoryQty();
    }
}
