package com.nnb.erp.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import com.nnb.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpOrderRefundMapper;
import com.nnb.erp.domain.ErpOrderRefund;
import com.nnb.erp.service.IErpOrderRefundService;

/**
 * 订单退款Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Service
public class ErpOrderRefundServiceImpl implements IErpOrderRefundService
{
    @Autowired
    private ErpOrderRefundMapper erpOrderRefundMapper;

    /**
     * 查询订单退款
     *
     * @param id 订单退款主键
     * @return 订单退款
     */
    @Override
    public ErpOrderRefund selectErpOrderRefundById(Long id)
    {
        return erpOrderRefundMapper.selectErpOrderRefundById(id);
    }

    /**
     * 查询订单退款列表
     *
     * @param erpOrderRefund 订单退款
     * @return 订单退款
     */
    @Override
    public List<ErpOrderRefund> selectErpOrderRefundList(ErpOrderRefund erpOrderRefund)
    {
        return erpOrderRefundMapper.selectErpOrderRefundList(erpOrderRefund);
    }

    /**
     * 新增订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    @Override
    public int insertErpOrderRefund(ErpOrderRefund erpOrderRefund)
    {
        erpOrderRefund.setCreateTime(LocalDateTime.now());
        return erpOrderRefundMapper.insertErpOrderRefund(erpOrderRefund);
    }

    /**
     * 修改订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    @Override
    public int updateErpOrderRefund(ErpOrderRefund erpOrderRefund)
    {
        return erpOrderRefundMapper.updateErpOrderRefund(erpOrderRefund);
    }

    /**
     * 批量删除订单退款
     *
     * @param ids 需要删除的订单退款主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderRefundByIds(List<Long> ids)
    {
        return erpOrderRefundMapper.deleteErpOrderRefundByIds(ids);
    }

    /**
     * 删除订单退款信息
     *
     * @param id 订单退款主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderRefundById(Long id)
    {
        return erpOrderRefundMapper.deleteErpOrderRefundById(id);
    }
}
