package com.nnb.erp.domain;

import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

import java.io.Serializable;

/**
 * 小程序活动/优惠券配置与优惠额度关联对象 xcx_activity_coupon_limit
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@ApiModel(value = "XcxActivityCouponLimit", description = "小程序活动/优惠券配置与优惠额度关联对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class XcxActivityCouponLimit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /**
     * 优惠券额度id
     */
    @Excel(name = "优惠券额度id")
    @ApiModelProperty("优惠券额度id")
    private Long couponLimitId;

    /**
     * 活动名称
     */
    @Excel(name = "活动名称")
    @ApiModelProperty("活动名称")
    private String activityName;
}
