package com.nnb.erp.service.impl.dingding;

import cn.hutool.json.JSONUtil;
import com.common.api.model.DingMessageDTO;
import com.common.api.service.DingService;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.erp.domain.Approvals;
import com.nnb.erp.domain.dingding.DingPersonDTO;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.service.DingDingService;
import com.nnb.erp.util.HttpClientUtil;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DingDingServiceImpl implements DingDingService {

    @Value("${erp.dingding.url}")
    private String dingUrl;

    @Value("${crm.url}")
    private String crmUrl;

    @Resource
    private DingService dingService;

    @Resource
    private RemoteUserService remoteUserService;

    @Override
    public void sendDingMessage(DingSendDTO dingSendDTO) {
        //net.sf.json.JSONObject jsonObjectReq = new net.sf.json.JSONObject();
        //jsonObjectReq.put("userid_list", dingSendDTO.getUseridList());
        //jsonObjectReq.put("title", dingSendDTO.getTitle());
        //jsonObjectReq.put("content", dingSendDTO.getContent());
        //log.info("erp发送钉钉消息入参为{}", jsonObjectReq);
        //return HttpClientUtil.doPostJson(dingUrl + "openApi/dingTalk/sendMsg", jsonObjectReq);

        DingMessageDTO dingMessageDTO = new DingMessageDTO(
                dingSendDTO.getUseridList(), dingSendDTO.getTitle(), dingSendDTO.getContent()
        );
        log.info("erp发送钉钉消息入参为{}", JSONUtil.toJsonStr(dingMessageDTO));
        dingService.sendDingMessageToUser(dingMessageDTO);
    }

    @Override
    public void sendDingApprovalMessage(List<Approvals> approvalsList, Integer typeAll) {
        try {
            List<Approvals> collect = approvalsList.stream().filter(item -> (Objects.nonNull(item.getSort()) && 1 == item.getSort())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                Approvals approvals = collect.get(0);
                R<SysUser> info = remoteUserService.getUserInfoById(approvals.getUserId(), SecurityConstants.INNER);
                if (200 == info.getCode()) {
                    SysUser sysUser = info.getData();
                    String approvalName = null;
                    switch (typeAll) {
                        case 2:
                            approvalName = "成本审批";
                            break;
                        case 3:
                            approvalName = "预付款审批";
                            break;
                        case 4:
                            approvalName = "产品审批";
                            break;
                    }
                    //发送钉钉消息
                    String dingContent = "### 审批通知 \n * " + info.getData().getNickName() + "，您好： \n "
                            + " * " + "您有一条" + approvalName + "，ID为：" + approvals.getId() + "，请及时审批。 \n"
                            + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/Approve/ApprovalList/waitMe?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
                    DingSendDTO dingSendDTO = new DingSendDTO(sysUser.getDingUserId(), "审批通知", dingContent);
                    sendDingMessage(dingSendDTO);
                }
            }
        } catch (Exception e) {
            log.error("钉钉发送审批链接通知异常，异常信息为", e);
        }
    }

    @Override
    public void sendDingNextApprovalMessage(Approvals approvals, Integer typeAll) {
        R<SysUser> info = remoteUserService.getUserInfoById(approvals.getUserId(), SecurityConstants.INNER);
        if (200 == info.getCode()) {
            SysUser sysUser = info.getData();
            String approvalName = null;
            switch (typeAll) {
                case 2:
                    approvalName = "成本审批";
                    break;
                case 3:
                    approvalName = "预付款审批";
                    break;
                case 4:
                    approvalName = "产品审批";
                    break;
            }
            //发送钉钉消息
            String dingContent = "### 审批通知 \n * " + info.getData().getNickName() + "，您好： \n "
                    + " * " + "您有一条" + approvalName + "，ID为：" + approvals.getId() + "，请及时审批。 \n"
                    + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/Approve/ApprovalList/waitMe?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
            DingSendDTO dingSendDTO = new DingSendDTO(sysUser.getDingUserId(), "审批通知", dingContent);
            sendDingMessage(dingSendDTO);
        }
    }

    @Override
    public String dingPersonMessage(DingPersonDTO dingPersonDTO) {
        net.sf.json.JSONObject jsonObjectReq = new net.sf.json.JSONObject();
        jsonObjectReq.put("userid_list", dingPersonDTO.getUseridList());
        jsonObjectReq.put("title", dingPersonDTO.getTitle());
        jsonObjectReq.put("content", dingPersonDTO.getContent());

        log.info("erp发送钉一下消息入参为{}", jsonObjectReq);

        String result = HttpClientUtil.doPostJson(dingUrl + "openApi/dingTalk/sendDing", jsonObjectReq);

        log.info("erp钉一下消息结果为{}", result);

        return result;
    }
}
