package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 任务操作记录，实体。
 *
 * <AUTHOR>
 * @since 2022-07-06 09:43:34
 */
@Data
public class TaskOperationEntity {

    /**
     * 主键标识。
     */
    @ApiModelProperty("主键标识。")
    private Integer id;

    @ApiModelProperty("是否执照任务。")
    private Integer isLicense;

    @ApiModelProperty("任务状态：1-待审核；2-待审核搁置；3-待分配；4-待分配搁置；5-待完结；6-完结已完成；7-完结未完成；8-撤销；9-驳回；10-完结重新发起。")
    private Integer status;
    /**
     * 任务标识。
     */
    @ApiModelProperty("任务标识。")
    private Integer taskId;

    /**
     * 操作类型。
     */
    @ApiModelProperty("操作类型。")
    private Integer type;

    /**
     * 标题。
     */
    @ApiModelProperty("标题。")
    private String title;

    /**
     * 备注。
     */
    @ApiModelProperty("备注。")
    private String remark;

    /**
     * 操作人。
     */
    @ApiModelProperty("操作人。")
    private Integer operationBy;
    @ApiModelProperty("操作人。")
    private String userName;

    @ApiModelProperty("原因。")
    private String reason;
    /**
     * 操作时间。
     */
    @ApiModelProperty("操作时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

}
