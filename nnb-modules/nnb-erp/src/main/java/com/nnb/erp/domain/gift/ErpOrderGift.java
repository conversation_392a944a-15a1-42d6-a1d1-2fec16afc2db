package com.nnb.erp.domain.gift;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 订单赠品对象 erp_order_gift
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel(value="ErpOrderGift",description="订单赠品对象")
public class ErpOrderGift extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 订单ID */
    @Excel(name = "订单ID")
    @ApiModelProperty("订单ID")
    private Long orderId;

    /** 赠品发放记录ID */
    @Excel(name = "赠品发放记录ID")
    @ApiModelProperty("赠品发放记录ID")
    private Long giftIssueRecordId;

    /** 赠品状态, 1:已提单， 2 :待发货，3：已发货，4.已取消 */
    @Excel(name = "赠品状态, 1:已提单， 2 :待发货，3：已发货，4.已取消")
    @ApiModelProperty("赠品状态, 1:已提单， 2 :待发货，3：已发货，4.已取消")
    private Integer giftStatus;

    /** 收货人 */
    @Excel(name = "收货人")
    @ApiModelProperty("收货人")
    private String consignee;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String phone;

    /** 收货地址 */
    @Excel(name = "收货地址")
    @ApiModelProperty("收货地址")
    private String address;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setGiftIssueRecordId(Long giftIssueRecordId) 
    {
        this.giftIssueRecordId = giftIssueRecordId;
    }

    public Long getGiftIssueRecordId() 
    {
        return giftIssueRecordId;
    }
    public void setGiftStatus(Integer giftStatus) 
    {
        this.giftStatus = giftStatus;
    }

    public Integer getGiftStatus() 
    {
        return giftStatus;
    }
    public void setConsignee(String consignee) 
    {
        this.consignee = consignee;
    }

    public String getConsignee() 
    {
        return consignee;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("giftIssueRecordId", getGiftIssueRecordId())
            .append("giftStatus", getGiftStatus())
            .append("consignee", getConsignee())
            .append("phone", getPhone())
            .append("address", getAddress())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
