package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.ErpBizServiceRegisterAreas;
import com.nnb.erp.domain.dto.ErpBizServiceAddressCostNameDto;
import com.nnb.erp.domain.vo.ErpBizServiceAddressCostNameVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpBizServiceAddressCostName;
import com.nnb.erp.service.IErpBizServiceAddressCostNameService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 地址成本配置Controller
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
@RestController
@RequestMapping("/ErpBizServiceAddressCostName")
@Api(tags = "ErpBizServiceAddressCostNameController", description = "地址成本配置")
public class ErpBizServiceAddressCostNameController extends BaseController
{
    @Autowired
    private IErpBizServiceAddressCostNameService erpBizServiceAddressCostNameService;

    /**
     * 查询地址成本配置列表
     */
    @ApiOperation(value = "查询地址成本配置列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpBizServiceAddressCostName.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpBizServiceAddressCostNameDto erpBizServiceAddressCostName)
    {
        erpBizServiceAddressCostName.setNewData(1);
        startPage();
        List<ErpBizServiceAddressCostNameVo> list = erpBizServiceAddressCostNameService.selectErpBizServiceAddressCostNameList(erpBizServiceAddressCostName);
        return getDataTable(list);
    }

//    /**
//     * 导出地址成本配置列表
//     */
//    @ApiOperation(value = "导出地址成本配置列表")
//    @PreAuthorize(hasPermi = "erp:name:export")
//    //@Log(title = "地址成本配置", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ErpBizServiceAddressCostName erpBizServiceAddressCostName) throws IOException
//    {
//        List<ErpBizServiceAddressCostName> list = erpBizServiceAddressCostNameService.selectErpBizServiceAddressCostNameList(erpBizServiceAddressCostName);
//        ExcelUtil<ErpBizServiceAddressCostName> util = new ExcelUtil<ErpBizServiceAddressCostName>(ErpBizServiceAddressCostName.class);
//        util.exportExcel(response, list, "地址成本配置数据");
//    }

    /**
     * 获取地址成本配置详细信息
     */
    @ApiOperation(value = "获取地址成本配置详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpBizServiceAddressCostName.class)})
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="地址成本配置id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpBizServiceAddressCostNameService.selectErpBizServiceAddressCostNameById(id));
    }

    /**
     * 新增地址成本配置
     */
    @ApiOperation(value = "新增地址成本配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ErpBizServiceAddressCostName erpBizServiceAddressCostName)
    {
        return toAjax(erpBizServiceAddressCostNameService.insertErpBizServiceAddressCostName(erpBizServiceAddressCostName));
    }

    /**
     * 修改地址成本配置
     */
    @ApiOperation(value = "修改地址成本配置")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ErpBizServiceAddressCostName erpBizServiceAddressCostName)
    {
        return toAjax(erpBizServiceAddressCostNameService.updateErpBizServiceAddressCostName(erpBizServiceAddressCostName));
    }

    /**
     * 删除地址成本配置
     */
    @ApiOperation(value = "删除地址成本配置")
    @PreAuthorize(hasPermi = "erp:name:remove")
    //@Log(title = "地址成本配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpBizServiceAddressCostNameService.deleteErpBizServiceAddressCostNameByIds(ids));
    }

    @GetMapping("/getAllByProductId")
    public List<ErpBizServiceAddressCostName> getAllByProductId(@ApiParam(name="productId",value="产品ID") @RequestParam(name = "productId", required = false) Long productId)
    {
        return erpBizServiceAddressCostNameService.getAllByProductId(productId);
    }
}
