package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpTransactionVoucherFollowInfoMapper;
import com.nnb.erp.domain.ErpTransactionVoucherFollowInfo;
import com.nnb.erp.service.IErpTransactionVoucherFollowInfoService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-05
 */
@Service
public class ErpTransactionVoucherFollowInfoServiceImpl implements IErpTransactionVoucherFollowInfoService 
{
    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ErpTransactionVoucherFollowInfo selectErpTransactionVoucherFollowInfoById(Long id)
    {
        return erpTransactionVoucherFollowInfoMapper.selectErpTransactionVoucherFollowInfoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpTransactionVoucherFollowInfo 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ErpTransactionVoucherFollowInfo> selectErpTransactionVoucherFollowInfoList(ErpTransactionVoucherFollowInfo erpTransactionVoucherFollowInfo)
    {
        return erpTransactionVoucherFollowInfoMapper.selectErpTransactionVoucherFollowInfoList(erpTransactionVoucherFollowInfo);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpTransactionVoucherFollowInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertErpTransactionVoucherFollowInfo(ErpTransactionVoucherFollowInfo erpTransactionVoucherFollowInfo)
    {
        return erpTransactionVoucherFollowInfoMapper.insertErpTransactionVoucherFollowInfo(erpTransactionVoucherFollowInfo);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpTransactionVoucherFollowInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateErpTransactionVoucherFollowInfo(ErpTransactionVoucherFollowInfo erpTransactionVoucherFollowInfo)
    {
        return erpTransactionVoucherFollowInfoMapper.updateErpTransactionVoucherFollowInfo(erpTransactionVoucherFollowInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpTransactionVoucherFollowInfoByIds(Long[] ids)
    {
        return erpTransactionVoucherFollowInfoMapper.deleteErpTransactionVoucherFollowInfoByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpTransactionVoucherFollowInfoById(Long id)
    {
        return erpTransactionVoucherFollowInfoMapper.deleteErpTransactionVoucherFollowInfoById(id);
    }
}
