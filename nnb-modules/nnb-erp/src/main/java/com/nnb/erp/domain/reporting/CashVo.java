package com.nnb.erp.domain.reporting;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-01-25
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CashVo implements Serializable {

    @Excel(name = "收款方式", readConverterExp = "0=二维码,1=对公转账,2=现金,3=POS机,4=店铺,5=其他,6=抵扣款")
    private Integer paymentType;

    @Excel(name = "收款公司主体")
    private String payCompanyStr;

    @Excel(name = "支付时间")
    private String payTime;

    @Excel(name = "单据编号")
    private String documentNumber;

    @Excel(name = "客户名称")
    private String clientName;

    @Excel(name = "金额")
    private BigDecimal fee;

    @Excel(name = "收款人")
    private String userName;

}
