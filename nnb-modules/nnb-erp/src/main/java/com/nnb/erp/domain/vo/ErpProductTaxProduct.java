package com.nnb.erp.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ErpProductTaxProduct implements Serializable {
    private static final long serialVersionUID = 1854729781685687624L;

    //纳税类型id
    private Long numTaxId;
    //纳税类型名称
    private String vcTaxName;

}
