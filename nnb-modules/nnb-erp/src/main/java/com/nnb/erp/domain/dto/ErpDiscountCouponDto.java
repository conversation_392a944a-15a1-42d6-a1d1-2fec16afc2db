package com.nnb.erp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券额度对象 erp_discount_coupon_amount
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@ApiModel(value = "ErpDiscountCouponAmount", description = "优惠券额度对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ErpDiscountCouponDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 优惠券额度
     */
    @ApiModelProperty("优惠券额度")
    private BigDecimal discountCouponAmount;

    /**
     * 分配对象id
     */
    @ApiModelProperty("分配对象id")
    private Long belongUserId;

    @ApiModelProperty("产品id")
    private Long numProductId;

    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("线索id")
    private Long clientId;

    @ApiModelProperty("线索id")
    private Integer numType;

    @ApiModelProperty("小程序优惠券绑定手机号")
    private String xcxPhone;

    @ApiModelProperty("最小使用金额")
    private BigDecimal minPrice;

    @ApiModelProperty("是否开启优惠券有效期1：开启，0：未开启")
    private Integer validityPeriod;

    @ApiModelProperty("优惠券有效期开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actStartTime;

    @ApiModelProperty("优惠券有效期结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actEndTime;

    @ApiModelProperty("优惠券说明")
    private String remark;

    @ApiModelProperty("小程序优惠券活动配置id")
    private Long xcxCouponConfigId;

}
