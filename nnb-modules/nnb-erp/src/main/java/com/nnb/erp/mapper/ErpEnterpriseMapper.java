package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpEnterprise;
import com.nnb.erp.domain.dto.approval.ApprovalsDTO;
import com.nnb.erp.domain.vo.ErpClientForOrderDetailVO;
import com.nnb.erp.domain.vo.backs.QueryForAccountantBacksListVO;
import com.nnb.erp.domain.vo.client.ErpClientListResultVo;
import com.nnb.erp.domain.vo.client.ErpEnterpriseDto;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 企业Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
public interface ErpEnterpriseMapper
{
    /**
     * 查询企业
     *
     * @param id 企业主键
     * @return 企业
     */
    public ErpEnterprise selectErpEnterpriseById(Long id);

    public ErpEnterprise selectErpEnterpriseByIdList(Long id);


    /**
     * 查询企业列表
     *
     * @param erpEnterprise 企业
     * @return 企业集合
     */
    public List<ErpClientListResultVo> selectErpEnterpriseList(@Param("erpEnterprise") ErpEnterpriseDto erpEnterprise);

    /**
     * 新增企业
     *
     * @param erpEnterprise 企业
     * @return 结果
     */
    public int insertErpEnterprise(ErpEnterprise erpEnterprise);

    /**
     * 修改企业
     *
     * @param erpEnterprise 企业
     * @return 结果
     */
    public int updateErpEnterprise(ErpEnterprise erpEnterprise);

    /**
     * 删除企业
     *
     * @param id 企业主键
     * @return 结果
     */
    public int deleteErpEnterpriseById(Long id);

    /**
     * 批量删除企业
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpEnterpriseByIds(Long[] ids);

    /**
     * 获取指定企业名称的记录条数。
     *
     * @param companyName 企业名称。
     * @return 返回指定企业名称的记录条数。
     * <AUTHOR>
     * @since 2022-03-14 18:00:00
     */
    public Integer countEnterpriseByName(@Param("companyName") String companyName);

    List<SysUser> selectUser(@Param("aLong") Long aLong);

    ErpEnterprise selectErpEnterpriseLists(ErpEnterprise erpEnterprise);

    List<ErpEnterprise> selectErpEnterpriseListByName(ErpEnterprise erpEnterprise);

    List<ErpClientListResultVo> companyList(@Param("erpEnterprise") ErpEnterpriseDto erpEnterprise);

    List<Integer> selectOrderIdsByCompanyName(@Param("enterpriseName") String enterpriseName);

    List<Integer> selectEnIdByCompanyName(@Param("enterpriseName") String enterpriseName);

    List<Integer> selectOrderIdsByLegalName(@Param("legalPersonName") String legalPersonName);

    List<Integer> selectEnIdByLegalName(@Param("legalPersonName") String legalPersonName);

    List<Integer> selectOrderIdsByContactName(@Param("contactName") String contactName);

    List<Integer> selectEnIdByContactName(@Param("contactName") String contactName);

    List<Integer> selectOrderIdsByCityId(@Param("clientCityId") Integer clientCityId);

    List<Integer> selectEnIdByCityId(@Param("clientCityId") Integer clientCityId);

    List<Integer> selectOrderIdsByTaxId(@Param("corporatePropertyId") Integer corporatePropertyId);

    List<Integer> selectEnIdByTaxId(@Param("corporatePropertyId") Integer corporatePropertyId);

    List<Integer> selectOrderIdsByContract(@Param("contract") Integer contract);

    List<QueryForAccountantBacksListVO> selectCompanyNameByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<QueryForAccountantBacksListVO> selectCompanyNameByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectCompanyNameByClientIds(@Param("clientIds") List<Integer> clientIds);

    List<QueryForAccountantBacksListVO> selectCityNameByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<QueryForAccountantBacksListVO> selectCityNameByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectCityNameByClientIds(@Param("clientIds") List<Integer> clientIds);

    List<QueryForAccountantBacksListVO> selectLegalPersonByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<QueryForAccountantBacksListVO> selectLegalPersonByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectContactByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<QueryForAccountantBacksListVO> selectContactByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectAccountContactByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectTaxByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<QueryForAccountantBacksListVO> selectTaxByEnIds(@Param("enIds") List<Integer> enIds);

    List<QueryForAccountantBacksListVO> selectTimeByOrderIds(@Param("orderIds") List<Integer> orderIds);

    List<ErpEnterprise> selectErpEnterpriseListByIds(@Param("ids") List<String> ids);

    List<ErpClientForOrderDetailVO> selectContactByEnIdsFromOrder(@Param("orderIdList") List<Integer> orderIdList);

    List<Map<String, Object>> selectCityNameByCityIds(@Param("cityIds") List<Long> cityIds);

    @Select("select * from erp_trades where name = #{name}")
    List<Map<String, Integer>> selectTradeByName(@Param("name") String name);

    List<Integer> selectClientIdByCompanyName(@Param("enterpriseName") String enterpriseName);

    List<Integer> selectClientIdByContactName(@Param("contractName") String contractName);

    @Select("select id from erp_client where num_city_id = #{clientCityId}")
    List<Integer> selectClientIdByCityId(@Param("clientCityId") Integer clientCityId);

    @Select("SELECT eo.vc_order_number FROM erp_enterprise ee LEFT JOIN erp_client ec ON ec.num_enterprise_id = ee.id " +
            "LEFT JOIN erp_orders eo ON eo.num_client_id = ec.id WHERE ee.id = #{id}")
    List<String> selectOrderNumberByEnterpriseId(@Param("id") Long id);

    @Update("update erp_enterprise set kp_limit = #{kpLimit} where id = #{enterpriseId}")
    public int updateKpLimit(@Param("enterpriseId") Long enterpriseId, @Param("kpLimit") BigDecimal kpLimit);

    List<Integer> selectOrderIdByAmount(@Param("realAmountStart") BigDecimal realAmountStart, @Param("realAmountEnd") BigDecimal realAmountEnd);

    @Update("UPDATE erp_enterprise SET year_inspect = 1 WHERE id = #{numEnterpriseId}")
    int yearInspect(@Param("numEnterpriseId") Long numEnterpriseId);

    @Update("UPDATE erp_enterprise SET year_inspect = 2")
    int updateYearInspectAll();

    List<ErpEnterprise> selectErpEnterpriseListLimit(@Param("companyName") String companyName);
}
