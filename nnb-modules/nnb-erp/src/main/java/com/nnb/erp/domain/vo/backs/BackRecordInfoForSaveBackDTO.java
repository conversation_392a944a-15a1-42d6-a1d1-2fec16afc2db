package com.nnb.erp.domain.vo.backs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 回访录音信息，用于添加回访，DTO。
 *
 * <AUTHOR>
 * @since 2022/7/4 17:48
 */
@Data
public class BackRecordInfoForSaveBackDTO {

    /**
     * 录音文件URL。
     */
    @ApiModelProperty("录音文件URL。")
    private String recordUrl;

    /**
     * 录音时长，秒。
     */
    @ApiModelProperty("录音时长，秒。")
    private BigDecimal recordTime;

    /**
     * 是否接通：1-是；2-否。
     */
    @ApiModelProperty("是否接通：1-是；2-否。")
    private Integer status;

}
