package com.nnb.erp.controller;

import java.util.ArrayList;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.erp.domain.CompanyCoupon;
import com.nnb.erp.domain.dto.CouponUsageDto;
import com.nnb.erp.domain.dto.ErpDiscountCouponDto;
import com.nnb.erp.domain.vo.CouponUsageVo;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponVo;
import com.nnb.erp.service.EmailService;
import com.nnb.system.api.model.BdClue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpDiscountCoupon;
import com.nnb.erp.service.IErpDiscountCouponService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 优惠券Controller
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@RestController
@RequestMapping("/ErpDiscountCoupon")
@Api(tags = "ErpDiscountCouponController", description = "优惠券")
@Slf4j
public class ErpDiscountCouponController extends BaseController
{
    @Autowired
    private IErpDiscountCouponService erpDiscountCouponService;

    @Autowired
    private EmailService emailService;

    /**
     * 查询优惠券列表
     */
    @ApiOperation(value = "查询优惠券列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpDiscountCoupon.class)})
    //@PreAuthorize(hasPermi = "erp:coupon:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpDiscountCoupon erpDiscountCoupon)
    {
        startPage();
        List<ErpDiscountCoupon> list = erpDiscountCouponService.selectErpDiscountCouponList(erpDiscountCoupon);
        return getDataTable(list);
    }

    /**
     * 查询折扣券
     */
    @ApiOperation(value = "查询优惠券列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpDiscountCoupon.class)})
    //@PreAuthorize(hasPermi = "erp:coupon:list")
    @GetMapping("/companyCouponList")
    public TableDataInfo companyCouponList(CompanyCoupon CompanyCoupon)
    {
        startPage();
        List<CompanyCoupon> list = erpDiscountCouponService.companyCouponList(CompanyCoupon);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询优惠券列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpDiscountCoupon.class)})
//    @PreAuthorize(hasPermi = "erp:coupon:page")
    @PostMapping("/page")
    public TableDataInfo page(@RequestBody ErpDiscountCoupon erpDiscountCoupon)
    {
        List<ErpDiscountCouponVo> erpDiscountCouponVos = new ArrayList<>();
        Integer pageNum = erpDiscountCoupon.getPageNum();
        Integer pageSize = erpDiscountCoupon.getPageSize();
        Long total = 0L;
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            Page<Object> page = PageHelper.startPage(pageNum, pageSize, null);
            erpDiscountCouponVos = erpDiscountCouponService.selectErpDiscountCouponVoList(erpDiscountCoupon);
            total = page.getTotal();
        }
//        erpDiscountCoupon.setCreateUser(userId);

        return getDataTableAndTotal(erpDiscountCouponVos, total);
    }

    /**
     * 导出优惠券列表
     */
    @ApiOperation(value = "导出优惠券列表")
//    @PreAuthorize(hasPermi = "erp:coupon:export")
    //@Log(title = "优惠券", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpDiscountCoupon erpDiscountCoupon) throws IOException
    {
        List<ErpDiscountCoupon> list = erpDiscountCouponService.selectErpDiscountCouponList(erpDiscountCoupon);
        ExcelUtil<ErpDiscountCoupon> util = new ExcelUtil<ErpDiscountCoupon>(ErpDiscountCoupon.class);
        util.exportExcel(response, list, "优惠券数据");
    }

    /**
     * 获取优惠券详细信息
     */
    @ApiOperation(value = "获取优惠券详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpDiscountCoupon.class)})
//    @PreAuthorize(hasPermi = "erp:coupon:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="优惠券id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpDiscountCouponService.selectErpDiscountCouponById(id));
    }

    /**
     * 新增优惠券
     */
    @ApiOperation(value = "新增优惠券")
//    @PreAuthorize(hasPermi = "erp:coupon:add")
    //@Log(title = "优惠券", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpDiscountCoupon erpDiscountCoupon)
    {
        return toAjax(erpDiscountCouponService.insertErpDiscountCoupon(erpDiscountCoupon));
    }

    /**
     * 修改优惠券
     */
    @ApiOperation(value = "修改优惠券")
//    @PreAuthorize(hasPermi = "erp:coupon:edit")
    //@Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpDiscountCoupon erpDiscountCoupon)
    {
        return toAjax(erpDiscountCouponService.updateErpDiscountCoupon(erpDiscountCoupon));
    }

    /**
     * 删除优惠券
     */
    @ApiOperation(value = "删除优惠券")
//    @PreAuthorize(hasPermi = "erp:coupon:remove")
    //@Log(title = "优惠券", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpDiscountCouponService.deleteErpDiscountCouponByIds(ids));
    }

    /**
     * 生成优惠券
     * @param erpDiscountCouponDto
     * @return
     */
    @ApiOperation(value = "生成优惠券")
    @PostMapping("/generateErpDiscountCoupon")
    public AjaxResult generateErpDiscountCoupon(@RequestBody ErpDiscountCouponDto erpDiscountCouponDto)
    {
        try {
            return toAjax(erpDiscountCouponService.generateErpDiscountCoupon(erpDiscountCouponDto));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }



    /**
     * 优惠券撤销
     * @return
     */
    @ApiOperation(value = "优惠券撤销")
//    @PreAuthorize(hasPermi = "erp:coupon:revocationErpDiscountCoupon")
    @GetMapping("/revocationErpDiscountCoupon/{id}")
    public AjaxResult revocationErpDiscountCoupon(@PathVariable("id") Long id)
    {
        try {
            return toAjax(erpDiscountCouponService.revocationErpDiscountCoupon(id));
        } catch (Exception e) {
            log.error("优惠券撤销异常，异常信息为：", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping("/sendEmail")
    public void sendEmail(){
        emailService.sendEmail("<EMAIL>","优惠券测试","优惠券测试");
    }

    @ApiOperation(value = "提单获取对应产品和线索的优惠券")
    @GetMapping("/getErpDiscountCoupon/{numProductId}/{clueId}")
    public AjaxResult getErpDiscountCoupon(@PathVariable("numProductId") Long numProductId, @PathVariable("clueId") Long clueId){
        try {
            return AjaxResult.success(erpDiscountCouponService.getErpDiscountCoupon(numProductId, clueId));
        } catch (Exception e) {
            log.error("提单获取对应产品和线索的优惠券异常，异常信息为：", e);
            return AjaxResult.error();
        }
    }


    @ApiOperation(value = "优惠券使用统计")
    @GetMapping("/couponUsage")
    public TableDataInfo couponUsage(CouponUsageDto couponUsageDto){
        Page<Object> objects = startPageReturn();
        List<CouponUsageVo> list = erpDiscountCouponService.couponUsage(couponUsageDto);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    @ApiOperation(value = "小程序根据手机号获取优惠券")
    @GetMapping("/getXcxCouponByPhone")
    public AjaxResult getXcxCouponByPhone(@RequestParam String phone){
        return AjaxResult.success(erpDiscountCouponService.getXcxCouponByPhone(phone));
    }
}
