package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_promotional_activities_product
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
@ApiModel(value="ErpPromotionalActivitiesProduct",description="【请填写功能名称】对象")
public class ErpPromotionalActivitiesProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Integer id;

    /** 规则ID */
    @Excel(name = "规则ID")
    @ApiModelProperty("规则ID")
    private Integer activitieId;

    /** 类型本金1赠金2 */
    @Excel(name = "类型本金1赠金2")
    @ApiModelProperty("类型本金1赠金2")
    private Integer type;

    /** 适用产品ID */
    @Excel(name = "适用产品ID")
    @ApiModelProperty("适用产品ID")
    private String productId;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    @ApiModelProperty("只定产品是否反选1是0否")
    private Long invert;

    /** 适用产品ID */
    @Excel(name = "适用产品ID")
    @ApiModelProperty("适用产品ID")
    private String productIdInvert;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setActivitieId(Integer activitieId) 
    {
        this.activitieId = activitieId;
    }

    public Integer getActivitieId() 
    {
        return activitieId;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setProductId(String productId) 
    {
        this.productId = productId;
    }

    public String getProductId() 
    {
        return productId;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }

    public Long getInvert() {
        return invert;
    }

    public void setInvert(Long invert) {
        this.invert = invert;
    }

    public String getProductIdInvert() {
        return productIdInvert;
    }

    public void setProductIdInvert(String productIdInvert) {
        this.productIdInvert = productIdInvert;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("activitieId", getActivitieId())
            .append("type", getType())
            .append("productId", getProductId())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
