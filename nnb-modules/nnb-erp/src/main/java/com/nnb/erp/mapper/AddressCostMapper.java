package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.AddressCost;

/**
 * 地址成本配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-28
 */
public interface AddressCostMapper 
{
    /**
     * 查询地址成本配置
     * 
     * @param id 地址成本配置主键
     * @return 地址成本配置
     */
    public AddressCost selectAddressCostById(Long id);

    /**
     * 查询地址成本配置列表
     * 
     * @param addressCost 地址成本配置
     * @return 地址成本配置集合
     */
    public List<AddressCost> selectAddressCostList(AddressCost addressCost);

    /**
     * 新增地址成本配置
     * 
     * @param addressCost 地址成本配置
     * @return 结果
     */
    public int insertAddressCost(AddressCost addressCost);

    /**
     * 修改地址成本配置
     * 
     * @param addressCost 地址成本配置
     * @return 结果
     */
    public int updateAddressCost(AddressCost addressCost);

    /**
     * 删除地址成本配置
     * 
     * @param id 地址成本配置主键
     * @return 结果
     */
    public int deleteAddressCostById(Long id);

    /**
     * 批量删除地址成本配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAddressCostByIds(Long[] ids);
}
