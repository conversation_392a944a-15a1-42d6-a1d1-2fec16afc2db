package com.nnb.erp.domain.vo;

import com.nnb.erp.domain.ErpExamineApproveTypePersion;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpExamineApproveTypePersionVo extends ErpExamineApproveTypePersion {
    private String approveName;
    private String deptName;
    private String statusName;


    List<Map<String, Object>> info;
}
