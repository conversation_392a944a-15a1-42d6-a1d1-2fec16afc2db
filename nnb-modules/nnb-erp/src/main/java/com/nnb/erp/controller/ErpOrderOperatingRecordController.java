package com.nnb.erp.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.vo.ErpOrderOperationRecordForOrderDetailVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpOrderOperatingRecord;
import com.nnb.erp.service.IErpOrderOperatingRecordService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 订单操作记录Controller
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@RestController
@RequestMapping("/ErpOrderOperatingRecord")
@Api(tags = "ErpOrderOperatingRecordController", description = "订单操作记录")
public class ErpOrderOperatingRecordController extends BaseController {
    @Autowired
    private IErpOrderOperatingRecordService erpOrderOperatingRecordService;

    /**
     * 查询订单操作记录列表
     */
    @ApiOperation(value = "查询订单操作记录列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = ErpOrderOperatingRecord.class)})
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpOrderOperatingRecord erpOrderOperatingRecord) {
        startPage();
        List<ErpOrderOperatingRecord> list = erpOrderOperatingRecordService.selectErpOrderOperatingRecordList(erpOrderOperatingRecord);
        return getDataTable(list);
    }

    /**
     * 导出订单操作记录列表
     */
    @ApiOperation(value = "导出订单操作记录列表")
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:export")
    //@Log(title = "订单操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpOrderOperatingRecord erpOrderOperatingRecord) throws IOException {
        List<ErpOrderOperatingRecord> list = erpOrderOperatingRecordService.selectErpOrderOperatingRecordList(erpOrderOperatingRecord);
        ExcelUtil<ErpOrderOperatingRecord> util = new ExcelUtil<ErpOrderOperatingRecord>(ErpOrderOperatingRecord.class);
        util.exportExcel(response, list, "订单操作记录数据");
    }

    /**
     * 获取订单操作记录详细信息
     */
    @ApiOperation(value = "获取订单操作记录详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = ErpOrderOperatingRecord.class)})
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name = "id", value = "订单操作记录id") @PathVariable("id") Long id) {
        return AjaxResult.success(erpOrderOperatingRecordService.selectErpOrderOperatingRecordById(id));
    }

    /**
     * 新增订单操作记录
     */
    @ApiOperation(value = "新增订单操作记录")
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:add")
    //@Log(title = "订单操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpOrderOperatingRecord erpOrderOperatingRecord) {
        return toAjax(erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord));
    }

    /**
     * 修改订单操作记录
     */
    @ApiOperation(value = "修改订单操作记录")
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:edit")
    //@Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpOrderOperatingRecord erpOrderOperatingRecord) {
        return toAjax(erpOrderOperatingRecordService.updateErpOrderOperatingRecord(erpOrderOperatingRecord));
    }

    /**
     * 删除订单操作记录
     */
    @ApiOperation(value = "删除订单操作记录")
    @PreAuthorize(hasPermi = "erp:ErpOrderOperatingRecord:remove")
    //@Log(title = "订单操作记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(erpOrderOperatingRecordService.deleteErpOrderOperatingRecordByIds(ids));
    }

    /**
     * 获取指定订单操作记录。
     *
     * @param orderId 订单标识。
     * @return 返回操作记录集合。
     * <AUTHOR>
     * @since 2022-04-27 10:20:30
     */
    @GetMapping("/getOperationRecord")
    @ApiOperation("获取指定订单操作记录。")
    public TableDataInfo getOperationRecordByOrderId(@RequestParam Long orderId) {
        startPage();
        List<ErpOrderOperationRecordForOrderDetailVO> operationRecordList = erpOrderOperatingRecordService.getOperationRecordByOrderId(orderId, null, "2");
        return getDataTable(operationRecordList);
    }

    /**
     * 获取指定订单操作记录。
     *
     * @param type 1创建订单2修改订单。
     * @return 返回操作记录集合。
     * <AUTHOR>
     * @since 2022-04-27 10:20:30
     */
    @GetMapping("/operateOrderGetRecord")
    @ApiOperation("获取指定订单操作记录。")
    public TableDataInfo operateOrderGetRecord(@RequestParam Long orderId, Integer examineType) {
        if (ObjectUtils.isEmpty(examineType) || !Arrays.asList(1,2,4).contains(examineType)) {
            return getDataTable(new ArrayList<>());
        }
        startPage();
        List<Long> typeList = new ArrayList<>();
        if (examineType.intValue() == 1) {
            typeList = Arrays.asList(1L,2L,3L,4L,5L,6L,7L,30L,31L);
        }
        if (examineType.intValue() == 2) {
            typeList = Arrays.asList(8L,9L,10L,11L,12L,26L,27L,30L,31L);
        }
        if (examineType.intValue() == 4) {
            typeList = Arrays.asList(19L,20L,21L,26L,27L);
        }

        List<ErpOrderOperationRecordForOrderDetailVO> operationRecordList = erpOrderOperatingRecordService.getOperationRecordByOrderId(orderId, typeList, "1");
        return getDataTable(operationRecordList);
    }

}
