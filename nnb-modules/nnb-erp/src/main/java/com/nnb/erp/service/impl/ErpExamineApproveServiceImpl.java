package com.nnb.erp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.*;
import com.nnb.erp.constant.enums.*;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.approval.*;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.dto.*;
import com.nnb.erp.domain.dto.approval.ErpExamineApproveQueryDTO;
import com.nnb.erp.domain.dto.approval.ErpQzdPaymentRecordDTO;
import com.nnb.erp.domain.generalapproval.AdvancePaymentVo;
import com.nnb.erp.domain.generalapproval.AppliedOrder;
import com.nnb.erp.domain.reporting.ErpOrderPayRecord;
import com.nnb.erp.domain.gift.ErpOrderGift;
import com.nnb.erp.domain.order.ErpOrderRefundCostExpenditure;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.approval.*;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftDetailVO;
import com.nnb.erp.domain.vo.report.CostSettleManageDetailVo;
import com.nnb.erp.enums.ErpOrderPayRecordEnum;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.approval.ApprovalsMapper;
import com.nnb.erp.mapper.approval.CostSettlementMapper;
import com.nnb.erp.mapper.approval.ErpQzdApprovalServiceMapper;
import com.nnb.erp.mapper.approval.ErpQzdPaymentRecordMapper;
import com.nnb.erp.mapper.reporting.ErpOrderPayRecordMapper;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftMapper;
import com.nnb.erp.mapper.order.ErpOrderRefundCostExpenditureMapper;
import com.nnb.erp.service.*;
import com.nnb.erp.service.approval.IErpQzdApprovalLogService;
import com.nnb.erp.util.MybatisBatchUtils;
import com.nnb.erp.util.PDFUtil;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Service
@Slf4j
@RefreshScope
public class ErpExamineApproveServiceImpl implements IErpExamineApproveService
{
    @Autowired
    private ErpExamineApproveMapper erpExamineApproveMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private ErpQzdPaymentRecordMapper erpQzdPaymentRecordMapper;
    @Autowired
    private ErpQzdApprovalServiceMapper erpQzdApprovalServiceMapper;
    @Autowired
    private IErpQzdApprovalLogService erpQzdApprovalLogService;
    @Autowired
    private ErpLicenseMapper erpLicenseMapper;
    @Autowired
    private CostSettlementMapper costSettlementMapper;
    @Autowired
    private ErpEnterpriseMapper erpEnterpriseMapper;
    @Autowired
    private ErpBizServiceAdvanceMapper erpBizServiceAdvanceMapper;
    @Autowired
    private ErpExamineApproveTypePersionMapper erpExamineApproveTypePersionMapper;
    @Autowired
    private DingDingService dingDingService;
    @Autowired
    private ErpExamineApproveTypeManageMapper erpExamineApproveTypeManageMapper;
    @Autowired
    private ErpProductDetailMapper erpProductDetailMapper;
    @Autowired
    private ApprovalsMapper approvalsMapper;
    @Autowired
    private ErpOrdersMapper erpOrdersMapper;
    @Autowired
    private ErpServiceOrdersMapper erpServiceOrdersMapper;
    @Autowired
    private ErpOrderRefundMapper erpOrderRefundMapper;
    @Autowired
    private ErpOrderRefundDetailMapper erpOrderRefundDetailMapper;
    @Autowired
    private SServiceMainMapper sServiceMainMapper;
    @Autowired
    private MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    private ErpContractMapper erpContractMapper;
    @Autowired
    private ErpProcureContractMapper erpProcureContractMapper;
    @Autowired
    private ErpOrderPayRecordMapper erpOrderPayRecordMapper;

    @Value("${erp.contract.pdf.path}")
    private String parentPath;
    @Resource
    private OssService ossService;
    @Value("${crm.url}")
    private String crmUrl;

    @Autowired
    private ErpOrderRefundCostExpenditureMapper erpOrderRefundCostExpenditureMapper;

    @Autowired
    private ErpOrderGiftMapper erpOrderGiftMapper;

    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;

    @Value("${erp.order.refund.consigner.dingId}")
    private String erpOrderRefundConsignerId;

    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;

    @Autowired
    private IErpProcureContractService procureContractService;
    @Autowired
    private ErpOrderInvoiceMapper erpOrderInvoiceMapper;
    @Autowired
    private ErpOrderInvoiceDetailMapper erpOrderInvoiceDetailMapper;
    @Autowired
    private ErpExamineApproveDetailMapper approveDetailMapper;
    @Autowired
    private ErpExamineAccountLsMapper erpExamineAccountLsMapper;
    @Autowired
    private ErpExamineBudgetDetailMapper erpExamineBudgetDetailMapper;
    @Autowired
    private ErpBudgetTypeMapper erpBudgetTypeMapper;
    @Autowired
    private ErpExamineBudgetDetailConsumeMapper erpExamineBudgetDetailConsumeMapper;
    @Autowired
    private ErpExamineApproveCommonPaymentMapper erpExamineApproveCommonPaymentMapper;
    @Autowired
    private ErpOrderInvoiceRedMapper erpOrderInvoiceRedMapper;
    @Autowired
    private SServiceLossReasonMapper sServiceLossReasonMapper;
    @Autowired
    private IErpWtdzKpService wtdzKpService;
    @Autowired
    private ErpWtdzKpMapper wtdzKpMapper;
    @Autowired
    private ErpExamineOtherOrderPayMapper erpExamineOtherOrderPayMapper;
    @Autowired
    private ErpAccountDateUpdateMapper erpAccountDateUpdateMapper;
    @Autowired
    private ErpProductApproveMapper erpProductApproveMapper;
    @Autowired
    private ErpProductDetailServiceImpl erpProductDetailService;
    @Autowired
    private SServiceQualificationsExtensionServiceImpl serviceQualificationsExtensionService;
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ErpExamineApprove selectErpExamineApproveById(Long id)
    {
        return erpExamineApproveMapper.selectErpExamineApproveById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param erpExamineApprove 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ErpExamineApprove> selectErpExamineApproveList(ErpExamineApproveDTO erpExamineApproveDTO)
    {
        return erpExamineApproveMapper.selectErpExamineApproveList(erpExamineApproveDTO);
    }

    /***
     * 判断是否走简易审批流
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ErpExamineBudgetDetailConsume> checkSample(ErpExamineApprove erpExamineApprove, SysUser sysUser, ErpExamineApproveTypeManage approveTypeManage) {
        SysDept sysDept = sysUser.getDept();
        List<ErpExamineBudgetDetailConsume> budgetDetailConsumeList = new ArrayList<>();

        //判断是否为消耗预算审批流
        if (ObjectUtils.isNotEmpty(approveTypeManage.getBudgetId())
                && ObjectUtils.isNotEmpty(erpExamineApprove.getFee()) && erpExamineApprove.getFee().compareTo(new BigDecimal("0")) > 0) {

            BigDecimal fee = erpExamineApprove.getFee();

            //查询可用余额预算
            ErpExamineBudgetDetailDto budgetDetailDto = new ErpExamineBudgetDetailDto();
            List<Long> ancestors = Arrays.asList(sysUser.getDept().getAncestors().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            ancestors.add(sysUser.getDeptId());
            budgetDetailDto.setAncestorsDept(ancestors);
            budgetDetailDto.setBudgetType(approveTypeManage.getBudgetId());
            budgetDetailDto.setBudgetMonth(DateUtils.parseDateToStr(DateUtils.YYYY_MM, new Date()));
            List<ErpExamineBudgetDetail> budgetDetailList = erpExamineBudgetDetailMapper.selectListByDto(budgetDetailDto);

            if (ObjectUtils.isNotEmpty(budgetDetailList) && budgetDetailList.size() > 0) {
                BigDecimal budgetFee = new BigDecimal("0");
                for (int i = 0; i < budgetDetailList.size(); i++) {
                    ErpExamineBudgetDetail budgetDetail = budgetDetailList.get(i);
                    budgetFee = budgetFee.add(ObjectUtils.isEmpty(budgetDetail.getBudgetBalance()) ? new BigDecimal("0") : budgetDetail.getBudgetBalance());
                }
                if (budgetFee.compareTo(fee) > 0) {

                    budgetDetailDto.setAncestorsDept(null);
                    do {
                        budgetDetailDto.setBudgetDept(sysUser.getDeptId());
                        List<ErpExamineBudgetDetail> budgetDetailBalanceList = erpExamineBudgetDetailMapper.selectListByDto(budgetDetailDto);
                        for (int i = 0; i < budgetDetailBalanceList.size(); i++) {
                            ErpExamineBudgetDetail budgetDetailBalance = budgetDetailBalanceList.get(i);
                            BigDecimal balanceFee = budgetDetailBalance.getBudgetBalance();

                            if (balanceFee.compareTo(new BigDecimal("0")) > 0) {
                                ErpExamineBudgetDetailConsume budgetDetailConsume = new ErpExamineBudgetDetailConsume();
                                budgetDetailConsume.setBudgetDetailId(budgetDetailBalance.getId());
                                if (fee.compareTo(balanceFee) > 0) {
                                    budgetDetailConsume.setFee(balanceFee);
                                    budgetDetailBalance.setBudgetBalance(new BigDecimal("0"));
                                    fee = fee.subtract(balanceFee);
                                } else {
                                    budgetDetailConsume.setFee(fee);
                                    budgetDetailBalance.setBudgetBalance(budgetDetailBalance.getBudgetBalance().subtract(fee));
                                    fee = new BigDecimal("0");
                                }
                                erpExamineBudgetDetailMapper.updateErpExamineBudgetDetail(budgetDetailBalance);
                                budgetDetailConsumeList.add(budgetDetailConsume);
                            }
                        }
                        sysDept = erpBudgetTypeMapper.selectDeptById(sysDept.getParentId().toString());
                    } while (fee.compareTo(new BigDecimal("0")) > 0);
                }
            }
        }
        return budgetDetailConsumeList;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param erpExamineApprove 【请填写功能名称】
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertErpExamineApprove(ErpExamineApprove erpExamineApprove)
    {
        if (
                ObjectUtil.isNull(erpExamineApprove.getOtherId())
                || ObjectUtil.isNull(erpExamineApprove.getApproveType())
        ) {
            throw new ServiceException("参数错误");
        }
        //消耗预算审批，消耗记录
        List<ErpExamineBudgetDetailConsume> consumeList = new ArrayList<>();

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if (ObjectUtil.isNotEmpty(erpExamineApprove.getCreatedUser())) {
            R<SysUser> createdUserInfo = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser().toString()), SecurityConstants.INNER);
            sysUser = createdUserInfo.getData();
        }
        ErpExamineApproveTypeManage approveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType()+""));

        ErpExamineApproveTypePersionDto dto = new ErpExamineApproveTypePersionDto();
        dto.setApproveType(erpExamineApprove.getApproveType());
        dto.setStatus(1);
        dto.setSample(2);

        consumeList = checkSample(erpExamineApprove, sysUser, approveTypeManage);
        if (consumeList.size() > 0) {
            dto.setSample(1);
        }
        List<ErpExamineApproveTypePersionVo> list = erpExamineApproveTypePersionMapper.selectErpExamineApproveTypePersionList(dto);

        if (list.size() > 0) {
            for (int z = 0; z < list.size(); z++) {
                ErpExamineApproveTypePersionVo vo = list.get(z);
                JSONArray userIdArr = ObjectUtils.isNotEmpty(vo.getUserIds()) ? JSONArray.fromObject(vo.getUserIds()) : new JSONArray();
                JSONArray deptIdArr = ObjectUtils.isNotEmpty(vo.getDeptIds()) ? JSONArray.fromObject(vo.getDeptIds()) : new JSONArray();
                if (
                        (approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && userIdArr.contains(sysUser.getUserId()))
                                || (approveTypeManage.getParentId().intValue() != ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && deptIdArr.contains(sysUser.getDeptId())) ) {

                    String followInfoStr = vo.getFollowInfo();
                    if (ObjectUtil.isNull(vo.getFollowInfo())) {
                        throw new ServiceException("审批流为空");
                    }

                    //获取审批流
                    JSONArray arr_ = JSONArray.fromObject(followInfoStr);
                    if (ObjectUtil.isNull(arr_) || arr_.size() == 0) {
                        throw new ServiceException("审批流错误");
                    }

                    //审批人是离职的判断
                    for (int i = 0; i <arr_.size(); i++) {
                        JSONObject approveObj = arr_.getJSONObject(i);
                        Integer approveUser = approveObj.getInt("approveUser");
                        R<SysUser> infoApprove = remoteUserService.getUserInfoById(Long.parseLong(approveUser+""), SecurityConstants.INNER);
                        if (Integer.parseInt(infoApprove.getData().getStatus()) == 1) {
                            throw new ServiceException(infoApprove.getData().getNickName()+"已离职，请联系财务修改审批人");
                        }
                    }
                    //跳过审批人是创建的
                    if (arr_.getJSONObject(0).getInt("approveUser") == loginUser.getSysUser().getUserId().intValue()) {
                        arr_.remove(0);
                    }
                    if (arr_.size() == 0) {
                        throw new ServiceException("审批流为空");
                    }

                    for (int j = 0; j < arr_.size(); j++) {
                        arr_.getJSONObject(j).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT);
                    }

                    int currentApproveUserId = arr_.getJSONObject(0).getInt("approveUser");
                    erpExamineApprove.setFollowInfo(arr_.toString());
                    erpExamineApprove.setApproveCurrentUser(currentApproveUserId);
                    erpExamineApprove.setCreatedUser(sysUser.getUserId().intValue());
                    erpExamineApprove.setUpdatedUser(sysUser.getUserId().intValue());
                    erpExamineApprove.setMakeCopyUser(",1,");
                    erpExamineApprove.setCreatedDate(new Date());
                    erpExamineApprove.setUpdatedTime(new Date());
                    erpExamineApproveMapper.insertErpExamineApprove(erpExamineApprove);

                    R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(currentApproveUserId+""), SecurityConstants.INNER);
                    //发送钉钉消息
                    String dingContent = "### 审批提醒： \n * " + "您好： \n "
                            +" * 审批类型: " + vo.getApproveName() + "    \n "
                            +" * 审批ID: " + erpExamineApprove.getId() + " 待审批 \n"
                            + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/approveManagement/pendingApprove?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
                    DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批提醒", dingContent);
                    dingDingService.sendDingMessage(dingSendDTO);
                    //

                    //记录日志  TODO  审批类型待加
                    ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.parseLong(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.SUBMIT_APPROVAL.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
                    erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);

                    for (int i = 0; i < consumeList.size(); i++) {
                        ErpExamineBudgetDetailConsume consume = consumeList.get(i);
                        consume.setApproveId(erpExamineApprove.getId());
                        if (consume.getFee().compareTo(new BigDecimal("0")) == 0) {
                            continue;
                        }
                        erpExamineBudgetDetailConsumeMapper.insertErpExamineBudgetDetailConsume(consume);
                    }

                    return Integer.parseInt(erpExamineApprove.getId().toString());
                }
            }
        }

        throw new ServiceException("暂无权限，请联系财务增加该类型下的部门");
    }

    @Override
    public int sendAgainErpExamineApprove(Long approveId) {
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(approveId);
        ErpExamineApproveTypeManage approveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType()+""));
        LoginUser loginUser = tokenService.getLoginUser();

        R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser().toString()), SecurityConstants.INNER);
        SysUser sysUser = info.getData();

        //消耗预算审批，消耗记录
        List<ErpExamineBudgetDetailConsume> consumeList = new ArrayList<>();

        ErpExamineApproveTypePersionDto dto = new ErpExamineApproveTypePersionDto();
        dto.setApproveType(erpExamineApprove.getApproveType());
        dto.setStatus(1);
        dto.setSample(2);

        consumeList = checkSample(erpExamineApprove, sysUser, approveTypeManage);
        if (consumeList.size() > 0) {
            dto.setSample(1);
        }
        List<ErpExamineApproveTypePersionVo> list = erpExamineApproveTypePersionMapper.selectErpExamineApproveTypePersionList(dto);
        if (list.size() > 0) {
            for (int z = 0; z < list.size(); z++) {
                ErpExamineApproveTypePersionVo vo = list.get(z);
                JSONArray userIdArr = ObjectUtils.isNotEmpty(vo.getUserIds()) ? JSONArray.fromObject(vo.getUserIds()) : new JSONArray();
                JSONArray deptIdArr = ObjectUtils.isNotEmpty(vo.getDeptIds()) ? JSONArray.fromObject(vo.getUserIds()) : new JSONArray();
                if (
                        (approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && userIdArr.contains(sysUser.getUserId()))
                                || (approveTypeManage.getParentId().intValue() != ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && deptIdArr.contains(sysUser.getUserId())) ) {

                    String followInfoStr = vo.getFollowInfo();
                    if (ObjectUtil.isNull(vo.getFollowInfo())) {
                        throw new ServiceException("审批流为空");
                    }

                    //获取审批流
                    JSONArray arr_ = JSONArray.fromObject(followInfoStr);
                    if (ObjectUtil.isNull(arr_) || arr_.size() == 0) {
                        throw new ServiceException("审批流错误");
                    }

                    //审批人是离职的判断
                    for (int i = 0; i <arr_.size(); i++) {
                        JSONObject approveObj = arr_.getJSONObject(i);
                        Integer approveUser = approveObj.getInt("approveUser");
                        R<SysUser> infoApprove = remoteUserService.getUserInfoById(Long.parseLong(approveUser+""), SecurityConstants.INNER);
                        if (Integer.parseInt(infoApprove.getData().getStatus()) == 1) {
                            throw new ServiceException(infoApprove.getData().getNickName()+"已离职，请联系财务修改审批人");
                        }
                    }
                    //跳过审批人是创建的
                    if (arr_.getJSONObject(0).getInt("approveUser") == loginUser.getSysUser().getUserId().intValue()) {
                        arr_.remove(0);
                    }
                    if (arr_.size() == 0) {
                        throw new ServiceException("审批流为空");
                    }

                    for (int j = 0; j < arr_.size(); j++) {
                        arr_.getJSONObject(j).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT);
                    }
                    int currentApproveUserId = arr_.getJSONObject(0).getInt("approveUser");
                    erpExamineApprove.setApproveCurrentUser(currentApproveUserId);

                    if (erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_CANCLE) {
                        erpExamineApprove.setFollowInfo(arr_.toString());
                        erpExamineApprove.setApproveCurrentPoint(0);
                    } else {
                        JSONArray newFollow = new JSONArray();
                        JSONArray oldFollow = JSONArray.fromObject(erpExamineApprove.getFollowInfo());
                        for (int j = 0; j < oldFollow.size(); j++) {
                            JSONObject oldFollowObj = oldFollow.getJSONObject(j);
                            if (oldFollowObj.getInt("approve_status") != ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT) {
                                newFollow.add(oldFollowObj);
                            }
                        }
                        JSONObject createFollowObj = new JSONObject();
                        createFollowObj.put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_SEND_AGAIN);
                        createFollowObj.put("approveUser", loginUser.getSysUser().getUserId());
                        createFollowObj.put("time", DateUtils.getTime());
                        newFollow.add(createFollowObj);

                        erpExamineApprove.setApproveCurrentPoint(newFollow.size());
                        for (int j = 0; j < arr_.size(); j++) {
                            newFollow.add(arr_.getJSONObject(j));
                        }
                        erpExamineApprove.setFollowInfo(newFollow.toString());
                    }


                    erpExamineApprove.setApproveStatus(0);
                    erpExamineApprove.setUpdatedUser(loginUser.getSysUser().getUserId().intValue());
                    erpExamineApprove.setMakeCopyUser(",1,");
                    erpExamineApprove.setUpdatedTime(new Date());
                    erpExamineApproveMapper.updateErpExamineApprove(erpExamineApprove);

                    //记录日志  TODO  审批类型待加
                    ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.parseLong(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.SUBMIT_APPROVAL.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
                    erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);

                    for (int i = 0; i < consumeList.size(); i++) {
                        ErpExamineBudgetDetailConsume consume = consumeList.get(i);
                        consume.setApproveId(erpExamineApprove.getId());
                        if (consume.getFee().compareTo(new BigDecimal("0")) == 0) {
                            continue;
                        }
                        erpExamineBudgetDetailConsumeMapper.insertErpExamineBudgetDetailConsume(consume);
                    }

                    return Integer.parseInt(erpExamineApprove.getId().toString());
                }
            }
        }
        throw new ServiceException("暂无权限，请联系财务增加该类型下的部门");
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param erpExamineApprove 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateErpExamineApprove(ErpExamineApprove erpExamineApprove)
    {
        return erpExamineApproveMapper.updateErpExamineApprove(erpExamineApprove);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpExamineApproveByIds(Long[] ids)
    {
        return erpExamineApproveMapper.deleteErpExamineApproveByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpExamineApproveById(Long id)
    {
        return erpExamineApproveMapper.deleteErpExamineApproveById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int operate(ErpExamineApproveDTO erpExamineApproveDTO) {

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
//        SysUser sysUser = new SysUser();
//        sysUser.setUserId(1L);

        if (ObjectUtil.isNull(erpExamineApproveDTO.getId()) || ObjectUtil.isNull(erpExamineApproveDTO.getOperateStatus())) {
            throw new ServiceException("参数错误");
        }

        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(erpExamineApproveDTO.getId());

        if (ObjectUtil.isNull(erpExamineApprove)) {
            throw new ServiceException("审批数据错误");
        }

        String followInfoStr = erpExamineApprove.getFollowInfo();
        if (ObjectUtil.isNull(erpExamineApprove.getFollowInfo())) {
            throw new ServiceException("审批流为空");
        }
        JSONArray followInfoArr = JSONArray.fromObject(followInfoStr);
        if (ObjectUtil.isNull(followInfoArr) || followInfoArr.size() == 0) {
            throw new ServiceException("审批流为空");
        }

        if (erpExamineApprove.getApproveCurrentPoint() > followInfoArr.size()) {
            throw new ServiceException("审批流异常");
        }

        ErpExamineApproveTypeManage approveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType()+""));

        //记账服务流失审批更新流失类型
        if (approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS) {
            if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getRetentionFee())) {
                erpExamineAccountLsMapper.updateRetentionFeeById(erpExamineApproveDTO.getRetentionFee(), Long.parseLong(erpExamineApprove.getOtherId()));
            }
        }

        switch (erpExamineApproveDTO.getOperateStatus().intValue()) {

            //审批流通过
            case ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_PASS:
                //记账服务流失审批更新流失类型
                if (approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS) {
                    if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getAccountLossReason())) {
                        erpExamineAccountLsMapper.updateLossReasonById(erpExamineApproveDTO.getAccountLossReason(), Long.parseLong(erpExamineApprove.getOtherId()));
                    }
                }

                if (erpExamineApprove.getApproveStatus() != ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT) {
                    throw new ServiceException("当前审批状态非待审批");
                }

                if (erpExamineApprove.getApproveCurrentUser().intValue() != sysUser.getUserId().intValue()) {
                    throw new ServiceException("当前审批人非当前登陆人");
                }

                JSONObject currentObj = followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint());
                currentObj.put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS);
                currentObj.put("time", DateUtils.getTime());
                if(StringUtils.isNotEmpty(erpExamineApproveDTO.getApproveRemark())){
                    currentObj.put("approve_remark", erpExamineApproveDTO.getApproveRemark());
                }
                followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("remark", erpExamineApproveDTO.getRemark());
                //记录审批通过时间
                if (currentObj.containsKey("approveUserRole") && ObjectUtil.isNotEmpty(currentObj.get("approveUserRole"))
                        && currentObj.containsKey("recordTime") && ObjectUtil.isNotEmpty(currentObj.get("recordTime"))) {
                    int recordTime = currentObj.getInt("recordTime");
                    if (recordTime == 1) {
                        ErpExamineApproveDetail approveDetail = new ErpExamineApproveDetail();
                        approveDetail.setApproveId(erpExamineApproveDTO.getId());
                        approveDetail.setOperateTime(new Date());
                        approveDetail.setOperateType(1);
                        approveDetail.setOperateUserType(currentObj.getInt("approveUserRole"));
                        approveDetailMapper.insertErpExamineApproveDetail(approveDetail);
                    }
                }

                //更新抄送人
                if (currentObj.containsKey("makeCopyUser")) {
                    JSONArray makeCopyUserArr = currentObj.getJSONArray("makeCopyUser");
                    //判断抄送人
                    String makeCopyUser = ",";
                    if (ObjectUtil.isNotNull(erpExamineApprove.getMakeCopyUser())) {
                        makeCopyUser = erpExamineApprove.getMakeCopyUser();
                    }
                    for (int i = 0; i < makeCopyUserArr.size(); i++) {
                        if (!makeCopyUser.contains(","+makeCopyUserArr.get(i)+",")) {
                            makeCopyUser += makeCopyUserArr.get(i)+",";
                        }
                    }
                    erpExamineApprove.setMakeCopyUser(makeCopyUser);
                }
                //更新已审批过的人
                String passUser = ",";
                if (ObjectUtil.isNotNull(erpExamineApprove.getPassUser())) {
                    passUser = erpExamineApprove.getPassUser();
                }
                if (!passUser.contains(","+erpExamineApprove.getApproveCurrentUser()+",")) {
                    passUser += erpExamineApprove.getApproveCurrentUser()+",";
                }
                erpExamineApprove.setPassUser(passUser);
                //退款保存成本支出  更新财务确认支出
                if (approveTypeManage.getParentId() == 8L) {
                    addRefundCost(erpExamineApproveDTO, erpExamineApproveDTO.getOperateStatus().intValue());
                }

                //审批流完结
                if (erpExamineApprove.getApproveCurrentPoint()+1 == followInfoArr.size()) {
                    erpExamineApprove.setApproveStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS);
                    if (approveTypeManage.getNeedPay() == 1) {
                        erpExamineApprove.setPayStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PAY_STATUS_WAIT);
                    }
                    if (approveTypeManage.getNeedPrint() == 1) {
                        erpExamineApprove.setPrintStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PRINT_STATUS_WAIT);
                    }
                    erpExamineApprove.setApproveCurrentUser(-1);
                    erpExamineApprove.setFinishApproveEnd(new Date());
                    //操作业务逻辑
                    approveEndPass(erpExamineApprove);

                    R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser()+""), SecurityConstants.INNER);
                    //发送钉钉消息
                    String dingContent = "### 审批通过提醒： \n * " + "您好： \n "
                            +" * 审批类型: " + approveTypeManage.getName() + "    \n "
                            +" * 审批ID: " + erpExamineApprove.getId() + " 已通过审批\n"
                            + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/approveManagement/pendingApprove?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
                    DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批通过提醒", dingContent);
                    dingDingService.sendDingMessage(dingSendDTO);
                } else {
                    //更新当前节点，和当前审批人
                    Integer nextPoint = erpExamineApprove.getApproveCurrentPoint()+1;
                    JSONObject nextObj = new JSONObject();
                    for (int i = nextPoint; i < followInfoArr.size(); i++) {
                        JSONObject approveObj = followInfoArr.getJSONObject(i);
                        Integer approveUser = approveObj.getInt("approveUser");
                        R<SysUser> infoApprove = remoteUserService.getUserInfoById(Long.parseLong(approveUser+""), SecurityConstants.INNER);
                        if (Integer.parseInt(infoApprove.getData().getStatus()) == 1) {
                            approveObj.put("remark", "已离职");
                            approveObj.put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS);
                        }
                        if (Integer.parseInt(infoApprove.getData().getStatus()) == 0) {
                            nextObj = followInfoArr.getJSONObject(i);
                            nextPoint = i;
                            break;
                        }
                    }
                    if (ObjectUtil.isEmpty(nextObj)) {
                        throw new ServiceException("审批人错误");
                    }

                    int currentApproveUserId = nextObj.getInt("approveUser");
                    erpExamineApprove.setApproveCurrentPoint(nextPoint);
                    erpExamineApprove.setApproveCurrentUser(nextObj.getInt("approveUser"));


                    R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(currentApproveUserId+""), SecurityConstants.INNER);
                    //发送钉钉消息
                    String dingContent = "### 审批提醒： \n * " + "您好： \n "
                            +" * 审批类型: " + approveTypeManage.getName() + "    \n "
                            +" * 审批ID: " + erpExamineApprove.getId() + " 待审批\n"
                            + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/approveManagement/pendingApprove?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
                    DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批提醒", dingContent);
                    dingDingService.sendDingMessage(dingSendDTO);

                    //记录日志
                    ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.valueOf(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.APPROVAL_PASS.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
                    erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);
                }
                if (approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_KP) {
                    if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getAnnex())) {
                        erpOrderInvoiceMapper.updateAnnexById(erpExamineApproveDTO.getAnnex(), Long.parseLong(erpExamineApprove.getOtherId()));
                    }
                    if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getProductName())) {
                        erpOrderInvoiceMapper.updateProductNameById(erpExamineApproveDTO.getProductName(), Long.parseLong(erpExamineApprove.getOtherId()));
                    }
                    if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getProductCount())) {
                        erpOrderInvoiceMapper.updateProductCountById(erpExamineApproveDTO.getProductCount(), Long.parseLong(erpExamineApprove.getOtherId()));
                    }
                    if (ObjectUtil.isNotEmpty(erpExamineApproveDTO.getProductUnit())) {
                        erpOrderInvoiceMapper.updateProductUnitById(erpExamineApproveDTO.getProductUnit(), Long.parseLong(erpExamineApprove.getOtherId()));
                    }
                }
                break;
            case ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_REFUSE:
            case ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_SAVE:
                if (erpExamineApprove.getApproveStatus() != ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT) {
                    throw new ServiceException("当前审批状态非待审批");
                }

                if (erpExamineApprove.getApproveCurrentUser().intValue() != sysUser.getUserId().intValue()) {
                    throw new ServiceException("当前审批人非当前登陆人");
                }

                //审批流拒绝
                if (erpExamineApproveDTO.getOperateStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_REFUSE) {
                    followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_REFUSE);
                    followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("remark", erpExamineApproveDTO.getRemark());
                }
                //审批流挽回成功
                if (erpExamineApproveDTO.getOperateStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_SAVE) {
                    followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_SAVE);
                    followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("remark", erpExamineApproveDTO.getRemark());
                }
                followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("time", DateUtils.getTime());
                if(StringUtils.isNotEmpty(erpExamineApproveDTO.getApproveRemark())){
                    followInfoArr.getJSONObject(erpExamineApprove.getApproveCurrentPoint()).put("approve_remark", erpExamineApproveDTO.getApproveRemark());
                }

                //更新已审批过的人
                String passUserRefuse = ",";
                if (ObjectUtil.isNotNull(erpExamineApprove.getPassUser())) {
                    passUserRefuse = erpExamineApprove.getPassUser();
                }
                if (!passUserRefuse.contains(","+erpExamineApprove.getApproveCurrentUser()+",")) {
                    passUserRefuse += erpExamineApprove.getApproveCurrentUser()+",";
                }
                erpExamineApprove.setPassUser(passUserRefuse);
                erpExamineApprove.setApproveCurrentUser(-1);
                //审批流拒绝
                if (erpExamineApproveDTO.getOperateStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_REFUSE) {
                    erpExamineApprove.setApproveStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_REFUSE);
                }
                //审批流挽回成功
                if (erpExamineApproveDTO.getOperateStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_SAVE) {
                    erpExamineApprove.setApproveStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_SAVE);
                }
                erpExamineApprove.setRemark(erpExamineApproveDTO.getRemark());

                //操作业务逻辑
                approveRefuse(erpExamineApprove, erpExamineApproveDTO.getOperateStatus());

                //退款保存附件
                if (approveTypeManage.getParentId() == 8L) {
                    addRefundCost(erpExamineApproveDTO, erpExamineApproveDTO.getOperateStatus().intValue());
                }

                R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser()+""), SecurityConstants.INNER);
                //发送钉钉消息
                String dingContent = "### 审批驳回提醒： \n * " + "您好： \n "
                        +" * 审批类型: " + approveTypeManage.getName() + "    \n "
                        +" ### <font color=red>审批ID: " + erpExamineApprove.getId() + " 已被驳回"+ "</font>" +"\n"
                        + " * 链接：[" + crmUrl + "]" + "(" + crmUrl + "/approveManagement/pendingApprove?corpId=dingb0e251176d7f0c8a35c2f4657eb6378f" + ")";
                DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批驳回提醒", dingContent);
                dingDingService.sendDingMessage(dingSendDTO);
                break;
            case ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_CANCEL:
                if (erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_REFUSE
                || erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_CANCLE) {
                    throw new ServiceException("当前审批流已被驳回/撤销");
                }
                //操作业务逻辑
                int can = approveCancel(erpExamineApprove);
                //审批流可以撤销
                if (can == 1) {
                    erpExamineApprove.setApproveStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_CANCLE);
                    erpExamineApprove.setApproveCurrentUser(-1);
                }
                break;
            case ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_PRINT:
                //审批流打印
                if (erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS
                && erpExamineApprove.getPrintStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PRINT_STATUS_WAIT) {
                    erpExamineApprove.setPrintStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PRINT_STATUS_OVER);
                } else {
                    throw new ServiceException("当前审批流未审批通过/打印状态错误");
                }
                break;
            default:
                break;
        }

        erpExamineApprove.setUpdatedUser(sysUser.getUserId().intValue());
        erpExamineApprove.setUpdatedTime(new Date());
        erpExamineApprove.setFollowInfo(followInfoArr.toString());
        return erpExamineApproveMapper.updateErpExamineApprove(erpExamineApprove);
    }

    @Override
    public int listOperate(ErpExamineApproveDTO erpExamineApprove) {
        if (ObjectUtil.isEmpty(erpExamineApprove.getIdList()) || erpExamineApprove.getIdList().size() < 1) {
            throw new ServiceException("至少选择一条数据");
        }
        for (int i = 0; i < erpExamineApprove.getIdList().size(); i++) {
            erpExamineApprove.setId(erpExamineApprove.getIdList().get(i));
            operate(erpExamineApprove);
        }
        return 1;
    }

    @Override
    public int listPrint(ErpExamineApproveDTO dto) {
        if (ObjectUtil.isNotEmpty(dto.getIdList()) && dto.getIdList().size() > 0) {
            for (int i = 0; i < dto.getIdList().size(); i++) {
                ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(dto.getIdList().get(i));
                if (ObjectUtil.isNotEmpty(erpExamineApprove)) {
                    erpExamineApprove.setPrintStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PRINT_STATUS_OVER);
                    erpExamineApproveMapper.updateErpExamineApprove(erpExamineApprove);
                }
            }
        }
        return 1;
    }

    @DataScope(deptAlias = "sd", userAlias = "su")
    @Override
    public List<ErpExamineApproveVO> selectErpExamineApproveVOList(ErpExamineApproveQueryDTO erpExamineApproveQueryDTO) {
        List<ErpExamineApproveVO> erpExamineApproveVOS = erpExamineApproveMapper.selectErpExamineApproveVOList(erpExamineApproveQueryDTO);
        if (Objects.nonNull(erpExamineApproveQueryDTO.getType()) && 5 == erpExamineApproveQueryDTO.getType()) {
            if (Objects.isNull(erpExamineApproveQueryDTO.getPrintStatus())) {
                throw new SecurityException("打印状态不能为空");
            }
        }
        for (ErpExamineApproveVO erpExamineApproveVO : erpExamineApproveVOS) {
            //付款撤销（启照多）特殊处理
            if(Objects.nonNull(erpExamineApproveVO) && 3 == erpExamineApproveVO.getApproveType()){
                String otherId = erpExamineApproveVO.getOtherId();
                ErpExamineApproveQueryDTO erpExamineApproveQueryDTONew = new ErpExamineApproveQueryDTO();
                erpExamineApproveQueryDTONew.setId(Long.valueOf(otherId));
                List<ErpExamineApproveVO> erpExamineApproveVOSNew = erpExamineApproveMapper.selectErpExamineApproveVOList(erpExamineApproveQueryDTONew);
                if(CollectionUtils.isNotEmpty(erpExamineApproveVOSNew)){
                    ErpExamineApproveVO examineApproveVO = erpExamineApproveVOSNew.get(0);
                    erpExamineApproveVO.setOtherId(examineApproveVO.getOtherId());
                    erpExamineApproveVO.setIsPayment(examineApproveVO.getIsPayment());
                    erpExamineApproveVO.setCompanyName(examineApproveVO.getCompanyName());
                    erpExamineApproveVO.setType(examineApproveVO.getType());
                    erpExamineApproveVO.setSupplementStatus(examineApproveVO.getSupplementStatus());
                    erpExamineApproveVO.setSupplementStatusName(examineApproveVO.getSupplementStatusName());
                }
            }
            if (Objects.nonNull(erpExamineApproveVO.getCreatedUser())) {
                R<SysUser> info = remoteUserService.getUserInfoById(erpExamineApproveVO.getCreatedUser().longValue(), SecurityConstants.INNER);
                erpExamineApproveVO.setCreatedUserName(Objects.nonNull(info.getData()) ? info.getData().getNickName() : null);
            }
            if (Objects.nonNull(erpExamineApproveVO.getApproveCurrentUser())) {
                R<SysUser> info = remoteUserService.getUserInfoById(erpExamineApproveVO.getApproveCurrentUser().longValue(), SecurityConstants.INNER);
                erpExamineApproveVO.setApproveCurrentUserName(Objects.nonNull(info.getData()) ? info.getData().getNickName() : null);
            }
        }
        return erpExamineApproveVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int markPayment(ErpQzdPaymentRecordDTO erpQzdPaymentRecordDTO) {
        //校验是否存在该otherId待审批的撤销审批流
        ErpExamineApprove approve = erpExamineApproveMapper.selectErpExamineApproveById(erpQzdPaymentRecordDTO.getId());
        if (ObjectUtil.isNull(approve)) {
            throw new ServiceException("审批ID有误。");
        }
        if (approve.getApproveStatus() != ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
            throw new ServiceException("该审批单非审批通过状态");
        }
        if (ObjectUtil.isNotEmpty(approve.getRevokeStatus()) && approve.getRevokeStatus() != ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_REVOKE_STATUS_REFUSE) {
            throw new ServiceException("该审批单已发起撤销， 请先处理审批。");
        }

        BigDecimal sum = BigDecimal.ZERO;
        //获取之前的付款总额
        ErpQzdPaymentRecord erpQzdPaymentRecord = new ErpQzdPaymentRecord();
        erpQzdPaymentRecord.setApprovalServiceId(erpQzdPaymentRecordDTO.getOtherId());
        erpQzdPaymentRecord.setApproveType(erpQzdPaymentRecordDTO.getApproveType());
        List<ErpQzdPaymentRecord> erpQzdPaymentRecords = erpQzdPaymentRecordMapper.selectErpQzdPaymentRecordList(erpQzdPaymentRecord);
        BigDecimal reduce = erpQzdPaymentRecords.stream().map(item -> item.getPaymentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        sum = sum.add(reduce);

        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(erpQzdPaymentRecordDTO.getApproveType().longValue());

        if (ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_PAY == erpExamineApproveTypeManage.getParentId()) {
            //校验付款金额
            ErpQzdApprovalService erpQzdApprovalService = erpQzdApprovalServiceMapper.selectErpQzdApprovalServiceById(erpQzdPaymentRecordDTO.getOtherId());
            BigDecimal money = erpQzdApprovalService.getMoney();
            BigDecimal subtract = money.subtract(sum);
            if (erpQzdPaymentRecordDTO.getPaymentAmount().compareTo(subtract) > 0 || erpQzdPaymentRecordDTO.getPaymentAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("不得大于申请付款金额");
            }
            //更新付款状态
            if ((Objects.isNull(erpQzdApprovalService.getIsPayment()) || (Objects.nonNull(erpQzdApprovalService.getIsPayment()) && 0 == erpQzdApprovalService.getIsPayment()))) {
                ErpQzdApprovalService erpQzdApprovalServiceUpdate = new ErpQzdApprovalService();
                erpQzdApprovalServiceUpdate.setId(erpQzdPaymentRecordDTO.getOtherId());
                erpQzdApprovalServiceUpdate.setIsPayment(1);
                int result = erpQzdApprovalServiceMapper.updateErpQzdApprovalService(erpQzdApprovalServiceUpdate);
//                return result;
            }
        }
        if (ErpExamineApproveConstants.APPROVE_CATEGORY_ADVANCE == erpExamineApproveTypeManage.getParentId()) {
            ErpBizServiceAdvance erpBizServiceAdvance = erpBizServiceAdvanceMapper.selectErpBizServiceAdvanceById(Integer.parseInt(erpQzdPaymentRecordDTO.getOtherId().toString()));
            BigDecimal money = erpBizServiceAdvance.getPay();
            BigDecimal subtract = money.subtract(sum);
            if (erpQzdPaymentRecordDTO.getPaymentAmount().compareTo(subtract) > 0 || erpQzdPaymentRecordDTO.getPaymentAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("不得大于申请付款金额");
            }
        }
        if (approve.getPayStatus() != 0) {
            ErpQzdPaymentRecord erpQzdPaymentRecordAdd = new ErpQzdPaymentRecord();
            BeanUtils.copyProperties(erpQzdPaymentRecordDTO, erpQzdPaymentRecordAdd);
            erpQzdPaymentRecordAdd.setApprovalServiceId(erpQzdPaymentRecordDTO.getOtherId());
            erpQzdPaymentRecordAdd.setCreateUser(SecurityUtils.getUserId());
            erpQzdPaymentRecordAdd.setUpdateUser(SecurityUtils.getUserId());
            int i = erpQzdPaymentRecordMapper.insertErpQzdPaymentRecord(erpQzdPaymentRecordAdd);

            if (approve.getPayStatus() == 1) {
                approve.setPayStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_PAY_STATUS_OVER);
                erpExamineApproveMapper.updateErpExamineApprove(approve);
            }
            return i;
        }
        return 0;
    }

    @Override
    public BigDecimal markPaymentAmount(Long otherId, int approveType) {
        BigDecimal sum = BigDecimal.ZERO;
        //获取之前的付款总额
        ErpQzdPaymentRecord erpQzdPaymentRecord = new ErpQzdPaymentRecord();
        erpQzdPaymentRecord.setApprovalServiceId(otherId);
        erpQzdPaymentRecord.setApproveType(approveType);
        List<ErpQzdPaymentRecord> erpQzdPaymentRecords = erpQzdPaymentRecordMapper.selectErpQzdPaymentRecordList(erpQzdPaymentRecord);
        BigDecimal reduce = erpQzdPaymentRecords.stream().map(item -> item.getPaymentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        sum = sum.add(reduce);
        return sum;
    }

    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpExamineApproveListVO> budgetList(ErpExamineBudgetDto dto) {

        List<ErpExamineApproveListVO> list = erpExamineApproveMapper.budgetList(dto);

        List<Integer> userIdList = new ArrayList<>();
        //组合参数
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (ObjectUtils.isEmpty(vo.getParentId())) {
                vo.setParentId(0L);
            }

            vo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(vo.getApproveStatus().toString()));
            if (!userIdList.contains(vo.getCreatedUser())) {
                userIdList.add(vo.getCreatedUser());
            }
            if (!userIdList.contains(vo.getApproveCurrentUser())) {
                userIdList.add(vo.getApproveCurrentUser());
            }
        }

        if (userIdList.size() > 0) {
            R<List<SysUser>> userListR = remoteUserService.getUserListByIds(StringUtils.join(userIdList,","), SecurityConstants.INNER);
            if (Objects.nonNull(userListR) && userListR.getCode() == 200) {
                List<SysUser> userList = userListR.getData();
                for (int i = 0; i < list.size(); i++) {
                    ErpExamineApproveListVO vo = list.get(i);
                    for (int j = 0; j < userList.size(); j++) {
                        SysUser sysUser_ = userList.get(j);
                        if (ObjectUtil.isNotEmpty(vo.getCreatedUser()) && sysUser_.getUserId().intValue() == vo.getCreatedUser().intValue()) {
                            vo.setCreatedUserName(sysUser_.getNickName());
                        }
                        if (ObjectUtil.isNotEmpty(vo.getApproveCurrentUser()) && sysUser_.getUserId().intValue() == vo.getApproveCurrentUser().intValue()) {
                            vo.setApproveCurrentUserName(sysUser_.getNickName());
                        }
                    }
                }
            }
        }

        return list;
    }

    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpExamineApproveListVO> approveList(ErpExamineApproveDTO erpExamineApproveDTO) {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if (ObjectUtil.isNotEmpty(sysUser.getUserId()) && sysUser.getUserId().intValue() != 1) {
            erpExamineApproveDTO.setLoginUserId(sysUser.getUserId());
        }

        List<ErpExamineApproveListVO> list = erpExamineApproveMapper.selectListPublic(erpExamineApproveDTO);

        List<Integer> userIdList = new ArrayList<>();
        //成本
        List<Integer> costSettlementIdList = new ArrayList<>();;
        //预付单
        List<Integer> advanceIdList = new ArrayList<>();
        //撤销审批otherId
        List<Long> cancelApproveOtherIdList = new ArrayList<>();
        //撤销审批otherId
        List<Integer> qzdOtherIdList = new ArrayList<>();
        //退款审批otherId
        List<Long> refundIdList = new ArrayList<>();
        //采购合同otherId
        List<Long> procureContractIdList = new ArrayList<>();
        //订单开票otherId
        List<Long> orderKpIdList = new ArrayList<>();
        //记账流失otherId
        List<Long> jzLsIdList = new ArrayList<>();
        //无订单退款：收款凭证otherId
        List<Long> refundVoucherIdList = new ArrayList<>();
        //新委托代征otherId
        List<Long> wtdzIdList = new ArrayList<>();
        //组合参数
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (ObjectUtils.isEmpty(vo.getParentId())) {
                vo.setParentId(0L);
            }
            if (ObjectUtil.isNotEmpty(vo.getFollowInfo())) {
                JSONArray arr_ = JSONArray.fromObject(vo.getFollowInfo());
                for (int j = 0; j < arr_.size(); j++) {
                    JSONObject obj = arr_.getJSONObject(j);
                    Integer approveUser = obj.getInt("approveUser");
                    if (ObjectUtil.isNotEmpty(approveUser) && !userIdList.contains(approveUser)) {
                        userIdList.add(obj.getInt("approveUser"));
                    }
                }
            }


            if (ObjectUtil.isNotEmpty(vo.getRevokeStatus())){
                vo.setRevokeStatusName(ErpExamineApproveConstants.ERP_EXAMINE_REVOKE_STATUS.get(vo.getRevokeStatus().toString()));
            }
            vo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(vo.getApproveStatus().toString()));
            vo.setPayStatusName(ErpExamineApproveConstants.PAY_STATUS_MAP.get(vo.getPayStatus().toString()));
            if (!userIdList.contains(vo.getCreatedUser())) {
                userIdList.add(vo.getCreatedUser());
            }
            if (!userIdList.contains(vo.getApproveCurrentUser())) {
                userIdList.add(vo.getApproveCurrentUser());
            }

            //成本审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_CHENGBEN) {
                if (!costSettlementIdList.contains(Integer.valueOf(vo.getOtherId()))) {
                    costSettlementIdList.add(Integer.parseInt(vo.getOtherId()));
                }
            }
            //预付单审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ADVANCE) {
                if (!advanceIdList.contains(Integer.valueOf(vo.getOtherId()))) {
                    advanceIdList.add(Integer.parseInt(vo.getOtherId()));
                }
            }
            //产品审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_PRODUCT) {
                vo.setProductId(Long.parseLong(vo.getOtherId()));
            }
            //撤销审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_CANCEL) {
                if (!cancelApproveOtherIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    cancelApproveOtherIdList.add(Long.parseLong(vo.getOtherId()));
                }
            }
            //启照多审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_PAY
                    || vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_REFUND
                    || vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_SUPPLEMENT) {
                if (!qzdOtherIdList.contains(Integer.valueOf(vo.getOtherId()))) {
                    qzdOtherIdList.add(Integer.parseInt(vo.getOtherId()));
                }
            }
            //退款审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REFUND){
                if (!refundIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    refundIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
            //退款审批
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_PROCURE_CONTRACT){
                if (!procureContractIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    procureContractIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
            //订单开票
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_KP){
                if (!orderKpIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    orderKpIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
            //记账流失
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS){
                if (!jzLsIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    jzLsIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
            //无订单退款
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_NO_ORDER_REFUND){
                if (!refundVoucherIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    refundVoucherIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
            //委托代征
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_DLS_JS){
                if (!wtdzIdList.contains(Long.valueOf(vo.getOtherId()))) {
                    wtdzIdList.add(Long.valueOf(vo.getOtherId()));
                }
            }
        }

        List<SysDept> deptList = erpExamineApproveMapper.selectFirstDeptId();

        if (userIdList.size() > 0) {
            R<List<SysUser>> userListR = remoteUserService.getUserListByIds(StringUtils.join(userIdList,","), SecurityConstants.INNER);
            List<SysUser> userList = userListR.getData();
            for (int i = 0; i < list.size(); i++) {
                ErpExamineApproveListVO vo = list.get(i);
                for (int j = 0; j < userList.size(); j++) {
                    SysUser sysUser_ = userList.get(j);
                    if (ObjectUtil.isNotEmpty(vo.getCreatedUser()) && sysUser_.getUserId().intValue() == vo.getCreatedUser().intValue()) {
                        vo.setCreatedUserName(sysUser_.getNickName());
                        vo.setAncestors(sysUser_.getDept().getAncestors());
                        vo.setCreatedUserDeptName(sysUser_.getDept().getDeptName());
                        vo.setDeptId(sysUser_.getDeptId());
                    }
                    if (ObjectUtil.isNotEmpty(vo.getApproveCurrentUser()) && sysUser_.getUserId().intValue() == vo.getApproveCurrentUser().intValue()) {
                        vo.setApproveCurrentUserName(sysUser_.getNickName());
                    }
                }


                if (ObjectUtil.isNotEmpty(vo.getFollowInfo())) {
                    JSONArray arr_ = JSONArray.fromObject(vo.getFollowInfo());
                    String approveUserRemark = "";
                    for (int z = 0; z < arr_.size(); z++) {
                        JSONObject obj = arr_.getJSONObject(z);
                        if (obj.containsKey("remark") && ObjectUtil.isNotEmpty(obj.getString("remark"))) {
                            for (int j = 0; j < userList.size(); j++) {
                                SysUser sysUser_ = userList.get(j);
                                if (ObjectUtil.isNotEmpty(obj.getInt("approveUser")) && sysUser_.getUserId().intValue() == obj.getInt("approveUser")) {
                                    approveUserRemark += "//" + sysUser_.getNickName() + ":" + obj.getString("remark");
                                    break;
                                }
                            }
                        }
                    }
                    vo.setApproveUserRemark(approveUserRemark);
                }
            }
        }
        JSONObject firstDeptObj = new JSONObject();
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (!firstDeptObj.containsKey(vo.getDeptId().toString())) {
                List<Long> parentDeptList = Arrays.asList(vo.getAncestors().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                for (int j = 0; j < deptList.size(); j++) {
                    SysDept sysDept = deptList.get(j);
                    if (parentDeptList.contains(sysDept.getDeptId())) {
                        firstDeptObj.put(vo.getDeptId().toString(), sysDept.getDeptName());
                        break;
                    }
                }
            }
            vo.setFirstDeptName(firstDeptObj.containsKey(vo.getDeptId().toString()) ? firstDeptObj.getString(vo.getDeptId().toString()) : "");
        }

        if (cancelApproveOtherIdList.size() > 0) {
            List<ErpExamineApproveListVO> otherList = erpExamineApproveMapper.getApproveByIds(cancelApproveOtherIdList);
            for (int i = 0; i < list.size(); i++) {
                ErpExamineApproveListVO vo = list.get(i);
                for (int j = 0; j < otherList.size(); j++) {
                    ErpExamineApproveListVO otherVo = otherList.get(j);
                    if (ObjectUtil.isNotEmpty(vo.getOtherId()) && otherVo.getId().intValue() == Integer.parseInt(vo.getOtherId())) {
                        vo.setApproveTypeName(vo.getApproveTypeName() + "(" +otherVo.getApproveTypeName() + ")");
                    }
                }
            }
        }



        if (wtdzIdList.size() > 0) {
            combinationWtdz(list, wtdzIdList);
        }
        //组合成本审批数据
        if (costSettlementIdList.size() > 0) {
            combinationCostSettlement(list, costSettlementIdList);
        }
        //预付款审批数据
        if (advanceIdList.size() > 0) {
            combinationAdvance(list, advanceIdList);
        }
        //预付款审批数据
        if (qzdOtherIdList.size() > 0) {
            combinationQzd(list, qzdOtherIdList);
        }
        //退款审批
        if (CollUtil.isNotEmpty(refundIdList)){
            combinationRefund(list, refundIdList);
        }
        //采购合同审批
        if (CollUtil.isNotEmpty(procureContractIdList)){
            combinationProcureContract(list, procureContractIdList);
        }
        if (CollUtil.isNotEmpty(orderKpIdList)){
            combinationOrderKp(list, orderKpIdList);
        }
        if (CollUtil.isNotEmpty(jzLsIdList)){
            combinationJzLs(list, jzLsIdList);
        }
        if (CollUtil.isNotEmpty(refundVoucherIdList)){
            combinationRefoundNoOrder(list, refundVoucherIdList);
        }

        return list;
    }

    public void combinationWtdz(List<ErpExamineApproveListVO> list, List<Long> wtdzIdList) {
        List<ErpExamineOtherOrderPay> otherOrderPayList = erpExamineOtherOrderPayMapper.selectListByIdList(wtdzIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_DLS_JS) {
                for (int j = 0; j < otherOrderPayList.size(); j++) {
                    ErpExamineOtherOrderPay otherOrderPay = otherOrderPayList.get(j);
                    if (vo.getOtherId().equals(otherOrderPay.getId().toString())) {
                        vo.setAgentNumber(otherOrderPay.getAgentNumber());
                    }
                }
            }
        }
    }

    public void combinationRefoundNoOrder(List<ErpExamineApproveListVO> list, List<Long> refundVoucherIdList) {
        List<ErpOrderPayRecord> commonPaymentList = erpExamineApproveCommonPaymentMapper.selectListByIds(refundVoucherIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_NO_ORDER_REFUND) {
                for (int j = 0; j < commonPaymentList.size(); j++) {
                    ErpOrderPayRecord commonPayment = commonPaymentList.get(j);
                    if (vo.getOtherId().equals(commonPayment.getId().toString())) {
                        vo.setRefundPayFee(commonPayment.getFee());
                        vo.setRefundPayDate(commonPayment.getPayTime());
                        vo.setRefundReason(commonPayment.getPayReason());
                        vo.setBillNo(commonPayment.getBillNo());
                        vo.setTradeId(commonPayment.getTradeId());
                        vo.setDocumentNumber(commonPayment.getDocumentNumber());
                    }
                }
            }
        }
    }

    public void combinationJzLs(List<ErpExamineApproveListVO> list, List<Long> jzLsIdList) {
        List<ErpExamineAccountLsVo> accountLsVoList = erpExamineAccountLsMapper.selectListByIds(jzLsIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS) {
                for (int j = 0; j < accountLsVoList.size(); j++) {
                    ErpExamineAccountLsVo accountLsVo = accountLsVoList.get(j);
                    if (vo.getOtherId().equals(accountLsVo.getId().toString())) {
                        vo.setEnterpriseNames(accountLsVo.getCompanyName());
                        vo.setNumEnterpriseId(accountLsVo.getNumEnterpriseId());
                        vo.setLossReasonName(accountLsVo.getLossReasonName());
                        vo.setSecondLossReason(accountLsVo.getSecondLossReason());
                        vo.setFirstLossReason(accountLsVo.getFirstLossReason());
                        vo.setRetentionFee(accountLsVo.getRetentionFee());
                        vo.setAccountLsDate(accountLsVo.getLsDate());
                    }
                }
            }
        }
    }
    /***
     * 审批列表组合采购合同审批数据
     */
    public void combinationOrderKp(List<ErpExamineApproveListVO> list, List<Long> orderKpIdList) {

        List<ErpOrderInvoice> orderInvoiceList = erpOrderInvoiceMapper.selectListByIds(orderKpIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_KP) {
                for (int j = 0; j < orderInvoiceList.size(); j++) {
                    ErpOrderInvoice orderInvoice = orderInvoiceList.get(j);
                    if (vo.getOtherId().equals(orderInvoice.getId().toString())) {
                        vo.setEnterpriseNames(orderInvoice.getInvoiceHeaderName());
                    }
                }
            }
        }
    }

    /***
     * 审批列表组合采购合同审批数据
     */
    public void combinationProcureContract(List<ErpExamineApproveListVO> list, List<Long> procureContractIdList) {

        List<ErpProcureContractVo> erpProcureContractList = erpProcureContractMapper.selectListByIds(procureContractIdList);
        for (int i = 0; i < erpProcureContractList.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_PROCURE_CONTRACT) {
                for (int j = 0; j < erpProcureContractList.size(); j++) {
                    ErpProcureContractVo erpProcureContractVo = erpProcureContractList.get(j);
                    if (vo.getOtherId().equals(erpProcureContractVo.getId().toString())) {
                        vo.setProcureContractNumber(erpProcureContractVo.getContractNumber());
                        vo.setAgentNumber(erpProcureContractVo.getAgentNumber());
                    }
                }
            }
        }
    }

    private void combinationRefund(List<ErpExamineApproveListVO> list, List<Long> refundIdList) {
        List<ErpOrderRefund> erpOrderRefunds = erpOrderRefundMapper.selectErpOrderRefundByIdList(refundIdList);
        //查询订单
        List<Long> orderIdList = erpOrderRefunds.stream().map(ErpOrderRefund::getOrderId).collect(Collectors.toList());
        List<ErpOrders> erpOrders = erpOrdersMapper.selectErpOrdersByIdList(orderIdList);
        for (ErpExamineApproveListVO vo : list) {

            ErpOrderRefund erpOrderRefund = erpOrderRefunds.stream()
                    .filter(en -> en.getId().equals(Long.valueOf(vo.getOtherId())))
                    .findAny().orElse(null);
            if (ObjectUtil.isNotEmpty(erpOrderRefund)){
                ErpOrders order = erpOrders.stream()
                        .filter(en -> en.getId().equals(erpOrderRefund.getOrderId()))
                        .findAny().orElse(null);
                if (ObjectUtil.isNotEmpty(order)) {
                    vo.setOrderId(order.getId());
                    vo.setEnterpriseNames(order.getCompanyName());
                    vo.setOrderNumbers(order.getVcOrderNumber());
                }
                vo.setRefundFee(erpOrderRefund.getRefundAmount());
                vo.setRefundPayFee(erpOrderRefund.getRefundPayFee());
                vo.setRefundPayDate(erpOrderRefund.getRefundPayDate());

                if (erpOrderRefund.getRefundReasonSelect() == 1) {
                    vo.setRefundReason("前端原因" + "("+erpOrderRefund.getRefundReason()+")");
                } else if (erpOrderRefund.getRefundReasonSelect() == 2) {
                    vo.setRefundReason("外部原因" + "("+erpOrderRefund.getRefundReason()+")");
                } else if (erpOrderRefund.getRefundReasonSelect() == 3) {
                    vo.setRefundReason("后端原因" + "("+erpOrderRefund.getRefundReason()+")");
                } else {
                    vo.setRefundReason("其它" + "("+erpOrderRefund.getRefundReason()+")");
                }
            }
        }
    }

    /***
     * 审批列表组合成本审批数据
     */
    public void combinationQzd(List<ErpExamineApproveListVO> list, List<Integer> qzdOtherIdList) {

        List<ErpQzdApprovalServiceVO> erpQzdApprovalServiceVOList = erpQzdApprovalServiceMapper.getListByIds(qzdOtherIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_PAY
                    || vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_REFUND
                    || vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_SUPPLEMENT) {
                for (int j = 0; j < erpQzdApprovalServiceVOList.size(); j++) {
                    ErpQzdApprovalServiceVO erpQzdApprovalServiceVO = erpQzdApprovalServiceVOList.get(j);
                    if (vo.getOtherId().equals(erpQzdApprovalServiceVO.getId().toString())) {
                        vo.setFee(erpQzdApprovalServiceVO.getMoney());
                        vo.setEnterpriseNames(erpQzdApprovalServiceVO.getCompanyName());
                        vo.setLicenseId(erpQzdApprovalServiceVO.getLicenseId());
                        vo.setLicenseNumber(erpQzdApprovalServiceVO.getLicenseNumber());
                        vo.setIsFirstApply(erpQzdApprovalServiceVO.getIsFirstApply());
                        vo.setSupplementStatus(erpQzdApprovalServiceVO.getSupplementStatus());
                        vo.setQzdApproveParentId(erpQzdApprovalServiceVO.getQzdApproveParentId());
                    }
                }
            }
        }
    }

    /***
     * 审批列表组合成本审批数据
     */
    public void combinationAdvance(List<ErpExamineApproveListVO> list, List<Integer> advanceIdList) {

        List<ErpBizServiceAdvanceDto> erpBizServiceAdvanceList = erpBizServiceAdvanceMapper.selectListByIds(advanceIdList);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_ADVANCE) {
                for (int j = 0; j < erpBizServiceAdvanceList.size(); j++) {
                    ErpBizServiceAdvanceDto dto = erpBizServiceAdvanceList.get(j);
                    if (vo.getOtherId().equals(dto.getId().toString())) {
                        vo.setAdvanceNumber(dto.getNumber());
                        vo.setAgentNumber(dto.getAgentName());
                        vo.setFee(dto.getPay());
                        vo.setAdvanceHxStatusName(ErpExamineApproveConstants.ADVANCE_HX_STATUS.get(dto.getHxStatus().toString()));
                    }
                }
            }
        }
    }

    /***
     * 审批列表组合成本审批数据
     */
    public void combinationCostSettlement(List<ErpExamineApproveListVO> list, List<Integer> costSettlementIdList) {
        List<String> enterpriseIdList = new ArrayList<>();
        List<Long> registerIdList = new ArrayList<>();
        List<CostSettleManageDetailVo> costSettleManageDetailVoList = costSettlementMapper.selectDetailByIds(costSettlementIdList);
        for (int i = 0; i < costSettleManageDetailVoList.size(); i++) {
            CostSettleManageDetailVo vo = costSettleManageDetailVoList.get(i);
            if (!ObjectUtil.isEmpty(vo.getEnterpriseIds())) {
                List<String> enterpriseIds = Arrays.asList(vo.getEnterpriseIds().split(","));
                for (int j = 0; j < enterpriseIds.size(); j++) {
                    if (!enterpriseIdList.contains(enterpriseIds.get(j))) {
                        enterpriseIdList.add(enterpriseIds.get(j));
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(vo.getRegisterIds())) {
                List<Long> registerIds = Arrays.asList(vo.getRegisterIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());;
                for (int j = 0; j < registerIds.size(); j++) {
                    if (!registerIdList.contains(registerIds.get(j))) {
                        registerIdList.add(registerIds.get(j));
                    }
                }
                for(int j = 0; j < list.size(); j++) {
                    ErpExamineApproveListVO approveVo = list.get(j);
                    if (vo.getId().intValue() == Integer.parseInt(approveVo.getOtherId())) {
                        approveVo.setRegisterIds(registerIds);
                    }
                }
            }
        }

        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveListVO vo = list.get(i);
            if (vo.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_CHENGBEN) {
                for (int j = 0; j < costSettleManageDetailVoList.size(); j++) {
                    CostSettleManageDetailVo detailVo = costSettleManageDetailVoList.get(j);
                    if (vo.getOtherId().equals(detailVo.getId().toString())) {
                        vo.setAdvanceNumber(detailVo.getAdvanceNumber());
                        vo.setOrderNumbers(detailVo.getOrderNumbers());
                        vo.setAgentId(detailVo.getAgentId());
                        vo.setAgentNumber(detailVo.getAgentNumber());
                        vo.setEnterpriseIds(detailVo.getEnterpriseIds());
                        vo.setFee(detailVo.getSumCostPrice());
                        vo.setNumberOfTimes(detailVo.getNumberOfTimes());
                        vo.setIsAdvance(detailVo.getIsAdvance());
                        vo.setDetailAddress(detailVo.getDetailAddress());
                        vo.setIsAdvanceName(ErpExamineApproveConstants.ERP_APPROVE_IS_ADVANCE_TYPE.get(detailVo.getIsAdvance().toString()));
                        vo.setAdvanceHxStatusName(ErpExamineApproveConstants.ADVANCE_HX_STATUS.get(detailVo.getHxStatus()+""));

                        if (ObjectUtil.isNotEmpty(detailVo.getSumCostPrice()) && ObjectUtils.isNotEmpty(costSettleManageDetailVoList.get(j).getRegisterIds())) {
                            List<Long> ssmIds = Arrays.stream(costSettleManageDetailVoList.get(j).getRegisterIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                            BigDecimal sumFee = detailVo.getSumCostPrice().multiply(new BigDecimal(ssmIds.size()+""));
                            sumFee = sumFee.multiply(new BigDecimal(vo.getNumberOfTimes()+""));
                            vo.setFee(sumFee);
                        }
                    }
                }
            }
        }

        if (enterpriseIdList.size() > 0) {
            List<ErpEnterprise> enterpriseList = erpEnterpriseMapper.selectErpEnterpriseListByIds(enterpriseIdList);
            for (int i = 0; i < list.size(); i++) {
                ErpExamineApproveListVO vo = list.get(i);
                if (ObjectUtils.isNotEmpty(vo.getEnterpriseIds())) {
                    List<String> enterpriseIdListVo = Arrays.asList(vo.getEnterpriseIds().split(","));
                    String enterpriseNames = "";
                    for (int j = 0; j < enterpriseList.size(); j++) {
                        ErpEnterprise enterprise = enterpriseList.get(j);
                        if (enterpriseIdListVo.contains(enterprise.getId().toString()) && !enterpriseNames.contains(enterprise.getVcCompanyName())) {
                            enterpriseNames += enterprise.getVcCompanyName();
                        }
                    }
                    vo.setEnterpriseNames(enterpriseNames);
                }
            }
        }

        if (registerIdList.size() > 0) {
            List<Map<String, Object>> mapList = costSettlementMapper.getProductInfoBySmIds(registerIdList);
            for (int j = 0; j < list.size(); j++) {
                ErpExamineApproveListVO vo = list.get(j);
                for (int i = 0; i < mapList.size(); i++) {
                    Map<String, Object> map = mapList.get(i);
                    if (map.containsKey("id") && ObjectUtil.isNotEmpty(map.get("id"))) {
                        String id = map.get("id").toString();
                        if (ObjectUtil.isNotEmpty(vo.getRegisterIds()) && vo.getRegisterIds().contains(Long.parseLong(id))) {
                            if (map.containsKey("productId") && ObjectUtil.isNotEmpty(map.get("productId"))) {
                                vo.setProductId(Long.parseLong(map.get("productId").toString()));
                            }
                            if (map.containsKey("serviceTypeName") && ObjectUtil.isNotEmpty(map.get("serviceTypeName"))) {
                                vo.setServiceTypeName(map.get("serviceTypeName").toString());
                            }
                            if (map.containsKey("productServiceName") && ObjectUtil.isNotEmpty(map.get("productServiceName"))) {
                                vo.setProductServiceName(map.get("productServiceName").toString());
                            }

                            if (map.containsKey("nickName") && ObjectUtil.isNotEmpty(map.get("nickName"))) {
                                vo.setNickName(map.get("nickName").toString());
                            }
                            if (map.containsKey("deptName") && ObjectUtil.isNotEmpty(map.get("deptName"))) {
                                vo.setDeptName(map.get("deptName").toString());
                            }
                            if (map.containsKey("regionName") && ObjectUtil.isNotEmpty(map.get("regionName"))) {
                                vo.setRegionName(map.get("regionName").toString());
                            }
                            if (map.containsKey("legalPersonName") && ObjectUtil.isNotEmpty(map.get("legalPersonName"))) {
                                vo.setLegalPersonName(map.get("legalPersonName").toString());
                            }
                            if (map.containsKey("totalPrice") && ObjectUtil.isNotEmpty(map.get("totalPrice"))) {
                                vo.setTotalPrice(new BigDecimal(map.get("totalPrice").toString()));
                            }

                            if (map.containsKey("datSigningDate") && ObjectUtil.isNotEmpty(map.get("datSigningDate"))) {
                                vo.setSignDate(map.get("datSigningDate").toString());
                            }
                        }
                    }
                }
            }

        }
    }

    @Override
    public ErpCostSettlementApproveVO costSettlementInfo(Long id) {
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(id);
        ErpCostSettlementApproveVO vo = new ErpCostSettlementApproveVO();
        BeanUtils.copyProperties(erpExamineApprove, vo);
        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType().toString()));
        vo.setApproveTypeName(erpExamineApproveTypeManage.getName());

        int costSettleId = Integer.parseInt(vo.getOtherId());
//        if (erpExamineApprove.getApproveType().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_TYPE_7) {
//            costSettleId = Integer.parseInt(vo.getOtherId());
//        } else {
//            ErpExamineApprove erpExamineApprove_ =  erpExamineApproveMapper.selectErpExamineApproveById(Long.parseLong(erpExamineApprove.getOtherId()));
//            costSettleId = Integer.parseInt(erpExamineApprove_.getOtherId());
//        }
        List<Integer> costSettleIds = new ArrayList<>();
        costSettleIds.add(costSettleId);
        List<CostSettleManageDetailVo> costSettleManageDetailVoList = costSettlementMapper.selectDetailByIds(costSettleIds);

        if (costSettleManageDetailVoList.size() != 1) {
            throw new ServiceException("数据错误");
        }
        //付款记录
        /*CostSettlementPayRecord costSettlementPayRecord = new CostSettlementPayRecord();
        costSettlementPayRecord.setCostSettlementId(Long.parseLong(costSettleId+""));
        List<CostSettlementPayRecord> payRecordList = costSettlementPayRecordMapper.selectCostSettlementPayRecordList(costSettlementPayRecord);*/
        try {
            ErpQzdPaymentRecord erpQzdPaymentRecord = new ErpQzdPaymentRecord();
            erpQzdPaymentRecord.setApprovalServiceId(id);
            erpQzdPaymentRecord.setApproveType(erpExamineApprove.getApproveType());
            List<ErpQzdPaymentRecord> erpQzdPaymentRecords = erpQzdPaymentRecordMapper.selectErpQzdPaymentRecordList(erpQzdPaymentRecord);
            vo.setPaymentRecordList(erpQzdPaymentRecords);
        } catch (Exception e) {
            log.error("成本类申请获取付款记录信息异常，异常信息为：", e);
        }

        BigDecimal fee = new BigDecimal("0");
        BigDecimal sumFee = new BigDecimal("0");
        if (costSettleManageDetailVoList.size() > 0) {
            fee = costSettleManageDetailVoList.get(0).getSumCostPrice();
            List<Long> ssmIds = Arrays.stream(costSettleManageDetailVoList.get(0).getRegisterIds().split(",")).map(Long::parseLong).collect(Collectors.toList());

            List<ErpApproveServiceMainVO> orderList = costSettlementMapper.getOrderDetailBySmIds(ssmIds);
            for (int i = 0; i < orderList.size(); i++) {
                orderList.get(i).setCostPrice(fee);
                orderList.get(i).setNumberOfTimes(costSettleManageDetailVoList.get(0).getNumberOfTimes());
            }
            vo.setOrderList(orderList);
            //vo.setPayList(payRecordList);

            sumFee = fee.multiply(new BigDecimal(ssmIds.size()+"")).multiply(new BigDecimal(costSettleManageDetailVoList.get(0).getNumberOfTimes()+""));
            vo.setCostFee(sumFee);
            vo.setDetailAddress(costSettleManageDetailVoList.get(0).getDetailAddress());
            vo.setAddressType(costSettleManageDetailVoList.get(0).getAddressType());
            vo.setCostAddressType(costSettleManageDetailVoList.get(0).getCostAddressType());
            vo.setAgentNumber(costSettleManageDetailVoList.get(0).getAgentNumber());
//            vo.setCostSettleTypeName(ErpExamineApproveConstants.ERP_APPROVE_COST_SETTLE_TYPE.get(costSettleManageDetailVoList.get(0).getType().toString()));
            vo.setIsAdvance(costSettleManageDetailVoList.get(0).getIsAdvance());
            vo.setAdvanceNumber(costSettleManageDetailVoList.get(0).getAdvanceNumber());
            vo.setIsAdvanceName(ErpExamineApproveConstants.ERP_APPROVE_IS_ADVANCE_TYPE.get(costSettleManageDetailVoList.get(0).getIsAdvance().toString()));
            vo.setPayeeName(costSettleManageDetailVoList.get(0).getPayee());
            vo.setBankName(costSettleManageDetailVoList.get(0).getBankName());
            vo.setBankNumber(costSettleManageDetailVoList.get(0).getBankNumber());
            vo.setRemarkCost(costSettleManageDetailVoList.get(0).getRemark());
            vo.setAttachment(costSettleManageDetailVoList.get(0).getAttachment());

        }

        R<SysUser> userInfo = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser().toString()), SecurityConstants.INNER);
        vo.setCreatedUserName(userInfo.getData().getNickName());
        vo.setCreatedUserDeptName(userInfo.getData().getDept().getDeptName());

        return vo;
    }

    @Override
    public ErpProcureContractVo procureContractInfo(Long id) {

        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(id);

        return procureContractService.selectErpProcureContractById(Long.parseLong(erpExamineApprove.getOtherId()));
    }

    /**
     * 订单或服务单已申请记录
     * @param id
     * @return
     */
    public List<AppliedOrder> getAppliedOrder(Long id) {
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(id);
        Integer approveType = erpExamineApprove.getApproveType();
        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(approveType.longValue());
        Long parentId = erpExamineApproveTypeManage.getParentId();
        switch (parentId.intValue()) {
            case 1:
                CostSettlement costSettlement = costSettlementMapper.selectCostSettlementById(Integer.parseInt(erpExamineApprove.getOtherId()));
                if (Objects.nonNull(costSettlement) && StringUtils.isNotEmpty(costSettlement.getRegisterIds())) {
                    String registerIds = costSettlement.getRegisterIds();
                    String[] split = registerIds.split(",");
                    List<Long> collect = Arrays.stream(split).map(item -> Long.valueOf(item)).collect(Collectors.toList());
                    List<AppliedOrder> appliedOrder = erpExamineApproveMapper.getAppliedOrder(collect, erpExamineApprove.getApproveType());
                    if (ObjectUtil.isNotEmpty(appliedOrder) && appliedOrder.size() > 0) {
                        for (int i = 0; i < appliedOrder.size(); i++) {
                            appliedOrder.get(i).setSumFee(appliedOrder.get(i).getApprovalAmount().multiply(new BigDecimal(appliedOrder.get(i).getNumberOfTimes())));
                        }
                    }
                    return appliedOrder;
                }
            case 5:
                ErpQzdApprovalService erpQzdApprovalService = erpQzdApprovalServiceMapper.selectErpQzdApprovalServiceById(Long.valueOf(erpExamineApprove.getOtherId()));
                if (Objects.nonNull(erpQzdApprovalService)) {
                    List<AppliedOrder> licenseAppliedOrder = erpExamineApproveMapper.getLicenseAppliedOrder(erpQzdApprovalService.getLicenseId(), erpExamineApprove.getApproveType());
                    return licenseAppliedOrder;
                }
        }
        return null;
    }

    @Override
    public ApprovalsDetail getProductDetail(ErpExamineApprove erpExamineApprove) {

        ApprovalsDetail productDetail = erpExamineApproveMapper.getProductDetail(erpExamineApprove);
        if (ObjectUtil.isNotEmpty(productDetail.getProductApproveId()) && productDetail.getProductApproveId().intValue() != 0) {
            productDetail.setProductApprove(erpProductApproveMapper.selectErpProductApproveById(productDetail.getProductApproveId()));
        }
        List<AreaCity> productAreaCity = erpExamineApproveMapper.getProductAreaCity(erpExamineApprove.getId());
        productAreaCity.removeAll(Collections.singleton(null));
        List<String> areaName = new ArrayList<>();
        List<String> cityNameList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(productAreaCity)) {
            areaName = productAreaCity.stream().map(item -> item.getAreaName()).collect(Collectors.toList());
            Map<Integer, List<AreaCity>> map = productAreaCity.stream().collect(Collectors.groupingBy(AreaCity::getParentId));

            for (Integer key : map.keySet()) {
                ComDictRegion comDictRegion = approvalsMapper.selectCityId(key);
                if (Objects.nonNull(comDictRegion)) {
                    cityNameList.add(comDictRegion.getTitle());
                }
            }
            productDetail.setAreaName(areaName);
            productDetail.setCityNameList(cityNameList);
        }
        return productDetail;
    }

    @Override
    public AdvancePaymentVo getAdvancePaymentVo(ErpExamineApprove erpExamineApprove) {
        AdvancePaymentVo advancePaymentVo = erpExamineApproveMapper.getAdvancePaymentVo(erpExamineApprove.getId());
        advancePaymentVo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(advancePaymentVo.getApproveStatus().toString()));
        return advancePaymentVo;
    }

    @Override
    public ErpExamineOtherOrderPayVo getOtherOrderPayVo(ErpExamineApprove erpExamineApprove) {
        ErpExamineOtherOrderPayVo erpExamineOtherOrderPayVo = erpExamineApproveMapper.getOtherOrderPayVo(erpExamineApprove.getId());
        erpExamineOtherOrderPayVo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(erpExamineOtherOrderPayVo.getApproveStatus().toString()));
        return erpExamineOtherOrderPayVo;
    }

    @Override
    public ErpExamineCommonPaymentVo getCommonPaymentVo(ErpExamineApprove erpExamineApprove) {
        ErpExamineCommonPaymentVo erpExamineCommonPaymentVo = erpExamineApproveMapper.getCommonPaymentVo(erpExamineApprove.getId());
        if (ObjectUtil.isNotEmpty(erpExamineCommonPaymentVo.getVoucherId())) {
            ErpOrderPayRecord payRecord = erpTransactionVoucherMapper.selectByVoucherId(erpExamineCommonPaymentVo.getVoucherId());
            erpExamineCommonPaymentVo.setTradeId(payRecord.getTradeId());
            erpExamineCommonPaymentVo.setBillNo(payRecord.getBillNo());
            erpExamineCommonPaymentVo.setDocumentNumber(payRecord.getDocumentNumber());
        }
        erpExamineCommonPaymentVo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(erpExamineCommonPaymentVo.getApproveStatus().toString()));
        return erpExamineCommonPaymentVo;
    }

    @Override
    public ErpExamineNoOrderRefoundVo getNoOrderRefoundInfo(ErpExamineApprove erpExamineApprove) {
        ErpExamineNoOrderRefoundVo erpExamineNoOrderRefoundVo = erpExamineApproveMapper.getNoOrderRefoundInfo(erpExamineApprove.getId());
        erpExamineNoOrderRefoundVo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(erpExamineNoOrderRefoundVo.getApproveStatus().toString()));
        erpExamineNoOrderRefoundVo.setCollectionTypeName(ErpOrderPayRecordEnum.getPaymentTypeStr(erpExamineNoOrderRefoundVo.getCollectionType()));
        return erpExamineNoOrderRefoundVo;
    }

    @Override
    public Long cancelApproveGetOtherParent(Long otherId) {
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(otherId);

        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType().toString()));
        return erpExamineApproveTypeManage.getParentId();
    }

    @Override
    public void restartErpExamineApprove(ErpExamineApprove erpExamineApprove) {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();

        ErpExamineApproveTypePersionDto dto = new ErpExamineApproveTypePersionDto();
        dto.setApproveType(erpExamineApprove.getApproveType());
        dto.setStatus(1);
        List<ErpExamineApproveTypePersionVo> list = erpExamineApproveTypePersionMapper.selectErpExamineApproveTypePersionList(dto);

        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveTypePersionVo vo = list.get(i);
            JSONArray arr = JSONArray.fromObject(vo.getDeptIds());
            if (arr.contains(sysUser.getDeptId())) {

                String followInfoStr = vo.getFollowInfo();
                if (ObjectUtil.isNull(vo.getFollowInfo())) {
                    throw new ServiceException("审批流为空");
                }

                //获取审批流
                JSONArray arr_ = JSONArray.fromObject(followInfoStr);
                if (ObjectUtil.isNull(arr_) || arr_.size() == 0) {
                    throw new ServiceException("审批流错误");
                }
                if (arr_.getJSONObject(0).getInt("approveUser") == loginUser.getSysUser().getUserId().intValue()) {
                    arr_.remove(0);
                }
                for (int j = 0; j < arr_.size(); j++) {
                    arr_.getJSONObject(j).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT);
                }

                int currentApproveUserId = arr_.getJSONObject(0).getInt("approveUser");
                erpExamineApprove.setFollowInfo(arr_.toString());
                erpExamineApprove.setApproveCurrentUser(currentApproveUserId);
                erpExamineApprove.setCreatedUser(sysUser.getUserId().intValue());
                erpExamineApprove.setUpdatedUser(sysUser.getUserId().intValue());
                erpExamineApprove.setMakeCopyUser(",1,");
                erpExamineApprove.setApproveStatus(0);
                erpExamineApprove.setCreatedDate(new Date());
                erpExamineApprove.setUpdatedTime(new Date());
                erpExamineApproveMapper.updateErpExamineApprove(erpExamineApprove);

                R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(currentApproveUserId+""), SecurityConstants.INNER);
                //发送钉钉消息
                String dingContent = "### 审批提醒： \n * " + "您好： \n "
                        +" * 审批类型: " + vo.getApproveName() + "    \n "
                        +" * 审批ID: " + erpExamineApprove.getId() + " 待审批";
                DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批提醒", dingContent);
                dingDingService.sendDingMessage(dingSendDTO);

                //记录日志  TODO  审批类型待加
                ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.parseLong(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.SUBMIT_APPROVAL.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
                erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);

                return;
            }
        }
        throw new ServiceException("暂无权限，请联系财务增加该类型下的部门");
    }

    @Override
    public ErpExamineReportOrderVo getReportOrderInfo(Long approveId) {
        ErpExamineReportOrderVo vo = erpExamineApproveMapper.getReportOrderInfo(approveId);
        vo.setPaymentTypeStr(ErpOrderPayRecordEnum.getPaymentTypeStr(vo.getPaymentType()));
        return vo;
    }

    @Override
    public ErpOrderInvoiceVo getOrderKpInfo(Long approveId) {
        ErpOrderInvoiceVo vo = erpExamineApproveMapper.getOrderInvoiceInfo(approveId);
        ErpOrderInvoiceDetail erpOrderInvoiceDetail = new ErpOrderInvoiceDetail();
        erpOrderInvoiceDetail.setInvoiceId(vo.getId());
        erpOrderInvoiceDetail.setStatus(1);
        vo.setInvoiceDetailList(erpOrderInvoiceDetailMapper.selectErpOrderInvoiceDetailList(erpOrderInvoiceDetail));

        ErpOrderForOrderDetailVO orderVo = erpOrdersMapper.getOrderForOrderDetailByOrderId(vo.getOrderId());
        if (ObjectUtil.isNotEmpty(orderVo)) {
            vo.setContranctNumber(orderVo.getContractNumber());
            vo.setOrderNumber(orderVo.getOrderNumber());
            vo.setVcOnlineContractPdf(orderVo.getVcOnlineContractPdf());
            if (orderVo.getIsElectronicContract().intValue() == 1) {
                List<ErpContractVvo> onlieContractByOrderId = erpOrdersMapper.getOnlieContractByOrderId(vo.getOrderId());
                if (Objects.nonNull(onlieContractByOrderId)) {
                    for (ErpContractVvo erpContractVvo : onlieContractByOrderId) {
                        vo.setContranctNumber(erpContractVvo.getContractNumber());
                    }
                }
            }
        }

        List<Long> kpProductList = erpServiceOrdersMapper.selectProductKpPorudctByOrderId(vo.getOrderId());
        if (ObjectUtil.isNotEmpty(kpProductList) && kpProductList.size() > 0) {
            String kpProductName = "";
            for (int i = 0; i < kpProductList.size(); i++) {
                kpProductName+= ErpOrderInvoiceConstants.KP_PRODUCT.get(kpProductList.get(i).toString()).toString()+",";
            }
            vo.setKpProductName(kpProductName);
        }
        return vo;
    }

    @Override
    public ErpAccountDateUpdateVo getAccountUpdateInfo(Long approveId) {
        ErpAccountDateUpdateVo vo = erpExamineApproveMapper.getAccountUpdateInfo(approveId);

        ErpOrderForOrderDetailVO orderVo = erpOrdersMapper.getOrderForOrderDetailByOrderId(vo.getOrderId());
        if (ObjectUtil.isNotEmpty(orderVo)) {
            vo.setContranctNumber(orderVo.getContractNumber());
            vo.setOrderNumber(orderVo.getOrderNumber());
            vo.setVcOnlineContractPdf(orderVo.getVcOnlineContractPdf());
            if (orderVo.getIsElectronicContract().intValue() == 1) {
                List<ErpContractVvo> onlieContractByOrderId = erpOrdersMapper.getOnlieContractByOrderId(vo.getOrderId());
                if (Objects.nonNull(onlieContractByOrderId)) {
                    for (ErpContractVvo erpContractVvo : onlieContractByOrderId) {
                        vo.setContranctNumber(erpContractVvo.getContractNumber());
                    }
                }
            }
        }
        return vo;
    }

    @Override
    public ErpExamineAccountLsVo getJzLsInfo(Long approveId) {
        ErpExamineAccountLsVo vo = erpExamineApproveMapper.getJzLsInfo(approveId);
        return vo;

    }

    @Override
    public ErpExamineBudgetVo getBudgetInfo(Long approveId) {
        ErpExamineBudgetVo vo = erpExamineApproveMapper.getBudgetInfo(approveId);

        ErpExamineBudgetDto dto = new ErpExamineBudgetDto();
        dto.setBudgetMonth(vo.getBudgetMonth());
        dto.setCreatedUser(vo.getCreatedUser());
        dto.setBudgetType(vo.getBudgetType());
        dto.setNoEqualBudgetId(vo.getId());
        dto.setParentId(vo.getParentId());
        vo.setHistoryList(budgetList(dto));
        return vo;
    }

    @Override
    public ErpExamineOtherOrderPayVo getWtdzKpInfo(Long approveId) {
        ErpExamineOtherOrderPayVo erpExamineOtherOrderPayVo = erpExamineApproveMapper.getOtherOrderPayVo(approveId);
        if (ObjectUtil.isNotEmpty(erpExamineOtherOrderPayVo.getWtdzIds())) {
            ErpWtdzKpDto dto = new ErpWtdzKpDto();
            List<Long> idList = Arrays.stream(erpExamineOtherOrderPayVo.getWtdzIds().split(",")).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
            dto.setIdList(idList);
            erpExamineOtherOrderPayVo.setWtdzList(wtdzKpService.selectErpWtdzKpList(dto));
        }
        erpExamineOtherOrderPayVo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(erpExamineOtherOrderPayVo.getApproveStatus().toString()));
        return erpExamineOtherOrderPayVo;

    }

    /**
     * 获取当前审批人的角色  1：普通管理，2：财务，3：高管
     * @param approveId
     * @return
     */
    @Override
    public Integer getApproveCurrentUserRole(Long approveId, Integer type) {
        Long userid = tokenService.getLoginUser().getUserid();
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(approveId);
        if (Objects.nonNull(erpExamineApprove)) {
            String followInfo = erpExamineApprove.getFollowInfo();
            Integer approveCurrentUser = erpExamineApprove.getApproveCurrentUser();
            JSONArray jsonArray = JSONArray.fromObject(followInfo);
            if (ObjectUtil.isNull(jsonArray) || jsonArray.size() == 0) {
                throw new ServiceException("审批流错误");
            }
            if (1 == type) {
                for (Object object : jsonArray) {
                    JSONObject jsonObject = JSONObject.fromObject(object);
                    if (jsonObject.getInt("approveUser") == approveCurrentUser) {
                        boolean userRole = jsonObject.containsKey("approveUserRole");
                        if (!userRole) {
                            throw new ServiceException("请创建审批人角色");
                        }
                        return jsonObject.getInt("approveUserRole");
                    }
                }
            } else if (2 == type) {
                for (Object object : jsonArray) {
                    JSONObject jsonObject = JSONObject.fromObject(object);
                    if (jsonObject.getInt("approveUser") == userid.intValue()) {
                        boolean userRole = jsonObject.containsKey("approveUserRole");
                        if (!userRole) {
                            throw new ServiceException("请创建审批人角色");
                        }
                        return jsonObject.getInt("approveUserRole");
                    }
                }
            }
        }
        return null;
    }

    @Override
    public ErpOrderCostAndPayment getErpOrderCostAndPayment(Long orderId, Long approveId) {
        ErpOrderCostAndPayment erpOrderCostAndPayment = new ErpOrderCostAndPayment();
        List<ErpOrderCostExpenditure> erpOrderCostExpenditure = erpOrderRefundCostExpenditureMapper.getErpOrderCostExpenditure(approveId);
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(orderId);
        if (Objects.nonNull(erpOrders)) {
            List<ErpOrderPaymentRecord> erpOrderPaymentRecord = erpQzdPaymentRecordMapper.getErpOrderPaymentRecord(erpOrders.getVcOrderNumber());
            for (int i = 0; i < erpOrderPaymentRecord.size(); i++) {
                ErpOrderPaymentRecord record = erpOrderPaymentRecord.get(i);
                record.setApproveStatusName("已通过");
                record.setApproveName("成本类-"+record.getApproveName());
            }


            erpOrderCostAndPayment.setErpOrderPaymentRecords(erpOrderPaymentRecord);
        }
        ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(approveId);
        if (Objects.nonNull(erpExamineApprove)) {
            ErpOrderRefund erpOrderRefund = erpOrderRefundMapper.selectErpOrderRefundById(Long.valueOf(erpExamineApprove.getOtherId()));
            erpOrderCostAndPayment.setErpOrderRefund(erpOrderRefund);
        }
        erpOrderCostAndPayment.setErpOrderCostExpenditures(erpOrderCostExpenditure);
        return erpOrderCostAndPayment;
    }

    @Override
    public ErpOrderRefundDetailVo getErpOrderRefundDetailVo(Long approveId) {
        ErpOrderRefundDetailVo erpOrderRefundDetail = erpExamineApproveMapper.getErpOrderRefundDetail(approveId);
        erpOrderRefundDetail.setTotalRefundAmount(erpExamineApproveMapper.getRefunded(erpOrderRefundDetail.getOrderId()));
        ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure = new ErpOrderRefundCostExpenditure();
        erpOrderRefundCostExpenditure.setApproveId(approveId);
        List<ErpOrderRefundCostExpenditure> erpOrderRefundCostExpenditures = erpOrderRefundCostExpenditureMapper.selectErpOrderRefundCostExpenditureList(erpOrderRefundCostExpenditure);
        Map<Long, ErpOrderRefundCostExpenditure> map = erpOrderRefundCostExpenditures.stream().collect(Collectors.toMap(ErpOrderRefundCostExpenditure::getApproveUserId, Function.identity(), (key1, key2) -> key2));
        if (CollectionUtils.isNotEmpty(erpOrderRefundCostExpenditures)) {
            //BigDecimal reduce = erpOrderRefundCostExpenditures.stream().map(ErpOrderRefundCostExpenditure::getFinanceConfirmAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<ErpOrderRefundCostExpenditure> collect = erpOrderRefundCostExpenditures.stream().sorted(Comparator.comparing(ErpOrderRefundCostExpenditure::getId).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                erpOrderRefundDetail.setRefundPayPrice(erpOrderRefundDetail.getRefundAmount().subtract(collect.get(0).getFinanceConfirmAmount()));
            }
        }


        List<ErpRefundExamineApprovalFlowVO> list = new ArrayList<>();
        ErpExamineApprove examineApprove = erpExamineApproveMapper.selectErpExamineApproveById(approveId);
        R<SysUser> info = remoteUserService.getUserInfoById(examineApprove.getCreatedUser().longValue(), SecurityConstants.INNER);
        if (200 == info.getCode()) {
            ErpRefundExamineApprovalFlowVO erpExamineApprovalFlowVO = new ErpRefundExamineApprovalFlowVO();
            erpExamineApprovalFlowVO.setNickname(info.getData().getNickName());
            erpExamineApprovalFlowVO.setApplyTime(examineApprove.getCreatedDate());
            list.add(erpExamineApprovalFlowVO);
        }

        String followInfo = examineApprove.getFollowInfo();
        if (StringUtils.isNotEmpty(followInfo)) {
            com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(followInfo);
            for (Object object : jsonArray) {
                com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) com.alibaba.fastjson.JSONObject.toJSON(object);
                Long approveUser = json.getLong("approveUser");
                R<SysUser> infoApply = remoteUserService.getUserInfoById(approveUser, SecurityConstants.INNER);
                if (200 == infoApply.getCode()) {
                    ErpOrderRefundCostExpenditure orderRefundCostExpenditure = map.get(approveUser);
                    ErpRefundExamineApprovalFlowVO erpExamineApprovalFlowVO = new ErpRefundExamineApprovalFlowVO();
                    erpExamineApprovalFlowVO.setNickname(infoApply.getData().getNickName());
                    Integer approve_status = json.getInteger("approve_status");
                    erpExamineApprovalFlowVO.setApprovalStatus(ApprovalExamineStatusEnum.getNameByType(approve_status));
                    Date time = json.getDate("time");
                    erpExamineApprovalFlowVO.setApplyTime(time);
                    erpExamineApprovalFlowVO.setExpenditureRemark(Objects.nonNull(orderRefundCostExpenditure) ? orderRefundCostExpenditure.getExpenditureRemark() : null);
                    erpExamineApprovalFlowVO.setFinanceConfirmAmount(Objects.nonNull(orderRefundCostExpenditure) ? orderRefundCostExpenditure.getFinanceConfirmAmount() : null);
                    erpExamineApprovalFlowVO.setGrossExpenditure(Objects.nonNull(orderRefundCostExpenditure) ? orderRefundCostExpenditure.getGrossExpenditure() : null);
                    list.add(erpExamineApprovalFlowVO);
                }
            }
        }
        erpOrderRefundDetail.setErpRefundExamineApprovalFlowVOS(list);
        return erpOrderRefundDetail;
    }

    @Override
    public String batchGenerateHtml(String html) {
        //生成pdf链接
        String prefix = "<!DOCTYPE html><html class=\"\"><head><meta charset=\"utf-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"><meta name=\"renderer\" content=\"webkit\"><meta name=\"referrer\" content=\"no-referrer\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"><link rel=\"icon\" href=\"/favicon.ico\"></link><title>批量打印</title></head><body>";
        String suffix = "</body></html>";
        html = prefix + html + suffix;

        String outPdfFilePath = parentPath + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + ".pdf";
        String outHtmlFilePath = parentPath + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + ".html";


        try {
            FileOutputStream fos = new FileOutputStream(outHtmlFilePath);
            fos.write(html.getBytes());
            fos.close();

            StringBuilder cmdArray = new StringBuilder();
            cmdArray.append("wkhtmltopdf");
            cmdArray.append(" ");
            cmdArray.append(outHtmlFilePath);
            cmdArray.append(" ");
            cmdArray.append(outPdfFilePath);
            Process process = Runtime.getRuntime().exec(cmdArray.toString());
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                File pdfFile = new File(outPdfFilePath);
                File htmlFile = new File(outHtmlFilePath);
                if(!pdfFile.exists()){
                    throw new ServiceException("PDF文件不存在");
                }

                // 上传PDF。
                OssFileForUploadVO ossFileForUploadVO = ossService.uploadFile(getMultipartFile(pdfFile));

                // 删除本地PDF。
                if (!pdfFile.delete()) {
                    throw new ServiceException("Pdf文件删除失败");
                }
                if (!htmlFile.delete()) {
                    throw new ServiceException("Pdf文件删除失败");
                }
                return ossFileForUploadVO.getWrapFilePath();
            }
        } catch (Exception e) {
            log.error("PDF生成失败",e);
            throw new ServiceException("PDF生成失败");
        }
        return null;
    }

    @Override
    public int checkApprove(Integer approveType) {
        if (ObjectUtil.isEmpty(approveType)) {
            throw new ServiceException("请选择申请类型");
        }
        ErpExamineApproveTypeManage approveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(approveType.toString()));

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();

        ErpExamineApproveTypePersionDto dto = new ErpExamineApproveTypePersionDto();
        dto.setApproveType(approveType);
        dto.setStatus(1);
        dto.setSample(2);
        List<ErpExamineApproveTypePersionVo> list = erpExamineApproveTypePersionMapper.selectErpExamineApproveTypePersionList(dto);

        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveTypePersionVo vo = list.get(i);
            JSONArray userIdArr = ObjectUtils.isNotEmpty(vo.getUserIds()) ? JSONArray.fromObject(vo.getUserIds()) : new JSONArray();
            JSONArray deptIdArr = ObjectUtils.isNotEmpty(vo.getDeptIds()) ? JSONArray.fromObject(vo.getDeptIds()) : new JSONArray();

            if ((approveTypeManage.getParentId().intValue() == ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && userIdArr.contains(sysUser.getUserId()))
                    || (approveTypeManage.getParentId().intValue() != ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET && deptIdArr.contains(sysUser.getDeptId())) ) {

                String followInfoStr = vo.getFollowInfo();
                if (ObjectUtil.isNull(vo.getFollowInfo())) {
                    throw new ServiceException("审批流为空");
                }

                //获取审批流
                JSONArray arr_ = JSONArray.fromObject(followInfoStr);
                if (ObjectUtil.isNull(arr_) || arr_.size() == 0) {
                    throw new ServiceException("审批流错误");
                }
                return 1;
            }
        }
        throw new ServiceException("暂无权限，请联系财务增加该类型下的部门");
    }

    @Override
    public List<ErpExamineApproveKPVo> approveListKP(ErpExamineApproveKPDto dto) {

        List<ErpExamineApproveKPVo> list = erpExamineApproveMapper.approveListKP(dto);
        if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                ErpExamineApproveKPVo vo = list.get(i);

                vo.setKpStatusStr(ErpOrderInvoiceConstants.KP_STATUS.get(vo.getKpStatus().toString()));
                vo.setInvoiceHeaderTypeStr(ErpOrderInvoiceConstants.KP_INVOICE_HEADER_TYPE.get(vo.getInvoiceHeaderType().toString()));
                vo.setInvoiceTypeStr(ErpOrderInvoiceConstants.KP_INVOICE_TYPE.get(vo.getInvoiceType().toString()));
                vo.setChangeRedStr(ErpOrderInvoiceConstants.KP_RED.get(vo.getChangeRed().toString()));
                if (ObjectUtil.isNotEmpty(vo.getRevokeStatus())){
                    vo.setRevokeStatusName(ErpExamineApproveConstants.ERP_EXAMINE_REVOKE_STATUS.get(vo.getRevokeStatus().toString()));
                }
                vo.setApproveStatusName(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS.get(vo.getApproveStatus().toString()));

                if (vo.getIsElectronicContract().intValue() == 1) {
                    List<ErpContractVvo> onlieContractByOrderId = erpOrdersMapper.getOnlieContractByOrderId(vo.getOrderId());
                    if (Objects.nonNull(onlieContractByOrderId)) {
                        for (ErpContractVvo erpContractVvo : onlieContractByOrderId) {
                            vo.setContranctNumber(erpContractVvo.getContractNumber());
                        }
                    }
                }
                vo.setRedFee(erpOrderInvoiceRedMapper.getRedFeeByInvoiceId(vo.getOtherId()));
            }
        }



        return list;
    }

    @Override
    public List<ErpExamineApproveListVO> getInApproveList(ErpExamineApproveDTO dto) {
        return erpExamineApproveMapper.getInApproveList(dto.getOrderNum());
    }

    /***
     * 审批流通过，更新otherId的业务方法
     */
    private void approveEndPass(ErpExamineApprove erpExamineApprove) {

        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType().toString()));
        int parentId = Integer.parseInt(erpExamineApproveTypeManage.getParentId().toString());
        switch (parentId) {
            case ErpExamineApproveConstants.APPROVE_CATEGORY_PROCURE_CONTRACT:
                //采购合同
                ErpProcureContract erpProcureContract = erpProcureContractMapper.selectErpProcureContractById(Long.parseLong(erpExamineApprove.getOtherId()));
                erpProcureContract.setStatus(2);
                if (erpProcureContract.getContractSource() == 1) {

                    String code = erpProcureContractMapper.selectHtmlById(erpProcureContract.getId());
                    if (ObjectUtil.isEmpty(code)) {
                        throw new ServiceException("html内容为空");
                    }

                    //生成pdf链接
                    String prefix = "<!DOCTYPE html><html class=\"\"><head><meta charset=\"utf-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"><meta name=\"renderer\" content=\"webkit\"><meta name=\"referrer\" content=\"no-referrer\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"><link rel=\"icon\" href=\"/favicon.ico\"></link><title>电子合同</title></head><body>";
                    String suffix = "</body></html>";
                    code = prefix + code + suffix;

                    String outPdfFilePath = parentPath + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + ".pdf";
                    String outHtmlFilePath = parentPath + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + ".html";


                    try {
                        FileOutputStream fos = new FileOutputStream(outHtmlFilePath);
                        fos.write(code.getBytes());
                        fos.close();

                        StringBuilder cmdArray = new StringBuilder();
                        cmdArray.append("wkhtmltopdf");
                        cmdArray.append(" ");
                        cmdArray.append(outHtmlFilePath);
                        cmdArray.append(" ");
                        cmdArray.append(outPdfFilePath);
                        Process process = Runtime.getRuntime().exec(cmdArray.toString());
                        int exitCode = process.waitFor();
                        if (exitCode == 0) {
                            File pdfFile = new File(outPdfFilePath);
                            File htmlFile = new File(outHtmlFilePath);
                            if(!pdfFile.exists()){
                                throw new ServiceException("PDF文件不存在");
                            }

                            // 上传PDF。
                            OssFileForUploadVO ossFileForUploadVO = ossService.uploadFile(getMultipartFile(pdfFile));

                            // 删除本地PDF。
                            if (!pdfFile.delete()) {
                                throw new ServiceException("Pdf文件删除失败");
                            }
                            if (!htmlFile.delete()) {
                                throw new ServiceException("Pdf文件删除失败");
                            }
                            erpProcureContract.setPdfUrl(ossFileForUploadVO.getWrapFilePath());
                        }
                    } catch (Exception e) {
                        log.error("PDF生成失败",e);
                        throw new ServiceException("PDF生成失败");
                    }
                }
                erpProcureContractMapper.updateErpProcureContract(erpProcureContract);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ADVANCE:
                //预付单审批通过
                ErpBizServiceAdvance erpBizServiceAdvance = erpBizServiceAdvanceMapper.selectErpBizServiceAdvanceById(Integer.parseInt(erpExamineApprove.getOtherId()));
                erpBizServiceAdvance.setStatus(2);
                erpBizServiceAdvanceMapper.updateErpBizServiceAdvance(erpBizServiceAdvance);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_CANCEL:
                //撤销
                ErpExamineApprove approveCancel = erpExamineApproveMapper.selectErpExamineApproveById(Long.parseLong(erpExamineApprove.getOtherId()));
                approveCancel.setRevokeStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_REVOKE_STATUS_PASS);
                approveCancel.setApproveStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_CANCLE);
                erpExamineApproveMapper.updateErpExamineApprove(approveCancel);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_PRODUCT:
                //产品审批通过
                ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(Long.parseLong(erpExamineApprove.getOtherId()));
                if (erpExamineApprove.getApproveType().intValue() == ErpExamineApproveConstants.APPROVE_TYPE_PRODUCT_UPDATE) {
                    if (erpProductDetail.getApproveInfoId().intValue() != 0) {
                        ErpProductApprove productApprove = erpProductApproveMapper.selectErpProductApproveById(erpProductDetail.getApproveInfoId());

                        if (ObjectUtil.isNotEmpty(productApprove.getNumServiceId())) {
                            erpProductDetail.setNumServiceId(productApprove.getNumServiceId());
                        }
                        if (ObjectUtil.isNotEmpty(productApprove.getStandardPrice())) {
                            erpProductDetail.setStandardPrice(productApprove.getStandardPrice());
                        }
                        if (ObjectUtil.isNotEmpty(productApprove.getCostPrice())) {
                            erpProductDetail.setCostPrice(productApprove.getCostPrice());
                        }
                        if (ObjectUtil.isNotEmpty(productApprove.getEarlyWarning())) {
                            erpProductDetail.setEarlyWarning(productApprove.getEarlyWarning());
                        }
                        if (ObjectUtil.isNotEmpty(productApprove.getEarlyWarningUrl())) {
                            erpProductDetail.setEarlyWarningUrl(productApprove.getEarlyWarningUrl());
                        }
                        JSONArray productConfiguration = JSONArray.fromObject(productApprove.getProductConfiguration());
                        if (productConfiguration.size() > 0) {
                            erpProductDetailService.addConfig(Long.parseLong(erpExamineApprove.getOtherId()), JSON.parseArray(productApprove.getProductConfigurationInfo(), ProductConfigDto.class));
                        }
                    }
                }

                erpProductDetail.setNumIsCheck(ErpProductConstants.Y);
                erpProductDetail.setNumIsUp(1L);
                erpProductDetail.setApproveInfoId(0L);
                erpProductDetailMapper.updateErpProductDetail(erpProductDetail);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_CHENGBEN:
                CostSettlement costSettlement = costSettlementMapper.selectCostSettlementById(Integer.parseInt(erpExamineApprove.getOtherId()));
                if (costSettlement.getAdvanceId()!=null) {
                    ErpBizServiceAdvance serviceAdvance = new ErpBizServiceAdvance();
                    serviceAdvance.setHxStatus(1);
                    serviceAdvance.setId(costSettlement.getAdvanceId().intValue());
                    erpBizServiceAdvanceMapper.updateErpBizServiceAdvance(serviceAdvance);
                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_REFUND:
                //标记执照退款状态
                ErpQzdApprovalService erpQzdApprovalService = erpQzdApprovalServiceMapper.selectErpQzdApprovalServiceById(Long.valueOf(erpExamineApprove.getOtherId()));
                if(Objects.nonNull(erpQzdApprovalService)){
                    ErpLicense erpLicense = new ErpLicense();
                    erpLicense.setId(erpQzdApprovalService.getLicenseId());
                    erpLicense.setIsRefund(1);
                    erpLicenseMapper.updateErpLicense(erpLicense);
                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_SUPPLEMENT:
                erpExamineApprove.setSupplementStatus(ApprovalExamineStatusEnum.APPROVAL_PASS.getType());
                ErpQzdApprovalService erpQzdApprovalServiceUpdate = new ErpQzdApprovalService();
                erpQzdApprovalServiceUpdate.setId(Long.valueOf(erpExamineApprove.getOtherId()));
                erpQzdApprovalServiceUpdate.setSupplementStatus(1);
                erpQzdApprovalServiceMapper.updateErpQzdApprovalService(erpQzdApprovalServiceUpdate);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REFUND:
                //更改订单状态，service_orders状态，服务单状态，退款表状态
                //查询退款信息
                BigDecimal orderRefundAmount = BigDecimal.ZERO;
                ErpOrderRefund erpOrderRefund = erpOrderRefundMapper
                        .selectErpOrderRefundById(Long.valueOf(erpExamineApprove.getOtherId()));
                //查询service_orders
                List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(erpOrderRefund.getOrderId());

                int licenseNumber = -1;
                for (int i = 0; i < serviceOrdersList.size(); i++) {
                    ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                    if (serviceOrders.getNumProductId().intValue() == 3413) {
                        licenseNumber = serviceOrders.getLicenseNumber();
                    }
                }
                //查询退款明细
                ErpOrderRefundDetail detail = new ErpOrderRefundDetail().setOrderRefundId(erpOrderRefund.getId());
                List<ErpOrderRefundDetail> detailList = erpOrderRefundDetailMapper.selectErpOrderRefundDetailList(detail);
                for (ErpOrderRefundDetail refundDetail : detailList) {
                    if (OrderRefundConstants.FULL_REFUND.equals(refundDetail.getRefundType())){
                        refundDetail.setVoidStatus(OrderVoidStatusEnum.REFUND.getCode());
                        //更新服务单
                        sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                                erpOrderRefund.getOrderId(), refundDetail.getProductId(),
                                ServiceMainStatusEnum.REFUND.getCode());
                        //更新内部服务单
                        sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(
                                Long.parseLong(ServiceMainStatusEnum.REFUND.getCode().toString()),
                                erpOrderRefund.getOrderId(),
                                refundDetail.getProductId());
                        //更新赠品服务单
                        sServiceMainMapper.updateGiveProductSServiceMain(
                                Long.parseLong(ServiceMainStatusEnum.REFUND.getCode().toString()),
                                erpOrderRefund.getOrderId());


                        ErpServiceOrders erpServiceOrders = serviceOrdersList.stream()
                                .filter(en -> en.getNumProductId().equals(refundDetail.getProductId()))
                                .findAny().orElse(null);
                        serviceQualificationsExtensionService.canclePass(erpServiceOrders.getId());
                    }
                    if (OrderRefundConstants.PARTIAL_REFUND.equals(refundDetail.getRefundType())){
                        //需要进行校验，弱产品所有的金额都已经退完，则变成全部退费
                        ErpServiceOrders erpServiceOrders = serviceOrdersList.stream()
                                .filter(en -> en.getNumProductId().equals(refundDetail.getProductId()))
                                .findAny().orElse(null);
                        //若实际收款 = 已退款 + 本次退款 则 状态为退款
                        if (erpServiceOrders.getNumPayPrice()
                                .compareTo(erpServiceOrders.getNumRefundPrice()
                                        .add(refundDetail.getRefundAmount())) == 0){
                            refundDetail.setVoidStatus(OrderVoidStatusEnum.REFUND.getCode());
                            //更新服务单
                            sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                                    erpOrderRefund.getOrderId(), refundDetail.getProductId(),
                                    ServiceMainStatusEnum.REFUND.getCode());
                            sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(
                                    Long.parseLong(ServiceMainStatusEnum.REFUND.getCode().toString()),
                                    erpOrderRefund.getOrderId(),
                                    refundDetail.getProductId());

                            serviceQualificationsExtensionService.canclePass(erpServiceOrders.getId());
                        }else {
                            refundDetail.setVoidStatus(OrderVoidStatusEnum.PARTIAL_REFUND.getCode());
                            //更新服务单
                            sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                                    erpOrderRefund.getOrderId(), refundDetail.getProductId(),
                                    ServiceMainStatusEnum.PARTIAL_REFUND.getCode());
                            sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(
                                    Long.parseLong(ServiceMainStatusEnum.PARTIAL_REFUND.getCode().toString()),
                                    erpOrderRefund.getOrderId(),
                                    refundDetail.getProductId());
                        }
                    }
                    //更新serviceOrders状态
                    erpServiceOrdersMapper.updateNumIsDeprecatedById(refundDetail.getServiceOrdersId(), refundDetail.getVoidStatus());
                    //更新serviceOrders退款金额
                    orderRefundAmount = orderRefundAmount.add(refundDetail.getRefundAmount());
                    erpServiceOrdersMapper.updateNumRefundAmountById(refundDetail.getServiceOrdersId(), refundDetail.getRefundAmount());
                }
                //更新退款明细
                mybatisBatchUtils.batchUpdateOrInsert(detailList, ErpOrderRefundDetailMapper.class,
                        (refundDetail, erpOrderRefundDetailMapper) ->
                                erpOrderRefundDetailMapper.updateErpOrderRefundDetail(refundDetail));
                //更新订单状态
                ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(erpOrderRefund.getOrderId());
                if (OrderRefundConstants.FULL_REFUND.equals(erpOrderRefund.getRefundType())){
                    //全额退款
                    erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                            OrderInvalidStatusEnum.INVALID_REFUNDED.getStatusType());
                    //若订单使用的是纸质合同，纸质合同状态变更为已发放
//                    if (OrderRefundConstants.PAPER_CONTRACTS.equals(erpOrders.getIsElectronicContract())){
//                        erpContractMapper.updateNumStatusById(erpOrders.getNumContractId(), OrderRefundConstants.ISSUED);
//                    }
                    //若有待发货赠品，则释放库存
                    cancelOrderGift(erpOrderRefund.getOrderId());

                    //更改执照状态
                    if (licenseNumber != -1) {
                        ErpLicense license = new ErpLicense();
                        license.setNumber(licenseNumber);
                        license.setStatus(1);
                        erpLicenseMapper.updateErpLicenseByNumber(license);
                    }
                }
                if (OrderRefundConstants.PARTIAL_REFUND.equals(erpOrderRefund.getRefundType())){
                    //需要进行校验，弱产品所有的金额都已经退完，则变成全部退费 若实际收款 = 已退款 + 本次退款 则 状态为退款
                    if (erpOrders.getNumPayPrice()
                            .compareTo(erpOrders.getNumRefundPrice().add(erpOrderRefund.getRefundAmount())) == 0){
                        erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                                OrderInvalidStatusEnum.INVALID_REFUNDED.getStatusType());
                        //若订单使用的是纸质合同，纸质合同状态变更为已发放
                        if (OrderRefundConstants.PAPER_CONTRACTS.equals(erpOrders.getIsElectronicContract())){
                            erpContractMapper.updateNumStatusById(erpOrders.getNumContractId(), OrderRefundConstants.ISSUED);
                        }
                    }else {
                        erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                                OrderInvalidStatusEnum.INVALID_PARTIALLY_REFUNDED.getStatusType());
                    }
                }
                //更新订单退款金额
                erpOrdersMapper.updateOrderRefundAmountById(erpOrderRefund.getOrderId(), orderRefundAmount);
                //发送钉钉通知
                sendDingDingMsg(erpOrderRefund);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REPORT:
                ErpOrderPayRecord erpOrderPayRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(Long.valueOf(erpExamineApprove.getOtherId()));
                erpOrderPayRecord.setReportReviewStatus(1);
                erpOrderPayRecord.setReportedOrNot(1);
                erpOrderPayRecord.setReportOverDate(new Date());
                if (erpOrderPayRecord.getPaymentType() == 1 || erpOrderPayRecord.getPaymentType() == 3 || erpOrderPayRecord.getPaymentType() == 5) {
                    R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(erpExamineApprove.getCreatedUser()+""), SecurityConstants.INNER);
                    if (200 == info.getCode()) {
                        erpOrderPayRecord.setUserId(info.getData().getUserId());
                        erpOrderPayRecord.setUserName(info.getData().getNickName());
                        erpOrderPayRecord.setDeptId(info.getData().getDept().getDeptId());
                        erpOrderPayRecord.setDeptName(info.getData().getDept().getDeptName());
                    }
                }
//                if (erpOrderPayRecord.getPaymentType() == 0) {
//                    erpOrderPayRecordMapper.updateApproveStatusByBillNo(erpOrderPayRecord.getBillNo(), erpOrderPayRecord.getReportReviewStatus(), erpOrderPayRecord.getReportedOrNot());
//                } else {
                    erpOrderPayRecordMapper.updateErpOrderPayRecord(erpOrderPayRecord);
//                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS:
                ErpExamineAccountLs examineAccountLs = erpExamineAccountLsMapper.selectErpExamineAccountLsById(Long.valueOf(erpExamineApprove.getOtherId()));
                SServiceMain serviceMainLs = sServiceMainMapper.selectSServiceMainById(examineAccountLs.getServiceId());
                serviceMainLs.setServicePointStatus(ServiceMainConstants.JZServiceLS);
                serviceMainLs.setLsDate(examineAccountLs.getLsDate());
                serviceMainLs.setLsRemark(examineAccountLs.getLsRemark());
//                serviceMainLs.setAccountLossReason(examineAccountLs.getAccountLossReason());

                if (ObjectUtil.isNotEmpty(examineAccountLs.getAccountLossReason())) {
                    List<Long> lsReasonList = Arrays.asList(examineAccountLs.getAccountLossReason().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    if (lsReasonList.size() > 0) {
                        for (int i = 0; i < lsReasonList.size(); i++) {
                            SServiceLossReason serviceLossReason = new SServiceLossReason();
                            serviceLossReason.setServiceId(serviceMainLs.getId());
                            serviceLossReason.setAccountLsId(lsReasonList.get(i));
                            serviceLossReason.setEnterpriseId(serviceMainLs.getNumEnterpriseId());
                            serviceLossReason.setLsDate(examineAccountLs.getLsDate());
                            sServiceLossReasonMapper.insertSServiceLossReason(serviceLossReason);
                        }
                    } else {
                        throw new ServiceException("流失原因不对");
                    }

                } else {
                    throw new ServiceException("流失原因不对");
                }
                sServiceMainMapper.updateSServiceMain(serviceMainLs);
                //更新所有历史记账服务
                sServiceMainMapper.updateSServiceMainByEnterPriseIdAndServiceType(serviceMainLs.getServicePointStatus(),serviceMainLs.getNumEnterpriseId(),serviceMainLs.getServiceType());
                //税控托管更新流失
                sServiceMainMapper.updateSServiceMainByEnterPriseIdAndServiceType(72L,serviceMainLs.getNumEnterpriseId(),18L);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ACCOUNT:
                ErpAccountDateUpdate accountDateUpdate = erpAccountDateUpdateMapper.selectErpAccountDateUpdateById(Long.valueOf(erpExamineApprove.getOtherId()));

                SServiceMain serviceMainAcUpdate = sServiceMainMapper.selectSServiceMainById(accountDateUpdate.getServiceId());
                serviceMainAcUpdate.setAcStart(accountDateUpdate.getNewStart());
                serviceMainAcUpdate.setAcEnd(accountDateUpdate.getNewEnd());
                sServiceMainMapper.updateSServiceMain(serviceMainAcUpdate);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_NO_ORDER_REFUND:
                ErpExamineApproveCommonPayment commonPayment = erpExamineApproveCommonPaymentMapper.selectErpExamineApproveCommonPaymentById(Long.valueOf(erpExamineApprove.getOtherId()));
                ErpTransactionVoucher transactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(commonPayment.getVoucherId());
                if (transactionVoucher.getType() == 2) {
                    ErpTransactionVoucher voucherGive = erpTransactionVoucherMapper.selectErpTransactionVoucherBySelfId(transactionVoucher.getId());
                    voucherGive.setBalance(new BigDecimal("0"));
                    voucherGive.setBalanceUse(new BigDecimal("0"));
                    voucherGive.setApproveIn(2);
                    erpTransactionVoucherMapper.updateErpTransactionVoucher(voucherGive);
                }
                String memo = "(无订单退款审批通过:"+commonPayment.getFee()+",审批ID:"+erpExamineApprove.getId()+")";
                transactionVoucher.setSubtractMemo(
                        ObjectUtil.isEmpty(transactionVoucher.getSubtractMemo()) ? memo : transactionVoucher.getSubtractMemo()+memo
                );
                erpTransactionVoucherMapper.updateErpTransactionVoucher(transactionVoucher);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_DLS_JS:

                ErpExamineOtherOrderPayVo erpExamineOtherOrderPayVo = erpExamineApproveMapper.getOtherOrderPayVo(erpExamineApprove.getId());
                if (ObjectUtil.isNotEmpty(erpExamineOtherOrderPayVo.getWtdzIds())) {
                    List<Long> idList = Arrays.stream(erpExamineOtherOrderPayVo.getWtdzIds().split(",")).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
                    for (int i = 0; i < idList.size(); i++) {
                        ErpWtdzKp erpWtdzKp = new ErpWtdzKp();
                        erpWtdzKp.setId(idList.get(i));
                        erpWtdzKp.setSettlement(1);
                        wtdzKpMapper.updateErpWtdzKp(erpWtdzKp);
                    }
                }
                break;
        }
        //记录日志
        ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.valueOf(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.APPROVAL_PASS.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
        erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);
    }

    private void cancelOrderGift(Long orderId) {
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(orderId);
        ErpOrderGiftDetailVO erpOrderGiftDetailVO = erpGiftIssueRecordMapper.getErpOrderGiftDetailVO(orderId);
        if (Objects.nonNull(erpOrderGiftDetailVO) && Arrays.asList(1, 2).contains(erpOrderGiftDetailVO.getGiftStatus())) {
            //更新状态
            int i = erpOrderGiftMapper.updateGiftStatusByOrderId(orderId, 4);
            if (i <= 0) {
                throw new ServiceException("状态更新失败，请稍后重试。");
            }
            //释放库存
            int result = erpGiftIssueRecordMapper.cancelErpOrderGift(erpOrderGiftDetailVO.getGiftIssueRecordId());
            if (result <= 0) {
                throw new ServiceException("库存释放失败，请稍后重试。");
            }
            if (erpOrderGiftDetailVO.getGiftType() == 1) {
                //发送钉钉消息
                String dingContent = "### 赠品订单已退款通知： \n * " + "您好： \n "
                        + " * 订单号: " + erpOrders.getVcOrderNumber() + "，该订单已成功退款，请核实赠品是否邮寄，及时进行登记。    \n ";
                DingSendDTO dingSendDTO = new DingSendDTO(erpOrderRefundConsignerId, "审批通过提醒", dingContent);
                dingDingService.sendDingMessage(dingSendDTO);
            }
        }
    }

    /**
     *
     * @param erpExamineApproveDTO
     * @param addType 1:审批通过 2：审批驳回
     */

    private void addRefundCost(ErpExamineApproveDTO erpExamineApproveDTO, Integer addType) {
        Integer currentUserRole = getApproveCurrentUserRole(erpExamineApproveDTO.getId(), 1);
        ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure = erpExamineApproveDTO.getErpOrderRefundCostExpenditure();
        if (ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_PASS == addType) {
            if (Arrays.asList(1, 2).contains(currentUserRole)) {
                //财务审核更新最新退款原因
                if (Objects.nonNull(currentUserRole) && 2 == currentUserRole && Objects.nonNull(erpExamineApproveDTO.getRefundReasonSelect())) {
                    ErpExamineApprove erpExamineApprove = erpExamineApproveMapper.selectErpExamineApproveById(erpExamineApproveDTO.getId());
                    ErpOrderRefund erpOrderRefund = new ErpOrderRefund();
                    erpOrderRefund.setId(Long.valueOf(erpExamineApprove.getOtherId()));
                    erpOrderRefund.setRefundReasonSelect(erpExamineApproveDTO.getRefundReasonSelect());
                    erpOrderRefundMapper.updateErpOrderRefund(erpOrderRefund);

                }
                //保存成本支出
                if (Objects.nonNull(erpOrderRefundCostExpenditure)) {
                    if (Objects.nonNull(currentUserRole) && 2 == currentUserRole) {
                        erpOrderRefundCostExpenditure.setFinanceConfirmAmount(erpOrderRefundCostExpenditure.getGrossExpenditure());
                        erpOrderRefundCostExpenditure.setAfterSaleConfirmAmount(erpOrderRefundCostExpenditure.getGrossExpenditure());
                    }
                    erpOrderRefundCostExpenditure.setApproveId(erpExamineApproveDTO.getId());
                    erpOrderRefundCostExpenditure.setApproveUserId(SecurityUtils.getUserId());
                    erpOrderRefundCostExpenditure.setCreateUser(SecurityUtils.getUserId());
                    erpOrderRefundCostExpenditure.setApproveAttachment(erpOrderRefundCostExpenditure.getApproveAttachment());
                    erpOrderRefundCostExpenditure.setAfterSaleConfirmAmount(erpOrderRefundCostExpenditure.getAfterSaleConfirmAmount());
                    erpOrderRefundCostExpenditureMapper.insertErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditure);
                }
                //更新财务确认支出
                if (CollectionUtils.isNotEmpty(erpExamineApproveDTO.getErpOrderCostExpenditures())) {
                    Integer approveCurrentUserRole = getApproveCurrentUserRole(erpExamineApproveDTO.getId(), 1);
                    if (Objects.nonNull(approveCurrentUserRole) && 2 == approveCurrentUserRole) {
                        for (ErpOrderCostExpenditure erpOrderCostExpenditure : erpExamineApproveDTO.getErpOrderCostExpenditures()) {
                            ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditureUpdate = new ErpOrderRefundCostExpenditure();
                            erpOrderRefundCostExpenditureUpdate.setId(erpOrderCostExpenditure.getId());
                            erpOrderRefundCostExpenditureUpdate.setFinanceConfirmAmount(erpOrderCostExpenditure.getFinanceConfirmAmount());
                            erpOrderRefundCostExpenditureUpdate.setAfterSaleConfirmAmount(erpOrderCostExpenditure.getAfterSaleConfirmAmount());
                            erpOrderRefundCostExpenditureMapper.updateErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditureUpdate);
                        }
                    }
                }
            } else if (Arrays.asList(3).contains(currentUserRole) && StringUtils.isNotEmpty(erpOrderRefundCostExpenditure.getApproveAttachment())) {
                ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditureAdd = new ErpOrderRefundCostExpenditure();
                erpOrderRefundCostExpenditureAdd.setGrossExpenditure(BigDecimal.ZERO);
                erpOrderRefundCostExpenditureAdd.setApproveId(erpExamineApproveDTO.getId());
                erpOrderRefundCostExpenditureAdd.setApproveUserId(SecurityUtils.getUserId());
                erpOrderRefundCostExpenditureAdd.setCreateUser(SecurityUtils.getUserId());
                erpOrderRefundCostExpenditureAdd.setApproveAttachment(erpOrderRefundCostExpenditure.getApproveAttachment());
                erpOrderRefundCostExpenditureMapper.insertErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditureAdd);
            }
        } else if (ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_REFUSE == addType && StringUtils.isNotEmpty(erpOrderRefundCostExpenditure.getApproveAttachment())) {
            ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditureAdd = new ErpOrderRefundCostExpenditure();
            erpOrderRefundCostExpenditureAdd.setGrossExpenditure(BigDecimal.ZERO);
            erpOrderRefundCostExpenditureAdd.setApproveId(erpExamineApproveDTO.getId());
            erpOrderRefundCostExpenditureAdd.setApproveUserId(SecurityUtils.getUserId());
            erpOrderRefundCostExpenditureAdd.setCreateUser(SecurityUtils.getUserId());
            erpOrderRefundCostExpenditureAdd.setApproveAttachment(erpOrderRefundCostExpenditure.getApproveAttachment());
            erpOrderRefundCostExpenditureMapper.insertErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditureAdd);
        }
    }

    private void sendDingDingMsg(ErpOrderRefund erpOrderRefund) {
        //发送钉钉通知 //找到需要通知的人
        Set<String> userIds = new HashSet<>();
        ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
        erpServiceOrders.setNumOrderId(erpOrderRefund.getOrderId());
        List<SServiceMain> sServiceMainList = sServiceMainMapper.selectSServiceMainListByOrderId(erpOrderRefund.getOrderId());
        List<ErpServiceOrders> erpServiceOrdersList = erpServiceOrdersMapper.selectErpServiceOrdersList(erpServiceOrders);
        for (ErpServiceOrders serviceOrders : erpServiceOrdersList) {
            if (serviceOrders.getNumPayPrice().compareTo(serviceOrders.getNumRefundPrice()) == 0){
                //获取跟进人
                if (CollUtil.isNotEmpty(sServiceMainList)){
                    SServiceMain sServiceMain = sServiceMainList.stream()
                            .filter(en -> en.getProductId().equals(serviceOrders.getNumProductId()))
                            .findAny().orElse(null);
                    if (ObjectUtil.isNotEmpty(sServiceMain)){
                        assert sServiceMain != null;
                        userIds.add(String.valueOf(sServiceMain.getUserId()));
                        //TODO 找主管
                    }
                }
            }
        }
        //发送钉钉通知
        String userIdStr = String.join(",", userIds);
        String dingContent = String.format(OrderRefundConstants.DING_REFUND_NOTICE, erpOrderRefund.getOrderNum());
        DingSendDTO dingSendDTO = new DingSendDTO(userIdStr, "服务单全额退款提醒", dingContent);
        dingDingService.sendDingMessage(dingSendDTO);
    }




    /***
     * 审批流撤销，更新otherId的业务方法
     */
    private int approveCancel(ErpExamineApprove erpExamineApprove) {
        int canCancel = 1;
        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType().toString()));
        int parentId = Integer.parseInt(erpExamineApproveTypeManage.getParentId().toString());
        if (ErpExamineApproveConstants.APPROVE_CATEGORY_PRODUCT == parentId) {
            throw new ServiceException("当前审批流不可撤销");
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_BUDGET == parentId || ErpExamineApproveConstants.APPROVE_CATEGORY_ACCOUNT == parentId) {
            if (erpExamineApprove.getApproveStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                throw new ServiceException("当前审批流不可撤销");
            }
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_NO_ORDER_REFUND == parentId) {
            if (erpExamineApprove.getApproveStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                throw new ServiceException("当前审批流不可撤销");
            } else {
                ErpExamineApproveCommonPayment commonPayment = erpExamineApproveCommonPaymentMapper.selectErpExamineApproveCommonPaymentById(Long.valueOf(erpExamineApprove.getOtherId()));
                ErpTransactionVoucher transactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(commonPayment.getVoucherId());
                transactionVoucher.setBalanceUse(transactionVoucher.getBalanceUse().add(commonPayment.getFee()));
                transactionVoucher.setBalance(transactionVoucher.getBalance().add(commonPayment.getFee()));
                if (transactionVoucher.getType() == 2){
                    ErpTransactionVoucher voucherGive=erpTransactionVoucherMapper.selectErpTransactionVoucherBySelfId(transactionVoucher.getId());
                    voucherGive.setApproveIn(2);
                    voucherGive.setSelfId(null);
                    erpTransactionVoucherMapper.updateErpTransactionVoucher(voucherGive);
                }
                erpTransactionVoucherMapper.updateErpTransactionVoucher(transactionVoucher);
            }
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS == parentId) {
            if (erpExamineApprove.getApproveStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT) {
                ErpExamineAccountLs examineAccountLs = erpExamineAccountLsMapper.selectErpExamineAccountLsById(Long.valueOf(erpExamineApprove.getOtherId()));
                SServiceMain serviceMainLs = sServiceMainMapper.selectSServiceMainById(examineAccountLs.getServiceId());
                serviceMainLs.setServicePointStatus(examineAccountLs.getOldPointStatus());
                sServiceMainMapper.updateSServiceMain(serviceMainLs);

                //更新所有历史记账服务
                sServiceMainMapper.updateSServiceMainByEnterPriseIdAndServiceType(serviceMainLs.getServicePointStatus(),serviceMainLs.getNumEnterpriseId(),serviceMainLs.getServiceType());
            } else {
                throw new ServiceException("当前审批流不可撤销");
            }
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_DLS_JS == parentId) {
            if (erpExamineApprove.getApproveStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                throw new ServiceException("当前审批流不可撤销");
            }
            ErpExamineOtherOrderPayVo erpExamineOtherOrderPayVo = erpExamineApproveMapper.getOtherOrderPayVo(Long.valueOf(erpExamineApprove.getId()));
            if (ObjectUtil.isNotEmpty(erpExamineOtherOrderPayVo.getWtdzIds())) {
                List<Long> idList = Arrays.stream(erpExamineOtherOrderPayVo.getWtdzIds().split(",")).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
                for (int i = 0; i < idList.size(); i++) {
                    ErpWtdzKp erpWtdzKp = new ErpWtdzKp();
                    erpWtdzKp.setId(idList.get(i));
                    erpWtdzKp.setSettlement(2);
                    wtdzKpMapper.updateErpWtdzKp(erpWtdzKp);
                }
            }
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_SUPPLEMENT == parentId) {
            erpExamineApprove.setSupplementStatus(ApprovalExamineStatusEnum.REVOKE_APPROVAL.getType());
            ErpQzdApprovalService erpQzdApprovalServiceUpdate = new ErpQzdApprovalService();
            erpQzdApprovalServiceUpdate.setId(Long.valueOf(erpExamineApprove.getOtherId()));
            erpQzdApprovalServiceUpdate.setSupplementStatus(3);
            erpQzdApprovalServiceMapper.updateErpQzdApprovalService(erpQzdApprovalServiceUpdate);
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_CANCEL == parentId) {
            if (erpExamineApprove.getApproveStatus().intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                throw new ServiceException("当前审批流不可撤销");
            } else {
                erpExamineApproveMapper.updateRevokeStatusById(Long.parseLong(erpExamineApprove.getOtherId()));
            }
        } else if (ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REPORT == parentId) {
            if (erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                throw new ServiceException("当前审批流已被通过");
            }
            ErpOrderPayRecord erpOrderPayRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(Long.valueOf(erpExamineApprove.getOtherId()));

            ErpExamineApproveDTO oldReportDto = new ErpExamineApproveDTO();
            oldReportDto.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_ORDER_REPORT);
            oldReportDto.setOtherId(erpExamineApprove.getOtherId());
            oldReportDto.setApproveStatus(2);
            List<ErpExamineApprove> oldReportList = erpExamineApproveMapper.selectErpExamineApproveList(oldReportDto);
            if (oldReportList.size() > 0) {
                erpOrderPayRecord.setReportReviewStatus(2);
            } else {
                erpOrderPayRecord.setReportReviewStatus(-1);
                erpOrderPayRecord.setSubmittedReport(ReportConstants.NO_REPORT);
                erpOrderPayRecord.setReportUserId(0L);
                erpOrderPayRecord.setReportRemark("");
                erpOrderPayRecord.setCollectionCategory(ReportConstants.NOTHING);
            }
//            if (erpOrderPayRecord.getPaymentType() == 0) {
//                erpOrderPayRecordMapper.updateApproveStatusByBillNo(erpOrderPayRecord.getBillNo(), erpOrderPayRecord.getReportReviewStatus(), erpOrderPayRecord.getReportedOrNot());
//            } else {
            erpOrderPayRecordMapper.updateErpOrderPayRecord(erpOrderPayRecord);
//            }
        } else {
            if (erpExamineApprove.getApproveStatus() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS) {
                ErpExamineApproveDTO erpExamineApprove_ = new ErpExamineApproveDTO();
                erpExamineApprove_.setOtherId(erpExamineApprove.getId().toString());
                erpExamineApprove_.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_CANCEL);
                //存在审批通过和待审批的不可提交
                erpExamineApprove_.setApproveStatusList(
                        Arrays.asList(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT,
                                ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_PASS)
                );

                List<ErpExamineApprove> list = erpExamineApproveMapper.selectErpExamineApproveList(erpExamineApprove_);
                if (list.size() > 0) {
                    throw new ServiceException("当前审批流已存在撤销审批");
                }
                canCancel = 0;
                cancelErpExamineApprove(erpExamineApprove.getId());
                erpExamineApprove.setRevokeStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_REVOKE_STATUS_NEW);
            }
        }

        if (ObjectUtils.isNotEmpty(erpExamineApproveTypeManage.getBudgetId())) {
            budgetReturn(erpExamineApprove.getId());
        }
        //记录日志
        ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.valueOf(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.REVOKE_APPROVAL.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
        erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);
        return canCancel;
    }

    /***
     * 审批流拒绝，更新otherId的业务方法
     */
    private void approveRefuse(ErpExamineApprove erpExamineApprove, Integer operateStatus) {
        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(erpExamineApprove.getApproveType().toString()));
        int parentId = Integer.parseInt(erpExamineApproveTypeManage.getParentId().toString());
        switch (parentId) {
            case ErpExamineApproveConstants.APPROVE_CATEGORY_PROCURE_CONTRACT:
                //采购合同
                ErpProcureContract erpProcureContract = erpProcureContractMapper.selectErpProcureContractById(Long.parseLong(erpExamineApprove.getOtherId()));
                erpProcureContract.setStatus(3);
                erpProcureContractMapper.updateErpProcureContract(erpProcureContract);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ADVANCE:
                //预付单审批通过
                ErpBizServiceAdvance erpBizServiceAdvance = erpBizServiceAdvanceMapper.selectErpBizServiceAdvanceById(Integer.parseInt(erpExamineApprove.getOtherId()));
                erpBizServiceAdvance.setStatus(4);
                erpBizServiceAdvanceMapper.updateErpBizServiceAdvance(erpBizServiceAdvance);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_CANCEL:
                //撤销审批
                ErpExamineApprove approveCancel = erpExamineApproveMapper.selectErpExamineApproveById(Long.parseLong(erpExamineApprove.getOtherId()));
                approveCancel.setRevokeStatus(ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_REVOKE_STATUS_REFUSE);
                erpExamineApproveMapper.updateErpExamineApprove(approveCancel);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_PRODUCT:
                //撤销审批
                //产品审批通过
                ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(Long.parseLong(erpExamineApprove.getOtherId()));
                if (erpExamineApprove.getApproveType().intValue() == ErpExamineApproveConstants.APPROVE_TYPE_PRODUCT_UPDATE) {
                    erpProductDetail.setNumIsCheck(2L);
                    if (erpProductDetail.getApproveInfoId().intValue() != 0) {
                        ErpProductApprove productApprove = erpProductApproveMapper.selectErpProductApproveById(erpProductDetail.getApproveInfoId());
                        erpProductDetail.setNumIsUp(productApprove.getNumIsUp());
                    }
                }
                erpProductDetail.setApproveInfoId(0L);
                erpProductDetailMapper.updateErpProductDetail(erpProductDetail);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_CHENGBEN:
                CostSettlement costSettlement = costSettlementMapper.selectCostSettlementById(Integer.parseInt(erpExamineApprove.getOtherId()));
                if (costSettlement.getAdvanceId()!=null) {
                    ErpBizServiceAdvance advanceCost = new ErpBizServiceAdvance();
                    advanceCost.setId(costSettlement.getAdvanceId().intValue());
                    advanceCost.setHxStatus(2);
                    advanceCost.setUpdatedBy(SecurityUtils.getUserId());
                    advanceCost.setUpdatedAt(new Date());
                    int bizServiceAdvance = erpBizServiceAdvanceMapper.updateErpBizServiceAdvance(advanceCost);
                    if (bizServiceAdvance <= 0) {
                        throw new ServiceException("更新预付款核销状态失败");
                    }
                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_QZD_SUPPLEMENT:
                erpExamineApprove.setSupplementStatus(ApprovalExamineStatusEnum.APPROVAL_REJECTION.getType());
                ErpQzdApprovalService erpQzdApprovalServiceUpdate = new ErpQzdApprovalService();
                erpQzdApprovalServiceUpdate.setId(Long.valueOf(erpExamineApprove.getOtherId()));
                erpQzdApprovalServiceUpdate.setSupplementStatus(2);
                erpQzdApprovalServiceMapper.updateErpQzdApprovalService(erpQzdApprovalServiceUpdate);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REFUND:
                //更改订单状态，service_orders状态，服务单状态，退款表状态
                //查询退款信息
                ErpOrderRefund erpOrderRefund = erpOrderRefundMapper
                        .selectErpOrderRefundById(Long.valueOf(erpExamineApprove.getOtherId()));
                //查询退款明细
                ErpOrderRefundDetail detail = new ErpOrderRefundDetail().setOrderRefundId(erpOrderRefund.getId());
                List<ErpOrderRefundDetail> detailList = erpOrderRefundDetailMapper.selectErpOrderRefundDetailList(detail);
                for (ErpOrderRefundDetail refundDetail : detailList) {
                    refundDetail.setVoidStatus(refundDetail.getVoidStatusBefore());
                    //更新服务单
                    sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                            erpOrderRefund.getOrderId(), refundDetail.getProductId(),
                            refundDetail.getServiceStatusBefore());
                    //更新serviceOrders
                    erpServiceOrdersMapper.updateNumIsDeprecatedById(refundDetail.getServiceOrdersId(), refundDetail.getVoidStatusBefore());
                }
                //更新退款明细
                mybatisBatchUtils.batchUpdateOrInsert(detailList, ErpOrderRefundDetailMapper.class,
                        (refundDetail, erpOrderRefundDetailMapper) ->
                                erpOrderRefundDetailMapper.updateErpOrderRefundDetail(refundDetail));
                //更新订单状态
                erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                        erpOrderRefund.getOrderStatusBefore().intValue());
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_REPORT:
                ErpOrderPayRecord erpOrderPayRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(Long.valueOf(erpExamineApprove.getOtherId()));
                erpOrderPayRecord.setReportReviewStatus(2);

                erpOrderPayRecord.setSubmittedReport(ReportConstants.NO_REPORT);
                erpOrderPayRecord.setReportUserId(0L);
                erpOrderPayRecord.setReportRemark("");
                erpOrderPayRecord.setCollectionCategory(ReportConstants.NOTHING);
//                if (erpOrderPayRecord.getPaymentType() == 0) {
//                    erpOrderPayRecordMapper.updateApproveStatusByBillNo(erpOrderPayRecord.getBillNo(), erpOrderPayRecord.getReportReviewStatus(), erpOrderPayRecord.getReportedOrNot());
//                } else {
                erpOrderPayRecordMapper.updateErpOrderPayRecord(erpOrderPayRecord);
                erpTransactionVoucherMapper.deleteByPayRecordId(Long.parseLong(erpOrderPayRecord.getId()));
//                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_JZ_LS:
                ErpExamineAccountLs examineAccountLs = erpExamineAccountLsMapper.selectErpExamineAccountLsById(Long.valueOf(erpExamineApprove.getOtherId()));
                SServiceMain serviceMainLs = sServiceMainMapper.selectSServiceMainById(examineAccountLs.getServiceId());
                serviceMainLs.setServicePointStatus(examineAccountLs.getOldPointStatus());
                sServiceMainMapper.updateSServiceMain(serviceMainLs);

                //更新所有历史记账服务
                sServiceMainMapper.updateSServiceMainByEnterPriseIdAndServiceType(serviceMainLs.getServicePointStatus(),serviceMainLs.getNumEnterpriseId(),serviceMainLs.getServiceType());

                if (operateStatus.intValue() == ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_OPERATE_STATUS_SAVE) {
                    LoginUser loginUser = tokenService.getLoginUser();
                    examineAccountLs.setSaveTime(new Date());
                    examineAccountLs.setSaveStatus(5L);
                    examineAccountLs.setSaveUser(loginUser.getSysUser().getUserId());
                    erpExamineAccountLsMapper.updateErpExamineAccountLs(examineAccountLs);
                }
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_NO_ORDER_REFUND:
                ErpExamineApproveCommonPayment commonPayment = erpExamineApproveCommonPaymentMapper.selectErpExamineApproveCommonPaymentById(Long.valueOf(erpExamineApprove.getOtherId()));
                ErpTransactionVoucher transactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(commonPayment.getVoucherId());
                transactionVoucher.setBalanceUse(transactionVoucher.getBalanceUse().add(commonPayment.getFee()));
                transactionVoucher.setBalance(transactionVoucher.getBalance().add(commonPayment.getFee()));
                if (transactionVoucher.getType() == 2) {
                    ErpTransactionVoucher voucherGive = erpTransactionVoucherMapper.selectErpTransactionVoucherBySelfId(transactionVoucher.getId());
                    voucherGive.setApproveIn(2);
                    voucherGive.setSelfId(null);
                    erpTransactionVoucherMapper.updateErpTransactionVoucher(voucherGive);
                }
                erpTransactionVoucherMapper.updateErpTransactionVoucher(transactionVoucher);
                break;
            case ErpExamineApproveConstants.APPROVE_CATEGORY_DLS_JS:

                ErpExamineOtherOrderPayVo erpExamineOtherOrderPayVo = erpExamineApproveMapper.getOtherOrderPayVo(Long.valueOf(erpExamineApprove.getId()));
                if (ObjectUtil.isNotEmpty(erpExamineOtherOrderPayVo.getWtdzIds())) {
                    List<Long> idList = Arrays.stream(erpExamineOtherOrderPayVo.getWtdzIds().split(",")).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
                    for (int i = 0; i < idList.size(); i++) {
                        ErpWtdzKp erpWtdzKp = new ErpWtdzKp();
                        erpWtdzKp.setId(idList.get(i));
                        erpWtdzKp.setSettlement(2);
                        wtdzKpMapper.updateErpWtdzKp(erpWtdzKp);
                    }
                }
                break;
        }
        if (ObjectUtils.isNotEmpty(erpExamineApproveTypeManage.getBudgetId())) {
            budgetReturn(erpExamineApprove.getId());
        }
        //记录日志
        ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.valueOf(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.APPROVAL_REJECTION.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
        erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);
    }

    /***
     *  撤销审批
     *  开票审批撤销走单独审批流
     *  其他审批撤销走原审批的审批流
     */
    void cancelErpExamineApprove(Long approveId) {
        ErpExamineApprove erpExamineApprove = new ErpExamineApprove();
        ErpExamineApprove approve = erpExamineApproveMapper.selectErpExamineApproveById(approveId);

        //获取哪个类型的审批流
        Integer personApprove = approve.getApproveType();
        ErpExamineApproveTypeManage erpExamineApproveTypeManage = erpExamineApproveTypeManageMapper.selectErpExamineApproveTypeManageById(Long.parseLong(approve.getApproveType().toString()));
        if (Integer.parseInt(erpExamineApproveTypeManage.getParentId().toString()) == ErpExamineApproveConstants.APPROVE_CATEGORY_ORDER_KP) {
            personApprove = ErpExamineApproveConstants.APPROVE_TYPE_ORDER_KP_CANCEL;
        }

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();

        ErpExamineApproveTypePersionDto dto = new ErpExamineApproveTypePersionDto();
        dto.setApproveType(personApprove);
        dto.setStatus(1);
        List<ErpExamineApproveTypePersionVo> list = erpExamineApproveTypePersionMapper.selectErpExamineApproveTypePersionList(dto);

        for (int i = 0; i < list.size(); i++) {
            ErpExamineApproveTypePersionVo vo = list.get(i);
            JSONArray arr = JSONArray.fromObject(vo.getDeptIds());
            if (arr.contains(sysUser.getDeptId())) {

                String followInfoStr = vo.getFollowInfo();
                if (ObjectUtil.isNull(vo.getFollowInfo())) {
                    throw new ServiceException("审批流为空");
                }

                //获取审批流
                JSONArray arr_ = JSONArray.fromObject(followInfoStr);
                if (ObjectUtil.isNull(arr_) || arr_.size() == 0) {
                    throw new ServiceException("审批流错误");
                }
                for (int j = 0; j < arr_.size(); j++) {
                    arr_.getJSONObject(j).put("approve_status", ErpExamineApproveConstants.ERP_EXAMINE_APPROVE_STATUS_WAIT);
                }

                int currentApproveUserId = arr_.getJSONObject(0).getInt("approveUser");
                erpExamineApprove.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_CANCEL);
                erpExamineApprove.setOtherId(approveId.toString());
                erpExamineApprove.setFollowInfo(arr_.toString());
                erpExamineApprove.setApproveCurrentUser(currentApproveUserId);
                erpExamineApprove.setCreatedUser(sysUser.getUserId().intValue());
                erpExamineApprove.setUpdatedUser(sysUser.getUserId().intValue());
                erpExamineApprove.setMakeCopyUser(",1,");
                erpExamineApprove.setCreatedDate(new Date());
                erpExamineApprove.setUpdatedTime(new Date());
                erpExamineApproveMapper.insertErpExamineApprove(erpExamineApprove);

                R<SysUser> info = remoteUserService.getUserInfoById(Long.parseLong(currentApproveUserId+""), SecurityConstants.INNER);
                //发送钉钉消息
                String dingContent = "### 审批提醒： \n * " + "您好： \n "
                        +" * 审批类型: 审批撤销    \n "
                        +" * 审批ID: " + erpExamineApprove.getId() + " 待审批";
                DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "审批提醒", dingContent);
                dingDingService.sendDingMessage(dingSendDTO);

                //记录日志  TODO  审批类型待加
                ErpQzdApprovalLog erpQzdApprovalLog = new ErpQzdApprovalLog(Long.parseLong(erpExamineApprove.getOtherId()), erpExamineApprove.getId(), erpExamineApprove.getApproveType().longValue(), QzdApprovalLogEnum.SUBMIT_APPROVAL.getType().longValue(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
                erpQzdApprovalLogService.addErpQzdApprovalLog(erpQzdApprovalLog);
                return;
            }
        }
        throw new ServiceException("暂无权限，请联系财务增加该类型下的部门");
    }


    private MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = new FileInputStream(file);
             OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }

        return new CommonsMultipartFile(item);
    }

    /***
     * 消耗预算的审批，驳回撤销，预算回退
     */
    public void budgetReturn(Long approveId) {
        ErpExamineBudgetDetailConsume erpExamineBudgetDetailConsume = new ErpExamineBudgetDetailConsume();
        erpExamineBudgetDetailConsume.setStatus(1);
        erpExamineBudgetDetailConsume.setApproveId(approveId);
        List<ErpExamineBudgetDetailConsume> consumeList = erpExamineBudgetDetailConsumeMapper.selectErpExamineBudgetDetailConsumeList(erpExamineBudgetDetailConsume);
        if (ObjectUtil.isNotEmpty(consumeList) && consumeList.size() > 0) {
            for (int i = 0; i < consumeList.size(); i++) {
                ErpExamineBudgetDetailConsume consume = consumeList.get(i);
                consume.setStatus(2);
                erpExamineBudgetDetailConsumeMapper.updateErpExamineBudgetDetailConsume(consume);

                ErpExamineBudgetDetail budgetDetail = erpExamineBudgetDetailMapper.selectErpExamineBudgetDetailById(consume.getBudgetDetailId());
                budgetDetail.setBudgetBalance(budgetDetail.getBudgetBalance().add(consume.getFee()));
                erpExamineBudgetDetailMapper.updateErpExamineBudgetDetail(budgetDetail);
            }
        }
    }
}
