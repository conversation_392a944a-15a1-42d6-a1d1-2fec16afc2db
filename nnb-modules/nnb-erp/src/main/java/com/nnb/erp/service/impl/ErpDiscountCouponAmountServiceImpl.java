package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.erp.domain.dto.ErpDiscountCouponAmountDto;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpDiscountCouponAmountMapper;
import com.nnb.erp.domain.ErpDiscountCouponAmount;
import com.nnb.erp.service.IErpDiscountCouponAmountService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 优惠券额度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class ErpDiscountCouponAmountServiceImpl implements IErpDiscountCouponAmountService 
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private ErpDiscountCouponAmountMapper erpDiscountCouponAmountMapper;

    @Autowired
    private ErpDiscountCouponLogServiceImpl erpDiscountCouponLogService;

    /**
     * 查询优惠券额度
     * 
     * @param id 优惠券额度主键
     * @return 优惠券额度
     */
    @Override
    public ErpDiscountCouponAmount selectErpDiscountCouponAmountById(Long id)
    {
        return erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(id);
    }

    /**
     * 查询优惠券额度列表
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 优惠券额度
     */
    @Override
    public List<ErpDiscountCouponAmount> selectErpDiscountCouponAmountList(ErpDiscountCouponAmount erpDiscountCouponAmount)
    {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if ("admin".equals(sysUser.getUserName())){
            erpDiscountCouponAmount.setBelongUserId(null);
        }
        List<ErpDiscountCouponAmount> amounts = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountList(erpDiscountCouponAmount);
        for (ErpDiscountCouponAmount amount : amounts) {
            if (ObjectUtil.isEmpty(amount.getUnusedCouponAmount())){
                amount.setUnusedCouponAmount(0L);
            }
        }
        return amounts;
    }

    /**
     * 新增优惠券额度
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 结果
     */
    @Override
    public int insertErpDiscountCouponAmount(ErpDiscountCouponAmount erpDiscountCouponAmount)
    {
        erpDiscountCouponAmount.setCreateTime(DateUtils.getNowDate());
        return erpDiscountCouponAmountMapper.insertErpDiscountCouponAmount(erpDiscountCouponAmount);
    }

    /**
     * 修改优惠券额度
     * 
     * @param erpDiscountCouponAmount 优惠券额度
     * @return 结果
     */
    @Override
    public int updateErpDiscountCouponAmount(ErpDiscountCouponAmount erpDiscountCouponAmount)
    {
        erpDiscountCouponAmount.setUpdateTime(DateUtils.getNowDate());
        return erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(erpDiscountCouponAmount);
    }

    /**
     * 批量删除优惠券额度
     * 
     * @param ids 需要删除的优惠券额度主键
     * @return 结果
     */
    @Override
    public int deleteErpDiscountCouponAmountByIds(Long[] ids)
    {
        return erpDiscountCouponAmountMapper.deleteErpDiscountCouponAmountByIds(ids);
    }

    /**
     * 删除优惠券额度信息
     * 
     * @param id 优惠券额度主键
     * @return 结果
     */
    @Override
    public int deleteErpDiscountCouponAmountById(Long id)
    {
        return erpDiscountCouponAmountMapper.deleteErpDiscountCouponAmountById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int distributeAmount(ErpDiscountCouponAmountDto erpDiscountCouponAmountDto) {
        Long id = erpDiscountCouponAmountDto.getId();
        ErpDiscountCouponAmount erpDiscountCouponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(id);
        //生成新的优惠券额度分配记录
        if (Objects.nonNull(erpDiscountCouponAmount)) {
            BigDecimal discountCouponAmount = erpDiscountCouponAmount.getDiscountCouponAmount();
            BigDecimal usingAmount = erpDiscountCouponAmount.getUsingAmount();//分配前的已使用额度
            BigDecimal surplusAmount = erpDiscountCouponAmount.getSurplusAmount();//分配前的剩余额度
            if (surplusAmount.compareTo(BigDecimal.ZERO) == 0 || surplusAmount.compareTo(erpDiscountCouponAmountDto.getDistributeCouponAmount()) < 0) {
                throw new ServiceException("分配额度不足，分配失败");
            }
            BigDecimal mergeAmount = BigDecimal.ZERO;
            mergeAmount = mergeAmount(erpDiscountCouponAmountDto.getBelongUserId());
            mergeAmount = mergeAmount.add(erpDiscountCouponAmountDto.getDistributeCouponAmount());
            ErpDiscountCouponAmount newErpDiscountCouponAmount = new ErpDiscountCouponAmount();
            newErpDiscountCouponAmount.setDiscountCouponAmount(mergeAmount);
            newErpDiscountCouponAmount.setUsingAmount(BigDecimal.ZERO);
            newErpDiscountCouponAmount.setSurplusAmount(mergeAmount);
            newErpDiscountCouponAmount.setProduceCouponAmount(0L);
            newErpDiscountCouponAmount.setUnusedCouponAmount(0L);
            newErpDiscountCouponAmount.setCouponStatus(1L);
            newErpDiscountCouponAmount.setBelongUserId(erpDiscountCouponAmountDto.getBelongUserId());
            newErpDiscountCouponAmount.setCreateUser(SecurityUtils.getUserId());
            newErpDiscountCouponAmount.setUpdateUser(SecurityUtils.getUserId());

            //更新分配的已使用额度和剩余额度
            ErpDiscountCouponAmount oldErpDiscountCouponAmount = new ErpDiscountCouponAmount();
            oldErpDiscountCouponAmount.setId(id);
            oldErpDiscountCouponAmount.setUsingAmount(usingAmount.add(erpDiscountCouponAmountDto.getDistributeCouponAmount()));
            oldErpDiscountCouponAmount.setSurplusAmount(surplusAmount.subtract(erpDiscountCouponAmountDto.getDistributeCouponAmount()));
            ErpDiscountCouponAmount erpDiscountCoupon = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(id);//分配前的优惠券额度
            BigDecimal discountCouponSurplusAmount = erpDiscountCoupon.getSurplusAmount();//分配前的可使用额度
            //乐观锁
            if (surplusAmount.compareTo(discountCouponSurplusAmount) != 0) {
                throw new ServiceException("分配额度变更，分配失败");
            }
            int result = erpDiscountCouponAmountMapper.insertErpDiscountCouponAmount(newErpDiscountCouponAmount);
            int update = erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(oldErpDiscountCouponAmount);
            if (result <= 0 || update <= 0) {
                throw new ServiceException("额度分配失败");
            }
            erpDiscountCouponLogService.saveLog(SecurityUtils.getUserId(), ErpProductConstants.DiscountCouponLogStatus.DISTRIBUTE, erpDiscountCouponAmountDto.getBelongUserId());
            return result;
        }
        return 0;
    }

    /**
     * 合并剩余额度大于0的优惠券额度
     * @param belongUserId
     * @return
     */
    private BigDecimal mergeAmount(Long belongUserId) {
        BigDecimal mergeAmount = BigDecimal.ZERO;
        ErpDiscountCouponAmount erpDiscountCouponAmount = new ErpDiscountCouponAmount();
        erpDiscountCouponAmount.setBelongUserId(belongUserId);
        erpDiscountCouponAmount.setCouponStatus(1L);
        List<ErpDiscountCouponAmount> erpDiscountCouponAmounts = erpDiscountCouponAmountMapper.getDiscountCouponAmountList(erpDiscountCouponAmount);
        if (CollectionUtils.isNotEmpty(erpDiscountCouponAmounts)) {
            List<ErpDiscountCouponAmount> collect = erpDiscountCouponAmounts.stream().filter(val -> val.getSurplusAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                for (ErpDiscountCouponAmount discountCouponAmount : collect) {
                    BigDecimal surplusAmount = discountCouponAmount.getSurplusAmount();
                    BigDecimal usingAmount = discountCouponAmount.getUsingAmount();
                    usingAmount = usingAmount.add(surplusAmount);
                    mergeAmount = mergeAmount.add(surplusAmount);
                    ErpDiscountCouponAmount erpDiscountCouponAmountUpdate = new ErpDiscountCouponAmount();
                    erpDiscountCouponAmountUpdate.setId(discountCouponAmount.getId());
                    erpDiscountCouponAmountUpdate.setUsingAmount(usingAmount);
                    erpDiscountCouponAmountUpdate.setSurplusAmount(BigDecimal.ZERO);
                    int result = erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(erpDiscountCouponAmountUpdate);
                    if(result <= 0){
                        throw new ServiceException("合并优惠券额度失败");
                    }
                }
            }
        }

        return mergeAmount;
    }


    @Override
    public void clearAmount() {
        ErpDiscountCouponAmount erpDiscountCouponAmount = new ErpDiscountCouponAmount();
        erpDiscountCouponAmount.setCouponStatus(1L);
        List<ErpDiscountCouponAmount> erpDiscountCouponAmounts = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountList(erpDiscountCouponAmount);
        if (CollectionUtils.isNotEmpty(erpDiscountCouponAmounts)) {
            erpDiscountCouponAmounts.forEach(val -> {
                ErpDiscountCouponAmount discountCouponAmount = new ErpDiscountCouponAmount();
                discountCouponAmount.setId(val.getId());
                discountCouponAmount.setCouponStatus(0L);
                discountCouponAmount.setDiscountCouponAmount(BigDecimal.ZERO);
                discountCouponAmount.setUsingAmount(BigDecimal.ZERO);
                discountCouponAmount.setSurplusAmount(BigDecimal.ZERO);
                erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(discountCouponAmount);
            });
        }
    }

}
