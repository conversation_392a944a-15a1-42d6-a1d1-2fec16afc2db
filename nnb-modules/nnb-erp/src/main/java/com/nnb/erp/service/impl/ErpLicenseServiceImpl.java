package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpLicenseConstants;
import com.nnb.erp.constant.ServiceMainConstants;
import com.nnb.erp.constant.enums.GjStatusEnum;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dingding.DingSendDTO;
//import com.nnb.erp.domain.dto.ErpBizServiceAddressCostDto;
import com.nnb.erp.domain.dto.license.ErpClueLicenseDTO;
import com.nnb.erp.domain.dto.license.ErpLicenseDTO;
import com.nnb.erp.domain.dto.license.ErpLicenseSaleDto;
import com.nnb.erp.domain.dto.license.ErpLicenseTransferDTO;
import com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO;
import com.nnb.erp.domain.vo.ErpOrderQueryForOmListDTO;
import com.nnb.erp.domain.vo.license.*;
import com.nnb.erp.domain.vo.product.LinceseProductVo;
import com.nnb.erp.domain.vo.qzd.LicensePaymentVo;
import com.nnb.erp.domain.vo.qzd.LicenseSalesBoardVo;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.enums.ApprovalNodeEnum;
import com.nnb.erp.enums.ErplicenseExamineStatusEnum;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.approval.ErpQzdPaymentRecordMapper;
import com.nnb.erp.service.*;
import com.nnb.erp.util.ThreadPoolUtil;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.BdClue;
import com.nnb.system.api.model.BdClueContacts;
import com.nnb.system.api.model.LoginUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
@RefreshScope
@Service
@Slf4j
public class ErpLicenseServiceImpl implements IErpLicenseService 
{
    @Autowired
    private ErpLicenseMapper erpLicenseMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ErpLicenseLogMapper erpLicenseLogMapper;
    @Autowired
    private ErpLicenseProductMapper erpLicenseProductMapper;
    @Autowired
    private SServiceMainMapper sServiceMainMapper;
    @Autowired
    private ErpBizServiceAddressCostMapper erpBizServiceAddressCostMapper;
    @Autowired
    private IComDictRegionService iComDictRegionService;
    @Autowired
    private ErpLicenseTypeMapper erpLicenseTypeMapper;
    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Value("${erp.qizhaoduo.administrator.id}")
    private Long qizhaoduoAdministratorId;
    @Value("${erp.qizhaoduo.roleFollow.id}")
    private Long roleFollowId;
    @Value("${erp.qizhaoduo.roleOperation.id}")
    private Long roleOperationId;

    @Autowired
    private EmailService emailService;

    @Autowired
    private RemoteUserService userService;

    @Value("${erp.qizhaoduo.send.email}")
    private String erpqzdEmail;

    /**
     * 冯雪倩  钉钉  userid
     */
    @Value("${erp.qizhaoduo.send.dinguserid}")
    private String erpqzdDingUserId;

    @Autowired
    private ErpExamineApproveMapper erpExamineApproveMapper;

    @Autowired
    private DingDingService dingDingService;
    @Autowired
    private ErpQzdPaymentRecordMapper erpQzdPaymentRecordMapper;
    @Autowired
    private ErpOrdersMapper erpOrdersMapper;

    @Autowired
    private ErpOrderPaymentTermMapper erpOrderPaymentTermMapper;

    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private ErpClientMapper erpClientMapper;
    @Autowired
    private ErpProcureContractMapper erpProcureContractMapper;
    /***
     * 执照新增
     * @param erpLicenseDto
     * @return
     * wangyu20230313
     */
    @Override
    public int add(ErpLicenseDTO erpLicenseDto, int source) {
        if (StringUtils.isNull(erpLicenseDto)) {
            throw new ServiceException("参数错误");
        }

        if (StringUtils.isNull(erpLicenseDto.getClue()) && StringUtils.isNull(erpLicenseDto.getCompanyName())) {
            throw new ServiceException("新增执照，线索和公司名称不可同时为空");
        }
        //如果线索Id为空，则证明该线索为运营专员新增，默认线索Id为0
        if (StringUtils.isNull(erpLicenseDto.getClue())) { erpLicenseDto.setClue(0L); }

        //校验企业名称是否已存在
        if (StringUtils.isNotNull(erpLicenseDto.getCompanyName())) {
            if (Objects.nonNull(erpLicenseDto.getId())) {
                ErpLicense erpLicense = erpLicenseMapper.getLicenseByCompanyNameAndId(erpLicenseDto.getCompanyName(), erpLicenseDto.getId());
                if (Objects.nonNull(erpLicense)) {
                    throw new ServiceException("该企业已存在");
                }
            } else {
                ErpLicense _erpLicense = erpLicenseMapper.getLicenseByCompanyName(erpLicenseDto.getCompanyName());
                if (StringUtils.isNotNull(_erpLicense)) {
                    throw new ServiceException("该企业已存在");
                }
            }

        }

        LoginUser loginUser = tokenService.getLoginUser();
        if (StringUtils.isNull(loginUser)) {
            throw new ServiceException("登录错误");
        }

        List<Long> roleIds = new ArrayList<>();
        for (SysRole role : loginUser.getSysUser().getRoles()) {
            roleIds.add(role.getRoleId());
        }


        int maxNumber = erpLicenseMapper.getNumberMax();
        String number = DateUtils.getYear() + String.valueOf(maxNumber).substring(4);
        erpLicenseDto.setNumber(Integer.parseInt(number)+1);
        //意向池创建线索并且创建执照的时候，审核状态为待提交
        if(1 == source){
            erpLicenseDto.setExamineStatus(ErpLicenseConstants.TO_BE_SUBMITTED);
        }
        //(再次确认)或(确认完提交时)校验执照状态是否为待提交状态或驳回的状态
        if (Objects.nonNull(erpLicenseDto.getId())) {
            ErpLicense erpLicense = erpLicenseMapper.selectErpLicenseById(erpLicenseDto.getId());
            if (Objects.nonNull(erpLicenseDto.getMenuType()) && (Objects.nonNull(erpLicense) && ((ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED == erpLicense.getExamineStatus() ||
                    ErpLicenseConstants.QZD_TO_BE_REVIEWED == erpLicense.getExamineStatus() || ErpLicenseConstants.QZD_REVIEWED_PASS == erpLicense.getExamineStatus())))) {
                throw new ServiceException("执照状态错误!");
            }
        }
        //记账客户管理和增值组新增时 区分是提交还是确认
        if (((Objects.nonNull(erpLicenseDto.getAddSource())) && (1 == erpLicenseDto.getAddSource() || 2 == erpLicenseDto.getAddSource()))) {
            initExamineStatus(erpLicenseDto);
        }else {
            if (roleIds.contains(qizhaoduoAdministratorId) || roleIds.contains(roleOperationId)) {
                erpLicenseDto.setExamineStatus(ErpLicenseConstants.QZD_TO_BE_REVIEWED);
            } else {
                erpLicenseDto.setExamineStatus(ErpLicenseConstants.TO_BE_SUBMITTED);
            }
        }

        //运营专员和管理员新建时插入创建人和时间，跟进专员在提交审核时插入
        if (roleIds.contains(qizhaoduoAdministratorId)
                || roleIds.contains(roleOperationId)) {
            erpLicenseDto.setCreatedUser(Integer.parseInt(loginUser.getSysUser().getUserId().toString()));
            erpLicenseDto.setCreatedTime(new Date());
        }
        erpLicenseDto.setTotalPrice(countTotal(erpLicenseDto));

        return saveOrUpdate(erpLicenseDto, loginUser.getSysUser().getUserId());
    }

    /**
     * 设置审核状态
     * @param erpLicenseDto
     */
    private void initExamineStatus(ErpLicenseDTO erpLicenseDto) {
        if (Objects.isNull(erpLicenseDto.getMenuType())) {
            throw new ServiceException("操作类型不能为空");
        }
        Integer examineStatus = 0;
        switch (erpLicenseDto.getMenuType()) {
            case 1:
                examineStatus = ErpLicenseConstants.TO_BE_SUBMITTED;
                erpLicenseDto.setCreatedUser(SecurityUtils.getUserId().intValue());
                erpLicenseDto.setCreatedTime(new Date());
                break;
            case 2:
                examineStatus = ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED;
                break;
        }
        erpLicenseDto.setExamineStatus(examineStatus);
    }

    /***
     * 执照修改
     * @param erpLicenseDto
     * @return
     * wangyu20230313
     */
    @Override
    public int update(ErpLicenseDTO erpLicenseDto) {
        if (StringUtils.isNull(erpLicenseDto)) {
            throw new ServiceException("参数错误");
        }

        if (erpLicenseDto.getClue().intValue() == 0 && StringUtils.isNull(erpLicenseDto.getCompanyName())) {
            throw new ServiceException("新增执照，线索和公司名称不可同时为空");
        }

        //校验企业名称是否已存在
        if (!StringUtils.isNull(erpLicenseDto.getCompanyName())){
            ErpLicense _erpLicense = erpLicenseMapper.getLicenseByCompanyName(erpLicenseDto.getCompanyName());
            if (StringUtils.isNotNull(_erpLicense) && erpLicenseDto.getId().intValue() != _erpLicense.getId().intValue()) {
                throw new ServiceException("该企业已存在");
            }
        }

        LoginUser loginUser = tokenService.getLoginUser();
        if (StringUtils.isNull(loginUser)) {
            throw new ServiceException("登录错误");
        }

        erpLicenseDto.setUpdateUser(Integer.parseInt(loginUser.getSysUser().getUserId().toString()));
        Date now = new Date();
        erpLicenseDto.setUpdateTime(now);
        erpLicenseDto.setTotalPrice(countTotal(erpLicenseDto));

        return saveOrUpdate(erpLicenseDto, loginUser.getSysUser().getUserId());
    }

    /***
     * 计算总价
     * @param erpLicenseDto
     * @return
     */
    private BigDecimal countTotal(ErpLicenseDTO erpLicenseDto) {

        BigDecimal totalPrice = new BigDecimal("0");

        BigDecimal licensePrice = StringUtils.isNull(erpLicenseDto.getLicensePrice()) ? new BigDecimal("0") : erpLicenseDto.getLicensePrice();
        BigDecimal addressPrice = StringUtils.isNull(erpLicenseDto.getAddressPrice()) ? new BigDecimal("0") : erpLicenseDto.getAddressPrice();
        BigDecimal intermediationPrice = StringUtils.isNull(erpLicenseDto.getIntermediationPrice()) ? new BigDecimal("0") : erpLicenseDto.getIntermediationPrice();
        BigDecimal accountPrice = StringUtils.isNull(erpLicenseDto.getAccountPrice()) ? new BigDecimal("0") : erpLicenseDto.getAccountPrice();
        BigDecimal otherPrice = StringUtils.isNull(erpLicenseDto.getOtherPrice()) ? new BigDecimal("0") : erpLicenseDto.getOtherPrice();

        BigDecimal promotionPreferentialPrice = StringUtils.isNull(erpLicenseDto.getPromotionPreferentialPrice()) ? new BigDecimal("0") : erpLicenseDto.getPromotionPreferentialPrice();
        BigDecimal salePreferentialPrice = StringUtils.isNull(erpLicenseDto.getSalePreferentialPrice()) ? new BigDecimal("0") : erpLicenseDto.getSalePreferentialPrice();

        List<ErpLicenseProduct> erpLicenseProductList = erpLicenseDto.getErpLicenseProductList();
        if(StringUtils.isNotNull(erpLicenseProductList) && erpLicenseProductList.size() > 0) {
            for (ErpLicenseProduct erpLicenseProduct : erpLicenseProductList) {
                totalPrice = totalPrice.add(erpLicenseProduct.getProductPrice());
            }
        }
        return totalPrice.add(licensePrice).add(addressPrice).add(intermediationPrice).add(accountPrice).add(otherPrice).subtract(promotionPreferentialPrice).subtract(salePreferentialPrice);
    }

    private int saveOrUpdate(ErpLicenseDTO erpLicenseDto, Long userId) {

        int returnId = 0;
        ErpLicense erpLicense = new ErpLicense(
                erpLicenseDto.getNumber(),erpLicenseDto.getLicensePrice(),erpLicenseDto.getAddressPrice(),erpLicenseDto.getIntermediationPrice(),
                erpLicenseDto.getAccountPrice(),erpLicenseDto.getOtherPrice(),erpLicenseDto.getLicensePrimePrice(),
                erpLicenseDto.getAddressPrimePrice(),erpLicenseDto.getQualificationPrimePrice(),erpLicenseDto.getOtherPrimePrice(),
                erpLicenseDto.getCompanyName(),erpLicenseDto.getUnifiedSocialCreditCode(),erpLicenseDto.getLicenseCreatedTime(),
                erpLicenseDto.getLegalPersonName(),erpLicenseDto.getTaxType(),erpLicenseDto.getRegisteredCapital(),erpLicenseDto.getType(),
                erpLicenseDto.getTaxControlType(),erpLicenseDto.getLicenseSource(),erpLicenseDto.getLicenseSourcePersion(),erpLicenseDto.getTaxControlException(),
                erpLicenseDto.getBank(),erpLicenseDto.getAddressType(),erpLicenseDto.getLegalPersonPhone(),erpLicenseDto.getArea(),
                erpLicenseDto.getBusinessScope(),erpLicenseDto.getRemarks(),erpLicenseDto.getAppendix(),erpLicenseDto.getStatus(),
                erpLicenseDto.getDeleted(),erpLicenseDto.getGrounding(),erpLicenseDto.getGroundingTime(),erpLicenseDto.getClue(),
                erpLicenseDto.getOrderId(),erpLicenseDto.getCreatedTime(),erpLicenseDto.getCreatedUser(),erpLicenseDto.getUpdateUser(),
                erpLicenseDto.getUpdateTime(),erpLicenseDto.getRefuseRemarks(),erpLicenseDto.getAddressStartTime(),erpLicenseDto.getAddressEndTime(),
                erpLicenseDto.getRenewFee(),erpLicenseDto.getRenewMonth(),erpLicenseDto.getBankName(),
                erpLicenseDto.getAddrestCost(),erpLicenseDto.getTotalPrice(),erpLicenseDto.getAppendixZztp(), erpLicenseDto.getAppendixZxbg(),erpLicenseDto.getAppendixJdbg(),
                erpLicenseDto.getAppendixGqht(), erpLicenseDto.getAppendixZqxy(), erpLicenseDto.getInvoiceVersion(), erpLicenseDto.getInvoiceCopies(), erpLicenseDto.getTaxLevel(),
                erpLicenseDto.getInvoicing(), erpLicenseDto.getInvoicingNum(), erpLicenseDto.getSocialRegisterDate(), erpLicenseDto.getTaxPay(), erpLicenseDto.getBankStatement(),
                erpLicenseDto.getHasAppendixZxbg(), erpLicenseDto.getAccumulationFundRegisterDate(), erpLicenseDto.getJdResult(), erpLicenseDto.getJdResultMark(),
                erpLicenseDto.getExamineStatus(), erpLicenseDto.getAccountUser(), erpLicenseDto.getHistoryName(), erpLicenseDto.getShareholderType(), erpLicenseDto.getBudgetCost(),
                erpLicenseDto.getOutBuyFee(), erpLicenseDto.getLicenseAgent(), erpLicenseDto.getSelfRegistDate(), erpLicenseDto.getOutBuyDate(),
                erpLicenseDto.getSelfRegistFee(), erpLicenseDto.getSaleTime(), erpLicenseDto.getTaxControlPanel(), erpLicenseDto.getProcureContractNumber(),
                erpLicenseDto.getPromotionPreferentialPrice(), erpLicenseDto.getSalePreferentialPrice()
        );

        int erpLicenseId = StringUtils.isNull(erpLicenseDto.getId()) ? 0 : Integer.parseInt(erpLicenseDto.getId().toString());
//        int userId = StringUtils.isNull(erpLicenseDto.getId()) ? erpLicenseDto.getCreatedUser() : erpLicenseDto.getUpdateUser();

        String memo = "";
        if (erpLicenseId == 0) {
            erpLicense.setHistoryName(erpLicense.getCompanyName());
            returnId = erpLicenseMapper.insertErpLicense(erpLicense);
            memo = "新增执照："+erpLicense.getNumber();
            erpLicenseId = Integer.parseInt(erpLicense.getId().toString());
        } else {

            String procureContractNumber = erpLicenseMapper.selectProcureContractNumberById(erpLicenseDto.getId());
            if (ObjectUtil.isNotEmpty(procureContractNumber) && !procureContractNumber.equals(erpLicenseDto.getProcureContractNumber())) {
                erpLicenseMapper.updateProcureContractNumberByLicenseId(null, procureContractNumber);
            }

            erpLicense.setId(erpLicenseDto.getId());
            returnId = erpLicenseMapper.updateErpLicense(erpLicense);
            memo = "更新执照："+erpLicense.getNumber();
            if (returnId > 0) {
                erpLicenseProductMapper.deleteBylicenseId(erpLicense.getId());
            }
        }
        if (ObjectUtil.isNotEmpty(erpLicenseDto.getProcureContractNumber())) {
            ErpProcureContract erpProcureContract = erpProcureContractMapper.selectErpProcureContractByContractNumber(erpLicenseDto.getProcureContractNumber());
            if (ObjectUtil.isNotEmpty(erpProcureContract.getLicenseId()) && erpLicenseId != erpProcureContract.getLicenseId()) {
                throw new ServiceException("该采购合同已绑定执照!");
            }
            erpLicenseMapper.updateProcureContractNumberByLicenseId(erpLicense.getId(), erpLicenseDto.getProcureContractNumber());
        }


        if (returnId > 0) {
            //插入执照所绑定的产品
            List<ErpLicenseProduct> erpLicenseProductList = erpLicenseDto.getErpLicenseProductList();
            if (StringUtils.isNotNull(erpLicenseProductList) && erpLicenseProductList.size() > 0) {
                for (ErpLicenseProduct erpLicenseProduct : erpLicenseProductList) {
                    erpLicenseProductMapper.insertErpLicenseProduct(
                            new ErpLicenseProduct(
                                    erpLicenseId,
                                    erpLicenseProduct.getProductId(),
                                    erpLicenseProduct.getProductPrice()
                            )
                    );
                }
            }

            //记录日志
            erpLicenseLogMapper.insertErpLicenseLog(new ErpLicenseLog(
                    Long.parseLong(erpLicenseId+""),
                    Integer.parseInt(userId.toString()),
                    memo,
                    JSONObject.toJSONString(erpLicense)
            ));
        }

        return returnId;
    }


    /***
     * 执照提交审核
     * @param id
     * @param submitSource  提审来源1跟进专员,2运营专员3主管
     * @return
     * wangyu20230313
     */
    @Override
    public int submitLicense(Long id, int submitSource) {
        if (StringUtils.isNull(id) || StringUtils.isNull(submitSource)) {
            throw new ServiceException("参数错误");
        }
        ErpLicense erpLicense = erpLicenseMapper.selectErpLicenseById(id);
        if (StringUtils.isNull(erpLicense)) {
            throw new ServiceException("执照错误");
        }
        if (StringUtils.isNull(erpLicense.getStatus())) {
            throw new ServiceException("执照状态错误");
        }

        LoginUser loginUser = tokenService.getLoginUser();
        if (StringUtils.isNull(loginUser)) {
            throw new ServiceException("登录错误");
        }

        switch (submitSource) {
            case 1://跟进专员提交审核
                if (erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.TO_BE_SUBMITTED
                        && erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.QZD_REVIEWED_REJECTED) {
                    throw new ServiceException("执照审核状态错误");
                }
                erpLicense.setCreatedUser(Integer.parseInt(loginUser.getSysUser().getUserId().toString()));
                erpLicense.setCreatedTime(new Date());

                // 提交审核后需修改线索状态为已完成。
                if (ObjectUtil.isNotNull(erpLicense.getClue()) && erpLicense.getClue().intValue() != 0) {
                    R<Boolean> booleanR = remoteCustomerService.completionClue(Long.valueOf(erpLicense.getClue()));
                    System.out.println(booleanR);
                }
                erpLicense.setExamineStatus(ErpLicenseConstants.QZD_TO_BE_REVIEWED);
                break;
            case 2://运营专员提交审核
            case 3://主管提交审核
                if (erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.TO_BE_SUBMITTED
                        && erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.QZD_REVIEWED_REJECTED) {
                    throw new ServiceException("执照审核状态错误");
                }
                erpLicense.setExamineStatus(ErpLicenseConstants.QZD_TO_BE_REVIEWED);
                break;
            case 4://会计提交审核
                if (erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.TO_BE_SUBMITTED
                        && erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.QZD_REVIEWED_REJECTED
                        && erpLicense.getExamineStatus().intValue() != ErpLicenseConstants.SUPERVISOR_TO_REJECTED
                ) {
                    throw new ServiceException("执照审核状态错误");
                }
                erpLicense.setExamineStatus(ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED);
                break;
            default:
                throw new ServiceException("执照来源错误");
        }

        int userId = Integer.parseInt(loginUser.getSysUser().getUserId().toString());
        String userName = loginUser.getSysUser().getNickName();

        checkLicense(erpLicense);

        erpLicense.setUpdateUser(userId);
        erpLicense.setUpdateTime(new Date());

        //记录日志
        erpLicenseLogMapper.insertErpLicenseLog(new ErpLicenseLog(
                erpLicense.getId(),
                Integer.parseInt(loginUser.getSysUser().getUserId().toString()),
                userName+"执照："+erpLicense.getNumber()+"提交审核："
        ));

        return erpLicenseMapper.updateErpLicense(erpLicense);
    }

    /***
     * 校验执照信息完整性
     * @param erpLicense
     * wangyu20230313
     */
    private void checkLicense(ErpLicense erpLicense) {

    }


    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ErpLicenseVO selectErpLicenseById(Long id)
    {
        ErpLicense erpLicense = erpLicenseMapper.selectErpLicenseById(id);
        ErpLicenseVO erpLicenseVO = new ErpLicenseVO();
        BeanUtils.copyProperties(erpLicense, erpLicenseVO);
        erpLicenseVO.setErpLicenseProductList(erpLicenseProductMapper.getErpLicenseProductBylicenseIds(id));

        if (StringUtils.isNotNull(erpLicenseVO.getAddrestCost())) {
            List<ErpLicenseVO> listVo = new ArrayList<>();
            listVo.add(erpLicenseVO);
            updateAgent(listVo,erpLicenseVO.getAddrestCost().toString());
        }
        //处理附件
        if (StringUtils.isNotEmpty(erpLicense.getAppendix())) {
            List<ErpLicenseAppendixUrl> list = new ArrayList<>();
            for (String appendix : erpLicense.getAppendix().split(",")) {
                ErpLicenseAppendixUrl erpLicenseAppendixUrl = new ErpLicenseAppendixUrl();
                erpLicenseAppendixUrl.setUrl(appendix);
                list.add(erpLicenseAppendixUrl);
            }

            erpLicenseVO.setAppendixList(list);
        }
        //执照跟进人
        if (Objects.nonNull(erpLicense.getLicenseSourcePersion())) {
            R<SysUser> info = userService.getUserInfoById(Long.valueOf(erpLicense.getLicenseSourcePersion()), SecurityConstants.INNER);
            if (200 == info.getCode()) {
                erpLicenseVO.setLicenseFollowUser(Objects.nonNull(info.getData()) ? info.getData().getNickName() : null);
            }
        }

        return erpLicenseVO;
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpLicense 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ErpLicenseVO> selectErpLicenseList(ErpLicenseDTO erpLicense)
    {
        if (ObjectUtil.isNotNull(erpLicense.getOperatingYearsBegin())) {
            Date date = DateUtils.addYears(new Date(), -erpLicense.getOperatingYearsBegin());
            String dateStr = DateUtils.dateTime(date);

            erpLicense.setLicenseCreatedTimeEnd(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, dateStr+" 23:59:59"));
        }
        if (ObjectUtil.isNotNull(erpLicense.getOperatingYearsEnd())) {
            Date date = DateUtils.addYears(new Date(), -erpLicense.getOperatingYearsEnd());
            String dateStr = DateUtils.dateTime(date);

            erpLicense.setLicenseCreatedTimeBegin(DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, dateStr+" 00:00:00"));
        }
        List<ErpLicense> list = erpLicenseMapper.selectErpLicenseList(erpLicense);
        return concordanceErpLicenseList(list);
    }

    @Override
    public List<ErpLicenseVO> selectErpLicenseListByCluIds(String clueIds) {
        List<ErpLicense> list = erpLicenseMapper.selectErpLicenseListByCluIds(clueIds);
        return concordanceErpLicenseList(list);
    }

    public List<ErpLicenseVO> concordanceErpLicenseList(List<ErpLicense> list) {
        List<ErpLicenseVO> listVo = new ArrayList<>();

        List<Long> orderIds = new ArrayList<>();
        List<Integer> areaIds = new ArrayList<>();
        List<Integer> addressCostIds = new ArrayList<>();
        List<Long> clueIds = new ArrayList<>();
        List<Integer> typeIds = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ErpLicense _erpLicense = list.get(i);
            if (StringUtils.isNotNull(_erpLicense.getOrderId()) && !orderIds.contains(_erpLicense.getOrderId())) {
                orderIds.add(_erpLicense.getOrderId());
            }
            if (StringUtils.isNotNull(_erpLicense.getArea()) && !areaIds.contains(_erpLicense.getArea())) {
                areaIds.add(_erpLicense.getArea());
            }
            if (StringUtils.isNotNull(_erpLicense.getAddrestCost()) && !addressCostIds.contains(_erpLicense.getAddrestCost() + "")) {
                addressCostIds.add( _erpLicense.getAddrestCost());
            }
            if (StringUtils.isNotNull(_erpLicense.getType()) && !typeIds.contains(_erpLicense.getType())) {
                typeIds.add(_erpLicense.getType());
            }
            if (StringUtils.isNotNull(_erpLicense.getClue()) && !clueIds.contains(_erpLicense.getClue())) {
                clueIds.add(_erpLicense.getClue());
            }
            ErpLicenseVO erpLicenseVO = new ErpLicenseVO();
            BeanUtils.copyProperties(_erpLicense, erpLicenseVO);
            listVo.add(erpLicenseVO);
        }

        //获取服务状态
        if (StringUtils.isNotEmpty(orderIds)) {
            updateServicePointStatusName(listVo, orderIds);
        }
        //获取地区名称
        if (StringUtils.isNotEmpty(areaIds)) {
            updateAreaName(listVo, areaIds);
        }
        //获取线索客户名称
        if (StringUtils.isNotEmpty(clueIds)) {
            updateVcCustomerName(listVo, clueIds);
        }
        //获取线索客户名称
        if (StringUtils.isNotEmpty(typeIds)) {
            updateTypeName(listVo, typeIds);
        }
//        //获取渠道商
//        if (StringUtils.isNotNull(addressCostIds)) {
//            updateAgent(listVo, addressCostIds);
//        }

        return listVo;
    }




    /**
     * 修改【请填写功能名称】
     * 
     * @param erpLicense 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateErpLicense(ErpLicense erpLicense)
    {
        return erpLicenseMapper.updateErpLicense(erpLicense);
    }

    @Override
    public int operateErpLicense(Long type, Long id, String refuseRemarks) {

        if (StringUtils.isNull(type) || StringUtils.isNull(id)) {
            throw new ServiceException("参数错误");
        }

        LoginUser loginUser = tokenService.getLoginUser();
        if (StringUtils.isNull(loginUser)) {
            throw new ServiceException("登录错误");
        }

        ErpLicense license = erpLicenseMapper.selectErpLicenseById(id);
        if (StringUtils.isNull(license)) {
            throw new ServiceException("执照错误");
        }

        String memo = "";
        switch (Integer.parseInt(type.toString())){
            case 1://上架
                if ((license.getGrounding() != ErpLicenseConstants.LICENSE_GROUDING_DOWN
                        && license.getGrounding() != ErpLicenseConstants.LICENSE_GROUDING__WAIT_UP)
                        || license.getStatus() != ErpLicenseConstants.LICENSE_STATUS_SELLING) {

                    throw new ServiceException("执照非下架状态/执照非待售状态");
                }
                //校验是否存在退款申请
                int i = erpExamineApproveMapper.countErpExamineApprove(id, Arrays.asList(0, 1), Arrays.asList(2));
                if(i > 0){
                    throw new ServiceException("该执照已申请退款，无法上架");
                }
                license.setGrounding(ErpLicenseConstants.LICENSE_GROUDING_UP);
                license.setGroundingTime(new Date());
                memo = loginUser.getSysUser().getNickName() + "上架执照" + license.getNumber();
                break;
            case 2://下架
                if (license.getGrounding() != ErpLicenseConstants.LICENSE_GROUDING_UP
                        || license.getStatus() != ErpLicenseConstants.LICENSE_STATUS_SELLING) {

                    throw new ServiceException("执照非上架架状态/执照非待售状态");
                }
                license.setGrounding(ErpLicenseConstants.LICENSE_GROUDING_DOWN);
                memo = loginUser.getSysUser().getNickName() + "下架执照" + license.getNumber();
                break;
            case 3://删除
                if (license.getStatus() == ErpLicenseConstants.LICENSE_STATUS_BILL
                        || license.getStatus() == ErpLicenseConstants.LICENSE_STATUS_SELLED
                        || license.getDeleted() != ErpLicenseConstants.LICENSE_NO_DELETED) {

                    throw new ServiceException("已提单或已售执照不可删除/执照不可删除");
                }
                license.setDeleted(ErpLicenseConstants.LICENSE_DELETED);
                memo = loginUser.getSysUser().getNickName() + "删除执照" + license.getNumber();
                break;
            case 4://通过
                if (license.getExamineStatus() != ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED &&
                        license.getExamineStatus() != ErpLicenseConstants.QZD_TO_BE_REVIEWED) {
                    throw new ServiceException("执照非待审核状态");
                }

                if (license.getExamineStatus() == ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED) {
                    license.setExamineStatus(ErpLicenseConstants.QZD_TO_BE_REVIEWED);
                } else if (license.getExamineStatus() == ErpLicenseConstants.QZD_TO_BE_REVIEWED) {
                    license.setExamineStatus(ErpLicenseConstants.QZD_REVIEWED_PASS);
                    license.setStatus(ErpLicenseConstants.LICENSE_STATUS_SELLING);
                }

                memo = loginUser.getSysUser().getNickName() + "通过执照" + license.getNumber();
                break;
            case 5://不通过
                if (license.getExamineStatus() != ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED &&
                        license.getExamineStatus() != ErpLicenseConstants.QZD_TO_BE_REVIEWED) {
                    throw new ServiceException("执照非待审核状态");
                }

                if (license.getExamineStatus() == ErpLicenseConstants.SUPERVISOR_TO_BE_REVIEWED) {
                    license.setExamineStatus(ErpLicenseConstants.SUPERVISOR_TO_REJECTED);
                }

                if (license.getExamineStatus() == ErpLicenseConstants.QZD_TO_BE_REVIEWED) {
                    license.setExamineStatus(ErpLicenseConstants.QZD_REVIEWED_REJECTED);
                }

                license.setRefuseRemarks(refuseRemarks);
                memo = loginUser.getSysUser().getNickName() + "不通过执照" + license.getNumber() + ",原因：" + refuseRemarks;
                break;
            default:
                throw new ServiceException("操作类型错误");
        }

        license.setUpdateUser(Integer.parseInt(loginUser.getSysUser().getUserId().toString()));
        license.setUpdateTime(new Date());

        //记录日志
        erpLicenseLogMapper.insertErpLicenseLog(new ErpLicenseLog(
                license.getId(),
                Integer.parseInt(loginUser.getSysUser().getUserId().toString()),
                memo
        ));

        int licenseUpdateReturn = erpLicenseMapper.updateErpLicense(license);
        //发送主管审核通过邮件
        if (licenseUpdateReturn > 0 && type.intValue() == 4 && license.getExamineStatus() == ErpLicenseConstants.QZD_TO_BE_REVIEWED) {
            try {
                sendLicenseEmail(license.getId(), license, 1);
            } catch (Exception e) {
                log.error("执照收转审核通过发送邮件异常，异常信息为：", e);
            }
        }
        //发送启照多审核驳回通过邮件
        if (licenseUpdateReturn > 0 && type.intValue() == 5 && license.getExamineStatus() == ErpLicenseConstants.QZD_REVIEWED_REJECTED
                && (Objects.nonNull(license.getLicenseSource()) && 1 == license.getLicenseSource())) {
            try {
                sendLicenseEmail(license.getId(), license, 2);
            } catch (Exception e) {
                log.error("执照启照多审核驳回发送邮件异常，异常信息为：", e);
            }
        }

        return licenseUpdateReturn;
    }


    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpClueLicenseVO> getClueLicenseList(ErpClueLicenseDTO erpClueLicenseDTO) {
        erpClueLicenseDTO.setCrmEnterpriseId(2);
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser currentUser = loginUser.getSysUser();
        List<Long> list = new ArrayList<>();
        if (Objects.nonNull(currentUser)) {
            List<SysRole> roles = currentUser.getRoles();
            if(CollectionUtils.isNotEmpty(roles)){
                list = roles.stream().map(item -> item.getRoleId()).collect(Collectors.toList());
            }
        }
        if (1 == erpClueLicenseDTO.getRequestType()) {
            erpClueLicenseDTO.setParams(null);
            //除系统管理员和启照多管理员外，其他角色只能看到 待领取 和 跟进未转化  的数据
            if ((Objects.nonNull(currentUser) && ! (currentUser.isAdmin())) && (CollectionUtils.isNotEmpty(list) && ! (list.contains(qizhaoduoAdministratorId)))) {
                erpClueLicenseDTO.setNumGjStatusList(Arrays.asList(GjStatusEnum.TO_RECEIVE.getStatus(), GjStatusEnum.NOT_CONVERTED.getStatus()));
            }
        }

        List<ErpClueLicenseVO> clueLicenseList = erpLicenseMapper.getClueLicenseList(erpClueLicenseDTO);
        List<ComDictRegion> comDictRegionLists = new ArrayList<>();

        List<Long> cityIdList = clueLicenseList.stream().filter(en -> ObjectUtil.isNotEmpty(en.getArea())).map(ErpClueLicenseVO::getArea).collect(Collectors.toList());
        if (!cityIdList.isEmpty()) {
            comDictRegionLists = erpLicenseMapper.selectComDictRegionList(cityIdList);
        }
        for (ErpClueLicenseVO erpClueLicenseVO : clueLicenseList) {
            Integer type = erpClueLicenseVO.getType();
            if (Objects.nonNull(type)) {
                ErpLicenseType erpLicenseType = erpLicenseTypeMapper.selectErpLicenseTypeById(Long.valueOf(type));
                erpClueLicenseVO.setTypeName(Objects.nonNull(erpLicenseType) ? erpLicenseType.getName() : null);
            }
            erpClueLicenseVO.setNumGjStatusName(GjStatusEnum.getNameByType(erpClueLicenseVO.getNumGjStatus()));
            if (!comDictRegionLists.isEmpty()) {
                List<ComDictRegion> collect = comDictRegionLists.stream().filter(en -> en.getId().equals(erpClueLicenseVO.getArea())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    ComDictRegion comDictRegion = collect.get(0);
                    if (ObjectUtil.isNotEmpty(comDictRegion)) {
                        erpClueLicenseVO.setAreaName(comDictRegion.getTitle());
                    }
                }
            }
        }
        List<Integer> collect = clueLicenseList.stream().map(item -> item.getType()).collect(Collectors.toList());
        updateLicenseTransferTypeName(null, clueLicenseList, collect, 2);

        return clueLicenseList;
    }

    @Override
    public List<LinceseProductVo> getErpLicenseProductByNumber(String number) {
        return erpLicenseProductMapper.getErpLicenseProductByNumber(number);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateLicenceErpLicenseProduct() {
        List<ErpLicense> erpLicenses = erpLicenseMapper.selectErpLicenseList(new ErpLicenseDTO());
        for (ErpLicense erpLicens : erpLicenses) {
            String productionIds = erpLicens.getProductionIds();
            String productionIdPrices = erpLicens.getProductionIdPrices();
            if (StringUtils.isNotEmpty(productionIds) && StringUtils.isNotEmpty(productionIdPrices)) {
                String replace = productionIds.replace("[", "").replace("]", "").replace("\"", "").replace(" ","");
                String replacePrice = productionIdPrices.replace("[", "").replace("]", "").replace("\"", "").replace(" ","");
                String[] split = replace.split(",");
                String[] splitPrice = replacePrice.split(",");
                int index = 0;
                if(split.length != splitPrice.length){
                    log.info("执照产品ID和产品价格不匹配，执照ID为{}", erpLicens.getId());
                    continue;
                }
                for (String s : split) {
                    ErpLicenseProduct erpLicenseProduct = new ErpLicenseProduct();
                    erpLicenseProduct.setLicenseId(Integer.valueOf(erpLicens.getId().toString()));
                    erpLicenseProduct.setProductId(Integer.valueOf(s));
                    BigDecimal bigDecimal = new BigDecimal(splitPrice[index]);
                    erpLicenseProduct.setProductPrice(bigDecimal);
                    erpLicenseProductMapper.insertErpLicenseProduct(erpLicenseProduct);
                    index++;
                }
            }
        }
    }

    @Override
    public List<ErpLicenseProduct> getErpLicenseProductBylicenseNo(String licenseNo) {
        return erpLicenseProductMapper.getErpLicenseProductBylicenseNo(licenseNo);
    }

    @Override
    public int updateErpLicenseByNumber(ErpLicense erpLicense) {
        return erpLicenseMapper.updateErpLicenseByNumber(erpLicense);
    }

    @DataScope(deptAlias = "sd", userAlias = "su")
    @Override
    public List<ErpLicenseTransferVO> getErpLicenseTransferList(ErpLicenseTransferDTO erpLicenseTransferDTO) {
        if (Objects.nonNull(erpLicenseTransferDTO.getMenuType()) && 2 == erpLicenseTransferDTO.getMenuType()) {
            erpLicenseTransferDTO.setExamineStatus(ErplicenseExamineStatusEnum.SUPERVISOR_TO_BE_REVIEWED.getCode());
        }
        if (Objects.nonNull(erpLicenseTransferDTO.getStatus()) && Arrays.asList(1, 2, 3).contains(erpLicenseTransferDTO.getStatus())) {
            erpLicenseTransferDTO.setDeleted(2);
        } else if (Objects.nonNull(erpLicenseTransferDTO.getStatus()) && Arrays.asList(-2).contains(erpLicenseTransferDTO.getStatus())) {
            erpLicenseTransferDTO.setDeleted(2);
            erpLicenseTransferDTO.setGrounding(2);
            erpLicenseTransferDTO.setStatus(null);
        } else if (Objects.nonNull(erpLicenseTransferDTO.getStatus()) && Arrays.asList(-1).contains(erpLicenseTransferDTO.getStatus())) {
            erpLicenseTransferDTO.setDeleted(1);
            erpLicenseTransferDTO.setStatus(null);
        }
        List<ErpLicenseTransferVO> erpLicenseTransferList = erpLicenseMapper.getErpLicenseTransferList(erpLicenseTransferDTO);
        //区域
        List<ComDictRegion> comDictRegionLists = new ArrayList<>();

        List<Long> cityIdList = erpLicenseTransferList.stream().filter(en -> ObjectUtil.isNotEmpty(en.getArea())).map(ErpLicenseTransferVO::getArea).collect(Collectors.toList());
        if (!cityIdList.isEmpty()) {
            comDictRegionLists = erpLicenseMapper.selectComDictRegionList(cityIdList);
        }
        for (ErpLicenseTransferVO erpLicenseTransferVO : erpLicenseTransferList) {
            //执照类型
            Integer type = erpLicenseTransferVO.getType();
            if (Objects.nonNull(type)) {
                ErpLicenseType erpLicenseType = erpLicenseTypeMapper.selectErpLicenseTypeById(Long.valueOf(type));
                erpLicenseTransferVO.setTypeName(Objects.nonNull(erpLicenseType) ? erpLicenseType.getName() : null);
            }
            //组合执照售卖状态
            if (Objects.nonNull(erpLicenseTransferVO.getDeleted()) && (1 == erpLicenseTransferVO.getDeleted())) {
                erpLicenseTransferVO.setStatusName("删除");
            } else if ((Objects.nonNull(erpLicenseTransferVO.getGrounding()) && 2 == erpLicenseTransferVO.getGrounding()) &&
                    (Objects.nonNull(erpLicenseTransferVO.getDeleted()) && (2 == erpLicenseTransferVO.getDeleted())) &&
                    (Objects.nonNull(erpLicenseTransferVO.getExamineStatus()) && (5 == erpLicenseTransferVO.getExamineStatus()))) {
                erpLicenseTransferVO.setStatusName("下架");
            } else if ((Objects.nonNull(erpLicenseTransferVO.getStatus()) && Arrays.asList(1, 2, 3).contains(erpLicenseTransferVO.getStatus())) &&
                    (Objects.nonNull(erpLicenseTransferVO.getExamineStatus()) && (5 == erpLicenseTransferVO.getExamineStatus())) &&
                    (Objects.nonNull(erpLicenseTransferVO.getDeleted()) && (2 == erpLicenseTransferVO.getDeleted()))) {
                switch (erpLicenseTransferVO.getStatus()) {
                    case 1:
                        erpLicenseTransferVO.setStatusName("待售");
                        break;
                    case 2:
                        erpLicenseTransferVO.setStatusName("已提单");
                        break;
                    case 3:
                        erpLicenseTransferVO.setStatusName("已售");
                        break;
                }
            }
            if (!comDictRegionLists.isEmpty()) {
                List<ComDictRegion> collect = comDictRegionLists.stream().filter(en -> en.getId().equals(erpLicenseTransferVO.getArea())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    ComDictRegion comDictRegion = collect.get(0);
                    if (ObjectUtil.isNotEmpty(comDictRegion)) {
                        erpLicenseTransferVO.setAreaName(comDictRegion.getTitle());
                    }
                }
            }
        }

        List<Integer> collect = erpLicenseTransferList.stream().map(item -> item.getType()).collect(Collectors.toList());
        updateLicenseTransferTypeName(erpLicenseTransferList, null, collect, 1);


        return erpLicenseTransferList;
    }

    @Override
    public JSONArray recoveryCountList(Date beginDate, Date endDate, Integer cityId) {



        JSONArray arr = new JSONArray();
        JSONObject obj1 = new JSONObject();
        obj1.put("source", 1);
        obj1.put("sourceName", "内部");
        obj1.put("sum", 0);
        obj1.put("sumOver", 0);
        obj1.put("sumNotOver", 0);
        obj1.put("sumShelve", 0);
        obj1.put("sumIn", 0);
        obj1.put("sumOverIn", 0);
        obj1.put("sumNotOverIn", 0);
        obj1.put("sumShelveIn", 0);
        arr.add(obj1);

        JSONObject obj2 = JSONObject.parseObject(obj1.toJSONString());
        obj2.put("source", 2);
        obj2.put("sourceName", "直客");
        arr.add(obj2);
        JSONObject obj3 = JSONObject.parseObject(obj1.toJSONString());
        obj3.put("source", 3);
        obj3.put("sourceName", "同行");
        arr.add(obj3);
        JSONObject obj4 = JSONObject.parseObject(obj1.toJSONString());
        obj4.put("source", 4);
        obj4.put("sourceName", "自营");
        arr.add(obj4);

        List<String> over = Arrays.asList("208,213,218".split(","));//外部执照完结的节点
        List<String> notOver = Arrays.asList("40,41,42,203".split(","));//外部执照未完结的节点状态
        List<String> overIn = Arrays.asList("253".split(","));//内部执照完结的节点
        List<String> notOverIn = Arrays.asList("249,250,251,252".split(","));

        List<String> sourceList = Arrays.asList("1,2,3,4".split(","));

        List<Map<String, Object>> list = erpLicenseMapper.recoveryCountList(beginDate, endDate, cityId);
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            if (!map.containsKey("serviceStatus") || !map.containsKey("pointStatus") ||
                    !map.containsKey("count1") || !map.containsKey("source1")) {
                continue;
            }
            int serviceStatus = Integer.parseInt(map.get("serviceStatus").toString());
            String pointStatus = map.get("pointStatus").toString();
            int count = Integer.parseInt(map.get("count1").toString());
            String source = map.get("source1").toString();

            if (sourceList.contains(source)) {
                JSONObject obj = arr.getJSONObject(Integer.parseInt(source)-1);

                if (serviceStatus == 4) {
                    if (over.contains(pointStatus) || notOver.contains(pointStatus)) {
                        //外部搁置
                        obj.put("sumShelve", obj.getInteger("sumShelve")+count);
                        obj.put("sum", obj.getInteger("sum")+count);
                    }
                    if (overIn.contains(pointStatus) || notOverIn.contains(pointStatus)) {
                        //内部搁置
                        obj.put("sumShelveIn", obj.getInteger("sumShelveIn")+count);
                        obj.put("sumIn", obj.getInteger("sumIn")+count);
                    }
                    arr.set(Integer.parseInt(source)-1, obj);
                    continue;
                }
                if (over.contains(pointStatus)) {
                    //外部完结
                    obj.put("sumOver", obj.getInteger("sumOver")+count);
                    obj.put("sum", obj.getInteger("sum")+count);
                }
                if (notOver.contains(pointStatus)) {
                    //外部完结
                    obj.put("sumNotOver", obj.getInteger("sumNotOver")+count);
                    obj.put("sum", obj.getInteger("sum")+count);
                }
                if (overIn.contains(pointStatus)) {
                    //外部完结
                    obj.put("sumOverIn", obj.getInteger("sumOverIn")+count);
                    obj.put("sumIn", obj.getInteger("sumIn")+count);
                }
                if (notOverIn.contains(pointStatus)) {
                    //外部完结
                    obj.put("sumNotOverIn", obj.getInteger("sumNotOverIn")+count);
                    obj.put("sumIn", obj.getInteger("sumIn")+count);
                }
                arr.set(Integer.parseInt(source)-1, obj);
            }
        }

        return arr;
    }

    @Override
    public List<ErpLicenseVO> selectInventoryList(ErpLicenseDTO erpLicense) {

        List<ErpLicenseVO> list = erpLicenseMapper.selectInventoryList(erpLicense);
        List<Long> licenseIdList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setLicenseSourceName(ErpLicenseConstants.ERP_LICENSE_SOURCE.get(list.get(i).getLicenseSource().toString()));
            licenseIdList.add(list.get(i).getId());
        }

        if (licenseIdList.size() > 0) {
            List<LicensePaymentVo> sumPayList = erpQzdPaymentRecordMapper.getLicensePaymentSumByLicenseId(licenseIdList);
            for (int i = 0; i < sumPayList.size(); i++) {
                LicensePaymentVo licensePaymentVo = sumPayList.get(i);
                for (int j = 0; j < list.size(); j++) {
                    ErpLicenseVO vo = list.get(j);
                    if (vo.getId().intValue() == licensePaymentVo.getLicenseId().intValue()) {
                        vo.setSumMoney(licensePaymentVo.getMoney());
                        if (ObjectUtil.isNotEmpty(vo.getOutBuyFee())) {
                            vo.setMoneyForPay(vo.getOutBuyFee().subtract(vo.getSumMoney()));
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<ErpLicenseSaleVo> selectSaleList(ErpLicenseSaleDto dto) {
        List<ErpLicenseSaleVo> list = erpLicenseMapper.selectSaleList(dto);
        List<Long> orderIdList = new ArrayList<>();
        List<Long> clientIdList = new ArrayList<>();
        List<Long> payReturnIdList = new ArrayList<>();
        List<Long> payOrderIdList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ErpLicenseSaleVo vo = list.get(i);
            if (ObjectUtil.isNotEmpty(vo.getOrderId()) && !orderIdList.contains(vo.getOrderId())) {
                orderIdList.add(vo.getOrderId());
            }
            if (ObjectUtil.isNotEmpty(vo.getClientId()) && !clientIdList.contains(vo.getClientId())) {
                clientIdList.add(vo.getClientId());
            }
            if (ObjectUtil.isNotEmpty(vo.getPayType()) && ObjectUtil.isNotEmpty(vo.getOrderId()) && !payOrderIdList.contains(vo.getOrderId()) && vo.getPayType() == 1) {
                payOrderIdList.add(vo.getOrderId());
            }
            if (ObjectUtil.isNotEmpty(vo.getPayType()) && ObjectUtil.isNotEmpty(vo.getRetainageReturnId()) && !payReturnIdList.contains(vo.getRetainageReturnId()) && vo.getPayType() == 2) {
                payReturnIdList.add(vo.getRetainageReturnId());
            }
        }

        if (orderIdList.size() > 0) {
            //查询合同号
            Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();

            ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderIdList)) {
                query.setOrderIdList(orderIdList);
            }
            List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
                map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
            }
            for (ErpLicenseSaleVo vo : list) {
                if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(vo.getIsElectronicContract()) && 1 == vo.getIsElectronicContract())) {
                    if (map.containsKey(vo.getOrderId())) {
                        List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(vo.getOrderId());
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                            StringBuffer vcContractNumber = new StringBuffer();
                            int size = erpOrderInfoForOmListVOS.size();
                            for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                                if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                                } else {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                                }
                            }
                            vo.setContactNumber(String.valueOf(vcContractNumber));
                        }
                    }
                }
            }
        }

        if (clientIdList.size() > 0) {
            List<Map<String, Object>> listMap = erpClientMapper.selectDataByClientIds(clientIdList);
            for (int i = 0; i < listMap.size(); i++) {
                Map<String, Object> map = listMap.get(i);
                for (int j = 0; j < list.size(); j++) {
                    ErpLicenseSaleVo vo = list.get(j);
                    if (map.containsKey("clientId") && map.get("clientId").toString().equals(vo.getClientId().toString())) {
                        vo.setCityName(map.containsKey("cityName") ? map.get("cityName").toString() : "");
                        vo.setEnterpriseName(map.containsKey("enterpriseName") ? map.get("enterpriseName").toString() : "");
                    }
                }
            }
        }
        if (payReturnIdList.size() > 0) {
            List<Map<String, Object>> listMap = erpOrderPaymentTermMapper.selectDataByReturnIdList(payReturnIdList);
            for (int i = 0; i < listMap.size(); i++) {
                Map<String, Object> map = listMap.get(i);
                for (int j = 0; j < list.size(); j++) {
                    ErpLicenseSaleVo vo = list.get(j);
                    if (ObjectUtil.isNotEmpty(vo.getPayType()) && vo.getPayType() == 2 && map.containsKey("retainageId") && map.get("retainageId").toString().equals(vo.getRetainageReturnId().toString())) {
                        vo.setCollectionUserName(map.containsKey("collectionUserName") ? map.get("collectionUserName").toString() : "");
                        vo.setCollectionUserDept(map.containsKey("collectionUserDept") ? map.get("collectionUserDept").toString() : "");
                    }
                }
            }
        }
        if (payOrderIdList.size() > 0) {
            List<Map<String, Object>> listMap = erpOrderPaymentTermMapper.selectDataByOrderIdList(payOrderIdList);
            for (int i = 0; i < listMap.size(); i++) {
                Map<String, Object> map = listMap.get(i);
                for (int j = 0; j < list.size(); j++) {
                    ErpLicenseSaleVo vo = list.get(j);
                    if (ObjectUtil.isNotEmpty(vo.getPayType()) && vo.getPayType() == 1 && map.containsKey("orderId") && map.get("orderId").toString().equals(vo.getOrderId().toString())) {
                        vo.setCollectionUserName(map.containsKey("collectionUserName") ? map.get("collectionUserName").toString() : "");
                        vo.setCollectionUserDept(map.containsKey("collectionUserDept") ? map.get("collectionUserDept").toString() : "");
                    }
                }
            }
        }
        return list;
    }

    private void sendLicenseEmail(Long id, ErpLicense erpLicenseNew, Integer type) {
        if (1 == type) {
            //查询记账会计和部门
            Integer userId = erpLicenseNew.getLicenseSourcePersion();
            R<SysUser> info = userService.getUserInfoById(Long.valueOf(userId), SecurityConstants.INNER);
            if (200 == info.getCode()) {
                String nickName = info.getData().getNickName();
                String deptName = info.getData().getDept().getDeptName();
                R<SysUser> currentLoginUser = userService.getUserInfoById(SecurityUtils.getUserId(), SecurityConstants.INNER);
                if (200 == currentLoginUser.getCode()) {
                    SysUser sysUser = currentLoginUser.getData();
                    //TODO 邮箱
                    ThreadPoolUtil.getPool().execute(() -> {
                        emailService.sendEmail(erpqzdEmail, "执照回收审核通过通知", "您好!\r\n"
                                + deptName + "+" + nickName + "提交的" + erpLicenseNew.getCompanyName() + "卖执照申请主管" + sysUser.getNickName() +
                                "已审核通过,执照编号为" + erpLicenseNew.getNumber() + "，可前往财税工作台-执照收转管理查看详细信息。");
                        log.error("执照编号{}，审核人{},", erpLicenseNew.getNumber(), sysUser.getNickName());
                        //发送钉钉消息
                        String dingContent = "### 执照回收审核通过通知 \n * " + sysUser.getNickName() + "，您好： \n "
                                + " * " + deptName + "+" + nickName + "提交的" + erpLicenseNew.getCompanyName() + "卖执照申请主管" + sysUser.getNickName() +
                                "已审核通过,执照编号为" + erpLicenseNew.getNumber() + "，可前往财税工作台-执照收转管理查看详细信息。";
                        DingSendDTO dingSendDTO = new DingSendDTO(erpqzdDingUserId, "执照回收审核通过通知", dingContent);
                        dingDingService.sendDingMessage(dingSendDTO);
                    });
                }
            }
        }
        if (2 == type) {
            if ((Objects.nonNull(erpLicenseNew.getLicenseSource()) && 1 == erpLicenseNew.getLicenseSource())) {
                Integer userId = erpLicenseNew.getLicenseSourcePersion();
                R<SysUser> info = userService.getUserInfoById(Long.valueOf(userId), SecurityConstants.INNER);
                if (200 == info.getCode()) {
                    String nickName = info.getData().getNickName();
                    String deptName = info.getData().getDept().getDeptName();
                    R<SysUser> currentLoginUser = userService.getUserInfoById(SecurityUtils.getUserId(), SecurityConstants.INNER);
                    if (200 == currentLoginUser.getCode()) {
                        SysUser sysUser = currentLoginUser.getData();
                        //TODO 邮箱
                        ThreadPoolUtil.getPool().execute(() -> {
                            emailService.sendEmail(erpqzdEmail, "执照回收审核驳回通知", "您好!\r\n"
                                    + deptName + "+" + nickName + "提交的" + erpLicenseNew.getCompanyName() + "卖执照申请启照多" + sysUser.getNickName() +
                                    "审核未通过,未通过原因：" + erpLicenseNew.getRefuseRemarks() + "，执照编号为" + erpLicenseNew.getNumber() + "，可前往财税工作台-执照收转管理-日志，查看详细信息。");
                            log.error("执照编号{}，审核人{},", erpLicenseNew.getNumber(), sysUser.getNickName());
                            //发送钉钉消息
                            String dingContent = "### 执照回收审核驳回通知 \n * " + sysUser.getNickName() + "，您好： \n "
                                    + " * " + deptName + "+" + nickName + "提交的" + erpLicenseNew.getCompanyName() + "卖执照申请启照多" + sysUser.getNickName() +
                                    "审核未通过,未通过原因：" + erpLicenseNew.getRefuseRemarks() + "，执照编号为" + erpLicenseNew.getNumber() + "，可前往财税工作台-执照收转管理-日志，查看详细信息。";
                            DingSendDTO dingSendDTO = new DingSendDTO(erpqzdDingUserId, "执照回收审核驳回通知", dingContent);
                            dingDingService.sendDingMessage(dingSendDTO);
                        });
                    }
                }
            }
        }

    }


    /***
     * 根据订单Id填充服务状态
     * @param listVo
     * @param orderIds
     */
    private void updateServicePointStatusName(List<ErpLicenseVO> listVo, List<Long> orderIds) {
        List<Map<String,Object>> arr = sServiceMainMapper.selectServiceTypeStatusByOrderIds(StringUtils.join(orderIds, ","), ServiceMainConstants.ZhiZhaoShouMaiService, ServiceMainConstants.ZhiZhaoShouMaiService);
        for (int i = 0; i < listVo.size(); i++) {
            ErpLicenseVO _erpLicenseVO = listVo.get(i);
            if (StringUtils.isNotNull(_erpLicenseVO.getOrderId())) {
                for (int j = 0; j < arr.size(); j++) {
                    Map<String,Object > _obj = arr.get(j);
                    if (_obj.containsKey("orderId") && _obj.containsKey("pointStatusName") && _erpLicenseVO.getOrderId().toString().equals(_obj.get("orderId").toString())) {
                        _erpLicenseVO.setPointStatusName(_obj.get("pointStatusName").toString());
                    }
                }
            }
        }
    }

    /***
     *
     * 填充地区名称
     * @param listVo
     * @param areaIds
     */
    private void updateAreaName(List<ErpLicenseVO> listVo, List<Integer> areaIds) {
        List<DictRegion> dictRegionList = iComDictRegionService.selectRegionByIds(StringUtils.join(areaIds, ","));
        for (int i = 0; i < listVo.size(); i++) {
            ErpLicenseVO _erpLicenseVO = listVo.get(i);
            if (StringUtils.isNotNull(_erpLicenseVO.getArea())) {
                for (int j = 0; j < dictRegionList.size(); j++) {
                    DictRegion dictRegion = dictRegionList.get(j);
                    if (StringUtils.isNotNull(_erpLicenseVO.getArea()) && _erpLicenseVO.getArea() == dictRegion.getId().intValue()) {
                        _erpLicenseVO.setAreaName(dictRegion.getName());
                    }
                }
            }
        }

        List<Integer> secondIds = new ArrayList<>();
        List<Integer> firstIds = new ArrayList<>();
        Map<Integer, JSONObject> voMap = new HashMap<Integer, JSONObject>();
        for (DictRegion vo : dictRegionList) {
            int id = Integer.parseInt(vo.getId().toString());
            int parentId = Integer.parseInt(vo.getParentId().toString());
            JSONObject obj = new JSONObject();

            int level = Integer.parseInt(vo.getLevel().toString());
            if (level == 3) {
                if (!secondIds.contains(parentId)) {
                    secondIds.add(parentId);
                }
                obj.put("id", id);
                obj.put("secondId", parentId);
            }
            if (level == 2) {
                if (!secondIds.contains(id)) {
                    secondIds.add(id);
                }
                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }
                obj.put("secondId", id);
            }

            if (level == 1) {
                if (!firstIds.contains(id)) {
                    firstIds.add(id);
                }
                obj.put("firstId", id);
            }
            obj.put("name", vo.getName());
            voMap.put(id, obj);
        }

        if (ObjectUtil.isNotNull(secondIds) && secondIds.size() > 0) {
            List<DictRegion> secondList = iComDictRegionService.selectRegionByIds(StringUtils.join(secondIds, ","));
            for (DictRegion vo : secondList) {
                int id = Integer.parseInt(vo.getId().toString());
                int parentId = Integer.parseInt(vo.getParentId().toString());

                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.containsKey("secondId") && tempVo.getInteger("secondId").intValue() == id) {
                        tempVo.put("firstId", parentId);
                    }
                }
            }
        }

        if (ObjectUtil.isNotNull(firstIds) && firstIds.size() > 0) {
            List<DictRegion> firstList = iComDictRegionService.selectRegionByIds(StringUtils.join(firstIds, ","));
            for (DictRegion vo : firstList) {
                int id = Integer.parseInt(vo.getId().toString());
                int parentId = Integer.parseInt(vo.getParentId().toString());

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.containsKey("firstId") && tempVo.getInteger("firstId").intValue() == id) {
                        tempVo.put("firstName", vo.getName());
                    }
                }
            }
        }


        for (int i = 0; i < listVo.size(); i++) {
            ErpLicenseVO _erpLicenseVO = listVo.get(i);
            if (StringUtils.isNotNull(_erpLicenseVO.getArea())) {
                for (int j = 0; j < dictRegionList.size(); j++) {
                    DictRegion dictRegion = dictRegionList.get(j);
                    if (StringUtils.isNotNull(_erpLicenseVO.getArea()) && _erpLicenseVO.getArea() == dictRegion.getId().intValue()) {
                        _erpLicenseVO.setAreaName(dictRegion.getName());
                    }
                }
            }
            JSONObject tempVo = voMap.get(_erpLicenseVO.getArea());
            if (ObjectUtil.isNotNull(tempVo)) {
                _erpLicenseVO.setProvinceName(tempVo.containsKey("firstName") ? tempVo.getString("firstName") : "");
            }
        }
    }

    /***
     *
     * 填充地区名称
     * @param listVo
     * @param areaIds
     */
    private void updateTypeName(List<ErpLicenseVO> listVo, List<Integer> typeIds) {
        List<Map<String, Object>> list = erpLicenseTypeMapper.selectListByIds(StringUtils.join(typeIds, ","));

        List<Integer> firstIds = new ArrayList<>();
        List<Integer> secondIds = new ArrayList<>();
        Map<Integer, JSONObject> voMap = new HashMap<Integer, JSONObject>();
        for (Map<String, Object> vo : list) {
            int id = Integer.parseInt(vo.get("id").toString());
            int parentId = Integer.parseInt(vo.get("parent_id").toString());
            JSONObject obj = new JSONObject();

            int level = Integer.parseInt(vo.get("type_level").toString());
            if (level == 3) {
                if (!secondIds.contains(parentId)) {
                    secondIds.add(parentId);
                }
                obj.put("secondId", parentId);
            }
            if (level == 2) {
                if (!secondIds.contains(id)) {
                    secondIds.add(id);
                }
                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }
                obj.put("secondId", id);
            }

            if (level == 1) {
                if (!firstIds.contains(id)) {
                    firstIds.add(id);
                }
                obj.put("firstId", id);
            }
            obj.put("name", vo.get("name"));
            obj.put("nameAll", vo.get("name"));
            obj.put("id", id);
            obj.put("level", level);
            voMap.put(id, obj);
        }

        if (ObjectUtil.isNotNull(secondIds)) {
            List<Map<String, Object>> secondList = erpLicenseTypeMapper.selectListByIds(StringUtils.join(secondIds, ","));
            for (Map<String, Object> vo : secondList) {
                int id = Integer.parseInt(vo.get("id").toString());
                int parentId = Integer.parseInt(vo.get("parent_id").toString());

                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.containsKey("secondId") && tempVo.getInteger("secondId").intValue() == id) {
                        tempVo.put("firstId", parentId);
                        if (tempVo.getInteger("level") == 3) {
                            tempVo.put("nameAll", vo.get("name") + "-" + tempVo.getString("nameAll"));
                        }
                    }
                }
            }
        }


        if (ObjectUtil.isNotNull(firstIds)) {
            List<Map<String, Object>> firstList = erpLicenseTypeMapper.selectListByIds(StringUtils.join(firstIds, ","));
            for (Map<String, Object> vo : firstList) {
                int id = Integer.parseInt(vo.get("id").toString());
                int parentId = Integer.parseInt(vo.get("parent_id").toString());

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.getInteger("level") != 1 && tempVo.containsKey("firstId") && tempVo.getInteger("firstId") == id) {
                        tempVo.put("nameAll", vo.get("name") + "-" + tempVo.getString("nameAll"));
                    }
                }
            }
        }

        for (int i = 0; i < listVo.size(); i++) {
            ErpLicenseVO _erpLicenseVO = listVo.get(i);
            if (StringUtils.isNotNull(_erpLicenseVO.getType())) {
                for (int j = 0; j < list.size(); j++) {
                    Map<String, Object> obj = list.get(j);
                    if (StringUtils.isNotNull(_erpLicenseVO.getType()) && _erpLicenseVO.getType() == Integer.parseInt(obj.get("id").toString())) {
                        _erpLicenseVO.setTypeName(obj.get("name").toString());
                        JSONObject tempVo = voMap.get(_erpLicenseVO.getType());
                        _erpLicenseVO.setTypeNameAll(tempVo.containsKey("nameAll") ? tempVo.getString("nameAll") : "");
                    }
                }
            }
        }
    }

    /***
     *
     * 填充地区名称
     * @param listVo
     * @param typeIds
     */
    private void updateLicenseTransferTypeName(List<ErpLicenseTransferVO> listVo, List<ErpClueLicenseVO> clueLicenseVO, List<Integer> typeIds, Integer type) {
        List<Map<String, Object>> list = erpLicenseTypeMapper.selectListByIds(StringUtils.join(typeIds, ","));

        List<Integer> firstIds = new ArrayList<>();
        List<Integer> secondIds = new ArrayList<>();
        Map<Integer, JSONObject> voMap = new HashMap<Integer, JSONObject>();
        for (Map<String, Object> vo : list) {
            int id = Integer.parseInt(vo.get("id").toString());
            int parentId = Integer.parseInt(vo.get("parent_id").toString());
            JSONObject obj = new JSONObject();

            int level = Integer.parseInt(vo.get("type_level").toString());
            if (level == 3) {
                if (!secondIds.contains(parentId)) {
                    secondIds.add(parentId);
                }
                obj.put("secondId", parentId);
            }
            if (level == 2) {
                if (!secondIds.contains(id)) {
                    secondIds.add(id);
                }
                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }
                obj.put("secondId", id);
            }

            if (level == 1) {
                if (!firstIds.contains(id)) {
                    firstIds.add(id);
                }
                obj.put("firstId", id);
            }
            obj.put("name", vo.get("name"));
            obj.put("nameAll", vo.get("name"));
            obj.put("id", id);
            obj.put("level", level);
            voMap.put(id, obj);
        }

        if (ObjectUtil.isNotNull(secondIds)) {
            List<Map<String, Object>> secondList = erpLicenseTypeMapper.selectListByIds(StringUtils.join(secondIds, ","));
            for (Map<String, Object> vo : secondList) {
                int id = Integer.parseInt(vo.get("id").toString());
                int parentId = Integer.parseInt(vo.get("parent_id").toString());

                if (!firstIds.contains(parentId)) {
                    firstIds.add(parentId);
                }

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.containsKey("secondId") && tempVo.getInteger("secondId").intValue() == id) {
                        tempVo.put("firstId", parentId);
                        if (tempVo.getInteger("level") == 3) {
                            tempVo.put("nameAll", vo.get("name") + "-" + tempVo.getString("nameAll"));
                        }
                        break;
                    }
                }
            }
        }


        if (ObjectUtil.isNotNull(firstIds)) {
            List<Map<String, Object>> firstList = erpLicenseTypeMapper.selectListByIds(StringUtils.join(firstIds, ","));
            for (Map<String, Object> vo : firstList) {
                int id = Integer.parseInt(vo.get("id").toString());
                int parentId = Integer.parseInt(vo.get("parent_id").toString());

                for (Integer k : voMap.keySet()) {
                    JSONObject tempVo = voMap.get(k);
                    if (tempVo.getInteger("level") != 1 && tempVo.containsKey("firstId") && tempVo.getInteger("firstId") == id) {
                        tempVo.put("nameAll", vo.get("name") + "-" + tempVo.getString("nameAll"));
                    }
                }
            }
        }

        if(1 == type){
            for (int i = 0; i < listVo.size(); i++) {
                ErpLicenseTransferVO _erpLicenseVO = listVo.get(i);
                if (StringUtils.isNotNull(_erpLicenseVO.getType())) {
                    for (int j = 0; j < list.size(); j++) {
                        Map<String, Object> obj = list.get(j);
                        if (StringUtils.isNotNull(_erpLicenseVO.getType()) && _erpLicenseVO.getType() == Integer.parseInt(obj.get("id").toString())) {
                            _erpLicenseVO.setTypeName(obj.get("name").toString());
                            JSONObject tempVo = voMap.get(_erpLicenseVO.getType());
                            _erpLicenseVO.setTypeNameAll(tempVo.containsKey("nameAll") ? tempVo.getString("nameAll") : "");
                        }
                    }
                }
            }
        }
        if(2 == type){
            for (int i = 0; i < clueLicenseVO.size(); i++) {
                ErpClueLicenseVO _erpLicenseVO = clueLicenseVO.get(i);
                if (StringUtils.isNotNull(_erpLicenseVO.getType())) {
                    for (int j = 0; j < list.size(); j++) {
                        Map<String, Object> obj = list.get(j);
                        if (StringUtils.isNotNull(_erpLicenseVO.getType()) && _erpLicenseVO.getType() == Integer.parseInt(obj.get("id").toString())) {
                            _erpLicenseVO.setTypeName(obj.get("name").toString());
                            JSONObject tempVo = voMap.get(_erpLicenseVO.getType());
                            _erpLicenseVO.setTypeNameAll(tempVo.containsKey("nameAll") ? tempVo.getString("nameAll") : "");
                        }
                    }
                }
            }
        }
    }

    private void updateAgent(List<ErpLicenseVO> listVo, String addressCostIds) {
        List<Long> idList = Arrays.stream(addressCostIds.replaceFirst(",", "").split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<ErpBizServiceAddressCost> erpBizServiceAddressCostList = erpBizServiceAddressCostMapper.selectErpBizServiceAddressCostByIds(idList);
        for (int i = 0; i < listVo.size(); i++) {
            ErpLicenseVO _erpLicenseVO = listVo.get(i);
            if (StringUtils.isNotNull(_erpLicenseVO.getAddrestCost())) {
                for (int j = 0; j < erpBizServiceAddressCostList.size(); j++) {
                    ErpBizServiceAddressCost erpBizServiceAddressCost = erpBizServiceAddressCostList.get(j);
                    if (StringUtils.isNotNull(_erpLicenseVO.getAddrestCost()) && _erpLicenseVO.getAddrestCost() == erpBizServiceAddressCost.getId().intValue()) {
                        _erpLicenseVO.setAgent(erpBizServiceAddressCost.getAgentId());
                        _erpLicenseVO.setAddressCostNameId(erpBizServiceAddressCost.getCostNameId());
                    }
                }
            }
        }
    }

    private void updateVcCustomerName(List<ErpLicenseVO> listVo, List<Long> clueIds) {
        R<List<BdClue>> data = remoteCustomerService.getInfoByClueIds(clueIds);
        if (200 == data.getCode() && data.getData().size() > 0) {
            List<BdClue> list = data.getData();
            for (int i = 0; i < listVo.size(); i++) {
                ErpLicenseVO _erpLicenseVO = listVo.get(i);
                if (StringUtils.isNotNull(_erpLicenseVO.getClue()) && _erpLicenseVO.getClue().intValue() != 0) {
                    for (int j = 0; j < list.size(); j++) {
                        BdClue bdClue = list.get(j);
                        if (_erpLicenseVO.getClue().intValue() == bdClue.getId().intValue()) {
                            _erpLicenseVO.setVcCustomerName(bdClue.getVcCustomerName());
                        }
                    }
                }
            }
        }
    }
}
