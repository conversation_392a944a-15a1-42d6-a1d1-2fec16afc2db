package com.nnb.erp.domain.vo.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.erp.domain.vo.ErpOrderInfoForOmListVO;
import lombok.Data;
import com.nnb.common.core.web.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 客户列表result
 *
 * <AUTHOR>
 * @date 2022-03-23
 */
@Data
public class ErpClientListResultVo
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 企业id */
    private Long numEnterpriseId;

    /** 个人id */
    private Long numPersonalId;

    /** 线索id，企业最后一个经绑定的线索 */
    private Long numClueId;

    /** 企业状态，0 未成交；1 已成交 */
    private Integer numStatus;

    /** 城市id */
    private Long numCityId;

    /** 客户类型：1 企业；2 个人 */
    private Integer numType;

    /** 备注 */
    private String vcRemark;

    /** 创建人 */
    private Long numCreatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date datSigningDatecreatedTime;

    /** 更新人 */
    private Long numUpdatedBy;

    /** 更新时间 */
    private Date datSigningDateupdatedTime;

    /** 企业名 */
    private String vcCompanyName;

    /** 曾用名 */
    private String vcHistoryName;

    /** 纳税类型 */
    private Long numCorporatePropertyId;

    /** 个人名称 */
    private String vcName;

    /** 联系方式 */
    private String vcPhone;

    /** 身份证号码 */
    private String vcIdCard;

    /** 微信号 */
    private String vcWeixin;

    /** qq号 */
    private String vcQq;

    /** 邮箱 */
    private String vcEmail;
    /** 城市名 */
    private String cityName;
    /** 纳税类型 */
    private String vcTaxName;

    private List<ErpOrderInfoForOmListVO> erpOrders;

    // 线索来源
    private String guestSrcName;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 会计
     */
    private String accountUserIdStr;

    /**
     * 增值
     */
    private String incrementUserIdStr;
    /***
     * 凭证参与活动标签
     */
    private String activitiesLabelStr;


}
