package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpCouponProduct;
import com.nnb.erp.service.IErpCouponProductService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 优惠券产品关系Controller
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@RestController
@RequestMapping("/ErpCouponProduct")
@Api(tags = "ErpCouponProductController", description = "优惠券产品关系")
public class ErpCouponProductController extends BaseController
{
    @Autowired
    private IErpCouponProductService erpCouponProductService;

    /**
     * 查询优惠券产品关系列表
     */
    @ApiOperation(value = "查询优惠券产品关系列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpCouponProduct.class)})
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpCouponProduct erpCouponProduct)
    {
        startPage();
        List<ErpCouponProduct> list = erpCouponProductService.selectErpCouponProductList(erpCouponProduct);
        return getDataTable(list);
    }

    /**
     * 导出优惠券产品关系列表
     */
    @ApiOperation(value = "导出优惠券产品关系列表")
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:export")
    //@Log(title = "优惠券产品关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpCouponProduct erpCouponProduct) throws IOException
    {
        List<ErpCouponProduct> list = erpCouponProductService.selectErpCouponProductList(erpCouponProduct);
        ExcelUtil<ErpCouponProduct> util = new ExcelUtil<ErpCouponProduct>(ErpCouponProduct.class);
        util.exportExcel(response, list, "优惠券产品关系数据");
    }

    /**
     * 获取优惠券产品关系详细信息
     */
    @ApiOperation(value = "获取优惠券产品关系详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpCouponProduct.class)})
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="优惠券产品关系id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpCouponProductService.selectErpCouponProductById(id));
    }

    /**
     * 新增优惠券产品关系
     */
    @ApiOperation(value = "新增优惠券产品关系")
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:add")
    //@Log(title = "优惠券产品关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpCouponProduct erpCouponProduct)
    {
        return toAjax(erpCouponProductService.insertErpCouponProduct(erpCouponProduct));
    }

    /**
     * 修改优惠券产品关系
     */
    @ApiOperation(value = "修改优惠券产品关系")
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:edit")
    //@Log(title = "优惠券产品关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpCouponProduct erpCouponProduct)
    {
        return toAjax(erpCouponProductService.updateErpCouponProduct(erpCouponProduct));
    }

    /**
     * 删除优惠券产品关系
     */
    @ApiOperation(value = "删除优惠券产品关系")
    @PreAuthorize(hasPermi = "erp:ErpCouponProduct:remove")
    //@Log(title = "优惠券产品关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpCouponProductService.deleteErpCouponProductByIds(ids));
    }
}
