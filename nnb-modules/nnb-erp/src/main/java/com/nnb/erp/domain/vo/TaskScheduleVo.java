package com.nnb.erp.domain.vo;

import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

@Data
public class TaskScheduleVo extends BaseEntity {


    private String taskIds;
    private String name;
    private Long userId;
    private List<Long> ids;

    private JSONObject date1;
    private JSONObject date2;
    private JSONObject date3;
    private JSONObject date4;
    private JSONObject date5;
    private JSONObject date6;
    private JSONObject date7;


}
