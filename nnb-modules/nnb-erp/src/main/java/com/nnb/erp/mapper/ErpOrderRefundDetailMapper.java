package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.nnb.erp.domain.ErpOrderRefundDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 订单退款明细Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface ErpOrderRefundDetailMapper
{
    /**
     * 查询订单退款明细
     *
     * @param id 订单退款明细主键
     * @return 订单退款明细
     */
    public ErpOrderRefundDetail selectErpOrderRefundDetailById(Long id);

    /**
     * 查询订单退款明细根据order_refund_id
     *
     * @param orderRefundId 订单退款明细主表id
     * @return 订单退款明细
     */
    public List<ErpOrderRefundDetail> selectErpOrderRefundDetailByRefundId(@Param("orderRefundId") Long orderRefundId);

    /**
     * 查询订单退款明细列表
     *
     * @param erpOrderRefundDetail 订单退款明细
     * @return 订单退款明细集合
     */
    public List<ErpOrderRefundDetail> selectErpOrderRefundDetailList(ErpOrderRefundDetail erpOrderRefundDetail);

    /**
     * 新增订单退款明细
     *
     * @param erpOrderRefundDetail 订单退款明细
     * @return 结果
     */
    public int insertErpOrderRefundDetail(ErpOrderRefundDetail erpOrderRefundDetail);

    /**
     * 修改订单退款明细
     *
     * @param erpOrderRefundDetail 订单退款明细
     * @return 结果
     */
    public int updateErpOrderRefundDetail(ErpOrderRefundDetail erpOrderRefundDetail);

    /**
     * 删除订单退款明细
     *
     * @param id 订单退款明细主键
     * @return 结果
     */
    public int deleteErpOrderRefundDetailById(Long id);

    /**
     * 批量删除订单退款明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpOrderRefundDetailByIds(@Param("ids") List<Long> ids);

    /***
     * 获取产品待审批的退款金额
     * @param serviceOrderId
     * @return
     */
    @Select("SELECT IFNULL(SUM(eord.refund_amount),0) FROM erp_order_refund_detail eord " +
            "LEFT JOIN erp_order_refund eor ON eor.id = eord.order_refund_id " +
            "LEFT JOIN erp_examine_approve eea ON eea.other_id = eor.id " +
            "WHERE eea.approve_status = 0 AND eea.approve_type = 33 AND eord.service_orders_id = #{serviceOrderId};")
    public BigDecimal selectRefundAmountByServiceOrderIdInApprove(@Param("serviceOrderId") Long serviceOrderId);
}
