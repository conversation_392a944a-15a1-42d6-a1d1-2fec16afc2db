package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 订单相关的付款申请对象 erp_examine_other_order_pay
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@ApiModel(value="ErpExamineOtherOrderPay",description="订单相关的付款申请对象")
public class ErpExamineOtherOrderPay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    @ApiModelProperty("审批类型")
    private Integer approveType;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String info;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createdUser;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    /** 付款金额 */
    @Excel(name = "付款金额")
    @ApiModelProperty("付款金额")
    private BigDecimal fee;

    /** 收款人信息 */
    @Excel(name = "收款人信息")
    @ApiModelProperty("收款人信息")
    private String payee;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty("银行账户")
    private String bankNumber;

    /** 开户行 */
    @Excel(name = "开户行")
    @ApiModelProperty("开户行")
    private String bankName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String memo;

    /** 附件 */
    @Excel(name = "附件")
    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("类型：1旧的通用审批2新的委托代征审批")
    private Integer type;

    @ApiModelProperty("erp_wtdz_kp 的id集合")
    private String wtdzIds;

    @ApiModelProperty("代理商ID")
    private Long agentId;

    private String agentNumber;
    public String getAgentNumber() {
        return agentNumber;
    }
    public void setAgentNumber(String agentNumber) {
        this.agentNumber = agentNumber;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setInfo(String info) 
    {
        this.info = info;
    }

    public String getInfo() 
    {
        return info;
    }
    public void setCreatedUser(Long createdUser)
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser()
    {
        return createdUser;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdateUser(Long updateUser)
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser()
    {
        return updateUser;
    }

    public Integer getApproveType() {
        return approveType;
    }

    public void setApproveType(Integer approveType) {
        this.approveType = approveType;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getPayee() {
        return payee;
    }

    public void setPayee(String payee) {
        this.payee = payee;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getWtdzIds() {
        return wtdzIds;
    }

    public void setWtdzIds(String wtdzIds) {
        this.wtdzIds = wtdzIds;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("info", getInfo())
            .append("createdUser", getCreatedUser())
            .append("createdTime", getCreatedTime())
            .append("updateUser", getUpdateUser())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
