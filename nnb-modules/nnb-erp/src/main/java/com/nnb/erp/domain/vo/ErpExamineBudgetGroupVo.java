package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpExamineBudgetDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ErpExamineBudgetGroupVo {

    private String detailIds;
    private List<Long> detailIdList;

    @Excel(name = "城市")
    private String cityName;

    private Integer budgetProject;
    @Excel(name = "项目")
    private String projectName;

    @Excel(name = "预算部门")
    private String deptName;

    @Excel(name = "预算月份")
    private String budgetMonth;

    @Excel(name = "预算类别")
    private String firstName;

    @Excel(name = "预算类型")
    private String secondName;

    @Excel(name = "预算明细")
    private String thirdName;

    @Excel(name = "预算金额")
    private BigDecimal budgetFee;

    @Excel(name = "已申请金额")
    private BigDecimal applyFee;

    @Excel(name = "预算余额")
    private BigDecimal budgetBalance;
}
