package com.nnb.erp.mapper;

import java.util.Date;
import java.util.List;
import com.nnb.erp.domain.ErpPhoneCardConfig;
import com.nnb.erp.domain.ErpPhoneCardConfigVO;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;

/**
 * 手机卡品牌管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-10-31
 */
public interface ErpPhoneCardConfigMapper 
{
    /**
     * 查询手机卡品牌管理
     * 
     * @param id 手机卡品牌管理主键
     * @return 手机卡品牌管理
     */
    public ErpPhoneCardConfig selectErpPhoneCardConfigById(Long id);

    /**
     * 查询手机卡品牌管理列表
     * 
     * @param erpPhoneCardConfig 手机卡品牌管理
     * @return 手机卡品牌管理集合
     */
    public List<ErpPhoneCardConfig> selectErpPhoneCardConfigList(ErpPhoneCardConfig erpPhoneCardConfig);

    /**
     * 查询手机卡品牌管理列表
     *
     * @param erpPhoneCardConfig 手机卡品牌管理
     * @return 手机卡品牌管理集合
     */
    public List<ErpPhoneCardConfigVO> selectErpPhoneCardConfigListVO(ErpPhoneCardConfigVO erpPhoneCardConfig);

    /**
     * 新增手机卡品牌管理
     * 
     * @param erpPhoneCardConfig 手机卡品牌管理
     * @return 结果
     */
    public int insertErpPhoneCardConfig(ErpPhoneCardConfig erpPhoneCardConfig);

    /**
     * 修改手机卡品牌管理
     * 
     * @param erpPhoneCardConfig 手机卡品牌管理
     * @return 结果
     */
    public int updateErpPhoneCardConfig(ErpPhoneCardConfig erpPhoneCardConfig);

    /**
     * 电话卡id自动补全
     *
     */
    public int repairPhoneId();

    /**
     * 删除手机卡品牌管理
     * 
     * @param id 手机卡品牌管理主键
     * @return 结果
     */
    public int deleteErpPhoneCardConfigById(Long id);

    /**
     * 批量删除手机卡品牌管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpPhoneCardConfigByIds(Long[] ids);

    /**
     * 手机卡信息批量导入
     *
     * @param phoneNum  手机号
     * @return
     */
    ErpPhoneCardConfig selectByPhoneNum(String phoneNum);

    /**
     *  导入验证重复
     * @param param
     * @return
     */
    ErpPhoneCardConfig selectByCardIdAndPackage(ErpPhoneCardConfig param);

    ErpPhoneCardConfig selectErpPhoneCardConfigByCallDate(@Param("phoneId") String phoneId, @Param("callDate")Date callDate);
}
