package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单信息，用于审核列表，VO。
 *
 * <AUTHOR>
 * @since 2022/4/8 13:53
 */
@Data
public class ErpOrderInfoForApprovalVO {

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private Long orderId;

    /**
     * 订单编号。
     */
    @ApiModelProperty("订单编号。")
    private String orderNumber;

    /**
     * 订单来源，num。
     */
    @ApiModelProperty("订单来源，num。")
    private Integer orderSourceType;

    /**
     * 订单来源，str。
     */
    @ApiModelProperty("订单来源，str。")
    private String orderSourceName;

    /**
     * 企业名称/个人客户，客户名称。
     */
    @ApiModelProperty("企业名称/个人客户，客户名称。")
    private String clientName;

    /**
     * 联系人，线索联系人。
     */
    @ApiModelProperty("联系人，线索联系人。")
    private String clueCustomerName;

    /**
     * 所属部门标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("所属部门标识。")
    private Long deptId;

    /**
     * 所属部门名称。
     */
    @ApiModelProperty("所属部门名称。")
    private String deptName;

    /**
     * 签约人标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("签约人标识。")
    private Long userId;

    /**
     * 签约人名称。
     */
    @ApiModelProperty("签约人名称。")
    private String userName;

    /**
     * 签约时间。
     */
    @ApiModelProperty("签约时间。")
    private String signingDate;

    /**
     * 优惠金额。
     */
    @ApiModelProperty("优惠金额。")
    private BigDecimal discountAmount;

    /**
     * 应收金额。
     */
    @ApiModelProperty("应收金额。")
    private BigDecimal totalPrice;

    /**
     * 实收金额。
     */
    @ApiModelProperty("实收金额。")
    private BigDecimal payPrice;

    /**
     * 尾款金额。
     */
    @ApiModelProperty("尾款金额。")
    private BigDecimal lastPrice;

    /**
     * 退款金额。
     */
    @ApiModelProperty("退款金额。")
    private BigDecimal sumRefundPrice;

    /**
     * 订单状态，num。
     */
    @ApiModelProperty("订单状态，num。")
    private Integer orderStatusType;

    /**
     * 订单状态，str。
     */
    @ApiModelProperty("订单状态，str。")
    private String orderStatusName;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    private String createTime;

    /**
     * 业务审核标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("业务审核标识。")
    private Long businessApprovalId;

    /**
     * 审核配置标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("审核配置标识。")
    private Long approvalConfigId;

}
