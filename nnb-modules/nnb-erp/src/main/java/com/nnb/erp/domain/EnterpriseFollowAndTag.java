package com.nnb.erp.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EnterpriseFollowAndTag {

    private Long enterpriseId;

    private String enterpriseName;

    private String lastFollowUser;

    private String lastFollowDept;

    private List<String> tagName;

    @ApiModelProperty("状态：1正常，2作废，3已提单")
    private Integer status;

    @ApiModelProperty("状态：跟进状态 1:待分配 ,2:待跟进,3:跟进中,4:跟进未转化,5:分配未跟进")
    private Integer followStatus;
}
