package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.erp.domain.vo.ErpApprovalFlowVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpApprovalFlow;
import com.nnb.erp.service.IErpApprovalFlowService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 审核流程Controller
 * 
 * <AUTHOR>
 * @date 2022-03-26
 */
@RestController
@RequestMapping("/ErpApprovalFlow")
@Api(tags = "ErpApprovalFlowController", description = "审核流程")
public class ErpApprovalFlowController extends BaseController
{
    @Autowired
    private IErpApprovalFlowService erpApprovalFlowService;

    /**
     * 查询审核流程列表
     */
    @ApiOperation(value = "查询审核流程列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpApprovalFlowVo.class)})
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpApprovalFlow erpApprovalFlow)
    {
        startPage();
        List<ErpApprovalFlowVo> list = erpApprovalFlowService.selectErpApprovalFlowVoList(erpApprovalFlow);
        return getDataTable(list);
    }

    /**
     * 导出审核流程列表
     */
    @ApiOperation(value = "导出审核流程列表")
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:export")
    //@Log(title = "审核流程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpApprovalFlow erpApprovalFlow) throws IOException
    {
        List<ErpApprovalFlow> list = erpApprovalFlowService.selectErpApprovalFlowList(erpApprovalFlow);
        ExcelUtil<ErpApprovalFlow> util = new ExcelUtil<ErpApprovalFlow>(ErpApprovalFlow.class);
        util.exportExcel(response, list, "审核流程数据");
    }

    /**
     * 获取审核流程详细信息
     */
    @ApiOperation(value = "获取审核流程详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpApprovalFlow.class)})
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="审核流程id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpApprovalFlowService.selectErpApprovalFlowById(id));
    }

    /**
     * 新增审核流程
     */
    @ApiOperation(value = "新增审核流程")
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:add")
    //@Log(title = "审核流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpApprovalFlow erpApprovalFlow)
    {
        return toAjax(erpApprovalFlowService.insertErpApprovalFlow(erpApprovalFlow));
    }

    /**
     * 修改审核流程
     */
    @ApiOperation(value = "修改审核流程")
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:edit")
    //@Log(title = "审核流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpApprovalFlow erpApprovalFlow)
    {
        return toAjax(erpApprovalFlowService.updateErpApprovalFlow(erpApprovalFlow));
    }

    /**
     * 删除审核流程
     */
    @ApiOperation(value = "删除审核流程")
    @PreAuthorize(hasPermi = "erp:ErpApprovalFlow:remove")
    //@Log(title = "审核流程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpApprovalFlowService.deleteErpApprovalFlowByIds(ids));
    }


    /**
     * 保存审核流程
     */
    @ApiOperation(value = "保存审核流程")
    @PostMapping("/saveList")
    public AjaxResult saveList(@RequestBody List<ErpApprovalFlowVo> vos)
    {
        if (ObjectUtil.isEmpty(vos)){
            AjaxResult.error("无数据!");
        }
        return toAjax(erpApprovalFlowService.insertVos(vos));
    }

}
