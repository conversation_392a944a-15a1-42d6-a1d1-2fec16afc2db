package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 执照需求对象 erp_license_demand
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@ApiModel(value="ErpLicenseDemand",description="执照需求对象")
public class ErpLicenseDemand extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 执照类型 */
    @Excel(name = "执照类型")
    @ApiModelProperty("执照类型")
    private Integer type;

    /** 区域 */
    @Excel(name = "区域")
    @ApiModelProperty("区域")
    private Integer area;

    /** 纳税类型 */
    @Excel(name = "纳税类型")
    @ApiModelProperty("纳税类型")
    private Integer taxType;

    /** 地址类型 */
    @Excel(name = "地址类型")
    @ApiModelProperty("地址类型")
    private Integer addressType;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remarks;

    /** 关联执照 */
    @Excel(name = "关联执照")
    @ApiModelProperty("关联执照")
    private Long licenseId;

    /** 申请人 */
    @Excel(name = "申请人")
    @ApiModelProperty("申请人")
    private Long createdUser;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("申请时间")
    private Date createdDate;

    /** 匹配状态1未匹配2已匹配3搁置 */
    @Excel(name = "匹配状态1未匹配2已匹配3搁置")
    @ApiModelProperty("匹配状态1未匹配2已匹配3搁置")
    private Integer matchStatus;

    /** 匹配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("匹配时间")
    private Date matchDate;

    @Excel(name = "售卖情况1已售卖2未售卖")
    @ApiModelProperty("售卖情况1已售卖2未售卖")
    private Integer saleStatus;

    @ApiModelProperty("执照年限")
    private Integer licenseYear;

    @ApiModelProperty("关联变更情况1无2关联变更少3零关联变更")
    private Integer associationChange;

    @ApiModelProperty("有无银行1有2无")
    private Integer haveBank;

    @ApiModelProperty("有无社保1有2无")
    private Integer haveSocialSecurity;

    @ApiModelProperty("资质要求")
    private String qualificationRequirement;

    @ApiModelProperty("客户预算")
    private String customerBudget;

    @ApiModelProperty("收款金额")
    private String payPrice;











    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("area", getArea())
            .append("taxType", getTaxType())
            .append("addressType", getAddressType())
            .append("remarks", getRemarks())
            .append("licenseId", getLicenseId())
            .append("createdUser", getCreatedUser())
            .append("createdDate", getCreatedDate())
            .append("matchStatus", getMatchStatus())
            .append("matchDate", getMatchDate())
            .toString();
    }
}
