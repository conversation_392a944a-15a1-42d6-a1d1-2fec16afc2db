package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 组合活动对象 erp_combined_activity
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@ApiModel(value = "ErpCombinedActivity", description = "组合活动对象")
public class ErpCombinedActivity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    @ApiModelProperty("部门id集合")
    private List<Long> numDeptIds;


    @ApiModelProperty("部门名称")
    private String numDept;

    @ApiModelProperty("部门id")
    private Long numDeptId;
    @ApiModelProperty("部门")
    private Long deptId;
    /**
     * 名称
     */
    @Excel(name = "名称")
    @ApiModelProperty("名称")
    private String vcName;

    @ApiModelProperty("有效时间")
    private String valiTime;


    /**
     * 状态 0：下架；1：上架
     */
    @Excel(name = "状态 0：下架；1：上架")
    @ApiModelProperty("状态 0：下架；1：上架")
    private Integer numStatus;

    /**
     * 适用城市
     */
    @Excel(name = "适用城市")
    @ApiModelProperty("适用城市")
    private Long numAreaId;

    @ApiModelProperty("适用城市名称")
    private String numAreaName;

    /**
     * 适用渠道：0-&gt;全部；1-&gt;移动；2-&gt;PC
     */
    @Excel(name = "适用渠道：0-&gt;全部；1-&gt;移动；2-&gt;PC")
    @ApiModelProperty("适用渠道：0-&gt;全部；1-&gt;移动；2-&gt;PC")
    private Long numChannel;

    /**
     * 活动价格总计
     */
    @Excel(name = "活动价格总计")
    @ApiModelProperty("活动价格总计")
    private BigDecimal numPrice;


    /**
     * 活动类型；0-&gt;组合；1-&gt;赠送
     */
    @Excel(name = "活动类型；0-&gt;组合；1-&gt;赠送")
    @ApiModelProperty("活动类型；0-&gt;组合；1-&gt;赠送")
    private Integer numType;


    /**
     * 组合主产品 json格式
     */
    @ApiModelProperty("组合主产品 json格式")
    private String vcCombinedProduct;

    /**
     * 赠送产品 json格式
     */
    @ApiModelProperty("赠送产品 json格式")
    private String vcComplimentaryProduct;

    /**
     * 开始使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始使用时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("开始使用时间")
    private Date datStartTime;

    /**
     * 结束使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束使用时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结束使用时间")
    private Date datEndTime;

    private String datStartTimeString;
    private String datEndTimeString;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String vcRemark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedTime;


    /**
     * 创建人
     */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedTime;

    /**
     * 更新人
     */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long datUpdatedBy;

    @ApiModelProperty("产品价格总计")
    private BigDecimal productPriceCount;

    @ApiModelProperty("活动价格")
    private List<BigDecimal> prices;

    @Excel(name = "活动价格单价")
    @ApiModelProperty("活动价格")
    private BigDecimal numActivityPrice;


    @ApiModelProperty("产品价格单价")
    private BigDecimal numProductPrice;

    @ApiModelProperty("产品分类名称")
    private String vcClassificationName;

    @ApiModelProperty("活动名称")
    private String vcProductName;

    @ApiModelProperty("组合活动产品")
    private List<CombunedActivityDto> erpCombinedActivityProducts;

    @ApiModelProperty("部门id")
    private List<Long> erpActivityDepts;

    @ApiModelProperty("部门名称")
    private List<String> numDepts;

    private Long numProductId;
    private Long    activityId;

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getNumProductId() {
        return numProductId;
    }

    public void setNumProductId(Long numProductId) {
        this.numProductId = numProductId;
    }

    private List<Long> erpCombinedActivityIds;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public List<String> getNumDepts() {
        return numDepts;
    }

    public void setNumDepts(List<String> numDepts) {
        this.numDepts = numDepts;
    }


    public List<Long> getErpActivityDepts() {
        return erpActivityDepts;
    }

    public void setErpActivityDepts(List<Long> erpActivityDepts) {
        this.erpActivityDepts = erpActivityDepts;
    }


    public List<CombunedActivityDto> getErpCombinedActivityProducts() {
        return erpCombinedActivityProducts;
    }

    public void setErpCombinedActivityProducts(List<CombunedActivityDto> erpCombinedActivityProducts) {
        this.erpCombinedActivityProducts = erpCombinedActivityProducts;
    }



    public String getVcProductName() {
        return vcProductName;
    }

    public void setVcProductName(String vcProductName) {
        this.vcProductName = vcProductName;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getVcClassificationName() {
        return vcClassificationName;
    }

    public void setVcClassificationName(String vcClassificationName) {
        this.vcClassificationName = vcClassificationName;
    }

    public BigDecimal getNumActivityPrice() {
        return numActivityPrice;
    }

    public void setNumActivityPrice(BigDecimal numActivityPrice) {
        this.numActivityPrice = numActivityPrice;
    }

    public BigDecimal getNumProductPrice() {
        return numProductPrice;
    }

    public void setNumProductPrice(BigDecimal numProductPrice) {
        this.numProductPrice = numProductPrice;
    }

    public String getNumAreaName() {
        return numAreaName;
    }

    public void setNumAreaName(String numAreaName) {
        this.numAreaName = numAreaName;
    }

    public List<Long> getNumDeptIds() {
        return numDeptIds;
    }

    public void setNumDeptIds(List<Long> numDeptIds) {
        this.numDeptIds = numDeptIds;
    }



    public List<Long> getErpCombinedActivityIds() {
        return erpCombinedActivityIds;
    }

    public void setErpCombinedActivityIds(List<Long> erpCombinedActivityIds) {
        this.erpCombinedActivityIds = erpCombinedActivityIds;
    }

    public String getValiTime() {
        return valiTime;
    }

    public void setValiTime(String valiTime) {
        this.valiTime = valiTime;
    }

    public List<BigDecimal> getPrices() {
        return prices;
    }

    public void setPrices(List<BigDecimal> prices) {
        this.prices = prices;
    }

    public BigDecimal getProductPriceCount() {
        return productPriceCount;
    }

    public void setProductPriceCount(BigDecimal productPriceCount) {
        this.productPriceCount = productPriceCount;
    }


    public String getNumDept() {
        return numDept;
    }

    public void setNumDept(String numDept) {
        this.numDept = numDept;
    }

    public Long getNumDeptId() {
        return numDeptId;
    }

    public void setNumDeptId(Long numDeptId) {
        this.numDeptId = numDeptId;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setVcName(String vcName) {
        this.vcName = vcName;
    }

    public String getVcName() {
        return vcName;
    }

    public void setNumStatus(Integer numStatus) {
        this.numStatus = numStatus;
    }

    public Integer getNumStatus() {
        return numStatus;
    }

    public void setNumAreaId(Long numAreaId) {
        this.numAreaId = numAreaId;
    }

    public Long getNumAreaId() {
        return numAreaId;
    }

    public void setNumChannel(Long numChannel) {
        this.numChannel = numChannel;
    }

    public Long getNumChannel() {
        return numChannel;
    }

    public void setNumPrice(BigDecimal numPrice) {
        this.numPrice = numPrice;
    }

    public BigDecimal getNumPrice() {
        return numPrice;
    }

    public void setNumType(Integer numType) {
        this.numType = numType;
    }

    public Integer getNumType() {
        return numType;
    }

    public void setVcCombinedProduct(String vcCombinedProduct) {
        this.vcCombinedProduct = vcCombinedProduct;
    }

    public String getVcCombinedProduct() {
        return vcCombinedProduct;
    }

    public void setVcComplimentaryProduct(String vcComplimentaryProduct) {
        this.vcComplimentaryProduct = vcComplimentaryProduct;
    }

    public String getVcComplimentaryProduct() {
        return vcComplimentaryProduct;
    }

    public void setDatStartTime(Date datStartTime) {
        this.datStartTime = datStartTime;
    }

    public Date getDatStartTime() {
        return datStartTime;
    }

    public void setDatEndTime(Date datEndTime) {
        this.datEndTime = datEndTime;
    }

    public Date getDatEndTime() {
        return datEndTime;
    }

    public void setVcRemark(String vcRemark) {
        this.vcRemark = vcRemark;
    }

    public String getVcRemark() {
        return vcRemark;
    }

    public void setDatCreatedTime(Date datCreatedTime) {
        this.datCreatedTime = datCreatedTime;
    }

    public Date getDatCreatedTime() {
        return datCreatedTime;
    }

    public void setNumCreatedBy(Long numCreatedBy) {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() {
        return numCreatedBy;
    }

    public void setDatUpdatedTime(Date datUpdatedTime) {
        this.datUpdatedTime = datUpdatedTime;
    }

    public Date getDatUpdatedTime() {
        return datUpdatedTime;
    }

    public void setDatUpdatedBy(Long datUpdatedBy) {
        this.datUpdatedBy = datUpdatedBy;
    }

    public Long getDatUpdatedBy() {
        return datUpdatedBy;
    }

    public String getDatStartTimeString() {
        return datStartTimeString;
    }

    public void setDatStartTimeString(String datStartTimeString) {
        this.datStartTimeString = datStartTimeString;
    }

    public String getDatEndTimeString() {
        return datEndTimeString;
    }

    public void setDatEndTimeString(String datEndTimeString) {
        this.datEndTimeString = datEndTimeString;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("vcName", getVcName())
                .append("numStatus", getNumStatus())
                .append("numAreaId", getNumAreaId())
                .append("numChannel", getNumChannel())
                .append("numPrice", getNumPrice())
                .append("numType", getNumType())
                .append("vcCombinedProduct", getVcCombinedProduct())
                .append("vcComplimentaryProduct", getVcComplimentaryProduct())
                .append("datStartTime", getDatStartTime())
                .append("datEndTime", getDatEndTime())
                .append("vcRemark", getVcRemark())
                .append("datCreatedTime", getDatCreatedTime())
                .append("numCreatedBy", getNumCreatedBy())
                .append("datUpdatedTime", getDatUpdatedTime())
                .append("datUpdatedBy", getDatUpdatedBy())
                .append("numDept", getNumDept())
                .append("valiTime", getValiTime())
                .append("productPriceCount", getProductPriceCount())
                .append("numDeptIds", getNumDeptIds())
                .append("erpCombinedActivityIds", getErpCombinedActivityIds())
                .append("prices", getPrices())
                .append("numActivityPrice", getNumActivityPrice())
                .append("numProductPrice", getNumProductPrice())
                .append("vcClassificationName", getVcClassificationName())
                .append("vcProductName",getVcProductName())
                .append("erpCombinedActivityProducts",getErpCombinedActivityProducts())
                .append("erpActivityDepts",getErpActivityDepts())
                .append("numDepts",getNumDepts())
                .toString();
    }



}
