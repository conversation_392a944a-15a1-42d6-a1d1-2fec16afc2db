package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品，用于提交订单，DTO。
 *
 * <AUTHOR>
 * @since 2022/3/22 9:52
 */
@Data
public class ErpProductForCommitOrderDTO {

    /**
     * 订单内容标识/服务单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单内容标识/服务单标识。")
    private Long serviceOrderId;

    /**
     * 产品标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("产品标识。")
    private Long productId;

    /**
     * 产品名称。
     */
    @ApiModelProperty("产品名称。")
    private String productName;

    /**
     * 纳税类型文本。
     */
    @ApiModelProperty("纳税类型文本。")
    private String taxType;

    /**
     * 产品价格。
     */
    @ApiModelProperty("产品价格。")
    private BigDecimal productPrice;

    /**
     * 服务类型文本。
     */
    @ApiModelProperty("服务类型文本。")
    private String productService;

    /**
     * 单位。
     */
    @ApiModelProperty("单位。")
    private String productUnit;

    /**
     * 产品区域文本。
     */
    @ApiModelProperty("产品区域文本。")
    private String productRegion;

    /**
     * 产品总价。
     */
    @ApiModelProperty("产品总价。")
    private BigDecimal sumPrice;

    /**
     * 产品应收。
     */
    @ApiModelProperty("产品应收。")
    private BigDecimal totalPrice;

    @ApiModelProperty("产品价格ID。")
    private Long configurationId;

    /**
     * 产品实付。
     */
    @ApiModelProperty("产品实付。")
    private BigDecimal payPrice;

    /**
     * 产品尾款。
     */
    @ApiModelProperty("产品尾款。")
    private BigDecimal lastPrice;

    /**
     * 优惠券标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("优惠券标识。")
    private Long couponId;

    /**
     * 组合标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("组合标识。")
    private Long combinedId;

    /**
     * 优惠金额。
     */
    @ApiModelProperty("优惠金额。")
    private BigDecimal couponPrice;

    /**
     * 优惠额度申请/手填优惠金额。
     */
    @ApiModelProperty("优惠额度申请/手填优惠金额。")
    private BigDecimal productPreferential;

    /**
     * 渠道费。
     */
    @ApiModelProperty("渠道费。")
    private BigDecimal channelFee;

    /**
     * 购买数量。
     */
    @ApiModelProperty("购买数量。")
    private Integer productCount;

    /**
     * 是否废弃：0未废弃，1废弃待审核，2已废弃。
     */
    @ApiModelProperty("是否废弃：0未废弃，1废弃待审核，2已废弃。（0.正常1.退费，2.退费待审核，3作废，4作废待审核，5部分退费，6部分退费待审核）")
    private Integer isDeprecated;

    /**
     * 退款金额。
     */
    @ApiModelProperty("退款金额。")
    private BigDecimal refundPrice;

    /**
     * 积分，单个产品可获得的积分 * 产品数量。
     */
    @ApiModelProperty("积分，单个产品可获得的积分 * 产品数量。")
    private BigDecimal points;

    /**
     * 产品优惠金额汇总。
     */
    @ApiModelProperty("产品优惠金额汇总。")
    private BigDecimal retainageDiscountPrice;

    @ApiModelProperty("产品名称id")
    private Long numNameId;

//    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("税控开始时间")
    private String acStart;

//    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("税控结束时间")
    private String acEnd;
    @ApiModelProperty("企业")
    private  Long clientId;

    private  Long serviceTypeId;


    @ApiModelProperty("执照编号")
    private  Integer licenseNumber;

    @ApiModelProperty("部门产品不需要合同选择状态：0 没选择，1选择")
    private Integer noContractSelectedStatus;

    @ApiModelProperty("是否为部门不需要合同产品： 1.是")
    private Integer isNoNeedContractProduct;

    @ApiModelProperty("产品一级分类ID")
    private Long classificationId;

    @ApiModelProperty("开票额度-个体开票产品专用")
    private BigDecimal gtKp;

    @ApiModelProperty("开票企业-个体开票产品专用")
    private Long kpEnterprise;

    private Integer billingCheckCost;

    @ApiModelProperty("资质延期1是2否")
    private Long qualificationsExtension;

    @ApiModelProperty("年检1是2否")
    private Long annualInspection;

    @ApiModelProperty("资质延期或者年检关联的服务单ID")
    private Long extensionServiceId;
}
