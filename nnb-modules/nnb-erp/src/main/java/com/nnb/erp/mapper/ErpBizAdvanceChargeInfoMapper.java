package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpBizAdvanceChargeInfo;
import com.nnb.erp.domain.vo.bizAdvanceChargeInfo.ErpBizAdvanceChargeInfoVo;

/**
 * 预付款信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface ErpBizAdvanceChargeInfoMapper 
{
    /**
     * 查询预付款信息
     * 
     * @param numId 预付款信息主键
     * @return 预付款信息
     */
    public ErpBizAdvanceChargeInfo selectErpBizAdvanceChargeInfoByNumId(Long numId);

    /**
     * 查询预付款信息列表
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 预付款信息集合
     */
    public List<ErpBizAdvanceChargeInfo> selectErpBizAdvanceChargeInfoList(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo);
    /**
     * 查询预付款信息列表
     *
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 预付款信息集合
     */
    public List<ErpBizAdvanceChargeInfoVo> selectErpBizAdvanceChargeInfoListNew(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo);

    /**
     * 新增预付款信息
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 结果
     */
    public int insertErpBizAdvanceChargeInfo(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo);

    /**
     * 修改预付款信息
     * 
     * @param erpBizAdvanceChargeInfo 预付款信息
     * @return 结果
     */
    public int updateErpBizAdvanceChargeInfo(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo);

    /**
     * 删除预付款信息
     * 
     * @param numId 预付款信息主键
     * @return 结果
     */
    public int deleteErpBizAdvanceChargeInfoByNumId(Long numId);

    /**
     * 批量删除预付款信息
     * 
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizAdvanceChargeInfoByNumIds(Long[] numIds);


    /**
     * 查询总数
     *
     * @param erpBizAdvanceChargeInfo 总数
     * @return 预付款信息集合
     */
    public int selectCountErpBizAdvanceChargeInfo(ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo);
}
