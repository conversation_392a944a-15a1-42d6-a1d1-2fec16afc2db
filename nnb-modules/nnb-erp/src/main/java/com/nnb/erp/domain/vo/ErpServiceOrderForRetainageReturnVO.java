package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单内容信息，用于尾款回款，VO。
 *
 * <AUTHOR>
 * @since 2022/4/2 11:21
 */
@Data
public class ErpServiceOrderForRetainageReturnVO {

    /**
     * 订单内容标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单内容标识。")
    private Long serviceOrderId;

    /**
     * 订单内容尾款。
     */
    @ApiModelProperty("订单内容尾款。")
    private BigDecimal lastPrice;

    /**
     * 实付金额。
     */
    @ApiModelProperty("实付金额。")
    private BigDecimal payPrice;

    /**
     * 应收金额。
     */
    @ApiModelProperty("应收金额。")
    private BigDecimal totalPrice;

    /**
     * 作废状态：0为作废，1作废待审核，2已作废。
     */
    @ApiModelProperty("作废状态：0为作废，1作废待审核，2已作废。")
    private Integer isDeprecated;

    @ApiModelProperty("产品id")
    private Long numProductId;

}
