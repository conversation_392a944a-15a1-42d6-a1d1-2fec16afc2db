package com.nnb.erp.mapper.order;

import com.nnb.erp.domain.approval.ErpOrderCostExpenditure;
import com.nnb.erp.domain.order.ErpOrderRefundCostExpenditure;

import java.util.List;

/**
 * 订单退款成本支出Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface ErpOrderRefundCostExpenditureMapper 
{
    /**
     * 查询订单退款成本支出
     * 
     * @param id 订单退款成本支出主键
     * @return 订单退款成本支出
     */
    public ErpOrderRefundCostExpenditure selectErpOrderRefundCostExpenditureById(Long id);

    /**
     * 查询订单退款成本支出列表
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 订单退款成本支出集合
     */
    public List<ErpOrderRefundCostExpenditure> selectErpOrderRefundCostExpenditureList(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure);

    /**
     * 新增订单退款成本支出
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 结果
     */
    public int insertErpOrderRefundCostExpenditure(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure);

    /**
     * 修改订单退款成本支出
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 结果
     */
    public int updateErpOrderRefundCostExpenditure(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure);

    /**
     * 删除订单退款成本支出
     * 
     * @param id 订单退款成本支出主键
     * @return 结果
     */
    public int deleteErpOrderRefundCostExpenditureById(Long id);

    /**
     * 批量删除订单退款成本支出
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpOrderRefundCostExpenditureByIds(Long[] ids);

    public List<ErpOrderCostExpenditure> getErpOrderCostExpenditure(Long approveId);
}
