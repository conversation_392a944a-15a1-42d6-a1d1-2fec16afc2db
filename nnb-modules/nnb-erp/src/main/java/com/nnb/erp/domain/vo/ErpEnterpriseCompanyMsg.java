package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpEnterpriseCompany;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 公司股东信息
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="ErpEnterpriseCompanyMsg",description="公司股东信息")
public class ErpEnterpriseCompanyMsg extends ErpEnterpriseCompany {

    private static final long serialVersionUID = 559198172850915464L;

    /** 营业执照副本照片 */
    @Excel(name = "营业执照副本照片")
    @ApiModelProperty("营业执照副本照片")
    private List<String> imgAddressList;

}
