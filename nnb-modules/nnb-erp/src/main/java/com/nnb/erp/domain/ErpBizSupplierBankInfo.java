package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 供应商收款账号信息对象 erp_biz_supplier_bank_info
 * 
 * <AUTHOR>
 * @date 2022-09-28
 */
@ApiModel(value="ErpBizSupplierBankInfo",description="供应商收款账号信息对象")
public class ErpBizSupplierBankInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long numId;

    @Excel(name = "收款人全称")
    @ApiModelProperty("收款人全称")
    private String numBizSupplierInfoId;

    /** 收款人全称 */
    @Excel(name = "收款人全称")
    @ApiModelProperty("收款人全称")
    private String numPayeeUserId;

    /** 开户行 */
    @Excel(name = "开户行")
    @ApiModelProperty("开户行")
    private String vcBankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @ApiModelProperty("银行账号")
    private String vcBankNo;

    /** 录入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("录入时间")
    private Date datInputDate;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private Long numOperationUserId;

    /** 有效1；无效2 */
    @Excel(name = "有效1；无效2")
    @ApiModelProperty("有效1；无效2")
    private Long numStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedTime;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long datUpdatedBy;

    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setNumPayeeUserId(String numPayeeUserId) 
    {
        this.numPayeeUserId = numPayeeUserId;
    }

    public String getNumPayeeUserId() 
    {
        return numPayeeUserId;
    }
    public void setVcBankName(String vcBankName) 
    {
        this.vcBankName = vcBankName;
    }

    public String getVcBankName() 
    {
        return vcBankName;
    }
    public void setVcBankNo(String vcBankNo) 
    {
        this.vcBankNo = vcBankNo;
    }

    public String getVcBankNo() 
    {
        return vcBankNo;
    }
    public void setDatInputDate(Date datInputDate) 
    {
        this.datInputDate = datInputDate;
    }

    public Date getDatInputDate() 
    {
        return datInputDate;
    }
    public void setNumOperationUserId(Long numOperationUserId) 
    {
        this.numOperationUserId = numOperationUserId;
    }

    public Long getNumOperationUserId() 
    {
        return numOperationUserId;
    }

    public Long getNumStatus() {
        return numStatus;
    }

    public void setNumStatus(Long numStatus) {
        this.numStatus = numStatus;
    }

    public void setDatCreatedTime(Date datCreatedTime)
    {
        this.datCreatedTime = datCreatedTime;
    }

    public Date getDatCreatedTime() 
    {
        return datCreatedTime;
    }
    public void setNumCreatedBy(Long numCreatedBy) 
    {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() 
    {
        return numCreatedBy;
    }
    public void setDatUpdatedTime(Date datUpdatedTime) 
    {
        this.datUpdatedTime = datUpdatedTime;
    }

    public Date getDatUpdatedTime() 
    {
        return datUpdatedTime;
    }
    public void setDatUpdatedBy(Long datUpdatedBy) 
    {
        this.datUpdatedBy = datUpdatedBy;
    }

    public Long getDatUpdatedBy() 
    {
        return datUpdatedBy;
    }

    public String getNumBizSupplierInfoId() {
        return numBizSupplierInfoId;
    }

    public void setNumBizSupplierInfoId(String numBizSupplierInfoId) {
        this.numBizSupplierInfoId = numBizSupplierInfoId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("numPayeeUserId", getNumPayeeUserId())
            .append("vcBankName", getVcBankName())
            .append("vcBankNo", getVcBankNo())
            .append("datInputDate", getDatInputDate())
            .append("numOperationUserId", getNumOperationUserId())
            .append("datCreatedTime", getDatCreatedTime())
            .append("numCreatedBy", getNumCreatedBy())
            .append("datUpdatedTime", getDatUpdatedTime())
            .append("datUpdatedBy", getDatUpdatedBy())
            .toString();
    }
}
