package com.nnb.erp.service.impl.accountpermance;

import java.util.List;

import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.erp.domain.accountpermance.ErpAccountPerformanceDownloadRecord;
import com.nnb.erp.domain.vo.ErpAccountPerformanceDownloadRecordVo;
import com.nnb.erp.mapper.accountpermance.ErpAccountPerformanceDownloadRecordMapper;
import com.nnb.erp.service.accountpermance.IErpAccountPerformanceDownloadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 会计绩效下载记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-04-04
 */
@Service
public class ErpAccountPerformanceDownloadRecordServiceImpl implements IErpAccountPerformanceDownloadRecordService
{
    @Autowired
    private ErpAccountPerformanceDownloadRecordMapper erpAccountPerformanceDownloadRecordMapper;

    /**
     * 查询会计绩效下载记录
     * 
     * @param id 会计绩效下载记录主键
     * @return 会计绩效下载记录
     */
    @Override
    public ErpAccountPerformanceDownloadRecord selectErpAccountPerformanceDownloadRecordById(Long id)
    {
        return erpAccountPerformanceDownloadRecordMapper.selectErpAccountPerformanceDownloadRecordById(id);
    }

    /**
     * 查询会计绩效下载记录列表
     * 
     * @param erpAccountPerformanceDownloadRecord 会计绩效下载记录
     * @return 会计绩效下载记录
     */
    @Override
    public List<ErpAccountPerformanceDownloadRecord> selectErpAccountPerformanceDownloadRecordList(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        return erpAccountPerformanceDownloadRecordMapper.selectErpAccountPerformanceDownloadRecordList(erpAccountPerformanceDownloadRecord);
    }


    @DataScope(deptAlias = "sd", userAlias = "su")
    @Override
    public List<ErpAccountPerformanceDownloadRecordVo> selectErpAccountPerformanceDownloadRecordByDeptId(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord) {
        return erpAccountPerformanceDownloadRecordMapper.selectErpAccountPerformanceDownloadRecordListByDeptId(erpAccountPerformanceDownloadRecord);
    }

    /**
     * 新增会计绩效下载记录
     * 
     * @param erpAccountPerformanceDownloadRecord 会计绩效下载记录
     * @return 结果
     */
    @Async
    @Override
    public int insertErpAccountPerformanceDownloadRecord(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        erpAccountPerformanceDownloadRecord.setCreateTime(DateUtils.getNowDate());
        return erpAccountPerformanceDownloadRecordMapper.insertErpAccountPerformanceDownloadRecord(erpAccountPerformanceDownloadRecord);
    }

    /**
     * 修改会计绩效下载记录
     * 
     * @param erpAccountPerformanceDownloadRecord 会计绩效下载记录
     * @return 结果
     */
    @Override
    public int updateErpAccountPerformanceDownloadRecord(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        erpAccountPerformanceDownloadRecord.setUpdateTime(DateUtils.getNowDate());
        return erpAccountPerformanceDownloadRecordMapper.updateErpAccountPerformanceDownloadRecord(erpAccountPerformanceDownloadRecord);
    }

    /**
     * 批量删除会计绩效下载记录
     * 
     * @param ids 需要删除的会计绩效下载记录主键
     * @return 结果
     */
    @Override
    public int deleteErpAccountPerformanceDownloadRecordByIds(Long[] ids)
    {
        return erpAccountPerformanceDownloadRecordMapper.deleteErpAccountPerformanceDownloadRecordByIds(ids);
    }

    /**
     * 删除会计绩效下载记录信息
     * 
     * @param id 会计绩效下载记录主键
     * @return 结果
     */
    @Override
    public int deleteErpAccountPerformanceDownloadRecordById(Long id)
    {
        return erpAccountPerformanceDownloadRecordMapper.deleteErpAccountPerformanceDownloadRecordById(id);
    }
}
