package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpContract;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="ErpContractVo",description="合同对象VO")
public class ErpContractVo extends ErpContract {

    @ApiModelProperty("合同类型")
    private String typeName;
    @ApiModelProperty("用户名称")
    private String userName;
    @ApiModelProperty("部门名称")
    private String deptName;
    @ApiModelProperty("订单编号")
    private String vcOrderNumber;
    @ApiModelProperty("城市名称")
    private String cityName;

    @Excel(name = "订单审核状态")
    private String orderAuditStatusStr;
}
