package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 服务订单流转配置对象 erp_biz_shift_user
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@ApiModel(value="ErpBizShiftUserVo",description="服务订单流转配置Vo对象")
public class ErpBizShiftUserVo extends BaseEntity
{

    /** $column.columnComment */
    @ApiModelProperty("主键")
    private Long numId;

    @ApiModelProperty("城市名称")
    private String cityName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty("城市id")
    private Long numCityId;

    /** 0停用，1启用 */
    @Excel(name = "0停用，1启用")
    @ApiModelProperty("0停用，1启用")
    private Long numIsUse;

    /** $column.columnComment */
    @Excel(name = "0停用，1启用")
    @ApiModelProperty("业务员id")
    private Long numOperaUserId;

    @ApiModelProperty("业务员姓名")
    private String numOperaUserName;

    @ApiModelProperty("产品ids")
    private String numErpProductIds;

    @ApiModelProperty("产品names")
    private String vcProductNames;

    /** 业支：1；会计：2；增值：3；法律：4； */
    @Excel(name = "业支：1；会计：2；增值：3；法律：4；")
    @ApiModelProperty("业支：1；会计：2；增值：3；法律：4；")
    private Long numType;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreateUserid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreateTime;

    /** 最后修改人 */
    @Excel(name = "最后修改人")
    @ApiModelProperty("最后修改人")
    private Long numLastUpdUserid;

    /** 最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("最后修改时间")
    private Date datLastUpd;

    /** 是否删除,  默认：0；删除：1 */
    @Excel(name = "是否删除,  默认：0；删除：1")
    @ApiModelProperty("是否删除,  默认：0；删除：1")
    private Long numIsDel;

    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setNumCityId(Long numCityId) 
    {
        this.numCityId = numCityId;
    }

    public Long getNumCityId() 
    {
        return numCityId;
    }
    public void setNumIsUse(Long numIsUse) 
    {
        this.numIsUse = numIsUse;
    }

    public Long getNumIsUse() 
    {
        return numIsUse;
    }
    public void setNumOperaUserId(Long numOperaUserId) 
    {
        this.numOperaUserId = numOperaUserId;
    }

    public Long getNumOperaUserId() 
    {
        return numOperaUserId;
    }
    public void setNumType(Long numType) 
    {
        this.numType = numType;
    }

    public Long getNumType() 
    {
        return numType;
    }
    public void setNumCreateUserid(Long numCreateUserid) 
    {
        this.numCreateUserid = numCreateUserid;
    }

    public Long getNumCreateUserid() 
    {
        return numCreateUserid;
    }
    public void setDatCreateTime(Date datCreateTime) 
    {
        this.datCreateTime = datCreateTime;
    }

    public Date getDatCreateTime() 
    {
        return datCreateTime;
    }
    public void setNumLastUpdUserid(Long numLastUpdUserid) 
    {
        this.numLastUpdUserid = numLastUpdUserid;
    }

    public Long getNumLastUpdUserid() 
    {
        return numLastUpdUserid;
    }
    public void setDatLastUpd(Date datLastUpd) 
    {
        this.datLastUpd = datLastUpd;
    }

    public Date getDatLastUpd() 
    {
        return datLastUpd;
    }
    public void setNumIsDel(Long numIsDel) 
    {
        this.numIsDel = numIsDel;
    }

    public Long getNumIsDel() 
    {
        return numIsDel;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getNumErpProductIds() {
        return numErpProductIds;
    }

    public void setNumErpProductIds(String numErpProductIds) {
        this.numErpProductIds = numErpProductIds;
    }

    public String getVcProductNames() {
        return vcProductNames;
    }

    public void setVcProductNames(String vcProductNames) {
        this.vcProductNames = vcProductNames;
    }

    public String getNumOperaUserName() {
        return numOperaUserName;
    }

    public void setNumOperaUserName(String numOperaUserName) {
        this.numOperaUserName = numOperaUserName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("numCityId", getNumCityId())
            .append("numIsUse", getNumIsUse())
            .append("numOperaUserId", getNumOperaUserId())
            .append("numType", getNumType())
            .append("numCreateUserid", getNumCreateUserid())
            .append("datCreateTime", getDatCreateTime())
            .append("numLastUpdUserid", getNumLastUpdUserid())
            .append("datLastUpd", getDatLastUpd())
            .append("numIsDel", getNumIsDel())
            .toString();
    }
}
