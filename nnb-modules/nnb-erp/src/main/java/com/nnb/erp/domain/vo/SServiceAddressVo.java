package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SServiceAddressVo {


    @Excel(name = "公司名称")
    private String vcCompanyName;

    @Excel(name = "企业等级")
    private String companyLevel;

    @Excel(name = "联系人")
    private String contactName;

    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signTime;

    @Excel(name = "销售")
    private String sellerName;

    @Excel(name = "收款金额")
    private BigDecimal collectionFee;

    @Excel(name = "退款金额")
    private BigDecimal refundPrice;

    @Excel(name = "产品")
    private String productName;

    @Excel(name = "产品数量")
    private Long number;

    @Excel(name = "税号")
    private String vcDutyParagraph;

    @Excel(name = "注册地址")
    private String vcRegisterAddress;

    @Excel(name = "法人")
    private String legalPersonName;

    @Excel(name = "续费开始月", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addressStart;

    /** 续费截止月 */
    @Excel(name = "续费截止月", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addressEnd;

    @Excel(name = "续费状态")
    private String renewStatusName;
}
