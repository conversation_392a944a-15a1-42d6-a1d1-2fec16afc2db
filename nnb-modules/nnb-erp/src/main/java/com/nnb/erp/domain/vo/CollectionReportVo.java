package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CollectionReportVo {

    private Long maxReturnId;
    private Long orderId;
    private Integer isElectronicContract;
    private Long payeeUserId;
    private Long serviceUserId;
    private String performanceUserIdStr;
    private String retainageReturnDetailIds;
    private List<Long> performanceUserIdList;
    private BigDecimal productPrice;
    private Long numCouponId;
    private Long numCombinedActivityId;
    private BigDecimal orderDiscountAmount;
    private String followIds;
    private Long productId;
    private Long pointStatusIsEnd;
    private Long smTypeBeforeZz;
    private Long smServiceType;
    private Long serviceOrderId;
    private Long numUserId;
    private Long pdServiceType;
    private BigDecimal gtKp;
    private BigDecimal invoiceCost;

//    private BigDecimal productNumtotalPrice;



    @Excel(name = "订单编号")
    private String vcOrderNumber;

    @Excel(name = "合同编号")
    private String contractNumber;

    @Excel(name = "服务单ID")
    private Long serviceMainId;

    @Excel(name = "城市")
    private String city;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datSigningDate;

    @Excel(name = "签约人")
    private String signer;

    @Excel(name = "签约一级部门")
    private String signSecondDeptName;

    @Excel(name = "签约部门")
    private String signDeptName;

    @Excel(name = "收款人")
    private String payee;

    @Excel(name = "收款一级部门")
    private String payeeSecondDeptName;

    @Excel(name = "收款部门")
    private String payeeDeptName;

    @Excel(name = "分业绩人")
    private String performanceName;

    @Excel(name = "分业绩部门")
    private String performanceDeptName;

    @Excel(name = "企业名称")
    private String enterpriseName;

    @Excel(name = "企业联系人")
    private String enterpriseContactName;

    @Excel(name = "纳税类型")
    private String vcCorporateProperty;

    @Excel(name = "产品大类")
    private String vcClassificationName;

    @Excel(name = "产品名称")
    private String vcServiceName;

    @Excel(name = "三级分类")
    private String vcProductName;

    @Excel(name = "服务类型")
    private String serviceTypeName;

    @Excel(name = "购买数量")
    private Integer numProductCount;

    @Excel(name = "产品原价")
    private BigDecimal productOriginalPrice;

    @Excel(name = "优惠类型")
    private String discountType;

    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    @Excel(name = "订单总金额")
    private BigDecimal orderPrice;

    @Excel(name = "订单总应收")
    private BigDecimal orderTotalPrice;

    @Excel(name = "本次收款金额")
    private BigDecimal collectionPrice;

    @Excel(name = "本次收款使用预存金额")
    private BigDecimal activitiesSelfPrice;
//
    @Excel(name = "分业绩金额综合")
    private BigDecimal performancePrice;
//
    @Excel(name = "订单尾款")
    private BigDecimal numLastPrice;

    @Excel(name = "产品应收")
    private BigDecimal productNumPayPrice;

    @Excel(name = "产品实收")
    private BigDecimal productPayPrice;

    @Excel(name = "绩效成本-个体开票")
    private BigDecimal performanceCostGTKP;

    @Excel(name = "产品尾款")
    private BigDecimal productNumLastPrice;

    @Excel(name = "产品状态")
    private String productStatus;

    @Excel(name = "是否完结",readConverterExp = "1=是,2=否")
    private Integer isFinish;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datFinanceCollectionTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "订单创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderCreateTime;

    @Excel(name = "收款备注")
    private String payeeRemark;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "转增值时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date toIncrementTime;

//    @Excel(name = "纯记账/首次托管")
//    private String firstTrusteeship;
//
//    @Excel(name = "首次记账赠送月份")
//    private String firstAccountMonth;

    @Excel(name = "是否退款")
    private String isRefund;

    @Excel(name = "退款类型")
    private String refoundType;

    @Excel(name = "退款金额")
    private BigDecimal refoundPrice;

    @Excel(name = "服务单状态")
    private String serviceTypeStatusName;

    @Excel(name = "搁置状态")
    private String shelveStatus;

    @Excel(name = "服务单当前跟进人部门")
    private String serviceUserDept;

    @Excel(name = "服务单业务对接人")
    private String serviceUserName;

    @Excel(name = "服务单当前服务节点")
    private String servicePointName;

    @Excel(name = "服务单当前节点状态")
    private String servicePointStatusName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记账开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date acStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记账结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date acEnd;

    @Excel(name = "服务月数")
    private String months;
}
