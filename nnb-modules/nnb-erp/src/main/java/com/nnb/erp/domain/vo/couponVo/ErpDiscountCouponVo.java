package com.nnb.erp.domain.vo.couponVo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 优惠券对象 erp_discount_coupon
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@ApiModel(value="ErpDiscountCoupon",description="优惠券对象")
@Data
public class ErpDiscountCouponVo
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long id;

    /** 优惠券额度id */
    @ApiModelProperty("优惠券额度id")
    private Long discountCouponAmountId;

    /** 优惠券额度 */
    @ApiModelProperty("优惠券额度")
    private BigDecimal discountAmount;

    /** 所属人id */
    @ApiModelProperty("所属人id")
    private String belongUser;

    /** 状态，0：未使用，1：已使用，2：已失效 */
    @ApiModelProperty("状态，0：未使用，1：已使用，2：已失效")
    private Long status;

    @ApiModelProperty("部门")
    private String dept;

    @ApiModelProperty("产品id")
    private String numProductId;

    @ApiModelProperty("线索id")
    private String clueId;

    @ApiModelProperty("线索id")
    private String clientId;

    @ApiModelProperty("线索id")
    private String createTime;

    private String createName;

    private String vcProductName;
    private String vcCompanyName;



}
