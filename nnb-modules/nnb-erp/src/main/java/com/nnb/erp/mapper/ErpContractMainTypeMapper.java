package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpContractMainType;

/**
 * 合同主体类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
public interface ErpContractMainTypeMapper 
{
    /**
     * 查询合同主体类型
     * 
     * @param id 合同主体类型主键
     * @return 合同主体类型
     */
    public ErpContractMainType selectErpContractMainTypeById(Long id);

    /**
     * 查询合同主体类型列表
     * 
     * @param erpContractMainType 合同主体类型
     * @return 合同主体类型集合
     */
    public List<ErpContractMainType> selectErpContractMainTypeList(ErpContractMainType erpContractMainType);

    /**
     * 新增合同主体类型
     * 
     * @param erpContractMainType 合同主体类型
     * @return 结果
     */
    public int insertErpContractMainType(ErpContractMainType erpContractMainType);

    /**
     * 修改合同主体类型
     * 
     * @param erpContractMainType 合同主体类型
     * @return 结果
     */
    public int updateErpContractMainType(ErpContractMainType erpContractMainType);

    /**
     * 删除合同主体类型
     * 
     * @param id 合同主体类型主键
     * @return 结果
     */
    public int deleteErpContractMainTypeById(Long id);

    /**
     * 批量删除合同主体类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpContractMainTypeByIds(Long[] ids);

    public List<ErpContractMainType> getErpContractMainTypeByMainId(Long mainId);
}
