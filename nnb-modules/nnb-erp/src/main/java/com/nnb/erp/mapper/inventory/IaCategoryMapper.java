package com.nnb.erp.mapper.inventory;

import java.util.List;

import com.nnb.erp.domain.inventory.IaCategory;
import org.apache.ibatis.annotations.Param;

/**
 * 存货核算大类Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface IaCategoryMapper {
    /**
     * 查询存货核算大类
     *
     * @param id 存货核算大类主键
     * @return 存货核算大类
     */
    public IaCategory selectIaCategoryById(Long id);

    /**
     * 查询存货核算大类列表
     *
     * @param iaCategory 存货核算大类
     * @return 存货核算大类集合
     */
    public List<IaCategory> selectIaCategoryList(IaCategory iaCategory);

    /**
     * 新增存货核算大类
     *
     * @param iaCategory 存货核算大类
     * @return 结果
     */
    public int insertIaCategory(IaCategory iaCategory);

    /**
     * 修改存货核算大类
     *
     * @param iaCategory 存货核算大类
     * @return 结果
     */
    public int updateIaCategory(IaCategory iaCategory);

    /**
     * 删除存货核算大类
     *
     * @param id 存货核算大类主键
     * @return 结果
     */
    public int deleteIaCategoryById(Long id);

    /**
     * 批量删除存货核算大类
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIaCategoryByIds(@Param("ids") List<Long> ids);

    List<IaCategory> selectIaCategoryTreeList(@Param("iaCategory") IaCategory iaCategory);

    List<IaCategory> selectChildList(@Param("iaCategory") IaCategory iaCategory);

    int countByName(@Param("iaCategory") IaCategory iaCategory);

    List<Long> selectIdsByName(@Param("iaCategory") IaCategory iaCategory);

    List<IaCategory> selectByNameList(@Param("categoryNames") List<String> categoryNames,
                                      @Param("type") Integer type, @Param("companyId") Long companyId);
}
