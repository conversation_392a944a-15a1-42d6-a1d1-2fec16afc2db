package com.nnb.erp.mapper;

import com.nnb.erp.domain.ErpRetainageReturnDetail;
import com.nnb.erp.domain.vo.ErpRetainageReturnForRetainageReturnDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 尾款回款详情，mapper。
 *
 * <AUTHOR>
 * @since 2022-04-06 09:33:56
 */
public interface ErpRetainageReturnDetailMapper {

    /**
     * 新增尾款回款详情。
     *
     * @param erpRetainageReturnDetail 尾款回款详情实体。
     * @return 受影响行数。
     * <AUTHOR>
     * @since 2022-04-06 09:32:56
     */
    public int insertErpRetainageReturnDetail(ErpRetainageReturnDetail erpRetainageReturnDetail);

    public List<ErpRetainageReturnDetail> selectList(ErpRetainageReturnDetail erpRetainageReturnDetail);

    /**
     * 修改尾款回款详情。
     *
     * @param erpRetainageReturnDetail 尾款回款详情实体。
     * @return 受影响行数。
     * <AUTHOR>
     * @since 2022-04-06 09:33:25
     */
    public int updateErpRetainageReturnDetail(ErpRetainageReturnDetail erpRetainageReturnDetail);

    int updateErpRetainageReturnDetailStatus(ErpRetainageReturnDetail erpRetainageReturnDetailUpdate);

    /**
     * 获取指定业务审核的尾款详情。
     *
     * @param orderId           订单标识。
     * @param retainageReturnId 回款标识。
     * @return 返回尾款详情集合。
     * <AUTHOR>
     * @since 2022-04-20 17:07:04
     */
    public List<ErpRetainageReturnForRetainageReturnDTO> getRetainagesById(@Param("orderId") Long orderId, @Param("retainageReturnId") Long retainageReturnId);

    public List<ErpRetainageReturnDetail> getErpRetainageReturnDetail(Long numServiceOrderId);

    List<ErpRetainageReturnDetail> selectErpRetainageReturnDetailList(@Param("serviceOrderId") Long serviceOrderId);

    List<ErpRetainageReturnDetail> getErpRetainageReturnDetailByType();

    ErpRetainageReturnDetail selectFirstOrderMoney(Long numServiceOrderId);

    @Update("update erp_retainage_return_detail set num_status = #{numStatus} where num_retainage_return_id = #{numRetainageReturnId}")
    void cancleErpRetainageReturnStatusByRetainageReturnId(@Param("numRetainageReturnId") Long numRetainageReturnId, @Param("numStatus") Integer numStatus);

}
