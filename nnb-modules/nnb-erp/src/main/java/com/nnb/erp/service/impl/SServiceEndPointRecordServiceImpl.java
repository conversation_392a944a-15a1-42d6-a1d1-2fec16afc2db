package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.SServiceEndPointRecordMapper;
import com.nnb.erp.domain.SServiceEndPointRecord;
import com.nnb.erp.service.ISServiceEndPointRecordService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-08
 */
@Service
public class SServiceEndPointRecordServiceImpl implements ISServiceEndPointRecordService 
{
    @Autowired
    private SServiceEndPointRecordMapper sServiceEndPointRecordMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SServiceEndPointRecord selectSServiceEndPointRecordById(Long id)
    {
        return sServiceEndPointRecordMapper.selectSServiceEndPointRecordById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sServiceEndPointRecord 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SServiceEndPointRecord> selectSServiceEndPointRecordList(SServiceEndPointRecord sServiceEndPointRecord)
    {
        return sServiceEndPointRecordMapper.selectSServiceEndPointRecordList(sServiceEndPointRecord);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sServiceEndPointRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSServiceEndPointRecord(SServiceEndPointRecord sServiceEndPointRecord)
    {
        return sServiceEndPointRecordMapper.insertSServiceEndPointRecord(sServiceEndPointRecord);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sServiceEndPointRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSServiceEndPointRecord(SServiceEndPointRecord sServiceEndPointRecord)
    {
        return sServiceEndPointRecordMapper.updateSServiceEndPointRecord(sServiceEndPointRecord);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSServiceEndPointRecordByIds(Long[] ids)
    {
        return sServiceEndPointRecordMapper.deleteSServiceEndPointRecordByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSServiceEndPointRecordById(Long id)
    {
        return sServiceEndPointRecordMapper.deleteSServiceEndPointRecordById(id);
    }
}
