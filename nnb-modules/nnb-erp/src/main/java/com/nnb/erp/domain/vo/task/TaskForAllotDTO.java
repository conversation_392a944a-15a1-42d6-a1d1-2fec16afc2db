package com.nnb.erp.domain.vo.task;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 任务，用于分配，DTO。
 *
 * <AUTHOR>
 * @since 2022/7/6 16:28
 */
@Data
public class TaskForAllotDTO {

    /**
     * 任务标识。
     */
    @ApiModelProperty("任务标识。")
    private Integer taskId;
    @ApiModelProperty("任务标识。")
    private List<Integer> taskIds;
    /**
     * 分配类型：1-分配；2-搁置。
     */
    @ApiModelProperty("分配类型：1-分配；2-搁置。3-变更任务")
    private Integer allotType;

    /**
     * 执行人标识。
     */
    @ApiModelProperty("执行人标识。")
    private Integer userId;

    /**
     * 原因。
     */
    @ApiModelProperty("原因。")
    private String reason;

    @ApiModelProperty("原因。")
    private String content;

    @ApiModelProperty("分配时间。")
    private String allotExecuteTime;

    @ApiModelProperty("任务地点")
    private String address;

    @ApiModelProperty("企业名称")
    private String vcCompanyName;

    @ApiModelProperty("")
    private String areaName;

    @ApiModelProperty("")
    private BigDecimal workHour;
}
