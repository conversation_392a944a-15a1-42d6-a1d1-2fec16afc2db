package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpBizLicenseType;

/**
 * 执照类型配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface ErpBizLicenseTypeMapper 
{
    /**
     * 查询执照类型配置
     * 
     * @param numId 执照类型配置主键
     * @return 执照类型配置
     */
    public ErpBizLicenseType selectErpBizLicenseTypeByNumId(Long numId);

    /**
     * 查询执照类型配置列表
     * 
     * @param erpBizLicenseType 执照类型配置
     * @return 执照类型配置集合
     */
    public List<ErpBizLicenseType> selectErpBizLicenseTypeList(ErpBizLicenseType erpBizLicenseType);

    /**
     * 新增执照类型配置
     * 
     * @param erpBizLicenseType 执照类型配置
     * @return 结果
     */
    public int insertErpBizLicenseType(ErpBizLicenseType erpBizLicenseType);

    /**
     * 修改执照类型配置
     * 
     * @param erpBizLicenseType 执照类型配置
     * @return 结果
     */
    public int updateErpBizLicenseType(ErpBizLicenseType erpBizLicenseType);

    /**
     * 删除执照类型配置
     * 
     * @param numId 执照类型配置主键
     * @return 结果
     */
    public int deleteErpBizLicenseTypeByNumId(Long numId);

    /**
     * 批量删除执照类型配置
     * 
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizLicenseTypeByNumIds(Long[] numIds);
}
