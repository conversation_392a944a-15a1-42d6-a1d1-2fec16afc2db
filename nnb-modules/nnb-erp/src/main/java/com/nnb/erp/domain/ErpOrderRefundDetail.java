package com.nnb.erp.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 订单退款明细对象 erp_order_refund_detail
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ErpOrderRefundDetail",description="订单退款明细对象")
public class ErpOrderRefundDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long id;

    /** 订单退款表主键id */
    @Excel(name = "订单退款表主键id")
    @ApiModelProperty("订单退款表主键id")
    private Long orderRefundId;

    /** 订单产品表主键id */
    @Excel(name = "订单产品表主键id")
    @ApiModelProperty("订单产品表主键id")
    private Long serviceOrdersId;

    /** 产品id */
    @Excel(name = "产品id")
    @ApiModelProperty("产品id")
    private Long productId;

    /** 产品名称id */
    @Excel(name = "产品名称id")
    @ApiModelProperty("产品名称id")
    private Long productNameId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    @ApiModelProperty("产品名称")
    private String productName;

    /** 服务的类型id */
    @Excel(name = "服务的类型id")
    @ApiModelProperty("服务的类型id")
    private Long serviceOrderTypeId;

    /** 服务的类型 */
    @Excel(name = "服务的类型")
    @ApiModelProperty("服务的类型")
    private String serviceOrderType;

    /** 自定义服务类型id */
    @Excel(name = "自定义服务类型id")
    @ApiModelProperty("自定义服务类型id")
    private Long customServiceTypeId;

    /** 自定义服务类型 */
    @Excel(name = "自定义服务类型")
    @ApiModelProperty("自定义服务类型")
    private String customServiceType;

    /** 纳税类型id */
    @Excel(name = "纳税类型id")
    @ApiModelProperty("纳税类型id")
    private String taxTypeId;

    /** 纳税类型 */
    @Excel(name = "纳税类型")
    @ApiModelProperty("纳税类型")
    private String taxType;

    /** 单位 */
    @Excel(name = "单位")
    @ApiModelProperty("单位")
    private String unit;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty("城市id")
    private String cityId;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty("城市")
    private String cityName;

    /** 价格 */
    @Excel(name = "价格")
    @ApiModelProperty("价格")
    private BigDecimal price;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /** 优惠券 */
    @Excel(name = "优惠券")
    @ApiModelProperty("优惠券")
    private String couponName;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    /** 渠道费 */
    @Excel(name = "渠道费")
    @ApiModelProperty("渠道费")
    private BigDecimal channelAmount;

    /** 应收金额 */
    @Excel(name = "应收金额")
    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    /** 实收金额-赠金金额 */
    @Excel(name = "实收金额-赠金金额")
    @ApiModelProperty("实收金额-赠金金额")
    private BigDecimal paidAmount;

    /** 实收金额 */
    @Excel(name = "实收金额")
    @ApiModelProperty("实收金额")
    private BigDecimal payAmount;

    /** 尾款 */
    @Excel(name = "尾款")
    @ApiModelProperty("尾款")
    private BigDecimal tailAmount;

    /** 退款金额 */
    @Excel(name = "订单产品退款金额")
    @ApiModelProperty("订单产品退款金额")
    private BigDecimal serviceOrderRefundAmount;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    /** 退款类型1：全额退款，2：部分退款 */
    @Excel(name = "退款类型1：全额退款，2：部分退款")
    @ApiModelProperty("退款类型1：全额退款，2：部分退款")
    private Short refundType;

    /** 作废状态通erp_service_orders */
    @Excel(name = "作废状态通erp_service_orders")
    @ApiModelProperty("作废状态通erp_service_orders")
    private Integer voidStatus;

    /** service_orders修改前状态 */
    @Excel(name = "service_orders修改前状态")
    @ApiModelProperty("service_orders修改前状态")
    private Integer voidStatusBefore;

    /** 服务单修改前状态 */
    @Excel(name = "服务单修改前状态")
    @ApiModelProperty("服务单修改前状态")
    private Integer serviceStatusBefore;

    private String voidStatusStr;

    private List<Long> ids;
}
