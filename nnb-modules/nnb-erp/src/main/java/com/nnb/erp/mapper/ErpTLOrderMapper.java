package com.nnb.erp.mapper;

import com.nnb.erp.domain.tLOrder.TlClue;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-02-07
 * @Version: 1.0
 */
public interface ErpTLOrderMapper {

    /**
     * 查询螳螂线索关系
     *
     * @param id 螳螂线索关系主键
     * @return 螳螂线索关系
     */
    public TlClue selectTlClueById(Long id);

    /**
     * 查询螳螂线索关系列表
     *
     * @param tlClue 螳螂线索关系
     * @return 螳螂线索关系集合
     */
    public List<TlClue> selectTlClueList(TlClue tlClue);

    /**
     * 新增螳螂线索关系
     *
     * @param tlClue 螳螂线索关系
     * @return 结果
     */
    public int insertTlClue(TlClue tlClue);

    /**
     * 修改螳螂线索关系
     *
     * @param tlClue 螳螂线索关系
     * @return 结果
     */
    public int updateTlClue(TlClue tlClue);

    /**
     * 删除螳螂线索关系
     *
     * @param id 螳螂线索关系主键
     * @return 结果
     */
    public int deleteTlClueById(Long id);

    /**
     * 批量删除螳螂线索关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTlClueByIds(Long[] ids);

    @Select("select vc_customer_name from bd_clue where id = #{numClueId}")
    String selectTlClueName(@Param("numClueId") Long numClueId);
}
