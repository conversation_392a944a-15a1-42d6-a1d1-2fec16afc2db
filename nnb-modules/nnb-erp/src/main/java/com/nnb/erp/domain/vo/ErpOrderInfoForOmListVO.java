package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.constant.enums.OrderInvalidStatusEnum;
import com.nnb.erp.constant.enums.OrderStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单信息，用于订单管理列表，VO。
 *
 * <AUTHOR>
 * @since 2022/3/24 10:58
 */
@Data
@ApiModel(value = "ErpOrderInfoForOmListVO", description = "订单信息，用于订单管理列表，VO。")
public class ErpOrderInfoForOmListVO {

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private Long orderId;

    /**
     * 订单编号。
     */
    @ApiModelProperty("订单编号。")
    @Excel(name = "订单编号")
    private String orderNumber;

    /**
     * 订单来源类型。
     */
    @ApiModelProperty("订单来源类型。")
    private Integer orderSourceType;

    /**
     * 订单来源名称。
     */
    @ApiModelProperty("订单来源名称。")
    @Excel(name = "订单来源名称")
    private String orderSourceName;

    /**
     * 企业名称/个人客户，客户名称。
     */
    @ApiModelProperty("企业名称/个人客户，客户名称。")
    @Excel(name = "企业名称/个人客户，客户名称")
    private String clientName;

    /**
     * 企业名称/个人客户Id，客户Id
     */
    @ApiModelProperty("企业名称/个人客户Id，客户Id。")
    private Long clientId;

    /**
     * 联系人，线索联系人。
     */
    @ApiModelProperty("联系人，线索联系人。")
    @Excel(name = "联系人")
    private String clueCustomerName;

    /**
     * 所属部门标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("所属部门标识。")
    private Long deptId;

    /**
     * 所属部门名称。
     */
    @ApiModelProperty("所属部门名称。")
    @Excel(name = "所属部门名称")
    private String deptName;

    /**
     * 签约人标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("签约人标识。")
    private Long userId;

    /**
     * 签约人名称。
     */
    @ApiModelProperty("签约人名称。")
    @Excel(name = "签约人名称")
    private String userName;

    /**
     * 签约时间，订单签约时间。
     */
    @ApiModelProperty("签约时间，订单签约时间。")
    @Excel(name = "签约时间")
    private String signingDate;

    /**
     * 优惠金额，总优惠金额。
     */
    @ApiModelProperty("优惠金额，总优惠金额。")
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 应收，总应收。
     */
    @ApiModelProperty("应收，总应收。")
    @Excel(name = "应收金额")
    private BigDecimal totalPrice;


    @ApiModelProperty("联系人手机号。")
    private String clueCustomerPhone;
    /**
     * 实收，总实收。
     */
    @ApiModelProperty("实收，总实收。")
    @Excel(name = "实收")
    private BigDecimal payPrice;

    /**
     * 尾款，总尾款。
     */
    @ApiModelProperty("尾款，总尾款。")
    @Excel(name = "尾款")
    private BigDecimal lastPrice;

    /**
     * 退款金额，服务单退款总和。
     */
    @ApiModelProperty("退款金额，服务单退款总和。")
    private BigDecimal sumRefundPrice;

    /**
     * 创建时间,订单创建时间。
     */
    @ApiModelProperty("创建时间,订单创建时间。")
    @Excel(name = "创建时间")
    private String createTime;

    @ApiModelProperty("订单财务收款时间。")
    @Excel(name = "订单财务收款时间")
    private String datFinanceCollectionTime;

    @ApiModelProperty("销售收款时间")
    @Excel(name = "销售收款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectionTime;

    /**
     * 回款记录。
     */
    @ApiModelProperty("回款记录。")
    private List<ErpRetainageForOmListVO> retainages;

    /**
     * 产品信息。
     */
    @ApiModelProperty("产品信息。")
    private List<ErpProductForOrderDetailVO> serviceOrders;

    /**
     * 合同号。
     */
    @ApiModelProperty("合同号。")
    @Excel(name = "合同号")
    private String vcContractNumber;

    /**
     * 总金额。
     */
    @ApiModelProperty("总金额。")
    private BigDecimal allPrice;

    /**
     * 订单所有状态。
     */
    @ApiModelProperty("订单审核所有状态。")
    private ErpOrderStatusVO orderStatusInfo;

    /**
     * 订单备注。
     */
    @ApiModelProperty("订单备注。")
    private String remark;

    /**
     * 订单状态。
     */
    @ApiModelProperty("订单状态。")
    private Integer orderStatusType;

    /**
     * 订单状态。
     */
    @ApiModelProperty("订单状态。")
    @Excel(name = "订单状态")
    private String orderStatusTypeStr;

    @ApiModelProperty("合同主体。")
    private Integer contractSubject;

    @ApiModelProperty("手机号。")
    private String vcPhone;

    @ApiModelProperty("线上合同PDF")
    private String vcOnlineContractPdf;

    @ApiModelProperty("是否电子合同: 1 是 ， 2：否")
    private Integer isElectronicContract;

    @ApiModelProperty("密文")
    private String cipherId;

    @ApiModelProperty("订单审核状态")
    @Excel(name = "订单审核状态")
    private String orderAuditStatusStr;

    @ApiModelProperty("提单审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过 7--法务审核驳回，8-法务审核通过，9-法务待审核。")
    private Integer numCreateOrderExamineStatus;

    /**
     * 修改审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过。
     */
    @ApiModelProperty("修改审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过,7-法务审核驳回，8-法务审核通过，9-法务待审核。")
    private Integer numModifyOrderExamineStatus;

    /**
     * 作废审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过。
     */
    @ApiModelProperty("作废审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过。")
    private Integer numCancelOrderExamineStatus;

    /**
     * 退款审核状态；0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过。
     */
    @ApiModelProperty("退款审核状态：0-默认值，未参与；1-经理待审核；2-经理审核驳回，3-财务待审核，4-财务审核驳回，5-财务审核通过。")
    private Integer numRefundExamineStatus;

    private Integer status;

    /**
     * 作废状态：0：有效；1：已撤销；2：已作废；3：已退款
     */
    private Integer numValidStatus;
    private String validStatusStr;

    /**
     * 提单来源  1：客保提单；2：客户提单
     */
    @ApiModelProperty("提单来源  1：客保提单；2：客户提单")
    private Integer commitOrderType;

    @ApiModelProperty("客户联系人。")
    private String contactName;

    @ApiModelProperty("客户联系电话。")
    private String contactPhone;

    @ApiModelProperty("有无记账")
    private int isAccount;

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("线索id")
    private Long clueId;

    @ApiModelProperty("电子合同是否重新签约，1是，0否")
    private Integer isReturn;

    @ApiModelProperty("实退款金额")
    private BigDecimal refundPayPrice;

    @ApiModelProperty("订单是否可以开票1是2否")
    private Integer canKP;

    @ApiModelProperty("订单是否已经开票1是2否")
    private Integer overKP;

    @ApiModelProperty("可开票金额")
    private BigDecimal canKpFee;

    public void setOrderStatusType(Integer orderStatusType){
        this.orderStatusType = orderStatusType;
        this.orderStatusTypeStr = OrderStatusEnum.getNameByType(orderStatusType);
    }


    public void setNumValidStatus(Integer numValidStatus){
        this.numValidStatus = numValidStatus;
        this.validStatusStr = OrderInvalidStatusEnum.getNameByType(numValidStatus);
    }

}
