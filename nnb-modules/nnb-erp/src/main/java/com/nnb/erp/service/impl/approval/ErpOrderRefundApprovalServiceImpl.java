package com.nnb.erp.service.impl.approval;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.OrderRefundConstants;
import com.nnb.erp.constant.enums.OrderInvalidStatusEnum;
import com.nnb.erp.constant.enums.OrderStatusEnum;
import com.nnb.erp.constant.enums.OrderVoidStatusEnum;
import com.nnb.erp.constant.enums.ServiceMainStatusEnum;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.vo.approval.ErpOrderRefundApprovalVo;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftDetailVO;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.service.DingDingService;
import com.nnb.erp.service.IErpExamineApproveService;
import com.nnb.erp.service.approval.IErpOrderRefundApprovalService;
import com.nnb.erp.util.MybatisBatchUtils;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-09-14
 * @Version: 1.0
 */
@Service
public class ErpOrderRefundApprovalServiceImpl implements IErpOrderRefundApprovalService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private ErpOrdersMapper erpOrdersMapper;

    @Autowired
    private ErpServiceOrdersMapper erpServiceOrdersMapper;

    @Autowired
    private ErpOrderRefundMapper erpOrderRefundMapper;

    @Autowired
    private ErpOrderRefundDetailMapper erpOrderRefundDetailMapper;

    @Autowired
    private MybatisBatchUtils mybatisBatchUtils;

    @Autowired
    private SServiceMainMapper sServiceMainMapper;

    @Autowired
    private IErpExamineApproveService erpExamineApproveService;

    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;

    @Autowired
    private ErpTransactionVoucherFollowMapper erpTransactionVoucherFollowMapper;

    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;

    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;

    @Autowired
    private DingDingService dingDingService;


    @Value("${erp.order.refund.consigner.dingId}")
    private String erpOrderRefundConsignerId;

    @Resource
    private ErpRetainageReturnMapper erpRetainageReturnMapper;


    @Override
    public ErpOrderRefund getApprovalMsg(ErpOrderRefundApprovalVo vo) {
        //查询订单信息
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(vo.getOrderId());
        if (Arrays.asList(1,3,9,10,12).contains(erpOrders.getNumModifyOrderExamineStatus())) {
            throw new ServiceException("该订单修改审核中");
        }
        if (Arrays.asList(1,3,9,10,12).contains(erpOrders.getNumCancelOrderExamineStatus())) {
            throw new ServiceException("该订单作废审核中");
        }
        BigDecimal inApproveFee = erpOrdersMapper.getKPFeeByOrderIdAndApproveStatus(vo.getOrderId(), 0L);
        if (inApproveFee.compareTo(new BigDecimal("0")) > 0) {
            throw new ServiceException("该订单开票审核中");
        }
        //获取订单绑定的流水记录
        List<ErpTransactionVoucherFollow> voucherFollowList = erpTransactionVoucherFollowMapper.selectListByOrderId(vo.getOrderId());
        if (ObjectUtil.isNotEmpty(voucherFollowList) && voucherFollowList.size() > 0) {
            //本金金额
            BigDecimal selfGold = new BigDecimal("0");
            for (int i = 0; i < voucherFollowList.size(); i++) {
                ErpTransactionVoucherFollow voucherFollow = voucherFollowList.get(i);
                if (voucherFollow.getStatus().intValue() == 2) {
                    ErpTransactionVoucher voucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(voucherFollow.getTransactionVoucher());
                    if (voucher.getType().intValue() != 3) {
                        selfGold = selfGold.add(voucherFollow.getFee());
                    }
                }
            }
            if (selfGold.compareTo(new BigDecimal("0")) <= 0) {
                throw new ServiceException("该订单使用了赠金，暂无剩余可退金额");
            }
        }


        //查询签约人
        R<SysUser> RUser = remoteUserService.getUserInfoById(erpOrders.getNumUserId(), SecurityConstants.INNER);
        //构建主要信息
        ErpOrderRefund erpOrderRefund = new ErpOrderRefund()
                .setOrderId(erpOrders.getId())
                .setOrderNum(erpOrders.getVcOrderNumber())
                .setSignUserId(erpOrders.getNumUserId())
                .setSignUserName(RUser.getData().getUserName());

        //构建详情
        //查询订单产品信息
        List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(vo.getOrderId());
        //获取产品id
        if (CollUtil.isNotEmpty(serviceOrdersList)){
            //过滤出可以退款的状态
            List<ErpServiceOrders> filterList = serviceOrdersList.stream()
                    .filter(en -> (OrderVoidStatusEnum.NORMAL.getCode().equals(en.getNumIsDeprecated())
                            || OrderVoidStatusEnum.PARTIAL_REFUND.getCode().equals(en.getNumIsDeprecated())
                            || OrderVoidStatusEnum.PARTIAL_REFUND_PENDING_REVIEW.getCode().equals(en.getNumIsDeprecated()))
                            && en.getNumStatus().equals(1))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(filterList)){
                List<Long> productIdList = filterList.stream()
                        .map(ErpServiceOrders::getNumProductId)
                        .collect(Collectors.toList());
                //查询产品相关信息
                if (CollUtil.isNotEmpty(productIdList)){
                    //组装明细
                    assemblyDetails(erpOrderRefund, filterList, productIdList);
                }
            }
        }
        erpOrderRefund.setKpFee(erpOrdersMapper.getKPFeeByOrderIdAndApproveStatus(vo.getOrderId(), 1L));
        return erpOrderRefund;
    }

    private void assemblyDetails(ErpOrderRefund erpOrderRefund, List<ErpServiceOrders> filterList, List<Long> productIdList) {
        List<ErpOrderRefundDetail> detailList =  erpServiceOrdersMapper.selectProductMsgByProductIdList(productIdList);
        //各服务单的可退款金额综合
        BigDecimal refundPayFee = new BigDecimal("0");

        for (ErpOrderRefundDetail detail : detailList) {
            ErpServiceOrders erpServiceOrders = filterList.stream()
                    .filter(en -> en.getNumProductId().equals(detail.getProductId()))
                    .findAny().orElse(null);
            if (ObjectUtil.isNotEmpty(erpServiceOrders)){
                assert erpServiceOrders != null;
                if (BigDecimal.ZERO.compareTo(erpServiceOrders.getNumCouponPrice()) != 0){
                    detail.setCouponName(String
                            .format(OrderRefundConstants.COUPON_TEM, erpServiceOrders.getNumCouponPrice()));
                }else {
                    detail.setCouponName("");
                }
                BigDecimal giveFee = erpTransactionVoucherFollowInfoMapper.getGiveFeeByOrderIdAndProductId(erpServiceOrders.getNumOrderId(), erpServiceOrders.getNumProductId());
                BigDecimal RefundAmountInApprove = erpOrderRefundDetailMapper.selectRefundAmountByServiceOrderIdInApprove(erpServiceOrders.getId());
                detail.setServiceOrdersId(erpServiceOrders.getId())
                        .setQuantity(new BigDecimal(erpServiceOrders.getNumProductCount()))
                        .setChannelAmount(erpServiceOrders.getNumChannelFee())
                        .setDiscountAmount(erpServiceOrders.getNumCouponPrice())
                        .setReceivableAmount(erpServiceOrders.getNumTotalPrice())
                        .setServiceOrderRefundAmount(erpServiceOrders.getNumRefundPrice().add(RefundAmountInApprove))
                        .setPaidAmount(erpServiceOrders.getNumPayPrice().subtract(giveFee).subtract(detail.getServiceOrderRefundAmount()))
                        .setPayAmount(erpServiceOrders.getNumPayPrice())
                        .setTailAmount(erpServiceOrders.getNumLastPrice())
                        .setRefundAmount(BigDecimal.ZERO)
                        .setVoidStatus(erpServiceOrders.getNumIsDeprecated())
                        .setVoidStatusStr(OrderVoidStatusEnum.getMsgByCode(erpServiceOrders.getNumIsDeprecated()));

                refundPayFee = ObjectUtil.isEmpty(detail.getPaidAmount()) ? refundPayFee : refundPayFee.add(detail.getPaidAmount());
            }
        }
        erpOrderRefund.setRefundPayFee(refundPayFee);
        erpOrderRefund.setErpOrderRefundDetailList(detailList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveApprovalMsg(ErpOrderRefund erpOrderRefund) {
        LoginUser loginUser = tokenService.getLoginUser();
        //校验退款原因
        if (StrUtil.isEmpty(erpOrderRefund.getRefundReason())){
            throw new ServiceException("退款原因不得为空！", 400);
        }
        //查询订单信息
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(erpOrderRefund.getOrderId());
        //封装信息
        erpOrderRefund.setOrderStatusBefore(erpOrders.getNumValidStatus().shortValue())
                .setCreateUserId(loginUser.getUserid())
                .setCreateUserName(loginUser.getUsername())
                .setCreateTime(LocalDateTime.now());
        //校验校验订单实收金额大于0
        if (BigDecimal.ZERO.compareTo(erpOrders.getNumPayPrice()) >= 0){
            throw new ServiceException("该订单实收金额为0，不允许退款！", 400);
        }
        List<ErpOrderRefundDetail> detailList = erpOrderRefund.getErpOrderRefundDetailList();
        //处理退款
        handleRefund(erpOrderRefund, detailList, erpOrders);
        //同步更改订单
        synStatus(erpOrderRefund, detailList);

        //查询订单实收（去赠金）
        BigDecimal giveFee = new BigDecimal("0");
        List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(erpOrderRefund.getOrderId());
        for (int j = 0; j < serviceOrdersList.size(); j++) {
            ErpServiceOrders erpServiceOrders = serviceOrdersList.get(j);
            giveFee = giveFee.add(erpTransactionVoucherFollowInfoMapper.getGiveFeeByOrderIdAndProductId(erpOrderRefund.getOrderId(), erpServiceOrders.getNumProductId()));
        }
        erpOrderRefund.setRefundPayFee(erpOrders.getNumPayPrice().subtract(giveFee));
        erpOrderRefund.setRefundPayDate(erpRetainageReturnMapper.getFirstFinanceCollectionTimeByOrderId(erpOrders.getId()));


        //保存申请信息及插入申请记录
        saveApplicationInformation(erpOrderRefund, detailList);
        //发货人发送钉钉提醒
        sendConsignerDing(erpOrderRefund.getOrderId());
        return true;
    }

    private void sendConsignerDing(Long orderId) {
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(orderId);
        ErpOrderGiftDetailVO erpOrderGiftDetailVO = erpGiftIssueRecordMapper.getErpOrderGiftDetailVO(orderId);
        if (Objects.nonNull(erpOrderGiftDetailVO) && erpOrderGiftDetailVO.getGiftType() == 1 && Arrays.asList(1, 2).contains(erpOrderGiftDetailVO.getGiftStatus())) {
            //发送钉钉消息
            String dingContent = "### 赠品订单发起退款通知： \n * " + "您好： \n "
                    + " * 订单号: " + erpOrders.getVcOrderNumber() + "，该订单签约人已发起退款申请，请核实信息，必要时暂停赠品的邮寄。    \n ";
            DingSendDTO dingSendDTO = new DingSendDTO(erpOrderRefundConsignerId, "审批通过提醒", dingContent);
            dingDingService.sendDingMessage(dingSendDTO);
        }
    }

    /**
     * 保存申请信息及插入申请记录
     * @param erpOrderRefund
     * @param detailList
     */
    private void saveApplicationInformation(ErpOrderRefund erpOrderRefund, List<ErpOrderRefundDetail> detailList) {
        //保存申请信息
        erpOrderRefundMapper.insertErpOrderRefund(erpOrderRefund);
        detailList.forEach(en -> en.setOrderRefundId(erpOrderRefund.getId()));
        //批量插入明细
        mybatisBatchUtils.batchUpdateOrInsert(detailList, ErpOrderRefundDetailMapper.class,
                (detail, erpOrderRefundDetailMapper) ->
                        erpOrderRefundDetailMapper.insertErpOrderRefundDetail(detail));

        //TODO 插入审批记录
        ErpExamineApprove erpExamineApprove = new ErpExamineApprove();
        erpExamineApprove.setApproveType(OrderRefundConstants.REFUND_APPROVAL_TYPE);
        erpExamineApprove.setOtherId(String.valueOf(erpOrderRefund.getId()));
        erpExamineApproveService.insertErpExamineApprove(erpExamineApprove);
    }

    /**
     * 处理退款
     * @param erpOrderRefund
     * @param detailList
     */
    private void handleRefund(ErpOrderRefund erpOrderRefund, List<ErpOrderRefundDetail> detailList, ErpOrders erpOrders) {
        ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
        erpServiceOrders.setNumOrderId(erpOrderRefund.getOrderId());
        List<SServiceMain> sServiceMainList = sServiceMainMapper.selectSServiceMainListByOrderId(erpOrderRefund.getOrderId());
        List<ErpServiceOrders> erpServiceOrdersList = erpServiceOrdersMapper.selectErpServiceOrdersList(erpServiceOrders);
        if (OrderRefundConstants.FULL_REFUND.equals(erpOrderRefund.getRefundType())){
            //全部退款
            fullRefund(erpOrderRefund, detailList, sServiceMainList, erpServiceOrdersList);
        }else {
            //部分退款
            partialRefund(erpOrderRefund, detailList, sServiceMainList, erpServiceOrdersList);
        }
        if (erpOrderRefund.getRefundAmount()
                .compareTo(erpOrders.getNumPayPrice().subtract(erpOrders.getNumRefundPrice())) > 0){
            throw new ServiceException("退款金额应小于等于实收金额 - 退款金额！", 400);
        }
    }

    /**
     * 部分退款
     * @param erpOrderRefund
     * @param detailList
     */
    private void partialRefund(ErpOrderRefund erpOrderRefund, List<ErpOrderRefundDetail> detailList,
                               List<SServiceMain> sServiceMainList, List<ErpServiceOrders> erpServiceOrdersList) {
        List<ErpOrderRefundDetail> filterDetailList = erpOrderRefund.getErpOrderRefundDetailList().stream()
                .filter(en -> BigDecimal.ZERO.compareTo(en.getRefundAmount()) != 0)
                .collect(Collectors.toList());
        //检验产品详情，若退款金额是否＞0且≤实收金额不成立
        for (ErpOrderRefundDetail detail : filterDetailList) {
            if (detail.getRefundAmount()
                    .compareTo(detail.getPaidAmount().subtract(detail.getServiceOrderRefundAmount())) > 0
                    || BigDecimal.ZERO.compareTo(detail.getRefundAmount()) > 0
            ){
                throw new ServiceException("退款金额应大于等于0且小于等于实收金额 - 退款金额！", 400);
            }
            //更改作废状态
            if (detail.getPaidAmount().subtract(detail.getRefundAmount()).compareTo(BigDecimal.ZERO) > 0){
                //若实收金额-退费金额≠0，作废状态变为部分退款待审核
                detail.setRefundType(OrderRefundConstants.PARTIAL_REFUND)
                        .setVoidStatus(OrderVoidStatusEnum.PARTIAL_REFUND_PENDING_REVIEW.getCode());
            }
            if (detail.getPaidAmount().subtract(detail.getRefundAmount()).compareTo(BigDecimal.ZERO) == 0){
                //若实收金额-退费金额=0，作废状态变为退款待审核
                detail.setRefundType(OrderRefundConstants.FULL_REFUND)
                        .setVoidStatus(OrderVoidStatusEnum.REFUND_PENDING_REVIEW.getCode());
            }
            setBeforeStatus(sServiceMainList, erpServiceOrdersList, detail);
        }

        //此时可能所有产品都全部退款，判断然后更改状态
        boolean match = detailList.stream()
                .allMatch(en ->
                        OrderRefundConstants.FULL_REFUND.equals(en.getRefundType()));
        if (match){
            //更改总状态为全部退款
            erpOrderRefund.setRefundType(OrderRefundConstants.FULL_REFUND);
        }
    }

    private void setBeforeStatus(List<SServiceMain> sServiceMainList, List<ErpServiceOrders> erpServiceOrdersList, ErpOrderRefundDetail detail) {
        detail.setServiceStatusBefore(0);
        if (CollUtil.isNotEmpty(sServiceMainList)) {
            SServiceMain sServiceMain = sServiceMainList.stream()
                    .filter(en -> en.getProductId().equals(detail.getProductId()))
                    .findAny().orElse(null);
            if (ObjectUtil.isNotEmpty(sServiceMain)){
                assert sServiceMain != null;
                detail.setServiceStatusBefore(sServiceMain.getServiceStatus().intValue());
            }
        }
        if (CollUtil.isNotEmpty(erpServiceOrdersList)) {
            ErpServiceOrders erpServiceOrders = erpServiceOrdersList.stream()
                    .filter(en -> en.getId().equals(detail.getServiceOrdersId()))
                    .findAny().orElse(null);
            if (ObjectUtil.isNotEmpty(erpServiceOrders)){
                assert erpServiceOrders != null;
                detail.setVoidStatusBefore(erpServiceOrders.getNumIsDeprecated());
            }
        }
    }

    /**
     * 全部退款
     * @param erpOrderRefund
     * @param detailList
     */
    private void fullRefund(ErpOrderRefund erpOrderRefund, List<ErpOrderRefundDetail> detailList,
                            List<SServiceMain> sServiceMainList, List<ErpServiceOrders> erpServiceOrdersList) {
        //处理明细为全额退款
        for (ErpOrderRefundDetail detail : detailList) {
            //更改退款方式为全部退款，更改作废状态为退费待审核，放入退款金额
            detail.setRefundType(OrderRefundConstants.FULL_REFUND)
                    .setRefundAmount(detail.getPaidAmount())
                    .setVoidStatus(OrderVoidStatusEnum.REFUND_PENDING_REVIEW.getCode());
            setBeforeStatus(sServiceMainList, erpServiceOrdersList, detail);
        }
        //退款金额
        BigDecimal reduce = detailList.stream()
                .map(ErpOrderRefundDetail::getPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        erpOrderRefund.setRefundAmount(reduce);
    }

    /**
     * 同步订单，service_orders，服务单状态
     * @param erpOrderRefund
     * @param detailList
     */
    private void synStatus(ErpOrderRefund erpOrderRefund, List<ErpOrderRefundDetail> detailList) {
        if (OrderRefundConstants.FULL_REFUND.equals(erpOrderRefund.getRefundType())){
            //退款待审核-num_valid_status
            erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                    OrderInvalidStatusEnum.INVALID_REFUND_PENDING_REVIEW.getStatusType());
        }
        if (OrderRefundConstants.PARTIAL_REFUND.equals(erpOrderRefund.getRefundType())){
            //部分退款待审核
            erpOrdersMapper.updateOrderNumValidStatusById(erpOrderRefund.getOrderId(),
                    OrderInvalidStatusEnum.INVALID_PARTIAL_REFUND_PENDING_REVIEW.getStatusType());
        }
        //同步serviceOrders状态和服务单状态
        for (ErpOrderRefundDetail detail : detailList) {
            //同步serviceOrders
            erpServiceOrdersMapper.updateNumIsDeprecatedById(detail.getServiceOrdersId(), detail.getVoidStatus());
            //同步服务单状态
            if (OrderRefundConstants.FULL_REFUND.equals(detail.getRefundType())){
                sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                        erpOrderRefund.getOrderId(), detail.getProductId(),
                        ServiceMainStatusEnum.FEES_TO_BE_REFUNDED.getCode());
            }
            if (OrderRefundConstants.PARTIAL_REFUND.equals(detail.getRefundType())){
                sServiceMainMapper.updateServiceStatusByOrderIdAndProductId(
                        erpOrderRefund.getOrderId(), detail.getProductId(),
                        ServiceMainStatusEnum.PENDING_PARTIAL_REFUND.getCode());
            }
        }
    }
}
