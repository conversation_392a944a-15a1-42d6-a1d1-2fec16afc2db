package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpLicenseLogMapper;
import com.nnb.erp.domain.ErpLicenseLog;
import com.nnb.erp.service.IErpLicenseLogService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class ErpLicenseLogServiceImpl implements IErpLicenseLogService 
{
    @Autowired
    private ErpLicenseLogMapper erpLicenseLogMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ErpLicenseLog selectErpLicenseLogById(Long id)
    {
        return erpLicenseLogMapper.selectErpLicenseLogById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ErpLicenseLog> selectErpLicenseLogList(ErpLicenseLog erpLicenseLog)
    {
        return erpLicenseLogMapper.selectErpLicenseLogList(erpLicenseLog);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertErpLicenseLog(ErpLicenseLog erpLicenseLog)
    {
        return erpLicenseLogMapper.insertErpLicenseLog(erpLicenseLog);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateErpLicenseLog(ErpLicenseLog erpLicenseLog)
    {
        return erpLicenseLogMapper.updateErpLicenseLog(erpLicenseLog);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpLicenseLogByIds(Long[] ids)
    {
        return erpLicenseLogMapper.deleteErpLicenseLogByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteErpLicenseLogById(Long id)
    {
        return erpLicenseLogMapper.deleteErpLicenseLogById(id);
    }
}
