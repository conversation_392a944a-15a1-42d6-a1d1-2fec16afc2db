package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 已成交企业标签对象 erp_old_enterprise_tags
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@ApiModel(value="ErpOldEnterpriseTags",description="已成交企业标签对象")
public class ErpOldEnterpriseTags extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long numId;

    /** 企业id */
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    /** 老客户标签id */
    @Excel(name = "老客户标签id")
    @ApiModelProperty("老客户标签id")
    private Long numUserTagId;

    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setEnterpriseId(Long enterpriseId) 
    {
        this.enterpriseId = enterpriseId;
    }

    public Long getEnterpriseId() 
    {
        return enterpriseId;
    }
    public void setNumUserTagId(Long numUserTagId) 
    {
        this.numUserTagId = numUserTagId;
    }

    public Long getNumUserTagId() 
    {
        return numUserTagId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("enterpriseId", getEnterpriseId())
            .append("numUserTagId", getNumUserTagId())
            .toString();
    }
}
