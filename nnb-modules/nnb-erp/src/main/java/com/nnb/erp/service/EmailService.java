package com.nnb.erp.service;

import java.io.ByteArrayOutputStream;
import java.util.List;

public interface EmailService {

    void sendEmail(String recEmail, String subject, String text);

    /**
     * 发邮件抄送
     * @param recEmail
     * @param subject
     * @param text
     * @param copyTo
     */
    void sendEmailAndCopyTo(String recEmail, String subject, String text, List<String> copyTo);

    /**
     *
     * @param recEmail 发送人
     * @param subject 主题
     * @param text 内容
     * @param fileName 文件名
     * @param outputStream 文件流
     * @param type 文件种类
     */
    void sendEmailWithFile(ByteArrayOutputStream outputStream, String recEmail, String subject, String text, String fileName, String type, List<String> copyTo);

}
