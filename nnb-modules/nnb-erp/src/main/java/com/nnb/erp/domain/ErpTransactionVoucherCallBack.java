package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 大额预存回访记录对象 erp_transaction_voucher_call_back
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@ApiModel(value="ErpTransactionVoucherCallBack",description="大额预存回访记录对象")
public class ErpTransactionVoucherCallBack extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** erp_order_pay_record.id */
    @Excel(name = "erp_order_pay_record.id")
    @ApiModelProperty("erp_order_pay_record.id")
    private Long payRecordId;

    /** erp_transaction_voucher.id */
    @Excel(name = "erp_transaction_voucher.id")
    @ApiModelProperty("erp_transaction_voucher.id")
    private Long voucher;

    /** 回访状态1已回访2未回访 */
    @Excel(name = "回访状态1已回访2未回访")
    @ApiModelProperty("回访状态1已回访2未回访")
    private Integer callBackStatus;

    /** 回访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "回访时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("回访时间")
    private Date callBackDate;

    /** 回访结果1结果一致2结果异常3无人接听 */
    @Excel(name = "回访结果1结果一致2结果异常3无人接听")
    @ApiModelProperty("回访结果1结果一致2结果异常3无人接听")
    private Integer callBackResult;

    /** 回访备注 */
    @Excel(name = "回访备注")
    @ApiModelProperty("回访备注")
    private String callBackMemo;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    private String createUserName;

    private String callBackStatusStr;

    private String callBackResultStr;

    //类型1电话录音2回访记录
    private Long type;

    private String sound;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("payRecordId", getPayRecordId())
            .append("voucher", getVoucher())
            .append("callBackStatus", getCallBackStatus())
            .append("callBackDate", getCallBackDate())
            .append("callBackResult", getCallBackResult())
            .append("callBackMemo", getCallBackMemo())
            .append("createUser", getCreateUser())
            .toString();
    }
}
