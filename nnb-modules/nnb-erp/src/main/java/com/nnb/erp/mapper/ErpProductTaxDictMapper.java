package com.nnb.erp.mapper;

import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpProductTaxDict;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 纳税类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface ErpProductTaxDictMapper 
{
    /**
     * 查询纳税类型
     * 
     * @param numTaxId 纳税类型主键
     * @return 纳税类型
     */
    public ErpProductTaxDict selectErpProductTaxDictByNumTaxId(Long numTaxId);

    /**
     * 查询纳税类型列表
     * 
     * @param erpProductTaxDict 纳税类型
     * @return 纳税类型集合
     */
    public List<ErpProductTaxDict> selectErpProductTaxDictList(ErpProductTaxDict erpProductTaxDict);

    /**
     * 新增纳税类型
     * 
     * @param erpProductTaxDict 纳税类型
     * @return 结果
     */
    public int insertErpProductTaxDict(ErpProductTaxDict erpProductTaxDict);

    /**
     * 修改纳税类型
     * 
     * @param erpProductTaxDict 纳税类型
     * @return 结果
     */
    public int updateErpProductTaxDict(ErpProductTaxDict erpProductTaxDict);

    /**
     * 删除纳税类型
     * 
     * @param numTaxId 纳税类型主键
     * @return 结果
     */
    public int deleteErpProductTaxDictByNumTaxId(Long numTaxId);

    /**
     * 批量删除纳税类型
     * 
     * @param numTaxIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpProductTaxDictByNumTaxIds(Long[] numTaxIds);

    List<Map<String,Object>> selectTaxByIds(@Param("taxIds") String taxIds);
}
