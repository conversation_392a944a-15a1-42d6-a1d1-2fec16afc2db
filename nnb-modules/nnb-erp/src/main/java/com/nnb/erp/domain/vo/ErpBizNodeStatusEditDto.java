package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpEnterpriseIncrementBank;
import com.nnb.erp.domain.ErpEnterpriseIncrementFile;
import com.nnb.erp.domain.ErpBizNodeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 修改状态，节点流转dto
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ErpBizNodeStatusEditDto",description = "修改状态，节点流转dto")
public class ErpBizNodeStatusEditDto extends ErpBizNodeStatus {
    private static final long serialVersionUID = -3254144255808848916L;


    /** 转增值存档信息 */
    @ApiModelProperty("转增值存档信息")
    private List<ErpEnterpriseIncrementFile> incrementFiles;

    /** 增值服务单开户银行 */
    @ApiModelProperty("增值服务单开户银行")
    private List<ErpEnterpriseIncrementBank> incrementBanks;

    /** 业务对接人id */
    @ApiModelProperty("业务对接人id")
    private Long numFlowUserId;

    @ApiModelProperty("流程id")
    private Long flowId;

    @ApiModelProperty("状态类型: 1普通，2跳BD，3跳售后，4BD转回，5售后转回")
    private Long numStatusType;

    /** 搁置类型：1长期，2定期 */
    @Excel(name = "搁置类型：1长期，2定期")
    @ApiModelProperty("搁置类型：1长期，2定期")
    private Long numShelveType;

    /** 下次跟进时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("下次跟进时间")
    private Date datLastFollowTime;

    /** 是否有签字流程 */
    @ApiModelProperty("是否有签字流程")
    private Long numIsSign;

    /** 下执照时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("下执照时间")
    private Date datLicenseTime;

    /** 社会统一信用代码 */
    @ApiModelProperty("社会统一信用代码")
    private String vcSocialUnifiedCreditCode;

    /** 注册地址 */
    @ApiModelProperty("注册地址")
    private String vcRegisterAddress;

    /** 记账标签 */
    @ApiModelProperty("记账标签")
    private Long numBookkeepTag;

    /** 预计记账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预计记账时间")
    private Date datEstimateBookkeepTime;

    /** 是否流失 */
    @ApiModelProperty("是否流失")
    private Long numIsDrain;

    /** 流失原因 */
    @ApiModelProperty("流失原因")
    private String vcDrainReason;

    /** 分配理由 */
    @ApiModelProperty("分配理由")
    private String vcDistributionReason;

    /** 预计开户时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预计开户时间")
    private Date datEstimateAccountTime;

    /** 领取营业执照时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("领取营业执照时间")
    private Date datReceiveLicenseTime;

    /** 银行开户时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("银行开户时间")
    private Date datCrtAccountTime;

    /** 税号 */
    @ApiModelProperty("税号")
    private String vcDutyParagraph;

    /** 税务报道日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("税务报道日期")
    private Date datTaxReportDate;

    /** 税控发行日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("税控发行日期")
    private Date datTaxControlIssueDate;

    /** 税控托管开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("税控托管开始时间")
    private Date datTaxControlCustodyTime;

    /** 代办类型:1公司代办；2企业代办 */
    @ApiModelProperty("代办类型:1公司代办；2企业代办")
    private Long numAgencyType;

    /** 沟通角色：1客户方，2销售方，3业务方，4BD */
    @ApiModelProperty("沟通角色：1客户方，2销售方，3业务方，4BD")
    private Long numConnectRole;

    /** 记账开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("记账开始时间")
    private Date datBeginBookkeepTime;

    /** 记账截至时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("记账截至时间")
    private Date datEndBookkeepTime;

    /** 纳税类型变更时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("纳税类型变更时间")
    private Date datTaxTypeChangeTime;

    /** 折算周期变更 */
    @ApiModelProperty("折算周期变更")
    private String vcConversionCycleChange;

    /** 预约开户日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预约开户日期")
    private Date datAppointmentAccountTime;

    /** 转会计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("转会计时间")
    private Date datTransferAccountingTime;

    /** 备注 */
    @ApiModelProperty("备注")
    private String vcRemarks;



    /** BD所属人id */
    @ApiModelProperty("BD所属人id")
    private Long numBdUserId;

    /** 售后所属人Id */
    @ApiModelProperty("售后所属人Id")
    private Long numAfterSaleUserId;

    /** 所属会计Id */
    @ApiModelProperty("所属会计Id")
    private Long numAccountingUserId;

    /** 增值所属人Id */
    @ApiModelProperty("增值所属人Id")
    private Long numIncrementUserId;


}
