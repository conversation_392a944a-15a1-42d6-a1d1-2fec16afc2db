package com.nnb.erp.converter.inventory.util;

import cn.hutool.core.util.StrUtil;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-12-27
 * @Version: 1.0
 */
public class IaUtils {

    /**
     * 获取最大编码
     * @param code
     * @return
     */
    public static String getMaxCode(String code) {
        if (StrUtil.isEmpty(code)){
            return "0001";
        }
        int maxCode = Integer.parseInt(code);
        //转成string
        maxCode = maxCode + 1;
        int length = String.valueOf(maxCode).length();
        if (length == 1) {
            return "000" + maxCode;
        } else if (length == 2) {
            return "00" + maxCode;
        } else if (length == 3){
            return "0" + maxCode;
        }else {
            return String.valueOf(maxCode);
        }
    }
}
