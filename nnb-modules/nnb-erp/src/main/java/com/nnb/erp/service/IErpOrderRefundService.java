package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpOrderRefund;

/**
 * 订单退款Service接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface IErpOrderRefundService
{
    /**
     * 查询订单退款
     *
     * @param id 订单退款主键
     * @return 订单退款
     */
    public ErpOrderRefund selectErpOrderRefundById(Long id);

    /**
     * 查询订单退款列表
     *
     * @param erpOrderRefund 订单退款
     * @return 订单退款集合
     */
    public List<ErpOrderRefund> selectErpOrderRefundList(ErpOrderRefund erpOrderRefund);

    /**
     * 新增订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    public int insertErpOrderRefund(ErpOrderRefund erpOrderRefund);

    /**
     * 修改订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    public int updateErpOrderRefund(ErpOrderRefund erpOrderRefund);

    /**
     * 批量删除订单退款
     *
     * @param ids 需要删除的订单退款主键集合
     * @return 结果
     */
    public int deleteErpOrderRefundByIds(List<Long> ids);

    /**
     * 删除订单退款信息
     *
     * @param id 订单退款主键
     * @return 结果
     */
    public int deleteErpOrderRefundById(Long id);
}
