package com.nnb.erp.domain.reporting;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-01-26
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ErpOrderPayRecordExcelVo implements Serializable {

    @Excel(name = "billNo")
    private String billNo;

    @Excel(name = "线索id")
    private Long clueId;

    @Excel(name = "客户id")
    private Long clientId;

    @Excel(name = "企业名称")
    private String vcCompanyName;

    @Excel(name = "客户名称")
    private String clientName;

    @Excel(name = "订单号")
    private String orderNumber;

    @Excel(name = "金额")
    private BigDecimal fee;

    @Excel(name = "是否报单")
    private String reportedOrNotStr;

    @Excel(name = "报单审核状态")
    private String reportReviewStatusStr;

    @Excel(name = "收款人")
    private String userName;

    @Excel(name = "收款部门")
    private String deptName;

    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Excel(name = "收款方式")
    private String paymentTypeStr;

    @Excel(name = "收款公司主体")
    private String payCompanyStr;

    @Excel(name = "开户行")
    private String openingBank;

    @Excel(name = "账号")
    private String bankAccount;

    @Excel(name = "交易流水号")
    private String tradeId;

    @Excel(name = "单据编号")
    private String documentNumber;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "收款类目")
    private String collectionCategoryStr;


}
