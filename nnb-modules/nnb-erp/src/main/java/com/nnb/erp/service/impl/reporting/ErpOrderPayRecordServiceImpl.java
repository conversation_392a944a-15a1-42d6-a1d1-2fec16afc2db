package com.nnb.erp.service.impl.reporting;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.api.model.DingRobotDTO;
import com.common.api.service.DingService;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.DateFormatConstants;
import com.nnb.erp.constant.ErpExamineApproveConstants;
import com.nnb.erp.constant.ReportConstants;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dto.ErpExamineApproveDTO;
import com.nnb.erp.domain.reporting.BdClueDto;
import com.nnb.erp.domain.reporting.ErpOrderPayRecord;
import com.nnb.erp.domain.reporting.PayCompany;
import com.nnb.erp.domain.reporting.ReportDto;
import com.nnb.erp.domain.vo.PayRecordBigCallBackVo;
import com.nnb.erp.enums.CollectionCategoryEnum;
import com.nnb.erp.enums.ErpOrderPayRecordEnum;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.reporting.ErpOrderPayRecordMapper;
import com.nnb.erp.service.IErpExamineApproveService;
import com.nnb.erp.service.IErpTransactionVoucherFollowService;
import com.nnb.erp.service.reporting.IErpOrderPayRecordService;
import com.nnb.erp.util.MybatisBatchUtils;
import com.nnb.erp.util.ThreadPoolUtil;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.BdClueContacts;
import com.nnb.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description: 交易流水记录Service业务层处理
 * @Date: 2024-01-24
 * @Version: 1.0
 */
@Service
@Slf4j
public class ErpOrderPayRecordServiceImpl implements IErpOrderPayRecordService {

    @Value("${report.deptIds}")
    private String reportDeptIds;

    @Resource
    private DingService dingService;

    @Autowired
    private ErpOrderPayRecordMapper erpOrderPayRecordMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private MybatisBatchUtils mybatisBatchUtils;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IErpExamineApproveService iErpExamineApproveService;

    @Autowired
    private ErpClientMapper erpClientMapper;

    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;

    @Autowired
    private ErpPromotionalActivitiesMapper erpPromotionalActivitiesMapper;

    @Autowired
    private ErpOrdersMapper erpOrdersMapper;

    @Autowired
    private ErpTransactionVoucherFollowMapper erpTransactionVoucherFollowMapper;

    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;

    @Autowired
    private IErpTransactionVoucherFollowService transactionVoucherFollowService;

    @Autowired
    private ErpExamineApproveCommonPaymentMapper erpExamineApproveCommonPaymentMapper;

    /**
     * 查询交易流水记录
     *
     * @param id 交易流水记录主键
     * @return 交易流水记录
     */
    @Override
    public ErpOrderPayRecord selectErpOrderPayRecordById(Long id) {
        ErpOrderPayRecord erpOrderPayRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(id);
        List<PayCompany> allPayCompany = erpOrderPayRecordMapper.getAllPayCompany();
        allPayCompany.stream()
                .filter(en -> en.getId().equals(erpOrderPayRecord.getPayCompanyId()))
                .findAny()
                .ifPresent(en ->
                        //处理状态
                        processingStatus(erpOrderPayRecord, en)
                );
        //收款人信息
        SysUser sysUserPay = erpOrderPayRecordMapper.selectSysUser(erpOrderPayRecord.getUserId());
        if (ObjectUtil.isNotEmpty(sysUserPay)) {
            erpOrderPayRecord.setUserName(sysUserPay.getNickName());
            //部门
            if (ObjectUtil.isNotEmpty(sysUserPay.getDeptId())) {
                SysDept sysDeptPay = erpOrderPayRecordMapper.selectSysDept(sysUserPay.getDeptId());
                erpOrderPayRecord.setDeptId(sysDeptPay.getDeptId());
                erpOrderPayRecord.setDeptName(sysDeptPay.getDeptName());
            }
        }
        //先赋值
        erpOrderPayRecord.setHasCollectionCategory(ReportConstants.HAS_NO_COLLECTION_CATEGORY);
        //获取登录人信息
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isNotEmpty(loginUser.getSysUser())) {
            SysUser sysUser = loginUser.getSysUser();
            //redis数据可能不准，从数据库实时查部门信息
            SysDept dept = erpOrderPayRecordMapper.selectSysDeptByUserId(sysUser.getUserId());
            boolean match = Arrays.stream(dept.getAncestors().split(","))
                    .map(Long::valueOf)
                    .anyMatch(ReportConstants.deptIdList::contains);
            if (match || ReportConstants.deptIdList.contains(dept.getDeptId())){
                erpOrderPayRecord.setHasCollectionCategory(ReportConstants.HAS_COLLECTION_CATEGORY);
            }
        } else {
            log.warn("跟进Id获取交易流水记录未获取到登录人。");
        }
        return erpOrderPayRecord;
    }

    /**
     * 查询交易流水记录列表
     *
     * @param erpOrderPayRecord 交易流水记录
     * @return 交易流水记录
     */
    @Override
    public List<ErpOrderPayRecord> selectErpOrderPayRecordList(ErpOrderPayRecord erpOrderPayRecord) {
        return erpOrderPayRecordMapper.selectErpOrderPayRecordList(erpOrderPayRecord);
    }

    /**
     * 新增交易流水记录
     *
     * @param erpOrderPayRecord 交易流水记录
     * @return 结果
     */
    @Override
    public int insertErpOrderPayRecord(ErpOrderPayRecord erpOrderPayRecord) {
        erpOrderPayRecord
                .setPayStatus(ErpOrderPayRecordEnum.PAID.getCode())
                .setCreatedTime(new Date());
        setMsgToEn(erpOrderPayRecord);

        if (ErpOrderPayRecordEnum.CORPORATE_TRANSFER.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> tradeIdList = new ArrayList<>();
            tradeIdList.add(erpOrderPayRecord.getTradeId());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByTradeIdList(tradeIdList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("交易流水号重复!!!", 400);
            }
        }else if (ErpOrderPayRecordEnum.POS_MACHINE.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> tradeIdList = new ArrayList<>();
            tradeIdList.add(erpOrderPayRecord.getTradeId());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByTradeIdList(tradeIdList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("交易流水号重复!!!", 400);
            }
        }else if (ErpOrderPayRecordEnum.CASH.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> documentNumberList = new ArrayList<>();
            documentNumberList.add(erpOrderPayRecord.getDocumentNumber());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByDocumentNumberList(documentNumberList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("单据号重复!!!", 400);
            }
        }else if (ErpOrderPayRecordEnum.SHOP.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> documentNumberList = new ArrayList<>();
            documentNumberList.add(erpOrderPayRecord.getDocumentNumber());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByDocumentNumberList(documentNumberList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("单据号重复!!!", 400);
            }
        }else if (ErpOrderPayRecordEnum.OTHER.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> tradeIdList = new ArrayList<>();
            tradeIdList.add(erpOrderPayRecord.getTradeId());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByTradeIdList(tradeIdList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("交易流水号重复!!!", 400);
            }
        }else if (ErpOrderPayRecordEnum.DEDUCTION.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<String> documentNumberList = new ArrayList<>();
            documentNumberList.add(erpOrderPayRecord.getDocumentNumber());
            List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectErpOrderPayByDocumentNumberList(documentNumberList, erpOrderPayRecord.getPaymentType());
            if (CollUtil.isNotEmpty(list)){
                throw new ServiceException("单据号重复!!!", 400);
            }
        }
        return erpOrderPayRecordMapper.insertErpOrderPayRecord(erpOrderPayRecord);
    }

    /**
     * 修改交易流水记录
     *
     * @param erpOrderPayRecord 交易流水记录
     * @return 结果
     */
    @Override
    public int updateErpOrderPayRecord(ErpOrderPayRecord erpOrderPayRecord) {
        erpOrderPayRecord
                .setUpdatedTime(new Date())
                .setUpdatedUser(tokenService.getLoginUser().getUserid());
        setMsgToEn(erpOrderPayRecord);
        return erpOrderPayRecordMapper.updateErpOrderPayRecord(erpOrderPayRecord);
    }

    private void setMsgToEn(ErpOrderPayRecord erpOrderPayRecord) {
        //收款人 //部门
        if (ObjectUtil.isNotEmpty(erpOrderPayRecord.getUserId())) {
            R<SysUser> sysUserR = remoteUserService.getUserInfoById(erpOrderPayRecord.getUserId(), SecurityConstants.INNER);
            if (ObjectUtil.isNotEmpty(sysUserR.getData())) {
                erpOrderPayRecord.setUserName(sysUserR.getData().getNickName());
                //部门
                if (ObjectUtil.isNotEmpty(sysUserR.getData().getDept())) {
                    erpOrderPayRecord.setDeptId(sysUserR.getData().getDept().getDeptId());
                    erpOrderPayRecord.setDeptName(sysUserR.getData().getDept().getDeptName());
                }
            }
        }
    }

    /**
     * 批量删除交易流水记录
     *
     * @param ids 需要删除的交易流水记录主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderPayRecordByIds(List<Long> ids) {
        return erpOrderPayRecordMapper.deleteErpOrderPayRecordByIds(ids);
    }

    /**
     * 删除交易流水记录信息
     *
     * @param id 交易流水记录主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderPayRecordById(Long id) {
        return erpOrderPayRecordMapper.deleteErpOrderPayRecordById(id);
    }

    @Override
    public List<PayCompany> getPayCompany() {
        return erpOrderPayRecordMapper.getPayCompany();
    }

    @Override
    public List<ErpOrderPayRecord> pageList(ErpOrderPayRecord erpOrderPayRecord) {
        //时间
        if (StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeBegin()) && StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeEnd())) {
            erpOrderPayRecord.setPayTimeBegin(erpOrderPayRecord.getPayTimeBegin() + DateFormatConstants.TIME_BEGIN);
            erpOrderPayRecord.setPayTimeEnd(erpOrderPayRecord.getPayTimeEnd() + DateFormatConstants.TIME_END);
        }
        if (ObjectUtil.isNotEmpty(erpOrderPayRecord.getPaymentType()) && erpOrderPayRecord.getPaymentType() == 1) {
            erpOrderPayRecord.setAllName(erpOrderPayRecord.getClientName());
        }
        List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.pageList(erpOrderPayRecord);
        //放入收款类目
        list.forEach(en ->
                en.setCollectionCategoryStr(CollectionCategoryEnum.getDescByCode(en.getCollectionCategory()))
        );
        List<PayCompany> allPayCompany = erpOrderPayRecordMapper.getAllPayCompany();
        //组装基本信息
        basicAssemblyInformation(erpOrderPayRecord, list, allPayCompany);
        return list;
    }

    private void basicAssemblyInformation(ErpOrderPayRecord erpOrderPayRecord, List<ErpOrderPayRecord> list, List<PayCompany> allPayCompany) {
        if (CollUtil.isNotEmpty(list)) {
            //获取所有的userId
            List<Long> userIdList = list.stream()
                    .map(ErpOrderPayRecord::getUserId)
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toList());
            List<SysUser> userList = new ArrayList<>();
            if (CollUtil.isNotEmpty(userIdList)){
                userList = erpOrderPayRecordMapper.selectByUserIdList(userIdList);
            }
            //获取所有的客户信息
            List<Long> clientIdList = list.stream()
                    .map(ErpOrderPayRecord::getClientId)
                    .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            List<Long> clueIdList = list.stream()
                    .map(ErpOrderPayRecord::getClueId)
                    .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            List<ErpClient> erpClientList = new ArrayList<>();
            if (CollUtil.isNotEmpty(clientIdList)){
                erpClientList = erpClientMapper.getErpClientByIds(clientIdList);
            }
            List<BdClueDto> bdClueList = new ArrayList<>();
            if (CollUtil.isNotEmpty(clueIdList)){
                bdClueList = erpOrderPayRecordMapper.selectBdClueByIds(clueIdList);
            }
            //组装信息
            for (ErpOrderPayRecord orderPayRecord : list) {
                //收款主体
                allPayCompany.stream()
                        .filter(en -> en.getId().equals(orderPayRecord.getPayCompanyId()))
                        .findAny()
                        .ifPresent(en -> processingStatus(orderPayRecord, en));
                //回填收款人信息
                fillInPayeeInformation(userIdList, userList, orderPayRecord);
                //回填客户信息
                backFillingCustomerInformation(erpOrderPayRecord, erpClientList, bdClueList, orderPayRecord);
                orderPayRecord.setAllFee(orderPayRecord.getFee());
                //补充流水数据
                fillTransactionVoucher(orderPayRecord);
            }
        }
    }

    private void fillTransactionVoucher(ErpOrderPayRecord erpOrderPayRecord) {
        List<JSONObject> list = erpTransactionVoucherMapper.selectErpTransactionVoucherByPayRecordId(Long.parseLong(erpOrderPayRecord.getId()));
        List<ErpOrderPayRecord> feeSplit = new ArrayList<>();
        //判断是否可退款
        erpOrderPayRecord.setCanRefund(2);
        erpOrderPayRecord.setHasApprove(2);
        for (int i = 0; i < list.size(); i++) {
            JSONObject voucher = list.get(i);
            ErpOrderPayRecord split = new ErpOrderPayRecord();
            BeanUtils.copyProperties(erpOrderPayRecord, split, "id");

            BigDecimal balance = voucher.getBigDecimal("balance");
            BigDecimal balance_use = voucher.getBigDecimal("balance_use");
            BigDecimal wait_entry = voucher.getBigDecimal("wait_entry");
            BigDecimal wait_deduction = voucher.getBigDecimal("wait_deduction");
            BigDecimal all_fee = voucher.getBigDecimal("all_fee");
//            Long
            split.setId("voucher" + voucher.getString("id"));
            split.setVoucher(voucher.getString("id"));
            split.setBalance(balance);
            split.setBalanceUse(balance_use);
            split.setWaitEntry(wait_entry);
            split.setWaitDeduction(wait_deduction);
            split.setAllFee(all_fee);
            split.setActivitieName(voucher.getString("name"));
            split.setActivitieId(voucher.getInteger("activitie_id"));
            split.setActivitieType(voucher.getInteger("activitieType"));
            split.setCanRefund(2);
            split.setHasApprove(2);
            split.setVoucherType(voucher.getInteger("type"));
            if (voucher.getInteger("type") == 3) {
                split.setActivitieOtherShare(voucher.getInteger("other_share") == 2 ? "不可与其他活动赠送金额一起使用":"");
            }
            if (voucher.getInteger("type") == 1) {
                erpOrderPayRecord.setBalance(balance);
                erpOrderPayRecord.setBalanceUse(balance_use);
                erpOrderPayRecord.setWaitEntry(wait_entry);
                erpOrderPayRecord.setWaitDeduction(wait_deduction);
                erpOrderPayRecord.setVoucher(voucher.getString("id"));
            } else {
                feeSplit.add(split);
            }
        }

        if (erpOrderPayRecord.getReportedOrNot() == 1 && erpOrderPayRecord.getReportReviewStatus() == 1) {
            ErpExamineApproveDTO erpExamineApproveDTO = new ErpExamineApproveDTO();
            erpExamineApproveDTO.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_NO_ORDER_REFUND);
            erpExamineApproveDTO.setApproveStatusList(Arrays.asList(0));

            if (ObjectUtil.isNotEmpty(erpOrderPayRecord.getVoucher()) && erpOrderPayRecord.getBalanceUse().compareTo(new BigDecimal("0")) > 0) {
                List<Long> otherIdList = erpExamineApproveCommonPaymentMapper.selectIdListByVoucherId(erpOrderPayRecord.getVoucher());
                if (ObjectUtils.isNotEmpty(otherIdList) && otherIdList.size() > 0) {
                    erpExamineApproveDTO.setOtherIdList(otherIdList);
                    List<ErpExamineApprove> approveList = iErpExamineApproveService.selectErpExamineApproveList(erpExamineApproveDTO);
                    if (approveList.size() > 0) {
                        erpOrderPayRecord.setHasApprove(1);
                    }
                }
                erpOrderPayRecord.setCanRefund(1);
            }
            if (feeSplit.size() > 0) {
                for (int i = 0; i <feeSplit.size(); i++) {
                    ErpOrderPayRecord split = feeSplit.get(i);
                    if (split.getVoucherType() == 2 && split.getBalanceUse().compareTo(new BigDecimal("0")) > 0) {
                        split.setCanRefund(1);
                        //查询赠金是否使用
                        Integer activitieId = split.getActivitieId();
                        for (int j = 0; j < feeSplit.size(); j++) {
                            ErpOrderPayRecord give = feeSplit.get(j);
                            if (give.getVoucherType() == 3 && give.getActivitieId() == activitieId && give.getBalanceUse().compareTo(give.getAllFee()) < 0) {
                                split.setCanRefund(2);
                            }
                        }
                    }
                    if (ObjectUtils.isNotEmpty(split.getVoucher())) {
                        List<Long> otherIdList = erpExamineApproveCommonPaymentMapper.selectIdListByVoucherId(split.getVoucher());
                        if (ObjectUtils.isNotEmpty(otherIdList) && otherIdList.size() > 0) {
                            erpExamineApproveDTO.setOtherIdList(otherIdList);
                            List<ErpExamineApprove> approveList = iErpExamineApproveService.selectErpExamineApproveList(erpExamineApproveDTO);
                            if (approveList.size() > 0) {
                                split.setHasApprove(1);
                            }
                        }
                    }
                }
            }
        }
        erpOrderPayRecord.setFeeSplit(feeSplit);
    }

    private void backFillingCustomerInformation(ErpOrderPayRecord erpOrderPayRecord, List<ErpClient> erpClientList, List<BdClueDto> bdClueList, ErpOrderPayRecord orderPayRecord) {
        if (StrUtil.isEmpty(orderPayRecord.getClientName())){
            //客户信息 -> 客户
            if (CollUtil.isNotEmpty(erpClientList)){
                if (ObjectUtil.isNotEmpty(orderPayRecord.getClientId())
                        && ObjectUtil.isEmpty(erpOrderPayRecord.getClueId())){
                    ErpClient erpClient = erpClientList.stream()
                            .filter(en -> en.getId().equals(orderPayRecord.getClientId()))
                            .findAny().orElse(null);
                    assert erpClient != null;
                    if (ObjectUtil.isNotEmpty(erpClient)){
                        orderPayRecord.setClientName(erpClient.getContactName());
                    }
                }
            }
            //客户信息 -> 线索
            if (CollUtil.isNotEmpty(bdClueList)){
                if (ObjectUtil.isNotEmpty(orderPayRecord.getClueId())
                        && ObjectUtil.isEmpty(orderPayRecord.getClientId())){
                    BdClueDto bdClueDto = bdClueList.stream()
                            .filter(en -> en.getId().equals(orderPayRecord.getClueId()))
                            .findAny().orElse(null);
                    assert bdClueDto != null;
                    if (ObjectUtil.isNotEmpty(bdClueDto)){
                        if (StrUtil.isEmpty(bdClueDto.getName())){
                            orderPayRecord.setClientName(bdClueDto.getPhone());
                        }else{
                            orderPayRecord.setClientName(bdClueDto.getName());
                        }
                    }
                }
            }
            //如果都有拼在一起
            if (CollUtil.isNotEmpty(bdClueList) && CollUtil.isNotEmpty(erpClientList)){
                if (ObjectUtil.isNotEmpty(orderPayRecord.getClueId())
                        && ObjectUtil.isNotEmpty(orderPayRecord.getClientId())){
                    Optional<ErpClient> optional = erpClientList.stream()
                            .filter(en -> en.getId().equals(orderPayRecord.getClientId()))
                            .findAny();
                    if (optional.isPresent()){
                        orderPayRecord.setClientName(optional.get().getContactName());
                    } else {
                        bdClueList.stream()
                                .filter(en -> en.getId().equals(orderPayRecord.getClueId()))
                                .findAny()
                                .ifPresent(en -> {
                                    orderPayRecord.setClientName(en.getName());
                                });
                    }
                }
            }
        }
    }

    private void fillInPayeeInformation(List<Long> userIdList, List<SysUser> userList, ErpOrderPayRecord orderPayRecord) {
        if (CollUtil.isNotEmpty(userIdList)){
            SysUser sysUser = userList.stream()
                    .filter(en -> en.getUserId().equals(orderPayRecord.getUserId()))
                    .findAny().orElse(null);
            assert sysUser != null;
            if (ObjectUtil.isNotEmpty(sysUser)) {
                orderPayRecord.setUserName(sysUser.getNickName());
                if (ObjectUtil.isNotEmpty(sysUser.getDept())) {
                    orderPayRecord.setDeptId(sysUser.getDept().getDeptId());
                    orderPayRecord.setDeptName(sysUser.getDept().getDeptName());
                }
            }
        }
    }

    @Override
    public Map<String, Object> importTemplate(MultipartFile file) throws IOException {
        Map<String, Object> hashMap = new HashMap<>();
        //读取excel
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());

        List<Map<String, Object>> mapList = reader.read(0, 1, Integer.MAX_VALUE);
        if (ObjectUtil.isEmpty(mapList)) {
            throw new ServiceException("文件为空", 400);
        }
        List<PayCompany> payCompanyList = erpOrderPayRecordMapper.getPayCompany();
        StringJoiner joiner = new StringJoiner("，");
        List<ErpOrderPayRecord> list = new ArrayList<>();
        List<List<Object>> read = reader.read(1, 1);
        if ("对公转账".equals(read.get(0).get(0)) || "POS机".equals(read.get(0).get(0)) || "其他".equals(read.get(0).get(0))) {
            //检验对公转账
            verifyCorporateTransfer(reader, mapList);
            //组装对公转账参数
            assemblyOfCorporateTransfer(mapList, payCompanyList, joiner, list);
        } else {
            //检验现金
            verifyCash(reader, mapList);
            //组装现金参数
            assemblyCashParameters(mapList, payCompanyList, joiner, list);
        }
        //检验单据是否重复
        verifyIfTheDocumentIsDuplicated(joiner, list);

        if (StrUtil.isNotEmpty(joiner.toString())) {
            throw new ServiceException(joiner.toString(), 400);
        }
        mybatisBatchUtils.batchUpdateOrInsert(list, ErpOrderPayRecordMapper.class,
                (orderPayRecord, orderPayRecordMapper) -> orderPayRecordMapper.insertErpOrderPayRecord(orderPayRecord));
        hashMap.put("msg", "共导入" + list.size() + "条数据");
        return hashMap;
    }

    private void verifyIfTheDocumentIsDuplicated(StringJoiner joiner, List<ErpOrderPayRecord> list) {
        List<String> tradeIdList = new ArrayList<>();
        List<String> documentNumberList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ErpOrderPayRecord record = list.get(i);
            if (ObjectUtil.isNotEmpty(record.getTradeId())) {
                if (tradeIdList.contains(record.getTradeId())) {
                    throw new ServiceException("文件中交易流水号有重复", 400);
                }
                tradeIdList.add(record.getTradeId());
            }
            if (ObjectUtil.isNotEmpty(record.getDocumentNumber())) {
                if (tradeIdList.contains(record.getDocumentNumber())) {
                    throw new ServiceException("文件中单据编号有重复", 400);
                }
                tradeIdList.add(record.getDocumentNumber());
            }
        }

        if (CollUtil.isNotEmpty(tradeIdList)){
            List<ErpOrderPayRecord> corporateList = erpOrderPayRecordMapper.selectErpOrderPayByTradeIdList(tradeIdList, list.get(0).getPaymentType());
            if (CollUtil.isNotEmpty(corporateList)){
                String msg = corporateList.stream()
                        .map(ErpOrderPayRecord::getTradeId)
                        .collect(Collectors.joining("，"));
                joiner.add("重复流水号：" + msg);
            }
        }
        if (CollUtil.isNotEmpty(documentNumberList)){
            List<ErpOrderPayRecord> cashList = erpOrderPayRecordMapper.selectErpOrderPayByDocumentNumberList(documentNumberList, list.get(0).getPaymentType());
            if (CollUtil.isNotEmpty(cashList)){
                String msg = cashList.stream()
                        .map(ErpOrderPayRecord::getDocumentNumber)
                        .collect(Collectors.joining("，"));
                joiner.add("重复单据号：" + msg);
            }
        }
    }

    /**
     * 组装现金参数
     *
     * @param mapList
     * @param payCompanyList
     * @param joiner
     * @param list
     * @return
     */
    private void assemblyCashParameters(List<Map<String, Object>> mapList, List<PayCompany> payCompanyList,
                                        StringJoiner joiner, List<ErpOrderPayRecord> list) {
        //查询所有人和部门
        List<String> userNameList = mapList.stream().map(m -> MapUtil.getStr(m, "收款人")).collect(Collectors.toList());
        List<SysUser> userList = erpOrderPayRecordMapper.selectByUserNameList(userNameList);
        for (Map<String, Object> map : mapList) {
            ErpOrderPayRecord erpOrderPayRecord = new ErpOrderPayRecord()
                    .setPayStatus(ErpOrderPayRecordEnum.PAID.getCode())
                    .setCreatedTime(new Date());
            int i = mapList.indexOf(map) + 1;

            //校验公告部分
            if (MapUtil.getStr(map, "收款方式").equals("现金")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i, erpOrderPayRecord, ErpOrderPayRecordEnum.CASH.getCode());
            } else if (MapUtil.getStr(map, "收款方式").equals("店铺")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i, erpOrderPayRecord, ErpOrderPayRecordEnum.SHOP.getCode());
            } else if (MapUtil.getStr(map, "收款方式").equals("抵扣款")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i, erpOrderPayRecord, ErpOrderPayRecordEnum.DEDUCTION.getCode());
            }

            //单据编号
            if (StrUtil.isNotEmpty(MapUtil.getStr(map, "单据编号"))) {
                erpOrderPayRecord.setDocumentNumber(MapUtil.getStr(map, "单据编号"));
            } else {
                joiner.add("第" + i + "行单据编号不得为空！！！");
            }
            //收款人，收款部门
            if (StrUtil.isNotEmpty(MapUtil.getStr(map, "收款人"))) {
                SysUser sysUser = userList.stream()
                        .filter(en -> en.getUserName().equals(MapUtil.getStr(map, "收款人")))
                        .findAny().orElse(null);
                if (ObjectUtil.isNotEmpty(sysUser)) {
                    erpOrderPayRecord.setUserId(sysUser.getUserId());
                    erpOrderPayRecord.setUserName(sysUser.getNickName());
                    if (ObjectUtil.isNotEmpty(sysUser.getDept())){
                        erpOrderPayRecord.setDeptId(sysUser.getDept().getDeptId());
                        erpOrderPayRecord.setDeptName(sysUser.getDept().getDeptName());
                    }else {
                        joiner.add("第" + i + "行未匹配到收款部门！！！");
                    }
                } else {
                    joiner.add("第" + i + "行未匹配到收款人！！！");
                }
            } else {
                joiner.add("第" + i + "行收款人不得为空！！！");
            }
            list.add(erpOrderPayRecord);
        }
    }

    /**
     * 检验现金
     *
     * @param reader
     * @param mapList
     */
    private void verifyCash(ExcelReader reader, List<Map<String, Object>> mapList) {
        //校验表头
        List<List<Object>> readHeader = reader.read(0, 0);
        if (!"收款方式".equals(readHeader.get(0).get(0)) || !"收款公司主体".equals(readHeader.get(0).get(1))
                || !"支付时间".equals(readHeader.get(0).get(2)) || !"单据编号".equals(readHeader.get(0).get(3))
                || !"客户名称".equals(readHeader.get(0).get(4)) || !"金额".equals(readHeader.get(0).get(5))
                || !"收款人".equals(readHeader.get(0).get(6))
        ) {
            throw new ServiceException("现金模板表头不正确，请检查模板！", 400);
        }


        //校验是否全是对公转账
        boolean match = mapList.stream().anyMatch(m -> MapUtil.getStr(m, "收款方式").equals("现金")
                || MapUtil.getStr(m, "收款方式").equals("店铺") || MapUtil.getStr(m, "收款方式").equals("抵扣款"));
//        boolean match = mapList.stream().map(m -> MapUtil.getStr(m, "收款方式")).allMatch(""::equals);
        if (!match) {
            throw new ServiceException("表格内容错误，请检查收款方式列", 400);
        }
    }

    /**
     * 组装对公转账参数
     *
     * @param hashMap
     * @param mapList
     * @param payCompanyList
     * @param joiner
     * @param list
     * @return
     */
    private void assemblyOfCorporateTransfer(List<Map<String, Object>> mapList, List<PayCompany> payCompanyList,
                                             StringJoiner joiner, List<ErpOrderPayRecord> list) {
        for (Map<String, Object> map : mapList) {
            ErpOrderPayRecord erpOrderPayRecord = new ErpOrderPayRecord()
                    .setPayStatus(ErpOrderPayRecordEnum.PAID.getCode())
                    .setCreatedTime(new Date());
            int i = mapList.indexOf(map) + 1;
            //校验公告部分

            if (MapUtil.getStr(map, "收款方式").equals("对公转账")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i,
                        erpOrderPayRecord, ErpOrderPayRecordEnum.CORPORATE_TRANSFER.getCode());
            } else if (MapUtil.getStr(map, "收款方式").equals("POS机")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i,
                        erpOrderPayRecord, ErpOrderPayRecordEnum.POS_MACHINE.getCode());
            } else if (MapUtil.getStr(map, "收款方式").equals("其他")) {
                inspectionOfPublicParts(payCompanyList, joiner, map, i,
                        erpOrderPayRecord, ErpOrderPayRecordEnum.OTHER.getCode());
            }

            //开户行
            if (StrUtil.isNotEmpty(MapUtil.getStr(map, "开户行"))) {
                erpOrderPayRecord.setOpeningBank(MapUtil.getStr(map, "开户行"));
            } else {
                joiner.add("第" + i + "行开户行不得为空！！！");
            }
            //账号
            if (StrUtil.isNotEmpty(MapUtil.getStr(map, "账号"))) {
                erpOrderPayRecord.setBankAccount(MapUtil.getStr(map, "账号"));
            } else {
                joiner.add("第" + i + "行账号不得为空！！！");
            }
            //交易流水号
            if (StrUtil.isNotEmpty(MapUtil.getStr(map, "交易流水号"))) {
                erpOrderPayRecord.setTradeId(MapUtil.getStr(map, "交易流水号"));
            } else {
                joiner.add("第" + i + "行交易流水号不得为空！！！");
            }
            list.add(erpOrderPayRecord);
        }
    }

    /**
     * 检验对公转账
     *
     * @param reader
     * @param mapList
     */
    private void verifyCorporateTransfer(ExcelReader reader, List<Map<String, Object>> mapList) {
        //校验表头
        List<List<Object>> readHeader = reader.read(0, 0);
        if (!"收款方式".equals(readHeader.get(0).get(0)) || !"收款公司主体".equals(readHeader.get(0).get(1))
                || !"支付时间".equals(readHeader.get(0).get(2)) || !"开户行".equals(readHeader.get(0).get(3))
                || !"账号".equals(readHeader.get(0).get(4)) || !"交易流水号".equals(readHeader.get(0).get(5))
                || !"客户名称".equals(readHeader.get(0).get(6)) || !"金额".equals(readHeader.get(0).get(7))
        ) {
            throw new ServiceException("模板表头不正确，请检查模板！", 400);
        }
        //校验是否全是对公转账
//        boolean match = mapList.stream().map(m -> MapUtil.getStr(m, "收款方式")).anyMatch("对公转账"::equals);
        boolean match = mapList.stream().anyMatch(m -> MapUtil.getStr(m, "收款方式").equals("对公转账")
                || MapUtil.getStr(m, "收款方式").equals("POS机")
                || MapUtil.getStr(m, "收款方式").equals("其他"));
        if (!match) {
            throw new ServiceException("表格内容错误，请检查收款方式列", 400);
        }
    }

    private void inspectionOfPublicParts(List<PayCompany> payCompanyList, StringJoiner
            joiner, Map<String, Object> map,
                                         int i, ErpOrderPayRecord erpOrderPayRecord, Integer paymentType) {
        //收款方式
        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "收款方式"))) {
            erpOrderPayRecord.setPaymentType(paymentType);
        } else {
            joiner.add("第" + i + "行收款方式不得为空！！！");
        }
        //收款公司主体
        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "收款公司主体"))) {
            PayCompany payCompany = payCompanyList.stream()
                    .filter(en -> en.getName().equals(MapUtil.getStr(map, "收款公司主体")))
                    .findAny().orElse(null);
            if (ObjectUtil.isEmpty(payCompany)) {
                joiner.add("第" + i + "行收款公司主体未匹配！！！");
            } else {
                erpOrderPayRecord.setPayCompanyId(payCompany.getId());
            }
        } else {
            joiner.add("第" + i + "行收款公司主体不得为空！！！");
        }
        //支付时间
        if (ObjectUtil.isNotEmpty(MapUtil.getDate(map, "支付时间"))) {
            erpOrderPayRecord.setPayTime(MapUtil.getDate(map, "支付时间"));
        } else {
            joiner.add("第" + i + "行支付时间不得为空！！！");
        }
        //客户名称
        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "客户名称"))) {
            erpOrderPayRecord.setClientName(MapUtil.getStr(map, "客户名称"));
        } else {
            joiner.add("第" + i + "行客户名称不得为空！！！");
        }
//        //金额
//        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "金额"))) {
//            String amount = MapUtil.getStr(map, "金额");
//            BigDecimal bigDecimal = MapUtil.get(map, "金额", BigDecimal.class);
//            if (!amount.contains(".")) {
//                joiner.add("第" + i + "行金额不是小数！！！");
//            } else {
//                if (amount.split("\\.")[1].length() != 2) {
//                    joiner.add("第" + i + "行金额不是2位小数！！！");
//                } else {
//                    erpOrderPayRecord.setFee(bigDecimal);
//                }
//            }
//        } else {
//            joiner.add("第" + i + "行金额不得为空！！！");
//        }
        //金额
        if (StrUtil.isNotEmpty(MapUtil.getStr(map, "金额"))) {
            BigDecimal bigDecimal = MapUtil.get(map, "金额", BigDecimal.class).setScale(2, RoundingMode.HALF_UP);
            if (bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                joiner.add("第" + i + "行金额不得小于等于0！！！");
            } else {
                erpOrderPayRecord.setFee(bigDecimal);
            }
        } else {
            joiner.add("第" + i + "行金额不得为空！！！");
        }
    }

    private void processingStatus(ErpOrderPayRecord erpOrderPayRecord, PayCompany payCompany) {
        erpOrderPayRecord
                .setPayCompanyStr(payCompany.getName())
                .setPayStatusStr(ErpOrderPayRecordEnum.getPayStatusStr(erpOrderPayRecord.getPayStatus()))
                .setOrderTypeStr(ErpOrderPayRecordEnum.getOrderTypeStr(erpOrderPayRecord.getOrderType()))
                .setReportedOrNotStr(ErpOrderPayRecordEnum.getReportedOrNotStr(erpOrderPayRecord.getReportedOrNot()))
                .setReportReviewStatusStr(ErpOrderPayRecordEnum.getReportReviewStatusStr(erpOrderPayRecord.getReportReviewStatus()))
                .setPaymentTypeStr(ErpOrderPayRecordEnum.getPaymentTypeStr(erpOrderPayRecord.getPaymentType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int report(ReportDto reportDto) {
        if (ObjectUtil.isEmpty(reportDto.getId())) {
            throw new ServiceException("参数错误");
        }

        ErpOrderPayRecord report = erpOrderPayRecordMapper.selectErpOrderPayRecordById(reportDto.getId());
        if (ObjectUtil.isEmpty(report) ||
                (report.getPaymentType() != 0 && ObjectUtil.isEmpty(reportDto.getVoucher()))) {
            throw new ServiceException("参数错误");
        }

        if (report.getReportReviewStatus() == 0 || report.getReportReviewStatus() == 1) {
            throw new ServiceException("该数据已报单");
        }

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if (report.getPaymentType() != 0){
            //不等于二维码时，更新报单状态为1，二维码在下面单独处理
            report.setSubmittedReport(ReportConstants.IS_REPORT);
            report.setReportUserId(sysUser.getUserId());
            report.setReportRemark(reportDto.getReportRemark());
            report.setCollectionCategory(reportDto.getCollectionCategory());
        }
        report.setReportReviewStatus(0);
        report.setVoucher(reportDto.getVoucher());
        int result = 0;
        //二维码付款不走审批
        if(report.getPaymentType() == 0) {
            report.setReportReviewStatus(1);
            report.setReportedOrNot(1);
            erpOrderPayRecordMapper.updateApproveStatusByBillNo(report.getBillNo(), 1, 1, new Date());
        }
        //二维码付款不走审批
        if (report.getPaymentType() != 0) {
            ErpExamineApprove erpExamineApprove = new ErpExamineApprove();
            erpExamineApprove.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_ORDER_REPORT);
            erpExamineApprove.setOtherId(report.getId());
            result = iErpExamineApproveService.insertErpExamineApprove(erpExamineApprove);
        }
        report.setPrestore(2L);

        ErpTransactionVoucher erpTransactionVoucher = new ErpTransactionVoucher();

        //收成收款凭证
        BigDecimal fee = report.getFee();
        JSONArray activitieArr = reportDto.getActivitieArr();
        if (ObjectUtil.isNotEmpty(activitieArr) && activitieArr.size() > 0) {
            for (int i = 0; i < activitieArr.size(); i++) {
                JSONObject activitieObj = activitieArr.getJSONObject(i);
                Integer activitiesId = activitieObj.getInteger("activities");
                Integer count = activitieObj.getInteger("count");
                Integer countUse = 0;
                if (ObjectUtil.isEmpty(activitiesId) || ObjectUtil.isEmpty(count)) {
                    throw new ServiceException("活动错误");
                }
                ErpPromotionalActivities activities = erpPromotionalActivitiesMapper.selectErpPromotionalActivitiesById(activitiesId);
                if (ObjectUtil.isEmpty(activities)) {
                    throw new ServiceException("活动错误");
                }
                if (activities.getPrestore().intValue() == 1) {
                    report.setPrestore(1L);
                }
                if (ObjectUtil.isNotEmpty(report.getClientId())) {
                    int userCount = erpTransactionVoucherMapper.selectCountByActivitiesIdAndClientId(activities.getId(), report.getClientId());
                    countUse += userCount;
                }
                if (ObjectUtil.isNotEmpty(report.getClueId())) {
                    int userCount = erpTransactionVoucherMapper.selectCountByActivitiesIdAndClueId(activities.getId(), report.getClueId());
                    countUse += userCount;
                }
                if (activities.getMaxCount() < count+countUse) {
                    throw new ServiceException("同一个客户，"+activities.getName()+"，最多可参与"+activities.getMaxCount()+"次");
                }

                for (int j = 0; j < count; j++) {
                    if (fee.compareTo(new BigDecimal("0")) < 0) {
                        throw new ServiceException("收款金额无法满足多次参与"+activities.getName());
                    }
                    fee = fee.subtract(activities.getFullPrice());

                    ErpTransactionVoucher self = new ErpTransactionVoucher();
                    self.setPayRecordId(reportDto.getId());
                    self.setType(2);
                    self.setAllFee(activities.getFullPrice());
                    self.setBalance(activities.getFullPrice());
                    self.setBalanceUse(activities.getFullPrice());
                    self.setActivitieId(activities.getId());
                    self.setOperateUser(sysUser.getUserId());
                    self.setOpetateTime(new Date());
                    erpTransactionVoucherMapper.insertErpTransactionVoucher(self);

                    ErpTransactionVoucher give = new ErpTransactionVoucher();
                    give.setPayRecordId(reportDto.getId());
                    give.setType(3);
                    give.setAllFee(activities.getGivePrice());
                    give.setBalance(activities.getGivePrice());
                    give.setBalanceUse(activities.getGivePrice());
                    give.setActivitieId(activities.getId());
                    give.setOperateUser(sysUser.getUserId());
                    give.setOpetateTime(new Date());
                    give.setEndTime(activities.getUseEndTime());
                    erpTransactionVoucherMapper.insertErpTransactionVoucher(give);
                }
            }
        } else {
            if (fee.compareTo(new BigDecimal("0")) == 0) {
                erpTransactionVoucher.setZeroPay(1);
            }
        }
        result = erpOrderPayRecordMapper.updateErpOrderPayRecord(report);

        if (fee.compareTo(new BigDecimal("0")) < 0) {
            throw new ServiceException("收款金额无法满足参与所选活动");
        }
        erpTransactionVoucher.setPayRecordId(reportDto.getId());
        erpTransactionVoucher.setType(1);
        erpTransactionVoucher.setAllFee(fee);
        erpTransactionVoucher.setBalance(fee);
        erpTransactionVoucher.setBalanceUse(fee);
        erpTransactionVoucher.setOperateUser(sysUser.getUserId());
        erpTransactionVoucher.setOpetateTime(new Date());
        erpTransactionVoucherMapper.insertErpTransactionVoucher(erpTransactionVoucher);
        //钉钉异步发送报单消息
        syncSendDingRobotMsg(report, reportDto, sysUser);
        return result;
    }

    private void syncSendDingRobotMsg(ErpOrderPayRecord report, ReportDto reportDto, SysUser sysUser) {
        //是否是二维码报单
        if (report.getPaymentType() != 0){
            return;
        }
        //不是第一次报单，判断上一次报单人和本次是否为一人，不是一人则报单
        if (ReportConstants.IS_REPORT.equals(report.getSubmittedReport())
                && report.getReportUserId().equals(sysUser.getUserId())){
            log.info("二维码收款记录id【{}】，上次报单人【{}】，本次报单人【{}】",
                    report.getId(), report.getReportUserId(), sysUser.getUserId());
            return;
        }
        //更新
        ErpOrderPayRecord updateEn = new ErpOrderPayRecord();
        updateEn.setId(report.getId());
        updateEn.setReportUserId(sysUser.getUserId());
        updateEn.setReportRemark(reportDto.getReportRemark());
        updateEn.setCollectionCategory(reportDto.getCollectionCategory());
        //报单内容
        String context = getDeclarationContent(report, reportDto, sysUser, updateEn);
        erpOrderPayRecordMapper.updateErpOrderPayRecord(updateEn);
        //非业务部门，报单不发送消息
        String ancestors = sysUser.getDept().getAncestors();
        boolean match = Arrays.stream(ancestors.split(","))
                .anyMatch(deptId ->
                        Arrays.asList(reportDeptIds.split(",")).contains(deptId)
                );
        //当包含，或者等于它本身时部门
        if (match || Arrays.asList(reportDeptIds.split(",")).contains(String.valueOf(sysUser.getDeptId()))){
            ThreadPoolUtil.getPool().execute(() -> {
                DingRobotDTO dto = new DingRobotDTO(ReportConstants.ACCESS_TOKEN, ReportConstants.MSG_TITLE, context);
                dingService.sendDingRobotMessage(dto);
            });
        }
    }

    private String getDeclarationContent(ErpOrderPayRecord report, ReportDto reportDto, SysUser sysUser, ErpOrderPayRecord updateEn) {
        //获取部门
        SysDept dept = sysUser.getDept();
        SysDept secondDept = getSecondDeptName(dept);
        //是否是第一次报单
        if (ReportConstants.NO_REPORT.equals(report.getSubmittedReport())){
            updateEn.setSubmittedReport(ReportConstants.IS_REPORT);
            return getDefaultContext(report, reportDto, sysUser, dept, secondDept);
        } else {
            //不是第一次报单，判断上一次报单人和本次是否为一人，不是一人则报单
            return getAgainContext(report, reportDto, sysUser, dept, secondDept);
        }
    }

    private String getAgainContext(ErpOrderPayRecord report,
                                   ReportDto reportDto, SysUser sysUser, SysDept dept, SysDept secondDept) {
        //收款日期为当天需变为红色
        String payTime = DateUtil.format(report.getPayTime(), DateFormatConstants.TIME_FORMAT_DAY_ZH);
        if (DateUtil.compare(new Date(), report.getPayTime(), DateFormatConstants.TIME_FORMAT_DAY) == 0) {
            payTime = String.format(ReportConstants.RED_FONT, payTime);
        }
        SysUser sysUserBefore = erpOrderPayRecordMapper.selectSysUser(report.getReportUserId());
        SysDept sysDeptBefore = erpOrderPayRecordMapper.selectSysDept(sysUserBefore.getDeptId());
        SysDept secondDeptBefore = getSecondDeptName(sysDeptBefore);
        return String.format(ReportConstants.AGAIN_TEXT_TEMPLATE,
                payTime,
                (sysDeptBefore.getDeptId().equals(secondDeptBefore.getDeptId())
                        ? secondDeptBefore.getDeptName()
                        : secondDeptBefore.getDeptName() + sysDeptBefore.getDeptName()),
                sysUserBefore.getNickName(), report.getFee(), reportDto.getPaymentTypeStr(),
                CollectionCategoryEnum.getDescByCode(report.getCollectionCategory()),
                payTime,
                (dept.getDeptId().equals(secondDept.getDeptId())
                        ? secondDept.getDeptName()
                        : secondDept.getDeptName() + dept.getDeptName()),
                sysUser.getNickName(), report.getFee(), reportDto.getPaymentTypeStr(),
                CollectionCategoryEnum.getDescByCode(reportDto.getCollectionCategory()),
                (dept.getDeptId().equals(secondDept.getDeptId())
                        ? secondDept.getDeptName()
                        : secondDept.getDeptName() + "-" + dept.getDeptName()),
                sysUser.getNickName(), report.getFee(), reportDto.getPaymentTypeStr(),
                CollectionCategoryEnum.getDescByCode(reportDto.getCollectionCategory()),
                StrUtil.isBlank(reportDto.getReportRemark())
                        ? ReportConstants.DEFAULT_REMARK
                        : reportDto.getReportRemark()
        );
    }

    private static String getDefaultContext(ErpOrderPayRecord report,
                                            ReportDto reportDto, SysUser sysUser, SysDept dept, SysDept secondDept) {
        //收款日期为当天需变为红色
        String payTime = DateUtil.format(report.getPayTime(), DateFormatConstants.TIME_FORMAT_DAY_ZH);
        if (DateUtil.compare(new Date(), report.getPayTime(), DateFormatConstants.TIME_FORMAT_DAY) == 0) {
            payTime = String.format(ReportConstants.RED_FONT, payTime);
        }
        //判断
        return String.format(ReportConstants.DEFAULT_TEXT_TEMPLATE,
                payTime,
                (dept.getDeptId().equals(secondDept.getDeptId())
                        ? secondDept.getDeptName()
                        : secondDept.getDeptName() + dept.getDeptName()),
                sysUser.getNickName(), report.getFee(), reportDto.getPaymentTypeStr(),
                CollectionCategoryEnum.getDescByCode(reportDto.getCollectionCategory()),
                (dept.getDeptId().equals(secondDept.getDeptId())
                        ? secondDept.getDeptName()
                        : secondDept.getDeptName() + "-" + dept.getDeptName()),
                sysUser.getNickName(), report.getFee(), reportDto.getPaymentTypeStr(),
                CollectionCategoryEnum.getDescByCode(reportDto.getCollectionCategory()),
                StrUtil.isBlank(reportDto.getReportRemark())
                        ? ReportConstants.DEFAULT_REMARK
                        : reportDto.getReportRemark()
        );
    }

    @Override
    public SysDept getSecondDeptName(SysDept dept) {
        //获取部门名称（二级 + 末级）
        List<SysDept> deptList = erpOrderPayRecordMapper.selectSecondDept();
        //先判断是不是二级部门
        boolean match = deptList.stream().anyMatch(en -> en.getDeptId().equals(dept.getDeptId()));
        if (match){
            return dept;
        } else {
            List<Long> list = Arrays.stream(dept.getAncestors().split(",")).map(Long::valueOf).collect(Collectors.toList());
            //找父级
            return deptList.stream()
                    .filter(en -> list.contains(en.getDeptId()))
                    .findFirst()
                    .get();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int reportBack(ReportDto reportDto) {
        if (ObjectUtil.isEmpty(reportDto.getId())) {
            throw new ServiceException("id不可为空");
        }
        ErpOrderPayRecord erpOrderPayRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(reportDto.getId());
        if (erpOrderPayRecord.getReportedOrNot().intValue() != 1
                || erpOrderPayRecord.getReportReviewStatus().intValue() != 1) {
            throw new ServiceException("该支付记录非已报单状态");
        }

        ErpTransactionVoucher erpTransactionVoucher = new ErpTransactionVoucher();
        erpTransactionVoucher.setPayRecordId(reportDto.getId());
        List<ErpTransactionVoucher> voucherList = erpTransactionVoucherMapper.selectErpTransactionVoucherList(erpTransactionVoucher);
        List<Integer> voucherArray = new ArrayList<>();
        for (int i = 0; i < voucherList.size(); i++) {
            ErpTransactionVoucher voucher = voucherList.get(i);
            if (voucher.getBalanceUse().compareTo(voucher.getAllFee()) != 0) {
                throw new ServiceException("凭证已使用");
            }
            voucherArray.add(voucher.getId());
        }

        if (ObjectUtil.isEmpty(voucherArray) || voucherArray.size() == 0) {
            throw new ServiceException("该凭证未报单过，请核实");
        } else {
            List<Long> followIdList = erpTransactionVoucherFollowMapper.selectFollowIdListByTransactionVoucherList(voucherArray);
            if (followIdList.size() > 0) {
                erpTransactionVoucherFollowInfoMapper.delteByFollowIdList(followIdList);
                erpTransactionVoucherFollowMapper.delteByIdList(followIdList);
            }
            erpOrderPayRecord.setReportedOrNot(0);
            erpOrderPayRecord.setReportReviewStatus(-1);
            erpOrderPayRecordMapper.updateErpOrderPayRecord(erpOrderPayRecord);

            Integer[] vouchers = voucherArray.toArray(new Integer[voucherArray.size()]);
            return erpTransactionVoucherMapper.deleteErpTransactionVoucherByIds(vouchers);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int release(String orderNum, Integer type) {
        if (ObjectUtil.isEmpty(orderNum)) {
            throw new ServiceException("订单编号不可为空");
        }
        ErpOrders orders = new ErpOrders();
        orders.setVcOrderNumber(orderNum);
        List<ErpOrders> ordersList = erpOrdersMapper.selectErpOrdersList(orders);
        if (ObjectUtil.isEmpty(ordersList) && ordersList.size() != 1) {
            throw new ServiceException("订单不存在");
        }
        BigDecimal allFee = new BigDecimal("0");
        if (ordersList.get(0).getNumValidStatus() == 2 || type.intValue() == 2) {
            List<ErpTransactionVoucherFollow> waitReleaseList = new ArrayList<>();
            List<ErpTransactionVoucherFollow> followListUse = erpTransactionVoucherFollowMapper.selectListByOrderId(ordersList.get(0).getId());
            for (int i = 0; i < followListUse.size(); i++) {
                ErpTransactionVoucherFollow follow = followListUse.get(i);

                if (follow.getStatus() == 2 && follow.getType().intValue() == 1) {
                    boolean release = false;
                    for (int j = 0; j < followListUse.size(); j++) {
                        if (ObjectUtil.isNotEmpty(followListUse.get(j).getReleaseId()) &&
                                followListUse.get(j).getReleaseId().equals(follow.getId())
                                && Arrays.asList(2,3).contains(followListUse.get(j).getStatus())){
                            release = true;
                        }
                    }
                    if (!release) {
                        waitReleaseList.add(follow);
                    }
                }
            }
            for (int i = 0; i < waitReleaseList.size(); i++) {
                ErpTransactionVoucherFollow followRelease = waitReleaseList.get(i);

                ErpTransactionVoucher voucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(followRelease.getTransactionVoucher());
                voucher.setBalance(voucher.getBalance().add(followRelease.getFee()));
                voucher.setBalanceUse(voucher.getBalanceUse().add(followRelease.getFee()));
                erpTransactionVoucherMapper.updateErpTransactionVoucher(voucher);

                followRelease.setFee(followRelease.getFee().negate());
                followRelease.setStatus(2);
                followRelease.setType(3);
                followRelease.setReleaseId(followRelease.getId());
                followRelease.setId(transactionVoucherFollowService.createdId());
                erpTransactionVoucherFollowMapper.insertErpTransactionVoucherFollow(followRelease);

                allFee = allFee.add(followRelease.getFee());
            }
        } else {
            throw new ServiceException("仅作废订单可释放");
        }
        if (allFee.compareTo(new BigDecimal("0")) == 0 && type.intValue() == 1) {
            throw new ServiceException("该订单无使用流水");
        }
        return 1;
    }

    @Override
    public int transferUser(ErpOrderPayRecord erpOrderPayRecord) {
        if (ObjectUtil.isEmpty(erpOrderPayRecord.getId()) || StringUtils.equals(erpOrderPayRecord.getId(), "")) {
            throw new ServiceException("交易记录ID错误");
        }
        if (ObjectUtil.isEmpty(erpOrderPayRecord.getUserId())) {
            throw new ServiceException("移交人错误");
        }
        ErpOrderPayRecord updateRecord = erpOrderPayRecordMapper.selectErpOrderPayRecordById(Long.parseLong(erpOrderPayRecord.getId()));
        if (ObjectUtil.isNotEmpty(updateRecord.getUserId()) &&
                erpOrderPayRecord.getUserId().intValue() == updateRecord.getUserId().intValue()) {
            throw new ServiceException("移交前后所属人一致");
        }
        String transferBeforeUser = ObjectUtil.isEmpty(updateRecord.getTransferBeforeUser()) ? "" : updateRecord.getTransferBeforeUser();
        updateRecord.setTransferBeforeUser(transferBeforeUser+","+updateRecord.getUserId());
        updateRecord.setUserId(erpOrderPayRecord.getUserId());

        return erpOrderPayRecordMapper.updateErpOrderPayRecord(updateRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int voucherRefound(ErpExamineApproveCommonPayment erpExamineApproveCommonPayment) {
        ErpTransactionVoucher voucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(erpExamineApproveCommonPayment.getVoucherId());
        if (voucher.getBalanceUse().compareTo(erpExamineApproveCommonPayment.getFee()) < 0) {
            throw new ServiceException("可用余额不足");
        }

        voucher.setBalanceUse(voucher.getBalanceUse().subtract(erpExamineApproveCommonPayment.getFee()));
        voucher.setBalance(voucher.getBalance().subtract(erpExamineApproveCommonPayment.getFee()));
        if (voucher.getType() == 2) {
            ErpTransactionVoucher voucherGiveDto = new ErpTransactionVoucher();
            voucherGiveDto.setPayRecordId(voucher.getPayRecordId());
            voucherGiveDto.setType(3);
            voucherGiveDto.setActivitieId(voucher.getActivitieId());
            List<ErpTransactionVoucher> giveList = erpTransactionVoucherMapper.selectErpTransactionVoucherList(voucherGiveDto);

            ErpTransactionVoucher refondGive = new ErpTransactionVoucher();
            if (ObjectUtil.isNotEmpty(giveList) && giveList.size() > 0) {
                for (int i = 0; i < giveList.size(); i++) {
                    ErpTransactionVoucher give = giveList.get(i);
                    if (give.getAllFee().compareTo(give.getBalanceUse()) == 0) {
                        refondGive = give;
                        break;
                    }
                }
            } else {
                throw new ServiceException("赠金有误");
            }
            if (ObjectUtil.isNotEmpty(refondGive) && ObjectUtil.isNotEmpty(refondGive.getId())) {
                refondGive.setApproveIn(1);
                refondGive.setSelfId(voucher.getId());
                erpTransactionVoucherMapper.updateErpTransactionVoucher(refondGive);
            } else {
                throw new ServiceException("赠金已使用");
            }
        }

        erpTransactionVoucherMapper.updateErpTransactionVoucher(voucher);


        LoginUser loginUser = tokenService.getLoginUser();
        erpExamineApproveCommonPayment.setCreatedUser(loginUser.getSysUser().getUserId());
        erpExamineApproveCommonPayment.setUpdateUser(loginUser.getSysUser().getUserId());
        erpExamineApproveCommonPayment.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_NO_ORDER_REFUND);
        erpExamineApproveCommonPayment.setCreatedTime(new Date());
        erpExamineApproveCommonPayment.setUpdateTime(new Date());
        erpExamineApproveCommonPayment.setVoucherId(voucher.getId());
        int i = erpExamineApproveCommonPaymentMapper.insertErpExamineApproveCommonPayment(erpExamineApproveCommonPayment);

        ErpExamineApprove erpExamineApprove = new ErpExamineApprove();
        erpExamineApprove.setApproveType(erpExamineApproveCommonPayment.getApproveType());
        erpExamineApprove.setOtherId(erpExamineApproveCommonPayment.getId().toString());
        iErpExamineApproveService.insertErpExamineApprove(erpExamineApprove);

        return i;
    }

    @Override
    public List<ErpOrderPayRecord> getReportParticipationDetails(List<Long> payRecordIds) {
        List<ErpOrderPayRecord> list = erpOrderPayRecordMapper.selectByPayRecordIds(payRecordIds);
        //放入收款类目
        list.forEach(en ->
                en.setCollectionCategoryStr(CollectionCategoryEnum.getDescByCode(en.getCollectionCategory()))
        );
        List<PayCompany> allPayCompany = erpOrderPayRecordMapper.getAllPayCompany();
        //组装基本信息
        basicAssemblyInformation(new ErpOrderPayRecord(), list, allPayCompany);
        //找出参与明细
        return list.stream()
                .map(ErpOrderPayRecord::getFeeSplit)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Override
    public List<PayRecordBigCallBackVo> bigCallBack(ErpOrderPayRecord erpOrderPayRecord) {
        //时间
        if (StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeBegin()) && StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeEnd())) {
            erpOrderPayRecord.setPayTimeBegin(erpOrderPayRecord.getPayTimeBegin() + DateFormatConstants.TIME_BEGIN);
            erpOrderPayRecord.setPayTimeEnd(erpOrderPayRecord.getPayTimeEnd() + DateFormatConstants.TIME_END);
        }
        if (StrUtil.isNotEmpty(erpOrderPayRecord.getCallBackDateBegin()) && StrUtil.isNotEmpty(erpOrderPayRecord.getCallBackDateEnd())) {
            erpOrderPayRecord.setCallBackDateBegin(erpOrderPayRecord.getCallBackDateBegin() + DateFormatConstants.TIME_BEGIN);
            erpOrderPayRecord.setCallBackDateEnd(erpOrderPayRecord.getCallBackDateEnd() + DateFormatConstants.TIME_END);
        }

        List<PayRecordBigCallBackVo> list = erpOrderPayRecordMapper.bigCallBack(erpOrderPayRecord);

        for (int i = 0; i < list.size(); i++) {
            PayRecordBigCallBackVo vo = list.get(i);

            if (ObjectUtil.isNotEmpty(vo.getClientId())) {
                vo.setVcPhone(vo.getClientPhone());
            }
            if (ObjectUtil.isNotEmpty(vo.getClueId())) {
                BdClueContacts contacts = erpOrderPayRecordMapper.selectByClueId(vo.getClueId());
                if (ObjectUtil.isNotEmpty(contacts)) {
                    vo.setVcPhone(contacts.getVcPhone());
                }
            }

            if (ObjectUtil.isNotEmpty(vo.getVcPhone())) {
                vo.setVcPhoneSecret(vo.getVcPhone().substring(0, 3) + "****" + vo.getVcPhone().substring(7));
            }


            List<JSONObject> transactionVoucherList = erpTransactionVoucherMapper.selectErpTransactionVoucherByPayRecordId(vo.getId());
            List<PayRecordBigCallBackVo> feeSplit = new ArrayList<>();
            for (int j = 0; j < transactionVoucherList.size(); j++) {
                JSONObject voucher = transactionVoucherList.get(j);
                PayRecordBigCallBackVo split = new PayRecordBigCallBackVo();
                BeanUtils.copyProperties(vo, split, "billNo");

                BigDecimal balance = voucher.getBigDecimal("balance");
                BigDecimal balance_use = voucher.getBigDecimal("balance_use");
                BigDecimal all_fee = voucher.getBigDecimal("all_fee");
                Long transactionVoucher = voucher.getLong("id");

                int type = voucher.getInteger("type");
                if (type == 1) {
                    vo.setAllFee(all_fee);
                    vo.setBalance(balance);
                    vo.setBalanceUse(balance_use);
                    vo.setVoucher(transactionVoucher);
                    continue;
                }
                split.setVoucher(transactionVoucher);
                split.setBizId(transactionVoucher);
                split.setBalance(balance);
                split.setBalanceUse(balance_use);
                split.setAllFee(all_fee);
                split.setActivitieName(voucher.getString("name"));
                split.setActivitieId(voucher.getInteger("activitie_id"));
                split.setActivitieType(voucher.getInteger("activitieType"));
                split.setCallBackStatusStr(voucher.getString("callBackStatusStr"));
                split.setCallBackResultStr(voucher.getString("callBackResultStr"));
                split.setCallBackMemo(voucher.getString("callBackMemo"));
                String callBackDate = voucher.getString("callBackDate");
                if (ObjectUtil.isNotEmpty(voucher.get("callBackDate"))) {
                    split.setCallBackDate(DateUtils.parseDate(callBackDate));
                }
                if (type == 3) {
                    split.setActivitieOtherShare(voucher.getInteger("other_share") == 2 ? "不可与其他活动赠送金额一起使用":"");
                }

                feeSplit.add(split);
            }
            vo.setFeeSplit(feeSplit);
        }

        return list;
    }

    @Override
    public List<PayRecordBigCallBackVo> bigCallBackExport(ErpOrderPayRecord erpOrderPayRecord) {
        //时间
        if (StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeBegin()) && StrUtil.isNotEmpty(erpOrderPayRecord.getPayTimeEnd())) {
            erpOrderPayRecord.setPayTimeBegin(erpOrderPayRecord.getPayTimeBegin() + DateFormatConstants.TIME_BEGIN);
            erpOrderPayRecord.setPayTimeEnd(erpOrderPayRecord.getPayTimeEnd() + DateFormatConstants.TIME_END);
        }
        if (StrUtil.isNotEmpty(erpOrderPayRecord.getCallBackDateBegin()) && StrUtil.isNotEmpty(erpOrderPayRecord.getCallBackDateEnd())) {
            erpOrderPayRecord.setCallBackDateBegin(erpOrderPayRecord.getCallBackDateBegin() + DateFormatConstants.TIME_BEGIN);
            erpOrderPayRecord.setCallBackDateEnd(erpOrderPayRecord.getCallBackDateEnd() + DateFormatConstants.TIME_END);
        }

        List<PayRecordBigCallBackVo> list = erpOrderPayRecordMapper.bigCallBackExport(erpOrderPayRecord);

        return list;
    }
}
