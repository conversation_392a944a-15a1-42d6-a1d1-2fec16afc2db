package com.nnb.erp.domain;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2025-02-08
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ErpBusinessLicenseLabel implements Serializable {

    private Long id;

    @ApiModelProperty("企业经营范围")
    private String scopeBusiness;

    @ApiModelProperty("产品许可标签")
    private String productLicenseLabel;

    @ApiModelProperty("企业标签id")
    private Long tagId;

}
