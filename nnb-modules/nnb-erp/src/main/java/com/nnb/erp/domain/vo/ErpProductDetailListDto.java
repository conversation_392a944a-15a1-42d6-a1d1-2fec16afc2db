package com.nnb.erp.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ErpProductDetailListDto implements Serializable {
    private static final long serialVersionUID = 3210337600617718965L;


    /** 产品名称 */
    private String vcProductName;

    /** 产品分类id */
    private Long numClassificationId;

    /** 产品类型id */
    private Long numTypeId;

    /** 产品名称id */
    private Long numNameId;
    private List<Long> numNameIdList;

    /** 城市/区域id */
    private Long numAreaId;
    private List<Long> numAreaIdList;

    /** 纳税类型id */
    private Long numTaxId;
    private List<Long> numTaxIdList;

    /** 单位类型id */
    private Long numUnitId;

    /** 状态*/
    private Integer numIsUp;

    /** 审批*/
    private Integer numIsCheck;
    private String vcServiceName;

    private String deptId;

    private BigDecimal numPrice;

    /**
     * 产品ID
     */
    private Long numProductId;

    /**
     * 执照编号
     */
    private String number;

    /**
     * 执照ID
     */
    private Integer licenseId;

    /**
     * 产品ID集合
     */
    private List<Long> productIds;
    /**
     * 价格配置小程序是否显示
     */
    private Integer configurationAppletShow;
}
