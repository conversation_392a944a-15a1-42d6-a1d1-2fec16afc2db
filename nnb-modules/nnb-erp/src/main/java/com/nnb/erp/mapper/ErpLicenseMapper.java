package com.nnb.erp.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ComDictRegion;
import com.nnb.erp.domain.DictRegion;
import com.nnb.erp.domain.ErpLicense;
import com.nnb.erp.domain.dto.license.ErpClueLicenseDTO;
import com.nnb.erp.domain.dto.license.ErpLicenseDTO;
import com.nnb.erp.domain.dto.license.ErpLicenseSaleDto;
import com.nnb.erp.domain.dto.license.ErpLicenseTransferDTO;
import com.nnb.erp.domain.dto.qzd.ChangeDataDto;
import com.nnb.erp.domain.dto.qzd.CostStatisticsDto;
import com.nnb.erp.domain.dto.qzd.FinanceBoardDto;
import com.nnb.erp.domain.dto.qzd.SellLicenseCountDto;
import com.nnb.erp.domain.vo.license.ErpClueLicenseVO;
import com.nnb.erp.domain.vo.license.ErpLicenseSaleVo;
import com.nnb.erp.domain.vo.license.ErpLicenseTransferVO;
import com.nnb.erp.domain.vo.license.ErpLicenseVO;
import com.nnb.erp.domain.vo.qzd.*;
import net.sf.json.JSONObject;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 执照Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
public interface ErpLicenseMapper 
{
    /**
     * 查询执照
     * 
     * @param id 执照主键
     * @return 执照
     */
    public ErpLicense selectErpLicenseById(Long id);

    /**
     * 查询执照列表
     * 
     * @param erpLicense 执照
     * @return 执照集合
     */
    public List<ErpLicense> selectErpLicenseList(@Param("erpLicense")ErpLicenseDTO erpLicense);

    /***
     * 根据线索id查询关联的执照
     * @param clueIds
     * @return
     */
    public List<ErpLicense> selectErpLicenseListByCluIds(String clueIds);

    /**
     * 新增执照
     * 
     * @param erpLicense 执照
     * @return 结果
     */
    public int insertErpLicense(ErpLicense erpLicense);

    /**
     * 修改执照
     * 
     * @param erpLicense 执照
     * @return 结果
     */
    public int updateErpLicense(ErpLicense erpLicense);

    public int updateErpLicenseByNumber(ErpLicense erpLicense);


    @Select("SELECT * from erp_license where company_name = #{companyName}")
    ErpLicense getLicenseByCompanyName(@Param("companyName") String companyName);

    /**
     * 意向池列表
     * @param erpClueLicenseDTO
     * @return
     */
    List<ErpClueLicenseVO> getClueLicenseList(ErpClueLicenseDTO erpClueLicenseDTO);

    public List<ComDictRegion> selectComDictRegionList(@Param("idList") List<Long> idList);


    @Select("SELECT MAX(number) from erp_license")
    int getNumberMax();

    @Select("SELECT * from erp_license where number = #{number}")
    public ErpLicense getErpLicenseByNumber(@Param("number") String number);

    public List<ErpLicenseTransferVO> getErpLicenseTransferList(ErpLicenseTransferDTO erpLicenseTransferDTO);

    @Select("SELECT * from erp_license where company_name = #{companyName} and id != #{id}")
    ErpLicense getLicenseByCompanyNameAndId(@Param("companyName") String companyName, @Param("id") Long id);

    List<SellLicenseInfoVo> getSellLicenseInfoVo(SellLicenseCountDto sellLicenseCountDto);

    List<FinanceBoardVo> getFinanceBoardVo(FinanceBoardDto financeBoardDto);

    List<ChangeDataInfoVo> getChangeDataInfoBySell(ChangeDataDto changeDataDto);

    List<ChangeDataInfoVo> getChangeDataInfoByInner(ChangeDataDto changeDataDto);

    List<Map<String, Object>> recoveryCountList(@Param("beginDate") Date beginDate,
                                                @Param("endDate") Date endDate,
                                                @Param("cityId") Integer cityId);

    List<CostStaticsVo> getCostStaticsVo(CostStatisticsDto costStatisticsDto);

    @Select("SELECT * from erp_license where number = #{number} limit 1")
    ErpLicense getErpLicenseListByNumber(@Param("number") String number);


    /**
     * 查询执照列表
     *
     * @param erpLicense 执照
     * @return 执照集合
     */
    public List<ErpLicenseVO> selectInventoryList(@Param("erpLicense")ErpLicenseDTO erpLicense);

    /**
     * 查询执照销售列表
     *
     * @param erpLicense 执照
     * @return 执照集合
     */
    public List<ErpLicenseSaleVo> selectSaleList(@Param("dto") ErpLicenseSaleDto dto);


    @Select("select procure_contract_number from erp_license where id = #{id}")
    public String selectProcureContractNumberById(@Param("id") Long id);

//    @Update("update erp_procure_contract set license_id = null where license_id = #{id}")
//    public String updateProcureContractNumberByLicenseId(@Param("id") Long id);

    @Update("update erp_procure_contract set license_id = #{id} where contract_number = #{contractNumber}")
    public int updateProcureContractNumberByLicenseId(@Param("id") Long id, @Param("contractNumber") String contractNumber);
}
