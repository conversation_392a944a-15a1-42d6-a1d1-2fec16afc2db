package com.nnb.erp.domain.approval;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 启照多审批业务对象 erp_qzd_approval_service
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@ApiModel(value="ErpQzdApprovalService",description="启照多审批业务对象")
public class ErpQzdApprovalService extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 执照ID */
    @Excel(name = "执照ID")
    @ApiModelProperty("执照ID")
    private Long licenseId;

    /** 执照ID */
    @Excel(name = "执照ID")
    @ApiModelProperty("执照ID")
    private Long licenseNumber;

    /** 类型，1：付款申请，2：退款申请， */
    @Excel(name = "类型，1：付款申请，2：退款申请，")
    @ApiModelProperty("类型，1：付款申请，2：退款申请，")
    private Integer type;

    @ApiModelProperty("erp_examine_approve_type表id")
    private Long approveTypeId;

    /** 是否为库存  0：否，1：是 */
    @Excel(name = "是否为库存  0：否，1：是")
    @ApiModelProperty("是否为库存  0：否，1：是")
    private Integer isInventory;

    /** 付款或退款事由 */
    @Excel(name = "付款或退款事由")
    @ApiModelProperty("付款或退款事由")
    private String reason;

    /** 付款或退款金额 */
    @Excel(name = "付款或退款金额")
    @ApiModelProperty("付款或退款金额")
    private BigDecimal money;

    /** 收款人全称 */
    @Excel(name = "收款人全称")
    @ApiModelProperty("收款人全称")
    private String payeeName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty("银行账户")
    private String bankAccount;

    /** 开户行 */
    @Excel(name = "开户行")
    @ApiModelProperty("开户行")
    private String openingBank;

    /** 附件 */
    @Excel(name = "附件")
    @ApiModelProperty("附件")
    private String attachment;

    /** 补录状态，0：待审批，1：审批通过，2：驳回 */
    @Excel(name = "补录状态，0：待审批，1：审批通过，2：驳回")
    @ApiModelProperty("补录状态，0：待审批，1：审批通过，2：驳回,3:撤销")
    private Integer supplementStatus;

    /** 是否第一次申请，0：否，1：是 */
    @Excel(name = "是否第一次申请，0：否，1：是")
    @ApiModelProperty("是否第一次申请，0：否，1：是")
    private Integer isFirstApply;

    @Excel(name = "是否已付款，0：否，1：是")
    @ApiModelProperty("是否已付款，0：否，1：是")
    private Integer isPayment;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    /** 删除标记，0：未删除，1：已删除 */
    @ApiModelProperty("删除标记，0：未删除，1：已删除")
    private Integer delFlag;

    @ApiModelProperty("付款类型，1：收付款，2：中款，3：尾款")
    private Integer paymentType;

    public Long getLicenseNumber() {
        return licenseNumber;
    }

    public void setLicenseNumber(Long licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setLicenseId(Long licenseId) 
    {
        this.licenseId = licenseId;
    }

    public Long getLicenseId() 
    {
        return licenseId;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setIsInventory(Integer isInventory) 
    {
        this.isInventory = isInventory;
    }

    public Integer getIsInventory() 
    {
        return isInventory;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setMoney(BigDecimal money) 
    {
        this.money = money;
    }

    public BigDecimal getMoney() 
    {
        return money;
    }
    public void setPayeeName(String payeeName) 
    {
        this.payeeName = payeeName;
    }

    public String getPayeeName() 
    {
        return payeeName;
    }
    public void setBankAccount(String bankAccount) 
    {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() 
    {
        return bankAccount;
    }
    public void setOpeningBank(String openingBank) 
    {
        this.openingBank = openingBank;
    }

    public String getOpeningBank() 
    {
        return openingBank;
    }
    public void setAttachment(String attachment) 
    {
        this.attachment = attachment;
    }

    public String getAttachment() 
    {
        return attachment;
    }
    public void setSupplementStatus(Integer supplementStatus) 
    {
        this.supplementStatus = supplementStatus;
    }

    public Integer getSupplementStatus() 
    {
        return supplementStatus;
    }
    public void setIsFirstApply(Integer isFirstApply) 
    {
        this.isFirstApply = isFirstApply;
    }

    public Integer getIsFirstApply() 
    {
        return isFirstApply;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public Long getApproveTypeId() {
        return approveTypeId;
    }

    public void setApproveTypeId(Long approveTypeId) {
        this.approveTypeId = approveTypeId;
    }

    public Integer getIsPayment() {
        return isPayment;
    }

    public void setIsPayment(Integer isPayment) {
        this.isPayment = isPayment;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("licenseId", getLicenseId())
            .append("type", getType())
            .append("isInventory", getIsInventory())
            .append("reason", getReason())
            .append("money", getMoney())
            .append("payeeName", getPayeeName())
            .append("bankAccount", getBankAccount())
            .append("openingBank", getOpeningBank())
            .append("attachment", getAttachment())
            .append("remark", getRemark())
            .append("supplementStatus", getSupplementStatus())
            .append("isFirstApply", getIsFirstApply())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
