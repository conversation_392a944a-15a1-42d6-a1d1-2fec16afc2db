package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 合同主体对象 erp_contract_main
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@ApiModel(value="ErpContractMain",description="合同主体对象")
public class ErpContractMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 主体名称 */
    @Excel(name = "主体名称")
    @ApiModelProperty("主体名称")
    private String vcName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setVcName(String vcName) 
    {
        this.vcName = vcName;
    }

    public String getVcName() 
    {
        return vcName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("vcName", getVcName())
            .toString();
    }
}
