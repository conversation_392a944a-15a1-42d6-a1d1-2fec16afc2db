package com.nnb.erp.service;

import com.nnb.erp.domain.vo.course.*;

import java.util.List;

/**
 * 课程，服务层，接口。
 *
 * <AUTHOR>
 * @date 2022-06-21 13:50:52
 */
public interface ICourseInfoService {

    /**
     * 在线课程配置列表。
     *
     * @param courseInfoPageForConfigDTO 检索条件。
     * @return 返回课程信息分页查询结果。
     * <AUTHOR>
     * @since 2022-06-21 14:23:22
     */
    public List<CourseInfoPageForConfigVO> getCourseInfoPageForConfig(CourseInfoPageForConfigDTO courseInfoPageForConfigDTO);

    /**
     * 新增课程。
     *
     * @param courseInfoInsertOrUpdateDTO 待新增对象。
     * <AUTHOR>
     * @since 2022-06-21 15:49:13
     */
    public void addCourseInfo(CourseInfoInsertOrUpdateDTO courseInfoInsertOrUpdateDTO);

    /**
     * 更新课程。
     *
     * @param courseInfoInsertOrUpdateDTO 待更新对象。
     * <AUTHOR>
     * @since 2022-06-21 16:36:43
     */
    public void updateCourseInfo(CourseInfoInsertOrUpdateDTO courseInfoInsertOrUpdateDTO);

    /**
     * 更改指定课程状态。
     *
     * @param courseId 课程标识。
     * <AUTHOR>
     * @since 2022-06-21 16:52:22
     */
    public void changeStatus(Integer courseId);

    /**
     * 根据权限获取课程列表。
     *
     * @param courseInfoForUserDTO 查询条件。
     * @return 返回课程列表。
     * <AUTHOR>
     * @since 2022-06-21 17:41:40
     */
    public List<CourseInfoForUserVO> getCourseInfoPageForUser(CourseInfoForUserDTO courseInfoForUserDTO);

    /**
     * 获取课程详情，用于用户查看。
     *
     * @param courseId 课程标识。
     * @return 返回课程详情。
     * <AUTHOR>
     * @since 2022-06-21 18:02:50
     */
    public CourseInfoForUserVO getCourseInfoForUser(Integer courseId);

}
