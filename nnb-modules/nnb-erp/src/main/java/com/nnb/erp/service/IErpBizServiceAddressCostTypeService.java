package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizServiceAddressCostType;
import com.nnb.erp.domain.vo.ErpBizServiceAddressCostTypeVo;

/**
 * 成本地址种类配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-09-30
 */
public interface IErpBizServiceAddressCostTypeService 
{
    /**
     * 查询成本地址种类配置
     * 
     * @param id 成本地址种类配置主键
     * @return 成本地址种类配置
     */
    public ErpBizServiceAddressCostType selectErpBizServiceAddressCostTypeById(Integer id);

    /**
     * 查询成本地址种类配置列表
     * 
     * @param erpBizServiceAddressCostType 成本地址种类配置
     * @return 成本地址种类配置集合
     */
    public List<ErpBizServiceAddressCostType> selectErpBizServiceAddressCostTypeList(ErpBizServiceAddressCostType erpBizServiceAddressCostType);

    /**
     * 新增成本地址种类配置
     * 
     * @param erpBizServiceAddressCostType 成本地址种类配置
     * @return 结果
     */
    public int insertErpBizServiceAddressCostType(ErpBizServiceAddressCostType erpBizServiceAddressCostType);

    /**
     * 修改成本地址种类配置
     * 
     * @param erpBizServiceAddressCostType 成本地址种类配置
     * @return 结果
     */
    public int updateErpBizServiceAddressCostType(ErpBizServiceAddressCostType erpBizServiceAddressCostType);

    /**
     * 批量删除成本地址种类配置
     * 
     * @param ids 需要删除的成本地址种类配置主键集合
     * @return 结果
     */
    public int deleteErpBizServiceAddressCostTypeByIds(Integer[] ids);

    /**
     * 删除成本地址种类配置信息
     * 
     * @param id 成本地址种类配置主键
     * @return 结果
     */
    public int deleteErpBizServiceAddressCostTypeById(Integer id);

    public List<ErpBizServiceAddressCostTypeVo> getTreeList(ErpBizServiceAddressCostType erpExamineApproveTypeManage);
}
