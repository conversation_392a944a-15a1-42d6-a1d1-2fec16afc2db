package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpWtdzKp;
import com.nnb.erp.domain.dto.ErpWtdzKpDto;
import com.nnb.erp.domain.vo.ErpWtdzKpExportVo;
import com.nnb.erp.domain.vo.ErpWtdzKpVo;
import net.sf.json.JSONObject;

/**
 * 委托代征开票申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-10
 */
public interface IErpWtdzKpService 
{
    /**
     * 查询委托代征开票申请
     * 
     * @param id 委托代征开票申请主键
     * @return 委托代征开票申请
     */
    public ErpWtdzKp selectErpWtdzKpById(Long id);

    /**
     * 查询委托代征开票申请列表
     * 
     * @param erpWtdzKp 委托代征开票申请
     * @return 委托代征开票申请集合
     */
    public List<ErpWtdzKpVo> selectErpWtdzKpList(ErpWtdzKpDto dto);

    public List<ErpWtdzKpExportVo> exportWithExamine(Long approveId);

    /**
     * 新增委托代征开票申请
     * 
     * @param erpWtdzKp 委托代征开票申请
     * @return 结果
     */
    public int addOrUpdate(ErpWtdzKp erpWtdzKp);

    public int settlementById(Long id);

    public JSONObject settlementByIdList(ErpWtdzKpDto dto);

    /**
     * 修改委托代征开票申请
     * 
     * @param erpWtdzKp 委托代征开票申请
     * @return 结果
     */
    public int updateErpWtdzKp(ErpWtdzKp erpWtdzKp);

    /**
     * 批量删除委托代征开票申请
     * 
     * @param ids 需要删除的委托代征开票申请主键集合
     * @return 结果
     */
    public int deleteErpWtdzKpByIds(Long[] ids);

    /**
     * 删除委托代征开票申请信息
     * 
     * @param id 委托代征开票申请主键
     * @return 结果
     */
    public int deleteErpWtdzKpById(Long id);
}
