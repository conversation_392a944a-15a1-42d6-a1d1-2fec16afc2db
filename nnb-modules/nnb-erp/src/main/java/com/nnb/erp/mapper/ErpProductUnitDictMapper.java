package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpProductUnitDict;

/**
 * 单位Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface ErpProductUnitDictMapper 
{
    /**
     * 查询单位
     * 
     * @param numUnitId 单位主键
     * @return 单位
     */
    public ErpProductUnitDict selectErpProductUnitDictByNumUnitId(Long numUnitId);

    /**
     * 查询单位列表
     * 
     * @param erpProductUnitDict 单位
     * @return 单位集合
     */
    public List<ErpProductUnitDict> selectErpProductUnitDictList(ErpProductUnitDict erpProductUnitDict);

    /**
     * 新增单位
     * 
     * @param erpProductUnitDict 单位
     * @return 结果
     */
    public int insertErpProductUnitDict(ErpProductUnitDict erpProductUnitDict);

    /**
     * 修改单位
     * 
     * @param erpProductUnitDict 单位
     * @return 结果
     */
    public int updateErpProductUnitDict(ErpProductUnitDict erpProductUnitDict);

    /**
     * 删除单位
     * 
     * @param numUnitId 单位主键
     * @return 结果
     */
    public int deleteErpProductUnitDictByNumUnitId(Long numUnitId);

    /**
     * 批量删除单位
     * 
     * @param numUnitIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpProductUnitDictByNumUnitIds(Long[] numUnitIds);
}
