package com.nnb.erp.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 课程信息，实体。
 *
 * <AUTHOR>
 * @since 2022-06-21 14:02:28
 */
@Data
public class CourseInfoEntity {

    /**
     * 主键标识。
     */
    @ApiModelProperty("主键标识。")
    private Integer id;

    /**
     * 课程名称。
     */
    @ApiModelProperty("课程名称。")
    private String courseName;

    /**
     * 课程分类标识。
     */
    @ApiModelProperty("课程分类标识。")
    private Integer typeId;

    /**
     * 课程封面URL。
     */
    @ApiModelProperty("课程封面URL。")
    private String courseCover;

    /**
     * 课程链接URL。
     */
    @ApiModelProperty("课程链接URL。")
    private String courseLink;

    /**
     * 讲师姓名。
     */
    @ApiModelProperty("讲师姓名。")
    private String teacherName;

    /**
     * 讲师头像URL。
     */
    @ApiModelProperty("讲师头像URL。")
    private String teacherAvatar;

    /**
     * 讲师简介。
     */
    @ApiModelProperty("讲师简介。")
    private String teacherIntroduction;

    /**
     * 课程二维码URL。
     */
    @ApiModelProperty("课程二维码URL。")
    private String qrcodeCourse;

    /**
     * 考试二维码URL。
     */
    @ApiModelProperty("考试二维码URL。")
    private String qrcodeExam;

    /**
     * 反馈二维码URL。
     */
    @ApiModelProperty("反馈二维码URL。")
    private String qrcodeFeedback;

    /**
     * 有效状态：0-无效；1-有效。
     */
    @ApiModelProperty("有效状态：0-无效；1-有效。")
    private Integer status;

    /**
     * 创建人。
     */
    @ApiModelProperty("创建人。")
    private Integer createdBy;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 更新人。
     */
    @ApiModelProperty("更新人。")
    private Integer updateBy;

    /**
     * 更新时间。
     */
    @ApiModelProperty("更新时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String roleIds;

}
