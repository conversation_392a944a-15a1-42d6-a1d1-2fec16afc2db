package com.nnb.erp.service.impl;

import java.util.List;

import com.nnb.common.core.exception.ServiceException;
import com.nnb.erp.domain.ErpBusinessApproval;
import com.nnb.erp.domain.ErpServiceOrders;
import com.nnb.erp.domain.vo.ErpOrderForOrderDetailVO;
import com.nnb.erp.domain.vo.ErpServiceOrdersVO;
import com.nnb.erp.mapper.*;
import com.nnb.erp.service.IErpCommitOrderService;
import com.nnb.erp.service.IErpOrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.domain.ErpCouponUseRecord;
import com.nnb.erp.service.IErpCouponUseRecordService;

import javax.annotation.Resource;

/**
 * 小程序优惠券使用记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-15
 */
@Service
public class ErpCouponUseRecordServiceImpl implements IErpCouponUseRecordService {
    @Resource
    private ErpCouponUseRecordMapper erpCouponUseRecordMapper;
    @Resource
    private ErpBusinessApprovalMapper erpBusinessApprovalMapper;
    @Resource
    private ErpServiceOrdersMapper erpServiceOrdersMapper;

    @Resource
    private IErpOrdersService erpOrdersService;

    /**
     * 查询小程序优惠券使用记录
     *
     * @param id 小程序优惠券使用记录主键
     * @return 小程序优惠券使用记录
     */
    @Override
    public ErpCouponUseRecord selectErpCouponUseRecordById(Long id) {
        return erpCouponUseRecordMapper.selectErpCouponUseRecordById(id);
    }

    /**
     * 查询小程序优惠券使用记录列表
     *
     * @param erpCouponUseRecord 小程序优惠券使用记录
     * @return 小程序优惠券使用记录
     */
    @Override
    public List<ErpCouponUseRecord> selectErpCouponUseRecordList(ErpCouponUseRecord erpCouponUseRecord) {
        return erpCouponUseRecordMapper.selectErpCouponUseRecordList(erpCouponUseRecord);
    }

    /**
     * 新增小程序优惠券使用记录
     *
     * @param erpCouponUseRecord 小程序优惠券使用记录
     * @return 结果
     */
    @Override
    public int insertErpCouponUseRecord(ErpCouponUseRecord erpCouponUseRecord) {
        return erpCouponUseRecordMapper.insertErpCouponUseRecord(erpCouponUseRecord);
    }

    /**
     * 修改小程序优惠券使用记录
     *
     * @param erpCouponUseRecord 小程序优惠券使用记录
     * @return 结果
     */
    @Override
    public int updateErpCouponUseRecord(ErpCouponUseRecord erpCouponUseRecord) {
        return erpCouponUseRecordMapper.updateErpCouponUseRecord(erpCouponUseRecord);
    }

    /**
     * 批量删除小程序优惠券使用记录
     *
     * @param ids 需要删除的小程序优惠券使用记录主键
     * @return 结果
     */
    @Override
    public int deleteErpCouponUseRecordByIds(Long[] ids) {
        return erpCouponUseRecordMapper.deleteErpCouponUseRecordByIds(ids);
    }

    /**
     * 删除小程序优惠券使用记录信息
     *
     * @param id 小程序优惠券使用记录主键
     * @return 结果
     */
    @Override
    public int deleteErpCouponUseRecordById(Long id) {
        return erpCouponUseRecordMapper.deleteErpCouponUseRecordById(id);
    }

    /**
     * 维护小程序优惠券使用记录。
     *
     * @param orderId          订单标识。
     * @param examineOperation 审核状态：1通过，2驳回。
     * <AUTHOR>
     * @since 2022-04-20 15:29:21
     */
    @Override
    public void maintainCouponRecord(Long orderId, Integer examineOperation) {
        // 提单审核通过时，维护优惠券使用状态为：已使用。
        if (examineOperation.equals(1)) {
            // 获取订单基本信息。
            ErpOrderForOrderDetailVO orderBase = erpOrdersService.getOrderBase(orderId);

            // 获取订单内serviceOrder信息。
            List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(orderId);

            // 获取订单下单时的手机号，用以获取该手机号的小程序用户的优惠券信息。
            String phone = orderBase.getPhone();

            // 遍历serviceOrder，维护优惠券。
            for (ErpServiceOrders serviceOrders : serviceOrdersList) {
                // 将原使用的优惠券修改为未使用。
                erpCouponUseRecordMapper.cancelCouponUseByServiceOrderId(serviceOrders.getId());

                // 判断优惠券是否可使用。
                if (erpCouponUseRecordMapper.getCouponCountByPhoneAndCoupon(phone, serviceOrders.getNumCouponId()) > 0) {
                    // 若优惠券可使用则进行维护。
                    erpCouponUseRecordMapper.useCoupon(phone, serviceOrders.getNumCouponId(), orderId, serviceOrders.getId());

                } else {
                    // 若优惠券不可使用则抛出。
                    throw new ServiceException("无可用优惠券！");
                }

            }
        }

    }

}
