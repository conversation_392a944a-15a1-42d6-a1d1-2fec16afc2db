package com.nnb.erp.service.impl.gift;

import java.util.List;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.erp.domain.gift.ErpGiftRuleLabel;
import com.nnb.erp.mapper.gift.ErpGiftRuleLabelMapper;
import com.nnb.erp.service.gift.IErpGiftRuleLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 赠品规则标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
@Service
public class ErpGiftRuleLabelServiceImpl implements IErpGiftRuleLabelService
{
    @Autowired
    private ErpGiftRuleLabelMapper erpGiftRuleLabelMapper;

    /**
     * 查询赠品规则标签
     * 
     * @param id 赠品规则标签主键
     * @return 赠品规则标签
     */
    @Override
    public ErpGiftRuleLabel selectErpGiftRuleLabelById(Long id)
    {
        return erpGiftRuleLabelMapper.selectErpGiftRuleLabelById(id);
    }

    /**
     * 查询赠品规则标签列表
     * 
     * @param erpGiftRuleLabel 赠品规则标签
     * @return 赠品规则标签
     */
    @Override
    public List<ErpGiftRuleLabel> selectErpGiftRuleLabelList(ErpGiftRuleLabel erpGiftRuleLabel)
    {
        return erpGiftRuleLabelMapper.selectErpGiftRuleLabelList(erpGiftRuleLabel);
    }

    /**
     * 新增赠品规则标签
     * 
     * @param erpGiftRuleLabel 赠品规则标签
     * @return 结果
     */
    @Override
    public int insertErpGiftRuleLabel(ErpGiftRuleLabel erpGiftRuleLabel)
    {
        List<ErpGiftRuleLabel> erpGiftRuleLabels = erpGiftRuleLabelMapper.selectErpGiftRuleLabelList(erpGiftRuleLabel);
        if(CollectionUtils.isNotEmpty(erpGiftRuleLabels)){
            throw new ServiceException("该标签已存在");
        }
        erpGiftRuleLabel.setCreateTime(DateUtils.getNowDate());
        return erpGiftRuleLabelMapper.insertErpGiftRuleLabel(erpGiftRuleLabel);
    }

    /**
     * 修改赠品规则标签
     * 
     * @param erpGiftRuleLabel 赠品规则标签
     * @return 结果
     */
    @Override
    public int updateErpGiftRuleLabel(ErpGiftRuleLabel erpGiftRuleLabel) {
        ErpGiftRuleLabel erpGiftRuleLabelQuery = new ErpGiftRuleLabel();
        erpGiftRuleLabelQuery.setLabelName(erpGiftRuleLabel.getLabelName());
        List<ErpGiftRuleLabel> erpGiftRuleLabels = erpGiftRuleLabelMapper.selectErpGiftRuleLabelList(erpGiftRuleLabelQuery);
        if (CollectionUtils.isNotEmpty(erpGiftRuleLabels)) {
            throw new ServiceException("该标签已存在");
        }
        erpGiftRuleLabel.setUpdateTime(DateUtils.getNowDate());
        return erpGiftRuleLabelMapper.updateErpGiftRuleLabel(erpGiftRuleLabel);
    }

    /**
     * 批量删除赠品规则标签
     * 
     * @param ids 需要删除的赠品规则标签主键
     * @return 结果
     */
    @Override
    public int deleteErpGiftRuleLabelByIds(Long[] ids)
    {
        return erpGiftRuleLabelMapper.deleteErpGiftRuleLabelByIds(ids);
    }

    /**
     * 删除赠品规则标签信息
     * 
     * @param id 赠品规则标签主键
     * @return 结果
     */
    @Override
    public int deleteErpGiftRuleLabelById(Long id)
    {
        return erpGiftRuleLabelMapper.deleteErpGiftRuleLabelById(id);
    }
}
