package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpExamineApproveTypeManage;
import com.nnb.erp.domain.vo.ErpExamineApproveTypeManageVo;

/**
 * 执照类型配置Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
public interface IErpExamineApproveTypeManageService 
{
    /**
     * 查询执照类型配置
     * 
     * @param id 执照类型配置主键
     * @return 执照类型配置
     */
    public ErpExamineApproveTypeManage selectErpExamineApproveTypeManageById(Long id);

    /**
     * 查询执照类型配置列表
     * 
     * @param erpExamineApproveTypeManage 执照类型配置
     * @return 执照类型配置集合
     */
    public List<ErpExamineApproveTypeManageVo> selectErpExamineApproveTypeManageList(ErpExamineApproveTypeManage erpExamineApproveTypeManage);

    /**
     * 新增执照类型配置
     * 
     * @param erpExamineApproveTypeManage 执照类型配置
     * @return 结果
     */
    public int insertErpExamineApproveTypeManage(ErpExamineApproveTypeManage erpExamineApproveTypeManage);

    /**
     * 修改执照类型配置
     * 
     * @param erpExamineApproveTypeManage 执照类型配置
     * @return 结果
     */
    public int updateErpExamineApproveTypeManage(ErpExamineApproveTypeManage erpExamineApproveTypeManage);

    /**
     * 批量删除执照类型配置
     * 
     * @param ids 需要删除的执照类型配置主键集合
     * @return 结果
     */
    public int deleteErpExamineApproveTypeManageByIds(Long[] ids);

    /**
     * 删除执照类型配置信息
     * 
     * @param id 执照类型配置主键
     * @return 结果
     */
    public int deleteErpExamineApproveTypeManageById(Long id);
}
