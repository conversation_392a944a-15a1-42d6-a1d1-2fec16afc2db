package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.ErpLicenseDemandFeedBack;
import com.nnb.erp.domain.dto.ErpLicenseDemandDto;
import com.nnb.erp.domain.vo.ErpLicenseDemandVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpLicenseDemand;
import com.nnb.erp.service.IErpLicenseDemandService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 执照需求Controller
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/erpLicenseDemand")
@Api(tags = "ErpLicenseDemandController", description = "执照需求")
public class ErpLicenseDemandController extends BaseController
{
    @Autowired
    private IErpLicenseDemandService erpLicenseDemandService;

    /**
     * 查询执照需求列表
     */
    @ApiOperation(value = "查询执照需求列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpLicenseDemand.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpLicenseDemandDto dto)
    {
        startPage();
        List<ErpLicenseDemandVo> list = erpLicenseDemandService.selectErpLicenseDemandList(dto);
        return getDataTable(list);
    }

    /**
     * 导出执照需求列表
     */
//    @ApiOperation(value = "导出执照需求列表")
//    @PreAuthorize(hasPermi = "erp:demand:export")
//    //@Log(title = "执照需求", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ErpLicenseDemand erpLicenseDemand) throws IOException
//    {
//        List<ErpLicenseDemand> list = erpLicenseDemandService.selectErpLicenseDemandList(erpLicenseDemand);
//        ExcelUtil<ErpLicenseDemand> util = new ExcelUtil<ErpLicenseDemand>(ErpLicenseDemand.class);
//        util.exportExcel(response, list, "执照需求数据");
//    }

    /**
     * 获取执照需求详细信息
     */
    @ApiOperation(value = "获取执照需求详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpLicenseDemand.class)})
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="执照需求id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpLicenseDemandService.selectErpLicenseDemandById(id));
    }

    /**
     * 新增执照需求
     */
    @ApiOperation(value = "新增执照需求")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ErpLicenseDemand erpLicenseDemand)
    {
        return toAjax(erpLicenseDemandService.insertErpLicenseDemand(erpLicenseDemand));
    }

    /**
     * 修改执照需求
     */
    @ApiOperation(value = "修改执照需求")
    @PreAuthorize(hasPermi = "erp:demand:edit")
    //@Log(title = "执照需求", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpLicenseDemand erpLicenseDemand)
    {
        return toAjax(erpLicenseDemandService.updateErpLicenseDemand(erpLicenseDemand));
    }

    /**
     * 删除执照需求
     */
    @ApiOperation(value = "删除执照需求")
    @PreAuthorize(hasPermi = "erp:demand:remove")
    //@Log(title = "执照需求", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpLicenseDemandService.deleteErpLicenseDemandByIds(ids));
    }


    @ApiOperation(value = "匹配执照")
    @PostMapping("/matchLicense")
    public AjaxResult matchLicense(@RequestBody ErpLicenseDemandDto dto) {
        return toAjax(erpLicenseDemandService.matchLicense(dto));
    }


    @ApiOperation(value = "搁置执照")
    @PostMapping("/shelveLicense")
    public AjaxResult shelveLicense(@RequestBody ErpLicenseDemandDto dto) {
        return toAjax(erpLicenseDemandService.shelveLicense(dto));
    }


    @ApiOperation(value = "取消匹配")
    @PostMapping("/cancelLicense")
    public AjaxResult cancelLicense(@RequestBody ErpLicenseDemandDto dto) {
        return toAjax(erpLicenseDemandService.cancelLicense(dto));
    }


    @ApiOperation(value = "执照反馈")
    @PostMapping("/feedBack")
    public AjaxResult feedBack(@RequestBody ErpLicenseDemandFeedBack feedBack) {
        return toAjax(erpLicenseDemandService.feedBack(feedBack));
    }


    @ApiOperation(value = "查询执照需求反馈列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpLicenseDemand.class)})
    @GetMapping("/feedBackFist")
    public TableDataInfo feedBackFist(ErpLicenseDemandFeedBack feedBack)
    {
        startPage();
        List<ErpLicenseDemandFeedBack> list = erpLicenseDemandService.feedBackFist(feedBack);
        return getDataTable(list);
    }


    @ApiOperation(value = "执照反馈审核")
    @PostMapping("/feedBackApprove")
    public AjaxResult feedBackApprove(@RequestBody ErpLicenseDemandFeedBack feedBack) {
        return toAjax(erpLicenseDemandService.feedBackApprove(feedBack));
    }
}
