package com.nnb.erp.domain;

import java.util.Date;

import lombok.Data;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 企业/个人客户对象 erp_client
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="ErpClient",description="企业/个人客户对象")
public class ErpClient extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 企业id */
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long numEnterpriseId;

    /** 个人id */
    @Excel(name = "个人id")
    @ApiModelProperty("个人id")
    private Long numPersonalId;

    /** 线索id，企业最后一个经绑定的线索 */
    @Excel(name = "线索id，企业最后一个经绑定的线索")
    @ApiModelProperty("线索id，企业最后一个经绑定的线索")
    private Long numClueId;
    /**
     * 联系人名称。
     */
    @ApiModelProperty("联系人名称。")
    private String contactName;

    /**
     * 联系电话。
     */
    @ApiModelProperty("联系电话。")
    private String contactPhone;

    /** 企业状态，0 未成交；1 已成交 */
    @Excel(name = "企业状态，0 未成交；1 已成交")
    @ApiModelProperty("企业状态，0 未成交；1 已成交")
    private Integer numStatus;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty("城市id")
    private Long numCityId;

    /** 客户类型：1 企业；2 个人 */
    @Excel(name = "客户类型：1 企业；2 个人")
    @ApiModelProperty("客户类型：1 企业；2 个人")
    private Integer numType;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String vcRemark;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date datSigningDatecreatedTime;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private Long numUpdatedBy;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    private Date datSigningDateupdatedTime;

    @ApiModelProperty("用户标签：（1：会计）")
    private Integer label;

    @ApiModelProperty("来源:1提单，2老客，3会计")
    private Integer source;

    @ApiModelProperty("置顶：1是0否")
    private Integer top;

    @ApiModelProperty("空号检测")
    private Integer spaceCheck;

    @ApiModelProperty("风险检测")
    private Integer riskCheck;

    @ApiModelProperty("邮箱")
    private String postBox;

    @ApiModelProperty("性别 0男 1女 2未知")
    private Integer sex;

    @ApiModelProperty("角色")
    private String role;
}
