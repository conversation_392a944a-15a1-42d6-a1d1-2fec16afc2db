package com.nnb.erp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpExamineApproveKPDto extends BaseEntity {

    @ApiModelProperty("审批ID")
    private Long id;

    @ApiModelProperty("申请人")
    private Long createdUser;

    @ApiModelProperty("撤销状态")
    private Long revokeStatus;

    @ApiModelProperty("订单编号")
    private String orderNumber;

    @ApiModelProperty("抬头名称")
    private String invoiceHeaderName;

    @ApiModelProperty("抬头类型")
    private Integer invoiceHeaderType;

    @ApiModelProperty("是否开票")
    private Integer kpStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最终审核通过时间")
    private Date finishApproveBegin;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最终审核通过时间")
    private Date finishApproveEnd;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("最终审核通过时间")
    private Date HTZApproveDateBegin;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("最终审核通过时间")
    private Date HTZApproveDateEnd;

    @ApiModelProperty("开票主体")
    private Integer invoiceCompany;

    @ApiModelProperty("开票代码")
    private String invoiceNumber;

    @ApiModelProperty("开票代码")
    private String changeRed;


}
