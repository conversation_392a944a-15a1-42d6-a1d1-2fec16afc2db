package com.nnb.erp.controller.accountpermance;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.accountpermance.ErpAccountPerformanceDownloadRecord;
import com.nnb.erp.domain.vo.ErpAccountPerformanceDownloadRecordVo;
import com.nnb.erp.service.accountpermance.IErpAccountPerformanceDownloadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 会计绩效下载记录Controller
 * 
 * <AUTHOR>
 * @date 2023-04-04
 */
@RestController
@RequestMapping("/accountPerformanceDownloadRecord")
@Api(tags = "ErpAccountPerformanceDownloadRecordController", description = "会计绩效下载记录")
public class ErpAccountPerformanceDownloadRecordController extends BaseController
{
    @Autowired
    private IErpAccountPerformanceDownloadRecordService erpAccountPerformanceDownloadRecordService;

    /**
     * 查询会计绩效下载记录列表
     */
    @ApiOperation(value = "查询会计绩效下载记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountPerformanceDownloadRecord.class)})
    @PreAuthorize(hasPermi = "erp:record:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        startPage();
        List<ErpAccountPerformanceDownloadRecord> list = erpAccountPerformanceDownloadRecordService.selectErpAccountPerformanceDownloadRecordList(erpAccountPerformanceDownloadRecord);
        return getDataTable(list);
    }

    /**
     * 查询会计绩效下载记录列表
     */
    @ApiOperation(value = "查询会计绩效下载记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountPerformanceDownloadRecordVo.class)})
    //PreAuthorize(hasPermi = "erp:record:list")
    @GetMapping("/listByDeptId")
    public TableDataInfo listByDeptId(ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        startPage();
        List<ErpAccountPerformanceDownloadRecordVo> list = erpAccountPerformanceDownloadRecordService.selectErpAccountPerformanceDownloadRecordByDeptId(erpAccountPerformanceDownloadRecord);
        return getDataTable(list);
    }

    /**
     * 导出会计绩效下载记录列表
     */
    @ApiOperation(value = "导出会计绩效下载记录列表")
    @PreAuthorize(hasPermi = "erp:record:export")
    //@Log(title = "会计绩效下载记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord) throws IOException
    {
        List<ErpAccountPerformanceDownloadRecord> list = erpAccountPerformanceDownloadRecordService.selectErpAccountPerformanceDownloadRecordList(erpAccountPerformanceDownloadRecord);
        ExcelUtil<ErpAccountPerformanceDownloadRecord> util = new ExcelUtil<ErpAccountPerformanceDownloadRecord>(ErpAccountPerformanceDownloadRecord.class);
        util.exportExcel(response, list, "会计绩效下载记录数据");
    }

    /**
     * 获取会计绩效下载记录详细信息
     */
    @ApiOperation(value = "获取会计绩效下载记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountPerformanceDownloadRecord.class)})
    @PreAuthorize(hasPermi = "erp:record:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="会计绩效下载记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpAccountPerformanceDownloadRecordService.selectErpAccountPerformanceDownloadRecordById(id));
    }

    /**
     * 新增会计绩效下载记录
     */
    @ApiOperation(value = "新增会计绩效下载记录")
    @PreAuthorize(hasPermi = "erp:record:add")
    //@Log(title = "会计绩效下载记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        return toAjax(erpAccountPerformanceDownloadRecordService.insertErpAccountPerformanceDownloadRecord(erpAccountPerformanceDownloadRecord));
    }

    /**
     * 修改会计绩效下载记录
     */
    @ApiOperation(value = "修改会计绩效下载记录")
    @PreAuthorize(hasPermi = "erp:record:edit")
    //@Log(title = "会计绩效下载记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpAccountPerformanceDownloadRecord erpAccountPerformanceDownloadRecord)
    {
        return toAjax(erpAccountPerformanceDownloadRecordService.updateErpAccountPerformanceDownloadRecord(erpAccountPerformanceDownloadRecord));
    }

    /**
     * 删除会计绩效下载记录
     */
    @ApiOperation(value = "删除会计绩效下载记录")
    @PreAuthorize(hasPermi = "erp:record:remove")
    //@Log(title = "会计绩效下载记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpAccountPerformanceDownloadRecordService.deleteErpAccountPerformanceDownloadRecordByIds(ids));
    }
}
