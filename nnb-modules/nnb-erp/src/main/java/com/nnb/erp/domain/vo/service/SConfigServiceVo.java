package com.nnb.erp.domain.vo.service;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 s_config_service
 * 
 * <AUTHOR>
 * @date 2022-08-30
 */
@ApiModel(value="SConfigService",description="【请填写功能名称】对象")
public class SConfigServiceVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("服务id")
    private Long id;

    @ApiModelProperty("服务名称")
    private String name;

    @ApiModelProperty("状态")
    private Long status;

    @ApiModelProperty("服务类型id")
    private Long servicetypeid;

    @ApiModelProperty("服务类型名称")
    private String servicetypename;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getServicetypeid() {
        return servicetypeid;
    }

    public void setServicetypeid(Long servicetypeid) {
        this.servicetypeid = servicetypeid;
    }

    public String getServicetypename() {
        return servicetypename;
    }

    public void setServicetypename(String servicetypename) {
        this.servicetypename = servicetypename;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("status", getStatus())
                .append("servicetypeid", getServicetypeid())
                .append("servicetypename", getServicetypename())
                .toString();
    }
}
