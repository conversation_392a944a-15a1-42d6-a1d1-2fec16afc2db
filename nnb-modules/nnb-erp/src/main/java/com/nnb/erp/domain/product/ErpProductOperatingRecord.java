package com.nnb.erp.domain.product;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 产品操作记录对象 erp_product_operating_record
 * 
 * <AUTHOR>
 * @date 2023-07-07
 */
@ApiModel(value="ErpProductOperatingRecord",description="产品操作记录对象")
public class ErpProductOperatingRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 产品id */
    @Excel(name = "产品id")
    @ApiModelProperty("产品id")
    private Long numProductId;

    /** 操作类型 1：创建产品，2：编辑产品，3：上架，4：下架 */
    @Excel(name = "操作类型 1：创建产品，2：编辑产品，3：上架，4：下架")
    @ApiModelProperty("操作类型 1：创建产品，2：编辑产品，3：上架，4：下架")
    private Integer numOperationType;

    /** 变更内容 */
    @Excel(name = "变更内容")
    @ApiModelProperty("变更内容")
    private String vcOperationContent;

    /** 变更对象 */
    @Excel(name = "变更对象")
    @ApiModelProperty("变更对象")
    private String vcOperationJson;

    /** 审批ID */
    @Excel(name = "审批ID")
    @ApiModelProperty("审批ID")
    private String approvalId;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long numUpdatedBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNumProductId(Long numProductId) 
    {
        this.numProductId = numProductId;
    }

    public Long getNumProductId() 
    {
        return numProductId;
    }
    public void setNumOperationType(Integer numOperationType) 
    {
        this.numOperationType = numOperationType;
    }

    public Integer getNumOperationType() 
    {
        return numOperationType;
    }
    public void setVcOperationContent(String vcOperationContent) 
    {
        this.vcOperationContent = vcOperationContent;
    }

    public String getVcOperationContent() 
    {
        return vcOperationContent;
    }
    public void setVcOperationJson(String vcOperationJson) 
    {
        this.vcOperationJson = vcOperationJson;
    }

    public String getVcOperationJson() 
    {
        return vcOperationJson;
    }
    public void setApprovalId(String approvalId) 
    {
        this.approvalId = approvalId;
    }

    public String getApprovalId() 
    {
        return approvalId;
    }
    public void setNumCreatedBy(Long numCreatedBy) 
    {
        this.numCreatedBy = numCreatedBy;
    }

    public Long getNumCreatedBy() 
    {
        return numCreatedBy;
    }
    public void setDatCreatedTime(Date datCreatedTime) 
    {
        this.datCreatedTime = datCreatedTime;
    }

    public Date getDatCreatedTime() 
    {
        return datCreatedTime;
    }
    public void setNumUpdatedBy(Long numUpdatedBy) 
    {
        this.numUpdatedBy = numUpdatedBy;
    }

    public Long getNumUpdatedBy() 
    {
        return numUpdatedBy;
    }
    public void setDatUpdatedTime(Date datUpdatedTime) 
    {
        this.datUpdatedTime = datUpdatedTime;
    }

    public Date getDatUpdatedTime() 
    {
        return datUpdatedTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("numProductId", getNumProductId())
            .append("numOperationType", getNumOperationType())
            .append("vcOperationContent", getVcOperationContent())
            .append("vcOperationJson", getVcOperationJson())
            .append("approvalId", getApprovalId())
            .append("numCreatedBy", getNumCreatedBy())
            .append("datCreatedTime", getDatCreatedTime())
            .append("numUpdatedBy", getNumUpdatedBy())
            .append("datUpdatedTime", getDatUpdatedTime())
            .toString();
    }
}
