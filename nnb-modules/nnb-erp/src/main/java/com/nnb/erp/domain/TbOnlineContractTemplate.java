package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 电子合同模版对象 tb_online_contract_template
 * 
 * <AUTHOR>
 * @date 2024-09-23
 */
@ApiModel(value="TbOnlineContractTemplate",description="电子合同模版对象")
public class TbOnlineContractTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 合同模板标题。 */
    @Excel(name = "合同模板标题。")
    @ApiModelProperty("合同模板标题。")
    private String title;

    /** 1正常，0废弃 */
    @Excel(name = "1正常，0废弃")
    @ApiModelProperty("1正常，0废弃")
    private Long status;

    private String fileName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("status", getStatus())
            .toString();
    }
}
