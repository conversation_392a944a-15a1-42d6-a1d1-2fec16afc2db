package com.nnb.erp.domain.inventory;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 存货出/入库明细对象 ia_storage_detail
 * @Date: 2023-12-26
 * @Version: 1.0
 */
@ApiModel(value="IaStorageDetail",description="存货出/入库明细对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaStorageDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 企业/公司id */
    @Excel(name = "企业/公司id")
    @ApiModelProperty("企业/公司id")
    private Long companyId;

    /** 存货出/入库id */
    @Excel(name = "存货出/入库id")
    @ApiModelProperty("存货出/入库id")
    private Long iaStorageId;

    /** 存货id */
    @Excel(name = "存货id")
    @ApiModelProperty("存货id")
    private Long inventoryId;

    /** 存货编码 */
    @Excel(name = "存货编码")
    @ApiModelProperty("存货编码")
    private String inventoryCode;

    /** 存货名称 */
    @Excel(name = "存货名称")
    @ApiModelProperty("存货名称")
    private String inventoryName;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /** 单价 */
    @Excel(name = "单价")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /** 单价 */
    @Excel(name = "成本单价")
    @ApiModelProperty("成本单价")
    private BigDecimal costUnitPrice;

    /** 成本金额 */
    @Excel(name = "成本金额")
    @ApiModelProperty("成本金额")
    private BigDecimal costAmount;

    /** 1：邮寄；2：本人领取 */
    @Excel(name = "1：邮寄；2：本人领取")
    @ApiModelProperty("1：邮寄；2：本人领取")
    private Integer shipmentType;

    /** 快递单号 */
    @Excel(name = "快递单号")
    @ApiModelProperty("快递单号")
    private String expressNumber;

    @ApiModelProperty("商品图片")
    private String productImageUrl;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    /** 成本金额 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "当前页码", notes = "当前页码")
    private Integer pageNum;

    @ApiModelProperty(value = "每页记录数，默认为10", notes = "每页记录数")
    private Integer pageSize = 10;

    /**
     * 非数据库字段
     */
    private String keyWord;
    private List<Long> ids;
    private List<Long> iaStorageIds;
    private IaStorage iaStorage;
    private IaInventoryArchives iaInventoryArchives;
    //规格型号
    private String model;
    //单位名称
    private String iaUnitName;
    //单据时间
    private LocalDate iaStorageTime;
    private String iaStorageTimeStr;
    //仓库id
    private Long warehouseId;
    //单据类型
    private Integer busType;
}
