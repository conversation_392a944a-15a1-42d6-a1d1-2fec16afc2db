package com.nnb.erp.controller;

import com.nnb.common.core.web.controller.BaseController;
import com.nnb.erp.service.IErpRetainageReturnDetailService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 尾款回款详情，控制器。
 *
 * <AUTHOR>
 * @since 2022-04-06 09:25:19
 */
@RestController
@RequestMapping("/ErpRetainageReturnDetail")
@Api(tags = "ErpRetainageReturnDetailController", description = "尾款回款详情")
public class ErpRetainageReturnDetailController extends BaseController {
    @Resource
    private IErpRetainageReturnDetailService erpRetainageReturnDetailService;

}
