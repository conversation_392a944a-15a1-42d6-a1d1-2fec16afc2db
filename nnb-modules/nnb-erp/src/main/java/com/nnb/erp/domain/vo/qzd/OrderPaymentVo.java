package com.nnb.erp.domain.vo.qzd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单收退款详情
 */
@Data
public class OrderPaymentVo {

    @ApiModelProperty("收退款金额")
    private BigDecimal money;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("收退款日期")
    private Date createDate;

}
