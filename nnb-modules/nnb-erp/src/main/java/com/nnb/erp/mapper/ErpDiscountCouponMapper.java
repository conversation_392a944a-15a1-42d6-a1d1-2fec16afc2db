package com.nnb.erp.mapper;

import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.CompanyCoupon;
import com.nnb.erp.domain.ErpDiscountCoupon;
import com.nnb.erp.domain.dto.CouponUsageDto;
import com.nnb.erp.domain.dto.ErpDiscountCouponQuery;
import com.nnb.erp.domain.vo.CouponUsageVo;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 优惠券Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
public interface ErpDiscountCouponMapper
{
    /**
     * 查询优惠券
     *
     * @param id 优惠券主键
     * @return 优惠券
     */
    public ErpDiscountCoupon selectErpDiscountCouponById(Long id);
    List<ErpDiscountCoupon> selectErpDiscountCouponByIds(@Param("ids") List<Long> ids);

    /**
     * 查询优惠券列表
     *
     * @param erpDiscountCoupon 优惠券
     * @return 优惠券集合
     */
    public List<ErpDiscountCoupon> selectErpDiscountCouponList(ErpDiscountCoupon erpDiscountCoupon);

    public List<ErpDiscountCoupon> selectDiscountCouponList(ErpDiscountCoupon erpDiscountCoupon);


    public List<ErpDiscountCouponVo> selectErpDiscountCouponVoList(ErpDiscountCoupon erpDiscountCoupon);

    /**
     * 新增优惠券
     *
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    public int insertErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon);

    /**
     * 修改优惠券
     *
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    public int updateErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon);

    /**
     * 删除优惠券
     *
     * @param id 优惠券主键
     * @return 结果
     */
    public int deleteErpDiscountCouponById(Long id);

    /**
     * 批量删除优惠券
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpDiscountCouponByIds(Long[] ids);

    List<CompanyCoupon> selectCompanyCouponList(@Param("companyCoupon") CompanyCoupon companyCoupon);

    /**
     * 优惠券使用统计
     * @param couponUsageDto
     * @return
     */
    List<CouponUsageVo> couponUsage(@Param("couponUsageDto") CouponUsageDto couponUsageDto);

    List<ErpDiscountCouponVo> selectErpDiscountCouponVoLists(ErpDiscountCoupon erpDiscountCoupon);


    /***
     * 根据id获取抵扣券的信息，来源订单，使用信息
     * @param ids
     * @return
     */
    List<Map<String, Object>> selectDCMessage(@Param("ids") List<Long> ids);

    @Select("select * from erp_discount_coupon where xcx_phone = #{phone} and num_type = 3 and status = 0")
    List<ErpDiscountCoupon> getXcxCouponByPhone(@Param("phone") String phone);

    @Select("select count(id) from erp_discount_coupon where xcx_coupon_config_id = #{xcxCouponConfigId}")
    int countXcxCouponConfig(@Param("xcxCouponConfigId") Long xcxCouponConfigId);

    @Select("select count(id) from erp_discount_coupon where xcx_coupon_config_id = #{xcxCouponConfigId} and xcx_phone = #{oldPhone}")
    int countUserXcxCouponConfig(@Param("xcxCouponConfigId") Long xcxCouponConfigId, @Param("oldPhone") String oldPhone);
}
