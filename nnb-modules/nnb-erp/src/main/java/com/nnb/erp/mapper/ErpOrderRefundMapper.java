package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpOrderRefund;
import org.apache.ibatis.annotations.Param;

/**
 * 订单退款Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface ErpOrderRefundMapper
{
    /**
     * 查询订单退款
     *
     * @param id 订单退款主键
     * @return 订单退款
     */
    public ErpOrderRefund selectErpOrderRefundById(Long id);

    /**
     * 查询订单退款（级联查询）
     *
     * @param orderId 订单id
     * @return 订单退款
     */
    public List<ErpOrderRefund> selectErpOrderRefundByOrderId(@Param("orderId") Long orderId);

    public List<ErpOrderRefund> selectErpOrderRefundByIdList(@Param("idList") List<Long> idList);

    /**
     * 查询订单退款列表
     *
     * @param erpOrderRefund 订单退款
     * @return 订单退款集合
     */
    public List<ErpOrderRefund> selectErpOrderRefundList(ErpOrderRefund erpOrderRefund);

    /**
     * 新增订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    public int insertErpOrderRefund(ErpOrderRefund erpOrderRefund);

    /**
     * 修改订单退款
     *
     * @param erpOrderRefund 订单退款
     * @return 结果
     */
    public int updateErpOrderRefund(ErpOrderRefund erpOrderRefund);

    /**
     * 删除订单退款
     *
     * @param id 订单退款主键
     * @return 结果
     */
    public int deleteErpOrderRefundById(Long id);

    /**
     * 批量删除订单退款
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpOrderRefundByIds(@Param("ids") List<Long> ids);
}
