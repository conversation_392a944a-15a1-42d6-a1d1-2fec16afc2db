package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpCombinedActivityProduct;

/**
 * 活动产品关系Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-16
 */
public interface IErpCombinedActivityProductService 
{
    /**
     * 查询活动产品关系
     * 
     * @param id 活动产品关系主键
     * @return 活动产品关系
     */
    public ErpCombinedActivityProduct selectErpCombinedActivityProductById(Long id);

    /**
     * 查询活动产品关系列表
     * 
     * @param erpCombinedActivityProduct 活动产品关系
     * @return 活动产品关系集合
     */
    public List<ErpCombinedActivityProduct> selectErpCombinedActivityProductList(ErpCombinedActivityProduct erpCombinedActivityProduct);

    /**
     * 新增活动产品关系
     * 
     * @param erpCombinedActivityProduct 活动产品关系
     * @return 结果
     */
    public int insertErpCombinedActivityProduct(ErpCombinedActivityProduct erpCombinedActivityProduct);

    /**
     * 修改活动产品关系
     * 
     * @param erpCombinedActivityProduct 活动产品关系
     * @return 结果
     */
    public int updateErpCombinedActivityProduct(ErpCombinedActivityProduct erpCombinedActivityProduct);

    /**
     * 批量删除活动产品关系
     * 
     * @param ids 需要删除的活动产品关系主键集合
     * @return 结果
     */
    public int deleteErpCombinedActivityProductByIds(Long[] ids);

    /**
     * 删除活动产品关系信息
     * 
     * @param id 活动产品关系主键
     * @return 结果
     */
    public int deleteErpCombinedActivityProductById(Long id);
}
