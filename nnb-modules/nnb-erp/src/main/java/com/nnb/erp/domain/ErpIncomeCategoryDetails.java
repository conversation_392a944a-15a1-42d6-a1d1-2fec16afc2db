package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_income_category_details
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
@ApiModel(value="ErpIncomeCategoryDetails",description="【请填写功能名称】对象")
public class ErpIncomeCategoryDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    @ApiModelProperty("主键自增")
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty("订单id")
    private String orderId;

    /** 分类id */
    @Excel(name = "分类id")
    @ApiModelProperty("分类id")
    private Long categoryId;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private BigDecimal fee;

    /** 状态(0:删除 1:正常) */
    @Excel(name = "状态(0:删除 1:正常)")
    @ApiModelProperty("状态(0:删除 1:正常)")
    private Long status;

    /** erp_income_details.id */
    @Excel(name = "erp_income_details.id")
    @ApiModelProperty("erp_income_details.id")
    private Long incomeDetailId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("修改时间")
    private Date updateDate;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty("修改人")
    private Long updateUser;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }
    public void setFee(BigDecimal fee)
    {
        this.fee = fee;
    }

    public BigDecimal getFee()
    {
        return fee;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setIncomeDetailId(Long incomeDetailId) 
    {
        this.incomeDetailId = incomeDetailId;
    }

    public Long getIncomeDetailId() 
    {
        return incomeDetailId;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("categoryId", getCategoryId())
            .append("fee", getFee())
            .append("status", getStatus())
            .append("incomeDetailId", getIncomeDetailId())
            .append("createDate", getCreateDate())
            .append("createUser", getCreateUser())
            .append("updateDate", getUpdateDate())
            .append("updateUser", getUpdateUser())
            .toString();
    }
}
