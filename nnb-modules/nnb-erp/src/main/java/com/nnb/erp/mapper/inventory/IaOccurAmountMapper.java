package com.nnb.erp.mapper.inventory;

import com.nnb.erp.domain.inventory.IaOccurAmount;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 存货-发生额（每年不同）Mapper接口
 * @Date: 2024-01-02
 * @Version: 1.0
 */
@Repository
public interface IaOccurAmountMapper {

    /**
     * 查询存货-发生额（每年不同）
     *
     * @param id 存货-发生额（每年不同）主键
     * @return 存货-发生额（每年不同）
     */
    public IaOccurAmount selectIaOccurAmountById(Long id);

    /**
     * 查询存货-发生额（每年不同）列表
     *
     * @param iaOccurAmount 存货-发生额（每年不同）
     * @return 存货-发生额（每年不同）集合
     */
    public List<IaOccurAmount> selectIaOccurAmountList(IaOccurAmount iaOccurAmount);

    /**
     * 查询存货-发生额（每年不同）列表-去重
     * @param iaOccurAmount
     * @return
     */
    public List<IaOccurAmount> selectDisIaOccurAmountList(IaOccurAmount iaOccurAmount);

    /**
     * 新增存货-发生额（每年不同）
     *
     * @param iaOccurAmount 存货-发生额（每年不同）
     * @return 结果
     */
    public int insertIaOccurAmount(IaOccurAmount iaOccurAmount);

    /**
     * 修改存货-发生额（每年不同）
     *
     * @param iaOccurAmount 存货-发生额（每年不同）
     * @return 结果
     */
    public int updateIaOccurAmount(IaOccurAmount iaOccurAmount);

    /**
     * 删除存货-发生额（每年不同）
     *
     * @param id 存货-发生额（每年不同）主键
     * @return 结果
     */
    public int deleteIaOccurAmountById(Long id);

    /**
     * 批量删除存货-发生额（每年不同）
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIaOccurAmountByIds(@Param("ids") List<Long> ids);

    int updateInventoryName(@Param("idList") List<Long> idList, @Param("inventoryName") String inventoryName);
}
