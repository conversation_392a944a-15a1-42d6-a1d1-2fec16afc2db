package com.nnb.erp.domain.dto;

import com.nnb.erp.domain.ErpIncomeDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpIncomeDetailsDto extends ErpIncomeDetails {

    private Date payTimeBegin;
    private Date payTimeEnd;
    private String orderId;
    private List<Long> parentIdList;
}
