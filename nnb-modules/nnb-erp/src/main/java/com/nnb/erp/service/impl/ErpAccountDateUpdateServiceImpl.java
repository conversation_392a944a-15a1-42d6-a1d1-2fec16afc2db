package com.nnb.erp.service.impl;

import java.util.Date;
import java.util.List;

import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpExamineApproveConstants;
import com.nnb.erp.domain.ErpExamineApprove;
import com.nnb.erp.domain.SServiceMain;
import com.nnb.erp.mapper.SServiceMainMapper;
import com.nnb.erp.service.IErpExamineApproveService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpAccountDateUpdateMapper;
import com.nnb.erp.domain.ErpAccountDateUpdate;
import com.nnb.erp.service.IErpAccountDateUpdateService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 记账周期修改审批关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class ErpAccountDateUpdateServiceImpl implements IErpAccountDateUpdateService 
{
    @Autowired
    private ErpAccountDateUpdateMapper erpAccountDateUpdateMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SServiceMainMapper sServiceMainMapper;
    @Autowired
    private IErpExamineApproveService iErpExamineApproveService;

    /**
     * 查询记账周期修改审批关联
     * 
     * @param id 记账周期修改审批关联主键
     * @return 记账周期修改审批关联
     */
    @Override
    public ErpAccountDateUpdate selectErpAccountDateUpdateById(Long id)
    {
        return erpAccountDateUpdateMapper.selectErpAccountDateUpdateById(id);
    }

    /**
     * 查询记账周期修改审批关联列表
     * 
     * @param erpAccountDateUpdate 记账周期修改审批关联
     * @return 记账周期修改审批关联
     */
    @Override
    public List<ErpAccountDateUpdate> selectErpAccountDateUpdateList(ErpAccountDateUpdate erpAccountDateUpdate)
    {
        return erpAccountDateUpdateMapper.selectErpAccountDateUpdateList(erpAccountDateUpdate);
    }

    /**
     * 新增记账周期修改审批关联
     * 
     * @param erpAccountDateUpdate 记账周期修改审批关联
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertErpAccountDateUpdate(ErpAccountDateUpdate erpAccountDateUpdate)
    {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
//        SysUser sysUser = new SysUser();
//        sysUser.setUserId(1L);

        SServiceMain main = sServiceMainMapper.selectSServiceMainById(erpAccountDateUpdate.getServiceId());
        if (ObjectUtils.isEmpty(main)) {
            throw new ServiceException("服务单Id错误");
        }
        erpAccountDateUpdate.setOldStart(main.getAcStart());
        erpAccountDateUpdate.setOldEnd(main.getAcEnd());
        erpAccountDateUpdate.setCreatedUser(sysUser.getUserId());
        erpAccountDateUpdate.setCreatedDate(new Date());
        erpAccountDateUpdateMapper.insertErpAccountDateUpdate(erpAccountDateUpdate);

        //插入审批记录
        ErpExamineApprove erpExamineApprove = new ErpExamineApprove();
        erpExamineApprove.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_AC_UPDATE);
        erpExamineApprove.setOtherId(erpAccountDateUpdate.getId().toString());
        iErpExamineApproveService.insertErpExamineApprove(erpExamineApprove);

        return 1;
    }

    /**
     * 修改记账周期修改审批关联
     * 
     * @param erpAccountDateUpdate 记账周期修改审批关联
     * @return 结果
     */
    @Override
    public int updateErpAccountDateUpdate(ErpAccountDateUpdate erpAccountDateUpdate)
    {
        return erpAccountDateUpdateMapper.updateErpAccountDateUpdate(erpAccountDateUpdate);
    }

    /**
     * 批量删除记账周期修改审批关联
     * 
     * @param ids 需要删除的记账周期修改审批关联主键
     * @return 结果
     */
    @Override
    public int deleteErpAccountDateUpdateByIds(Long[] ids)
    {
        return erpAccountDateUpdateMapper.deleteErpAccountDateUpdateByIds(ids);
    }

    /**
     * 删除记账周期修改审批关联信息
     * 
     * @param id 记账周期修改审批关联主键
     * @return 结果
     */
    @Override
    public int deleteErpAccountDateUpdateById(Long id)
    {
        return erpAccountDateUpdateMapper.deleteErpAccountDateUpdateById(id);
    }
}
