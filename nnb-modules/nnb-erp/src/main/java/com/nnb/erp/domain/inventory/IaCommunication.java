package com.nnb.erp.domain.inventory;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: Chen-xy
 * @Description: 往来单位对象 ia_communication
 * @Date: 2024-03-01
 * @Version: 1.0
 */
@ApiModel(value="IaCommunication",description="往来单位对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaCommunication implements Serializable {

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long id;

    /** 往来单位名称 */
    @Excel(name = "往来单位名称")
    @ApiModelProperty("往来单位名称")
    private String commName;
}
