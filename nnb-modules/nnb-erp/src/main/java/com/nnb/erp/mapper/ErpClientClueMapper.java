package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpClientClue;

/**
 * 企业/个人线索关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-14
 */
public interface ErpClientClueMapper 
{
    /**
     * 查询企业/个人线索关系
     * 
     * @param id 企业/个人线索关系主键
     * @return 企业/个人线索关系
     */
    public ErpClientClue selectErpClientClueById(Long id);

    /**
     * 查询企业/个人线索关系列表
     * 
     * @param erpClientClue 企业/个人线索关系
     * @return 企业/个人线索关系集合
     */
    public List<ErpClientClue> selectErpClientClueList(ErpClientClue erpClientClue);

    /**
     * 新增企业/个人线索关系
     * 
     * @param erpClientClue 企业/个人线索关系
     * @return 结果
     */
    public int insertErpClientClue(ErpClientClue erpClientClue);

    /**
     * 修改企业/个人线索关系
     * 
     * @param erpClientClue 企业/个人线索关系
     * @return 结果
     */
    public int updateErpClientClue(ErpClientClue erpClientClue);

    /**
     * 删除企业/个人线索关系
     * 
     * @param id 企业/个人线索关系主键
     * @return 结果
     */
    public int deleteErpClientClueById(Long id);

    /**
     * 批量删除企业/个人线索关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpClientClueByIds(Long[] ids);
}
