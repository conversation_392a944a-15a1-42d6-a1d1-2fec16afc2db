package com.nnb.erp.service.impl.inventory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.DateFormatConstants;
import com.nnb.erp.constant.InventoryConstants;
import com.nnb.erp.constant.RedissonConstant;
import com.nnb.erp.converter.inventory.IaConverterContext;
import com.nnb.erp.converter.inventory.config.IaConfig;
import com.nnb.erp.converter.inventory.util.IaUtils;
import com.nnb.erp.domain.ErpOrders;
import com.nnb.erp.domain.inventory.*;
import com.nnb.erp.domain.vo.ErpOrderDetailForOmVO;
import com.nnb.erp.domain.vo.ErpProductForOrderDetailVO;
import com.nnb.erp.enums.IaStorageBusTypeEnum;
import com.nnb.erp.enums.IaStorageSourceTypeEnum;
import com.nnb.erp.mapper.ErpOrdersMapper;
import com.nnb.erp.mapper.inventory.*;
import com.nnb.erp.service.IErpOrdersService;
import com.nnb.erp.service.inventory.IaFinalService;
import com.nnb.erp.service.inventory.IaInventoryArchivesService;
import com.nnb.erp.service.inventory.IaOccurAmountService;
import com.nnb.erp.service.inventory.IaStorageService;
import com.nnb.erp.util.MybatisBatchUtils;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description: 存货出/入库Service业务层处理
 * @Date: 2023-12-26
 * @Version: 1.0
 */
@Slf4j
@Service
public class IaStorageServiceImpl implements IaStorageService {

    @Autowired
    private IaCompanyMapper iaCompanyMapper;

    @Autowired
    private IaStorageMapper iaStorageMapper;

    @Autowired
    private IaStorageDetailMapper iaStorageDetailMapper;

    @Autowired
    private IaInventoryArchivesMapper iaInventoryArchivesMapper;

    @Autowired
    private IaConverterContext iaConverterContext;

    @Autowired
    private MybatisBatchUtils mybatisBatchUtils;

    @Autowired
    private IaOccurAmountService iaOccurAmountService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IaInventoryWarehouseMapper iaInventoryWarehouseMapper;

    @Autowired
    private IaWarehouseArchivesMapper iaWarehouseArchivesMapper;

    @Autowired
    private IaInventoryArchivesService iaInventoryArchivesService;

    @Autowired
    private IaUnitMapper iaUnitMapper;

    @Autowired
    private IaFinalService iaFinalService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IErpOrdersService erpOrdersService;

    @Autowired
    private IaCommunicationMapper iaCommunicationMapper;

    @Autowired
    private IaConfig iaConfig;

    private ErpOrdersMapper erpOrdersMapper;
    /**
     * 查询存货出/入库
     *
     * @param id 存货出/入库主键
     * @return 存货出/入库
     */
    @Override
    public IaStorage selectIaStorageById(Long id) {
        IaStorage iaStorage = iaStorageMapper.selectIaStorageById(id);

        if (CollUtil.isNotEmpty(iaStorage.getIaStorageDetails())) {
            List<IaStorageDetail> iaStorageDetailList = iaStorage.getIaStorageDetails();
            //查询存货信息
            List<Long> InventoryIds = iaStorageDetailList.stream().map(IaStorageDetail::getInventoryId).collect(Collectors.toList());
            List<IaInventoryArchives> inventoryArchives = iaInventoryArchivesMapper.selectIaInventoryArchivesByIds(InventoryIds);
            //放入信息
            for (IaStorageDetail storageDetail : iaStorageDetailList) {
                //放入存货信息
                if (CollUtil.isNotEmpty(inventoryArchives)) {
                    for (IaInventoryArchives inventoryArchive : inventoryArchives) {
                        if (storageDetail.getInventoryId().equals(inventoryArchive.getId())) {
                            storageDetail.setIaInventoryArchives(inventoryArchive);
                            storageDetail.setModel(storageDetail.getIaInventoryArchives().getModel());
                            storageDetail.setIaUnitName(storageDetail.getIaInventoryArchives().getIaUnitName());
                        }
                    }
                }
                //判断是否结账，计算过成本时出库单显示成本金额
                if (storageDetail.getCostUnitPrice().compareTo(BigDecimal.ZERO) != 0) {
                    storageDetail.setUnitPrice(storageDetail.getCostUnitPrice());
                    storageDetail.setAmount(storageDetail.getCostAmount());
                }
            }
        }
        return iaStorage;
    }

    /**
     * 查询存货出/入库列表
     *
     * @param iaStorage 存货出/入库
     * @return 存货出/入库
     */
    @Override
    public List<IaStorage> selectIaStorageList(IaStorage iaStorage) {
        return iaStorageMapper.selectIaStorageList(iaStorage);
    }

    /**
     * 新增存货出/入库
     *
     * @param iaStorage 存货出/入库
     * @return 结果
     */
    @Override
    public int insertIaStorage(IaStorage iaStorage) {
        iaStorage.setCreateTime(LocalDate.now());
        return iaStorageMapper.insertIaStorage(iaStorage);
    }

    /**
     * 修改存货出/入库
     *
     * @param iaStorage 存货出/入库
     * @return 结果
     */
    @Override
    public int updateIaStorage(IaStorage iaStorage) {
        return iaStorageMapper.updateIaStorage(iaStorage);
    }

    /**
     * 批量删除存货出/入库
     *
     * @param ids 需要删除的存货出/入库主键
     * @return 结果
     */
    @Override
    public int deleteIaStorageByIds(List<Long> ids) {
        return iaStorageMapper.deleteIaStorageByIds(ids);
    }

    /**
     * 删除存货出/入库信息
     *
     * @param id 存货出/入库主键
     * @return 结果
     */
    @Override
    public int deleteIaStorageById(Long id) {
        return iaStorageMapper.deleteIaStorageById(id);
    }

    /**
     * 判断当前登陆人是否为仓库管理员
     *
     * @param sysUser
     * @return
     */
    private boolean isWarehouseAdministrator(SysUser sysUser) {
        if (ObjectUtil.isNotEmpty(sysUser)) {
            Long userId = sysUser.getUserId();
            IaWarehouseArchives searchPeo = new IaWarehouseArchives().setUserId(userId);
            List<IaWarehouseArchives> archives = iaWarehouseArchivesMapper.selectIaWarehouseArchivesList(searchPeo);
            return CollUtil.isNotEmpty(archives);
        }
        return false;
    }

    @Override
    public Map<String, Object> iaPageList(IaStorageSearch search) {
        Map<String, Object> map = new HashMap<>(2);
        //判断当前登陆人是否为仓库管理员
        SysUser sysUser = tokenService.getLoginUser().getSysUser();
        boolean flag = isWarehouseAdministrator(sysUser);
        //先查主表信息
        IaStorage iaStorage = getIaStorageParams(search, flag, sysUser);
        //存货/编码为空时
        if (ObjectUtil.isEmpty(search.getInventoryId())){
            return getIaPageList(search, iaStorage, map);
        } else {
            List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(iaStorage);
            List<Long> ids = iaStorageList.stream().map(IaStorage::getId).collect(Collectors.toList());
            //查询子表信息
            IaStorageDetail iaStorageDetail = new IaStorageDetail();
            iaStorageDetail.setInventoryId(search.getInventoryId()).setIaStorageIds(ids);
            List<IaStorageDetail> iaStorageDetailList = iaStorageDetailMapper.selectIaStorageDetailList(iaStorageDetail);
            if (CollUtil.isNotEmpty(iaStorageDetailList)){
                //重新查询
                List<Long> idList = iaStorageDetailList.stream()
                        .map(IaStorageDetail::getIaStorageId).collect(Collectors.toList());
                IaStorage iaSearch = new IaStorage().setIds(idList);
                return getIaPageList(search, iaSearch, map);
            } else {
                return getIaPageList(search, iaStorage, map);
            }
        }
    }

    @Override
    public Map<String, Object> iaPageListWithDetail(IaStorageSearch search) {
        Map<String, Object> map = new HashMap<>(2);
        //判断当前登陆人是否为仓库管理员
        SysUser sysUser = tokenService.getLoginUser().getSysUser();
        boolean flag = isWarehouseAdministrator(sysUser);
        //先查主表信息
        IaStorage iaStorage = getIaStorageParams(search, flag, sysUser);
        //存货/编码为空时
        if (ObjectUtil.isEmpty(search.getInventoryId())){
            return getIaPageListWithDetail(search, iaStorage, map);
        } else {
            List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(iaStorage);
            List<Long> ids = iaStorageList.stream().map(IaStorage::getId).collect(Collectors.toList());
            //查询子表信息
            IaStorageDetail iaStorageDetail = new IaStorageDetail();
            iaStorageDetail.setInventoryId(search.getInventoryId()).setIaStorageIds(ids);
            List<IaStorageDetail> iaStorageDetailList = iaStorageDetailMapper.selectIaStorageDetailList(iaStorageDetail);
            if (ObjectUtil.isNotEmpty(search.getInventoryId())){
                if (CollUtil.isNotEmpty(iaStorageDetailList)){
                    //重新查询
                    List<Long> idList = iaStorageDetailList.stream()
                            .map(IaStorageDetail::getIaStorageId).collect(Collectors.toList());
                    IaStorage iaSearch = new IaStorage().setIds(idList);
                    return getIaPageListWithDetail(search, iaSearch, map);
                } else {
                    map.put("list", new ArrayList<IaStorageDetail>());
                    map.put("total", 0);
                    return map;
                }
            } else {
                return getIaPageListWithDetail(search, iaStorage, map);
            }
        }
    }

    private IaStorage getIaStorageParams(IaStorageSearch search, boolean flag, SysUser sysUser) {
        IaStorage iaStorage = new IaStorage();
        BeanUtils.copyProperties(search, iaStorage);
        if (flag) {
            if (ObjectUtil.isNotEmpty(sysUser)) {
                iaStorage.setCreatorId(sysUser.getUserId());
            }
        }
        return iaStorage;
    }

    private Map<String, Object> getIaPageList(IaStorageSearch search, IaStorage iaStorage, Map<String, Object> map) {
        //分页
        Page<Object> objects = PageHelper.startPage(search.getPageNum(), search.getPageSize(), null);
        List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(iaStorage);
        if (CollUtil.isNotEmpty(iaStorageList)) {
            //放入类型
            iaStorageList.forEach(en -> {
                en.setBusTypeStr(IaStorageBusTypeEnum.getTypeByCode(en.getBusType()));
                en.setSourceTypeStr(IaStorageSourceTypeEnum.getTypeByCode(en.getSourceType()));
                en.setReviewStr(IaStorage.ReviewStatusEnum.getDescByStatus(en.getReviewStatus()));
            });
            map.put("list", iaStorageList);
            map.put("total", objects.getTotal());
        } else {
            map.put("list", new ArrayList<IaStorage>());
            map.put("total", 0);
        }
        return map;
    }

    private Map<String, Object> getIaPageListWithDetail(IaStorageSearch search, IaStorage iaStorage, Map<String, Object> map) {
        //分页
        Page<Object> objects = PageHelper.startPage(search.getPageNum(), search.getPageSize(), null);
        List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(iaStorage);
        if (CollUtil.isNotEmpty(iaStorageList)) {
            //查询明细用明细封装
            List<Long> ids = iaStorageList.stream().map(IaStorage::getId).collect(Collectors.toList());
            List<IaStorageDetail> iaStorageDetails = iaStorageDetailMapper.selectIaStorageDetailList(
                    new IaStorageDetail().setIaStorageIds(ids)
            );
            //放入类型
            iaStorageList.forEach(en -> {
                en.setBusTypeStr(IaStorageBusTypeEnum.getTypeByCode(en.getBusType()));
                en.setSourceTypeStr(IaStorageSourceTypeEnum.getTypeByCode(en.getSourceType()));
                en.setReviewStr(IaStorage.ReviewStatusEnum.getDescByStatus(en.getReviewStatus()));
            });
            List<IaStorageDetail> resultList = iaStorageDetails.stream()
                    .collect(Collectors.groupingBy(IaStorageDetail::getIaStorageId))
                    .entrySet().stream().map(en -> {
                        IaStorageDetail detail = new IaStorageDetail();
                        BeanUtils.copyProperties(en.getValue().get(0), detail);
                        iaStorageList.stream().filter(ia -> ia.getId().equals(en.getKey()))
                                .findAny()
                                .ifPresent(ia -> {
                                    BigDecimal reduce = en.getValue().stream().map(IaStorageDetail::getAmount)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    detail.setAmount(reduce);
                                    detail.setIaStorage(ia);
                                    detail.setIaStorageTime(ia.getCreateTime());
                                });
                        return detail;
                    }).sorted(Comparator.comparing(IaStorageDetail::getIaStorageTime).reversed())
                    .collect(Collectors.toList());
            map.put("list", resultList);
            map.put("total", objects.getTotal());
        } else {
            map.put("list", new ArrayList<IaStorageDetail>());
            map.put("total", 0);
        }
        return map;
    }

    @Override
    public Map<String, Object> pageList(IaStorageSearch search) {
        Map<String, Object> map = new HashMap<>(2);
        //判断当前登陆人是否为仓库管理员
        SysUser sysUser = tokenService.getLoginUser().getSysUser();
        boolean flag = isWarehouseAdministrator(sysUser);
        //先查主表信息
        IaStorage iaStorage = getIaStorageParams(search, flag, sysUser);
        List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(iaStorage);
        if (CollUtil.isEmpty(iaStorageList)) {
            map.put("list", new ArrayList<IaStorageDetail>());
            map.put("total", 0);
            return map;
        }
        //放入类型
        for (IaStorage storage : iaStorageList) {
            storage.setBusTypeStr(IaStorageBusTypeEnum.getTypeByCode(storage.getBusType()));
            storage.setSourceTypeStr(IaStorageSourceTypeEnum.getTypeByCode(storage.getSourceType()));
        }
        List<Long> ids = iaStorageList.stream().map(IaStorage::getId).collect(Collectors.toList());
        //查询子表信息
        IaStorageDetail iaStorageDetail = new IaStorageDetail();
        iaStorageDetail.setInventoryId(search.getInventoryId()).setIaStorageIds(ids);
        //是否分页
        List<IaStorageDetail> iaStorageDetailList = new ArrayList<>();
        if (!InventoryConstants.EXPORT_FILE.equals(search.getExportFile())) {
            //分页
            Page<Object> objects = PageHelper.startPage(search.getPageNum(), search.getPageSize(), null);
            iaStorageDetailList = iaStorageDetailMapper.selectIaStorageDetailList(iaStorageDetail);
            map.put("total", objects.getTotal());
        } else {
            iaStorageDetailList = iaStorageDetailMapper.selectIaStorageDetailList(iaStorageDetail);
        }
        if (CollUtil.isEmpty(iaStorageDetailList)) {
            map.put("list", new ArrayList<IaStorageDetail>());
            map.put("total", 0);
            return map;
        }
        //查询存货信息
        List<Long> InventoryIds = iaStorageDetailList.stream().map(IaStorageDetail::getInventoryId).collect(Collectors.toList());
        List<IaInventoryArchives> inventoryArchives = iaInventoryArchivesMapper.selectIaInventoryArchivesByIds(InventoryIds);
        //放入信息
        for (IaStorageDetail storageDetail : iaStorageDetailList) {
            //放入主表信息
            for (IaStorage storage : iaStorageList) {
                if (storageDetail.getIaStorageId().equals(storage.getId())) {
                    storageDetail.setIaStorage(storage);
                    storageDetail.setIaStorageTime(storage.getCreateTime());
                }
            }
            //放入存货信息
            if (CollUtil.isNotEmpty(inventoryArchives)) {
                for (IaInventoryArchives inventoryArchive : inventoryArchives) {
                    if (storageDetail.getInventoryId().equals(inventoryArchive.getId())) {
                        storageDetail.setIaInventoryArchives(inventoryArchive);
                    }
                }
            }
        }
        iaStorageDetailList.sort(Comparator.comparing(IaStorageDetail::getIaStorageTime));
        map.put("list", iaStorageDetailList);
        return map;
    }

    @Override
    public String getMaxCode(IaStorage iaStorage) {
        String code = iaStorageMapper.selectMaxCode(iaStorage.getBusType(), iaStorage.getCreateTime(), iaStorage.getCompanyId());
        return IaUtils.getMaxCode(code);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(IaStorage iaStorage) {

        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(iaStorage.getCompanyId());
        verifyCheckout(iaStorage.getCreateTime(), iaCompany);

        Boolean flag;
        if (IaStorageSourceTypeEnum.END_OF_WORK_ENTRY.getCode().equals(iaStorage.getSourceType())) {
            flag = iaConverterContext
                    .getInstance(IaStorageBusTypeEnum.getBusTypeByCode(iaStorage.getBusType()))
                    .manualInputSave(iaStorage);
        } else {
            flag = iaConverterContext
                    .getInstance(IaStorageBusTypeEnum.getBusTypeByCode(iaStorage.getBusType()))
                    .manualImportSave(iaStorage);
        }
        //更新仓库库存 -- 新增明细
        RLock rLock = redissonClient.getLock(RedissonConstant.INVENTORY_LOCK_KEY);
        try {
            /**
             *  主要执行一下几个操作
             *
             *  1、将localKey设置到Redis服务器上，默认过期时间是30s（看门狗）
             *  2、每10s触发一次锁续命功能
             *  rLock.tryLock()
             *  场景：查询商品信息加缓存的操作，读是占大部分的，所以如何减少加锁逻辑就很有必要，所以可以尝试使用trylock的方式提升性能
             *  rLock.lock()
             *  场景：只是减库存操作，建议使用lock
             */
            boolean isLocked = rLock.tryLock(10, 30, TimeUnit.SECONDS);
            if (isLocked) {
                //处理库存 (处理的都是新增的明细，新增的明细包括出库以及入库)
                inHandleSaveList(iaStorage, iaStorage.getIaStorageDetails());
            } else {
                throw new ServiceException("新增：获取redisson锁失败", 400);
            }
        } catch (Exception e) {
            log.error("新增：添加库存失败{}", e.getCause().getMessage());
            throw new ServiceException("新增：添加库存失败", 400);
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return flag;
    }

    /**
     * 检验库存
     *
     * @param iaStorage
     */
    @Override
    public void inventoryInspection(IaStorage iaStorage, Integer methodType) {
        if (IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getCode().equals(iaStorage.getBusType())
                || IaStorageBusTypeEnum.SALES_OUTBOUND.getCode().equals(iaStorage.getBusType())
                || (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType()) && InventoryConstants.DELETE_METHOD.equals(methodType))
                || (IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType()) && InventoryConstants.DELETE_METHOD.equals(methodType))
                || (IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType()))
                || (IaStorageBusTypeEnum.SALES_RETURNS.getCode().equals(iaStorage.getBusType()))
        ) {
            //校验是否能够出库
            List<String> collect = iaStorage.getIaStorageDetails().stream()
                    .map(en -> iaStorage.getWarehouseId() + "_" + en.getInventoryId())
                    .collect(Collectors.toList());
            //查询
            IaInventoryWarehouse search = new IaInventoryWarehouse()
                    .setCompanyId(iaStorage.getCompanyId()).setInWareIndexList(collect);
            List<IaInventoryWarehouse> list = iaInventoryWarehouseMapper.selectIaInventoryWarehouseList(search);

            //查询源数据 用于更新时检验库存
            List<IaStorageDetail> sourceList = new ArrayList<>();
            if (InventoryConstants.UPDATE_METHOD.equals(methodType)){
                sourceList = iaStorageDetailMapper.selectByIaStorageId(iaStorage.getId());
            }

            //循环校验
            for (IaStorageDetail detail : iaStorage.getIaStorageDetails()) {
                //若处于更新数据时，前后两次数量相等时，不校验此存货库存
                if (InventoryConstants.UPDATE_METHOD.equals(methodType)){
                    IaStorageDetail sourceDetail = sourceList.stream()
                            .filter(en -> en.getId().equals(detail.getId()))
                            .findAny().orElse(null);
                    if (ObjectUtil.isNotEmpty(sourceDetail)){
                        if (detail.getQuantity().compareTo(sourceDetail.getQuantity()) == 0){
                            continue;
                        }
                    }
                }
                IaInventoryWarehouse orElse = list.stream()
                        .filter(en -> en.getInventoryId().equals(detail.getInventoryId()))
                        .findAny().orElse(null);
                if (ObjectUtil.isNotEmpty(orElse)) {
                    assert orElse != null;
                    if (InventoryConstants.DELETE_METHOD.equals(methodType)) {
                        //入库校验（若 库存 - 当前删除的存货数量 < 0 时 不允许删除
                        //入库校验（若 库存 <  当前删除的存货数量 时 不允许删除
                        //销售出库，库存得够才能删除
                        if (IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())
                                || IaStorageBusTypeEnum.SALES_RETURNS.getCode().equals(iaStorage.getBusType())
                        ) {
                            if (orElse.getInQty().compareTo(detail.getQuantity().abs()) < 0) {
                                String template = String.format(InventoryConstants.INSUFFICIENT_INVENTORY, iaStorage.getWarehouseCode(),
                                        iaStorage.getWarehouseName(), detail.getInventoryCode(), detail.getInventoryName());
                                throw new ServiceException(template, 400);
                            }
                        }
                    } else {
                        //出库校验（若出库大于库存则不允许
                        //采购退货时 库存数 > 退货数 才能退货
                        if (IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getCode().equals(iaStorage.getBusType())
                                || IaStorageBusTypeEnum.SALES_OUTBOUND.getCode().equals(iaStorage.getBusType())
                                || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType())
                        ) {
                            if (detail.getQuantity().abs().compareTo(orElse.getInQty()) > 0) {
                                String template = String.format(InventoryConstants.INSUFFICIENT_INVENTORY, iaStorage.getWarehouseCode(),
                                        iaStorage.getWarehouseName(), detail.getInventoryCode(), detail.getInventoryName());
                                throw new ServiceException(template, 400);
                            }
                        }
                    }
                } else {
                    //未录入库存
                    String template = String.format(InventoryConstants.INVENTORY_NOT_ENTERED, iaStorage.getWarehouseCode(),
                            iaStorage.getWarehouseName(), detail.getInventoryCode(), detail.getInventoryName());
                    throw new ServiceException(template, 400);
                }
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(IaStorage iaStorage) {
        //已经审核不允许修改
        IaStorage storage = iaStorageMapper.selectIaStorageById(iaStorage.getId());
        if (InventoryConstants.APPROVED.equals(storage.getReviewStatus())) {
            throw new ServiceException("当前单据已审核通过不允许修改！", 400);
        }
        //检验库存
        inventoryInspection(iaStorage, InventoryConstants.UPDATE_METHOD);
        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(iaStorage.getCompanyId());
        verifyCheckout(iaStorage.getCreateTime(), iaCompany);
        //检验期初
        if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                && !iaCompany.getStartPeriod().equals(iaCompany.getCurrentPeriod())) {
            throw new ServiceException("当前期间不是开始期间，期初不允许修改！", 400);
        }
        //已存在出入库期初不允许修改
//        if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())){
//            if (iaStorageMapper.selectIaStorageCountNotBegin() > 0){
//                throw new ServiceException("已存在出入库数据，期初不允许修改！", 400);
//            }
//        }
        //主信息，仓库，往来单位，采购合同，领用人
        updateMainInformation(iaStorage);
        iaStorageMapper.updateIaStorage(iaStorage);
        //子表信息
        List<IaStorageDetail> detailList = iaStorage.getIaStorageDetails();
        if (CollUtil.isEmpty(detailList)) {
            throw new ServiceException("详情为空，更新失败！", 400);
        }
        //查询子表信息
        IaStorageDetail search = new IaStorageDetail().setIaStorageId(iaStorage.getId());
        List<IaStorageDetail> detailListDB = iaStorageDetailMapper.selectIaStorageDetailList(search);
        List<Long> ids = detailList.stream().map(IaStorageDetail::getId).collect(Collectors.toList());
        List<Long> idsDB = detailListDB.stream().map(IaStorageDetail::getId).collect(Collectors.toList());
        //子表新增 详情id为空的
        List<IaStorageDetail> saveList = detailList.stream()
                .filter(en -> ObjectUtil.isEmpty(en.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(saveList)) {
            //组装存货信息
            assemblyInventory(iaStorage, saveList);
            //保存
            mybatisBatchUtils.batchUpdateOrInsert(saveList, IaStorageDetailMapper.class,
                    (detail, storageDetailMapper) -> storageDetailMapper.insertIaStorageDetail(detail));
        }
        //子表更新
        List<IaStorageDetail> updateList = detailList.stream()
                .filter(en -> idsDB.contains(en.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateList)) {
            //组装存货信息
            assemblyInventory(iaStorage, updateList);
            //更新
            mybatisBatchUtils.batchUpdateOrInsert(updateList, IaStorageDetailMapper.class,
                    (detail, storageDetailMapper) -> storageDetailMapper.updateIaStorageDetail(detail));
        }
        //子表删除 从detailList过滤出detailListDB不包含的，排除为空的
        List<IaStorageDetail> deleteList = detailListDB.stream()
                .filter(en -> !ids.contains(en.getId()))
                .collect(Collectors.toList());
        //更新仓库库存 -> 更新明细
        /**
         * 更新明细分为：
         * 1.新增明细：在现有的明细基础上增加
         *    （1：入库新增，没有此库存-> 库存新增，有此库存->基础上累加
         *    （2：出库新增，有此库存  -> 基础上累减
         * 2.更新明细：在原基础的明细上做修改，则说明库存数据已经入库，则需要更新库存
         *    （1：入库更新的明细, 有此库存-> 根据两次修改的记录 在库存上加或减
         *    （2：出库更新的明细, 有此库存-> 根据两次修改的记录 在库存上加或减
         * 3.删除明细：在现有的明细基础上删除，删除说明已经存进去了，则需要更新库存
         *    （1：入库删除的明细, 有此库存-> 基础上累减
         *    （2：出库删除的明细, 有此库存-> 基础上累加
         */
        RLock rLock = redissonClient.getLock(RedissonConstant.INVENTORY_LOCK_KEY);
        try {
            boolean isLocked = rLock.tryLock(10, 30, TimeUnit.SECONDS);
            if (isLocked) {
                //处理库存-新增
                inHandleSaveList(iaStorage, saveList);
                //处理库存-更新
                inHandleUpdateList(iaStorage, detailListDB, updateList);
                //处理库存-删除
                inHandleDeleteList(iaStorage, deleteList);
            } else {
                throw new ServiceException("更新：获取redisson锁失败", 400);
            }
        } catch (ServiceException e) {
            log.error("更新：更新库存失败", e);
            throw new ServiceException(e.getMessage(), 400);
        } catch (Exception e) {
            log.error("更新：更新库存失败", e);
            throw new ServiceException("更新：更新库存失败", 400);
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        //删除
        List<Long> deleteIds = deleteList.stream().map(IaStorageDetail::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deleteIds)) {
            iaStorageDetailMapper.deleteIaStorageDetailByIds(deleteIds);
        }
        return true;
    }

    private void updateMainInformation(IaStorage iaStorage) {
        //放入制单人
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser)) {
            if (ObjectUtil.isNotEmpty(loginUser.getSysUser())) {
                iaStorage.setCreatorId(loginUser.getSysUser().getUserId());
                iaStorage.setCreatorName(loginUser.getSysUser().getNickName());
            }
        }

        //期初入库更新
        if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())) {
            iaConfig.setEnToBegin(iaStorage);
        }
        //采购入库更新
        if (IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())
                || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType()) ) {
            iaConfig.getIaCommunication(iaStorage);
        }
        //领用出库更新
        if (IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getCode().equals(iaStorage.getBusType())) {
            iaConfig.setEnToRequisition(iaStorage);
        }
        //销售出库更新
        if (IaStorageBusTypeEnum.SALES_OUTBOUND.getCode().equals(iaStorage.getBusType())
                || IaStorageBusTypeEnum.SALES_RETURNS.getCode().equals(iaStorage.getBusType())) {
            iaConfig.handleSalesEn(iaStorage);
        }
    }

    /**
     * 删除明细：在现有的明细基础上删除，删除说明已经存进去了，则需要更新库存
     * （1：入库删除的明细, 有此库存-> 基础上累减
     * （2：出库删除的明细, 有此库存-> 基础上累加
     *
     * @param iaStorage
     * @param deleteList
     */
    private void inHandleDeleteList(IaStorage iaStorage, List<IaStorageDetail> deleteList) {
        if (CollUtil.isNotEmpty(deleteList)) {
            List<String> inWareIndexList = deleteList.stream()
                    .map(en -> iaStorage.getWarehouseId() + "_" + en.getInventoryId())
                    .collect(Collectors.toList());
            //查询
            IaInventoryWarehouse searchEn = new IaInventoryWarehouse()
                    .setCompanyId(iaStorage.getCompanyId()).setInWareIndexList(inWareIndexList);
            List<IaInventoryWarehouse> list = iaInventoryWarehouseMapper.selectIaInventoryWarehouseList(searchEn);
            for (IaInventoryWarehouse ia : list) {
                //要删除的明细
                IaStorageDetail detail = deleteList.stream()
                        .filter(en -> en.getInventoryId().equals(ia.getInventoryId()))
                        .findAny().orElse(null);
                assert detail != null;
                if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                        || IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())
                        || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType())) {
                    //基础上累减
                    ia.setInQty(ia.getInQty().subtract(detail.getQuantity()));
                } else {
                    //基础上累加
                    ia.setInQty(ia.getInQty().add(detail.getQuantity()));
                }
            }
            //更新库存
            mybatisBatchUtils.batchUpdateOrInsert(list, IaInventoryWarehouseMapper.class,
                    (entry, inventoryWarehouseMapper) -> inventoryWarehouseMapper.updateIaInventoryWarehouse(entry));
        }
    }

    /**
     * 更新明细：在原基础的明细上做修改，则说明库存数据已经入库，则需要更新库存
     * （1：入库更新的明细, 有此库存-> 根据两次修改的记录 在库存上加或减
     * （2：出库更新的明细, 有此库存-> 根据两次修改的记录 在库存上加或减
     *
     * @param iaStorage
     * @param detailListDB
     * @param updateList
     */
    private void inHandleUpdateList(IaStorage iaStorage, List<IaStorageDetail> detailListDB, List<IaStorageDetail> updateList) {
        if (CollUtil.isNotEmpty(updateList)) {
            List<String> inWareIndexList = updateList.stream()
                    .map(en -> iaStorage.getWarehouseId() + "_" + en.getInventoryId())
                    .collect(Collectors.toList());
            //查询
            IaInventoryWarehouse searchEn = new IaInventoryWarehouse()
                    .setCompanyId(iaStorage.getCompanyId()).setInWareIndexList(inWareIndexList);
            List<IaInventoryWarehouse> list = iaInventoryWarehouseMapper.selectIaInventoryWarehouseList(searchEn);
            //对比两次数据差值
            for (IaInventoryWarehouse ia : list) {
                //寻找源数据
                IaStorageDetail source = detailListDB.stream()
                        .filter(en -> en.getInventoryId().equals(ia.getInventoryId()))
                        .findAny().orElse(null);
                //找现数据
                IaStorageDetail target = updateList.stream()
                        .filter(en -> en.getInventoryId().equals(ia.getInventoryId()))
                        .findAny().orElse(null);
                //计算差值
                BigDecimal subtract = target.getQuantity().subtract(source.getQuantity());
                if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                        || IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())
                        || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType())) {
                    //新库存加上差值
                    if (ia.getInQty().add(subtract).compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("修改后结存数量不得小于0！");
                    }
                    ia.setInQty(ia.getInQty().add(subtract));
                } else {
                    if (ia.getInQty().subtract(subtract).compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("修改后结存数量不得小于0！");
                    }
                    ia.setInQty(ia.getInQty().subtract(subtract));
                }


            }
            //更新库存
            mybatisBatchUtils.batchUpdateOrInsert(list, IaInventoryWarehouseMapper.class,
                    (entry, inventoryWarehouseMapper) -> inventoryWarehouseMapper.updateIaInventoryWarehouse(entry));
        }
    }

    /**
     * 更新仓库库存 -- 新增明细
     * 新增的数据：分为两种，
     * 1：入库新增，没有此库存->库存新增，有此库存->基础上累加
     * 2：出库新增，有此库存->基础上累减
     */
    private void inHandleSaveList(IaStorage iaStorage, List<IaStorageDetail> saveDetailList) {
        if (CollUtil.isNotEmpty(saveDetailList)) {
            List<String> inWareIndexList = saveDetailList.stream()
                    .map(en -> iaStorage.getWarehouseId() + "_" + en.getInventoryId())
                    .collect(Collectors.toList());
            //查询
            IaInventoryWarehouse search = new IaInventoryWarehouse()
                    .setCompanyId(iaStorage.getCompanyId()).setInWareIndexList(inWareIndexList);
            List<IaInventoryWarehouse> list = iaInventoryWarehouseMapper.selectIaInventoryWarehouseList(search);
            if (CollUtil.isEmpty(list)) {
                //增加库存数据
                saveInList(iaStorage, saveDetailList);
            } else {
                List<Long> inIds = list.stream().map(IaInventoryWarehouse::getInventoryId).collect(Collectors.toList());
                if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                        || IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())) {
                    //入库没有的新增
                    List<IaStorageDetail> saveList = saveDetailList.stream()
                            .filter(en -> !inIds.contains(en.getInventoryId()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(saveList)) {
                        //保存库存
                        saveInList(iaStorage, saveList);
                    }
                }
                //存在的库存过滤出则更新
                List<IaStorageDetail> updateList = saveDetailList.stream()
                        .filter(en -> inIds.contains(en.getInventoryId()))
                        .collect(Collectors.toList());
                List<IaInventoryWarehouse> updateDataList = updateList.stream()
                        .map(en -> {
                            IaInventoryWarehouse iaInventoryWarehouse = new IaInventoryWarehouse();
                            IaInventoryWarehouse orElse = list.stream()
                                    .filter(ia -> ia.getInventoryId().equals(en.getInventoryId()))
                                    .findAny().orElse(null);
                            BeanUtils.copyProperties(orElse, iaInventoryWarehouse);
                            //入库相加，出库相减
                            if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                                    || IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(iaStorage.getBusType())
                                    || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(iaStorage.getBusType())) {
                                iaInventoryWarehouse.setInQty(orElse.getInQty().add(en.getQuantity()));
                            } else {
                                iaInventoryWarehouse.setInQty(orElse.getInQty().subtract(en.getQuantity()));
                            }
                            return iaInventoryWarehouse;
                        }).collect(Collectors.toList());
                mybatisBatchUtils.batchUpdateOrInsert(updateDataList, IaInventoryWarehouseMapper.class,
                        (entry, inventoryWarehouseMapper) -> inventoryWarehouseMapper.updateIaInventoryWarehouse(entry));
            }
        }
    }

    //保存库存
    private void saveInList(IaStorage iaStorage, List<IaStorageDetail> saveList) {
        List<IaInventoryWarehouse> saveDataList = saveList.stream()
                .map(en -> new IaInventoryWarehouse()
                        .setCompanyId(iaStorage.getCompanyId())
                        .setWarehouseId(iaStorage.getWarehouseId())
                        .setInventoryId(en.getInventoryId())
                        .setInQty(en.getQuantity())
                        .setInventoryWarehouseIndex(iaStorage.getWarehouseId() + "_" + en.getInventoryId()))
                .collect(Collectors.toList());
        mybatisBatchUtils.batchUpdateOrInsert(saveDataList, IaInventoryWarehouseMapper.class,
                (entry, inventoryWarehouseMapper) -> inventoryWarehouseMapper.insertIaInventoryWarehouse(entry));
    }

    @Transactional
    @Override
    public boolean delete(IaStorage iaStorage) {
        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(iaStorage.getCompanyId());
        verifyCheckout(iaStorage.getCreateTime(), iaCompany);
        //检验期初
        if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                && !iaCompany.getStartPeriod().equals(iaCompany.getCurrentPeriod())) {
            throw new ServiceException("当前期间不是开始期间，期初不允许修改！", 400);
        }
        iaStorageDetailMapper.deleteIaStorageDetailByIaStorageId(iaStorage.getId());
        iaStorageMapper.deleteIaStorageById(iaStorage.getId());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteDetail(IaStorage iaStorage) {
        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(iaStorage.getCompanyId());
        //检验期初
        if (IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType())
                && !iaCompany.getStartPeriod().equals(iaCompany.getCurrentPeriod())) {
            throw new ServiceException("当前期间不是开始期间，期初不允许修改！", 400);
        }
        //查询全部信息
        List<IaStorageDetail> sourceList = iaStorageDetailMapper.selectByIds(iaStorage.getIds());
        Map<Long, List<IaStorageDetail>> sourceGroup = sourceList.stream().collect(Collectors.groupingBy(IaStorageDetail::getIaStorageId));
        //找到父id
        List<Long> iaStorageIds = sourceList.stream().map(IaStorageDetail::getIaStorageId).collect(Collectors.toList());
        //根据父id查询完完整的所有明细用来做对比
        IaStorageDetail search = new IaStorageDetail().setIaStorageIds(iaStorageIds);
        List<IaStorageDetail> detailList = iaStorageDetailMapper.selectIaStorageDetailList(search);
        Map<Long, List<IaStorageDetail>> detailGroup = detailList.stream().collect(Collectors.groupingBy(IaStorageDetail::getIaStorageId));
        //判断父级是否删除
        List<Long> parentIds = new ArrayList<>();
        for (Map.Entry<Long, List<IaStorageDetail>> entry : sourceGroup.entrySet()) {
            for (Map.Entry<Long, List<IaStorageDetail>> listEntry : detailGroup.entrySet()) {
                //若父id，一样，判断子集合的大小是否一样
                if (entry.getKey().equals(listEntry.getKey())) {
                    if (entry.getValue().size() == listEntry.getValue().size()) {
                        //若相同则说明父级下的子集都被删除，则不保留父级
                        parentIds.add(entry.getKey());
                    }
                }
            }
        }
        //查询父
        IaStorage searchEn = new IaStorage().setCompanyId(iaStorage.getCompanyId()).setIds(iaStorageIds);
        List<IaStorage> iaStorageList = iaStorageMapper.selectIaStorageList(searchEn);
        //删除校验
        deleteVerification(iaStorage, iaStorageList);
        //检验采购库存（只删除采购入库时校验
        //查询父级
        IaStorage storageSearch = new IaStorage().setIds(iaStorageIds);
        List<IaStorage> storageList = iaStorageMapper.selectIaStorageList(storageSearch);
        for (IaStorage storage : storageList) {
            List<IaStorageDetail> details = sourceList.stream()
                    .filter(en -> en.getIaStorageId().equals(storage.getId()))
                    .collect(Collectors.toList());
            storage.setIaStorageDetails(details);
            //检验采购库存
            inventoryInspection(storage, InventoryConstants.DELETE_METHOD);
        }
        //删除子集
        iaStorageDetailMapper.deleteIaStorageDetailByIds(iaStorage.getIds());
        if (CollUtil.isNotEmpty(parentIds)) {
            iaStorageMapper.deleteIaStorageByIds(parentIds);
        }
        //同步库存
        RLock rLock = redissonClient.getLock(RedissonConstant.INVENTORY_LOCK_KEY);
        try {
            // 尝试获取锁，等待10秒，锁自动释放时间为30秒
            boolean isLocked = rLock.tryLock(10, 30, TimeUnit.SECONDS);
            if (isLocked) {
                //处理库存-删除
                for (IaStorage storage : iaStorageList) {
                    List<IaStorageDetail> collect = sourceList.stream()
                            .filter(en -> storage.getId().equals(en.getIaStorageId()))
                            .collect(Collectors.toList());
                    inHandleDeleteList(storage, collect);
                }
            } else {
                throw new ServiceException("删除：获取redisson锁失败", 400);
            }
        } catch (Exception e) {
            log.error("删除：更新库存失败", e);
            throw new ServiceException("删除：更新库存失败", 400);
        } finally {
            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return true;
    }

    private void deleteVerification(IaStorage iaStorage, List<IaStorage> iaStorageList) {
        //已存在出入库期初不允许删除
        boolean anyMatch = iaStorageList.stream()
                .anyMatch(en -> IaStorageBusTypeEnum.OPENING_RECEIPT.getCode().equals(iaStorage.getBusType()));
        if (anyMatch) {
            if (iaStorageMapper.selectIaStorageCountNotBegin() > 0) {
                throw new ServiceException("已存在出入库数据，期初不允许删除！", 400);
            }
        }
        //审核后不允许操作
        boolean match = iaStorageList.stream()
                .anyMatch(en -> InventoryConstants.APPROVED.equals(en.getReviewStatus()));
        if (match) {
            throw new ServiceException("存在已审核的单据不允许删除！", 400);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> importBegin(MultipartFile file, Long companyId) throws IOException {
        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(companyId);
        if (!iaCompany.getStartPeriod().equals(iaCompany.getCurrentPeriod())) {
            throw new ServiceException("当前不是期初无法操作！", 400);
        }
        Map<String, Object> hashMap = new HashMap<>();
        //读取excel
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        //校验模板
        List<Map<String, Object>> filter = verificationTemplate(reader);
        //校验仓库
        List<IaWarehouseArchives> warehouseArchives = verifyWarehouse(companyId, filter);
        //校验存货大类
        iaInventoryArchivesService.verifyCategory(filter, companyId);
        //校验没有物品的物品
        IaInventoryArchives inventoryArchives = new IaInventoryArchives().setCompanyId(companyId);
        List<IaInventoryArchives> inventoryList = iaInventoryArchivesMapper.selectIaInventoryArchivesList(inventoryArchives);
        //过滤出没有的返回
        List<Map<String, Object>> filterList = verifyItems(filter, inventoryList);
        if (CollUtil.isNotEmpty(filterList)) {
            String msg = filterList.stream()
                    .map(m -> MapUtil.getStr(m, "物品名称") + "-" + MapUtil.getStr(m, "规格", ""))
                    .collect(Collectors.joining(","));
            hashMap.put("warm", "没有物品：" + msg);
            return hashMap;
        }
        //校验单位 -> 没有创建
//        List<IaUnit> iaUnitList = verificationUnit(companyId, filter);
        //没有的存货新增
//        List<IaInventoryArchives> insertList = iaInventoryArchivesService.saveInventory(companyId, iaCategoryList, filterList, iaUnitList);
//        inventoryList.addAll(insertList);
        //构建期初单，根据仓库分类
        List<IaStorage> iaStorageList = assemblyDocuments(companyId, iaCompany, filter, warehouseArchives, inventoryList);
        //保存
        iaStorageList.forEach(this::save);
        return hashMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importPurchaseReceipt(MultipartFile file, Long companyId) throws IOException {
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(companyId);
        Map<String, Object> hashMap = new HashMap<>();
        //读取excel
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        //校验模板
        List<Map<String, Object>> filter = verificationTemplatePurchase(reader);
        //校验仓库
        List<IaWarehouseArchives> warehouseArchives = verifyWarehouse(companyId, filter);
        //校验存货大类
        iaInventoryArchivesService.verifyCategory(filter, companyId);
        //校验没有物品的物品
        IaInventoryArchives inventoryArchives = new IaInventoryArchives().setCompanyId(companyId);
        List<IaInventoryArchives> inventoryList = iaInventoryArchivesMapper.selectIaInventoryArchivesList(inventoryArchives);
        //过滤出没有的返回
        List<Map<String, Object>> filterList = verifyItems(filter, inventoryList);
        if (CollUtil.isNotEmpty(filterList)) {
            String msg = filterList.stream()
                    .map(m -> MapUtil.getStr(m, "物品名称") + "-" + MapUtil.getStr(m, "规格", ""))
                    .collect(Collectors.joining(","));
            hashMap.put("warm", "没有物品：" + msg);
            return hashMap;
        }
        //构建期初单，根据仓库分类
        List<IaStorage> iaStorageList = assemblyPurchaseDocuments(companyId, iaCompany, filter, warehouseArchives, inventoryList);
        //保存
        iaStorageList.forEach(this::save);
        return hashMap;
    }

    private List<IaStorage> assemblyPurchaseDocuments(Long companyId, IaCompany iaCompany,
                                                      List<Map<String, Object>> filter,
                                                      List<IaWarehouseArchives> warehouseArchives,
                                                      List<IaInventoryArchives> inventoryList) {
        Map<String, List<Map<String, Object>>> listMap = filter.stream()
                .collect(Collectors.groupingBy(m ->
                        MapUtil.getStr(m, "仓库名称") + "-" +
                                MapUtil.getStr(m, "往来单位") + "-" + MapUtil.getStr(m, "日期")));
        //校验日期，不能比当前结账日期小
        List<Integer> dateList = filter.stream().map(m -> {
            String date = MapUtil.getStr(m, "日期");
            return Integer.valueOf(date.substring(0, 10).replace("-", ""));
        }).collect(Collectors.toList());
        boolean match = dateList.stream().anyMatch(date -> date < Integer.parseInt(iaCompany.getCurrentPeriod() + "01"));
        if (match) {
            throw new ServiceException("单据日期不得小于当前期间", 400);
        }
        //查询往来单位
        List<String> commNameList = filter.stream().map(m -> MapUtil.getStr(m, "往来单位")).collect(Collectors.toList());
        List<IaCommunication> iaCommunications = iaCommunicationMapper.selectByCommNameList(commNameList);
        //循环组装单据
        List<IaStorage> iaStorageList = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : listMap.entrySet()) {
            //maxCode
            String createTime = MapUtil.getStr(entry.getValue().get(0), "日期").substring(0, 10);
            LocalDate localDate = DateUtil.parseLocalDateTime(createTime, DateFormatConstants.TIME_FORMAT_DAY).toLocalDate();
            IaStorage iaMaxCode = new IaStorage()
                    .setBusType(IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode())
                    .setCreateTime(localDate).setCompanyId(companyId);
            String maxCode = getMaxCode(iaMaxCode);
            assemblyIaStoragePurchase(companyId, warehouseArchives, inventoryList, localDate, maxCode, iaStorageList, iaCommunications, entry);
        }
        return iaStorageList;
    }

    private void assemblyIaStoragePurchase(Long companyId, List<IaWarehouseArchives> warehouseArchives,
                                           List<IaInventoryArchives> inventoryList, LocalDate localDate, String maxCode,
                                           List<IaStorage> iaStorageList, List<IaCommunication> iaCommunications,
                                           Map.Entry<String, List<Map<String, Object>>> entry) {
        Map<String, Object> map = entry.getValue().get(0);
        String warehouseName = MapUtil.getStr(map, "仓库名称");
        String commName = MapUtil.getStr(map, "往来单位");
        IaStorage iaStorage = new IaStorage()
                .setCompanyId(companyId)
                .setCreateTime(localDate)
                .setOrderCode(maxCode)
                .setBusType(IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode())
                .setSourceType(IaStorageSourceTypeEnum.MANUAL_IMPORT.getCode())
                .setCommunicationName(commName);
        //找到仓库
        IaWarehouseArchives orElse = warehouseArchives.stream()
                .filter(en -> en.getName().equals(warehouseName))
                .findAny().orElse(null);
        iaStorage.setWarehouseId(orElse.getId())
                .setWarehouseCode(orElse.getCode()).setWarehouseName(orElse.getName());
        //找到往来单位
        if (CollUtil.isNotEmpty(iaCommunications)) {
            IaCommunication iaCommunication = iaCommunications.stream()
                    .filter(en -> en.getCommName().equals(commName))
                    .findAny().orElse(null);
            if (ObjectUtil.isNotEmpty(iaCommunication)) {
                iaStorage.setCommunicationId(iaCommunication.getId())
                        .setCommunicationName(iaCommunication.getCommName());
            }
        }
        //处理明细
        List<IaStorageDetail> detailList = entry.getValue().stream()
                .map(en -> {
                    IaStorageDetail detail = new IaStorageDetail().setCompanyId(companyId)
                            .setQuantity(MapUtil.get(en, "数量", BigDecimal.class))
                            .setUnitPrice(MapUtil.get(en, "单价", BigDecimal.class))
                            .setAmount(MapUtil.get(en, "金额", BigDecimal.class))
                            .setRemark(MapUtil.getStr(en, "备注", ""));
                    //找寻存货
                    IaInventoryArchives inventory = inventoryList.stream()
                            .filter(in -> in.getName().equals(MapUtil.getStr(en, "物品名称")))
                            .findAny().orElse(null);
                    assert inventory != null;
                    detail.setInventoryId(inventory.getId());
                    return detail;
                }).collect(Collectors.toList());
        iaStorage.setIaStorageDetails(detailList);
        iaStorageList.add(iaStorage);
    }

    private List<Map<String, Object>> verificationTemplatePurchase(ExcelReader reader) {
        List<List<Object>> read = reader.read(0, 0);
        if (!"序号".equals(read.get(0).get(0)) || !"仓库名称".equals(read.get(0).get(1))
                || !"物品大类".equals(read.get(0).get(2)) || !"物品名称".equals(read.get(0).get(3))
                || !"规格".equals(read.get(0).get(4)) || !"单位".equals(read.get(0).get(5))
                || !"数量".equals(read.get(0).get(6)) || !"单价".equals(read.get(0).get(7))
                || !"金额".equals(read.get(0).get(8)) || !"备注".equals(read.get(0).get(9))
                || !"往来单位".equals(read.get(0).get(10)) || !"日期".equals(read.get(0).get(11))
        ) {
            throw new ServiceException("模板不正确，请检查模板！", 400);
        }
        List<Map<String, Object>> mapList = reader.read(0, 1, Integer.MAX_VALUE);
        if (ObjectUtil.isEmpty(mapList)) {
            throw new ServiceException("文件为空", 400);
        }
        //先过滤出金额不为0的和序号不为小计的数据
        return mapList.stream()
                .filter(m -> StrUtil.isNotEmpty(MapUtil.getStr(m, "金额"))
                        && !" -   \n".equals(MapUtil.getStr(m, "金额")))
                .filter(m -> !"小计".equals(MapUtil.getStr(m, "序号")))
                .collect(Collectors.toList());
    }

    private List<IaStorage> assemblyDocuments(Long companyId, IaCompany iaCompany, List<Map<String, Object>> filter,
                                              List<IaWarehouseArchives> warehouseArchives, List<IaInventoryArchives> inventoryList) {
        Map<String, List<Map<String, Object>>> listMap = filter.stream()
                .collect(Collectors.groupingBy(m -> MapUtil.getStr(m, "仓库名称")));
        //maxCode
        String createTime = String.valueOf(iaCompany.getStartPeriod()).substring(0, 4)
                + "-" + String.valueOf(iaCompany.getStartPeriod()).substring(4, 6) + "-01";
        LocalDate localDate = DateUtil.parseLocalDateTime(createTime, DateFormatConstants.TIME_FORMAT_DAY).toLocalDate();
        IaStorage iaMaxCode = new IaStorage()
                .setBusType(IaStorageBusTypeEnum.OPENING_RECEIPT.getCode())
                .setCreateTime(localDate).setCompanyId(companyId);
        String maxCode = getMaxCode(iaMaxCode);
        //循环组装单据
        List<IaStorage> iaStorageList = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : listMap.entrySet()) {
            assemblyIaStorage(companyId, warehouseArchives, inventoryList, localDate, maxCode, iaStorageList, entry);
            maxCode = IaUtils.getMaxCode(maxCode);
        }
        return iaStorageList;
    }

    private void assemblyIaStorage(Long companyId, List<IaWarehouseArchives> warehouseArchives,
                                   List<IaInventoryArchives> inventoryList, LocalDate localDate, String maxCode,
                                   List<IaStorage> iaStorageList, Map.Entry<String, List<Map<String, Object>>> entry) {
        IaStorage iaStorage = new IaStorage()
                .setCompanyId(companyId).setCreateTime(localDate).setOrderCode(maxCode)
                .setBusType(IaStorageBusTypeEnum.OPENING_RECEIPT.getCode())
                .setSourceType(IaStorageSourceTypeEnum.MANUAL_IMPORT.getCode());
        //找到仓库
        IaWarehouseArchives orElse = warehouseArchives.stream()
                .filter(en -> en.getName().equals(entry.getKey()))
                .findAny().orElse(null);
        iaStorage.setWarehouseId(orElse.getId())
                .setWarehouseCode(orElse.getCode()).setWarehouseName(orElse.getName());
        //处理明细
        List<IaStorageDetail> detailList = entry.getValue().stream()
                .map(en -> {
                    IaStorageDetail detail = new IaStorageDetail().setCompanyId(companyId)
                            .setQuantity(MapUtil.get(en, "月末盘点数量", BigDecimal.class))
                            .setUnitPrice(MapUtil.get(en, "单价", BigDecimal.class))
                            .setAmount(MapUtil.get(en, "月末盘点金额", BigDecimal.class))
                            .setRemark(MapUtil.getStr(en, "备注", "")
                                    + "-" + MapUtil.getStr(en, "存放地点", ""));
                    //找寻存货
                    IaInventoryArchives inventory = inventoryList.stream()
                            .filter(in -> in.getName().equals(MapUtil.getStr(en, "物品名称")))
                            .findAny().orElse(null);
                    assert inventory != null;
                    detail.setInventoryId(inventory.getId());
                    return detail;
                }).collect(Collectors.toList());
        iaStorage.setIaStorageDetails(detailList);
        iaStorageList.add(iaStorage);
    }

    private List<IaUnit> verificationUnit(Long companyId, List<Map<String, Object>> filter) {
        List<String> nameList = filter.stream()
                .map(m -> MapUtil.getStr(m, "单位"))
                .distinct()
                .collect(Collectors.toList());
        List<IaUnit> iaUnitList = iaUnitMapper.selectByNameList(nameList, companyId);
        iaInventoryArchivesService.verificationUnit(companyId, nameList, iaUnitList);
        return iaUnitList;
    }

    private List<Map<String, Object>> verificationTemplate(ExcelReader reader) {
        List<List<Object>> read = reader.read(0, 0);
        if (!"序号".equals(read.get(0).get(0)) || !"仓库名称".equals(read.get(0).get(1))
                || !"物品大类".equals(read.get(0).get(2)) || !"物品名称".equals(read.get(0).get(3))
                || !"规格".equals(read.get(0).get(4)) || !"单位".equals(read.get(0).get(5))
                || !"月末盘点数量".equals(read.get(0).get(6))
                || !"单价".equals(read.get(0).get(7)) || !"月末盘点金额".equals(read.get(0).get(8))
        ) {
            throw new ServiceException("模板不正确，请检查模板！", 400);
        }
        List<Map<String, Object>> mapList = reader.read(0, 1, Integer.MAX_VALUE);
        if (ObjectUtil.isEmpty(mapList)) {
            throw new ServiceException("文件为空", 400);
        }
        //先过滤出金额不为0的和序号不为小计的数据
        return mapList.stream()
                .filter(m -> StrUtil.isNotEmpty(MapUtil.getStr(m, "月末盘点金额"))
                        && !" -   \n".equals(MapUtil.getStr(m, "月末盘点金额")))
//                .filter(m -> MapUtil.get(m, "月末盘点金额", BigDecimal.class).compareTo(BigDecimal.ZERO) != 0)
                .filter(m -> !"小计".equals(MapUtil.getStr(m, "序号")))
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> verifyItems(List<Map<String, Object>> filter, List<IaInventoryArchives> inventoryList) {
        List<String> combinationNameList = inventoryList.stream()
                .map(archive -> archive.getName() + "-" + archive.getModel())
                .collect(Collectors.toList());
        //过滤出要保存的
        return filter.stream().filter(map
                        -> !combinationNameList.contains(
                MapUtil.getStr(map, "物品名称") + "-" + MapUtil.getStr(map, "规格", "")
                )
        ).collect(Collectors.toList());
    }

    @Override
    public List<IaWarehouseArchives> verifyWarehouse(Long companyId, List<Map<String, Object>> filter) {
        List<String> warehouseNames = filter.stream()
                .map(m -> MapUtil.getStr(m, "仓库名称"))
                .distinct()
                .collect(Collectors.toList());
        List<IaWarehouseArchives> archives = iaWarehouseArchivesMapper.selectByNameList(warehouseNames, companyId);
        if (CollUtil.isEmpty(archives)) {
            throw new ServiceException("仓库不存在，请创建！", 400);
        }
        for (String warehouseName : warehouseNames) {
            IaWarehouseArchives warehouseArchives = archives.stream()
                    .filter(en -> en.getName().equals(warehouseName))
                    .findAny().orElse(null);
            assert warehouseArchives != null;
            if (ObjectUtil.isEmpty(warehouseArchives)) {
                throw new ServiceException(warehouseName + "仓库不存在，请创建！", 400);
            }
        }
        return archives;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean auditing(IaStorage iaStorage) {
        IaStorage storage = iaStorageMapper.selectIaStorageById(iaStorage.getId());
        //结账不允许操作
        IaCompany iaCompany = iaCompanyMapper.selectIaCompanyById(storage.getCompanyId());
        verifyCheckout(storage.getCreateTime(), iaCompany);
        //出库校验 出库审核时候，出库单所对应的存货，这些存货在期初和采购入库单必须是已审核状态
        List<Long> iaStorageIdList = outboundVerification(storage);
        //先更新审核人和状态
        if (InventoryConstants.AUDITING.equals(iaStorage.getAuditType())) {
            storage.setReviewStatus(InventoryConstants.APPROVED);
            //发货管理更新为已发货
            if (ObjectUtil.isNotEmpty(storage.getErpOrderId())){
                iaStorageMapper.updateGiftStatus(storage.getErpOrderId(), InventoryConstants.SHIPPED);
            }
        } else {
            //TODO 发货管理更新为待发货
            storage.setReviewStatus(InventoryConstants.UNAUDITED);
            //更新单据里物品的成本为0
            List<IaStorageDetail> detailList = iaStorageDetailMapper.selectByIaStorageId(storage.getId());
            detailList.forEach(en -> {
                en.setCostUnitPrice(BigDecimal.ZERO);
                en.setCostAmount(BigDecimal.ZERO);
            });
            //更新出库单明细
            mybatisBatchUtils.batchUpdateOrInsert(detailList, IaStorageDetailMapper.class,
                    (detail, storageDetailMapper) -> storageDetailMapper.updateIaStorageDetail(detail));
        }
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser)) {
            if (ObjectUtil.isNotEmpty(loginUser.getSysUser())) {
                storage.setReviewerId(loginUser.getSysUser().getUserId());
                storage.setReviewerName(loginUser.getSysUser().getNickName());
            }
        }
        iaStorageMapper.updateIaStorage(storage);
        //同步发生额
        String time = DateUtil.format(storage.getCreateTime().atTime(LocalTime.now()), DateFormatConstants.TIME);
        iaOccurAmountService.synchronousAmount(time.substring(0, 6), storage.getBusType(), storage.getCompanyId());
        //实时计算成本
        CostSearch costSearch = new CostSearch()
                .setCompanyId(iaCompany.getId())
                .setDateTime(Integer.valueOf(time.substring(0, 6)));
        iaFinalService.calculateUnitCost(costSearch, iaStorageIdList);
        return true;
    }

    private List<Long> outboundVerification(IaStorage storage) {
        if (IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getCode().equals(storage.getBusType())
                || IaStorageBusTypeEnum.SALES_OUTBOUND.getCode().equals(storage.getBusType())
                || IaStorageBusTypeEnum.SALES_RETURNS.getCode().equals(storage.getBusType())) {
            //查询所有此存货的单据
            List<IaStorageDetail> detailList = iaStorageDetailMapper.selectByIaStorageId(storage.getId());
            List<Long> inventoryIds = detailList.stream().map(IaStorageDetail::getInventoryId).collect(Collectors.toList());
            //根据存货查询所有单据明细
            List<IaStorageDetail> details = iaStorageDetailMapper.selectByInventoryIdList(inventoryIds);
            List<Long> iaStorageIds = details.stream().map(IaStorageDetail::getIaStorageId).collect(Collectors.toList());
            //查询未审核的采购单或者期初单
            List<IaStorage> list = iaStorageMapper.selectOpeningOrPurchase(iaStorageIds, storage.getWarehouseId());
            if (CollUtil.isNotEmpty(list)) {
                String orderNumbers = list.stream().map(IaStorage::getOrderNumber).collect(Collectors.joining("，"));
                throw new ServiceException("期初或采购单存在未审核的单据，单据号：" + orderNumbers, 400);
            }
            //成本计算时用
            return iaStorageIds;
        }
        return new ArrayList<>();
    }

    @Override
    public List<IaStorageDetail> relatedOrders(IaStorage iaStorage) {
        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setVcOrderNumber(iaStorage.getErpOrderNumber());
        List<ErpOrders> ordersList = erpOrdersService.selectErpOrdersList(erpOrders);
        if (CollUtil.isEmpty(ordersList)) {
            throw new ServiceException("未查询到订单，请重新输入！", 400);
        }
        if (ordersList.size() > 1) {
            throw new ServiceException("查询到多个订单，请重新输入！", 400);
        }
        ErpOrderDetailForOmVO orderDetailForOm = erpOrdersService.getOrderDetailForOm(ordersList.get(0).getId(), null,1, 0);
        //找到产品信息
        if (ObjectUtil.isEmpty(orderDetailForOm)) {
            throw new ServiceException("查询订单详情信息为空", 400);
        }
        if (CollUtil.isEmpty(orderDetailForOm.getProducts())) {
            throw new ServiceException("订单产品信息为空", 400);
        }
        //校验又没有录入过存货
        List<Long> productIds = orderDetailForOm.getProducts()
                .stream().map(ErpProductForOrderDetailVO::getProductId).collect(Collectors.toList());
        //查询存货
        IaInventoryArchives search = new IaInventoryArchives().setProductIds(productIds);
        List<IaInventoryArchives> archivesList = iaInventoryArchivesMapper.selectIaInventoryArchivesList(search);
        if (CollUtil.isEmpty(archivesList)) {
            String msg = productIds.stream().map(String::valueOf).collect(Collectors.joining("，"));
            throw new ServiceException("未查询到产品id为：”" + msg + "“相关的存货", 400);
        }
        //找到两集合差集以productIds为参照
        List<IaInventoryArchives> collect = archivesList.stream()
                .filter(en -> !productIds.contains(en.getProductId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            String msg = collect.stream()
                    .map(en -> String.valueOf(en.getProductId())).collect(Collectors.joining("，"));
            throw new ServiceException("未查询到产品id为：”" + msg + "“相关的存货", 400);
        }
        return orderDetailForOm.getProducts().stream()
                .map(en -> {
                    IaInventoryArchives inventoryArchives = archivesList.stream()
                            .filter(archives -> archives.getProductId().equals(en.getProductId()))
                            .findAny().orElse(null);
                    return new IaStorageDetail()
                            .setCompanyId(inventoryArchives.getCompanyId())
                            .setInventoryId(inventoryArchives.getId())
                            .setInventoryCode(inventoryArchives.getCode())
                            .setInventoryName(inventoryArchives.getName())
                            .setModel(inventoryArchives.getModel())
                            .setIaUnitName(inventoryArchives.getIaUnitName())
                            .setQuantity(new BigDecimal(en.getProductCount()))
                            .setUnitPrice(en.getUnitPrice())
                            .setAmount(en.getTotalPrice());
                }).collect(Collectors.toList());
    }

    @Override
    public List<IaCommunication> querySuppliers() {
        return iaCommunicationMapper.selectIaCommunicationList(new IaCommunication());
    }

    @Override
    public List<Map<String, Object>> queryEnterprises(String name) {
        return iaCommunicationMapper.selectEnterprises(name);
    }

    @Override
    public List<ErpOrders> selectActualAmountByOrderIds(List<Long> orderIds) {
        return erpOrdersMapper.selectErpOrdersByIds(orderIds);
    }

    /**
     * 组装存货信息
     *
     * @param iaStorage
     * @param detailList
     */
    private void assemblyInventory(IaStorage iaStorage, List<IaStorageDetail> detailList) {
        //先获取存货id
        List<Long> inventoryIds = detailList.stream()
                .map(IaStorageDetail::getInventoryId)
                .collect(Collectors.toList());
        List<IaInventoryArchives> archivesList = iaInventoryArchivesMapper.selectIaInventoryArchivesByIds(inventoryIds);
        //处理存货信息
        for (IaStorageDetail detail : detailList) {
            for (IaInventoryArchives iaInventory : archivesList) {
                if (detail.getInventoryId().equals(iaInventory.getId())) {
                    detail.setInventoryCode(iaInventory.getCode()).setInventoryName(iaInventory.getName());
                }
            }
            detail.setIaStorageId(iaStorage.getId());
        }
    }

    /**
     * @param createTime
     * @param iaCompany
     */
    private void verifyCheckout(LocalDate createTime, IaCompany iaCompany) {
        String format = DateUtil.format(createTime.atTime(LocalTime.now()), DateFormatConstants.TIME_FORMAT_DAY);
        int date = Integer.parseInt(format.substring(0, 4) + format.substring(5, 7));
        if (date < iaCompany.getCurrentPeriod()) {
            throw new ServiceException("当前期间已结账无法操作！", 400);
        }
    }

    @Autowired
    private void setErpOrdersMapper(ErpOrdersMapper erpOrdersMapper){
        this.erpOrdersMapper = erpOrdersMapper;
    }
}
