package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizSupplierLog;

/**
 * 修改日志Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface IErpBizSupplierLogService 
{
    /**
     * 查询修改日志
     * 
     * @param numId 修改日志主键
     * @return 修改日志
     */
    public ErpBizSupplierLog selectErpBizSupplierLogByNumId(Long numId);

    /**
     * 查询修改日志列表
     * 
     * @param erpBizSupplierLog 修改日志
     * @return 修改日志集合
     */
    public List<ErpBizSupplierLog> selectErpBizSupplierLogList(ErpBizSupplierLog erpBizSupplierLog);

    /**
     * 新增修改日志
     * 
     * @param erpBizSupplierLog 修改日志
     * @return 结果
     */
    public int insertErpBizSupplierLog(ErpBizSupplierLog erpBizSupplierLog);

    /**
     * 修改修改日志
     * 
     * @param erpBizSupplierLog 修改日志
     * @return 结果
     */
    public int updateErpBizSupplierLog(ErpBizSupplierLog erpBizSupplierLog);

    /**
     * 批量删除修改日志
     * 
     * @param numIds 需要删除的修改日志主键集合
     * @return 结果
     */
    public int deleteErpBizSupplierLogByNumIds(Long[] numIds);

    /**
     * 删除修改日志信息
     * 
     * @param numId 修改日志主键
     * @return 结果
     */
    public int deleteErpBizSupplierLogByNumId(Long numId);
}
