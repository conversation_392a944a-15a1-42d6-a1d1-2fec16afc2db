package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.CommitOrderConstants;
import com.nnb.erp.constant.DateFormatConstants;
import com.nnb.erp.constant.OperatingConstants;
import com.nnb.erp.constant.ServiceMainConstants;
import com.nnb.erp.constant.enums.*;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.gift.ErpOrderGift;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.gift.ErpOrderGiftMapper;
import com.nnb.erp.service.*;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 尾款回款Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Service
public class ErpRetainageReturnServiceImpl implements IErpRetainageReturnService {
    @Resource
    private ErpRetainageReturnMapper erpRetainageReturnMapper;
    @Resource
    private ErpServiceOrdersMapper erpServiceOrdersMapper;
    @Resource
    private ErpRetainageReturnDetailMapper erpRetainageReturnDetailMapper;
    @Resource
    private ErpCommitOrderMapper erpCommitOrderMapper;

    @Resource
    private IErpOrdersService erpOrdersService;
    @Resource
    private IErpOrderPaymentTermService termService;
    @Resource
    private IErpOrderPaymentTermInfoService termInfoService;
    @Resource
    private IErpRetainageReturnDetailService erpRetainageReturnDetailService;
    @Resource
    private TokenService tokenService;
    @Resource
    private IErpOrderOperatingRecordService erpOrderOperatingRecordService;
    @Resource
    private SServiceMainMapper sServiceMainMapper;
    @Resource
    private ErpOrdersMapper erpOrdersMapper;
    @Resource
    private ErpOrderPaymentTermInfoMapper erpOrderPaymentTermInfoMapper;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private IErpBizMainInfoService iErpBizMainInfoService;

    @Autowired
    private DingDingService dingDingService;
    @Autowired
    private ErpIncomeDetailsServiceImpl erpIncomeDetailsService;

    @Autowired
    private SConfigServicePointMapper sConfigServicePointMapper;

    @Autowired
    private ErpOrderGiftMapper erpOrderGiftMapper;
    @Autowired
    private ErpCommitOrderServiceImpl commitOrderService;

    @Autowired
    private ErpTransactionVoucherFollowServiceImpl transactionVoucherFollowService;

    @Value("${erp.order.transactionVoucher.orderCreateTime}")
    public String voucherOrderCreateTime;

    @Autowired
    private ErpOrderPerformanceMapper erpOrderPerformanceMapper;

    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;
    @Autowired
    private ErpTransactionVoucherFollowMapper erpTransactionVoucherFollowMapper;
    @Autowired
    private ErpAccountVisitMapper accountVisitMapper;
    @Resource
    private IErpServiceOrdersService erpServiceOrdersService;

    /**
     * 查询尾款回款
     *
     * @param id 尾款回款主键
     * @return 尾款回款
     */
    @Override
    public ErpRetainageReturn selectErpRetainageReturnById(Long id) {
        return erpRetainageReturnMapper.selectErpRetainageReturnById(id);
    }

    /**
     * 查询尾款回款列表
     *
     * @param erpRetainageReturn 尾款回款
     * @return 尾款回款
     */
    @Override
    public List<ErpRetainageReturn> selectErpRetainageReturnList(ErpRetainageReturn erpRetainageReturn) {
        return erpRetainageReturnMapper.selectErpRetainageReturnList(erpRetainageReturn);
    }

    /**
     * 新增尾款回款
     *
     * @param erpRetainageReturn 尾款回款
     * @return 结果
     */
    @Override
    public int insertErpRetainageReturn(ErpRetainageReturn erpRetainageReturn) {
        return erpRetainageReturnMapper.insertErpRetainageReturn(erpRetainageReturn);
    }

    /**
     * 修改尾款回款
     *
     * @param erpRetainageReturn 尾款回款
     * @return 结果
     */
    @Override
    public int updateErpRetainageReturn(ErpRetainageReturn erpRetainageReturn) {
        return erpRetainageReturnMapper.updateErpRetainageReturn(erpRetainageReturn);
    }

    /**
     * 批量删除尾款回款
     *
     * @param ids 需要删除的尾款回款主键
     * @return 结果
     */
    @Override
    public int deleteErpRetainageReturnByIds(Long[] ids) {
        return erpRetainageReturnMapper.deleteErpRetainageReturnByIds(ids);
    }

    /**
     * 删除尾款回款信息
     *
     * @param id 尾款回款主键
     * @return 结果
     */
    @Override
    public int deleteErpRetainageReturnById(Long id) {
        return erpRetainageReturnMapper.deleteErpRetainageReturnById(id);
    }

    /**
     * 保存尾款回款。
     *
     * @param erpParamForRetainageReturnDTO 待保存尾款回款信息。
     * @return 是否保存完成。
     * <AUTHOR>
     * @since 2022-04-02 09:21:36
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRetainageReturn(ErpParamForRetainageReturnDTO erpParamForRetainageReturnDTO) {
        // 校验信息是否完善。
        if (ObjectUtil.isEmpty(erpParamForRetainageReturnDTO)) {
            throw new ServiceException("未接收到请求参数");
        }

            if (ObjectUtil.hasEmpty(
                    erpParamForRetainageReturnDTO.getOrderId(),
                    erpParamForRetainageReturnDTO.getPayments(),
                    erpParamForRetainageReturnDTO.getRetainageReturns()
            )) {
                throw new ServiceException("参数缺失");
            }

        Long orderId = erpParamForRetainageReturnDTO.getOrderId();

        // 校验订单是否可以进行回款操作。
        erpOrdersService.isOrderToOperation(orderId, 3);

        // 获取订单内各服务单信息，用于校验是否可以进行回款操作。
        List<ErpServiceOrderForRetainageReturnVO> dbServiceOrders = erpServiceOrdersMapper.getServiceOrdersForRetainageReturn(orderId);

        // 服务单剩余尾款 ≥ 传参指定服务单回款金额。
        dbServiceOrders.forEach(serviceOrder -> {
            ErpRetainageReturnForRetainageReturnDTO erpRetainageReturnForRetainageReturnDTO = erpParamForRetainageReturnDTO.getRetainageReturns().stream()
                    .filter(retainageReturn -> retainageReturn.getServiceOrderId().equals(serviceOrder.getServiceOrderId())).collect(Collectors.toList()).get(0);

            if (!serviceOrder.getIsDeprecated().equals(0) &&
                    (erpRetainageReturnForRetainageReturnDTO.getCollectionPrice().compareTo(new BigDecimal("0")) > 0
                            && erpRetainageReturnForRetainageReturnDTO.getDiscounts().compareTo(new BigDecimal("0")) > 0)) {
                throw new ServiceException("产品已作废，不可参与回款");
            }
            if (serviceOrder.getLastPrice().compareTo(erpRetainageReturnForRetainageReturnDTO.getCollectionPrice().add(erpRetainageReturnForRetainageReturnDTO.getDiscounts())) < 0) {
                throw new ServiceException("产品尾款 < ( 回款金额 + 回款优惠 )");
            }
        });
        // 收款信息总金额 = 传参服务单总回款金额。
        // 统计传参服务单内的总回款金额。
        BigDecimal summaryReturnPrice = erpParamForRetainageReturnDTO.getRetainageReturns().stream().filter(e -> ObjectUtil.isNotNull(e.getCollectionPrice()) && e.getCollectionPrice().compareTo(new BigDecimal("0")) > 0).map(ErpRetainageReturnForRetainageReturnDTO::getCollectionPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 统计传参服务单内的总尾款优惠金额。
        BigDecimal summaryDiscountPrice = erpParamForRetainageReturnDTO.getRetainageReturns().stream().filter(e -> ObjectUtil.isNotNull(e.getCollectionPrice()) && e.getCollectionPrice().compareTo(new BigDecimal("0")) > 0).map(ErpRetainageReturnForRetainageReturnDTO::getDiscounts).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 统计传参收款信息内的总收款金额。
        BigDecimal summaryCollectionPrice = new BigDecimal("0");
            for (ErpRetainageReturnForRetainageReturnDTO payment : erpParamForRetainageReturnDTO.getRetainageReturns()) {
                summaryCollectionPrice = summaryCollectionPrice.add(payment.getCollectionPrice());
            }

        if (summaryReturnPrice.compareTo(summaryCollectionPrice) != 0) {
            throw new ServiceException("收款金额 = 回款金额");
        }

        // 获取当前时间，用以维护到数据库。
        Date nowDate = new Date();

        // 获取当前登录用户，用以维护到数据库。
        Long userId = tokenService.getLoginUser().getUserid();

        List<ErpOrderPerformance> performanceList = erpParamForRetainageReturnDTO.getPerformanceList();
//        erpOrderPerformanceMapper.updateStatusByOrderId(orderId,1L,3L);
        if (ObjectUtil.isNotEmpty(performanceList) && performanceList.size() > 0) {
            for (int i = 0; i < performanceList.size(); i++) {
                ErpOrderPerformance performance = performanceList.get(i);
                performance.setStatus(4);
                performance.setOrderId(orderId);
                erpOrderPerformanceMapper.insertErpOrderPerformance(performance);
            }
        }

        ErpOrders ordersDB = erpOrdersService.selectErpOrdersById(orderId);
        //凭证流水上线之后
        if (DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).before(ordersDB.getDatSigningDatecreatedTime())) {
            //查询此次回款的凭证在该订单中是否用过
            List<ErpTransactionVoucherFollow> voucherFollowList = erpTransactionVoucherFollowMapper.selectListByOrderId(orderId);
            JSONObject noReleaseObj = new JSONObject();
            for (int i = 0; i < voucherFollowList.size(); i++) {
                ErpTransactionVoucherFollow follow = voucherFollowList.get(i);
                if (follow.getStatus().intValue() != 2 && follow.getStatus().intValue() != 4) {
                    throw new ServiceException("交易流水有误1");
                }
                if (follow.getFee().compareTo(new BigDecimal("0")) == 0 || follow.getStatus().intValue() == 4) {
                    continue;
                }
                if (follow.getType() == 1) {
                    boolean release = false;
                    for (int j = 0; j < voucherFollowList.size(); j++) {
                        //有针对于消费记录的释放退回记录
                        if (ObjectUtil.isNotEmpty(voucherFollowList.get(j).getReleaseId()) &&
                                voucherFollowList.get(j).getReleaseId().equals(follow.getId()) &&
                                voucherFollowList.get(j).getStatus() != 4) {
                            release = true;
                            break;
                        }
                    }
                    if (!release) {
                        JSONObject obj = new JSONObject();
                        obj.put("fee", follow.getFee());
                        obj.put("followId", follow.getId());
                        noReleaseObj.put(follow.getTransactionVoucher().toString(), obj);
                    }
                }
            }
            for (int i = 0; i < erpParamForRetainageReturnDTO.getTransactionVoucherList().size(); i++) {
                if (noReleaseObj.containsKey(erpParamForRetainageReturnDTO.getTransactionVoucherList().get(i).getId().toString())) {
                    throw new ServiceException("所选凭证已在订单中使用，请去修改订单");
                }
            }



            //添加凭证
            List<ErpProductForCommitOrderDTO> productForConfirmOrderVOList = new ArrayList<>();
            for (int i = 0; i < erpParamForRetainageReturnDTO.getRetainageReturns().size(); i++) {
                ErpRetainageReturnForRetainageReturnDTO dto = erpParamForRetainageReturnDTO.getRetainageReturns().get(i);
                if (dto.getCollectionPrice().compareTo(new BigDecimal("0")) > 0) {
                    ErpProductForCommitOrderDTO product = new ErpProductForCommitOrderDTO();
                    product.setProductId(dto.getProductId());
                    product.setPayPrice(dto.getCollectionPrice());
                    productForConfirmOrderVOList.add(product);
                }
            }
            commitOrderService.commitOrderOperateTransactionVoucherFollow(
                    erpParamForRetainageReturnDTO.getOrderId(),
                    erpParamForRetainageReturnDTO.getTransactionVoucherList(),
                    productForConfirmOrderVOList,
                    4,
                    new BigDecimal("0")
            );

            JSONObject payObject = new JSONObject();
            List<Map<String, Object>> payList = erpTransactionVoucherFollowInfoMapper.selectFeeByPayTime(orderId);
            for (int i = 0; i < payList.size(); i++) {
                Map<String, Object> payMap = payList.get(i);
                String monthDate = payMap.get("monthDate").toString();
                String payTime = payMap.get("payTime").toString();
                String payProductId = payMap.get("productId").toString();
                String payProductFee = payMap.get("fee").toString();
                String followId = payMap.get("followId").toString();

                JSONObject payObjectMonth = new JSONObject();
                if (payObject.containsKey(monthDate)) {
                    payObjectMonth = payObject.getJSONObject(monthDate);
                }

                if (!payObjectMonth.containsKey("collectionTime") ||
                        DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,payTime).after(payObjectMonth.getDate("collectionTime"))) {
                    payObjectMonth.put("collectionTime", DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,payTime));
                }

                BigDecimal allFee = payObjectMonth.containsKey(payProductId) ? payObjectMonth.getBigDecimal(payProductId) : new BigDecimal("0");
                payObjectMonth.put(payProductId, allFee.add(new BigDecimal(payProductFee)));

                JSONArray followIdList = payObjectMonth.containsKey("followIdList") ? payObjectMonth.getJSONArray("followIdList") : new JSONArray();
                if (!followIdList.contains(followId)) {
                    followIdList.add(followId);
                }
                payObjectMonth.put("followIdList", followIdList);
                payObject.put(monthDate, payObjectMonth);
            }

            BigDecimal allFee = new BigDecimal("0");
            List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(orderId);
            List<Long> returnIdList = new ArrayList<>();
            Long payee = erpParamForRetainageReturnDTO.getPayments().get(0).getPayee();
            for (String key : payObject.keySet()) {
                JSONObject payObjectMonth = payObject.getJSONObject(key);
                BigDecimal summaryReturnPriceOne = new BigDecimal("0");
                for (int i = 0; i < serviceOrdersList.size(); i++) {
                    ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                    if (payObjectMonth.containsKey(serviceOrders.getNumProductId().toString())) {
                        summaryReturnPriceOne = summaryReturnPriceOne.add(payObjectMonth.getBigDecimal(serviceOrders.getNumProductId().toString()));
                    }
                }
                List<String> followIdList = new ArrayList<>();
                JSONArray followIdArr = payObjectMonth.getJSONArray("followIdList");
                for (int i = 0; i < followIdArr.size(); i++) {
                    if (!followIdList.contains(followIdArr.getString(i))) {
                        followIdList.add(followIdArr.getString(i));
                    }
                }
//                for (String key : payObjectMonth.keySet()) {
                allFee = allFee.add(summaryReturnPriceOne);
                // 维护尾款回款表。
                ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
                erpRetainageReturn.setNumOrderId(orderId);
                erpRetainageReturn.setNumCollectionPrice(summaryReturnPriceOne);
                erpRetainageReturn.setNumDiscounts(summaryDiscountPrice);
                erpRetainageReturn.setNumStatus(RetainageStatusEnum.AUDITING.getStatusType());
                erpRetainageReturn.setDatFinanceCollectionTime(payObjectMonth.getDate("collectionTime"));
                erpRetainageReturn.setNumCreatedBy(userId);
                erpRetainageReturn.setDatSigningDatecreatedTime(nowDate);
                erpRetainageReturn.setNumType(2L);
                erpRetainageReturn.setNumPayee(payee);
                erpRetainageReturn.setFollowIds(StringUtils.join(followIdList, ","));
                erpRetainageReturnMapper.insertErpRetainageReturn(erpRetainageReturn);
                returnIdList.add(erpRetainageReturn.getId());

                for (int i = 0; i < serviceOrdersList.size(); i++) {
                    ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                    if (payObjectMonth.containsKey(serviceOrders.getNumProductId().toString())) {
                        ErpRetainageReturnDetail erpRetainageReturnDetail = new ErpRetainageReturnDetail();
                        erpRetainageReturnDetail.setNumRetainageReturnId(erpRetainageReturn.getId());
                        erpRetainageReturnDetail.setNumServiceOrderId(serviceOrders.getId());
//                        erpRetainageReturnDetail.setNumDiscounts(erpRetainageReturn.getDiscounts());
                        erpRetainageReturnDetail.setNumDiscounts(new BigDecimal("0"));
                        erpRetainageReturnDetail.setNumCollectionPrice(payObjectMonth.getBigDecimal(serviceOrders.getNumProductId().toString()));
                        erpRetainageReturnDetail.setPayType(2);
                        erpRetainageReturnDetail.setCreateTime(new Date());
                        erpRetainageReturnDetail.setCreateUser(userId);

                        erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetail);

                        if (ObjectUtil.isNotEmpty(performanceList) && performanceList.size() > 0) {
                            for (int z = 0; z < performanceList.size(); z++) {
                                ErpOrderPerformance productPerformance = performanceList.get(z);
                                if (productPerformance.getProductId().intValue() == serviceOrders.getNumProductId().intValue()) {
//                                    erpOrderPerformanceMapper.deleteErpOrderPerformanceInfoByPerformanceId(productPerformance.getId());
                                    List<Map<String, Object>> followList = erpTransactionVoucherFollowMapper.selectMapByFollowIds(followIdList, serviceOrders.getNumProductId(), 1L);
                                    for (int x = 0; x < followList.size(); x++) {
                                        if (productPerformance.getFee().compareTo(new BigDecimal("0")) == 0) {
                                            continue;
                                        }
                                        Map<String, Object> followMap = followList.get(x);
                                        if (followMap.containsKey("voucherType") && ObjectUtil.isNotEmpty(followMap.get("voucherType"))
                                                && followMap.containsKey("fee") && ObjectUtil.isNotEmpty(followMap.get("fee"))
                                                && Integer.parseInt(followMap.get("voucherType").toString()) != 3) {
                                            BigDecimal followMapFee = new BigDecimal(followMap.get("fee").toString());

                                            ErpOrderPerformanceInfo erpOrderPerformanceInfo = new ErpOrderPerformanceInfo();
                                            erpOrderPerformanceInfo.setReturnDetailId(erpRetainageReturnDetail.getId());
                                            erpOrderPerformanceInfo.setPerformanceId(productPerformance.getId());
                                            erpOrderPerformanceInfo.setUserId(productPerformance.getUserId());
                                            if (followMapFee.compareTo(productPerformance.getFee()) >= 0) {
                                                erpOrderPerformanceInfo.setFee(productPerformance.getFee());
                                                productPerformance.setFee(new BigDecimal("0"));
                                            } else {
                                                erpOrderPerformanceInfo.setFee(followMapFee);
                                                productPerformance.setFee(productPerformance.getFee().subtract(followMapFee));
                                            }
                                            erpOrderPerformanceMapper.insertErpOrderPerformanceInfo(erpOrderPerformanceInfo);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // 维护收款方式表。
                ErpOrderPaymentTerm erpOrderPaymentTerm = new ErpOrderPaymentTerm();
                erpOrderPaymentTerm.setNumRetainageId(erpRetainageReturn.getId());
                erpOrderPaymentTerm.setNumPayee(payee);
//                erpOrderPaymentTerm.setNumOrderId(orderId);
                erpOrderPaymentTerm.setNumType(2);
//        erpOrderPaymentTerm.setDatCollectionTime(payObjectMonth.getDate("collectionTime"));
                erpOrderPaymentTerm.setNumCreatedBy(userId);
                erpOrderPaymentTerm.setDatSigningDatecreatedTime(nowDate);
                Long termId = termService.saveOrUpdate(erpOrderPaymentTerm);

                ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = new ErpOrderPaymentTermInfo();
                erpOrderPaymentTermInfo.setTermId(termId);
//                        erpOrderPaymentTermInfo.setNumPaymentType(Long.valueOf(term.getPaymentType()));
                erpOrderPaymentTermInfo.setNumMoney(summaryReturnPriceOne);
                erpOrderPaymentTermInfo.setNumStatus(1);
//                        erpOrderPaymentTermInfo.setVcPaymentUrl(term.getPaymentUrl());
                termInfoService.saveOrUpdate(erpOrderPaymentTermInfo);

                for (int i = 0; i < returnIdList.size(); i++) {
                    ErpRetainageReturn retainageReturn = erpRetainageReturnMapper.selectErpRetainageReturnById(returnIdList.get(i));
                    retainageReturn.setTogetherIds(StringUtils.join(returnIdList, ","));
                    erpRetainageReturnMapper.updateErpRetainageReturn(retainageReturn);
                }
            }

            erpServiceOrdersService.checkGiveWithServiceType(orderId);

        } else {
            Long payee = 0L;
            for (int i = 0; i < erpParamForRetainageReturnDTO.getPayments().size(); i++) {
                ErpPaymentForRetainageReturnDTO payment = erpParamForRetainageReturnDTO.getPayments().get(i);
                if (payee.intValue() == 0 && ObjectUtil.isNotEmpty(payment.getPayee())) {
                    payee = payment.getPayee();
                }
            }

            // 维护尾款回款表。
            ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
            erpRetainageReturn.setNumOrderId(orderId);
            erpRetainageReturn.setNumCollectionPrice(summaryReturnPrice);
            erpRetainageReturn.setNumDiscounts(summaryDiscountPrice);
            erpRetainageReturn.setNumStatus(RetainageStatusEnum.AUDITING.getStatusType());
            erpRetainageReturn.setNumCreatedBy(userId);
            erpRetainageReturn.setDatSigningDatecreatedTime(nowDate);
            erpRetainageReturn.setNumType(2L);
            erpRetainageReturn.setNumPayee(payee);
            this.insertErpRetainageReturn(erpRetainageReturn);

            // 维护尾款回款详情表。
            erpParamForRetainageReturnDTO.getRetainageReturns().forEach(retainage -> {
                if (ObjectUtil.isNotNull(retainage.getCollectionPrice()) && retainage.getCollectionPrice().compareTo(new BigDecimal("0")) > 0) {
                    ErpRetainageReturnDetail erpRetainageReturnDetail = new ErpRetainageReturnDetail();
                    erpRetainageReturnDetail.setNumRetainageReturnId(erpRetainageReturn.getId());
                    erpRetainageReturnDetail.setNumServiceOrderId(retainage.getServiceOrderId());
                    erpRetainageReturnDetail.setNumDiscounts(retainage.getDiscounts());
                    erpRetainageReturnDetail.setNumCollectionPrice(retainage.getCollectionPrice());
                    erpRetainageReturnDetail.setPayType(2);
                    erpRetainageReturnDetail.setCreateTime(new Date());
                    erpRetainageReturnDetail.setCreateUser(userId);
                    erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetail);
                }
            });

            erpParamForRetainageReturnDTO.getPayments().forEach(payment -> {
                // 维护收款方式表。
                ErpOrderPaymentTerm erpOrderPaymentTerm = new ErpOrderPaymentTerm();
                erpOrderPaymentTerm.setNumRetainageId(erpRetainageReturn.getId());
                erpOrderPaymentTerm.setNumPayee(payment.getPayee());
                erpOrderPaymentTerm.setNumType(2);
                erpOrderPaymentTerm.setDatCollectionTime(payment.getCollectionTime());
                erpOrderPaymentTerm.setNumCreatedBy(userId);
                erpOrderPaymentTerm.setDatSigningDatecreatedTime(nowDate);
                Long termId = termService.saveOrUpdate(erpOrderPaymentTerm);

                // 维护收款详情表。
                if (payment.getTerms()!=null) {
                    payment.getTerms().forEach(term -> {
                        ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = new ErpOrderPaymentTermInfo();
                        erpOrderPaymentTermInfo.setTermId(termId);
                        if (ObjectUtil.isNotEmpty(term.getPaymentType())) {
                            erpOrderPaymentTermInfo.setNumPaymentType(Long.valueOf(term.getPaymentType()));
                        }
                        erpOrderPaymentTermInfo.setNumMoney(term.getMoney());
                        erpOrderPaymentTermInfo.setVcPaymentUrl(term.getPaymentUrl());
                        termInfoService.saveOrUpdate(erpOrderPaymentTermInfo);
                    });
                }

            });
        }

        // 维护订单最新收款人信息及收款时间。
        erpCommitOrderMapper.updatePayeeForOrder(orderId);
        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_RETAINAGE_RETURN.getTypeInt());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);




        return Boolean.TRUE;
    }

    /**
     * 维护尾款信息。
     *
     * @param erpRetainageReturnExamineOperationDTO 审核操作。
     * <AUTHOR>
     * @since 2022-04-20 16:43:51
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void maintainRetainageReturn(ErpRetainageReturnExamineOperationDTO erpRetainageReturnExamineOperationDTO) {
        Long orderId = erpRetainageReturnExamineOperationDTO.getOrderId();
        Long returnId = erpRetainageReturnExamineOperationDTO.getReturnId();
        Integer examineOperation = erpRetainageReturnExamineOperationDTO.getExamineOperation();

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();


        // 构建尾款回款对象，用以维护数据库。
        ErpRetainageReturn retainageReturnSelect = erpRetainageReturnMapper.selectErpRetainageReturnById(returnId);
        List<Long> togetherIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(retainageReturnSelect.getTogetherIds())) {
            togetherIdList = Arrays.stream(retainageReturnSelect.getTogetherIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        } else {
            togetherIdList.add(returnId);
        }

        // 获取当前时间，用以维护到数据库。
        Date nowDate = new Date();
        // 获取当前操作用户标识，用以维护到数据库。
        Long userId = tokenService.getLoginUser().getUserid();
        for (int i = 0; i < togetherIdList.size(); i++) {

            // 获取订单信息。
            ErpOrderForOrderDetailVO orderBase = erpOrdersService.getOrderBase(orderId);
            // 获取回款信息。
            List<ErpRetainageReturnForRetainageReturnDTO> retainageReturns = erpRetainageReturnDetailMapper.getRetainagesById(orderId, togetherIdList.get(i));
            if (ObjectUtil.isEmpty(retainageReturns)) {
                throw new ServiceException("回款信息有误");
            }

//            ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
//            erpRetainageReturn.setId(togetherIdList.get(i));
            ErpRetainageReturn erpRetainageReturn = erpRetainageReturnMapper.selectErpRetainageReturnById(togetherIdList.get(i));

            String time = "";
            // 若审核通过，则维护数据到订单表内。
            if (examineOperation.equals(1)) {

                // 获取订单内serviceOrder信息。
                List<ErpServiceOrderForRetainageReturnVO> dbServiceOrders = erpServiceOrdersMapper.getServiceOrdersForRetainageReturn(orderId);

                // 统计传参服务单内的总回款金额。
                BigDecimal summaryReturnPrice = retainageReturns.stream().filter(e -> ObjectUtil.isNotNull(e.getCollectionPrice()) && e.getCollectionPrice().compareTo(new BigDecimal("0")) > 0).map(ErpRetainageReturnForRetainageReturnDTO::getCollectionPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 统计传参服务单内的总尾款优惠金额。
                BigDecimal summaryDiscountPrice = retainageReturns.stream().filter(e -> ObjectUtil.isNotNull(e.getCollectionPrice()) && e.getCollectionPrice().compareTo(new BigDecimal("0")) > 0).map(ErpRetainageReturnForRetainageReturnDTO::getDiscounts).reduce(BigDecimal.ZERO, BigDecimal::add);

                // 修改订单信息。
                ErpOrders erpOrders = new ErpOrders();
                erpOrders.setId(orderBase.getOrderId());
                erpOrders.setNumTotalPrice(orderBase.getTotalPrice().subtract(summaryDiscountPrice));
                erpOrders.setNumPayPrice(orderBase.getPayPrice().add(summaryReturnPrice));
                erpOrders.setNumLastPrice(orderBase.getLastPrice().subtract(summaryReturnPrice).subtract(summaryDiscountPrice));
                erpOrders.setNumDiscountAmount(orderBase.getDiscountAmount().add(summaryDiscountPrice));
                erpOrders.setNumRetainageStatus(erpOrders.getNumLastPrice().compareTo(new BigDecimal("0")) != 0 ? 1 : 2);
                erpOrders.setNumUpdatedBy(userId);
                erpOrders.setDatSigningDateupdatedTime(nowDate);
//            erpOrders.setDatFinanceCollectionTime(nowDate);

                if (
                        !checkPrice(erpOrders.getNumTotalPrice())
                                ||
                                !checkPrice(erpOrders.getNumPayPrice())
                                ||
                                !checkPrice(erpOrders.getNumLastPrice())
                                ||
                                !checkPrice(erpOrders.getNumDiscountAmount())
                ) {
                    throw new ServiceException("尾款金额计算异常！（1）");
                }

                erpOrdersService.updateErpOrders(erpOrders);


                ErpPaymentTermForCommitOrderDTO paymentTerm = erpRetainageReturnExamineOperationDTO.getPaymentTerm();

                ErpOrders ordersDB = erpOrdersService.selectErpOrdersById(orderId);
                if (DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).after(ordersDB.getDatSigningDatecreatedTime())) {
                    for (ErpPaymentTermInfoForCommitOrderDTO paymentTermInfo : paymentTerm.getPaymentTermInfos()) {
                        //状态为1（未废弃）
                        if (OperatingConstants.RETURN_NOT_ABANDONED.equals(paymentTermInfo.getNumStatus())){
                            List<ErpOrderPaymentTermInfo> erpOrderPaymentTermInfos = erpOrderPaymentTermInfoMapper.selectErpOrderPaymentTermInfoByTermId(paymentTermInfo.getPaymentId());
                            for (ErpOrderPaymentTermInfo orderPaymentTermInfo : erpOrderPaymentTermInfos) {
                                orderPaymentTermInfo.setFinanceTime(DateUtil.parse(paymentTermInfo.getFinanceTime()));
                                erpOrderPaymentTermInfoMapper.updateErpOrderPaymentTermInfo(orderPaymentTermInfo);
                            }
                            String financeTime = paymentTermInfo.getFinanceTime() + DateFormatConstants.TIME_BEGIN;
                            time = financeTime;
                            DateTime parse = DateUtil.parse(financeTime, DateFormatConstants.TIME_FORMAT);
                            erpRetainageReturn.setDatFinanceCollectionTime(parse);
                        }
                    }
                }
                erpRetainageReturn.setNumStatus(RetainageStatusEnum.PASS.getStatusType());

                Date visitDate_ = accountVisitMapper.selectVisitDateByOrderId(orderId);
                if (ObjectUtil.isNotEmpty(visitDate_)) {
                    Date visitDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.dateTime(visitDate_) + " 00:00:00");
                    List<Date> dateList = new ArrayList<>();
                    dateList.add(visitDate);
                    for (int j = 1; j < 5; j++) {
                        Date nextMonth = com.nnb.erp.util.DateUtil.getMonthsFirstDay(visitDate,j);
                        Date newtMonthDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.dateTime(nextMonth) + " 00:00:00");
                        dateList.add(newtMonthDate);
                    }
                    dateList.add(com.nnb.erp.util.DateUtil.plusMonths(visitDate, 4));
                    Date signDate = erpRetainageReturn.getDatFinanceCollectionTime();
                    for (int j = 0; j < dateList.size()-1; j++) {
                        if ((signDate.after(dateList.get(j)) || signDate.equals(dateList.get(j)))
                                && signDate.before(dateList.get(j+1))) {
                            erpRetainageReturn.setVisitAfterMonths(j+1);
                        }
                    }
                }

//            erpRetainageReturn.setDatFinanceCollectionTime(DateUtil.parse(erpRetainageReturnExamineOperationDTO.getFinanceTime()));

                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_RETAINAGE_RETURN_PASS_A.getTypeInt());

                // 修改订单内serviceOrder信息。
                for (ErpRetainageReturnForRetainageReturnDTO retainageReturn : retainageReturns) {
                    if (ObjectUtil.isNotNull(retainageReturn.getCollectionPrice()) && retainageReturn.getCollectionPrice().compareTo(new BigDecimal("0")) > 0) {
                        ErpServiceOrderForRetainageReturnVO dbServiceOrder = dbServiceOrders.stream().filter(e -> e.getServiceOrderId().equals(retainageReturn.getServiceOrderId())).collect(Collectors.toList()).get(0);
                        ErpServiceOrders serviceOrder = new ErpServiceOrders();
                        serviceOrder.setId(retainageReturn.getServiceOrderId());
                        serviceOrder.setNumTotalPrice(dbServiceOrder.getTotalPrice().subtract(retainageReturn.getDiscounts()));
                        serviceOrder.setNumPayPrice(dbServiceOrder.getPayPrice().add(retainageReturn.getCollectionPrice()));
                        serviceOrder.setNumLastPrice(dbServiceOrder.getLastPrice().subtract(retainageReturn.getCollectionPrice()).subtract(retainageReturn.getDiscounts()));
                        serviceOrder.setVcUpdatedBy(userId);
                        serviceOrder.setDatUpdatedTime(nowDate);
                        if (serviceOrder.getNumLastPrice().compareTo(BigDecimal.ZERO) == 0) {
                            serviceOrder.setLastPriceFinishTime(erpRetainageReturn.getDatFinanceCollectionTime());
                        }
                        if (
                                !checkPrice(serviceOrder.getNumTotalPrice())
                                        ||
                                        !checkPrice(serviceOrder.getNumPayPrice())
                                        ||
                                        !checkPrice(serviceOrder.getNumLastPrice())

                        ) {
                            throw new ServiceException("尾款金额计算异常！（2）");
                        }
                        erpServiceOrdersMapper.updateErpServiceOrders(serviceOrder);

                        //维护服务单
                        SServiceMain sServiceMain = new SServiceMain();
                        sServiceMain.setOrderFee(serviceOrder.getNumTotalPrice());
                        sServiceMain.setCollectionFee(serviceOrder.getNumPayPrice());
                        sServiceMain.setTailFee(serviceOrder.getNumLastPrice());
                        sServiceMain.setHasTailFee(serviceOrder.getNumLastPrice().compareTo(BigDecimal.ZERO) == 1 ? 1L : 0L);
                        sServiceMain.setOrderId(orderId);
                        sServiceMain.setProductId(dbServiceOrder.getNumProductId());
                        sServiceMainMapper.updateServiceMainByOrderIdAndProductId(sServiceMain);
                    }
                }
            } else {
                erpRetainageReturn.setNumStatus(RetainageStatusEnum.REJECTED.getStatusType());
                // 获取订单信息。
//                ErpOrderForOrderDetailVO orderBase = erpOrdersService.getOrderBase(orderId);
                erpRetainageReturn.setNumStatus(RetainageStatusEnum.REJECTED.getStatusType());
                for (ErpRetainageReturnForRetainageReturnDTO retainageReturn : retainageReturns) {
                    R<SysUser> info = remoteUserService.getUserInfoById(retainageReturn.getNumCreatedBy(), SecurityConstants.INNER);
                    if (info.getCode() == 200) {
                        SysUser sysUser = info.getData();
                        String email = info.getData().getEmail();
                        emailService.sendEmail(email, "财务回款审核驳回", " 您好:\r\n"
                                + "      订单号:" + orderBase.getOrderNumber() + "财务已驳回！\r\n"
                                + "      驳回原因:" + erpRetainageReturnExamineOperationDTO.getExamineRemark());
                        //发送钉钉消息
                        String dingContent = "### 财务回款审核驳回 \n * " + sysUser.getNickName() + "，您好： \n "
                                + " * 订单号:" + orderBase.getOrderNumber() + "财务已驳回！\n"
                                + " * 驳回原因:" + erpRetainageReturnExamineOperationDTO.getExamineRemark();
                        DingSendDTO dingSendDTO = new DingSendDTO(sysUser.getDingUserId(), "财务回款审核驳回", dingContent);
                        dingDingService.sendDingMessage(dingSendDTO);
                    }
                }

                //将收款详情作废掉
                erpRetainageReturnDetailMapper.cancleErpRetainageReturnStatusByRetainageReturnId(erpRetainageReturn.getId(), 0);

                // 维护订单最新收款人信息及收款时间。
                erpCommitOrderMapper.updatePayeeForOrder(orderId);

                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_RETAINAGE_RETURN_REJECTED_A.getTypeInt());

            }

            // 维护尾款回款审核记录。
            erpRetainageReturn.setNumUpdatedBy(userId);
            erpRetainageReturn.setDatSigningDateupdatedTime(nowDate);
            this.updateErpRetainageReturn(erpRetainageReturn);

            erpOrderOperatingRecord.setNumOrderId(orderId);
            erpOrderOperatingRecord.setVcOperationContent(erpRetainageReturnExamineOperationDTO.getExamineRemark());
            erpOrderOperatingRecord.setNumCreatedBy(userId);
            erpOrderOperatingRecord.setDatCreatedTime(nowDate);
            // 添加操作记录。
            erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

            if (examineOperation.equals(1)) {
                iErpBizMainInfoService.createErpBizMainInfoByOrderId(orderId);

//            Map<String, Object> map = new HashMap<>();
//            map.put("examineType", "2");
//            map.put("orderId", erpRetainageReturnExamineOperationDTO.getOrderId().toString());
//            map.put("returnId", erpRetainageReturnExamineOperationDTO.getReturnId().toString());
//            map.put("incomeDetailIds",erpRetainageReturnExamineOperationDTO.getIncomeDetailIds());
//            map.put("time", time);
//            erpIncomeDetailsService.examineOrderPass(map);
            }
        }

        if (examineOperation.equals(1)) {
            erpOrderPerformanceMapper.updateStatusByOrderId(orderId,4L,1L);
            transactionVoucherFollowService.updateTransactionVoucherFollowPass(orderId, 4);
        } else {
            erpOrderPerformanceMapper.updateStatusByOrderId(orderId,4L,2L);
            transactionVoucherFollowService.updateTransactionVoucherFollowReject(orderId, 4);
        }

    }


    /**
     * 更新赠品状态
     * @param erpOrders
     */
    private void updateOrderGiftStatus(ErpOrders erpOrders) {
        List<ErpProductForOrderDetailVO> productsForOrderDetail = erpOrdersMapper.getProductsForOrderDetail(erpOrders.getId());
        if (CollectionUtils.isNotEmpty(productsForOrderDetail)) {
            List<Long> serviceTypeIdList = productsForOrderDetail.stream().filter(item -> (Objects.nonNull(item.getIsDeprecated()) && 0 == item.getIsDeprecated())).collect(Collectors.toList()).stream().map(item -> item.getDetailServiceTypeId()).collect(Collectors.toList());
            List<Long> list = sConfigServicePointMapper.selectServiceTypeByPointIdList(ServiceMainConstants.GiftNotSendServicePointList);
            List<Long> collect = serviceTypeIdList.stream().filter(item -> list.contains(item)).collect(Collectors.toList());

            if ((erpOrders.getNumLastPrice().compareTo(BigDecimal.ZERO) <= 0) && CollectionUtils.isEmpty(collect)) {
                ErpOrderGift erpOrderGift = new ErpOrderGift();
                erpOrderGift.setOrderId(erpOrders.getId());
                List<ErpOrderGift> erpOrderGiftList = erpOrderGiftMapper.getErpOrderGiftList(erpOrderGift);
                if (CollectionUtils.isNotEmpty(erpOrderGiftList)) {
                    erpOrderGift.setGiftStatus(2);
                    int i = erpOrderGiftMapper.updateErpOrderGiftByOrderId(erpOrderGift);
                    if (i <= 0) {
                        throw new ServiceException("更新赠品状态失败");
                    }
                }
            }
        }
    }

    /**
     * 校验金额是否合法。
     *
     * @param price 待校验金额。
     * @return 返回校验结果。
     * <AUTHOR>
     * @since 2022-04-26 11:48:41
     */
    private Boolean checkPrice(BigDecimal price) {
        if (ObjectUtil.isNull(price)) {
            return Boolean.FALSE;
        }
        return price.compareTo(new BigDecimal("0")) >= 0;
    }

    /**
     * 获取尾款回款审核列表。
     *
     * @param erpRetainageReturnForExamineDTO 搜索条件。
     * @return 返回尾款回款审核列表。
     * <AUTHOR>
     * @since 2022-04-25 17:41:24
     */
    @DataScope(deptAlias = "sd", userAlias = "su")
    @Override
    public List<ErpRetainageReturnForExamineVO> getRetainageReturnForExamine(ErpRetainageReturnForExamineDTO erpRetainageReturnForExamineDTO) {
        SysUser loginUser = tokenService.getLoginUser().getSysUser();
        if(!SecurityUtils.isAdmin(loginUser.getUserId())){
            erpRetainageReturnForExamineDTO.setCurrentCityId(Objects.nonNull(loginUser) ? loginUser.getDept().getCityId() : null);
        }
        //财务  江东平  走自定义数据权限
        if (2407L == loginUser.getUserId() || 3121L == loginUser.getUserId() || 4242L == loginUser.getUserId() || 4008L == loginUser.getUserId()
                || 4640L == loginUser.getUserId() || 4218L == loginUser.getUserId()) {
            erpRetainageReturnForExamineDTO.setCurrentCityId(null);
            erpRetainageReturnForExamineDTO.setIsUsingDataScope(1);
        }
        erpRetainageReturnForExamineDTO.setNumType(2);
        List<ErpRetainageReturnForExamineVO> retainageReturnList = erpRetainageReturnMapper.getRetainageReturnForExamine(erpRetainageReturnForExamineDTO);
        //查询合同号
        ErpOrderQueryForOmListDTO query=new ErpOrderQueryForOmListDTO();
        List<Long> collect = retainageReturnList.stream().map(ErpRetainageReturnForExamineVO::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            query.setOrderIdList(collect);
        }
        List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
        Map<Long, List<ErpOrderInfoForOmListVO>> map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(val -> val.getOrderId()));

        for (ErpRetainageReturnForExamineVO erpRetainageReturn : retainageReturnList) {
            //显示联系人
            if((Objects.nonNull(erpRetainageReturn.getCommitOrderType()) && 2 == erpRetainageReturn.getCommitOrderType()) || (Objects.isNull(erpRetainageReturn.getClueId())) || (Objects.nonNull(erpRetainageReturn.getClueId()) && (0 == erpRetainageReturn.getClueId()))){
                erpRetainageReturn.setCustomerName(erpRetainageReturn.getClientContactName());
            }
            List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(erpRetainageReturn.getOrderId());
            if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                StringBuffer vcContractNumber = new StringBuffer();
                int size = erpOrderInfoForOmListVOS.size();
                for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                    if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                    } else {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                    }
                }
                erpRetainageReturn.setVcContractNumber(String.valueOf(vcContractNumber));
            }
            erpRetainageReturn.setExamineStatusName(RetainageStatusEnum.getNameByType(erpRetainageReturn.getExamineStatusType()));
        }
        return retainageReturnList;
    }
}
