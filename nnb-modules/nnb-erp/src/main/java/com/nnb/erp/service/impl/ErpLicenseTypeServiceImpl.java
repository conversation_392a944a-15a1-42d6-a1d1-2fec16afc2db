package com.nnb.erp.service.impl;

import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.vo.license.ErpLicenseTypeVO;
import com.nnb.system.api.model.LoginUser;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpLicenseTypeMapper;
import com.nnb.erp.domain.ErpLicenseType;
import com.nnb.erp.service.IErpLicenseTypeService;

/**
 * 执照类型配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class ErpLicenseTypeServiceImpl implements IErpLicenseTypeService 
{
    @Autowired
    private ErpLicenseTypeMapper erpLicenseTypeMapper;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询执照类型配置
     * 
     * @param id 执照类型配置主键
     * @return 执照类型配置
     */
    @Override
    public ErpLicenseType selectErpLicenseTypeById(Long id)
    {
        return erpLicenseTypeMapper.selectErpLicenseTypeById(id);
    }

    /**
     * 查询执照类型配置列表
     * 
     * @param erpLicenseType 执照类型配置
     * @return 执照类型配置
     */
    @Override
    public List<ErpLicenseTypeVO> selectErpLicenseTypeList(ErpLicenseType erpLicenseType)
    {
        List<ErpLicenseTypeVO> list_ = erpLicenseTypeMapper.selectErpLicenseTypeList(erpLicenseType);

        //根据parentId组装树形结构
        Map<Integer, ErpLicenseTypeVO> voMap = new HashMap<Integer, ErpLicenseTypeVO>();
        for (ErpLicenseTypeVO vo : list_) {
            int id = Integer.parseInt(vo.getId().toString());
            voMap.put(id, vo);
        }
        for (ErpLicenseTypeVO vo : list_) {
            int pid = vo.getParentId().intValue();
            if (pid != 0 && voMap.containsKey(pid)) {
                ErpLicenseTypeVO tempVo = voMap.get(pid);
                List<ErpLicenseTypeVO> tempListVo = tempVo.getChildren();
                if (tempListVo == null) {
                    tempListVo = new ArrayList<ErpLicenseTypeVO>();
                }
                tempListVo.add(vo);
                tempVo.setChildren(tempListVo);
            }
        }
        List<Integer> list = new ArrayList<Integer>();
        for (Integer k : voMap.keySet()) {
            ErpLicenseTypeVO tempVo = voMap.get(k);
            if (tempVo.getParentId().intValue() != 0) {
                list.add(k);
            }
        }
        for (int i : list) {
            voMap.remove(i);
        }
        return new ArrayList<ErpLicenseTypeVO>(voMap.values());
    }

    /**
     * 新增执照类型配置
     * 
     * @param erpLicenseType 执照类型配置
     * @return 结果
     */
    @Override
    public int insertErpLicenseType(ErpLicenseType erpLicenseType)
    {
        erpLicenseType.setCreatedTime(new Date());
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotNull(erpLicenseType.getParentId())) {
            ErpLicenseType temp = erpLicenseTypeMapper.selectErpLicenseTypeById(erpLicenseType.getParentId());
            if (ObjectUtil.isNotNull(temp)) {
                erpLicenseType.setTypeLevel(temp.getTypeLevel() + 1);
            } else {
                erpLicenseType.setTypeLevel(1);
            }
        }
        return erpLicenseTypeMapper.insertErpLicenseType(erpLicenseType);
    }

    /**
     * 修改执照类型配置
     * 
     * @param erpLicenseType 执照类型配置
     * @return 结果
     */
    @Override
    public int updateErpLicenseType(ErpLicenseType erpLicenseType)
    {
        erpLicenseType.setUpdateTime(new Date());
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotNull(erpLicenseType.getParentId())) {
            ErpLicenseType temp = erpLicenseTypeMapper.selectErpLicenseTypeById(erpLicenseType.getParentId());
            if (ObjectUtil.isNotNull(temp)) {
                erpLicenseType.setTypeLevel(temp.getTypeLevel() + 1);
            } else {
                erpLicenseType.setTypeLevel(1);
            }
        }
        return erpLicenseTypeMapper.updateErpLicenseType(erpLicenseType);
    }
}
