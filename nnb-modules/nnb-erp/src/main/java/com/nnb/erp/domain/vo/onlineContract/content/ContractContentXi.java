package com.nnb.erp.domain.vo.onlineContract.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/7/13 13:25
 */
@Data
@ApiModel(value = "ContractContentX", description = "牛牛帮-新办建筑资质合同")
public class ContractContentXi {

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 甲方代表人姓名
     */
    private String firstPrincipalName;

    /**
     * 甲方联系电话
     */
    private String firstContactPhone;

    /**
     * 甲方部门
     */
    private String firstDeptName;

    /**
     * 乙方
     */
    private String secondParty;
    /**
     * 乙方代表人姓名
     */
    private String secondPrincipalName;
    /**
     * 乙方联系电话
     */
    private String secondtContactPhone;
    /**
     * 乙方部门
     */
    private String secondDeptName;
    /**
     * 支付人民币大写
     */
    private String payerMoneyCapital;
    /**
     * 支付人民币小写
     */
    private BigDecimal payerMoneyLowerCase;

    /**
     * 实收金额
     */
    private BigDecimal payPrice;

    /**
     * 尾款小写
     */
    private BigDecimal balancePayment;

    /**
     * 合同其它详情信息
     */
    private Object contractDetailObject;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
    /**
     * 应收金额
     */
    private BigDecimal totalPrice;

}
