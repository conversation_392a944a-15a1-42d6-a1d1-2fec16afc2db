package com.nnb.erp.domain.dto.service;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpAccountPerformanceDTO extends BaseEntity {

    @ApiModelProperty("财务收款开始时间")
    private Date startDate;

    @ApiModelProperty("财务收款结束时间")
    private Date endDate;

    @ApiModelProperty("城市ID")
    private Long cityId;

    @ApiModelProperty("部门ID")
    private Long deptId;

    @ApiModelProperty("年份")
    private String year;

    @ApiModelProperty("季度 1:第一季度，2：第二季度，3：第三季度，4：第四季度")
    private String quarter;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("主管绩效月份开始")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthStart;

    @ApiModelProperty("主管绩效月份结束")
    @JsonFormat(pattern = "yyyy-MM")
    private Date monthEnd;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("记账结束时间开始")
    private Date acEndStart;

    @ApiModelProperty("记账结束时间结束")
    private Date acEndEnd;

    @ApiModelProperty("记账开始时间开始")
    private Date acStartStart;

    @ApiModelProperty("记账开始时间结束")
    private Date acStartEnd;

    @ApiModelProperty("客户ID")
    private Long client;

    @ApiModelProperty("节点状态")
    private List<Integer> servicePointStatus;

    @ApiModelProperty("主管绩效查询时间List")
    private List<String> deptPerformanceDate;

    @ApiModelProperty("1:尾款为0")
    private Integer type;

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("权限 1:管理员权限，2：其它权限")
    private Integer dataScope;

    @ApiModelProperty("导出类型 1:主管绩效，2：个人绩效")
    private Integer exportType;


    @ApiModelProperty("生成报表类型 1:主管绩效生成，2：个人绩效生成")
    private Integer generateType;

    @ApiModelProperty("历史报表查询类型 1:主管绩效查询，2：个人绩效查询")
    private Integer oldAccountPermanceType;

    @ApiModelProperty("当前登录人的部门ID")
    private Long currentUserDeptId;

    @ApiModelProperty("待续记账结束时间开始")
    private Date toBeRenewedAcEndStart;



}
