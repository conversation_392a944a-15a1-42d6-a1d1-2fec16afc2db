package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.SConfigServiceCatalogue;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
public interface SConfigServiceCatalogueMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SConfigServiceCatalogue selectSConfigServiceCatalogueById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServiceCatalogue 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SConfigServiceCatalogue> selectSConfigServiceCatalogueList(SConfigServiceCatalogue sConfigServiceCatalogue);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServiceCatalogue 【请填写功能名称】
     * @return 结果
     */
    public int insertSConfigServiceCatalogue(SConfigServiceCatalogue sConfigServiceCatalogue);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServiceCatalogue 【请填写功能名称】
     * @return 结果
     */
    public int updateSConfigServiceCatalogue(SConfigServiceCatalogue sConfigServiceCatalogue);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSConfigServiceCatalogueById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSConfigServiceCatalogueByIds(Long[] ids);
}
