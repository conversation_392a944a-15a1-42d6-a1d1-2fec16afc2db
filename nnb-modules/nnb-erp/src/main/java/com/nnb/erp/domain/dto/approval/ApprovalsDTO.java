package com.nnb.erp.domain.dto.approval;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 审批对象 approvals
 *
 * <AUTHOR>
 * @date 2022-10-14
 */
@ApiModel(value="Approvals",description="审批对象")
@Data
public class ApprovalsDTO
{
    private static final long serialVersionUID = 1L;
     private Integer id;
    /** 类型，1 审批 2抄送 */
    @Excel(name = "类型，1 审批 2抄送")
    @ApiModelProperty("类型，1 审批 2抄送")
    private Integer type;

    /** type_all是1（合同）：审批状态 1待我审批， 2，通过 3，没轮到我 5，驳回 6撤销 7失效； type_all是2（成本结算）1待我审批，2，审批通过，3，没轮到我 4已支付，4驳回，6撤销 */
    @Excel(name = "type_all是1", readConverterExp = "合=同")
    @ApiModelProperty("type_all是1（合同）：审批状态 1待我审批， 2，通过 3，没轮到我 5，驳回 6撤销 7失效； type_all是2（成本结算）1待我审批，2，审批通过，3，没轮到我 4已支付，4驳回，6撤销")
    private Integer status;

    @ApiModelProperty("type_all是1（合同）：审批状态 1待我审批， 2，通过 3，没轮到我 5，驳回 6撤销 7失效； type_all是2（成本结算）1待我审批，2，审批通过，3，没轮到我 4已支付，4驳回，6撤销")
    private List<Integer> statusList;


    /** $column.columnComment */
    @Excel(name = "创建人")
    @ApiModelProperty("$column.columnComment")
    private Integer createdBy;

    /** $column.columnComment */
    @Excel(name = "拒绝审批理由")
    @ApiModelProperty("$column.columnComment")
    private Integer updatedBy;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间开始")
    private String createdAtStart;

    @ApiModelProperty("创建时间截止")
    private String createdAtEnd;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("提交日期")
    private Date createdAt;

    @Excel(name = "对应类型的id,cost_settlement.id或者其他Id")
    @ApiModelProperty("对应类型的id,cost_settlement.id或者其他Id")
    private Long otherId;
    /** 1.合同申请，2.成本结算申请 3预付款审批 4产品审批 production_id,5.疑难审批 6,知识产权审批 */
    @Excel(name = "1.合同申请，2.成本结算申请 3预付款审批 4产品审批 production_id,5.疑难审批 6,知识产权审批")
    @ApiModelProperty("1.合同申请，2.成本结算申请 3预付款审批 4产品审批 production_id,5.疑难审批 6,知识产权审批")
    private Integer typeAll;

    @ApiModelProperty("1 到我审批 0 不是我")
    private Integer isMe;

    @ApiModelProperty("申请类型")
    private Integer applyType;

    @ApiModelProperty("是否预付")
    private Integer isAdvance;

    @ApiModelProperty("核销状态 1 已核销 2未核销")
    private Integer hxStatus;

    @ApiModelProperty("预付款单号")
    private String vcAdvanceChargeNo;

    @ApiModelProperty("菜单类型  1 我发起的审批，2 待我审批，3 抄送我的审批")
    private Integer menuType;

    @ApiModelProperty("审批人Id")
    private Long userId;

    @ApiModelProperty("代理代号")
    private String agentNumber;

    @ApiModelProperty("供应商")
    private Integer agentId;
    @ApiModelProperty("企业名称")
    private String enterpriseName;

    private Long sSMainId;

    private Long supplier;

    @ApiModelProperty("企业名称")
    private String orderNum;

    @ApiModelProperty("1:待审批，2：已审批")
    private Integer statusType;

}
