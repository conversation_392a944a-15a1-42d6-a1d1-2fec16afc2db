package com.nnb.erp.controller.inventory;

import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.erp.domain.inventory.IaCompany;
import com.nnb.erp.service.inventory.IaCompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 公司存货参数Controller
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@RestController
@RequestMapping("/iaCompany")
@Api(tags = "IaCompanyController", description = "公司存货参数")
public class IaCompanyController extends BaseController {

    @Autowired
    private IaCompanyService iaCompanyService;

    /**
     * 获取公司存货参数详细信息
     */
    @ApiOperation(value = "获取公司存货参数详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaCompany.class)})
    @PostMapping("/getById")
    public AjaxResult getInfo(@RequestBody IaCompany iaCompany) {
        return AjaxResult.success(iaCompanyService.selectIaCompanyById(iaCompany.getId()));
    }

    /**
     * 新增公司存货参数
     */
    @ApiOperation(value = "新增公司存货参数")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IaCompany iaCompany) {
        return toAjax(iaCompanyService.insertIaCompany(iaCompany));
    }

    /**
     * 修改公司存货参数
     */
    @ApiOperation(value = "修改公司存货参数")
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody IaCompany iaCompany) {
        return toAjax(iaCompanyService.updateIaCompany(iaCompany));
    }

    /**
     * 删除公司存货参数
     */
    @ApiOperation(value = "删除公司存货参数")
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody IaCompany iaCompany) {
        return toAjax(iaCompanyService.deleteIaCompanyByIds(iaCompany.getIds()));
    }
}
