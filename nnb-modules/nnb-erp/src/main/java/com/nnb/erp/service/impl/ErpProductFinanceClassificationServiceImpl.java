package com.nnb.erp.service.impl;

import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.ErpIncomeCategoryDetails;
import com.nnb.erp.domain.vo.ErpProductFinanceClassificationVO;
import com.nnb.erp.mapper.ErpIncomeCategoryDetailsMapper;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpProductFinanceClassificationMapper;
import com.nnb.erp.domain.ErpProductFinanceClassification;
import com.nnb.erp.service.IErpProductFinanceClassificationService;

/**
 * 财务产品分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Service
public class ErpProductFinanceClassificationServiceImpl implements IErpProductFinanceClassificationService
{
    @Autowired
    private ErpProductFinanceClassificationMapper erpProductFinanceClassificationMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ErpIncomeCategoryDetailsMapper erpIncomeCategoryDetailsMapper;

    /**
     * 查询财务产品分类
     *
     * @param id 财务产品分类主键
     * @return 财务产品分类
     */
    @Override
    public ErpProductFinanceClassification selectErpProductFinanceClassificationById(Long id)
    {
        return erpProductFinanceClassificationMapper.selectErpProductFinanceClassificationById(id);
    }

    /**
     * 查询财务产品分类列表
     *
     * @param erpProductFinanceClassification 财务产品分类
     * @return 财务产品分类
     */
    @Override
    public List<ErpProductFinanceClassificationVO> selectErpProductFinanceClassificationList(ErpProductFinanceClassification erpProductFinanceClassification)
    {
        List<ErpProductFinanceClassificationVO> list_ = erpProductFinanceClassificationMapper.selectErpProductFinanceClassificationList(erpProductFinanceClassification);

        //根据parentId组装树形结构
        Map<Integer, ErpProductFinanceClassificationVO> voMap = new HashMap<Integer, ErpProductFinanceClassificationVO>();
        for (ErpProductFinanceClassificationVO vo : list_) {
            int id = Integer.parseInt(vo.getId().toString());
            voMap.put(id, vo);
        }
        for (ErpProductFinanceClassificationVO vo : list_) {
            int pid = vo.getParentId().intValue();
            if (pid != 0 && voMap.containsKey(pid)) {
                ErpProductFinanceClassificationVO tempVo = voMap.get(pid);
                List<ErpProductFinanceClassificationVO> tempListVo = tempVo.getChildren();
                if (tempListVo == null) {
                    tempListVo = new ArrayList<ErpProductFinanceClassificationVO>();
                }
                tempListVo.add(vo);
                tempVo.setChildren(tempListVo);
            }
        }
        List<Integer> list = new ArrayList<Integer>();
        for (Integer k : voMap.keySet()) {
            ErpProductFinanceClassificationVO tempVo = voMap.get(k);
            if (tempVo.getParentId().intValue() != 0) {
                list.add(k);
            }
        }
        for (int i : list) {
            voMap.remove(i);
        }
        return new ArrayList<ErpProductFinanceClassificationVO>(voMap.values());
    }

    /**
     * 新增财务产品分类
     *
     * @param erpProductFinanceClassification 财务产品分类
     * @return 结果
     */
    @Override
    public int insertErpProductFinanceClassification(ErpProductFinanceClassification erpProductFinanceClassification)
    {
        erpProductFinanceClassification.setCreatedTime(new Date());
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotNull(erpProductFinanceClassification.getParentId())) {
            ErpProductFinanceClassification temp = erpProductFinanceClassificationMapper.selectErpProductFinanceClassificationById(erpProductFinanceClassification.getParentId());
            if (ObjectUtil.isNotNull(temp)) {
                erpProductFinanceClassification.setTypeLevel(temp.getTypeLevel() + 1);
            } else {
                erpProductFinanceClassification.setTypeLevel(1);
            }
        }
        erpProductFinanceClassification.setCreatedUser(loginUser.getSysUser().getUserId().intValue());
        erpProductFinanceClassification.setCreatedTime(new Date());
        erpProductFinanceClassification.setUpdateUser(loginUser.getSysUser().getUserId().intValue());
        erpProductFinanceClassification.setUpdateTime(new Date());
        return erpProductFinanceClassificationMapper.insertErpProductFinanceClassification(erpProductFinanceClassification);
    }

    /**
     * 修改财务产品分类
     * 
     * @param erpProductFinanceClassification 财务产品分类
     * @return 结果
     */
    @Override
    public int updateErpProductFinanceClassification(ErpProductFinanceClassification erpProductFinanceClassification)
    {
        if (erpProductFinanceClassification.getStatus() == 0) {
            ErpIncomeCategoryDetails erpIncomeCategoryDetails = new ErpIncomeCategoryDetails();
            erpIncomeCategoryDetails.setCategoryId(erpProductFinanceClassification.getId());
            erpIncomeCategoryDetails.setStatus(1L);
            List<ErpIncomeCategoryDetails> list = erpIncomeCategoryDetailsMapper.selectErpIncomeCategoryDetailsList(erpIncomeCategoryDetails);
            if (list.size() > 0) {
                throw new ServiceException("该分类在明细中已存在！");
            }
        }

        erpProductFinanceClassification.setUpdateTime(new Date());
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNotNull(erpProductFinanceClassification.getParentId())) {
            ErpProductFinanceClassification temp = erpProductFinanceClassificationMapper.selectErpProductFinanceClassificationById(erpProductFinanceClassification.getParentId());
            if (ObjectUtil.isNotNull(temp)) {
                erpProductFinanceClassification.setTypeLevel(temp.getTypeLevel() + 1);
            } else {
                erpProductFinanceClassification.setTypeLevel(1);
            }
        }
        erpProductFinanceClassification.setUpdateUser(loginUser.getSysUser().getUserId().intValue());
        erpProductFinanceClassification.setUpdateTime(new Date());
        return erpProductFinanceClassificationMapper.updateErpProductFinanceClassification(erpProductFinanceClassification);
    }

    /**
     * 批量删除财务产品分类
     * 
     * @param ids 需要删除的财务产品分类主键
     * @return 结果
     */
    @Override
    public int deleteErpProductFinanceClassificationByIds(Long[] ids)
    {
        return erpProductFinanceClassificationMapper.deleteErpProductFinanceClassificationByIds(ids);
    }

    /**
     * 删除财务产品分类信息
     * 
     * @param id 财务产品分类主键
     * @return 结果
     */
    @Override
    public int deleteErpProductFinanceClassificationById(Long id)
    {
        return erpProductFinanceClassificationMapper.deleteErpProductFinanceClassificationById(id);
    }
}
