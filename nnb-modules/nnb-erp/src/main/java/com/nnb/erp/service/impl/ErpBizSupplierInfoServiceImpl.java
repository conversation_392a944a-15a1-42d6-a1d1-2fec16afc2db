package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpBizSupplierInfoMapper;
import com.nnb.erp.domain.ErpBizSupplierInfo;
import com.nnb.erp.service.IErpBizSupplierInfoService;

/**
 * 供应商信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-09-28
 */
@Service
public class ErpBizSupplierInfoServiceImpl implements IErpBizSupplierInfoService 
{
    @Autowired
    private ErpBizSupplierInfoMapper erpBizSupplierInfoMapper;

    /**
     * 查询供应商信息
     * 
     * @param numId 供应商信息主键
     * @return 供应商信息
     */
    @Override
    public ErpBizSupplierInfo selectErpBizSupplierInfoByNumId(Long numId)
    {
        return erpBizSupplierInfoMapper.selectErpBizSupplierInfoByNumId(numId);
    }

    /**
     * 查询供应商信息列表
     * 
     * @param erpBizSupplierInfo 供应商信息
     * @return 供应商信息
     */
    @Override
    public List<ErpBizSupplierInfo> selectErpBizSupplierInfoList(ErpBizSupplierInfo erpBizSupplierInfo)
    {
        return erpBizSupplierInfoMapper.selectErpBizSupplierInfoList(erpBizSupplierInfo);
    }

    /**
     * 新增供应商信息
     * 
     * @param erpBizSupplierInfo 供应商信息
     * @return 结果
     */
    @Override
    public int insertErpBizSupplierInfo(ErpBizSupplierInfo erpBizSupplierInfo)
    {
        return erpBizSupplierInfoMapper.insertErpBizSupplierInfo(erpBizSupplierInfo);
    }

    /**
     * 修改供应商信息
     * 
     * @param erpBizSupplierInfo 供应商信息
     * @return 结果
     */
    @Override
    public int updateErpBizSupplierInfo(ErpBizSupplierInfo erpBizSupplierInfo)
    {
        return erpBizSupplierInfoMapper.updateErpBizSupplierInfo(erpBizSupplierInfo);
    }

    /**
     * 批量删除供应商信息
     * 
     * @param numIds 需要删除的供应商信息主键
     * @return 结果
     */
    @Override
    public int deleteErpBizSupplierInfoByNumIds(Long[] numIds)
    {
        return erpBizSupplierInfoMapper.deleteErpBizSupplierInfoByNumIds(numIds);
    }

    /**
     * 删除供应商信息信息
     * 
     * @param numId 供应商信息主键
     * @return 结果
     */
    @Override
    public int deleteErpBizSupplierInfoByNumId(Long numId)
    {
        return erpBizSupplierInfoMapper.deleteErpBizSupplierInfoByNumId(numId);
    }
}
