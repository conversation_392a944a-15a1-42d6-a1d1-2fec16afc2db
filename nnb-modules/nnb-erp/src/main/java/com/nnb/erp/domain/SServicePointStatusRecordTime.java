package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 s_service_point_status_record_time
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
@ApiModel(value="SServicePointStatusRecordTime",description="【请填写功能名称】对象")
public class SServicePointStatusRecordTime extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 服务单Id */
    @Excel(name = "服务单Id")
    @ApiModelProperty("服务单Id")
    private Long serviceId;

    /** 服务节点状态 */
    @Excel(name = "服务节点状态")
    @ApiModelProperty("服务节点状态")
    private Long servicePointStatus;

    /** 节点操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "节点操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("节点操作时间")
    private Date recordTime;

    /** 操作人 */
    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private Long createdUser;

    /** 状态1正常2被覆盖 */
    @Excel(name = "状态1正常2被覆盖")
    @ApiModelProperty("状态1正常2被覆盖")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setServiceId(Long serviceId) 
    {
        this.serviceId = serviceId;
    }

    public Long getServiceId() 
    {
        return serviceId;
    }
    public void setServicePointStatus(Long servicePointStatus)
    {
        this.servicePointStatus = servicePointStatus;
    }

    public Long getServicePointStatus()
    {
        return servicePointStatus;
    }
    public void setRecordTime(Date recordTime) 
    {
        this.recordTime = recordTime;
    }

    public Date getRecordTime() 
    {
        return recordTime;
    }
    public void setCreatedUser(Long createdUser)
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser()
    {
        return createdUser;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("serviceId", getServiceId())
            .append("servicePointStatus", getServicePointStatus())
            .append("recordTime", getRecordTime())
            .append("createdUser", getCreatedUser())
            .append("status", getStatus())
            .toString();
    }
}
