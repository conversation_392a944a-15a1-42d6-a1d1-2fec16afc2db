package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.XcxScoreingAccount;
import com.nnb.erp.domain.dto.XcxScoreingAccountDto;
import com.nnb.erp.domain.vo.XcxScoreingAccountVo;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IXcxScoreingAccountService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public XcxScoreingAccount selectXcxScoreingAccountById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param xcxScoreingAccount 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<XcxScoreingAccountVo> selectXcxScoreingAccountList(XcxScoreingAccountDto xcxScoreingAccount);

    /**
     * 新增【请填写功能名称】
     * 
     * @param xcxScoreingAccount 【请填写功能名称】
     * @return 结果
     */
    public int insertXcxScoreingAccount(XcxScoreingAccount xcxScoreingAccount);

    /**
     * 修改【请填写功能名称】
     * 
     * @param xcxScoreingAccount 【请填写功能名称】
     * @return 结果
     */
    public int updateXcxScoreingAccount(XcxScoreingAccount xcxScoreingAccount);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteXcxScoreingAccountByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXcxScoreingAccountById(Long id);

    public String getCompanyNamesByPhone(String phone);
}
