package com.nnb.erp.domain.inventory;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 存货档案对象 ia_inventory_archives
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@ApiModel(value = "IaInventoryArchives", description = "存货档案对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaInventoryArchives implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /**
     * 企业/公司id
     */
    @ApiModelProperty("企业/公司id")
    private Long companyId;

    /**
     * 存货大类id
     */
    @ApiModelProperty("存货大类id")
    private Long iaCategoryId;

    /**
     * 存货大类编码
     */
    @ApiModelProperty("存货大类编码")
    private String iaCategoryCode;

    /** 仓库id */
    @ApiModelProperty("仓库id")
    private Long warehouseId;

    /** 仓库编码 */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /** 仓库名称 */
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /**
     * 存货编码
     */
    @Excel(name = "存货编码")
    @ApiModelProperty("存货编码")
    private String code;

    /**
     * 存货名称
     */
    @Excel(name = "存货名称")
    @ApiModelProperty("存货名称")
    private String name;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    @ApiModelProperty("规格型号")
    private String model;

    /**
     * 计量单位id
     */
    @ApiModelProperty("计量单位id")
    private Long iaUnitId;

    /**
     * 计量单位编码
     */
    @ApiModelProperty("计量单位编码")
    private String iaUnitCode;

    /**
     * 计量单位名称
     */
    @Excel(name = "计量单位名称")
    @ApiModelProperty("计量单位名称")
    private String iaUnitName;

    /**
     * 存货大类名称
     */
    @Excel(name = "存货分类")
    @ApiModelProperty("存货大类名称")
    private String iaCategoryName;

    /**
     * 计价方式 1：全月加权平均法
     */
    @Excel(name = "计价方式", readConverterExp = "1=全月加权平均法")
    @ApiModelProperty("计价方式 1：全月加权平均法")
    private Integer pricingMethod;

    /** 产品id */
    @ApiModelProperty("产品id")
    private Long productId;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("上传图片路径")
    private String productImageUrl;

    @ApiModelProperty(value = "当前页码", notes = "当前页码")
    private Integer pageNum;

    @ApiModelProperty(value = "每页记录数，默认为10", notes = "每页记录数")
    private Integer pageSize = 10;

    /** 非数据库字段*/
    private String keyWord;
    private List<Long> ids;
    private List<Long> iaCategoryIds;
    private List<Long> productIds;
}
