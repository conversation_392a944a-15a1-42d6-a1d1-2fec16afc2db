package com.nnb.erp.domain.vo.onlineContract.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资质、设立、记账代理合同
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ContractContentXXVII", description = "资质、设立、记账代理合同")
public class ContractContentXXVII extends contractBaseEntity{

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 甲方代表人姓名
     */
    private String firstRepresentativeName;


    /**
     * 甲方联系电话
     */
    private String firstContactPhone;

    /**
     * 甲方住址
     */
    private String firstAddress;

    /**
     * 乙方
     */
    private String secondParty;
    /**
     * 乙方代表人姓名
     */
    private String secondRepresentativeName;
    /**
     * 乙方联系电话
     */
    private String secondtContactPhone;
    /**
     * 乙方住址
     */
    private String secondAddress;


    /**
     * 应付人民币大写
     */
    private String payerMoneyCapital;

    /**
     * 应付人民币小写
     */
    private BigDecimal payerMoneyLowerCase;
    /**
     * 尾款
     */
    private BigDecimal balancePaymentLowerCase;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 合同其它详情信息
     */
    private Object contractDetailObject;


}
