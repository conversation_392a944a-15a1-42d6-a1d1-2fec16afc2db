package com.nnb.erp.controller;

import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.domain.XcxCouponConfig;
import com.nnb.erp.service.IXcxCouponConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 小程序优惠券配置Controller
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@RestController
@RequestMapping("/xcxCouponConfig")
@Api(tags = "XcxCouponConfigController", description = "小程序优惠券配置")
public class XcxCouponConfigController extends BaseController {
    @Autowired
    private IXcxCouponConfigService xcxCouponConfigService;

    /**
     * 查询小程序优惠券配置列表
     */
    @ApiOperation(value = "查询小程序优惠券配置列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = XcxCouponConfig.class)})
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody XcxCouponConfig xcxCouponConfig) {
        startPage();
        List<XcxCouponConfig> list = xcxCouponConfigService.selectXcxCouponConfigList(xcxCouponConfig);
        return getDataTable(list);
    }

    /**
     * 导出小程序优惠券配置列表
     */
    @ApiOperation(value = "导出小程序优惠券配置列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, XcxCouponConfig xcxCouponConfig) throws IOException {
        List<XcxCouponConfig> list = xcxCouponConfigService.selectXcxCouponConfigList(xcxCouponConfig);
        ExcelUtil<XcxCouponConfig> util = new ExcelUtil<XcxCouponConfig>(XcxCouponConfig.class);
        util.exportExcel(response, list, "小程序优惠券配置数据");
    }

    /**
     * 获取小程序优惠券配置详细信息
     */
    @ApiOperation(value = "获取小程序优惠券配置详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = XcxCouponConfig.class)})
    @PostMapping("/getById")
    public AjaxResult getInfo(@RequestBody XcxCouponConfig xcxCouponConfig) {
        return AjaxResult.success(xcxCouponConfigService.selectXcxCouponConfigById(xcxCouponConfig.getId()));
    }

    /**
     * 新增小程序优惠券配置
     */
    @ApiOperation(value = "新增小程序优惠券配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody XcxCouponConfig xcxCouponConfig) {
        return toAjax(xcxCouponConfigService.insertXcxCouponConfig(xcxCouponConfig));
    }

    /**
     * 修改小程序优惠券配置
     */
    @ApiOperation(value = "修改小程序优惠券配置")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody XcxCouponConfig xcxCouponConfig) {
        return toAjax(xcxCouponConfigService.updateXcxCouponConfig(xcxCouponConfig));
    }

    /**
     * 删除小程序优惠券配置
     */
    @ApiOperation(value = "删除小程序优惠券配置")
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody XcxCouponConfig xcxCouponConfig) {
        return toAjax(xcxCouponConfigService.deleteXcxCouponConfigByIds(xcxCouponConfig.getIds()));
    }
}
