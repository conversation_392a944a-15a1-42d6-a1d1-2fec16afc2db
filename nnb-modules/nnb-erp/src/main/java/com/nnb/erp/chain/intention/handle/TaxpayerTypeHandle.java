package com.nnb.erp.chain.intention.handle;

import com.nnb.erp.chain.intention.IntentionHandleIntercept;
import com.nnb.erp.chain.intention.entity.IntentionConfigLastParam;
import com.nnb.erp.constant.IntentionConstants;
import com.nnb.erp.mapper.IntentionConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description: 纳税人类型
 * @Date: 2024-11-13
 * @Version: 1.0
 */
@Component("taxpayerType")
public class TaxpayerTypeHandle implements IntentionHandleIntercept {

    private IntentionConfigMapper intentionConfigMapper;

    @Override
    public List<Long> handle(IntentionConfigLastParam context) {
        List<Long> ids = Arrays.stream(context.getConditionValueTwo().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (IntentionConstants.EQUAL_ANY_TERM.equals(context.getConditionValueOne())){
            //等于任意
            return intentionConfigMapper.selectServiceMainIdByTaxpayerType(
                    context.getConditionValueOne(), ids
            );
        } else if (IntentionConstants.NOT_EQUAL_ANY_TERM.equals(context.getConditionValueOne())){
            //不等于任意
            return intentionConfigMapper.selectServiceMainIdByTaxpayerType(
                    context.getConditionValueOne(), ids
            );
        }
        return Collections.emptyList();
    }

    @Autowired
    private void setIntentionConfigMapper(IntentionConfigMapper intentionConfigMapper) {
        this.intentionConfigMapper = intentionConfigMapper;
    }
}
