package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.SConfigServicePoint;
import com.nnb.erp.domain.vo.service.SConfigServicePointVo;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
public interface ISConfigServicePointService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SConfigServicePoint selectSConfigServicePointById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServicePoint 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SConfigServicePointVo> selectSConfigServicePointList(SConfigServicePoint sConfigServicePoint);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServicePoint 【请填写功能名称】
     * @return 结果
     */
    public int insertSConfigServicePoint(SConfigServicePoint sConfigServicePoint);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServicePoint 【请填写功能名称】
     * @return 结果
     */
    public int updateSConfigServicePoint(SConfigServicePoint sConfigServicePoint);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSConfigServicePointByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSConfigServicePointById(Long id);
}
