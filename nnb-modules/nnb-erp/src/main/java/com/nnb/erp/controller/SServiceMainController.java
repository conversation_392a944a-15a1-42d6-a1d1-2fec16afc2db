package com.nnb.erp.controller;

import java.math.BigDecimal;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.core.utils.sql.SqlUtil;
import com.nnb.common.core.web.page.PageDomain;
import com.nnb.common.core.web.page.TableSupport;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpExamineApproveConstants;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dto.ErpBizServiceAdvanceDto;
import com.nnb.erp.domain.dto.ErpExamineApproveDTO;
import com.nnb.erp.domain.dto.QualificationsExtensionDto;
import com.nnb.erp.domain.dto.service.SServiceRecordDto;
import com.nnb.erp.domain.dto.service.ServiceByEnterpriseDto;
import com.nnb.erp.domain.dto.service.ServiceMainDto;
import com.nnb.erp.domain.vo.ErpExamineApproveTypeManageVo;
import com.nnb.erp.domain.vo.QualificationsExtensionVo;
import com.nnb.erp.domain.vo.SServiceAddressVo;
import com.nnb.erp.domain.vo.service.SConfigServicePointStatusVo;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.domain.vo.service.ServiceByEnterpriseVo;
import com.nnb.erp.domain.vo.task.QueryForTaskListDTO;
import com.nnb.erp.domain.vo.task.QueryForTaskListVO;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.approval.ApprovalZhongzhuansMapper;
import com.nnb.erp.mapper.approval.ApprovalsMapper;
import com.nnb.erp.mapper.approval.CostSettlementMapper;
import com.nnb.erp.service.ISConfigServicePointStatusService;
import com.nnb.erp.service.ISConfigServiceTypeService;
import com.nnb.erp.service.impl.ExportLogServiceImpl;
import com.nnb.erp.util.DateUtil;
import com.nnb.erp.util.ThreadPoolUtil;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.service.ISServiceMainService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@RestController
@RequestMapping("/main")
@Api(tags = "SServiceMainController", description = "【请填写功能名称】")
@Slf4j
public class SServiceMainController extends BaseController {
    @Autowired
    private ISServiceMainService sServiceMainService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SConfigServicePointStatusMapper sConfigServicePointStatusMapper;
    @Autowired
    private SServiceMainMapper sServiceMainMapper;
    @Autowired
    private SConfigServiceTypeMapper sConfigServiceTypeMapper;
    @Autowired
    private ErpEnterpriseDetailMapper erpEnterpriseDetailMapper;
    @Autowired
    private ExportLogServiceImpl exportLogService;
    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "查询【请填写功能名称】列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = SServiceMain.class)})
//    @PreAuthorize(hasPermi = "erp:main:list")
    @GetMapping("/list")
    public TableDataInfo list(ServiceMainDto sServiceMain) {
        if (!ObjectUtil.isEmpty(sServiceMain.getServicePointStatus())) {
            SConfigServicePointStatus servicePointStatus = sConfigServicePointStatusMapper.selectSConfigServicePointStatusById(sServiceMain.getServicePointStatus());
            if (servicePointStatus.getIsEnd() == 1 && !ObjectUtil.isEmpty(servicePointStatus.getNextService())) {
                String ids = sServiceMainMapper.getIdsByTypeBeforeZz(sServiceMain.getServiceType());
                if (ObjectUtil.isNotEmpty(ids)) {
                    sServiceMain.setIds(ids);
                    sServiceMain.setServiceCatalogue(null);
                    sServiceMain.setServiceType(null);
                    sServiceMain.setServicePoint(null);
                    sServiceMain.setServicePointStatus(null);
                    sServiceMain.setWorkPlatformId(null);
                    sServiceMain.setChekRole(0L);
                }
            }
        }

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        SysRole sysRole = sysUser.getRoles().get(0);
        sServiceMain.setDataScope(sysRole.getDataScope());
        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)
                && StringUtils.isEmpty(sServiceMain.getCompanyName())) {
//            sServiceMain.setCityId(sysUser.getDept().getCityId());
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }
        if (!ObjectUtil.isEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType().intValue() == 29) {
            sServiceMain.setServiceCatalogue(null);
            sServiceMain.setServiceType(null);
            sServiceMain.setServicePoint(null);
            sServiceMain.setServicePointStatus(null);
            sServiceMain.setWorkPlatformId(null);
            sServiceMain.setIsAddress(1L);
        }
        sServiceMain.setOrderRegistOver(1L);
        if (ObjectUtil.isNotEmpty(sServiceMain.getWorkPlatformId()) &&
                ((sServiceMain.getWorkPlatformId().intValue() == 3 && sServiceMain.getIsBd().intValue() == 1) || sServiceMain.getWorkPlatformId().intValue() == 7)
        ) {
            sServiceMain.setWorkPlatformId(-1L);
        }
        System.out.println("isBD====>" + sServiceMain.getIsBd());
        sServiceMain.setType(1);
        //城市筛选
//        sServiceMain.setCityIdCondition(sServiceMain.getCityId());
//        sServiceMain.setCityId(null);
        //服务单增加城市权限
//        if (Objects.nonNull(sysUser)) {
//            Long userId = 202L;
//            Long userUserId = sysUser.getUserId();
//            sServiceMain.setCityId(( !(userId.longValue() == (userUserId.longValue()))  && Objects.nonNull(sysUser.getDept())) ? sysUser.getDept().getCityId() : null);
//           log.info("----userUserId--{},---比较结果为---{}",userUserId, !(userId.longValue() == (userUserId.longValue())));
//        }

        if (ObjectUtil.isNotEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType() != -1) {
            sServiceMain.setTypeBeforeZzIds(sServiceMain.getServiceType().toString());
        } else {
            sServiceMain.setTypeBeforeZzIds(sConfigServiceTypeMapper.selectServiceTypeIdsByWorkPlatFormId(sServiceMain.getWorkPlatformId(), sServiceMain.getServiceCatalogue()));
        }
        if (ObjectUtil.isNotEmpty(sServiceMain.getComplianceAccount()) && sServiceMain.getComplianceAccount().intValue() == 1) {
            sServiceMain.setEnterpriseIdList(sServiceMainService.hgzEnterprise());
        }

        Page<Object> objects = startPageReturn();
        List<SServiceVo> list = sServiceMainService.selectSServiceMainList(sServiceMain);

        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }


    @ApiOperation(value = "查询企业客户列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = SServiceMain.class)})
//    @PreAuthorize(hasPermi = "erp:main:list")
    @GetMapping("/getEnterpriseServiceList")
    public TableDataInfo getEnterpriseServiceList(ServiceMainDto sServiceMain) {

        // 获取登录信息、 用户、角色、权限
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        SysRole sysRole = sysUser.getRoles().get(0);
        sServiceMain.setDataScope(sysRole.getDataScope());
        sServiceMain.setChekRole(1L);
        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)
                && StringUtils.isEmpty(sServiceMain.getCompanyName())) {
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }

        // 如果不为空，并且serviceType值为29； 即为地址续费; servicePoint = 80
        if (!ObjectUtil.isEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType().intValue() == 29) {
            sServiceMain.setServiceCatalogue(null);
            sServiceMain.setServiceType(null);
            sServiceMain.setServicePoint(null);
//            sServiceMain.setServicePointStatus(null);
            sServiceMain.setWorkPlatformId(null);
            sServiceMain.setIsAddress(1L);
            sServiceMain.setChekRole(0L);
        }
        sServiceMain.setOrderRegistOver(1L);
        if (ObjectUtil.isNotEmpty(sServiceMain.getWorkPlatformId()) && (sServiceMain.getWorkPlatformId().intValue() == 3 || sServiceMain.getWorkPlatformId().intValue() == 7)) {
            sServiceMain.setWorkPlatformId(-1L);
        }
        System.out.println("isBD====>" + sServiceMain.getIsBd());

        if (ObjectUtil.isNotEmpty(sServiceMain.getComplianceAccount()) && sServiceMain.getComplianceAccount().intValue() == 1) {
            sServiceMain.setEnterpriseIdList(sServiceMainService.hgzEnterprise());
        }


        Page<Object> objects = startPageReturn();
        List<SServiceVo> listEnterprise = new ArrayList<>();
        if(Objects.nonNull(sServiceMain.getOperateType()) && 1 == sServiceMain.getOperateType()){
            if(CollectionUtils.isNotEmpty(sServiceMain.getSServiceMainIds())){
                listEnterprise = sServiceMainService.getEnterpriseServiceListById(sServiceMain);
            }else {
                return getDataTableAndTotal(new ArrayList<>(), 0L);
            }
        }else {
            listEnterprise = sServiceMainService.getEnterpriseServiceList(sServiceMain);
        }
        long total = objects.getTotal();

        String ids = "";
        for (SServiceVo sServiceVo : listEnterprise) {
            ids += "," + sServiceVo.getId();
        }

        if (!ObjectUtil.isEmpty(ids) && !ids.equals("")) {
            sServiceMain.setIds(ids.replaceFirst(",", ""));
            sServiceMain.setChekRole(0L);
            sServiceMain.setType(1);
//            //服务单增加城市权限
//            if (Objects.nonNull(sysUser)) {
//                Long userId = 202L;
//                Long userUserId = sysUser.getUserId();
//                sServiceMain.setCityId(( !(userId.longValue() == (userUserId.longValue()))  && Objects.nonNull(sysUser.getDept())) ? sysUser.getDept().getCityId() : null);
//                log.info("----userUserId--{},---比较结果为---{}",userUserId, !(userId.longValue() == (userUserId.longValue())));
//            }
            List<SServiceVo> list = sServiceMainService.selectSServiceMainList(sServiceMain);
            return getDataTableAndTotal(list, total);
        }
        return getDataTableAndTotal(new ArrayList<>(), 0L);
    }

    @GetMapping("/sendEmailAccount")
    public AjaxResult sendEmailAccount() throws IOException {
        return AjaxResult.success(sServiceMainService.sendEmailAccount());
    }

    @GetMapping("/getServiceListByEnterpriseId")
    public TableDataInfo getServiceListByEnterpriseId(ServiceByEnterpriseDto serviceByEnterpriseDto) {
        startPage();
        List<SServiceVo> list = sServiceMainService.getServiceListByEnterpriseId(serviceByEnterpriseDto);
        return getDataTable(list);
    }

    @GetMapping("/getKeepAcountType")
    public List<ServiceMainCount> getKeepAcountTypeList() {
        return sServiceMainService.getKeepAcountTypeList();
    }


//    @ApiOperation(value = "查询【请填写功能名称】列表")
//    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ServiceByEnterpriseVo.class)})
////    @PreAuthorize(hasPermi = "erp:main:list")
//    @GetMapping("/getListByEnterprise")
//    public TableDataInfo getListByEnterprise(ServiceByEnterpriseDto serviceByEnterpriseDto)
//    {
//        Page<Object> objects = startPageReturn();
//        List<SServiceVo> list = sServiceMainService.selectSServiceMainListByEnterprise(serviceByEnterpriseDto);
//        long total = objects.getTotal();
//        return getDataTableAndTotal(list, total);
//    }

    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation(value = "导出【请填写功能名称】列表")
    @PreAuthorize(hasPermi = "erp:main:export")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceMainDto sServiceMain) throws IOException {
        sServiceMain.setType(2);
        List<SServiceVo> list = sServiceMainService.selectSServiceMainList(sServiceMain);
        ExcelUtil<SServiceVo> util = new ExcelUtil<SServiceVo>(SServiceVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation(value = "获取【请填写功能名称】详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = SServiceMain.class)})
    @PreAuthorize(hasPermi = "erp:main:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name = "id", value = "【请填写功能名称】id") @PathVariable("id") Long id) {
        return AjaxResult.success(sServiceMainService.selectSServiceMainById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation(value = "新增【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:main:add")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SServiceMain sServiceMain) {
        return toAjax(sServiceMainService.insertSServiceMain(sServiceMain));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation(value = "修改【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:main:edit")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SServiceMain sServiceMain) {
        return toAjax(sServiceMainService.updateSServiceMain(sServiceMain));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "删除【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:main:remove")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sServiceMainService.deleteSServiceMainByIds(ids));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation(value = "新增【请填写功能名称】")
//    @PreAuthorize(hasPermi = "erp:main:add")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping("/operateService")
    public AjaxResult operateService(@RequestBody SServiceRecordDto sServiceRecord) {
        return toAjax(sServiceMainService.operateService(sServiceRecord));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "服务单领取")
//    @PreAuthorize(hasPermi = "erp:main:serviceBDBack")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @GetMapping("/getService/{id}")
    public AjaxResult getService(@PathVariable Long id) {
        return toAjax(sServiceMainService.getService(id));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "BD转回服务单")
    @PreAuthorize(hasPermi = "erp:main:serviceBDBack")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @GetMapping("/serviceBDBack/{id}")
    public AjaxResult serviceBDBack(@PathVariable Long id) {
        return toAjax(sServiceMainService.serviceBDBack(id));
    }

    @ApiOperation(value = "服务单转BD")
    @PreAuthorize(hasPermi = "erp:main:serviceToBD")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @GetMapping("/serviceToBD/{id}")
    public AjaxResult serviceToBD(@PathVariable Long id) {
        return toAjax(sServiceMainService.serviceToBD(id));
    }

    /**
     * 售后转回服务单
     */
    @ApiOperation(value = "售后转回服务单")
    @PreAuthorize(hasPermi = "erp:main:serviceAfterBack")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @GetMapping("/serviceAfterBack/{id}")
    public AjaxResult serviceAfterBack(@PathVariable Long id) {
        return toAjax(sServiceMainService.serviceAfterBack(id));
    }

    @ApiOperation(value = "服务单转售后")
    @PreAuthorize(hasPermi = "erp:main:serviceToAfter")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @GetMapping("/serviceToAfter/{id}")
    public AjaxResult serviceToAfter(@PathVariable Long id) {
        return toAjax(sServiceMainService.serviceToAfter(id));
    }


    /**
     * 成本结算申请
     *
     * @param costSettlementDTO
     * @return
     */
    @ApiOperation(value = "成本结算申请")
//    @PreAuthorize(hasPermi = "erp:main:addCostSettlement")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @PostMapping("/addCostSettlement")
    public AjaxResult addCostSettlement(@RequestBody CostSettlementDTO costSettlementDTO) {
        return toAjax(sServiceMainService.addCostSettlement(costSettlementDTO));
    }


    @ApiOperation(value = "导出增值列表等")
    //@PreAuthorize(hasPermi = "tax:controService:export")
    @PostMapping("/exportServiceVoList")
    public void exportServiceVoList(HttpServletResponse response, @RequestBody ServiceMainDto sServiceMain) throws IOException {
        sServiceMain.setType(2);
        if (6 == sServiceMain.getServiceCatalogue()) {
            List<IncrementCustomerExport> list = new ArrayList<>();
            List<SServiceVo> sServiceVoList = getsServiceVosList(sServiceMain);
            list = sServiceVoList.stream().map(en -> {
                IncrementCustomerExport export = new IncrementCustomerExport();
                BeanUtils.copyProperties(en, export);
                return export;
            }).collect(Collectors.toList());
            ExcelUtil<IncrementCustomerExport> util = new ExcelUtil<>(IncrementCustomerExport.class);
            exportLogService.smInsert(sServiceMain,sServiceVoList);
            util.exportExcel(response, list, "增值数据");

        } else {
            List<SServiceVo> sServiceVoList = getsServiceVosList(sServiceMain);
            ExcelUtil<SServiceVo> util = new ExcelUtil<>(SServiceVo.class);
            exportLogService.smInsert(sServiceMain,sServiceVoList);
            util.exportExcel(response, sServiceVoList, "业务数据");
        }
    }


    @ApiOperation(value = "导出记账客户列表等")
    //@PreAuthorize(hasPermi = "tax:controService:export")
    @PostMapping("/exportBookkeepingCustomer")
    public void exportBookkeepingCustomer(HttpServletResponse response, @RequestBody ServiceMainDto sServiceMain) throws IOException {
        sServiceMain.setType(2);

        JSONArray companyNameList = new JSONArray();
        if (10 == sServiceMain.getServiceType()) {
            //记账客户管理 getEnterpriseServiceList
            List<BookkeepingCustomerExport> list = new ArrayList<>();
            List<SServiceVo> sServiceVoList = getEnterpriseServiceVo(sServiceMain);
            list = sServiceVoList.stream().map(en -> {
                BookkeepingCustomerExport export = new BookkeepingCustomerExport();
                BeanUtils.copyProperties(en, export);
                companyNameList.add(en.getVcCompanyName());
                return export;
            }).collect(Collectors.toList());
            ExcelUtil<BookkeepingCustomerExport> util = new ExcelUtil<BookkeepingCustomerExport>(BookkeepingCustomerExport.class);
            util.exportExcel(response, list, "记账客户管理数据");

        } else if (18 == sServiceMain.getServiceType()) {
            List<TaxControlledCustomerExport> list = new ArrayList<>();
            List<SServiceVo> sServiceVoList = getEnterpriseServiceVo(sServiceMain);
            list = sServiceVoList.stream().map(en -> {
                TaxControlledCustomerExport export = new TaxControlledCustomerExport();
                BeanUtils.copyProperties(en, export);
                companyNameList.add(en.getVcCompanyName());
                return export;
            }).collect(Collectors.toList());
            ExcelUtil<TaxControlledCustomerExport> util = new ExcelUtil<TaxControlledCustomerExport>(TaxControlledCustomerExport.class);
            util.exportExcel(response, list, "税控客户管理数据");
        }

        exportLogService.insertData("财税工作台", sServiceMain.getServiceType().intValue()==10?"记账客户管理":"税控客户管理", companyNameList.toString());

    }


    private List<SServiceVo> getEnterpriseServiceVo(ServiceMainDto sServiceMain) {
        List<SServiceVo> sServiceVoList = new ArrayList<>();

        // 获取登录信息、 用户、角色、权限
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        SysRole sysRole = sysUser.getRoles().get(0);
        sServiceMain.setDataScope(sysRole.getDataScope());
        sServiceMain.setChekRole(1L);

        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)) {
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }
//        if (ObjectUtil.isEmpty(sServiceMain.getCityId())) {
//            sServiceMain.setCityId(sysUser.getDept().getCityId());
//        }

        // 如果不为空，并且serviceType值为29； 即为地址续费; servicePoint = 80
        if (!ObjectUtil.isEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType().intValue() == 29) {
            sServiceMain.setServiceCatalogue(null);
            sServiceMain.setServiceType(null);
            sServiceMain.setServicePoint(null);
            //sServiceMain.setServicePointStatus(null);
            sServiceMain.setWorkPlatformId(null);
            sServiceMain.setIsAddress(1L);
            sServiceMain.setChekRole(0L);
        }
        sServiceMain.setOrderRegistOver(1L);
        if (ObjectUtil.isNotEmpty(sServiceMain.getWorkPlatformId()) && (sServiceMain.getWorkPlatformId().intValue() == 3 || sServiceMain.getWorkPlatformId().intValue() == 7)) {
            sServiceMain.setWorkPlatformId(-1L);
        }
        System.out.println("isBD====>" + sServiceMain.getIsBd());


        if (ObjectUtil.isNotEmpty(sServiceMain.getComplianceAccount()) && sServiceMain.getComplianceAccount().intValue() == 1) {
            sServiceMain.setEnterpriseIdList(sServiceMainService.hgzEnterprise());
        }
        List<SServiceVo> listEnterprise = sServiceMainService.getEnterpriseServiceList(sServiceMain);
        String ids = "";
        for (SServiceVo sServiceVo : listEnterprise) {
            ids += "," + sServiceVo.getId();
        }

        if (!ObjectUtil.isEmpty(ids) && !ids.equals("")) {
            sServiceMain.setIds(ids.replaceFirst(",", ""));
            sServiceMain.setChekRole(0L);
            sServiceVoList = sServiceMainService.selectSServiceMainList(sServiceMain);
        }
        return sServiceVoList;
    }

    private List<SServiceVo> getsServiceVosList(ServiceMainDto sServiceMain) {
        if (!ObjectUtil.isEmpty(sServiceMain.getServicePointStatus())) {
            SConfigServicePointStatus servicePointStatus = sConfigServicePointStatusMapper.selectSConfigServicePointStatusById(sServiceMain.getServicePointStatus());
            if (servicePointStatus.getIsEnd() == 1 && !ObjectUtil.isEmpty(servicePointStatus.getNextService()) && servicePointStatus.getNextService().intValue() == 9) {
                String ids = sServiceMainMapper.getIdsByTypeBeforeZz(sServiceMain.getServiceType());
                if (ObjectUtil.isNotEmpty(ids)) {
                    sServiceMain.setIds(ids);
                    sServiceMain.setServiceCatalogue(null);
                    sServiceMain.setServiceType(null);
                    sServiceMain.setServicePoint(null);
                    sServiceMain.setServicePointStatus(null);
                    sServiceMain.setWorkPlatformId(null);
                    sServiceMain.setChekRole(0L);
                }
            }
        }

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        SysRole sysRole = sysUser.getRoles().get(0);

//        if (ObjectUtil.isEmpty(sServiceMain.getCityId())) {
//            sServiceMain.setCityId(sysUser.getDept().getCityId());
//        }
        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)) {
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }

        sServiceMain.setDataScope(sysRole.getDataScope());
        if (!ObjectUtil.isEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType().intValue() == 29) {
            sServiceMain.setServiceCatalogue(null);
            sServiceMain.setServiceType(null);
            sServiceMain.setServicePoint(null);
            sServiceMain.setServicePointStatus(null);
            sServiceMain.setWorkPlatformId(null);
            sServiceMain.setIsAddress(1L);
        }
        sServiceMain.setOrderRegistOver(1L);
        if (ObjectUtil.isNotEmpty(sServiceMain.getWorkPlatformId()) &&
                ((sServiceMain.getWorkPlatformId().intValue() == 3 && sServiceMain.getIsBd().intValue() == 1) || sServiceMain.getWorkPlatformId().intValue() == 7)
        ) {
            sServiceMain.setWorkPlatformId(-1L);
        }
        System.out.println("isBD====>" + sServiceMain.getIsBd());
        //城市筛选
//        sServiceMain.setCityIdCondition(sServiceMain.getCityId());
//        sServiceMain.setCityId(null);
//        if (Objects.nonNull(sysUser)) {
//            Long userId = 202L;
//            Long userUserId = sysUser.getUserId();
//            sServiceMain.setCityId(( !(userId.longValue() == (userUserId.longValue()))  && Objects.nonNull(sysUser.getDept())) ? sysUser.getDept().getCityId() : null);
//            log.info("----userUserId--{},---比较结果为---{}",userUserId, !(userId.longValue() == (userUserId.longValue())));
//        }
        if (ObjectUtil.isNotEmpty(sServiceMain.getServiceType()) && sServiceMain.getServiceType() != -1) {
            sServiceMain.setTypeBeforeZzIds(sServiceMain.getServiceType().toString());
        } else {
            sServiceMain.setTypeBeforeZzIds(sConfigServiceTypeMapper.selectServiceTypeIdsByWorkPlatFormId(sServiceMain.getWorkPlatformId(), sServiceMain.getServiceCatalogue()));
        }

        if (ObjectUtil.isNotEmpty(sServiceMain.getComplianceAccount()) && sServiceMain.getComplianceAccount().intValue() == 1) {
            sServiceMain.setEnterpriseIdList(sServiceMainService.hgzEnterprise());
        }

        List<SServiceVo> list = sServiceMainService.selectSServiceMainList(sServiceMain);
        return list;
    }

    @ApiOperation(value = "业支工作台发送邮件")
    @GetMapping("/sendServiceMainMail")
    public AjaxResult sendServiceMainMail(){
        sServiceMainService.sendServiceMainMail();
        return AjaxResult.success();
    }


    /***
     * wangyu20230317
     * 修改税控周期
     */
    @GetMapping("/test111")
    public void test111(){
        List<Map<String,Long>> arr = sServiceMainMapper.getNumEnterpriseIds();
        List<Long> listUpdate = new ArrayList<>();
        for (int i = 0; i < arr.size(); i++) {
            Map<String,Long> _map = arr.get(i);
            List<SServiceVo> list = sServiceMainMapper.getServiceMainB(_map.get("num_enterprise_id"));
            Long first = 0L;
            Date start1 = null;
            Date end1 = null;
            Date start2 = null;
            Date end2 = null;
            Long month = 0L;
            if (ObjectUtil.isNotNull(list) && list.size() == 2) {
                first = list.get(0).getId();
                start1 = ObjectUtil.isNotNull(list.get(0).getAcStart()) ? list.get(0).getAcStart() : null;
                end1 = ObjectUtil.isNotNull(list.get(0).getAcEnd()) ? list.get(0).getAcEnd() : null;
                start2 = ObjectUtil.isNotNull(list.get(1).getAcStart()) ? list.get(1).getAcStart() : null;
                end2 = ObjectUtil.isNotNull(list.get(1).getAcEnd()) ? list.get(1).getAcEnd() : null;
                month = ObjectUtil.isNotNull(list.get(0).getMonths()) ? list.get(0).getMonths() : 0L;
            }

            if (first.intValue() != 0 && month.intValue() != 0 &&
                    ObjectUtil.isNotNull(start1) && ObjectUtil.isNotNull(start2)
                    && ObjectUtil.isNotNull(end1) && ObjectUtil.isNotNull(end2)
                    && !DateUtil.plusDays(end2,1).equals(start1)
            ) {
                SServiceMain main = new SServiceMain();
                main.setId(first);
                main.setAcStart(DateUtil.plusDays(end2,1));
                main.setAcEnd(DateUtil.plusDays(DateUtil.plusMonths(main.getAcStart(),month),-1));
//
//
                sServiceMainMapper.updateShuiKongZhouqi(main.getId(),main.getAcStart(),main.getAcEnd());

                logger.info(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, start2)+"-----"+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, end2));
                logger.info(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, start1)+"-----"+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, end1) + "=========" + month);
                logger.info(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, main.getAcStart())+"-----"+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, main.getAcEnd()));
                logger.info("-----------------------------------");

                listUpdate.add(first);
//                return;
            }
        }
        logger.info("list==="+listUpdate);
        logger.info("list==="+listUpdate.size());
    }

    @ApiOperation(value = "财税工作台,记账,税控更换会计")
    @GetMapping("/renewalAccountUser")
    public AjaxResult renewalAccountUser(
            @ApiParam(name="fromUser",value="来源会计") @RequestParam(name = "fromUser") Long fromUser,
            @ApiParam(name="toUser",value="目标会计")  @RequestParam(name = "toUser") Long toUser,
            @ApiParam(name="serviceType",value="服务类型") @RequestParam(name = "serviceType") Long serviceType){

        return AjaxResult.success(sServiceMainService.renewalAccountUser(fromUser, toUser, serviceType));
    }

    @ApiOperation(value = "记账客户列表批量更换会计")
    @GetMapping("/updateAccountBySMIds")
    public AjaxResult updateAccountBySMIds(
            @ApiParam(name="ids",value="服务单Ids") @RequestParam(name = "ids") List<Long> ids,
            @ApiParam(name="toUser",value="目标会计")  @RequestParam(name = "toUser") Long toUser,
            @ApiParam(name="serviceType",value="服务类型") @RequestParam(name = "serviceType") Long serviceType){

        return AjaxResult.success(sServiceMainService.updateAccountBySMIds(ids, toUser, serviceType));
    }


    /***
     * 历史会计数据更新，上线删除，本地运行方法
     */
    @GetMapping("/updateAccountIdWithEnterpriseDatil")
    public void updateAccountIdWithEnterpriseDatil(){
        List<Map<String,Object>> list = sServiceMainMapper.updateAccountIdWithEnterpriseDatil();
        for (int i = 0; i < list.size(); i++) {
            Long enterpriseId = (Long) list.get(i).get("num_enterprise_id");
            String accountId = (String) list.get(i).get("account_id");
            if (ObjectUtil.isNotNull(enterpriseId) && ObjectUtil.isNotNull(accountId)) {
                ErpEnterpriseDetail erpEnterpriseDetail = erpEnterpriseDetailMapper.selectEnterpriseInfoByEnterpriseId(enterpriseId);
                if (ObjectUtil.isNotNull(erpEnterpriseDetail)) {
                    erpEnterpriseDetail.setAccountId(accountId);
                    erpEnterpriseDetailMapper.updateErpBizDetail(erpEnterpriseDetail);
                }
            } else {
                logger.info("enterpriseId===>"+enterpriseId+"--------------"+accountId);
            }
        }
    }

    @ApiOperation(value = "执照转内部")
    @GetMapping("/licenseIn/{licenseId}")
    public AjaxResult licenseIn(@PathVariable Long licenseId) {
        return toAjax(sServiceMainService.licenseIn(licenseId));
    }

    @ApiOperation(value = "获取创新工作台信息收集")
    @GetMapping("/getSmInfomation/{serviceId}")
    public AjaxResult getSmInfomation(@PathVariable Long serviceId) {
        return AjaxResult.success(sServiceMainService.getSmInfomation(serviceId));
    }

    @ApiOperation(value = "保存创新工作台信息收集")
    @PostMapping("/updateSmInfomation")
    public AjaxResult updateSmInfomation(@RequestBody SServiceInformation sServiceInformation) {
        return toAjax(sServiceMainService.updateSmInfomation(sServiceInformation));
    }

    @ApiOperation(value = "服务转回")
    @PostMapping("/serviceTypeBack")
    public AjaxResult serviceTypeBack(@RequestBody ServiceMainDto sServiceMain) {
        return toAjax(sServiceMainService.serviceTypeBack(sServiceMain.getId()));
    }

    @ApiOperation(value = "产品服务单时效预警")
    @GetMapping("/serviceProductAgeing")
    public AjaxResult serviceProductAgeing() {

        sServiceMainService.serviceProductAgeing();
        return AjaxResult.success();
    }

    /**
     * 发送短信。（链接短信、验证码短信。）
     *
     * @param orderId 订单标识。
     * @param phone   手机号。
     * @return 返回发送结果。
     * @since 2022-07-19 16:29:54
     */
    @GetMapping("/pointSendMessage")
    @ApiOperation("发送短信。（链接短信、验证码短信。）")
    public AjaxResult pointSendMessage(String mainId, Long pointId) {
        sServiceMainService.pointSendMessage(mainId, pointId);
        return AjaxResult.success();
    }

    @GetMapping("/historyOperateAccount")
    @ApiOperation("记账历史服务，分配会计")
    public AjaxResult historyOperateAccount(Long mainId, Long userId) {
        return AjaxResult.success(sServiceMainService.historyOperateAccount(mainId, userId));
    }



    @GetMapping("/isBDOperateUser")
    @ApiOperation("记账历史服务，分配会计")
    public AjaxResult isBDOperateUser(@RequestParam Long mainId, @RequestParam Long userId) {
        return AjaxResult.success(sServiceMainService.isBDOperateUser(mainId, userId));
    }

    @PostMapping("/updateProductWithZC")
    @ApiOperation("注册服务更换产品")
    public AjaxResult updateProductWithZC(@RequestBody JSONObject obj) {
        return AjaxResult.success(sServiceMainService.updateProductWithZC(obj));
    }
    @PostMapping("/createInternalTypeService")
    @ApiOperation("创建内部服务单")
    public AjaxResult createInternalTypeService(@RequestBody JSONObject obj) {
        return AjaxResult.success(sServiceMainService.createInternalTypeService(obj));
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "内部服务完结表")
    @GetMapping("/internalServiceStatisticsList")
    public TableDataInfo internalServiceStatisticsList(InternalServiceStatistics internalServiceStatistics)
    {
        Page<Object> objects = startPageReturn();
        List<InternalServiceStatistics> list = sServiceMainService.selectInternalServiceStatisticsList(internalServiceStatistics);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    @ApiOperation(value = "导出内部服务完结列表")
    @PostMapping("/internalServiceStatisticsListExport")
    public void internalServiceStatisticsListExport(HttpServletResponse response, @RequestBody InternalServiceStatistics internalServiceStatistics) throws IOException
    {
        List<InternalServiceStatistics> list = sServiceMainService.selectInternalServiceStatisticsList(internalServiceStatistics);
        ExcelUtil<InternalServiceStatistics> util = new ExcelUtil<InternalServiceStatistics>(InternalServiceStatistics.class);
        util.exportExcel(response, list, "内部服务完结数据");

        JSONArray idList = new JSONArray();
        if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                idList.add(list.get(i).getId());
            }
        }
        exportLogService.insertData("内部服务单工作台", "完结表", idList.toString());
    }

    @ApiOperation(value = "查询地址续费客户列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = SServiceMain.class)})
    @GetMapping("/getAddressEnterpriseList")
    public Map<String, Object> getAddressEnterpriseList(ServiceMainDto sServiceMain) {
        Map<String, Object> returnObj = new HashMap<>();
        returnObj.put("code", 200);
        returnObj.put("msg", "查询成功");

        // 获取登录信息、 用户、角色、权限
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)) {
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }
        List<SServiceVo> listEnterprise = sServiceMainService.getAddressEnterpriseList(sServiceMain);

        List<Long> idList = new ArrayList<>();
        List<Long> enterpriseIdList = new ArrayList<>();
        for (SServiceVo sServiceVo : listEnterprise) {
            idList.add(sServiceVo.getId());
            if (!enterpriseIdList.contains(sServiceVo.getNumEnterpriseId())) {
                enterpriseIdList.add(sServiceVo.getNumEnterpriseId());
            }
        }
        if (!ObjectUtil.isEmpty(idList) && idList.size() > 0 && !ObjectUtil.isEmpty(enterpriseIdList) && enterpriseIdList.size() > 0) {

            //每个企业下最新的一条地址周期服务单ID
            List<SServiceVo> lastList = new ArrayList<>();
            if (enterpriseIdList.size() > 0) {
                lastList = sServiceMainMapper.getLastAddressByEnterpriseIdList(enterpriseIdList);
            }

            //未续费的地址周期服务单ID
            List<Long> waitRenewIdList = new ArrayList<>();
            //已续费的地址周期服务单ID
            List<Long> renewIdList = new ArrayList<>();
            List<Long> renewFeeIdList = new ArrayList<>();
            //超期3个月未续费
            List<Long> overRenewIdList = new ArrayList<>();
            for (int i = 0; i < listEnterprise.size(); i++) {
                SServiceVo vo = listEnterprise.get(i);
                for (int j = 0; j < lastList.size(); j++) {
                    SServiceVo lastVo = lastList.get(j);
                    if (vo.getNumEnterpriseId().intValue() == lastVo.getNumEnterpriseId().intValue()) {
                        if (lastVo.getId().intValue() == vo.getId().intValue()) {
                            waitRenewIdList.add(vo.getId());
                            if (ObjectUtil.isNotEmpty(lastVo.getAddressEnd()) && DateUtils.addMonths(lastVo.getAddressEnd(), 3).before(new Date())) {
                                overRenewIdList.add(vo.getId());
                            }
                        } else {
                            renewIdList.add(vo.getId());
                            renewFeeIdList.add(lastVo.getId());
                        }
                    }
                }
            }

            List<Long> selectIdList = new ArrayList<>();
            if (ObjectUtil.isEmpty(sServiceMain.getRenewStatus())) {
                selectIdList = idList;
            } else {
                if (sServiceMain.getRenewStatus().intValue() == 1) {
                    selectIdList = renewIdList;
                } else if (sServiceMain.getRenewStatus().intValue() == 2) {
                    selectIdList = waitRenewIdList;
                } else if (sServiceMain.getRenewStatus().intValue() == 3) {
                    selectIdList = overRenewIdList;
                }
            }


            returnObj.put("total", selectIdList.size());
            returnObj.put("waitNumber", listEnterprise.size());
            returnObj.put("renewNumber", renewIdList.size());
            returnObj.put("overNumber", overRenewIdList.size());
            returnObj.put("renewMoney", new BigDecimal("0"));
            if (renewIdList.size() > 0) {
                returnObj.put("renewMoney", sServiceMainMapper.getRenewFeeByIdList(renewFeeIdList));
            }

            if (ObjectUtil.isNotEmpty(selectIdList) && selectIdList.size() > 0) {
                Page<Object> objects = startPageReturn();
                ServiceMainDto dto = new ServiceMainDto();
                dto.setIds(StringUtils.join(selectIdList, ","));
                dto.setIsAddress(1L);
                dto.setChekRole(0L);
                dto.setType(1);
                List<SServiceVo> list = sServiceMainService.selectSServiceMainList(dto);
                for (int i = 0; i < list.size(); i++) {
                    if (waitRenewIdList.contains(list.get(i).getId())) {
                        list.get(i).setRenewStatusName("未续费");
                    } else {
                        list.get(i).setRenewStatusName("已续费");
                    }
                }
                returnObj.put("rows", list);
                return returnObj;
            }
        }

        returnObj.put("rows", new ArrayList<>());

        return returnObj;
    }


    @GetMapping("/getAccountLossReason")
    @ApiOperation("获取流失原因列表。")
    public TableDataInfo getAccountLossReason() {
        startPage();
        List<AccountLossReason> list = sServiceMainService.getAccountLossReason();
        return getDataTable(list);
    }

    @ApiOperation(value = "会计离职给会计主管发送邮件")
    @PostMapping("/sendEmailByLiZhiAccount")
    public AjaxResult sendEmailByLiZhiAccount(@RequestParam("userId") Long userId) {
        sServiceMainService.sendEmailByLiZhiAccount(userId);
        return AjaxResult.success();
    }
    // TODO 根据城市、区域获取地点。
    @GetMapping("/getCity")
    public AjaxResult getCity(@RequestParam("parentIdList") List<Long> parentIdList){
        List<ComDictRegion> city = sServiceMainService.getCity(parentIdList);
        return AjaxResult.success(city);
    }



    /**
     * 绑定代理商
     */
    @PostMapping("/bindAgent")
    public AjaxResult bindAgent(@RequestBody SServiceMain sServiceMain) {
        return toAjax(sServiceMainMapper.bindAgent(sServiceMain.getId(), sServiceMain.getAgentId()));
    }


    @ApiOperation(value = "址续费客户列表导出")
    @PostMapping("/exportAddressEnterpriseList")
    public void exportAddressEnterpriseList(HttpServletResponse response,@RequestBody ServiceMainDto sServiceMain) throws IOException {

        List<SServiceAddressVo> returnList = new ArrayList<>();



        // 获取登录信息、 用户、角色、权限
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if ((ObjectUtil.isEmpty(sServiceMain.getCityIdList()) || sServiceMain.getCityIdList().size() == 0)) {
            sServiceMain.setCityIdList(Arrays.asList(sysUser.getDept().getCityId()));
        }
        List<SServiceVo> listEnterprise = sServiceMainService.getAddressEnterpriseList(sServiceMain);

        List<Long> idList = new ArrayList<>();
        List<Long> enterpriseIdList = new ArrayList<>();
        for (SServiceVo sServiceVo : listEnterprise) {
            idList.add(sServiceVo.getId());
            if (!enterpriseIdList.contains(sServiceVo.getNumEnterpriseId())) {
                enterpriseIdList.add(sServiceVo.getNumEnterpriseId());
            }
        }
        if (!ObjectUtil.isEmpty(idList) && idList.size() > 0 && !ObjectUtil.isEmpty(enterpriseIdList) && enterpriseIdList.size() > 0) {

            //每个企业下最新的一条地址周期服务单ID
            List<SServiceVo> lastList = new ArrayList<>();
            if (enterpriseIdList.size() > 0) {
                lastList = sServiceMainMapper.getLastAddressByEnterpriseIdList(enterpriseIdList);
            }

            //未续费的地址周期服务单ID
            List<Long> waitRenewIdList = new ArrayList<>();
            //已续费的地址周期服务单ID
            List<Long> renewIdList = new ArrayList<>();
            List<Long> renewFeeIdList = new ArrayList<>();
            //超期3个月未续费
            List<Long> overRenewIdList = new ArrayList<>();
            for (int i = 0; i < listEnterprise.size(); i++) {
                SServiceVo vo = listEnterprise.get(i);
                for (int j = 0; j < lastList.size(); j++) {
                    SServiceVo lastVo = lastList.get(j);
                    if (vo.getNumEnterpriseId().intValue() == lastVo.getNumEnterpriseId().intValue()) {
                        if (lastVo.getId().intValue() == vo.getId().intValue()) {
                            waitRenewIdList.add(vo.getId());
                            if (ObjectUtil.isNotEmpty(lastVo.getAddressEnd()) && DateUtils.addMonths(lastVo.getAddressEnd(), 3).before(new Date())) {
                                overRenewIdList.add(vo.getId());
                            }
                        } else {
                            renewIdList.add(vo.getId());
                            renewFeeIdList.add(lastVo.getId());
                        }
                    }
                }
            }

            List<Long> selectIdList = new ArrayList<>();
            if (ObjectUtil.isEmpty(sServiceMain.getRenewStatus())) {
                selectIdList = idList;
            } else {
                if (sServiceMain.getRenewStatus().intValue() == 1) {
                    selectIdList = renewIdList;
                } else if (sServiceMain.getRenewStatus().intValue() == 2) {
                    selectIdList = waitRenewIdList;
                } else if (sServiceMain.getRenewStatus().intValue() == 3) {
                    selectIdList = overRenewIdList;
                }
            }

            if (ObjectUtil.isNotEmpty(selectIdList) && selectIdList.size() > 0) {
                ServiceMainDto dto = new ServiceMainDto();
                dto.setIds(StringUtils.join(selectIdList, ","));
                dto.setIsAddress(1L);
                dto.setChekRole(0L);
                dto.setType(1);
                List<SServiceVo> list = sServiceMainService.selectSServiceMainList(dto);
                for (int i = 0; i < list.size(); i++) {
                    if (waitRenewIdList.contains(list.get(i).getId())) {
                        list.get(i).setRenewStatusName("未续费");
                    } else {
                        list.get(i).setRenewStatusName("已续费");
                    }
                    SServiceAddressVo addressVo = new SServiceAddressVo();
                    BeanUtils.copyProperties(list.get(i), addressVo);
                    returnList.add(addressVo);
                }
            }
        }
        ExcelUtil<SServiceAddressVo> util = new ExcelUtil<SServiceAddressVo>(SServiceAddressVo.class);
        util.exportExcel(response, returnList, "【请填写功能名称】数据");
    }


    @ApiOperation(value = "工商年检申报")
    @GetMapping("/yearInspect")
    public AjaxResult yearInspect(@RequestParam(name = "numEnterpriseId") Long numEnterpriseId) {
        return toAjax(sServiceMainService.yearInspect(numEnterpriseId));
    }

    @ApiOperation(value = "记账客户资料交接")
    @GetMapping("/dataHandover")
    public AjaxResult dataHandover(@RequestParam(name = "numEnterpriseId") Long numEnterpriseId,
                                   @RequestParam(name = "dataHandoverStatus") Long dataHandoverStatus,
                                   String dataHandoverDate) {
        return toAjax(sServiceMainService.dataHandover(numEnterpriseId, dataHandoverStatus, dataHandoverDate));
    }


    @ApiOperation(value = "更新年检和资质延期")
    @PostMapping("/updateQualificationsExtension")
    public AjaxResult updateQualificationsExtension(@RequestBody SServiceMain sServiceMain) {
        return toAjax(sServiceMainService.updateQualificationsExtension(sServiceMain));
    }

    @ApiOperation(value = "资质延期列表")
    @GetMapping("/qualificationsExtensionList")
    public TableDataInfo qualificationsExtensionList(QualificationsExtensionDto qualificationsExtensionDto)
    {
        startPage();
        List<QualificationsExtensionVo> list = sServiceMainService.qualificationsExtensionList(qualificationsExtensionDto);
        return getDataTable(list);
    }

    @ApiOperation(value = "年检和资质延期流失")
    @PostMapping("/lsQualificationsExtension")
    public AjaxResult lsQualificationsExtension(@RequestBody SServiceMain sServiceMain) {
        return toAjax(sServiceMainService.lsQualificationsExtension(sServiceMain));
    }

    @ApiOperation(value = "定时修改工商年检状态")
    @GetMapping("/qualificationsExtensionLossTask")
    public AjaxResult qualificationsExtensionLossTask() {
        return toAjax(sServiceMainService.qualificationsExtensionLossTask());
    }
}
