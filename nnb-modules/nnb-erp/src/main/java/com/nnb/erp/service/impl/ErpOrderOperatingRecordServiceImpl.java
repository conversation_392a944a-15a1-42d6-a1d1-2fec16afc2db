package com.nnb.erp.service.impl;

import java.util.List;

import com.nnb.erp.constant.enums.OrderOperationTypeEnum;
import com.nnb.erp.domain.vo.ErpOrderOperationRecordForOrderDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpOrderOperatingRecordMapper;
import com.nnb.erp.domain.ErpOrderOperatingRecord;
import com.nnb.erp.service.IErpOrderOperatingRecordService;

/**
 * 订单操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-16
 */
@Service
public class ErpOrderOperatingRecordServiceImpl implements IErpOrderOperatingRecordService 
{
    @Autowired
    private ErpOrderOperatingRecordMapper erpOrderOperatingRecordMapper;

    /**
     * 查询订单操作记录
     * 
     * @param id 订单操作记录主键
     * @return 订单操作记录
     */
    @Override
    public ErpOrderOperatingRecord selectErpOrderOperatingRecordById(Long id)
    {
        return erpOrderOperatingRecordMapper.selectErpOrderOperatingRecordById(id);
    }

    /**
     * 查询订单操作记录列表
     * 
     * @param erpOrderOperatingRecord 订单操作记录
     * @return 订单操作记录
     */
    @Override
    public List<ErpOrderOperatingRecord> selectErpOrderOperatingRecordList(ErpOrderOperatingRecord erpOrderOperatingRecord)
    {
        return erpOrderOperatingRecordMapper.selectErpOrderOperatingRecordList(erpOrderOperatingRecord);
    }

    /**
     * 新增订单操作记录
     * 
     * @param erpOrderOperatingRecord 订单操作记录
     * @return 结果
     */
    @Override
    public int insertErpOrderOperatingRecord(ErpOrderOperatingRecord erpOrderOperatingRecord)
    {
        return erpOrderOperatingRecordMapper.insertErpOrderOperatingRecord(erpOrderOperatingRecord);
    }

    /**
     * 修改订单操作记录
     * 
     * @param erpOrderOperatingRecord 订单操作记录
     * @return 结果
     */
    @Override
    public int updateErpOrderOperatingRecord(ErpOrderOperatingRecord erpOrderOperatingRecord)
    {
        return erpOrderOperatingRecordMapper.updateErpOrderOperatingRecord(erpOrderOperatingRecord);
    }

    /**
     * 批量删除订单操作记录
     * 
     * @param ids 需要删除的订单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderOperatingRecordByIds(Long[] ids)
    {
        return erpOrderOperatingRecordMapper.deleteErpOrderOperatingRecordByIds(ids);
    }

    /**
     * 删除订单操作记录信息
     * 
     * @param id 订单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderOperatingRecordById(Long id)
    {
        return erpOrderOperatingRecordMapper.deleteErpOrderOperatingRecordById(id);
    }

    /**
     * 获取指定订单的操作记录。
     *
     * @param orderId 订单标识。
     * @return 返回操作记录集合。
     * <AUTHOR>
     * @since 2022-04-26 17:15:44
     */
    @Override
    public List<ErpOrderOperationRecordForOrderDetailVO> getOperationRecordByOrderId(Long orderId, List<Long> typeList, String orderBy) {
        List<ErpOrderOperationRecordForOrderDetailVO> operationRecordList = erpOrderOperatingRecordMapper.getOperationRecordByOrderId(orderId, typeList, orderBy);
        for (ErpOrderOperationRecordForOrderDetailVO operationRecord : operationRecordList) {
            operationRecord.setOperationTypeStr(OrderOperationTypeEnum.getStringByInt(operationRecord.getOperationTypeInt()));
        }

        return operationRecordList;
    }
}
