package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpCouponDept;
import com.nnb.erp.service.IErpCouponDeptService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 优惠券适用部门Controller
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@RestController
@RequestMapping("/ErpCouponDept")
@Api(tags = "ErpCouponDeptController", description = "优惠券适用部门")
public class ErpCouponDeptController extends BaseController
{
    @Autowired
    private IErpCouponDeptService erpCouponDeptService;

    /**
     * 查询优惠券适用部门列表
     */
    @ApiOperation(value = "查询优惠券适用部门列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpCouponDept.class)})
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpCouponDept erpCouponDept)
    {
        startPage();
        List<ErpCouponDept> list = erpCouponDeptService.selectErpCouponDeptList(erpCouponDept);
        return getDataTable(list);
    }

    /**
     * 导出优惠券适用部门列表
     */
    @ApiOperation(value = "导出优惠券适用部门列表")
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:export")
    //@Log(title = "优惠券适用部门", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpCouponDept erpCouponDept) throws IOException
    {
        List<ErpCouponDept> list = erpCouponDeptService.selectErpCouponDeptList(erpCouponDept);
        ExcelUtil<ErpCouponDept> util = new ExcelUtil<ErpCouponDept>(ErpCouponDept.class);
        util.exportExcel(response, list, "优惠券适用部门数据");
    }

    /**
     * 获取优惠券适用部门详细信息
     */
    @ApiOperation(value = "获取优惠券适用部门详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpCouponDept.class)})
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="优惠券适用部门id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpCouponDeptService.selectErpCouponDeptById(id));
    }

    /**
     * 新增优惠券适用部门
     */
    @ApiOperation(value = "新增优惠券适用部门")
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:add")
    //@Log(title = "优惠券适用部门", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpCouponDept erpCouponDept)
    {
        return toAjax(erpCouponDeptService.insertErpCouponDept(erpCouponDept));
    }

    /**
     * 修改优惠券适用部门
     */
    @ApiOperation(value = "修改优惠券适用部门")
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:edit")
    //@Log(title = "优惠券适用部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpCouponDept erpCouponDept)
    {
        return toAjax(erpCouponDeptService.updateErpCouponDept(erpCouponDept));
    }

    /**
     * 删除优惠券适用部门
     */
    @ApiOperation(value = "删除优惠券适用部门")
    @PreAuthorize(hasPermi = "erp:ErpCouponDept:remove")
    //@Log(title = "优惠券适用部门", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpCouponDeptService.deleteErpCouponDeptByIds(ids));
    }
}
