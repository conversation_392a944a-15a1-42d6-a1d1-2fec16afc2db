package com.nnb.erp.service;

import java.io.IOException;
import java.util.List;
import com.nnb.erp.domain.ErpProductConfiguration;
import com.nnb.erp.domain.vo.ErpProductConfigurationVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 产品库配置信息Service接口
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
public interface IErpProductConfigurationService
{
    /**
     * 查询产品库配置信息
     *
     * @param id 产品库配置信息主键
     * @return 产品库配置信息
     */
    public ErpProductConfiguration selectErpProductConfigurationById(Long id);

    /**
     * 查询产品库配置信息列表
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 产品库配置信息集合
     */
    public List<ErpProductConfiguration> selectErpProductConfigurationList(ErpProductConfiguration erpProductConfiguration);

    /**
     * 新增产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    public int insertErpProductConfiguration(ErpProductConfiguration erpProductConfiguration);

    /**
     * 修改产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    public int updateErpProductConfiguration(ErpProductConfiguration erpProductConfiguration);

    /**
     * 批量删除产品库配置信息
     *
     * @param ids 需要删除的产品库配置信息主键集合
     * @return 结果
     */
    public int deleteErpProductConfigurationByIds(Long[] ids);

    /**
     * 删除产品库配置信息信息
     *
     * @param id 产品库配置信息主键
     * @return 结果
     */
    public int deleteErpProductConfigurationById(Long id);

    public List<ErpProductConfigurationVo> getProductConfigurationById(Long id);

    void importProductConfigExcel(MultipartFile file) throws IOException;
}
