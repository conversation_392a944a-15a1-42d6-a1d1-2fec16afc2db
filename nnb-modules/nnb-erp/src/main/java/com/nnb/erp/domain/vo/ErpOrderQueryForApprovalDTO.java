package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 订单查询条件，用于订单审核列表，DTO。
 *
 * <AUTHOR>
 * @since 2022/4/8 13:45
 */
@Data
public class ErpOrderQueryForApprovalDTO {

    /**
     * 关键词搜索：订单编号精确搜索，联系人、公司名称、签约人模糊搜索。
     */
    @ApiModelProperty("关键词搜索：订单编号精确搜索，联系人、公司名称、签约人模糊搜索。")
    private String keyword;

    /**
     * 订单来源：0CRM，1小程序。
     */
    @ApiModelProperty("订单来源：0CRM，1小程序。")
    private Integer source;

    /**
     * 所属部门。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("所属部门。")
    private Long deptId;

    /**
     * 是否有尾款：0无尾款，1有尾款。
     */
    @ApiModelProperty("是否有尾款：0无尾款，1有尾款。")
    private Integer isHaveLastPrice;

    /**
     * 订单创建起始时间，字符串。
     */
    @ApiModelProperty("订单创建起始时间，字符串。")
    private String createTimeBegin;

    /**
     * 订单创建结束时间，字符串。
     */
    @ApiModelProperty("订单创建结束时间，字符串。")
    private String createTimeEnd;

    /**
     * 筛选类型 1:提单未审核，2修改待审核，3退款待审核，4作废待审核，5已审核。
     */
    @ApiModelProperty("筛选类型 1:提单待审核，2修改待审核，3退款待审核，4作废待审核，5已审核。")
    private Integer handleType;

    /**
     * 当前用户标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("当前用户标识。")
    private Long userId;

}
