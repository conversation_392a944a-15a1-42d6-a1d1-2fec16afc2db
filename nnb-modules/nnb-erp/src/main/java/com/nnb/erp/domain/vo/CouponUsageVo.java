package com.nnb.erp.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2022-12-01
 * @Version: 1.0
 */
@Data
public class CouponUsageVo implements Serializable {


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNum;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品原价
     */
    private BigDecimal originalPrice;

    /**
     * 产品实收
     */
    private BigDecimal realPrice;

    /**
     * 优惠券ID
     */
    private Long discountId;

    /**
     * 优惠额度
     */
    private BigDecimal discountPrice;

    /**
     * 签约部门
     */
    private String sellerDeptName;

    /**
     * 签约人
     */
    private String sellerUserName;

    /**
     * 创建时间
     */
    private String createTime;

}
