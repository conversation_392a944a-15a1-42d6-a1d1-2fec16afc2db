package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 微信小程序-用户和企业绑定对象 weChat_applet_bind
 * 
 * <AUTHOR>
 * @date 2023-04-17
 */
@ApiModel(value="WechatAppletBind",description="微信小程序-用户和企业绑定对象")
public class WechatAppletBind extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 微信小程序唯一标识id */
    @ApiModelProperty("微信小程序唯一标识id")
    private String openId;

    /** 企业id，对应erp_client.id */
    @ApiModelProperty("企业id，对应erp_client.id")
    private Long clientid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String name;

    /** 微信小程序客户手机号 */
    @Excel(name = "微信小程序客户手机号")
    @ApiModelProperty("微信小程序客户手机号")
    private String phone;

    /** 请求的appId */
    @Excel(name = "请求的appId")
    @ApiModelProperty("请求的appId")
    private String appid;

    /** 是否默认：0.不默认 1. 默认 */
    @Excel(name = "是否默认：0.不默认 1. 默认")
    @ApiModelProperty("是否默认：0.不默认 1. 默认")
    private Integer isDefault;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "是否默认：0.不默认 1. 默认", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date createdAt;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "是否默认：0.不默认 1. 默认", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date updatedBy;

    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setClientid(Long clientid) 
    {
        this.clientid = clientid;
    }

    public Long getClientid() 
    {
        return clientid;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setAppid(String appid) 
    {
        this.appid = appid;
    }

    public String getAppid() 
    {
        return appid;
    }
    public void setIsDefault(Integer isDefault) 
    {
        this.isDefault = isDefault;
    }

    public Integer getIsDefault() 
    {
        return isDefault;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedBy(Date updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedBy() 
    {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("openId", getOpenId())
            .append("clientid", getClientid())
            .append("name", getName())
            .append("phone", getPhone())
            .append("appid", getAppid())
            .append("isDefault", getIsDefault())
            .append("createdAt", getCreatedAt())
            .append("updatedBy", getUpdatedBy())
            .toString();
    }
}
