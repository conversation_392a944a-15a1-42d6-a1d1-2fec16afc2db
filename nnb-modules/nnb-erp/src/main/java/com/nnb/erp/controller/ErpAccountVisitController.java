package com.nnb.erp.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.erp.domain.ErpAccountVisitIntention;
import com.nnb.erp.domain.ErpAccountVisitTaxRiskType;
import com.nnb.erp.domain.InternalServiceStatistics;
import com.nnb.erp.domain.dto.ErpAccountVisitDto;
import com.nnb.erp.domain.vo.AccountVisitProduct;
import com.nnb.erp.domain.vo.AccountVisitStatisticAnalysis;
import com.nnb.erp.domain.vo.ErpAccountVisitVo;
import com.nnb.erp.mapper.ErpAccountVisitMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpAccountVisit;
import com.nnb.erp.service.IErpAccountVisitService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 记账客户拜访记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/erpAccountVisit")
@Api(tags = "ErpAccountVisitController", description = "记账客户拜访记录")
public class ErpAccountVisitController extends BaseController
{
    @Autowired
    private IErpAccountVisitService erpAccountVisitService;
    /**
     * 查询记账客户拜访记录列表
     */
    @ApiOperation(value = "查询记账客户拜访记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpAccountVisit erpAccountVisit)
    {
        startPage();
        List<ErpAccountVisit> list = erpAccountVisitService.selectErpAccountVisitList(erpAccountVisit);
        return getDataTable(list);
    }

    @ApiOperation(value = "意向度列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping("/intentionList")
    public TableDataInfo intentionList()
    {
        startPage();
        List<ErpAccountVisitIntention> list = erpAccountVisitService.intentionList();
        return getDataTable(list);
    }

    @ApiOperation(value = "税务风险列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping("/taxRiskList")
    public TableDataInfo taxRiskList()
    {
        startPage();
        List<ErpAccountVisitTaxRiskType> list = erpAccountVisitService.taxRiskList();
        return getDataTable(list);
    }







    /**
     * 查询记账客户拜访记录列表
     */
    @ApiOperation(value = "查询记账客户拜访记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping("/getList")
    public TableDataInfo getList(ErpAccountVisitDto dto)
    {
        dto.setType(1);
        startPage();
        List<ErpAccountVisitVo> list = erpAccountVisitService.getList(dto);
        return getDataTable(list);
    }

    /**
     * 导出记账客户拜访记录列表
     */
    @ApiOperation(value = "导出记账客户拜访记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ErpAccountVisitDto dto) throws IOException
    {
        dto.setType(2);
        List<ErpAccountVisitVo> list = erpAccountVisitService.getList(dto);
        ExcelUtil<ErpAccountVisitVo> util = new ExcelUtil<ErpAccountVisitVo>(ErpAccountVisitVo.class);
        util.exportExcel(response, list, "记账客户拜访记录数据");
    }

    /**
     * 获取记账客户拜访记录详细信息
     */
    @ApiOperation(value = "获取记账客户拜访记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="记账客户拜访记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpAccountVisitService.selectErpAccountVisitById(id));
    }

    /**
     * 获取记账客户拜访记录详细信息
     */
    @ApiOperation(value = "获取记账客户拜访记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping(value = "/getVisitInfo")
    public AjaxResult getVisitInfo(ErpAccountVisitDto dto)
    {
        return AjaxResult.success(erpAccountVisitService.getVisitInfo(dto));
    }

    /**
     * 新增记账客户拜访记录
     */
    @ApiOperation(value = "新增记账客户拜访记录")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ErpAccountVisitDto dto)
    {
        return toAjax(erpAccountVisitService.insertErpAccountVisit(dto));
    }

    /**
     * 修改记账客户拜访记录
     */
    @ApiOperation(value = "修改记账客户拜访记录")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ErpAccountVisitDto dto)
    {
        return toAjax(erpAccountVisitService.updateErpAccountVisit(dto));
    }

    /**
     * 删除记账客户拜访记录
     */
    @ApiOperation(value = "删除记账客户拜访记录")
    @PreAuthorize(hasPermi = "erp:visit:remove")
    //@Log(title = "记账客户拜访记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpAccountVisitService.deleteErpAccountVisitByIds(ids));
    }


    /**
     * 查询记账客户拜访记录列表
     */
    @ApiOperation(value = "统计分析列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountVisit.class)})
    @GetMapping("/statisticAnalysis")
    public TableDataInfo statisticAnalysis(ErpAccountVisitDto dto)
    {
        startPage();
        List<AccountVisitStatisticAnalysis> list = erpAccountVisitService.statisticAnalysis(dto);
        return getDataTable(list);
    }



    @ApiOperation(value = "导出统计分析列表")
    @PostMapping("/statisticAnalysisExport")
    public void statisticAnalysisExport(HttpServletResponse response, @RequestBody ErpAccountVisitDto dto) throws IOException
    {
        List<AccountVisitStatisticAnalysis> list = erpAccountVisitService.statisticAnalysis(dto);
        ExcelUtil<AccountVisitStatisticAnalysis> util = new ExcelUtil<AccountVisitStatisticAnalysis>(AccountVisitStatisticAnalysis.class);
        util.exportExcel(response, list, "统计分析");
    }



    @ApiOperation(value = "获取意向产品排行")
    @GetMapping("/getIntentionProductList")
    public TableDataInfo getIntentionProductList(AccountVisitProduct accountVisitProduct)
    {
        startPage();
        List<AccountVisitProduct> list = erpAccountVisitService.getIntentionProductList(accountVisitProduct);
        return getDataTable(list);
    }

    @ApiOperation(value = "获取合作产品排行")
    @GetMapping("/getDealProductList")
    public TableDataInfo getDealProductList(AccountVisitProduct accountVisitProduct)
    {
        startPage();
        List<AccountVisitProduct> list = erpAccountVisitService.getDealProductList(accountVisitProduct);
        return getDataTable(list);
    }

    @ApiOperation(value = "处理备注字段")
    @GetMapping("/handRemark")
    public void handRemark(){
        erpAccountVisitService.handRemark();
    }
}
