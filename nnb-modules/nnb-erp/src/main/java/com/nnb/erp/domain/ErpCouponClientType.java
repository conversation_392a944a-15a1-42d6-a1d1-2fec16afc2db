package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 优惠券适用客户对象 erp_coupon_client_type
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@ApiModel(value="ErpCouponClientType",description="优惠券适用客户对象")
public class ErpCouponClientType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 优惠券id */
    @Excel(name = "优惠券id")
    @ApiModelProperty("优惠券id")
    private Long numCouponId;

    /**  0-&gt;全部； 1-&gt;黄金会员； 2-&gt;铂金会员； 3-&gt;钻石会员；  */
    @Excel(name = " 0-&gt;全部； 1-&gt;黄金会员； 2-&gt;铂金会员； 3-&gt;钻石会员； ")
    @ApiModelProperty(" 0-&gt;全部； 1-&gt;黄金会员； 2-&gt;铂金会员； 3-&gt;钻石会员； ")
    private Long numMemberType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNumCouponId(Long numCouponId) 
    {
        this.numCouponId = numCouponId;
    }

    public Long getNumCouponId() 
    {
        return numCouponId;
    }
    public void setNumMemberType(Long numMemberType) 
    {
        this.numMemberType = numMemberType;
    }

    public Long getNumMemberType() 
    {
        return numMemberType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("numCouponId", getNumCouponId())
            .append("numMemberType", getNumMemberType())
            .toString();
    }
}
