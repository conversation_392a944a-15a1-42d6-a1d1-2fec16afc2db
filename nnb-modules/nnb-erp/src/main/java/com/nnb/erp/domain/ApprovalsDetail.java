package com.nnb.erp.domain;

import lombok.Data;
import sun.util.resources.cldr.fa.LocaleNames_fa;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApprovalsDetail {
    private Integer id;
    private Integer typeAll;
    private String numOrder;
    private String signTime;
    private String userName;
    private String regionName;
    private String vcCompanyName;
    private String vcCostName;
    private String addressName;
    private String detailAddress;
    private BigDecimal NoPrice;
    private BigDecimal HasPrice;
    private BigDecimal price;
    private  Integer  splits;
    private BigDecimal historyHasPrice;
    private BigDecimal historyNoPrice;
    private Integer typePrice;
    private Integer cityId;
    private String cityName;
    private  List<String> areaName;
    private Integer areaId;
    private BigDecimal confirmAmount;
    private String vcProductName;
    private String vcClassificationName;
    private String vcTypeName;
    private String vcTaxName;
    private BigDecimal NumPrice;
    private  String vcUnitName;
    private String numIsAppletShow;
    private String numIsNeedContract;
    private String vcRemark;
    private String vcServiceName;
    private Long   numProductId;
    private String deptName;
    private String registerIds;
    //法人
    private String legalPersonName;
    //注册区域
    private String registerZone;
    //产品名称
    private String productName;
    //订单ID
    private Long orderId;
    //产品二级区域List
    private List<String> cityNameList;

    private Long productApproveId;
    private ErpProductApprove productApprove;
}
