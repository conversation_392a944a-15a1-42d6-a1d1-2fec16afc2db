package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.erp.domain.CompanyCoupon;
import com.nnb.erp.domain.ErpDiscountCouponAmount;
import com.nnb.erp.domain.dto.CouponUsageDto;
import com.nnb.erp.domain.dto.ErpDiscountCouponDto;
import com.nnb.erp.domain.vo.ErpProductDetailListDto;
import com.nnb.erp.domain.vo.ErpProductDetailListVo;
import com.nnb.erp.domain.vo.CouponUsageVo;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponVo;
import com.nnb.erp.mapper.ErpDiscountCouponAmountMapper;
import com.nnb.erp.mapper.ErpProductDetailMapper;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpDiscountCouponMapper;
import com.nnb.erp.domain.ErpDiscountCoupon;
import com.nnb.erp.service.IErpDiscountCouponService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 优惠券Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class ErpDiscountCouponServiceImpl implements IErpDiscountCouponService
{
    @Autowired
    private ErpDiscountCouponMapper erpDiscountCouponMapper;

    @Autowired
    private ErpDiscountCouponAmountMapper erpDiscountCouponAmountMapper;

    @Autowired
    private ErpDiscountCouponLogServiceImpl erpDiscountCouponLogService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ErpProductDetailMapper erpProductDetailMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询优惠券
     *
     * @param id 优惠券主键
     * @return 优惠券
     */
    @Override
    public ErpDiscountCoupon selectErpDiscountCouponById(Long id)
    {
        return erpDiscountCouponMapper.selectErpDiscountCouponById(id);
    }

    /**
     * 查询优惠券列表
     *
     * @param erpDiscountCoupon 优惠券
     * @return 优惠券
     */
    @Override
    public List<ErpDiscountCoupon> selectErpDiscountCouponList(ErpDiscountCoupon erpDiscountCoupon)
    {
        return erpDiscountCouponMapper.selectErpDiscountCouponList(erpDiscountCoupon);
    }

    @Override
    public List<ErpDiscountCouponVo> selectErpDiscountCouponVoList(ErpDiscountCoupon erpDiscountCoupon) {
        Long userId = SecurityUtils.getUserId();
        erpDiscountCoupon.setBelongUserId(userId);
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if ("admin".equals(sysUser.getUserName())){
            erpDiscountCoupon.setBelongUserId(null);
        }
        erpDiscountCoupon.setDiscountCouponAmountId(erpDiscountCoupon.getId());
        erpDiscountCoupon.setId(null);
        return erpDiscountCouponMapper.selectErpDiscountCouponVoList(erpDiscountCoupon);
    }

    /**
     * 新增优惠券
     *
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    @Override
    public int insertErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon)
    {
        erpDiscountCoupon.setCreateTime(DateUtils.getNowDate());
        return erpDiscountCouponMapper.insertErpDiscountCoupon(erpDiscountCoupon);
    }

    /**
     * 修改优惠券
     *
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    @Override
    public int updateErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon)
    {
        erpDiscountCoupon.setUpdateTime(DateUtils.getNowDate());
        return erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
    }

    /**
     * 批量删除优惠券
     *
     * @param ids 需要删除的优惠券主键
     * @return 结果
     */
    @Override
    public int deleteErpDiscountCouponByIds(Long[] ids)
    {
        return erpDiscountCouponMapper.deleteErpDiscountCouponByIds(ids);
    }

    /**
     * 删除优惠券信息
     *
     * @param id 优惠券主键
     * @return 结果
     */
    @Override
    public int deleteErpDiscountCouponById(Long id)
    {
        return erpDiscountCouponMapper.deleteErpDiscountCouponById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int generateErpDiscountCoupon(ErpDiscountCouponDto erpDiscountCouponDto) {
        Long userId = tokenService.getLoginUser().getSysUser().getUserId();
        if (ObjectUtil.isEmpty(erpDiscountCouponDto.getNumType())) {
            erpDiscountCouponDto.setNumType(1);
        }
        if (erpDiscountCouponDto.getNumType() == 1) {
            //校验  产品价格-优惠金额 >= 成本价格
            Boolean result = checkCostPrice(erpDiscountCouponDto.getNumProductId(), erpDiscountCouponDto.getBelongUserId(), erpDiscountCouponDto.getDiscountCouponAmount());
            if (result) {
                throw new ServiceException("此优惠券超限，请联系商务部！");
            }
        }

        Long id = erpDiscountCouponDto.getId();
        //校验剩余额度
        ErpDiscountCouponAmount erpDiscountCouponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(id);
        BigDecimal usingAmount = erpDiscountCouponAmount.getUsingAmount();//分配前的已使用额度
        BigDecimal surplusAmount = erpDiscountCouponAmount.getSurplusAmount();//分配前的剩余额度
        Long produceCouponAmount = erpDiscountCouponAmount.getProduceCouponAmount();//生成优惠券的个数
        Long unusedCouponAmount = erpDiscountCouponAmount.getUnusedCouponAmount();//优惠券待使用个数
        if (surplusAmount.compareTo(BigDecimal.ZERO) == 0 || surplusAmount.compareTo(erpDiscountCouponDto.getDiscountCouponAmount()) < 0) {
            throw new ServiceException("分配额度不足，分配失败");
        }
        //生成优惠券
        ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
        erpDiscountCoupon.setDiscountCouponAmountId(id);
        erpDiscountCoupon.setDiscountAmount(erpDiscountCouponDto.getDiscountCouponAmount());
        erpDiscountCoupon.setStatus(0L);
        erpDiscountCoupon.setCreateUser(userId);
        erpDiscountCoupon.setUpdateUser(userId);
        erpDiscountCoupon.setNumType(erpDiscountCouponDto.getNumType());
        if (erpDiscountCouponDto.getNumType() == 1) {
            erpDiscountCoupon.setClueId(erpDiscountCouponDto.getClueId());
            erpDiscountCoupon.setClientId(erpDiscountCouponDto.getClientId());
            erpDiscountCoupon.setBelongUserId(erpDiscountCouponDto.getBelongUserId());
            erpDiscountCoupon.setNumProductId(erpDiscountCouponDto.getNumProductId());
        } else {
            erpDiscountCoupon.setXcxPhone(erpDiscountCouponDto.getXcxPhone());
        }
        erpDiscountCoupon.setMinPrice(ObjectUtil.isEmpty(erpDiscountCouponDto.getMinPrice()) ? BigDecimal.ZERO : erpDiscountCouponDto.getMinPrice());
        erpDiscountCoupon.setValidityPeriod(ObjectUtil.isEmpty(erpDiscountCouponDto.getValidityPeriod()) ? 0 : erpDiscountCouponDto.getValidityPeriod());
        erpDiscountCoupon.setActStartTime(ObjectUtil.isEmpty(erpDiscountCouponDto.getActStartTime()) ? null : erpDiscountCouponDto.getActStartTime());
        erpDiscountCoupon.setActEndTime(ObjectUtil.isEmpty(erpDiscountCouponDto.getActEndTime()) ? null : erpDiscountCouponDto.getActEndTime());
        erpDiscountCoupon.setRemark(StrUtil.isEmpty(erpDiscountCouponDto.getRemark()) ? "" : erpDiscountCouponDto.getRemark());
        erpDiscountCoupon.setXcxCouponConfigId(ObjectUtil.isEmpty(erpDiscountCouponDto.getXcxCouponConfigId()) ? 0 : erpDiscountCouponDto.getXcxCouponConfigId());

        //更新优惠券额度的已使用和剩余额度
        ErpDiscountCouponAmount updateErpDiscountCouponAmount = new ErpDiscountCouponAmount();
        updateErpDiscountCouponAmount.setId(id);
        updateErpDiscountCouponAmount.setUsingAmount(usingAmount.add(erpDiscountCouponDto.getDiscountCouponAmount()));
        updateErpDiscountCouponAmount.setSurplusAmount(surplusAmount.subtract(erpDiscountCouponDto.getDiscountCouponAmount()));
        updateErpDiscountCouponAmount.setProduceCouponAmount(produceCouponAmount + 1L);
        updateErpDiscountCouponAmount.setUnusedCouponAmount(unusedCouponAmount + 1L);

        //加乐观锁
        ErpDiscountCouponAmount discountCouponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(id);
        BigDecimal amountSurplusAmount = discountCouponAmount.getSurplusAmount();
        if (surplusAmount.compareTo(amountSurplusAmount) != 0) {
            throw new ServiceException("优惠券额度变更，生成优惠券失败");
        }
        int insert = erpDiscountCouponMapper.insertErpDiscountCoupon(erpDiscountCoupon);
        int update = erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(updateErpDiscountCouponAmount);
        if (insert <= 0 || update <= 0) {
            throw new ServiceException("生成优惠券失败");
        }
        erpDiscountCouponLogService.saveLog(SecurityUtils.getUserId(), ErpProductConstants.DiscountCouponLogStatus.GENERATE, erpDiscountCouponDto.getBelongUserId());
        return insert;
    }

    /**
     * 校验产品价格  产品价格-优惠金额 >= 成本价格
     * @param numNameId
     * @return
     */
    private Boolean checkCostPrice(Long numProductId, Long userId, BigDecimal discountAmount) {
        Boolean flag = Boolean.FALSE;
        ErpProductDetailListDto erpProductDetailListDto = new ErpProductDetailListDto();
        erpProductDetailListDto.setNumProductId(numProductId);
        //获取分配人人的部门
        R<SysUser> info = remoteUserService.getUserInfoById(userId, SecurityConstants.INNER);
        if (info.getCode() != 200 || Objects.isNull(info.getData())) {
            throw new ServiceException("分配人部门为空");
        }
        erpProductDetailListDto.setDeptId(String.valueOf(erpProductDetailMapper.selectSecondDeptIdByDeptId(info.getData().getDeptId())));
        List<ErpProductDetailListVo> erpProductDetailListVos = erpProductDetailMapper.selectErpProductList(erpProductDetailListDto);
        if(CollectionUtils.isEmpty(erpProductDetailListVos)){
            throw new ServiceException("根据分配人部门和产品ID无法匹配到产品！");
        }
        if (CollectionUtils.isNotEmpty(erpProductDetailListVos) && erpProductDetailListVos.size() > 1) {
            throw new ServiceException("产品部门配置不唯一，无法下发");
        }
        for (ErpProductDetailListVo en : erpProductDetailListVos) {
            if (Objects.nonNull(en.getCostPrice()) && (en.getCostPrice().compareTo(BigDecimal.ZERO) > 0)) {
                if (ObjectUtil.isNotEmpty(en.getActivityStartTime()) && ObjectUtil.isNotEmpty(en.getActivityEndTime())) {
                    if (DateUtil.parse(en.getActivityStartTime(), "yyyy-MM-dd 00:00:00").isBefore(new Date())
                            && DateUtil.parse(en.getActivityEndTime(), "yyyy-MM-dd 00:00:00").isAfter(new Date())) {
                        if (ObjectUtil.isNotEmpty(en.getDiscountAmount())) {
                            en.setNumPrice(en.getDiscountAmount());
                        }
                    } else if (en.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd 00:00:00"))
                            && en.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd 00:00:00"))) {
                        if (ObjectUtil.isNotEmpty(en.getDiscountAmount())) {
                            en.setNumPrice(en.getDiscountAmount());
                        }
                    } else if (en.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd 00:00:00"))
                            || en.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd 00:00:00"))) {
                        if (ObjectUtil.isNotEmpty(en.getDiscountAmount())) {
                            en.setNumPrice(en.getDiscountAmount());
                        }
                    }
                }
                //产品价格（优惠金额）-优惠券金额 >= 成本价格
                BigDecimal numPrice = en.getNumPrice();
                if (Objects.nonNull(numPrice) && Objects.nonNull(en.getDiscountAmount())) {
                    if ((numPrice.subtract(discountAmount)).compareTo(en.getCostPrice()) < 0) {
                        flag = Boolean.TRUE;
                    }
                }
                if(Objects.nonNull(numPrice) && Objects.isNull(en.getDiscountAmount())){
                    if ((numPrice.subtract(en.getCostPrice())).compareTo(discountAmount) < 0) {
                        flag = Boolean.TRUE;
                    }
                }
            }
        }
        return flag;
    }

    @Override
    public int revocationErpDiscountCoupon(Long id) {
        ErpDiscountCoupon erpDiscountCoupon = erpDiscountCouponMapper.selectErpDiscountCouponById(id);
        if (Objects.nonNull(erpDiscountCoupon) && 1 == erpDiscountCoupon.getStatus()) {
            throw new ServiceException("优惠券已使用，无法撤销");
        }
        if (Objects.nonNull(erpDiscountCoupon) && 2 == erpDiscountCoupon.getStatus()) {
            throw new ServiceException("优惠券失效，无法撤销");
        }
        Long discountCouponAmountId = erpDiscountCoupon.getDiscountCouponAmountId();
        ErpDiscountCouponAmount discountCouponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(discountCouponAmountId);
        BigDecimal surplusAmount = discountCouponAmount.getSurplusAmount();
        BigDecimal usingAmount = discountCouponAmount.getUsingAmount();
//        Long unusedCouponAmount = discountCouponAmount.getUnusedCouponAmount();

        //将额度返还给所属优惠券额度
        BigDecimal discountAmount = erpDiscountCoupon.getDiscountAmount();
        ErpDiscountCouponAmount erpDiscountCouponAmount = new ErpDiscountCouponAmount();
        erpDiscountCouponAmount.setId(discountCouponAmountId);
        erpDiscountCouponAmount.setSurplusAmount(surplusAmount.add(discountAmount));
        erpDiscountCouponAmount.setUsingAmount(usingAmount.subtract(discountAmount));
//        erpDiscountCouponAmount.setUnusedCouponAmount(unusedCouponAmount + 1L);

        //乐观锁
        ErpDiscountCouponAmount couponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(discountCouponAmountId);
        BigDecimal amount = couponAmount.getSurplusAmount();
        if (surplusAmount.compareTo(amount) != 0) {
            throw new ServiceException("优惠券额度变更，撤销失败！");
        }

        ErpDiscountCoupon updateErpDiscountCoupon = new ErpDiscountCoupon();
        updateErpDiscountCoupon.setId(id);
        updateErpDiscountCoupon.setStatus(2L);
        int j = erpDiscountCouponMapper.updateErpDiscountCoupon(updateErpDiscountCoupon);
        int i = erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(erpDiscountCouponAmount);
        if (i <= 0 || j <= 0) {
            throw new ServiceException("优惠券撤销失败");
        }
        erpDiscountCouponLogService.saveLog(SecurityUtils.getUserId(), ErpProductConstants.DiscountCouponLogStatus.REVOCATION, null);
        return i;
    }

    @Override
    public ErpDiscountCoupon getErpDiscountCoupon(Long numProductId, Long clueId) {
        ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
        erpDiscountCoupon.setNumProductId(numProductId);
        erpDiscountCoupon.setClueId(clueId);
        List<ErpDiscountCoupon> erpDiscountCoupons = erpDiscountCouponMapper.selectErpDiscountCouponList(erpDiscountCoupon);
        if (CollectionUtils.isNotEmpty(erpDiscountCoupons)) {
            return erpDiscountCoupons.get(0);
        }
        return null;
    }

    @Override
    public List<CompanyCoupon> companyCouponList(CompanyCoupon companyCoupon) {
        return erpDiscountCouponMapper.selectCompanyCouponList(companyCoupon);
    }

    @Override
    public List<CouponUsageVo> couponUsage(CouponUsageDto couponUsageDto) {
        return erpDiscountCouponMapper.couponUsage(couponUsageDto);
    }

    @Override
    public List<ErpDiscountCoupon> getXcxCouponByPhone(String phone) {
        if (ObjectUtil.isEmpty(phone)) {
            throw new ServiceException("手机号不可为空");
        }
        if (phone.length() != 11) {
            throw new ServiceException("手机号格式错误");
        }
        List<ErpDiscountCoupon> list = erpDiscountCouponMapper.getXcxCouponByPhone(phone);
        //过期状态删除
        list.removeIf(coupon ->
                ObjectUtil.isNotEmpty(coupon.getActEndTime()) && coupon.getActEndTime().isBefore(LocalDateTime.now())
        );
        return list;
    }


}
