package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 支付公司主体对象 erp_pay_company
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
@ApiModel(value="ErpPayCompany",description="支付公司主体对象")
public class ErpPayCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 支付公司主体名称 */
    @Excel(name = "支付公司主体名称")
    @ApiModelProperty("支付公司主体名称")
    private String name;

    /** 商户号 */
    @Excel(name = "商户号")
    @ApiModelProperty("商户号")
    private String mid;

    /** 来源编号 */
    @Excel(name = "来源编号")
    @ApiModelProperty("来源编号")
    private String msgSrcId;

    /** 终端号 */
    @Excel(name = "终端号")
    @ApiModelProperty("终端号")
    private String tid;

    /** 该支付主体的默认使用部门 */
    @Excel(name = "该支付主体的默认使用部门")
    @ApiModelProperty("该支付主体的默认使用部门")
    private Integer deptId;

    /** 友商类型：1：银联，2：富友 */
    @Excel(name = "友商类型：1：银联，2：富友")
    @ApiModelProperty("友商类型：1：银联，2：富友")
    private Integer friendType;

    /** 支付公司主体别名 */
    @Excel(name = "支付公司主体别名")
    @ApiModelProperty("支付公司主体别名")
    private String alias;

    @ApiModelProperty("是否开票主体1是2否")
    private Integer kp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setMid(String mid) 
    {
        this.mid = mid;
    }

    public String getMid() 
    {
        return mid;
    }
    public void setMsgSrcId(String msgSrcId) 
    {
        this.msgSrcId = msgSrcId;
    }

    public String getMsgSrcId() 
    {
        return msgSrcId;
    }
    public void setTid(String tid) 
    {
        this.tid = tid;
    }

    public String getTid() 
    {
        return tid;
    }
    public void setDeptId(Integer deptId) 
    {
        this.deptId = deptId;
    }

    public Integer getDeptId() 
    {
        return deptId;
    }
    public void setFriendType(Integer friendType) 
    {
        this.friendType = friendType;
    }

    public Integer getFriendType() 
    {
        return friendType;
    }
    public void setAlias(String alias) 
    {
        this.alias = alias;
    }

    public String getAlias() 
    {
        return alias;
    }

    public Integer getKp() {
        return kp;
    }

    public void setKp(Integer kp) {
        this.kp = kp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("mid", getMid())
            .append("msgSrcId", getMsgSrcId())
            .append("tid", getTid())
            .append("deptId", getDeptId())
            .append("friendType", getFriendType())
            .append("alias", getAlias())
            .toString();
    }
}
