package com.nnb.erp.mapper;

import com.nnb.erp.domain.ErpOrderType;
import org.apache.ibatis.annotations.Param;

/**
 * 订单类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-27 18:38:42
 */
public interface ErpOrderTypeMapper {

    /**
     * 根据订单标识查询订单类型标识。
     *
     * @param orderId 订单标识。
     * @return 返回订单类型标识。
     * <AUTHOR>
     * @since 2022-04-27 18:51:15
     */
    public Long getIdByOrderId(@Param("orderId") Long orderId);

    /**
     * 新增订单类型。
     *
     * @param erpOrderType 订单类型。
     * <AUTHOR>
     * @since 2022-04-27 18:51:42
     */
    public void insertErpOrderType(ErpOrderType erpOrderType);

    /**
     * 修改订单类型。
     *
     * @param erpOrderType 订单类型。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-04-27 18:52:06
     */
    public int updateErpOrderType(ErpOrderType erpOrderType);

    /**
     * 获取指定订单类型。
     *
     * @param orderId 订单标识。
     * @return 返回订单类型。
     * <AUTHOR>
     * @since 2022-06-09 17:07:34
     */
    public ErpOrderType getOrderTypeByOrderId(@Param("orderId") Long orderId);

}
