package com.nnb.erp.domain.approval;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 通用审批人对象 erp_new_approval_user
 * 
 * <AUTHOR>
 * @date 2023-02-13
 */
@ApiModel(value="ErpNewApprovalUser",description="通用审批人对象")
public class ErpNewApprovalUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;

    /** 审批类型，1：个人审批，2：部门审批 */
    @Excel(name = "审批类型，1：个人审批，2：部门审批")
    @ApiModelProperty("审批类型，1：个人审批，2：部门审批")
    private Integer approvalType;

    /** 审批节点ID */
    @Excel(name = "审批节点ID")
    @ApiModelProperty("审批节点ID")
    private Long approvalNodeId;

    /** 审批部门ID */
    @Excel(name = "审批部门ID")
    @ApiModelProperty("审批部门ID")
    private Long deptId;

    /** 审批人ID */
    @Excel(name = "审批人ID")
    @ApiModelProperty("审批人ID")
    private Long userId;

    /** 城市区域ID */
    @Excel(name = "城市区域ID")
    @ApiModelProperty("城市区域ID")
    private Long cityId;

    /** 审批人排序 */
    @Excel(name = "审批人排序")
    @ApiModelProperty("审批人排序")
    private Integer sort;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    /** 删除标记，0：未删除，1：已删除 */
    @ApiModelProperty("删除标记，0：未删除，1：已删除")
    private Integer delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setApprovalType(Integer approvalType) 
    {
        this.approvalType = approvalType;
    }

    public Integer getApprovalType()
    {
        return approvalType;
    }

    public Long getApprovalNodeId() {
        return approvalNodeId;
    }

    public void setApprovalNodeId(Long approvalNodeId) {
        this.approvalNodeId = approvalNodeId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public void setSort(Integer sort)
    {
        this.sort = sort;
    }

    public Integer getSort()
    {
        return sort;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("approvalType", getApprovalType())
            .append("approvalNodeId", getApprovalNodeId())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("sort", getSort())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
