package com.nnb.erp.service.accountpermance;

import com.nnb.erp.domain.accountpermance.ErpAccountPerformance;
import com.nnb.erp.domain.dto.service.ErpOldAccountPerformanceDTO;

import java.util.List;
/**
 * 会计历史绩效Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-03
 */
public interface IErpAccountPerformanceService 
{
    /**
     * 查询会计历史绩效
     * 
     * @param id 会计历史绩效主键
     * @return 会计历史绩效
     */
    public ErpAccountPerformance selectErpAccountPerformanceById(Long id);

    /**
     * 查询会计历史绩效列表
     * 
     * @param erpAccountPerformance 会计历史绩效
     * @return 会计历史绩效集合
     */
    public List<ErpAccountPerformance> selectErpAccountPerformanceList(ErpAccountPerformance erpAccountPerformance);

    public List<ErpAccountPerformance> selectOldErpAccountPerformanceList(ErpOldAccountPerformanceDTO erpOldAccountPerformanceDTO);


    /**
     * 新增会计历史绩效
     * 
     * @param erpAccountPerformance 会计历史绩效
     * @return 结果
     */
    public int insertErpAccountPerformance(ErpAccountPerformance erpAccountPerformance);

    public int insertErpAccountPerformanceList(List<ErpAccountPerformance> erpAccountPerformanceList);


    /**
     * 修改会计历史绩效
     * 
     * @param erpAccountPerformance 会计历史绩效
     * @return 结果
     */
    public int updateErpAccountPerformance(ErpAccountPerformance erpAccountPerformance);

    /**
     * 批量删除会计历史绩效
     * 
     * @param ids 需要删除的会计历史绩效主键集合
     * @return 结果
     */
    public int deleteErpAccountPerformanceByIds(Long[] ids);

    /**
     * 删除会计历史绩效信息
     * 
     * @param id 会计历史绩效主键
     * @return 结果
     */
    public int deleteErpAccountPerformanceById(Long id);
}
