package com.nnb.erp.controller;

import com.github.pagehelper.Page;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.domain.SalesStatisticsDto;
import com.nnb.erp.domain.dto.CostSettleManageDto;
import com.nnb.erp.domain.vo.report.CostSettleManageVo;
import com.nnb.erp.service.SalesStatisticsTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/SalesStatisticsTable")
@Api(tags = "SalesStatisticsTableController")
public class SalesStatisticsTableController {
    @Autowired
    private SalesStatisticsTableService salesStatisticsTableService;


    @ApiOperation(value = "销售经营日报")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = CostSettleManageVo.class)})
    @GetMapping("/list")
    public AjaxResult list(SalesStatisticsDto statisticsDto) {
     return AjaxResult.success(salesStatisticsTableService.getCount(statisticsDto));

    }
    @ApiOperation(value = "销售经营日报")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = CostSettleManageVo.class)})
    @GetMapping("/seniority")
    public AjaxResult seniority(SalesStatisticsDto statisticsDto){
        return AjaxResult.success(salesStatisticsTableService.getSeniority(statisticsDto));
    }

}
