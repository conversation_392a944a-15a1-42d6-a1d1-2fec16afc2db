package com.nnb.erp.controller.reporting;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.constant.ErpExamineApproveConstants;
import com.nnb.erp.domain.ErpExamineApprove;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.AaaQualifies;
import com.nnb.erp.domain.ErpAccountDateUpdate;
import com.nnb.erp.domain.ErpExamineApproveCommonPayment;
import com.nnb.erp.domain.ErpExamineOtherOrderPayInfo;
import com.nnb.erp.domain.dto.ErpExamineApproveDTO;
import com.nnb.erp.domain.inventory.IaStorageDetail;
import com.nnb.erp.domain.reporting.*;
import com.nnb.erp.domain.vo.PayRecordBigCallBackVo;
import com.nnb.erp.enums.ErpOrderPayRecordEnum;
import com.nnb.erp.mapper.ErpExamineApproveMapper;
import com.nnb.erp.mapper.reporting.ErpOrderPayRecordMapper;
import com.nnb.erp.service.impl.ExportLogServiceImpl;
import com.nnb.erp.service.reporting.IErpOrderPayRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-01-24
 * @Version: 1.0
 */
@RestController
@RequestMapping("/erpOrderPayRecord")
@Api(tags = "ErpOrderPayRecordController", value = "交易流水记录")
public class ErpOrderPayRecordController extends BaseController {

    @Autowired
    private IErpOrderPayRecordService erpOrderPayRecordService;
    @Autowired
    private ErpExamineApproveMapper erpExamineApproveMapper;
    @Autowired
    private ErpOrderPayRecordMapper erpOrderPayRecordMapper;

    @Autowired
    private ExportLogServiceImpl exportLogService;
    /**
     * 查询交易流水记录列表
     */
    @ApiOperation(value = "查询交易流水记录列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = ErpOrderPayRecord.class)})
    @PostMapping("/pageList")
    public TableDataInfo pageList(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        Page<Object> objects = PageHelper.startPage(
                erpOrderPayRecord.getPageNum(), erpOrderPayRecord.getPageSize(), null);
        List<ErpOrderPayRecord> list = erpOrderPayRecordService.pageList(erpOrderPayRecord);
        return getDataTableAndTotal(list, objects.getTotal());
    }

    /**
     * 导出交易流水记录列表
     */
    @ApiOperation(value = "导出交易流水记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ErpOrderPayRecord erpOrderPayRecord) throws IOException {
        List<ErpOrderPayRecord> list = erpOrderPayRecordService.pageList(erpOrderPayRecord);
        JSONArray idList = new JSONArray();
        if (CollUtil.isNotEmpty(list)){
            List<ErpOrderPayRecordExcelVo> collect = list.stream().map(en -> {
                ErpOrderPayRecordExcelVo excelVo = new ErpOrderPayRecordExcelVo();
                BeanUtils.copyProperties(en, excelVo);
                idList.add(en.getId());
                return excelVo;
            }).collect(Collectors.toList());
            ExcelUtil<ErpOrderPayRecordExcelVo> util = new ExcelUtil<ErpOrderPayRecordExcelVo>(ErpOrderPayRecordExcelVo.class);
            util.exportExcel(response, collect, "报单记录列表", "报单记录列表");


            exportLogService.insertData("销售模块", "收款报单", idList.toString());
        }
    }

    /**
     * 获取交易流水记录详细信息
     */
    @ApiOperation(value = "获取交易流水记录详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = ErpOrderPayRecord.class)})
    @PostMapping("/getById")
    public AjaxResult getInfo(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        return AjaxResult.success(erpOrderPayRecordService.selectErpOrderPayRecordById(Long.parseLong(erpOrderPayRecord.getId())));
    }

    /**
     * 新增交易流水记录
     */
    @ApiOperation(value = "新增交易流水记录")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        return toAjax(erpOrderPayRecordService.insertErpOrderPayRecord(erpOrderPayRecord));
    }

    /**
     * 修改交易流水记录
     */
    @ApiOperation(value = "修改交易流水记录")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        return toAjax(erpOrderPayRecordService.updateErpOrderPayRecord(erpOrderPayRecord));
    }

    /**
     * 删除交易流水记录
     */
    @ApiOperation(value = "删除交易流水记录")
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        return toAjax(erpOrderPayRecordService.deleteErpOrderPayRecordByIds(erpOrderPayRecord.getIds()));
    }

    /**
     * 获取支付主体
     *
     * @return
     */
    @ApiOperation(value = "获取支付主体")
    @PostMapping("/getPayCompany")
    public AjaxResult getPayCompany() {
        return AjaxResult.success(erpOrderPayRecordService.getPayCompany());
    }

    /**
     * 下载流水记录模板
     */
    @ApiOperation(value = "下载流水记录模板")
    @PostMapping("/download-template")
    public void downloadTemplate(HttpServletResponse response, @RequestBody ErpOrderPayRecord erpOrderPayRecord) throws IOException {
        if (ErpOrderPayRecordEnum.CORPORATE_TRANSFER.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CorporateTransferVo> list = new ArrayList<>();
            CorporateTransferVo vo = new CorporateTransferVo()
                    .setPaymentType(ErpOrderPayRecordEnum.CORPORATE_TRANSFER.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setOpeningBank("中国银行")
                    .setBankAccount("****************")
                    .setTradeId("************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"));
            list.add(vo);
            ExcelUtil<CorporateTransferVo> util = new ExcelUtil<CorporateTransferVo>(CorporateTransferVo.class);
            util.exportExcel(response, list, "对公转账模板", "对公转账模板");
        }else if (ErpOrderPayRecordEnum.DEDUCTION.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CashVo> list = new ArrayList<>();
            CashVo vo = new CashVo()
                    .setPaymentType(ErpOrderPayRecordEnum.DEDUCTION.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setDocumentNumber("*****************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"))
                    .setUserName("admin");
            list.add(vo);
            ExcelUtil<CashVo> util = new ExcelUtil<CashVo>(CashVo.class);
            util.exportExcel(response, list, "抵扣款", "现金模板");
        }else if (ErpOrderPayRecordEnum.CASH.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CashVo> list = new ArrayList<>();
            CashVo vo = new CashVo()
                    .setPaymentType(ErpOrderPayRecordEnum.CASH.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setDocumentNumber("*****************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"))
                    .setUserName("admin");
            list.add(vo);
            ExcelUtil<CashVo> util = new ExcelUtil<CashVo>(CashVo.class);
            util.exportExcel(response, list, "现金模板", "现金模板");
        }else if (ErpOrderPayRecordEnum.POS_MACHINE.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CorporateTransferVo> list = new ArrayList<>();
            CorporateTransferVo vo = new CorporateTransferVo()
                    .setPaymentType(ErpOrderPayRecordEnum.POS_MACHINE.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setOpeningBank("中国银行")
                    .setBankAccount("****************")
                    .setTradeId("************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"));
            list.add(vo);
            ExcelUtil<CorporateTransferVo> util = new ExcelUtil<CorporateTransferVo>(CorporateTransferVo.class);
            util.exportExcel(response, list, "POS机模板", "POS机模板");
        }else if (ErpOrderPayRecordEnum.SHOP.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CashVo> list = new ArrayList<>();
            CashVo vo = new CashVo()
                    .setPaymentType(ErpOrderPayRecordEnum.SHOP.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setDocumentNumber("*****************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"))
                    .setUserName("admin");
            list.add(vo);
            ExcelUtil<CashVo> util = new ExcelUtil<CashVo>(CashVo.class);
            util.exportExcel(response, list, "店铺模板", "店铺模板");
        }else if (ErpOrderPayRecordEnum.OTHER.getCode().equals(erpOrderPayRecord.getPaymentType())){
            List<CorporateTransferVo> list = new ArrayList<>();
            CorporateTransferVo vo = new CorporateTransferVo()
                    .setPaymentType(ErpOrderPayRecordEnum.OTHER.getCode())
                    .setPayCompanyStr("北京启照多商务服务有限公司")
                    .setPayTime("2024-01-26")
                    .setOpeningBank("中国银行")
                    .setBankAccount("****************")
                    .setTradeId("************")
                    .setClientName("导入测试客户1")
                    .setFee(new BigDecimal("8.88"));
            list.add(vo);
            ExcelUtil<CorporateTransferVo> util = new ExcelUtil<CorporateTransferVo>(CorporateTransferVo.class);
            util.exportExcel(response, list, "其他模板", "其他模板");
        }
    }

    /**
     * 导入流水记录模板
     */
    @ApiOperation(value = "导入流水记录模板")
    @PostMapping("/import-template")
    public AjaxResult importTemplate(@RequestParam(required = false) MultipartFile file) throws IOException {
        Map<String, Object> map = erpOrderPayRecordService.importTemplate(file);
        return AjaxResult.success(map);
    }

    /**
     * 新增交易流水记录
     */
    @ApiOperation(value = "报单")
    @PostMapping("/report")
    public AjaxResult report(@RequestBody ReportDto reportDto) {
        return toAjax(erpOrderPayRecordService.report(reportDto));
    }

    /**
     * 新增交易流水记录
     */
    @ApiOperation(value = "报单")
    @PostMapping("/reportBack")
    public AjaxResult reportBack(@RequestBody ReportDto reportDto) {
        return toAjax(erpOrderPayRecordService.reportBack(reportDto));
    }
    @ApiOperation(value = "释放")
    @GetMapping("/release")
    public AjaxResult release(@RequestParam("orderNum") String orderNum) {
        return toAjax(erpOrderPayRecordService.release(orderNum, 1));
    }

    /**
     * 移交交易流水记录
     */
    @ApiOperation(value = "报单")
    @PostMapping("/transferUser")
    public AjaxResult transferUser(@RequestBody ErpOrderPayRecord erpOrderPayRecord) {
        return toAjax(erpOrderPayRecordService.transferUser(erpOrderPayRecord));
    }







    @GetMapping("/updateOverDate")
    public void updateOverDate() {
        ErpExamineApproveDTO dto = new ErpExamineApproveDTO();
        dto.setApproveType(ErpExamineApproveConstants.APPROVE_TYPE_ORDER_REPORT);
        dto.setApproveStatus(1);
        List<ErpExamineApprove> list = erpExamineApproveMapper.selectErpExamineApproveList(dto);
        for (int i = 0; i < list.size(); i++) {
            ErpExamineApprove approve = list.get(i);
            String followInfoStr = approve.getFollowInfo();
            if (ObjectUtil.isNull(approve.getFollowInfo())) {
                continue;
            }
            JSONArray followInfoArr = JSONArray.fromObject(followInfoStr);
            if (followInfoArr.size() > 0) {
                JSONObject lastObj = followInfoArr.getJSONObject(followInfoArr.size()-1);
                Date overDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, lastObj.getString("time"));
                ErpOrderPayRecord record = erpOrderPayRecordMapper.selectErpOrderPayRecordById(Long.parseLong(approve.getOtherId()));
                record.setReportOverDate(overDate);
                erpOrderPayRecordMapper.updateErpOrderPayRecord(record);
            }
        }
    }

    /**
     * 新增通用付款审批关联
     */
    @ApiOperation(value = "凭证退款")
    @PostMapping("/voucherRefound")
    public AjaxResult voucherRefound(@RequestBody ErpExamineApproveCommonPayment erpExamineApproveCommonPayment)
    {
        return toAjax(erpOrderPayRecordService.voucherRefound(erpExamineApproveCommonPayment));
    }


    /**
     * 查询记账周期修改审批关联列表
     */
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpAccountDateUpdate.class)})
    @GetMapping("/bigCallBack")
    public TableDataInfo bigCallBack(ErpOrderPayRecord erpOrderPayRecord)
    {
        startPage();
        List<PayRecordBigCallBackVo> list = erpOrderPayRecordService.bigCallBack(erpOrderPayRecord);
        return getDataTable(list);
    }


    @PostMapping("/bigCallBackExport")
    public void bigCallBackExport(HttpServletResponse response,@RequestBody ErpOrderPayRecord erpOrderPayRecord) throws IOException
    {
        List<PayRecordBigCallBackVo> list = erpOrderPayRecordService.bigCallBackExport(erpOrderPayRecord);
        ExcelUtil<PayRecordBigCallBackVo> util = new ExcelUtil<PayRecordBigCallBackVo>(PayRecordBigCallBackVo.class);
        util.exportExcel(response, list, "大额预存回访");
    }
}
