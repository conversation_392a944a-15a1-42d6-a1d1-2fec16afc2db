package com.nnb.erp.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.map.MapUtil;
import com.nnb.erp.domain.PointStatusAttribute;
import com.nnb.erp.domain.ServiceTypeStatus;
import com.nnb.erp.domain.vo.service.SConfigServicePointStatusVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.SConfigServicePointStatusMapper;
import com.nnb.erp.domain.SConfigServicePointStatus;
import com.nnb.erp.service.ISConfigServicePointStatusService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
@Service
public class SConfigServicePointStatusServiceImpl implements ISConfigServicePointStatusService 
{
    @Autowired
    private SConfigServicePointStatusMapper sConfigServicePointStatusMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SConfigServicePointStatus selectSConfigServicePointStatusById(Long id)
    {
        return sConfigServicePointStatusMapper.selectSConfigServicePointStatusById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServicePointStatus 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SConfigServicePointStatusVo> selectSConfigServicePointStatusList(SConfigServicePointStatus sConfigServicePointStatus)
    {
        return sConfigServicePointStatusMapper.selectSConfigServicePointStatusList(sConfigServicePointStatus);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServicePointStatus 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSConfigServicePointStatus(SConfigServicePointStatus sConfigServicePointStatus)
    {
        return sConfigServicePointStatusMapper.insertSConfigServicePointStatus(sConfigServicePointStatus);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServicePointStatus 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSConfigServicePointStatus(SConfigServicePointStatus sConfigServicePointStatus)
    {
        return sConfigServicePointStatusMapper.updateSConfigServicePointStatus(sConfigServicePointStatus);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSConfigServicePointStatusByIds(Long[] ids)
    {
        return sConfigServicePointStatusMapper.deleteSConfigServicePointStatusByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSConfigServicePointStatusById(Long id)
    {
        return sConfigServicePointStatusMapper.deleteSConfigServicePointStatusById(id);
    }

    @Override
    public Map<Long, PointStatusAttribute> getAttributeByPointStatusId(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<PointStatusAttribute> attribute = sConfigServicePointStatusMapper.getAttributeByPointStatusId(idList);
        Map<Long, List<PointStatusAttribute>> collectMap = attribute.stream().collect(Collectors.groupingBy(PointStatusAttribute::getPointStatusId));
        return collectMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, m -> m.getValue().get(0)));
    }

}
