package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpCouponDeptMapper;
import com.nnb.erp.domain.ErpCouponDept;
import com.nnb.erp.service.IErpCouponDeptService;

/**
 * 优惠券适用部门Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@Service
public class ErpCouponDeptServiceImpl implements IErpCouponDeptService 
{
    @Autowired
    private ErpCouponDeptMapper erpCouponDeptMapper;

    /**
     * 查询优惠券适用部门
     * 
     * @param id 优惠券适用部门主键
     * @return 优惠券适用部门
     */
    @Override
    public ErpCouponDept selectErpCouponDeptById(Long id)
    {
        return erpCouponDeptMapper.selectErpCouponDeptById(id);
    }

    /**
     * 查询优惠券适用部门列表
     * 
     * @param erpCouponDept 优惠券适用部门
     * @return 优惠券适用部门
     */
    @Override
    public List<ErpCouponDept> selectErpCouponDeptList(ErpCouponDept erpCouponDept)
    {
        return erpCouponDeptMapper.selectErpCouponDeptList(erpCouponDept);
    }

    /**
     * 新增优惠券适用部门
     * 
     * @param erpCouponDept 优惠券适用部门
     * @return 结果
     */
    @Override
    public int insertErpCouponDept(ErpCouponDept erpCouponDept)
    {
        return erpCouponDeptMapper.insertErpCouponDept(erpCouponDept);
    }

    /**
     * 修改优惠券适用部门
     * 
     * @param erpCouponDept 优惠券适用部门
     * @return 结果
     */
    @Override
    public int updateErpCouponDept(ErpCouponDept erpCouponDept)
    {
        return erpCouponDeptMapper.updateErpCouponDept(erpCouponDept);
    }

    /**
     * 批量删除优惠券适用部门
     * 
     * @param ids 需要删除的优惠券适用部门主键
     * @return 结果
     */
    @Override
    public int deleteErpCouponDeptByIds(Long[] ids)
    {
        return erpCouponDeptMapper.deleteErpCouponDeptByIds(ids);
    }

    /**
     * 删除优惠券适用部门信息
     * 
     * @param id 优惠券适用部门主键
     * @return 结果
     */
    @Override
    public int deleteErpCouponDeptById(Long id)
    {
        return erpCouponDeptMapper.deleteErpCouponDeptById(id);
    }
}
