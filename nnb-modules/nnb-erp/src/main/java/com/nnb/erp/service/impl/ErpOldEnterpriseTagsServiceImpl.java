package com.nnb.erp.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.erp.domain.ErpEnterprise;
import com.nnb.erp.domain.ErpEnterpriseDetail;
import com.nnb.erp.domain.ErpOldEnterpriseTrades;
import com.nnb.erp.domain.dto.ErpOldEnterpriseTagsDTO;
import com.nnb.erp.domain.dto.enterprise.ErpAddOldEnterpriseTagsDTO;
import com.nnb.erp.mapper.ErpEnterpriseDetailMapper;
import com.nnb.erp.mapper.ErpEnterpriseMapper;
import com.nnb.erp.mapper.ErpOldEnterpriseTradesMapper;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.domain.BdUserTags;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpOldEnterpriseTagsMapper;
import com.nnb.erp.domain.ErpOldEnterpriseTags;
import com.nnb.erp.service.IErpOldEnterpriseTagsService;

/**
 * 已成交企业标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Service
public class ErpOldEnterpriseTagsServiceImpl implements IErpOldEnterpriseTagsService 
{
    @Autowired
    private ErpOldEnterpriseTagsMapper erpOldEnterpriseTagsMapper;

    @Autowired
    private RemoteCustomerService remoteCustomerService;


    @Autowired
    private ErpEnterpriseMapper erpEnterpriseMapper;

    @Autowired
    private ErpOldEnterpriseTradesMapper erpOldEnterpriseTradesMapper;


    @Autowired
    private ErpEnterpriseDetailMapper erpEnterpriseDetailMapper;
    /**
     * 查询已成交企业标签
     * 
     * @param numId 已成交企业标签主键
     * @return 已成交企业标签
     */
    @Override
    public ErpOldEnterpriseTags selectErpOldEnterpriseTagsByNumId(Long numId)
    {
        return erpOldEnterpriseTagsMapper.selectErpOldEnterpriseTagsByNumId(numId);
    }

    /**
     * 查询已成交企业标签列表
     * 
     * @param erpOldEnterpriseTags 已成交企业标签
     * @return 已成交企业标签
     */
    @Override
    public List<ErpOldEnterpriseTags> selectErpOldEnterpriseTagsList(ErpOldEnterpriseTags erpOldEnterpriseTags)
    {
        return erpOldEnterpriseTagsMapper.selectErpOldEnterpriseTagsList(erpOldEnterpriseTags);
    }

    /**
     * 新增已成交企业标签
     * 
     * @param erpOldEnterpriseTags 已成交企业标签
     * @return 结果
     */
    @Override
    public int insertErpOldEnterpriseTags(ErpOldEnterpriseTags erpOldEnterpriseTags)
    {
        return erpOldEnterpriseTagsMapper.insertErpOldEnterpriseTags(erpOldEnterpriseTags);
    }

    @Override
    public int insertErpOldEnterpriseTagList(ErpAddOldEnterpriseTagsDTO erpOldEnterpriseTags) {
        int result = 0;
        if (CollectionUtils.isNotEmpty(erpOldEnterpriseTags.getNumUserTagId())) {
            for (Long aLong : erpOldEnterpriseTags.getNumUserTagId()) {
                ErpOldEnterpriseTags erpOldEnterpriseTag = new ErpOldEnterpriseTags();
                erpOldEnterpriseTag.setEnterpriseId(erpOldEnterpriseTags.getEnterpriseId());
                erpOldEnterpriseTag.setNumUserTagId(aLong);
                List<ErpOldEnterpriseTags> oldEnterpriseTags = erpOldEnterpriseTagsMapper.selectErpOldEnterpriseTagsList(erpOldEnterpriseTag);
                if (CollectionUtils.isEmpty(oldEnterpriseTags)) {
                    result += erpOldEnterpriseTagsMapper.insertErpOldEnterpriseTags(erpOldEnterpriseTag);
                }

            }
        }
        return result;
    }

    /**
     * 修改已成交企业标签
     * 
     * @param erpOldEnterpriseTags 已成交企业标签
     * @return 结果
     */
    @Override
    public int updateErpOldEnterpriseTags(ErpOldEnterpriseTags erpOldEnterpriseTags)
    {
        return erpOldEnterpriseTagsMapper.updateErpOldEnterpriseTags(erpOldEnterpriseTags);
    }

    /**
     * 批量删除已成交企业标签
     * 
     * @param numIds 需要删除的已成交企业标签主键
     * @return 结果
     */
    @Override
    public int deleteErpOldEnterpriseTagsByNumIds(Long[] numIds)
    {
        return erpOldEnterpriseTagsMapper.deleteErpOldEnterpriseTagsByNumIds(numIds);
    }

    /**
     * 删除已成交企业标签信息
     * 
     * @param numId 已成交企业标签主键
     * @return 结果
     */
    @Override
    public int deleteErpOldEnterpriseTagsByNumId(Long numId)
    {
        return erpOldEnterpriseTagsMapper.deleteErpOldEnterpriseTagsByNumId(numId);
    }

    @Override
    public String importEnterpriseTags(List<ErpOldEnterpriseTagsDTO> erpOldEnterpriseTagsDTOS) {
        if (StringUtils.isNull(erpOldEnterpriseTagsDTOS) || erpOldEnterpriseTagsDTOS.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int errorNum = 0;
        for (ErpOldEnterpriseTagsDTO erpOldEnterpriseTagsDTO : erpOldEnterpriseTagsDTOS) {
            if (StringUtils.isEmpty(erpOldEnterpriseTagsDTO.getEnterpriseName()) || StringUtils.isEmpty(erpOldEnterpriseTagsDTO.getTagName())) {
                ++errorNum;
                continue;
            }
            //老客户标签 tagName
            String tagName = erpOldEnterpriseTagsDTO.getTagName();
            //行业
            String trade = erpOldEnterpriseTagsDTO.getTrade();

            BdUserTags bdUserTags = new BdUserTags();
            bdUserTags.setVcName(tagName);
            bdUserTags.setTagType(2);
            R<List<BdUserTags>> bdUserTagsByName = remoteCustomerService.getBdUserTagsByName(bdUserTags);
            if (200 == bdUserTagsByName.getCode()) {
                List<BdUserTags> data = bdUserTagsByName.getData();
                ErpEnterprise erpEnterprise = new ErpEnterprise();
                erpEnterprise.setVcCompanyName(erpOldEnterpriseTagsDTO.getEnterpriseName());
                List<ErpEnterprise> erpEnterprises = erpEnterpriseMapper.selectErpEnterpriseListByName(erpEnterprise);


                List<Map<String, Integer>> trades = erpEnterpriseMapper.selectTradeByName(trade);
                //企业名称或标签为空上传失败
                if (CollectionUtils.isEmpty(data) || CollectionUtils.isEmpty(erpEnterprises)) {
                    ++errorNum;
                    continue;
                }
                //行业不存在上传失败
                if (StringUtils.isNotEmpty(trade) && (CollectionUtils.isEmpty(trades))) {
                    ++errorNum;
                    continue;
                }
                for (ErpEnterprise erpEnterpris : erpEnterprises) {
                    for (BdUserTags userTags : data) {
                        ErpOldEnterpriseTags erpOldEnterpriseTags = new ErpOldEnterpriseTags();
                        erpOldEnterpriseTags.setEnterpriseId(erpEnterpris.getId());
                        erpOldEnterpriseTags.setNumUserTagId(userTags.getNumId());
                        List<ErpOldEnterpriseTags> oldEnterpriseTags = erpOldEnterpriseTagsMapper.selectErpOldEnterpriseTagsList(erpOldEnterpriseTags);
                        //企业用户标签不存在，则添加标签
                        if (CollectionUtils.isEmpty(oldEnterpriseTags)) {
                            ErpOldEnterpriseTags enterpriseTags = new ErpOldEnterpriseTags();
                            enterpriseTags.setNumUserTagId(userTags.getNumId());
                            enterpriseTags.setEnterpriseId(erpEnterpris.getId());
                            int tags = erpOldEnterpriseTagsMapper.insertErpOldEnterpriseTags(enterpriseTags);
                            successNum = successNum + tags;
                        }
                    }
                    //关联行业
                    erpOldEnterpriseTradesMapper.updateTradeByEnterpriseId(erpEnterpris.getId());
                    for (Map<String, Integer> map : trades) {
                        if(Objects.nonNull(map.get("id"))){
                            ErpEnterpriseDetail erpEnterpriseDetail = new ErpEnterpriseDetail();
                            erpEnterpriseDetail.setNumErpEnterpriseId(erpEnterpris.getId());
                            erpEnterpriseDetail.setTrade(Long.valueOf(map.get("id")));
                            erpEnterpriseDetailMapper.updateErpBizDetailByErpEnterpriseId(erpEnterpriseDetail);
                        }
                    }
                }
            }

        }
        if (errorNum > 0) {
            return "部分失败，上传成功" + (erpOldEnterpriseTagsDTOS.size() - errorNum) + "个~";
        } else {
            return "上传成功" + erpOldEnterpriseTagsDTOS.size() + "个~";
        }
    }
}
