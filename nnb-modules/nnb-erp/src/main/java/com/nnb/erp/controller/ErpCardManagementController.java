package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.*;
import com.nnb.erp.service.IErpPhoneCardConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.erp.service.IErpCardManagementService;

/**
 * 语音数据和手机卡配置关系Controller
 * 
 * <AUTHOR>
 * @date 2021-11-01
 */
@RestController
@RequestMapping("/management")
public class ErpCardManagementController extends BaseController
{
    @Autowired
    private IErpCardManagementService erpCardManagementService;

    @Autowired
    private IErpPhoneCardConfigService erpPhoneCardConfigService;

    /**
     * 查询未配置套餐，通话统计列表
     */
    @PreAuthorize(hasPermi = "erp:management:offMmatchingPhoneCardList")
    @GetMapping("/offMmatchingPhoneCardList")
    public TableDataInfo offMmatchingPhoneCardList(ErpCardManagementByUser erpCardManagementByUser)
    {
        startPage();
        List<OffMmatchingPhoneCard> list = erpCardManagementService.offMmatchingPhoneCardList(erpCardManagementByUser);
        return getDataTable(list);
    }

    /**
     * 导出未配置套餐，通话统计
     */
    @PostMapping("/exportOffMmatchingPhoneCardList")
    public void exportOffMmatchingPhoneCardList(HttpServletResponse response,ErpCardManagementByUser erpCardManagementByUser) throws IOException
    {
        List<OffMmatchingPhoneCard> list = erpCardManagementService.offMmatchingPhoneCardList(erpCardManagementByUser);
        ExcelUtil<OffMmatchingPhoneCard> util = new ExcelUtil<OffMmatchingPhoneCard>(OffMmatchingPhoneCard.class);
        util.exportExcel(response, list, "未配置套餐电话卡统计");
    }

    /**
     * 查询语音数据和手机卡配置关系列表
     */
    @PreAuthorize(hasPermi = "erp:management:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpCardManagement erpCardManagement)
    {
        startPage();
        List<ErpCardManagement> list = erpCardManagementService.selectErpCardManagementList(erpCardManagement);
        return getDataTable(list);
    }

    /**
     * 导出语音数据和手机卡配置关系列表
     */
    @PreAuthorize(hasPermi = "erp:management:export")
    //@Log(title = "语音数据和手机卡配置关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpCardManagement erpCardManagement) throws IOException
    {
        List<ErpCardManagement> list = erpCardManagementService.selectErpCardManagementList(erpCardManagement);
        ExcelUtil<ErpCardManagement> util = new ExcelUtil<ErpCardManagement>(ErpCardManagement.class);
        util.exportExcel(response, list, "语音数据和手机卡配置关系数据");
    }

    /**
     * 导出手机卡数据统计信息
     */
    @PreAuthorize(hasPermi = "erp:management:exportStatis")
    //@Log(title = "手机卡品牌管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportStatis")
    public void exportStatis(HttpServletResponse response, ErpPhoneCardConfigVO erpPhoneCardConfig) throws IOException
    {
        List<ErpPhoneCardConfigVO> list = erpPhoneCardConfigService.selectErpPhoneCardConfigListVO(erpPhoneCardConfig);
        ExcelUtil<ErpPhoneCardConfigVO> util = new ExcelUtil<ErpPhoneCardConfigVO>(ErpPhoneCardConfigVO.class);
        util.exportExcel(response, list, "手机卡数据统计");
    }

    /**
     * 获取语音数据和手机卡配置关系详细信息
     */
    @PreAuthorize(hasPermi = "erp:management:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(erpCardManagementService.selectErpCardManagementById(id));
    }

    /**
     * 新增语音数据和手机卡配置关系
     */
    @PreAuthorize(hasPermi = "erp:management:add")
    //@Log(title = "语音数据和手机卡配置关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpCardManagement erpCardManagement)
    {
        return toAjax(erpCardManagementService.insertErpCardManagement(erpCardManagement));
    }

    /**
     * 修改语音数据和手机卡配置关系
     */
    @PreAuthorize(hasPermi = "erp:management:edit")
    //@Log(title = "语音数据和手机卡配置关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpCardManagement erpCardManagement)
    {
        return toAjax(erpCardManagementService.updateErpCardManagement(erpCardManagement));
    }

    /**
     * 删除语音数据和手机卡配置关系
     */
    @PreAuthorize(hasPermi = "erp:management:remove")
    //@Log(title = "语音数据和手机卡配置关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpCardManagementService.deleteErpCardManagementByIds(ids));
    }

    /**
     * 部门通话统计
     */
    @PreAuthorize(hasPermi = "erp:management:call-statis")
    @GetMapping("/call-statis")
    public TableDataInfo callStatis(ErpCardManagementByDept erpCardManagementByDept) {
        startPage();
        List<ErpCardManagementByDept> list = erpCardManagementService.selectErpStatisByDepart(erpCardManagementByDept);
        return getDataTable(list);
    }

    /**
     * 导出部门通话统计
     */
    @PreAuthorize(hasPermi = "erp:management:export-dept")
    //@Log(title = "导出部门通话统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export-dept")
    public void exportDept(HttpServletResponse response, ErpCardManagementByDept erpCardManagementByDept) throws IOException
    {
        List<ErpCardManagementByDept> list = erpCardManagementService.selectErpStatisByDepart(erpCardManagementByDept);
        ExcelUtil<ErpCardManagementByDept> util = new ExcelUtil<ErpCardManagementByDept>(ErpCardManagementByDept.class);
        util.exportExcel(response, list, "部门通话统计");
    }

    /**
     * 员工手机卡通话时长统计
     */
    @PreAuthorize(hasPermi = "erp:management:card-statis")
    @GetMapping("/card-statis")
    public TableDataInfo cardStatis(ErpCardManagementByUser erpCardManagementByUser) {
        startPage();
        List<ErpCardManagementByUser> list = erpCardManagementService.selectErpStatisByUser(erpCardManagementByUser);
        return getDataTable(list);
    }
}
