package com.nnb.erp.domain.vo.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 任务，用于审核，DTO。
 *
 * <AUTHOR>
 * @since 2022/7/6 16:28
 */
@Data
public class TaskForAuditDTO {

    @ApiModelProperty("任务标识。")
    private Integer taskId;

    @ApiModelProperty("任务标识。")
    private List<Integer> taskIds;

    @ApiModelProperty("审核类型：1-通过；2-驳回；3-搁置。 4-变更任务")
    private Integer auditType;

    @ApiModelProperty("原因。")
    private String reason;

    @ApiModelProperty("变更时间。")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date changeTime;

    @ApiModelProperty("变更地点。")
    private String changeAddress;

    @ApiModelProperty("审核备注")
    private String remark;

}
