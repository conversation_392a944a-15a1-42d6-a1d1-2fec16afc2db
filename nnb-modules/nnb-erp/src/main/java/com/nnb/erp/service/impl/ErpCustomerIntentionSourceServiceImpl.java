package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpCustomerIntentionSourceMapper;
import com.nnb.erp.domain.ErpCustomerIntentionSource;
import com.nnb.erp.service.IErpCustomerIntentionSourceService;

/**
 * 记账客户意向来源Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class ErpCustomerIntentionSourceServiceImpl implements IErpCustomerIntentionSourceService 
{
    @Autowired
    private ErpCustomerIntentionSourceMapper erpCustomerIntentionSourceMapper;

    /**
     * 查询记账客户意向来源
     * 
     * @param id 记账客户意向来源主键
     * @return 记账客户意向来源
     */
    @Override
    public ErpCustomerIntentionSource selectErpCustomerIntentionSourceById(Long id)
    {
        return erpCustomerIntentionSourceMapper.selectErpCustomerIntentionSourceById(id);
    }

    /**
     * 查询记账客户意向来源列表
     * 
     * @param erpCustomerIntentionSource 记账客户意向来源
     * @return 记账客户意向来源
     */
    @Override
    public List<ErpCustomerIntentionSource> selectErpCustomerIntentionSourceList(ErpCustomerIntentionSource erpCustomerIntentionSource)
    {
        return erpCustomerIntentionSourceMapper.selectErpCustomerIntentionSourceList(erpCustomerIntentionSource);
    }

    /**
     * 新增记账客户意向来源
     * 
     * @param erpCustomerIntentionSource 记账客户意向来源
     * @return 结果
     */
    @Override
    public int insertErpCustomerIntentionSource(ErpCustomerIntentionSource erpCustomerIntentionSource)
    {
        return erpCustomerIntentionSourceMapper.insertErpCustomerIntentionSource(erpCustomerIntentionSource);
    }

    /**
     * 修改记账客户意向来源
     * 
     * @param erpCustomerIntentionSource 记账客户意向来源
     * @return 结果
     */
    @Override
    public int updateErpCustomerIntentionSource(ErpCustomerIntentionSource erpCustomerIntentionSource)
    {
        return erpCustomerIntentionSourceMapper.updateErpCustomerIntentionSource(erpCustomerIntentionSource);
    }

    /**
     * 批量删除记账客户意向来源
     * 
     * @param ids 需要删除的记账客户意向来源主键
     * @return 结果
     */
    @Override
    public int deleteErpCustomerIntentionSourceByIds(Long[] ids)
    {
        return erpCustomerIntentionSourceMapper.deleteErpCustomerIntentionSourceByIds(ids);
    }

    /**
     * 删除记账客户意向来源信息
     * 
     * @param id 记账客户意向来源主键
     * @return 结果
     */
    @Override
    public int deleteErpCustomerIntentionSourceById(Long id)
    {
        return erpCustomerIntentionSourceMapper.deleteErpCustomerIntentionSourceById(id);
    }
}
