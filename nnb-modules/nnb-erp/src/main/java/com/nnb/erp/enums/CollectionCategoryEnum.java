package com.nnb.erp.enums;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.erp.constant.ReportConstants;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-11-26
 * @Version: 1.0
 */
public enum CollectionCategoryEnum {

    //收款类目：0:无，1:续费，2:税筹，3:增值
    NOTHING(0, "-"),
    RENEW(1, "续费"),
    TAX_PREPARATION(2, "税筹"),
    INCREMENT(3, "增值");

    CollectionCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(Integer code){
        if (ObjectUtil.isEmpty(code)){
            return ReportConstants.DEFAULT_COLLECTION_CATEGORY;
        }
        for(CollectionCategoryEnum collectionCategoryEnum : CollectionCategoryEnum.values()){
            if (collectionCategoryEnum.getCode().equals(code)){
                return collectionCategoryEnum.getDesc();
            }
        }
        return "";
    }
}
