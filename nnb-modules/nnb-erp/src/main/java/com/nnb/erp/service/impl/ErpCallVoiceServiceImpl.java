package com.nnb.erp.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.nnb.common.core.utils.StringUtils;
import com.nnb.erp.domain.ErpPhoneCardConfig;
import com.nnb.erp.domain.ErpPhoneCardStat;
import com.nnb.erp.service.IErpPhoneCardConfigService;
import com.nnb.erp.service.IErpPhoneCardStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpCallVoiceMapper;
import com.nnb.erp.domain.ErpCallVoice;
import com.nnb.erp.service.IErpCallVoiceService;

/**
 * 语音数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-11-01
 */
@Service
public class ErpCallVoiceServiceImpl implements IErpCallVoiceService 
{
    @Autowired
    private ErpCallVoiceMapper erpCallVoiceMapper;

    /**
     * 查询语音数据
     * 
     * @param id 语音数据主键
     * @return 语音数据
     */
    @Override
    public ErpCallVoice selectErpCallVoiceById(Long id)
    {
        return erpCallVoiceMapper.selectErpCallVoiceById(id);
    }

    /**
     * 查询语音数据列表
     * 
     * @param erpCallVoice 语音数据
     * @return 语音数据
     */
    @Override
    public List<ErpCallVoice> selectErpCallVoiceList(ErpCallVoice erpCallVoice)
    {
        return erpCallVoiceMapper.selectErpCallVoiceList(erpCallVoice);
    }

    @Autowired
    private IErpPhoneCardStatService erpPhoneCardStatService;

    @Autowired
    private IErpPhoneCardConfigService erpPhoneCardConfigService;

    /**
     * 新增语音数据
     * 
     * @param erpCallVoice 语音数据
     * @return 结果
     */
    @Override
    public int insertErpCallVoice(ErpCallVoice erpCallVoice)
    {
        erpCallVoiceMapper.insertErpCallVoice(erpCallVoice);
        //将时间 的时分秒置为0
        Date callDate = erpCallVoice.getCallDate();
        LocalDate localDate=callDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date newDate=java.sql.Date.valueOf(localDate);

        //更新语音统计表数据
        ErpPhoneCardConfig erpPhoneCardConfig =  erpPhoneCardConfigService.selectErpPhoneCardConfigByCallDate(erpCallVoice.getPhoneId(),newDate);
        if(StringUtils.isNull(erpPhoneCardConfig)){
            return 1;
        }
        ErpPhoneCardStat erpPhoneCardStat = erpPhoneCardStatService.selectByConfigId(erpPhoneCardConfig.getId());
        if(StringUtils.isNull(erpPhoneCardStat)){
            return 1;
        }
        if(StringUtils.isNull(erpPhoneCardStat.getStartTime())){
            erpPhoneCardStat.setStartTime(erpCallVoice.getCallDate());
        }
        erpPhoneCardStat.setStaffId(erpCallVoice.getStaffId());
        Long callConsume = erpPhoneCardStat.getCallConsume()==null?0:erpPhoneCardStat.getCallConsume();
        Long callConsume2 = new Double(Double.parseDouble(erpCallVoice.getCallDuration())).longValue();
        erpPhoneCardStat.setCallConsume(callConsume + callConsume2);
        erpPhoneCardStat.setCallNumDay((erpPhoneCardStat.getCallNumDay()==null?0:erpPhoneCardStat.getCallNumDay())+1);
        erpPhoneCardStatService.updateErpPhoneCardStat(erpPhoneCardStat);

        return 1;
    }

    /**
     * 修改语音数据
     * 
     * @param erpCallVoice 语音数据
     * @return 结果
     */
    @Override
    public int updateErpCallVoice(ErpCallVoice erpCallVoice)
    {
        return erpCallVoiceMapper.updateErpCallVoice(erpCallVoice);
    }

    /**
     * 批量删除语音数据
     * 
     * @param ids 需要删除的语音数据主键
     * @return 结果
     */
    @Override
    public int deleteErpCallVoiceByIds(Long[] ids)
    {
        return erpCallVoiceMapper.deleteErpCallVoiceByIds(ids);
    }

    /**
     * 删除语音数据信息
     * 
     * @param id 语音数据主键
     * @return 结果
     */
    @Override
    public int deleteErpCallVoiceById(Long id)
    {
        return erpCallVoiceMapper.deleteErpCallVoiceById(id);
    }
}
