package com.nnb.erp.service.impl.approval;

import java.util.List;

import com.nnb.erp.mapper.approval.ApprovalZhongzhuansMapper;
import com.nnb.erp.service.approval.IApprovalZhongzhuansService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.domain.ApprovalZhongzhuans;

/**
 * 审批中转Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-14
 */
@Service
public class ApprovalZhongzhuansServiceImpl implements IApprovalZhongzhuansService
{
    @Autowired
    private ApprovalZhongzhuansMapper approvalZhongzhuansMapper;

    /**
     * 查询审批中转
     * 
     * @param id 审批中转主键
     * @return 审批中转
     */
    @Override
    public ApprovalZhongzhuans selectApprovalZhongzhuansById(Long id)
    {
        return approvalZhongzhuansMapper.selectApprovalZhongzhuansById(id);
    }

    /**
     * 查询审批中转列表
     * 
     * @param approvalZhongzhuans 审批中转
     * @return 审批中转
     */
    @Override
    public List<ApprovalZhongzhuans> selectApprovalZhongzhuansList(ApprovalZhongzhuans approvalZhongzhuans)
    {
        return approvalZhongzhuansMapper.selectApprovalZhongzhuansList(approvalZhongzhuans);
    }

    /**
     * 新增审批中转
     * 
     * @param approvalZhongzhuans 审批中转
     * @return 结果
     */
    @Override
    public int insertApprovalZhongzhuans(ApprovalZhongzhuans approvalZhongzhuans)
    {
        return approvalZhongzhuansMapper.insertApprovalZhongzhuans(approvalZhongzhuans);
    }

    /**
     * 修改审批中转
     * 
     * @param approvalZhongzhuans 审批中转
     * @return 结果
     */
    @Override
    public int updateApprovalZhongzhuans(ApprovalZhongzhuans approvalZhongzhuans)
    {
        return approvalZhongzhuansMapper.updateApprovalZhongzhuans(approvalZhongzhuans);
    }

    /**
     * 批量删除审批中转
     * 
     * @param ids 需要删除的审批中转主键
     * @return 结果
     */
    @Override
    public int deleteApprovalZhongzhuansByIds(Long[] ids)
    {
        return approvalZhongzhuansMapper.deleteApprovalZhongzhuansByIds(ids);
    }

    /**
     * 删除审批中转信息
     * 
     * @param id 审批中转主键
     * @return 结果
     */
    @Override
    public int deleteApprovalZhongzhuansById(Long id)
    {
        return approvalZhongzhuansMapper.deleteApprovalZhongzhuansById(id);
    }
}
