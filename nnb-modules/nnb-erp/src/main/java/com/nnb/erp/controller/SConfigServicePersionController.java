package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.Page;
import com.nnb.erp.domain.SConfigServiceCatalogue;
import com.nnb.erp.domain.vo.service.SConfigServicePersionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.SConfigServicePersion;
import com.nnb.erp.service.ISConfigServicePersionService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@RestController
@RequestMapping("/configServicePersion")
@Api(tags = "SConfigServicePersionController", description = "【请填写功能名称】")
public class SConfigServicePersionController extends BaseController
{
    @Autowired
    private ISConfigServicePersionService sConfigServicePersionService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "查询【请填写功能名称】列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = SConfigServicePersionVo.class)})
    @GetMapping("/getAll")
    public List<SConfigServicePersionVo> getAll(SConfigServicePersion sConfigServicePersion)
    {
        return sConfigServicePersionService.selectSConfigServicePersionList(sConfigServicePersion);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "查询【请填写功能名称】列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = SConfigServicePersionVo.class)})
    @PreAuthorize(hasPermi = "erp:configServicePersion:list")
    @GetMapping("/list")
    public TableDataInfo list(SConfigServicePersion sConfigServicePersion)
    {
        Page<Object> objects = startPageReturn();
        List<SConfigServicePersionVo> list = sConfigServicePersionService.selectSConfigServicePersionList(sConfigServicePersion);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation(value = "导出【请填写功能名称】列表")
    @PreAuthorize(hasPermi = "erp:configServicePersion:export")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SConfigServicePersion sConfigServicePersion) throws IOException
    {
        List<SConfigServicePersionVo> list = sConfigServicePersionService.selectSConfigServicePersionList(sConfigServicePersion);
        ExcelUtil<SConfigServicePersionVo> util = new ExcelUtil<SConfigServicePersionVo>(SConfigServicePersionVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation(value = "获取【请填写功能名称】详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = SConfigServicePersion.class)})
    @PreAuthorize(hasPermi = "erp:configServicePersion:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="【请填写功能名称】id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(sConfigServicePersionService.selectSConfigServicePersionById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation(value = "新增【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:configServicePersion:add")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody SConfigServicePersion sConfigServicePersion)
    {
        return toAjax(sConfigServicePersionService.insertSConfigServicePersion(sConfigServicePersion));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation(value = "修改【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:configServicePersion:edit")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SConfigServicePersion sConfigServicePersion)
    {
        return toAjax(sConfigServicePersionService.updateSConfigServicePersion(sConfigServicePersion));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "删除【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:configServicePersion:remove")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sConfigServicePersionService.deleteSConfigServicePersionByIds(ids));
    }
}
