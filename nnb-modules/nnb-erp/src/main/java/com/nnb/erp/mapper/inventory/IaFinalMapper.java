package com.nnb.erp.mapper.inventory;

import com.nnb.erp.domain.inventory.IaFinal;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 期末结账Mapper接口
 * @Date: 2024-01-05
 * @Version: 1.0
 */
@Repository
public interface IaFinalMapper {

    /**
     * 查询期末结账
     *
     * @param id 期末结账主键
     * @return 期末结账
     */
    public IaFinal selectIaFinalById(Long id);

    /**
     * 查询期末结账列表
     *
     * @param iaFinal 期末结账
     * @return 期末结账集合
     */
    public List<IaFinal> selectIaFinalList(IaFinal iaFinal);

    /**
     * 新增期末结账
     *
     * @param iaFinal 期末结账
     * @return 结果
     */
    public int insertIaFinal(IaFinal iaFinal);

    /**
     * 修改期末结账
     *
     * @param iaFinal 期末结账
     * @return 结果
     */
    public int updateIaFinal(IaFinal iaFinal);

    /**
     * 删除期末结账
     *
     * @param id 期末结账主键
     * @return 结果
     */
    public int deleteIaFinalById(Long id);

    /**
     * 批量删除期末结账
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIaFinalByIds(List<Long> ids);
}
