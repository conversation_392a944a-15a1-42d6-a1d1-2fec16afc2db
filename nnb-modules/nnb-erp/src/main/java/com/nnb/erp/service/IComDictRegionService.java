package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ComDictRegion;
import com.nnb.erp.domain.DictRegion;

/**
 * 城市区域表Service接口
 * 
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IComDictRegionService 
{
    /**
     * 查询城市区域表
     * 
     * @param id 城市区域表主键
     * @return 城市区域表
     */
    public ComDictRegion selectComDictRegionById(String id);

    /**
     * 查询城市区域表列表
     * 
     * @param comDictRegion 城市区域表
     * @return 城市区域表集合
     */
    public List<ComDictRegion> selectComDictRegionList(ComDictRegion comDictRegion);

    /**
     * 新增城市区域表
     * 
     * @param comDictRegion 城市区域表
     * @return 结果
     */
    public int insertComDictRegion(ComDictRegion comDictRegion);

    /**
     * 修改城市区域表
     * 
     * @param comDictRegion 城市区域表
     * @return 结果
     */
    public int updateComDictRegion(ComDictRegion comDictRegion);

    /**
     * 批量删除城市区域表
     * 
     * @param ids 需要删除的城市区域表主键集合
     * @return 结果
     */
    public int deleteComDictRegionByIds(String[] ids);

    /**
     * 删除城市区域表信息
     * 
     * @param id 城市区域表主键
     * @return 结果
     */
    public int deleteComDictRegionById(String id);

    /**
     * 查询城市区域配置列表
     *
     * @param comDictRegion 城市区域表
     * @return 城市区域表集合
     */
    public List<ComDictRegion> selectComDictRegionErpList(ComDictRegion comDictRegion);


    /**
     * 查询城市区域表列表(原始数据)
     *
     * @return 城市区域表集合
     */
    public List<ComDictRegion> selectRegionList();

    public List<DictRegion> selectRegionByIds(String ids);
}
