package com.nnb.erp.mapper;


import com.nnb.erp.domain.CourseInfoEntity;
import com.nnb.erp.domain.vo.course.CourseInfoForUserDTO;
import com.nnb.erp.domain.vo.course.CourseInfoForUserVO;
import com.nnb.erp.domain.vo.course.CourseInfoPageForConfigDTO;
import com.nnb.erp.domain.vo.course.CourseInfoPageForConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程，mapper，接口。
 *
 * <AUTHOR>
 * @date 2022-06-21 13:53:34
 */
public interface CourseInfoMapper {

    /**
     * 在线课程配置列表。
     *
     * @param courseInfoPageForConfigDTO 检索条件。
     * @return 返回课程信息分页查询结果。
     * <AUTHOR>
     * @since 2022-06-21 14:23:22
     */
    public List<CourseInfoPageForConfigVO> getCourseInfoPageForConfig(@Param("query") CourseInfoPageForConfigDTO courseInfoPageForConfigDTO);

    /**
     * 添加实体。
     *
     * @param courseInfoEntity 待添加实体信息。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-06-21 15:54:56
     */
    public Integer insertEntity(CourseInfoEntity courseInfoEntity);

    /**
     * 修改实体。
     *
     * @param courseInfoEntity 待修改实体信息。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-06-21 16:42:50
     */
    public Integer updateEntity(CourseInfoEntity courseInfoEntity);

    /**
     * 更改指定课程状态。
     *
     * @param courseId 课程标识。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-06-21 16:53:41
     */
    public Integer changeStatus(@Param("courseId") Integer courseId);

    /**
     * 根据权限获取课程列表。
     *
     * @param userId               当前登录用户标识。
     * @param courseInfoForUserDTO 查询条件。
     * @return 返回课程列表。
     * <AUTHOR>
     * @since 2022-06-21 17:44:23
     */
    public List<CourseInfoForUserVO> getCourseInfoPageForUser(@Param("roleIds") List<Long> roleIds, @Param("query") CourseInfoForUserDTO courseInfoForUserDTO);

    /**
     * 根据权限获取课程详情。
     *
     * @param userId               当前登录用户标识。
     * @param courseInfoForUserDTO 查询条件。
     * @return 返回课程详情。
     * <AUTHOR>
     * @since 2022-06-21 17:44:23
     */
    public CourseInfoForUserVO getCourseInfoForUser(@Param("roleIds") List<Long> roleIds, @Param("courseId") Integer courseId);

}
