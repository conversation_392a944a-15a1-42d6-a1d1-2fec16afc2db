package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpBizNodeStatus;
import com.nnb.erp.domain.ErpBizStatusConfig;

/**
 * 服务单状态Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface ErpBizNodeStatusMapper 
{
    /**
     * 查询服务单状态
     * 
     * @param numId 服务单状态主键
     * @return 服务单状态
     */
    public ErpBizNodeStatus selectErpBizNodeStatusByNumId(Long numId);

    /**
     * 查询服务单状态列表
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 服务单状态集合
     */
    public List<ErpBizNodeStatus> selectErpBizNodeStatusList(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 新增服务单状态
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 结果
     */
    public int insertErpBizNodeStatus(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 修改服务单状态
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 结果
     */
    public int updateErpBizNodeStatus(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 删除服务单状态
     * 
     * @param numId 服务单状态主键
     * @return 结果
     */
    public int deleteErpBizNodeStatusByNumId(Long numId);

    /**
     * 批量删除服务单状态
     * 
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizNodeStatusByNumIds(Long[] numIds);

    /**
     * 查询服务单状态列表
     *
     * @param erpBizStatusConfig 服务单状态
     * @return 服务单状态集合
     */
    public List<ErpBizNodeStatus> selectNodeStatusListByStatusConfig(ErpBizStatusConfig erpBizStatusConfig);

    /**
     * 修改服务单状态,-注册服务更新BD售后前置节点id
     *
     * @param erpBizNodeStatus 服务单状态
     * @return 结果
     */
    public int updateErpBizNodeStatusByinfoId(ErpBizNodeStatus erpBizNodeStatus);


    /**
     * 查询服务单状态数量
     *
     * @param erpBizNodeStatus 服务单状态
     * @return 服务单状态集合
     */
    public Long selectCountErpBizNodeStatus(ErpBizNodeStatus erpBizNodeStatus);



}
