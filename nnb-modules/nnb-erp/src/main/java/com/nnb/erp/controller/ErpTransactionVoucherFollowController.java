package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.erp.domain.dto.ErpTransactionVoucherFollowDto;
import com.nnb.erp.domain.vo.ErpTransactionVoucherFollowVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpTransactionVoucherFollow;
import com.nnb.erp.service.IErpTransactionVoucherFollowService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-02-27
 */
@RestController
@RequestMapping("/erpTransactionVoucherFollow")
@Api(tags = "ErpTransactionVoucherFollowController", description = "【请填写功能名称】")
public class ErpTransactionVoucherFollowController extends BaseController
{
    @Autowired
    private IErpTransactionVoucherFollowService erpTransactionVoucherFollowService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "查询【请填写功能名称】列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpTransactionVoucherFollow.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpTransactionVoucherFollowDto dto)
    {
        startPage();
        if (ObjectUtil.isEmpty(dto.getStatus())) {
            dto.setStatus(2);
        }
        List<ErpTransactionVoucherFollowVo> list = erpTransactionVoucherFollowService.selectErpTransactionVoucherFollowList(dto);
        return getDataTable(list);
    }

//    /**
//     * 导出【请填写功能名称】列表
//     */
//    @ApiOperation(value = "导出【请填写功能名称】列表")
//    @PreAuthorize(hasPermi = "erp:follow:export")
//    //@Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ErpTransactionVoucherFollow erpTransactionVoucherFollow) throws IOException
//    {
//        List<ErpTransactionVoucherFollow> list = erpTransactionVoucherFollowService.selectErpTransactionVoucherFollowList(erpTransactionVoucherFollow);
//        ExcelUtil<ErpTransactionVoucherFollow> util = new ExcelUtil<ErpTransactionVoucherFollow>(ErpTransactionVoucherFollow.class);
//        util.exportExcel(response, list, "【请填写功能名称】数据");
//    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation(value = "获取【请填写功能名称】详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpTransactionVoucherFollow.class)})
    @PreAuthorize(hasPermi = "erp:follow:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="【请填写功能名称】id") @PathVariable("id") String id)
    {
        return AjaxResult.success(erpTransactionVoucherFollowService.selectErpTransactionVoucherFollowById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation(value = "新增【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:follow:add")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpTransactionVoucherFollow erpTransactionVoucherFollow)
    {
        return toAjax(erpTransactionVoucherFollowService.insertErpTransactionVoucherFollow(erpTransactionVoucherFollow));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation(value = "修改【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:follow:edit")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpTransactionVoucherFollow erpTransactionVoucherFollow)
    {
        return toAjax(erpTransactionVoucherFollowService.updateErpTransactionVoucherFollow(erpTransactionVoucherFollow));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "删除【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:follow:remove")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(erpTransactionVoucherFollowService.deleteErpTransactionVoucherFollowByIds(ids));
    }
}
