package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.SConfigServiceTypeStatus;
import com.nnb.erp.domain.ServiceTypeStatus;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
public interface ISConfigServiceTypeStatusService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SConfigServiceTypeStatus selectSConfigServiceTypeStatusById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SConfigServiceTypeStatus> selectSConfigServiceTypeStatusList(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 结果
     */
    public int insertSConfigServiceTypeStatus(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 结果
     */
    public int updateSConfigServiceTypeStatus(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSConfigServiceTypeStatusByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSConfigServiceTypeStatusById(Long id);


    /**
     * 查询状态
     */
    public List<ServiceTypeStatus> getServiceTypeStatusByIds(String ids);
}
