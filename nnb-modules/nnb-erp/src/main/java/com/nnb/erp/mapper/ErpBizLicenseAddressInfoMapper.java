package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpBizLicenseAddressInfo;

/**
 * 执照关联地址信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface ErpBizLicenseAddressInfoMapper 
{
    /**
     * 查询执照关联地址信息
     * 
     * @param numId 执照关联地址信息主键
     * @return 执照关联地址信息
     */
    public ErpBizLicenseAddressInfo selectErpBizLicenseAddressInfoByNumId(Long numId);

    /**
     * 查询执照关联地址信息列表
     * 
     * @param erpBizLicenseAddressInfo 执照关联地址信息
     * @return 执照关联地址信息集合
     */
    public List<ErpBizLicenseAddressInfo> selectErpBizLicenseAddressInfoList(ErpBizLicenseAddressInfo erpBizLicenseAddressInfo);

    /**
     * 新增执照关联地址信息
     * 
     * @param erpBizLicenseAddressInfo 执照关联地址信息
     * @return 结果
     */
    public int insertErpBizLicenseAddressInfo(ErpBizLicenseAddressInfo erpBizLicenseAddressInfo);

    /**
     * 修改执照关联地址信息
     * 
     * @param erpBizLicenseAddressInfo 执照关联地址信息
     * @return 结果
     */
    public int updateErpBizLicenseAddressInfo(ErpBizLicenseAddressInfo erpBizLicenseAddressInfo);

    /**
     * 删除执照关联地址信息
     * 
     * @param numId 执照关联地址信息主键
     * @return 结果
     */
    public int deleteErpBizLicenseAddressInfoByNumId(Long numId);

    /**
     * 根据删除执照关联地址信息
     *
     * @param numLicenseId 执照id
     * @return 结果
     */
    public int deleteErpBizLicenseAddressInfoByNumLicenseId(Long numLicenseId);

    /**
     * 批量删除执照关联地址信息
     * 
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizLicenseAddressInfoByNumIds(Long[] numIds);
}
