package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ErpContractVvo {
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("合同标识。")
    private Long contractId;

    /**
     * 合同编号。
     */
    @ApiModelProperty("合同编号。")
    private String contractNumber;

}
