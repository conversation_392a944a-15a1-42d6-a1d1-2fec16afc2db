package com.nnb.erp.service;

import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpExamineOtherOrderPay;
import com.nnb.erp.domain.ErpExamineOtherOrderPayInfo;
import net.sf.json.JSONObject;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 订单相关的付款申请Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface IErpExamineOtherOrderPayService 
{
    /**
     * 查询订单相关的付款申请
     * 
     * @param id 订单相关的付款申请主键
     * @return 订单相关的付款申请
     */
    public ErpExamineOtherOrderPay selectErpExamineOtherOrderPayById(Long id);

    /**
     * 查询订单相关的付款申请列表
     * 
     * @param erpExamineOtherOrderPay 订单相关的付款申请
     * @return 订单相关的付款申请集合
     */
    public List<ErpExamineOtherOrderPay> selectErpExamineOtherOrderPayList(ErpExamineOtherOrderPay erpExamineOtherOrderPay);

    /**
     * 新增订单相关的付款申请
     * 
     * @param erpExamineOtherOrderPay 订单相关的付款申请
     * @return 结果
     */
    public int insertErpExamineOtherOrderPay(ErpExamineOtherOrderPay erpExamineOtherOrderPay);

    /**
     * 修改订单相关的付款申请
     * 
     * @param erpExamineOtherOrderPay 订单相关的付款申请
     * @return 结果
     */
    public int updateErpExamineOtherOrderPay(ErpExamineOtherOrderPay erpExamineOtherOrderPay);

    /**
     * 批量删除订单相关的付款申请
     * 
     * @param ids 需要删除的订单相关的付款申请主键集合
     * @return 结果
     */
    public int deleteErpExamineOtherOrderPayByIds(Long[] ids);

    /**
     * 删除订单相关的付款申请信息
     * 
     * @param id 订单相关的付款申请主键
     * @return 结果
     */
    public int deleteErpExamineOtherOrderPayById(Long id);

    public Map<String, Object> getSignInfoByOrderNum(String orderNumber);

    public JSONObject importOtherOrderPayInfo(List<ErpExamineOtherOrderPayInfo> list);
}
