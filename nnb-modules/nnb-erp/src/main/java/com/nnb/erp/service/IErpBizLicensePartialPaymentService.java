package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizLicensePartialPayment;

/**
 * 执照分批付款Service接口
 * 
 * <AUTHOR>
 * @date 2022-04-27
 */
public interface IErpBizLicensePartialPaymentService 
{
    /**
     * 查询执照分批付款
     * 
     * @param numId 执照分批付款主键
     * @return 执照分批付款
     */
    public ErpBizLicensePartialPayment selectErpBizLicensePartialPaymentByNumId(Long numId);

    /**
     * 查询执照分批付款列表
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 执照分批付款集合
     */
    public List<ErpBizLicensePartialPayment> selectErpBizLicensePartialPaymentList(ErpBizLicensePartialPayment erpBizLicensePartialPayment);

    /**
     * 新增执照分批付款
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 结果
     */
    public int insertErpBizLicensePartialPayment(ErpBizLicensePartialPayment erpBizLicensePartialPayment);

    /**
     * 修改执照分批付款
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 结果
     */
    public int updateErpBizLicensePartialPayment(ErpBizLicensePartialPayment erpBizLicensePartialPayment);

    /**
     * 批量删除执照分批付款
     * 
     * @param numIds 需要删除的执照分批付款主键集合
     * @return 结果
     */
    public int deleteErpBizLicensePartialPaymentByNumIds(Long[] numIds);

    /**
     * 删除执照分批付款信息
     * 
     * @param numId 执照分批付款主键
     * @return 结果
     */
    public int deleteErpBizLicensePartialPaymentByNumId(Long numId);
}
