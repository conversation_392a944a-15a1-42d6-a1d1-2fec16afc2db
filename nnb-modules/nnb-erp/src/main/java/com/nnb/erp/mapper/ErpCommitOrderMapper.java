package com.nnb.erp.mapper;

import com.nnb.erp.domain.ErpCombinedActivity;
import com.nnb.erp.domain.ErpProductConfiguration;
import com.nnb.erp.domain.OpZhuXiaoVo;
import com.nnb.erp.domain.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 提单，操作层。
 *
 * <AUTHOR>
 * @since 2022/3/15 16:39
 */
public interface ErpCommitOrderMapper {

    /**
     * 获取待确认订单的产品基本信息。
     *
     * @param productIds 待确认的产品标识、集合。
     * @return 返回产品基本信息集合。
     * <AUTHOR>
     * @since 2022-03-15 17:37:45
     */
    public List<ErpProductForConfirmOrderVO> getProductInfoByIds(@Param("dto") ErpProductConfiguration dto);

    /**
     * @param combinationJson 组合json。
     * @return 返回组合产品详细内容。
     * <AUTHOR>
     * @since 2022-03-16 17:18:05
     * @deprecated 通过产品对象集合json获取组合信息。
     */
    public ErpCombinedForConfirmOrderVO getCombinationByJson(@Param("combinationJson") String combinationJson);

    /**
     * 通过产品标识集合json文本获取组合信息。
     *
     * @param idListToString 产品标识集合json。
     * @return 返回组合产品详细内容。
     * <AUTHOR>
     * @since 2022-03-18 16:10:21
     */
    public ErpCombinedForConfirmOrderVO getCombinationByIdList(@Param("idList") String idListToString);

    /**
     * 获取指定产品可用优惠券，用于CRM确认订单。
     *
     * @param productId        产品标识。
     * @param classificationId 产品类型标识。
     * @param deptId           当前操作用户所属部门标识。
     * @param sumPrice         产品总价，用于过滤满减券。
     * @return 返回优惠券集合。
     * <AUTHOR>
     * @since 2022-03-17 14:05:28
     */
    public List<ErpDiscountForConfirmOrderVO> getDiscountsForCrm(
            @Param("productId") Long productId,
            @Param("classificationId") Long classificationId,
            @Param("deptId") Long deptId,
            @Param("sumPrice") BigDecimal sumPrice
    );

    /**
     * 获取指定产品可用优惠券，用于小程序确认订单。
     *
     * @param phone            小程序用户手机号。
     * @param productId        产品标识。
     * @param classificationId 产品类型标识。
     * @return 返回优惠券集合。
     * <AUTHOR>
     * @since 2022-04-20 14:38:26
     */
    public List<ErpDiscountForConfirmOrderVO> getDiscountsForXcx(
            @Param("phone") String phone,
            @Param("productId") Long productId,
            @Param("classificationId") Long classificationId,
            @Param("sumPrice") BigDecimal sumPrice
    );

    /**
     * 获取订单编号。
     *
     * @param cityId 城市标识。
     * @return 返回订单编号。
     * <AUTHOR>
     * @since 2022-03-24 13:51:14
     */
    public String getOrderNumber(@Param("cityId") Long cityId);

    /**
     * 更新指定订单的收款人信息为最新。
     *
     * @param orderId 订单标识。
     * <AUTHOR>
     * @since 2022-03-24 14:00:55
     */
    public void updatePayeeForOrder(@Param("orderId") Long orderId);

    /**
     * 获取指定订单内、指定产品的废弃状态。
     *
     * @param orderId   订单标识。
     * @param productId 产品标识。
     * @return 返回废弃状态。
     * <AUTHOR>
     * @since 2022-03-29 14:37:41
     */
    public ErpProductForServiceOrdersVO getProductForServiceOrder(@Param("orderId") Long orderId, @Param("productId") Long productId);

    /**
     * 更新指定订单的业务审核标识。
     *
     * @param orderId            订单标识。
     * @param businessApprovalId 业务审核标识。
     * <AUTHOR>
     * @since 2022-04-07 18:22:09
     */
    public void updateApprovalForOrder(@Param("orderId") Long orderId, @Param("businessApprovalId") Long businessApprovalId);

    /**
     * 获取指定订单历史退款金额。
     *
     * @param orderId 订单标识。
     * @return 返回历史退款金额。
     * <AUTHOR>
     * @since 2022-04-19 19:14:28
     */
    public BigDecimal getSummaryRefundPriceByOrderId(@Param("orderId") Long orderId);

    ErpDiscountForConfirmOrderVO getDiscountAmount(Long orderId, Long productId);

    List<ErpCombinedActivity> getCombinationActivity(ErpCombinedActivity erpCombinedActivity);

    List<ErpProductForConfirmOrderVO> getCombinationActivityByIds(@Param("ids") List<Long> activityIdList);

    String getContractNum(@Param("contractId") Long contractId);

    int insterZhuxiao(OpZhuXiaoVo zhuXiaoVo);

    @Update("update erp_order_pay_record set orderId = #{orderId},orderNumber = #{orderNumber} where id = #{billRunId}")
    void updateOrderBill(@Param("billRunId") String billRunId, @Param("orderId") String orderId, @Param("orderNumber") String orderNumber);

    @Update("update erp_order_pay_record set orderId = #{orderId} where clueId = #{clueId}")
    void updateOrderBillByClueId(@Param("clueId") Long clueId, @Param("orderId") Long orderId);

    @Update("update erp_order_pay_record set orderId = #{orderId} where clientId = #{clientId}")
    void updateOrderBillByClientId(@Param("clientId") Long clientId, @Param("orderId") Long orderId);

    @Select("SELECT * FROM xcx_commit_order_bind_product WHERE commit_order_product = #{productId}")
    Map<String,String> selectXcxCommitOrderBindProductByProductId(@Param("productId") Long productId);
}
