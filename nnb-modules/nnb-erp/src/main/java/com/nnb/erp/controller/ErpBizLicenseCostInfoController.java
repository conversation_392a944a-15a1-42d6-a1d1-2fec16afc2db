package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpBizLicenseCostInfo;
import com.nnb.erp.service.IErpBizLicenseCostInfoService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 执照成本付款信息Controller
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@RestController
@RequestMapping("/ErpBizLicenseCostInfo")
@Api(tags = "ErpBizLicenseCostInfoController", description = "执照成本付款信息")
public class ErpBizLicenseCostInfoController extends BaseController
{
    @Autowired
    private IErpBizLicenseCostInfoService erpBizLicenseCostInfoService;

    /**
     * 查询执照成本付款信息列表
     */
    @ApiOperation(value = "查询执照成本付款信息列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpBizLicenseCostInfo.class)})
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpBizLicenseCostInfo erpBizLicenseCostInfo)
    {
        startPage();
        List<ErpBizLicenseCostInfo> list = erpBizLicenseCostInfoService.selectErpBizLicenseCostInfoList(erpBizLicenseCostInfo);
        return getDataTable(list);
    }

    /**
     * 导出执照成本付款信息列表
     */
    @ApiOperation(value = "导出执照成本付款信息列表")
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:export")
    //@Log(title = "执照成本付款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpBizLicenseCostInfo erpBizLicenseCostInfo) throws IOException
    {
        List<ErpBizLicenseCostInfo> list = erpBizLicenseCostInfoService.selectErpBizLicenseCostInfoList(erpBizLicenseCostInfo);
        ExcelUtil<ErpBizLicenseCostInfo> util = new ExcelUtil<ErpBizLicenseCostInfo>(ErpBizLicenseCostInfo.class);
        util.exportExcel(response, list, "执照成本付款信息数据");
    }

    /**
     * 获取执照成本付款信息详细信息
     */
    @ApiOperation(value = "获取执照成本付款信息详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpBizLicenseCostInfo.class)})
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:query")
    @GetMapping(value = "/{numId}")
    public AjaxResult getInfo(@ApiParam(name="numId",value="执照成本付款信息id") @PathVariable("numId") Long numId)
    {
        return AjaxResult.success(erpBizLicenseCostInfoService.selectErpBizLicenseCostInfoByNumId(numId));
    }

    /**
     * 新增执照成本付款信息
     */
    @ApiOperation(value = "新增执照成本付款信息")
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:add")
    //@Log(title = "执照成本付款信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpBizLicenseCostInfo erpBizLicenseCostInfo)
    {
        return toAjax(erpBizLicenseCostInfoService.insertErpBizLicenseCostInfo(erpBizLicenseCostInfo));
    }

    /**
     * 修改执照成本付款信息
     */
    @ApiOperation(value = "修改执照成本付款信息")
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:edit")
    //@Log(title = "执照成本付款信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpBizLicenseCostInfo erpBizLicenseCostInfo)
    {
        return toAjax(erpBizLicenseCostInfoService.updateErpBizLicenseCostInfo(erpBizLicenseCostInfo));
    }

    /**
     * 删除执照成本付款信息
     */
    @ApiOperation(value = "删除执照成本付款信息")
    @PreAuthorize(hasPermi = "erp:ErpBizLicenseCostInfo:remove")
    //@Log(title = "执照成本付款信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{numIds}")
    public AjaxResult remove(@PathVariable Long[] numIds)
    {
        return toAjax(erpBizLicenseCostInfoService.deleteErpBizLicenseCostInfoByNumIds(numIds));
    }
}
