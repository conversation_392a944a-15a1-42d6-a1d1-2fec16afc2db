package com.nnb.erp.mapper;

import java.util.List;

import com.nnb.erp.domain.CostSettlementVo;
import com.nnb.erp.domain.ErpBizServiceAdvance;
import com.nnb.erp.domain.dto.ErpBizServiceAdvanceDto;
import com.nnb.erp.domain.vo.report.CostSettleManageDetailVo;
import org.apache.ibatis.annotations.Param;

/**
 * 预付单号Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
public interface ErpBizServiceAdvanceMapper
{
    /**
     * 查询预付单号
     *
     * @param id 预付单号主键
     * @return 预付单号
     */
    public ErpBizServiceAdvance selectErpBizServiceAdvanceById(Integer id);

    /**
     * 查询预付单号列表
     *
     * @param erpBizServiceAdvance 预付单号
     * @return 预付单号集合
     */
    public List<ErpBizServiceAdvanceDto> selectErpBizServiceAdvanceList(ErpBizServiceAdvance erpBizServiceAdvance);

    /**
     * 新增预付单号
     *
     * @param erpBizServiceAdvance 预付单号
     * @return 结果
     */
    public int insertErpBizServiceAdvance(ErpBizServiceAdvance erpBizServiceAdvance);

    /**
     * 修改预付单号
     *
     * @param erpBizServiceAdvance 预付单号
     * @return 结果
     */
    public int updateErpBizServiceAdvance(ErpBizServiceAdvance erpBizServiceAdvance);

    /**
     * 删除预付单号
     *
     * @param id 预付单号主键
     * @return 结果
     */
    public int deleteErpBizServiceAdvanceById(Integer id);

    /**
     * 批量删除预付单号
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizServiceAdvanceByIds(Integer[] ids);

    CostSettlementVo selectErpBizServiceAdvanceByIds(@Param("intValue") int intValue);

    public List<ErpBizServiceAdvanceDto> selectListByIds(@Param("ids") List<Integer> ids);
}
