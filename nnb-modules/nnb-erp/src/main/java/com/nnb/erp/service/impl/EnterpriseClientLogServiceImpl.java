package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.EnterpriseClientLogMapper;
import com.nnb.erp.domain.EnterpriseClientLog;
import com.nnb.erp.service.IEnterpriseClientLogService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-13
 */
@Service
public class EnterpriseClientLogServiceImpl implements IEnterpriseClientLogService 
{
    @Autowired
    private EnterpriseClientLogMapper enterpriseClientLogMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public EnterpriseClientLog selectEnterpriseClientLogById(Long id)
    {
        return enterpriseClientLogMapper.selectEnterpriseClientLogById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param enterpriseClientLog 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<EnterpriseClientLog> selectEnterpriseClientLogList(EnterpriseClientLog enterpriseClientLog)
    {
        return enterpriseClientLogMapper.selectEnterpriseClientLogList(enterpriseClientLog);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param enterpriseClientLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertEnterpriseClientLog(EnterpriseClientLog enterpriseClientLog)
    {
        return enterpriseClientLogMapper.insertEnterpriseClientLog(enterpriseClientLog);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param enterpriseClientLog 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateEnterpriseClientLog(EnterpriseClientLog enterpriseClientLog)
    {
        return enterpriseClientLogMapper.updateEnterpriseClientLog(enterpriseClientLog);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteEnterpriseClientLogByIds(Long[] ids)
    {
        return enterpriseClientLogMapper.deleteEnterpriseClientLogByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteEnterpriseClientLogById(Long id)
    {
        return enterpriseClientLogMapper.deleteEnterpriseClientLogById(id);
    }
}
