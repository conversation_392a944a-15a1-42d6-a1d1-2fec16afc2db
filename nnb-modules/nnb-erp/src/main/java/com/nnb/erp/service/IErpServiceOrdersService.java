package com.nnb.erp.service;

import java.util.List;

import com.nnb.erp.domain.ErpServiceOrders;

/**
 * 订单产品Service接口
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
public interface IErpServiceOrdersService {
    /**
     * 查询订单产品
     *
     * @param id 订单产品主键
     * @return 订单产品
     */
    public ErpServiceOrders selectErpServiceOrdersById(Long id);

    /**
     * 查询订单产品列表
     *
     * @param erpServiceOrders 订单产品
     * @return 订单产品集合
     */
    public List<ErpServiceOrders> selectErpServiceOrdersList(ErpServiceOrders erpServiceOrders);

    /**
     * 新增订单产品
     *
     * @param erpServiceOrders 订单产品
     * @return 结果
     */
    public int insertErpServiceOrders(ErpServiceOrders erpServiceOrders);

    /**
     * 修改订单产品
     *
     * @param erpServiceOrders 订单产品
     * @return 结果
     */
    public int updateErpServiceOrders(ErpServiceOrders erpServiceOrders);

    /**
     * 批量删除订单产品
     *
     * @param ids 需要删除的订单产品主键集合
     * @return 结果
     */
    public int deleteErpServiceOrdersByIds(Long[] ids);

    /**
     * 删除订单产品信息
     *
     * @param id 订单产品主键
     * @return 结果
     */
    public int deleteErpServiceOrdersById(Long id);

    /**
     * 保存或更改服务单信息。
     *
     * @param erpServiceOrders 服务单实体。
     * @return 返回操作后的服务单标识。
     * <AUTHOR>
     * @since 2022-03-30 11:37:10
     */
    public Long saveOrUpdate(ErpServiceOrders erpServiceOrders);

    /**
     * 根据订单标识更改订单内产品审核状态。
     *
     * @param orderId      订单标识。
     * @param afterStatus  修改前状态。
     * @param beforeStatus 修改后状态。
     * @return 修改的service_order_id。
     * <AUTHOR>
     * @since 2022-06-06 15:56:44
     */
    public List<Long> updateStatusByOrderId(Long orderId, Integer beforeStatus, Integer afterStatus);

    public void dealCommitOrderMoneyData();

    public void getCommitOrderMoneyErrorData();

    public void dealCommitOrderMoneyErrorData();

    public void checkGiveWithServiceType(Long orderId);

}
