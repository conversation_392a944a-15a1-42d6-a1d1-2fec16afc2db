package com.nnb.erp.controller.approval;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.erp.domain.ErpExamineOtherOrderPayInfo;
import com.nnb.erp.domain.ErpExaminePayExport;
import com.nnb.erp.domain.approval.ErpQzdPaymentRecord;
import com.nnb.erp.service.approval.IErpQzdPaymentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 启照多标记付款记录Controller
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/ErpQzdPaymentRecord")
@Api(tags = "ErpQzdPaymentRecordController", description = "启照多标记付款记录")
public class ErpQzdPaymentRecordController extends BaseController
{
    @Autowired
    private IErpQzdPaymentRecordService erpQzdPaymentRecordService;

    /**
     * 查询启照多标记付款记录列表
     */
    @ApiOperation(value = "查询启照多标记付款记录列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpQzdPaymentRecord.class)})
//    @PreAuthorize(hasPermi = "erp:record:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpQzdPaymentRecord erpQzdPaymentRecord)
    {
        startPage();
        List<ErpQzdPaymentRecord> list = erpQzdPaymentRecordService.selectErpQzdPaymentRecordList(erpQzdPaymentRecord);
        return getDataTable(list);
    }

    /**
     * 导出启照多标记付款记录列表
     */
    @ApiOperation(value = "导出启照多标记付款记录列表")
    @PreAuthorize(hasPermi = "erp:record:export")
    //@Log(title = "启照多标记付款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpQzdPaymentRecord erpQzdPaymentRecord) throws IOException
    {
        List<ErpQzdPaymentRecord> list = erpQzdPaymentRecordService.selectErpQzdPaymentRecordList(erpQzdPaymentRecord);
        ExcelUtil<ErpQzdPaymentRecord> util = new ExcelUtil<ErpQzdPaymentRecord>(ErpQzdPaymentRecord.class);
        util.exportExcel(response, list, "启照多标记付款记录数据");
    }

    /**
     * 获取启照多标记付款记录详细信息
     */
    @ApiOperation(value = "获取启照多标记付款记录详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpQzdPaymentRecord.class)})
    @PreAuthorize(hasPermi = "erp:record:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="启照多标记付款记录id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpQzdPaymentRecordService.selectErpQzdPaymentRecordById(id));
    }

    /**
     * 新增启照多标记付款记录
     */
    @ApiOperation(value = "新增启照多标记付款记录")
    @PreAuthorize(hasPermi = "erp:record:add")
    //@Log(title = "启照多标记付款记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpQzdPaymentRecord erpQzdPaymentRecord)
    {
        return toAjax(erpQzdPaymentRecordService.insertErpQzdPaymentRecord(erpQzdPaymentRecord));
    }

    /**
     * 修改启照多标记付款记录
     */
    @ApiOperation(value = "修改启照多标记付款记录")
    @PreAuthorize(hasPermi = "erp:record:edit")
    //@Log(title = "启照多标记付款记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpQzdPaymentRecord erpQzdPaymentRecord)
    {
        return toAjax(erpQzdPaymentRecordService.updateErpQzdPaymentRecord(erpQzdPaymentRecord));
    }

    /**
     * 删除启照多标记付款记录
     */
    @ApiOperation(value = "删除启照多标记付款记录")
    @PreAuthorize(hasPermi = "erp:record:remove")
    //@Log(title = "启照多标记付款记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpQzdPaymentRecordService.deleteErpQzdPaymentRecordByIds(ids));
    }


    @ApiOperation(value = "付款列表导出")
    @PostMapping("/erportPayCostSettlement")
    public void erportPayApprove(HttpServletResponse response, @RequestBody ErpExaminePayExport payExport) throws IOException
    {
        if (ObjectUtil.isNotEmpty(payExport.getIdList()) && payExport.getIdList().size() > 200) {
            throw new ServiceException("所选审批不得大于200条");
        }
        List<ErpExaminePayExport> list = erpQzdPaymentRecordService.erportPayCostSettlement(payExport.getIdList());
        ExcelUtil<ErpExaminePayExport> util = new ExcelUtil(ErpExaminePayExport.class);
        util.exportExcel(response, list, "审批列表");
    }

    @PostMapping("/erportPaytCostSettlementTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<ErpQzdPaymentRecord> util = new ExcelUtil(ErpQzdPaymentRecord.class);
        util.importTemplateExcel(response, "编辑导入模板");
    }

    @PostMapping("/importPaymentCostSettlementList")
    public AjaxResult importInfo(MultipartFile file) throws Exception {
        long size = file.getSize();
        if (size > 5242880l) {
            throw new ServiceException("大小应≤5Mb");
        }
        String fileSuffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));

        if (!(".xls".equals(fileSuffix) || ".xlsx".equals(fileSuffix) || ".csv".equals(fileSuffix))) {
            throw new ServiceException("格式错误，请下载模板");
        }
        ExcelUtil<ErpQzdPaymentRecord> util = new ExcelUtil(ErpQzdPaymentRecord.class);
        List<ErpQzdPaymentRecord> erpQzdPaymentRecordList = util.importExcel(file.getInputStream());
        String message = erpQzdPaymentRecordService.importPaymentList(erpQzdPaymentRecordList);
        return AjaxResult.success(message);
    }
}
