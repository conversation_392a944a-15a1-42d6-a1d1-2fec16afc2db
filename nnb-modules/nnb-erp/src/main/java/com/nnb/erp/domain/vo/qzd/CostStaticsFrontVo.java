package com.nnb.erp.domain.vo.qzd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 成本统计VO
 */
@Data
public class CostStaticsFrontVo {

    @ApiModelProperty("员工")
    private String nickName;

    @ApiModelProperty("回收量")
    private Integer recycleCount;

    @ApiModelProperty("总成本")
    private BigDecimal sumCostPrice;

    @ApiModelProperty("已付成本")
    private BigDecimal paymentCost;

    @ApiModelProperty("收付款")
    private BigDecimal firstPayment;

    @ApiModelProperty("中款")
    private BigDecimal middlePayment;

    @ApiModelProperty("尾款")
    private BigDecimal finalPayment;

    @ApiModelProperty("退款数量")
    private Integer refundAmount;

    @ApiModelProperty("退款金额")
    private BigDecimal refundMoney;


}
