package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.nnb.erp.domain.ErpPromotionalActivitiesProduct;
import com.nnb.erp.domain.gift.ErpGiftRuleProduct;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface ErpPromotionalActivitiesProductMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpPromotionalActivitiesProduct selectErpPromotionalActivitiesProductById(Integer id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpPromotionalActivitiesProduct 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpPromotionalActivitiesProduct> selectErpPromotionalActivitiesProductList(ErpPromotionalActivitiesProduct erpPromotionalActivitiesProduct);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpPromotionalActivitiesProduct 【请填写功能名称】
     * @return 结果
     */
    public int insertErpPromotionalActivitiesProduct(ErpPromotionalActivitiesProduct erpPromotionalActivitiesProduct);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpPromotionalActivitiesProduct 【请填写功能名称】
     * @return 结果
     */
    public int updateErpPromotionalActivitiesProduct(ErpPromotionalActivitiesProduct erpPromotionalActivitiesProduct);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesProductById(Integer id);
    public int deleteErpPromotionalActivitiesProductByActivitieId(Integer activitieId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesProductByIds(Integer[] ids);

    public List<Long> selectActivitieIdByProductStr(@Param("productFindStr") String productFindStr, @Param("type") Integer type);

    public List<ErpPromotionalActivitiesProduct> selectByActivitieIds(@Param("activitieIdList") List<Long> activitieIdList, @Param("type") Integer type);
}
