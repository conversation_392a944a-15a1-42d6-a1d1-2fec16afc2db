package com.nnb.erp.service;

import java.util.List;

import com.nnb.erp.domain.*;

/**
 * 语音数据和手机卡配置关系Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-01
 */
public interface IErpCardManagementService 
{
    /**
     * 查询语音数据和手机卡配置关系
     * 
     * @param id 语音数据和手机卡配置关系主键
     * @return 语音数据和手机卡配置关系
     */
    public ErpCardManagement selectErpCardManagementById(Long id);

    /**
     * 查询语音数据和手机卡配置关系列表
     * 
     * @param erpCardManagement 语音数据和手机卡配置关系
     * @return 语音数据和手机卡配置关系集合
     */
    public List<ErpCardManagement> selectErpCardManagementList(ErpCardManagement erpCardManagement);

    /**
     * 新增语音数据和手机卡配置关系
     * 
     * @param erpCardManagement 语音数据和手机卡配置关系
     * @return 结果
     */
    public int insertErpCardManagement(ErpCardManagement erpCardManagement);

    /**
     * 修改语音数据和手机卡配置关系
     * 
     * @param erpCardManagement 语音数据和手机卡配置关系
     * @return 结果
     */
    public int updateErpCardManagement(ErpCardManagement erpCardManagement);

    /**
     * 批量删除语音数据和手机卡配置关系
     * 
     * @param ids 需要删除的语音数据和手机卡配置关系主键集合
     * @return 结果
     */
    public int deleteErpCardManagementByIds(Long[] ids);

    /**
     * 删除语音数据和手机卡配置关系信息
     * 
     * @param id 语音数据和手机卡配置关系主键
     * @return 结果
     */
    public int deleteErpCardManagementById(Long id);

    /**
     * 根据部门统计语音通话信息
     */
    List<ErpCardManagementByDept> selectErpStatisByDepart(ErpCardManagementByDept erpCardManagementByDept);

    /**
     * 根据员工统计语音通话信息
     */
    List<ErpCardManagementByUser> selectErpStatisByUser(ErpCardManagementByUser erpCardManagementByUser);

    /**
     * 统计语音信息 根据已配置的手机卡统计出剩余通话分钟少于提醒限制分钟数的电话卡
     */
    List<ErpPhoneCardConfigVO>  selectErpStatisToSendEmail();

    List<OffMmatchingPhoneCard> offMmatchingPhoneCardList(ErpCardManagementByUser erpCardManagementByUser);
}
