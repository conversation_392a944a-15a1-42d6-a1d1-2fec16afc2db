package com.nnb.erp.service;

import java.util.Date;
import java.util.List;
import com.nnb.erp.domain.ErpLicenseAccount;
import com.nnb.erp.domain.dto.license.ErpLicenseDTO;
import com.nnb.erp.domain.vo.license.ErpLicenseVO;
import io.swagger.annotations.ApiParam;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 执照会计服务Service接口
 * 
 * <AUTHOR>
 * @date 2023-05-17
 */
public interface IErpLicenseAccountService 
{
    /**
     * 查询执照会计服务
     * 
     * @param id 执照会计服务主键
     * @return 执照会计服务
     */
    public ErpLicenseAccount selectErpLicenseAccountById(Long id);

    /**
     * 查询执照会计服务列表
     * 
     * @param erpLicenseAccount 执照会计服务
     * @return 执照会计服务集合
     */
    public List<ErpLicenseVO> selectErpLicenseAccountList(ErpLicenseDTO erpLicenseAccount);

    /**
     * 新增执照会计服务
     * 
     * @param erpLicenseAccount 执照会计服务
     * @return 结果
     */
    public int insertErpLicenseAccount(ErpLicenseAccount erpLicenseAccount);

    /**
     * 修改执照会计服务
     * 
     * @param erpLicenseAccount 执照会计服务
     * @return 结果
     */
    public int updateErpLicenseAccount(ErpLicenseAccount erpLicenseAccount);

    /**
     * 批量删除执照会计服务
     * 
     * @param ids 需要删除的执照会计服务主键集合
     * @return 结果
     */
    public int deleteErpLicenseAccountByIds(Long[] ids);

    /**
     * 删除执照会计服务信息
     * 
     * @param id 执照会计服务主键
     * @return 结果
     */
    public int deleteErpLicenseAccountById(Long id);

    int operateErpLicenseFollowAccount(Long licenseId, Long userId);

    int handoverErpLicense(Long licenseId);

    int operateErpLicenseAccount(Integer type, Long licenseId, Date date);

    JSONObject getErpLicenseBS(Long licenseId);
}
