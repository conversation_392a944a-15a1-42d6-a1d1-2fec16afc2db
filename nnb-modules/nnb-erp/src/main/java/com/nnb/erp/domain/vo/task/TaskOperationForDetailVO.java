package com.nnb.erp.domain.vo.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作记录，用于详情，VO。
 *
 * <AUTHOR>
 * @since 2022/7/7 11:06
 */
@Data
public class TaskOperationForDetailVO {

    /**
     * 主键标识。
     */
    @ApiModelProperty("主键标识。")
    private Integer id;

    /**
     * 操作。
     */
    @ApiModelProperty("操作。")
    private String title;

    /**
     * 操作人。
     */
    @ApiModelProperty("操作人。")
    private String operationByName;

    /**
     * 操作时间。
     */
    @ApiModelProperty("操作时间。")
    private String operationTime;

    /**
     * 备注。
     */
    @ApiModelProperty("备注。")
    private String remark;

}
