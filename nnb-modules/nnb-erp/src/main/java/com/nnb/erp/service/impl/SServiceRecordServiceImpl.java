package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.SServiceRecordMapper;
import com.nnb.erp.domain.SServiceRecord;
import com.nnb.erp.service.ISServiceRecordService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
@Service
public class SServiceRecordServiceImpl implements ISServiceRecordService 
{
    @Autowired
    private SServiceRecordMapper sServiceRecordMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SServiceRecord selectSServiceRecordById(Long id)
    {
        return sServiceRecordMapper.selectSServiceRecordById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sServiceRecord 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SServiceRecord> selectSServiceRecordList(SServiceRecord sServiceRecord)
    {
        return sServiceRecordMapper.selectSServiceRecordList(sServiceRecord);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sServiceRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSServiceRecord(SServiceRecord sServiceRecord)
    {
        return sServiceRecordMapper.insertSServiceRecord(sServiceRecord);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sServiceRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSServiceRecord(SServiceRecord sServiceRecord)
    {
        return sServiceRecordMapper.updateSServiceRecord(sServiceRecord);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSServiceRecordByIds(Long[] ids)
    {
        return sServiceRecordMapper.deleteSServiceRecordByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSServiceRecordById(Long id)
    {
        return sServiceRecordMapper.deleteSServiceRecordById(id);
    }
}
