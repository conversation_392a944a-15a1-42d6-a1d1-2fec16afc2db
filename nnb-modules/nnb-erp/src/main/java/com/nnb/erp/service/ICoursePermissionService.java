package com.nnb.erp.service;

import com.nnb.erp.domain.vo.course.CoursePermissionUserVO;
import com.nnb.system.api.domain.SysRole;

import java.util.List;

/**
 * 课程权限，服务层，接口。
 *
 * <AUTHOR>
 * @date 2022-06-21 13:50:52
 */
public interface ICoursePermissionService {

    /**
     * 维护课程权限。
     *
     * @param courseId   课程标识。
     * @param courseName 课程名称。
     * @param userIds    用户标识集合。
     * <AUTHOR>
     * @since 2022-06-21 16:08:15
     */
    public void maintainPermission(Integer courseId, String courseName, List<Integer> userIds);

    /**
     * 获取指定部门的所有员工，用于维护课程查看权限。
     *
     * @param deptId 部门标识。
     * @return 返回员工信息。
     * <AUTHOR>
     * @since 2022-06-24 16:51:08
     */
    public List<CoursePermissionUserVO> getUserListByDeptId(Integer deptId);

    /**
     * 获取指定课程的权限员工。
     *
     * @param courseId 课程标识。
     * @return 返回员工信息。
     * <AUTHOR>
     * @since 2022-06-24 16:51:08
     */
    public List<CoursePermissionUserVO> getUserPermissionByCourseId(Integer courseId);

}
