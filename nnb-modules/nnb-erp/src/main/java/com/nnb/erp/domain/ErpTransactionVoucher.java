package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_transaction_voucher
 * 
 * <AUTHOR>
 * @date 2024-02-27
 */
@ApiModel(value="ErpTransactionVoucher",description="【请填写功能名称】对象")
public class ErpTransactionVoucher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("$column.columnComment")
    private Integer id;

    @Excel(name = "erp_order_pay_record.id")
    @ApiModelProperty("erp_order_pay_record.id")
    private Long payRecordId;

    @Excel(name = "类型1本金2活动本金3活动赠金")
    @ApiModelProperty("类型1本金2活动本金3活动赠金")
    private Integer type;

    @Excel(name = "erp_promotional_activities.id")
    @ApiModelProperty("erp_promotional_activities.id")
    private Integer activitieId;

    @Excel(name = "总金额")
    @ApiModelProperty("总金额")
    private BigDecimal allFee;

    @Excel(name = "余额")
    @ApiModelProperty("余额")
    private BigDecimal balance;

    @Excel(name = "可用余额")
    @ApiModelProperty("可用余额")
    private BigDecimal balanceUse;

    @Excel(name = "待入账")
    @ApiModelProperty("待入账")
    private BigDecimal waitEntry;

    @Excel(name = "待扣款")
    @ApiModelProperty("待扣款")
    private BigDecimal waitDeduction;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "赠金截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("赠金截止日期")
    private Date endTime;

    @Excel(name = "操作人")
    @ApiModelProperty("操作人")
    private Long operateUser;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("操作时间")
    private Date opetateTime;

    @ApiModelProperty("扣减可用余额备注")
    private String subtractMemo;

    private Integer zeroPay;

    @ApiModelProperty("赠金关联本金id")
    private Integer selfId;

    @ApiModelProperty("是否在审批流")
    private Integer approveIn;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setPayRecordId(Long payRecordId)
    {
        this.payRecordId = payRecordId;
    }

    public Long getPayRecordId()
    {
        return payRecordId;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setActivitieId(Integer activitieId) 
    {
        this.activitieId = activitieId;
    }

    public Integer getActivitieId() 
    {
        return activitieId;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getBalanceUse() {
        return balanceUse;
    }

    public void setBalanceUse(BigDecimal balanceUse) {
        this.balanceUse = balanceUse;
    }

    public BigDecimal getWaitEntry() {
        return waitEntry;
    }

    public void setWaitEntry(BigDecimal waitEntry) {
        this.waitEntry = waitEntry;
    }

    public BigDecimal getWaitDeduction() {
        return waitDeduction;
    }

    public void setWaitDeduction(BigDecimal waitDeduction) {
        this.waitDeduction = waitDeduction;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(Long operateUser) {
        this.operateUser = operateUser;
    }

    public Date getOpetateTime() {
        return opetateTime;
    }

    public void setOpetateTime(Date opetateTime) {
        this.opetateTime = opetateTime;
    }

    public BigDecimal getAllFee() {
        return allFee;
    }

    public void setAllFee(BigDecimal allFee) {
        this.allFee = allFee;
    }

    public String getSubtractMemo() {
        return subtractMemo;
    }

    public void setSubtractMemo(String subtractMemo) {
        this.subtractMemo = subtractMemo;
    }

    public Integer getZeroPay() {
        return zeroPay;
    }

    public void setZeroPay(Integer zeroPay) {
        this.zeroPay = zeroPay;
    }

    public Integer getSelfId() {
        return selfId;
    }

    public void setSelfId(Integer selfId) {
        this.selfId = selfId;
    }

    public Integer getApproveIn() {
        return approveIn;
    }

    public void setApproveIn(Integer approveIn) {
        this.approveIn = approveIn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("payRecordId", getPayRecordId())
            .append("type", getType())
            .append("activitieId", getActivitieId())
            .toString();
    }
}
