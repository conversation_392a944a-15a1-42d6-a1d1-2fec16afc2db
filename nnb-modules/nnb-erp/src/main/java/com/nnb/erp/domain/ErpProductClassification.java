package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 产品分类对象 erp_product_classification
 * 
 * <AUTHOR>
 * @date 2021-12-27
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ErpProductClassification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Excel(name = "ID")
    private Long numClassificationId;

    /** 产品分类 */
    @Excel(name = "产品分类")
    @NotBlank(message = "产品分类不能为空")
    @Size(min = 0, max = 20, message = "产品分类长度不能超过20个字符")
    private String vcClassificationName;

    /** 状态： 0 未启用，1启用 */
    @Excel(name = "状态： 0 未启用，1启用")
    private Long numIsUse;

    /** 创建人 */
    @Excel(name = "创建人")
    private Long numCreateUserid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datCreateTime;

    /** 最后修改人 */
    @Excel(name = "最后修改人")
    private Long numLastUpdUserid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datLastUpd;

    public void setNumClassificationId(Long numClassificationId) 
    {
        this.numClassificationId = numClassificationId;
    }

    public Long getNumClassificationId() 
    {
        return numClassificationId;
    }
    public void setVcClassificationName(String vcClassificationName) 
    {
        this.vcClassificationName = vcClassificationName;
    }

    public String getVcClassificationName() 
    {
        return vcClassificationName;
    }
    public void setNumIsUse(Long numIsUse) 
    {
        this.numIsUse = numIsUse;
    }

    public Long getNumIsUse() 
    {
        return numIsUse;
    }
    public void setNumCreateUserid(Long numCreateUserid) 
    {
        this.numCreateUserid = numCreateUserid;
    }

    public Long getNumCreateUserid() 
    {
        return numCreateUserid;
    }
    public void setDatCreateTime(Date datCreateTime) 
    {
        this.datCreateTime = datCreateTime;
    }

    public Date getDatCreateTime() 
    {
        return datCreateTime;
    }
    public void setNumLastUpdUserid(Long numLastUpdUserid) 
    {
        this.numLastUpdUserid = numLastUpdUserid;
    }

    public Long getNumLastUpdUserid() 
    {
        return numLastUpdUserid;
    }
    public void setDatLastUpd(Date datLastUpd) 
    {
        this.datLastUpd = datLastUpd;
    }

    public Date getDatLastUpd() 
    {
        return datLastUpd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numClassificationId", getNumClassificationId())
            .append("vcClassificationName", getVcClassificationName())
            .append("numIsUse", getNumIsUse())
            .append("numCreateUserid", getNumCreateUserid())
            .append("datCreateTime", getDatCreateTime())
            .append("numLastUpdUserid", getNumLastUpdUserid())
            .append("datLastUpd", getDatLastUpd())
            .toString();
    }
}
