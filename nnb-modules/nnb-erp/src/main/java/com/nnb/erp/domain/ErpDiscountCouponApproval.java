package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 优惠券额度审批对象 erp_discount_coupon_approval
 * 
 * <AUTHOR>
 * @date 2022-08-12
 */
@ApiModel(value="ErpDiscountCouponApproval",description="优惠券额度审批对象")
public class ErpDiscountCouponApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 申请人id */
    @Excel(name = "申请人id")
    @ApiModelProperty("申请人id")
    private Long applyUserId;

    /** 申请额度 */
    @Excel(name = "申请额度")
    @ApiModelProperty("申请额度")
    private BigDecimal applyAmount;
    @Excel(name = "申请原因")
    @ApiModelProperty("申请原因")
    private String applyReason;


    /** 审批人 */
    @Excel(name = "审批人")
    @ApiModelProperty("审批人")
    private Long approvalUserId;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("审批时间")
    private Date approvalTime;

    /** 审批状态，1：审批中；2：审批通过；3：驳回 */
    @Excel(name = "审批状态，1：审批中；2：审批通过；3：驳回; 4:已发放")
    @ApiModelProperty("审批状态，1：审批中；2：审批通过；3：驳回")
    private Long approvalStatus;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApplyUserId(Long applyUserId) 
    {
        this.applyUserId = applyUserId;
    }

    public Long getApplyUserId() 
    {
        return applyUserId;
    }
    public void setApplyAmount(BigDecimal applyAmount) 
    {
        this.applyAmount = applyAmount;
    }

    public BigDecimal getApplyAmount() 
    {
        return applyAmount;
    }
    public void setApprovalUserId(Long approvalUserId) 
    {
        this.approvalUserId = approvalUserId;
    }

    public Long getApprovalUserId() 
    {
        return approvalUserId;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }
    public void setApprovalStatus(Long approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public Long getApprovalStatus() 
    {
        return approvalStatus;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyUserId", getApplyUserId())
            .append("applyAmount", getApplyAmount())
            .append("approvalUserId", getApprovalUserId())
            .append("approvalTime", getApprovalTime())
            .append("approvalStatus", getApprovalStatus())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
