package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AddressCostVo {

    @ApiModelProperty("订单编号")
    private String orderNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("签约时间")
    private Date signTime;

    @ApiModelProperty("签约人")
    private String signer;

    @ApiModelProperty("注册区域")
    private String registerZone;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("法人")
    private String legalPerson;

    @ApiModelProperty("成本种类")
    private String addressCostName;

    @ApiModelProperty("地址类型")
    private String addressCostType;

    @ApiModelProperty("地址名称")
    private String addressCostTypeName;

    @ApiModelProperty("企业ID")
    private Long numEnterpriseId;
}
