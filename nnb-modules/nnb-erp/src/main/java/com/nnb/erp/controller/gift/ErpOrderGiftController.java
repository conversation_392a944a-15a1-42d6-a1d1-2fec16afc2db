package com.nnb.erp.controller.gift;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dto.gift.ErpOrderGiftDto;
import com.nnb.erp.domain.gift.ErpOrderGift;
import com.nnb.erp.domain.gift.ErpOrderGiftLog;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftIssueDetailVO;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftListVo;
import com.nnb.erp.mapper.ErpOrdersMapper;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftLogMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftMapper;
import com.nnb.erp.service.gift.IErpOrderGiftService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 订单赠品Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/erpOrderGift")
@Api(tags = "ErpOrderGiftController", description = "订单赠品")
public class ErpOrderGiftController extends BaseController
{
    @Autowired
    private IErpOrderGiftService erpOrderGiftService;
    @Autowired
    private ErpOrdersMapper ordersMapper;
    @Autowired
    private ErpOrderGiftMapper erpOrderGiftMapper;
    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;
    @Autowired
    private ErpOrderGiftLogMapper erpOrderGiftLogMapper;



    /**
     * 查询订单赠品列表
     */
    @ApiOperation(value = "查询订单赠品列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpOrderGift.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpOrderGiftDto erpOrderGiftDto)
    {
        startPage();
        List<ErpOrderGiftListVo> list = erpOrderGiftService.selectErpOrderGiftList(erpOrderGiftDto);
        return getDataTable(list);
    }

//    /**
//     * 导出订单赠品列表
//     */
//    @ApiOperation(value = "导出订单赠品列表")
//    @PreAuthorize(hasPermi = "erp:gift:export")
//    //@Log(title = "订单赠品", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ErpOrderGift erpOrderGift) throws IOException
//    {
//        List<ErpOrderGift> list = erpOrderGiftService.selectErpOrderGiftList(erpOrderGift);
//        ExcelUtil<ErpOrderGift> util = new ExcelUtil<ErpOrderGift>(ErpOrderGift.class);
//        util.exportExcel(response, list, "订单赠品数据");
//    }

    /**
     * 获取订单赠品详细信息
     */
    @ApiOperation(value = "获取订单赠品详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpOrderGift.class)})
    @PreAuthorize(hasPermi = "erp:gift:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="订单赠品id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpOrderGiftService.selectErpOrderGiftById(id));
    }

    /**
     * 新增订单赠品
     */
    @ApiOperation(value = "新增订单赠品")
    @PreAuthorize(hasPermi = "erp:gift:add")
    //@Log(title = "订单赠品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpOrderGift erpOrderGift)
    {
        return toAjax(erpOrderGiftService.insertErpOrderGift(erpOrderGift));
    }

    /**
     * 修改订单赠品
     */
    @ApiOperation(value = "修改订单赠品")
    @PreAuthorize(hasPermi = "erp:gift:edit")
    //@Log(title = "订单赠品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpOrderGift erpOrderGift)
    {
        return toAjax(erpOrderGiftService.updateErpOrderGift(erpOrderGift));
    }

    /**
     * 删除订单赠品
     */
    @ApiOperation(value = "删除订单赠品")
    @PreAuthorize(hasPermi = "erp:gift:remove")
    //@Log(title = "订单赠品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpOrderGiftService.deleteErpOrderGiftByIds(ids));
    }



    @GetMapping("/readExcelBingGiftOrder")
    @Transactional(rollbackFor = Exception.class)
    public void readExcelBingGiftOrder() {
        try {
            // 读取Excel文件
            FileInputStream fis = new FileInputStream(new File("E:\\giftOrder.xlsx"));

            // 创建工作簿对象
            Workbook workbook = new XSSFWorkbook(fis);

            // 读取第一个工作表
            Sheet sheet0 = workbook.getSheetAt(0);

            List<String> orderNumList = new ArrayList<>();

            // 遍历每行并读取数据
            for (Row row : sheet0) {
                if (row.getRowNum() == 0) {
                    continue;
                }
                if (ObjectUtil.isEmpty(row.getCell(0).toString())) {
                    return;
                }
                ErpOrders erpOrders = new ErpOrders();
                erpOrders.setVcOrderNumber(row.getCell(0).toString());
                List<ErpOrders> ordersList = ordersMapper.selectErpOrdersList(erpOrders);
                if (ordersList.size() != 1) {
                    throw new ServiceException("订单编号错误。"+row.getCell(0).toString());
                }

                ErpOrderGift erpOrderGift = new ErpOrderGift();
                erpOrderGift.setOrderId(ordersList.get(0).getId());
                erpOrderGift.setConsignee(row.getCell(1).toString());
                erpOrderGift.setGiftIssueRecordId(Long.parseLong(row.getCell(2).toString()));
                erpOrderGift.setAddress(row.getCell(3).toString());
                erpOrderGift.setPhone(row.getCell(4).toString());
                erpOrderGift.setCreateUser(1L);
                erpOrderGift.setGiftStatus(2);

                int i = erpOrderGiftMapper.insertErpOrderGift(erpOrderGift);
                if (i <= 0) {
                    orderNumList.add(row.getCell(0).toString());
                    throw new ServiceException("生成订单赠品记录失败。"+row.getCell(0).toString());
                }
                ErpOrderGiftIssueDetailVO detailVOByRecordId = erpGiftIssueRecordMapper.getErpOrderGiftDetailVOByRecordId(erpOrderGift.getGiftIssueRecordId());
                if (detailVOByRecordId.getSurplusAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("所选赠品已无库存，请更换赠品。"+row.getCell(0).toString());
                }
                int result = erpGiftIssueRecordMapper.reduceErpGiftIssueRecord(erpOrderGift.getGiftIssueRecordId());
                if (result <= 0) {
                    throw new ServiceException("库存扣减失败，请稍后重试。"+row.getCell(0).toString());
                }

                ErpOrderGiftLog erpOrderGiftLog = new ErpOrderGiftLog();
                erpOrderGiftLog.setGiftIssueRecordId(erpOrderGift.getGiftIssueRecordId());
                erpOrderGiftLog.setOrderId(erpOrderGift.getOrderId());
                erpOrderGiftLog.setCreateUser(1L);
                erpOrderGiftLogMapper.insertErpOrderGiftLog(erpOrderGiftLog);
            }
            // 关闭流
            workbook.close();
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
