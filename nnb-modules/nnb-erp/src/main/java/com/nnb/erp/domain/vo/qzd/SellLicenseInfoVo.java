package com.nnb.erp.domain.vo.qzd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SellLicenseInfoVo {

    private Long licenseId;

    @ApiModelProperty("订单编号")
    private String orderNumber;

    @ApiModelProperty("签约部门")
    private String deptName;

    @ApiModelProperty("签约人")
    private String nickName;

    @ApiModelProperty("服务类别")
    private Integer serviceCatalogue;

    @ApiModelProperty("服务类别")
    private String serviceCatalogueName;

    @ApiModelProperty("服务节点")
    private Integer servicePoint;

    @ApiModelProperty("服务节点")
    private String servicePointName;

    @ApiModelProperty("执照类型")
    private Integer type;

    @ApiModelProperty("行业类型")
    private String industryType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("到达启照多")
    private Date arriveQZDTime;

    @ApiModelProperty("线索")
    private Long clue;

    @ApiModelProperty("跟进人")
    private String followUser;

    @ApiModelProperty("应收")
    private BigDecimal numTotalPrice = BigDecimal.ZERO;

    @ApiModelProperty("实收")
    private BigDecimal numPayPrice = BigDecimal.ZERO;

    @ApiModelProperty("尾款")
    private BigDecimal numLastPrice = BigDecimal.ZERO;

    @ApiModelProperty("成本")
    private BigDecimal cost = BigDecimal.ZERO;

    @ApiModelProperty("毛利")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @ApiModelProperty("毛利率")
    private BigDecimal grossMarginRate = BigDecimal.ZERO;

    @ApiModelProperty("执照售价")
    private BigDecimal licenseTotalPrice = BigDecimal.ZERO;

    @ApiModelProperty("记账费")
    private BigDecimal accountPrice = BigDecimal.ZERO;

    @ApiModelProperty("执照来源人")
    private Long licenseSourcePersion;



}
