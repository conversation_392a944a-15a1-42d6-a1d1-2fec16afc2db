package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_examine_approve
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@ApiModel(value="ErpExamineApprove",description="【请填写功能名称】对象")
public class ErpExamineApprove extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "审批ID")
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 其他Id，比如订单id，执照Id等等，根据type变化 */
    @ApiModelProperty("其他Id，比如订单id，执照Id等等，根据type变化")
    private String otherId;

    /** 审批类型 */
    @ApiModelProperty("审批类型")
    private Integer approveType;

    /** 状态0待审批1审批通过2驳回3撤销 */
    @ApiModelProperty("状态0待审批1审批通过2驳回3撤销")
    private Integer approveStatus;

    /** 撤销状态0待审批1审批通过2驳回 */
    @ApiModelProperty("撤销状态0待审批1审批通过2驳回")
    private Integer revokeStatus;

    /** 审批流JSON */
    @ApiModelProperty("审批流JSON")
    private String followInfo;

    /** 该审批流通过人的字符串逗号分割 */
    @ApiModelProperty("该审批流通过人的字符串逗号分割")
    private String passUser;

    /** 该审批流抄送人的字符串逗号分割 */
    @ApiModelProperty("该审批流抄送人的字符串逗号分割")
    private String makeCopyUser;

    /** 当前审批人 */
    @ApiModelProperty("当前审批人")
    private Integer approveCurrentUser;

    /** 当前审批人 */
    @ApiModelProperty("当前审批人")
    private Integer approveCurrentPoint;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Integer createdUser;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createdDate;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private Integer updatedUser;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date updatedTime;

    /** 更新人 */
    @ApiModelProperty("打印状态")
    private Integer printStatus;

    /** 更新人 */
    @ApiModelProperty("付款状态0未付款1待付款2已付款")
    private Integer payStatus;

    /** 更新人 */
    @ApiModelProperty("审核拒绝日志")
    private String remark;

    @ApiModelProperty("补录状态，0：待审批，1：审批通过，2：驳回,3:撤销")
    private Integer supplementStatus;

    @ApiModelProperty("创建时间")
    private Date finishApproveEnd;

    @ApiModelProperty("金额(消耗预算判断用)")
    private BigDecimal fee;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOtherId(String otherId) 
    {
        this.otherId = otherId;
    }

    public String getOtherId() 
    {
        return otherId;
    }
    public void setApproveType(Integer approveType) 
    {
        this.approveType = approveType;
    }

    public Integer getApproveType() 
    {
        return approveType;
    }
    public void setApproveStatus(Integer approveStatus) 
    {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveStatus() 
    {
        return approveStatus;
    }
    public void setRevokeStatus(Integer revokeStatus) 
    {
        this.revokeStatus = revokeStatus;
    }

    public Integer getRevokeStatus() 
    {
        return revokeStatus;
    }
    public void setFollowInfo(String followInfo) 
    {
        this.followInfo = followInfo;
    }

    public String getFollowInfo() 
    {
        return followInfo;
    }
    public void setPassUser(String passUser) 
    {
        this.passUser = passUser;
    }

    public String getPassUser() 
    {
        return passUser;
    }
    public void setMakeCopyUser(String makeCopyUser) 
    {
        this.makeCopyUser = makeCopyUser;
    }

    public String getMakeCopyUser() 
    {
        return makeCopyUser;
    }

    public Integer getApproveCurrentUser() {
        return approveCurrentUser;
    }

    public void setApproveCurrentUser(Integer approveCurrentUser) {
        this.approveCurrentUser = approveCurrentUser;
    }

    public Integer getApproveCurrentPoint() {
        return approveCurrentPoint;
    }

    public void setApproveCurrentPoint(Integer approveCurrentPoint) {
        this.approveCurrentPoint = approveCurrentPoint;
    }

    public void setCreatedUser(Integer createdUser)
    {
        this.createdUser = createdUser;
    }

    public Integer getCreatedUser() 
    {
        return createdUser;
    }
    public void setCreatedDate(Date createdDate) 
    {
        this.createdDate = createdDate;
    }

    public Date getCreatedDate() 
    {
        return createdDate;
    }
    public void setUpdatedUser(Integer updatedUser) 
    {
        this.updatedUser = updatedUser;
    }

    public Integer getUpdatedUser() 
    {
        return updatedUser;
    }
    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSupplementStatus() {
        return supplementStatus;
    }

    public void setSupplementStatus(Integer supplementStatus) {
        this.supplementStatus = supplementStatus;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Date getFinishApproveEnd() {
        return finishApproveEnd;
    }

    public void setFinishApproveEnd(Date finishApproveEnd) {
        this.finishApproveEnd = finishApproveEnd;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("otherId", getOtherId())
            .append("approveType", getApproveType())
            .append("approveStatus", getApproveStatus())
            .append("revokeStatus", getRevokeStatus())
            .append("followInfo", getFollowInfo())
            .append("passUser", getPassUser())
            .append("makeCopyUser", getMakeCopyUser())
            .append("createdUser", getCreatedUser())
            .append("createdDate", getCreatedDate())
            .append("updatedUser", getUpdatedUser())
            .append("updatedTime", getUpdatedTime())
            .toString();
    }
}
