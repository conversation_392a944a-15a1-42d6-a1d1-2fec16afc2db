package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpBizSettlement;
import com.nnb.erp.domain.vo.ErpBizSettlementDto;
import com.nnb.erp.domain.vo.ErpBizSettlementModel;
import com.nnb.erp.domain.vo.ErpBizSettlementVo;

/**
 * 服务单结算Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface ErpBizSettlementMapper 
{
    /**
     * 查询服务单结算
     * 
     * @param numId 服务单结算主键
     * @return 服务单结算
     */
    public ErpBizSettlement selectErpBizSettlementByNumId(Long numId);

    /**
     * 查询服务单结算列表
     * 
     * @param erpBizSettlement 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlement> selectErpBizSettlementList(ErpBizSettlement erpBizSettlement);

    /**
     * 新增服务单结算
     * 
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int insertErpBizSettlement(ErpBizSettlement erpBizSettlement);

    /**
     * 修改服务单结算
     * 
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int updateErpBizSettlement(ErpBizSettlement erpBizSettlement);

    /**
     * 删除服务单结算
     * 
     * @param numId 服务单结算主键
     * @return 结果
     */
    public int deleteErpBizSettlementByNumId(Long numId);

    /**
     * 批量删除服务单结算
     * 
     * @param numIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpBizSettlementByNumIds(Long[] numIds);

    /**
     * 查询服务单结算
     *
     * @param numInfoId 服务单主键
     * @return 服务单结算
     */
    public ErpBizSettlementModel selectErpBizSettlementVoByNumInfoId(Long numInfoId);

    /**
     * 查询服务单结算列表
     *
     * @param dto 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlementVo> selectErpBizSettlementVoList(ErpBizSettlementDto dto);

    /**
     * 查询预付款审核
     *
     * @param dto 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlementVo> selectErpBizSettlementVoByAdvanceList(ErpBizSettlementDto dto);
}
