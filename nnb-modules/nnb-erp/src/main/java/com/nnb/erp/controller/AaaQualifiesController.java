package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.AaaQualifies;
import com.nnb.erp.service.IAaaQualifiesService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 3A资质Controller
 * 
 * <AUTHOR>
 * @date 2022-10-16
 */
@RestController
@RequestMapping("/aaaQualifies")
@Api(tags = "AaaQualifiesController", description = "3A资质")
public class AaaQualifiesController extends BaseController
{
    @Autowired
    private IAaaQualifiesService aaaQualifiesService;

    /**
     * 查询3A资质列表
     */
    @ApiOperation(value = "查询3A资质列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = AaaQualifies.class)})
    @PreAuthorize(hasPermi = "erp:aaaQualifies:list")
    @GetMapping("/list")
    public TableDataInfo list(AaaQualifies aaaQualifies)
    {
        Page<Object> objects = startPageReturn();
        List<AaaQualifies> list = aaaQualifiesService.selectAaaQualifiesList(aaaQualifies);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    /**
     * 导出3A资质列表
     */
    @ApiOperation(value = "导出3A资质列表")
    @PreAuthorize(hasPermi = "erp:aaaQualifies:export")
    //@Log(title = "3A资质", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AaaQualifies aaaQualifies) throws IOException
    {
        List<AaaQualifies> list = aaaQualifiesService.selectAaaQualifiesList(aaaQualifies);
        ExcelUtil<AaaQualifies> util = new ExcelUtil<AaaQualifies>(AaaQualifies.class);
        util.exportExcel(response, list, "3A资质数据");
    }

    /**
     * 获取3A资质详细信息
     */
    @ApiOperation(value = "获取3A资质详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = AaaQualifies.class)})
    @PreAuthorize(hasPermi = "erp:aaaQualifies:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="3A资质id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(aaaQualifiesService.selectAaaQualifiesById(id));
    }

    /**
     * 新增3A资质
     */
    @ApiOperation(value = "新增3A资质")
    @PreAuthorize(hasPermi = "erp:aaaQualifies:add")
    //@Log(title = "3A资质", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody AaaQualifies aaaQualifies)
    {
        return toAjax(aaaQualifiesService.insertAaaQualifies(aaaQualifies));
    }

    /**
     * 修改3A资质
     */
    @ApiOperation(value = "修改3A资质")
    @PreAuthorize(hasPermi = "erp:aaaQualifies:edit")
    //@Log(title = "3A资质", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody AaaQualifies aaaQualifies)
    {
        return toAjax(aaaQualifiesService.updateAaaQualifies(aaaQualifies));
    }

    /**
     * 删除3A资质
     */
    @ApiOperation(value = "删除3A资质")
    @PreAuthorize(hasPermi = "erp:aaaQualifies:remove")
    //@Log(title = "3A资质", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(aaaQualifiesService.deleteAaaQualifiesByIds(ids));
    }
}
