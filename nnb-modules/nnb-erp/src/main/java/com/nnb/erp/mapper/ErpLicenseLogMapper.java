package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpLicenseLog;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
public interface ErpLicenseLogMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpLicenseLog selectErpLicenseLogById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpLicenseLog> selectErpLicenseLogList(ErpLicenseLog erpLicenseLog);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 结果
     */
    public int insertErpLicenseLog(ErpLicenseLog erpLicenseLog);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpLicenseLog 【请填写功能名称】
     * @return 结果
     */
    public int updateErpLicenseLog(ErpLicenseLog erpLicenseLog);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpLicenseLogById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpLicenseLogByIds(Long[] ids);
}
