package com.nnb.erp.config;

import com.nnb.erp.constant.RedissonConstant;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2024-01-09
 * @Version: 1.0
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private String redisPort;

    @Value("${spring.redis.database}")
    private int redisDatabase;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        String url = String.format(RedissonConstant.URL_TEMPLATE, redisHost, redisPort);
        config.useSingleServer()
//                .setDatabase(redisDatabase)
                .setAddress(url);
//                .setPassword(redisPassword);
        return Redisson.create(config);
    }
}
