package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.erp.domain.ErpExamineApprove;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpExamineCommonPaymentVo extends ErpExamineApprove {

    @ApiModelProperty("创建人名称")
    private String createdUserName;
    @ApiModelProperty("创建人部门")
    private String createdUserDeptName;
    @ApiModelProperty("审批状态")
    private String approveStatusName;
    @ApiModelProperty("审批类型名称")
    private String approveTypeName;
    @ApiModelProperty("申请部门")
    private Integer deptId;
    @ApiModelProperty("付款事由")
    private String payReason;
    @ApiModelProperty("付款金额")
    private BigDecimal fee;
    @ApiModelProperty("发票")
    private String invoice;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("付款时间")
    private Date payTime;
    @ApiModelProperty("收款人")
    private String payee;
    @ApiModelProperty("银行卡号")
    private String bankNumber;
    @ApiModelProperty("银行卡名称")
    private String bankName;
    @ApiModelProperty("备注")
    private String memo;
    @ApiModelProperty("附件")
    private String attachment;

    private Long voucherId;
    private String tradeId;
    private String billNo;
    private String documentNumber;
}
