package com.nnb.erp.service.inventory;

import com.nnb.erp.domain.inventory.IaWarehouseArchives;

import java.util.List;
import java.util.Map;

/**
 * 仓库档案Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface IaWarehouseArchivesService {
    /**
     * 查询仓库档案
     *
     * @param id 仓库档案主键
     * @return 仓库档案
     */
    public IaWarehouseArchives selectIaWarehouseArchivesById(Long id);

    /**
     * 查询仓库档案列表
     *
     * @param iaWarehouseArchives 仓库档案
     * @return 仓库档案集合
     */
    public List<IaWarehouseArchives> selectIaWarehouseArchivesList(IaWarehouseArchives iaWarehouseArchives);

    /**
     * 新增仓库档案
     *
     * @param iaWarehouseArchives 仓库档案
     * @return 结果
     */
    public int insertIaWarehouseArchives(IaWarehouseArchives iaWarehouseArchives);

    /**
     * 修改仓库档案
     *
     * @param iaWarehouseArchives 仓库档案
     * @return 结果
     */
    public int updateIaWarehouseArchives(IaWarehouseArchives iaWarehouseArchives);

    /**
     * 批量修改仓库档案
     * @param iaWarehouseArchives 仓库档案
     * @return
     */
    public int batchUpdateIaWarehouseArchives(IaWarehouseArchives iaWarehouseArchives);

    /**
     * 批量删除仓库档案
     *
     * @param ids 需要删除的仓库档案主键集合
     * @return 结果
     */
    public int deleteIaWarehouseArchivesByIds(List<Long> ids);

    /**
     * 删除仓库档案信息
     *
     * @param id 仓库档案主键
     * @return 结果
     */
    public int deleteIaWarehouseArchivesById(Long id);

    /**
     * 获取最大编码
     * @param
     * @return
     */
    String getMaxCode();
}
