package com.nnb.erp.domain.vo.qzd;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 财务看板
 */
@Data
public class FinanceBoardExportVo {

    @ApiModelProperty("执照编号")
    @Excel(name = "执照编号")
    private String licenseNo;

    @ApiModelProperty("执照状态")
    @Excel(name = "执照状态")
    private String licenseStatus;

    @ApiModelProperty("区域")
    @Excel(name = "执照状态")
    private String areaName;

    @Excel(name = "执照状态")
    @ApiModelProperty("地址成本")
    private BigDecimal addressCost = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照已付款")
    private BigDecimal payment = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照付退款详情")
    private List<LicensePaymentVo> LicensePaymentVoList;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照未付款")
    private BigDecimal nonPayment = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照预算成本")
    private BigDecimal budgetCost = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照总成本")
    private BigDecimal sumCost = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("执照售价")
    private BigDecimal totalPrice = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("毛利")
    private BigDecimal grossMargin = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("毛利率")
    private BigDecimal grossMarginRate = BigDecimal.ZERO;

    @Excel(name = "执照状态")
    @ApiModelProperty("订单编号")
    private String orderNum;

    @Excel(name = "执照状态")
    @ApiModelProperty("订单审核状态")
    private String orderStatus;

    @Excel(name = "执照状态")
    @ApiModelProperty("销售")
    private String seller;

    @Excel(name = "执照状态")
    @ApiModelProperty("部门")
    private String deptName;

    @Excel(name = "执照状态")
    @ApiModelProperty("已收")
    private BigDecimal numPaymentPrice;

    @ApiModelProperty("订单收退款详情")
    private List<OrderPaymentVo> orderPaymentVoList;

    @Excel(name = "执照状态")
    @ApiModelProperty("尾款")
    private BigDecimal numLastPrice;


    @Excel(name = "执照状态")
    @ApiModelProperty("记账费")
    private BigDecimal accountPrice;

}
