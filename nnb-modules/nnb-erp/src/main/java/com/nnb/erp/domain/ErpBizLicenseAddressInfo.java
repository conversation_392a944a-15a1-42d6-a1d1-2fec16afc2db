package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 执照关联地址信息对象 erp_biz_license_address_info
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@ApiModel(value="ErpBizLicenseAddressInfo",description="执照关联地址信息对象")
public class ErpBizLicenseAddressInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long numId;

    /** 原地址：1；迁址：2； */
    @Excel(name = "原地址：1；迁址：2；")
    @ApiModelProperty("原地址：1；迁址：2；")
    private Long numType;

    /** 地址 */
    @Excel(name = "地址")
    @ApiModelProperty("地址")
    private String vcAddress;

    /** 地址成本id */
    @Excel(name = "地址成本id")
    @ApiModelProperty("地址成本id")
    private Long numCostManageId;

    /** 执照id */
    @Excel(name = "执照id")
    @ApiModelProperty("执照id")
    private Long numLicenseId;



    public void setNumId(Long numId) 
    {
        this.numId = numId;
    }

    public Long getNumId() 
    {
        return numId;
    }
    public void setNumType(Long numType) 
    {
        this.numType = numType;
    }

    public Long getNumType() 
    {
        return numType;
    }
    public void setVcAddress(String vcAddress) 
    {
        this.vcAddress = vcAddress;
    }

    public String getVcAddress() 
    {
        return vcAddress;
    }
    public void setNumCostManageId(Long numCostManageId) 
    {
        this.numCostManageId = numCostManageId;
    }

    public Long getNumCostManageId() 
    {
        return numCostManageId;
    }
    public void setNumLicenseId(Long numLicenseId)
    {
        this.numLicenseId = numLicenseId;
    }

    public Long getNumLicenseId()
    {
        return numLicenseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numId", getNumId())
            .append("numType", getNumType())
            .append("vcAddress", getVcAddress())
            .append("numCostManageId", getNumCostManageId())
            .append("numLicenseId", getNumLicenseId())
            .toString();
    }
}
