package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizSettlement;
import com.nnb.erp.domain.vo.ErpBizSettlementDto;
import com.nnb.erp.domain.vo.ErpBizSettlementModel;
import com.nnb.erp.domain.vo.ErpBizSettlementVo;

/**
 * 服务单结算Service接口
 * 
 * <AUTHOR>
 * @date 2022-04-09
 */
public interface IErpBizSettlementService 
{
    /**
     * 查询服务单结算
     * 
     * @param numId 服务单结算主键
     * @return 服务单结算
     */
    public ErpBizSettlement selectErpBizSettlementByNumId(Long numId);

    /**
     * 查询服务单结算列表
     * 
     * @param erpBizSettlement 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlement> selectErpBizSettlementList(ErpBizSettlement erpBizSettlement);

    /**
     * 查询成本结算列表
     *
     * @param dto 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlementVo> selectErpBizSettlementVoList(ErpBizSettlementDto dto);

    /**
     * 查询预付款审核
     *
     * @param dto 服务单结算
     * @return 服务单结算集合
     */
    public List<ErpBizSettlementVo> selectErpBizSettlementVoByAdvanceList(ErpBizSettlementDto dto);

    /**
     * 新增服务单结算
     * 
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int insertErpBizSettlement(ErpBizSettlement erpBizSettlement);

    /**
     * 修改服务单结算
     * 
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int updateErpBizSettlement(ErpBizSettlement erpBizSettlement);

    /**
     * 批量删除服务单结算
     * 
     * @param numIds 需要删除的服务单结算主键集合
     * @return 结果
     */
    public int deleteErpBizSettlementByNumIds(Long[] numIds);

    /**
     * 删除服务单结算信息
     * 
     * @param numId 服务单结算主键
     * @return 结果
     */
    public int deleteErpBizSettlementByNumId(Long numId);

    /**
     * 查询服务单结算
     *
     * @param numInfoId 服务单主键
     * @return 服务单结算
     */
    public ErpBizSettlementModel selectErpBizSettlementVoByNumInfoId(Long numInfoId);


    /**
     * 新增服务单结算
     *
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int insertErpBizSettlementModel(ErpBizSettlementModel erpBizSettlement);

    /**
     * 修改服务单结算
     *
     * @param erpBizSettlement 服务单结算
     * @return 结果
     */
    public int updateErpBizSettlementModel(ErpBizSettlementModel erpBizSettlement);



}
