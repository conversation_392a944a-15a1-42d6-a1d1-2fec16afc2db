package com.nnb.erp.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ErpProductTypeByClassVo implements Serializable {
    private static final long serialVersionUID = -4474326743172129602L;

    /** ID */
    private Long numTypeId;

    /** 产品类型 */
    private String vcTypeName;
}
