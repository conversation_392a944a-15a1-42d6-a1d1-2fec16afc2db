package com.nnb.erp.domain.dto.enterprise;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 已成交企业标签对象 erp_old_enterprise_tags
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ErpOldEnterpriseTags",description="已成交企业标签对象")
@Data
public class ErpAddOldEnterpriseTagsDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long numId;

    /** 企业id */
    @Excel(name = "企业id")
    @ApiModelProperty("企业id")
    private Long enterpriseId;

    /** 老客户标签id */
    @Excel(name = "老客户标签id")
    @ApiModelProperty("老客户标签id")
    private List<Long> numUserTagId;


}
