package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpApprovalConfig;

/**
 * 流程配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
public interface ErpApprovalConfigMapper 
{
    /**
     * 查询流程配置
     * 
     * @param id 流程配置主键
     * @return 流程配置
     */
    public ErpApprovalConfig selectErpApprovalConfigById(Long id);

    /**
     * 查询流程配置列表
     * 
     * @param erpApprovalConfig 流程配置
     * @return 流程配置集合
     */
    public List<ErpApprovalConfig> selectErpApprovalConfigList(ErpApprovalConfig erpApprovalConfig);

    /**
     * 新增流程配置
     * 
     * @param erpApprovalConfig 流程配置
     * @return 结果
     */
    public int insertErpApprovalConfig(ErpApprovalConfig erpApprovalConfig);

    /**
     * 修改流程配置
     * 
     * @param erpApprovalConfig 流程配置
     * @return 结果
     */
    public int updateErpApprovalConfig(ErpApprovalConfig erpApprovalConfig);

    /**
     * 删除流程配置
     * 
     * @param id 流程配置主键
     * @return 结果
     */
    public int deleteErpApprovalConfigById(Long id);

    /**
     * 批量删除流程配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpApprovalConfigByIds(Long[] ids);
}
