package com.nnb.erp.domain.approval;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 启照多标记付款记录对象 erp_qzd_payment_record
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@ApiModel(value="ErpQzdPaymentRecord",description="启照多标记付款记录对象")
public class ErpQzdPaymentRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 审批业务表ID */
    @Excel(name = "审批ID")
    @ApiModelProperty("审批业务表ID")
    private Long approvalServiceId;

    /** 付款金额 */
    @Excel(name = "付款金额")
    @ApiModelProperty("付款金额")
    private BigDecimal paymentAmount;

    /** 付款凭证 */
    @ApiModelProperty("付款凭证")
    private String paymentVoucher;

    /** 付款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "支付日期（格式为：年-月-日）", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("付款日期")
    private Date paymentTime;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private Long updateUser;

    /** 删除标记，0：未删除，1：已删除 */
    @ApiModelProperty("删除标记，0：未删除，1：已删除")
    private Integer delFlag;

    @ApiModelProperty("申请类型")
    private Integer approveType;
    @Excel(name = "付款银行")
    @ApiModelProperty(name = "付款银行")
    private String paymentBank;
    @Excel(name = "收款人")
    @ApiModelProperty(name = "收款人")
    private String paymentUserName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApprovalServiceId(Long approvalServiceId) 
    {
        this.approvalServiceId = approvalServiceId;
    }

    public Long getApprovalServiceId() 
    {
        return approvalServiceId;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) 
    {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getPaymentAmount() 
    {
        return paymentAmount;
    }
    public void setPaymentVoucher(String paymentVoucher) 
    {
        this.paymentVoucher = paymentVoucher;
    }

    public String getPaymentVoucher() 
    {
        return paymentVoucher;
    }
    public void setPaymentTime(Date paymentTime) 
    {
        this.paymentTime = paymentTime;
    }

    public Date getPaymentTime() 
    {
        return paymentTime;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    public Integer getApproveType() {
        return approveType;
    }

    public void setApproveType(Integer approveType) {
        this.approveType = approveType;
    }

    public String getPaymentBank() {
        return paymentBank;
    }

    public void setPaymentBank(String paymentBank) {
        this.paymentBank = paymentBank;
    }

    public String getPaymentUserName() {
        return paymentUserName;
    }

    public void setPaymentUserName(String paymentUserName) {
        this.paymentUserName = paymentUserName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("approvalServiceId", getApprovalServiceId())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentVoucher", getPaymentVoucher())
            .append("paymentTime", getPaymentTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
