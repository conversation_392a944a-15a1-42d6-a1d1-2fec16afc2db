package com.nnb.erp.enums;

import com.nnb.erp.domain.vo.approval.ApprovalNewNodes;

import java.util.ArrayList;
import java.util.List;

public enum ApprovalTypeEnum {

    COMMIT_ORDER(1, "提单"),
    EDIT_ORDR(2, "订单编辑"),
    MODIFY_ORDER(3, "订单修改"),
    REFUND_ORDER(4, "订单退款"),
    CANCEL_ORDER(5, "订单作废")

    ;

    private Integer type;

    private String name;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ApprovalTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNodeNameByCode(String type) {
        ApprovalTypeEnum[] values = ApprovalTypeEnum.values();
        for (ApprovalTypeEnum value : values) {
            if (type.equals(value.getType())) {
                return value.getName();
            }
        }
        return null;
    }
}
