package com.nnb.erp.domain.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单审核，用于订单审核列表查询及操作，DTO。
 *
 * <AUTHOR>
 * @since 2022/4/24 10:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ErpOrderExamineDTO extends BaseEntity {

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private Long orderId;

    private List<Long> orderIdList;

    /**
     * 审核角色：1部门经理，2财务会计。
     */
    @ApiModelProperty("审核角色：1部门经理，2财务会计 3法务 4 业务对接人。")
    private Integer examineRole;

    /**
     * 审核类型：1提单，2修改，3退款，4作废。
     */
    @ApiModelProperty("审核类型：1提单，2修改，3退款，4作废。")
    private Integer examineType;

    /**
     * 审核操作：1通过，2驳回。
     */
    @ApiModelProperty("审核操作：1通过，2驳回。")
    private Integer examineOperation;

    /**
     * 审核备注。
     */
    @ApiModelProperty("审核备注。")
    private String examineRemark;

    /**
     * 关键字搜索：订单编号精确搜索，联系人、公司名称、签约人模糊搜索。
     */
    @ApiModelProperty("关键字搜索：订单编号精确搜索，联系人、公司名称、签约人模糊搜索。")
    private String keyword;

    /**
     * 创建时间-开始。
     */
    @ApiModelProperty("创建时间-开始。")
    private String createTimeBegin;

    /**
     * 创建时间-结束。
     */
    @ApiModelProperty("创建时间-结束。")
    private String createTimeEnd;

    @ApiModelProperty("部门标识")
    private Long deptId;

    @ApiModelProperty("部门标识")
    private List<Long> deptIdList;

    @ApiModelProperty("财务更新时间。")
    private String financeTime;
    @ApiModelProperty("电子合同是否重新签约，1是，0否")
    private Integer isReturn;

    @ApiModelProperty("电子合同状态")
    private Integer status;

    @ApiModelProperty("汇款方式。")
    private ErpPaymentTermForCommitOrderDTO paymentTerm;

    @ApiModelProperty("业务对接人审批ID。")
    private Long approvalId;

    @ApiModelProperty("用户ID。")
    private Long userId;

    private String vcContractNumber;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("提单是否电子合同: 1：电子合同，2 纸质合同，3：不需要合同")
    private Integer isElectronicContract;

    @ApiModelProperty("订单修改更换合同类型:  1：电子合同，2 纸质合同，3：不需要合同")
    private Integer isElectronicContractNew;

    @ApiModelProperty("收款开始")
    private Date collectionTimeBegin;

    @ApiModelProperty("收款结束")
    private Date collectionTimeEnd;
}
