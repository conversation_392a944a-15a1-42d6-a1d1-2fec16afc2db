package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.OpZhuxiaoMapper;
import com.nnb.erp.domain.OpZhuxiao;
import com.nnb.erp.service.IOpZhuxiaoService;

/**
 * order_productions的冗余Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-20
 */
@Service
public class OpZhuxiaoServiceImpl implements IOpZhuxiaoService 
{
    @Autowired
    private OpZhuxiaoMapper opZhuxiaoMapper;

    /**
     * 查询order_productions的冗余
     * 
     * @param id order_productions的冗余主键
     * @return order_productions的冗余
     */
    @Override
    public OpZhuxiao selectOpZhuxiaoById(Long id)
    {
        return opZhuxiaoMapper.selectOpZhuxiaoById(id);
    }

    /**
     * 查询order_productions的冗余列表
     * 
     * @param opZhuxiao order_productions的冗余
     * @return order_productions的冗余
     */
    @Override
    public List<OpZhuxiao> selectOpZhuxiaoList(OpZhuxiao opZhuxiao)
    {
        return opZhuxiaoMapper.selectOpZhuxiaoList(opZhuxiao);
    }

    /**
     * 新增order_productions的冗余
     * 
     * @param opZhuxiao order_productions的冗余
     * @return 结果
     */
    @Override
    public int insertOpZhuxiao(OpZhuxiao opZhuxiao)
    {
        return opZhuxiaoMapper.insertOpZhuxiao(opZhuxiao);
    }

    /**
     * 修改order_productions的冗余
     * 
     * @param opZhuxiao order_productions的冗余
     * @return 结果
     */
    @Override
    public int updateOpZhuxiao(OpZhuxiao opZhuxiao)
    {
        return opZhuxiaoMapper.updateOpZhuxiao(opZhuxiao);
    }

    /**
     * 批量删除order_productions的冗余
     * 
     * @param ids 需要删除的order_productions的冗余主键
     * @return 结果
     */
    @Override
    public int deleteOpZhuxiaoByIds(Long[] ids)
    {
        return opZhuxiaoMapper.deleteOpZhuxiaoByIds(ids);
    }

    /**
     * 删除order_productions的冗余信息
     * 
     * @param id order_productions的冗余主键
     * @return 结果
     */
    @Override
    public int deleteOpZhuxiaoById(Long id)
    {
        return opZhuxiaoMapper.deleteOpZhuxiaoById(id);
    }
}
