package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 代理商配置对象 erp_biz_service_agent
 * 
 * <AUTHOR>
 * @date 2023-11-22
 */
@ApiModel(value="ErpBizServiceAgent",description="代理商配置对象")
public class ErpBizServiceAgent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("代理代号 ")
    private String agentNumber;

    @Excel(name = "联系人")
    private String contactName;

    @Excel(name = "联系人手机号")
    private String contactPhone;

    @ApiModelProperty("地址")
    @Excel(name = "地址")
    private String address;

    /** $column.columnComment */
    @Excel(name = "地址")
    @ApiModelProperty("$column.columnComment")
    private Long createdUser;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "地址", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date createdDate;

    /** $column.columnComment */
    @Excel(name = "地址")
    @ApiModelProperty("$column.columnComment")
    private Long updateUser;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "地址", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date updateDate;

    @ApiModelProperty("1新数据0旧数据")
    private Integer newData;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAgentNumber(String agentNumber) 
    {
        this.agentNumber = agentNumber;
    }

    public String getAgentNumber() 
    {
        return agentNumber;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setCreatedUser(Long createdUser) 
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser() 
    {
        return createdUser;
    }
    public void setCreatedDate(Date createdDate) 
    {
        this.createdDate = createdDate;
    }

    public Date getCreatedDate() 
    {
        return createdDate;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }

    public Integer getNewData() {
        return newData;
    }

    public void setNewData(Integer newData) {
        this.newData = newData;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentNumber", getAgentNumber())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("address", getAddress())
            .append("createdUser", getCreatedUser())
            .append("createdDate", getCreatedDate())
            .append("updateUser", getUpdateUser())
            .append("updateDate", getUpdateDate())
            .toString();
    }
}
