package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.erp.domain.ErpSetRecords;
import com.nnb.erp.service.IErpSetRecordsService;

/**
 * 手机卡套餐超时信息推送记录Controller
 * 
 * <AUTHOR>
 * @date 2021-11-11
 */
@RestController
@RequestMapping("/records")
public class ErpSetRecordsController extends BaseController
{
    @Autowired
    private IErpSetRecordsService erpSetRecordsService;

    /**
     * 查询手机卡套餐超时信息推送记录列表
     */
    @PreAuthorize(hasPermi = "erp:records:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpSetRecords erpSetRecords)
    {
        startPage();
        List<ErpSetRecords> list = erpSetRecordsService.selectErpSetRecordsList(erpSetRecords);
        return getDataTable(list);
    }

    /**
     * 导出手机卡套餐超时信息推送记录列表
     */
    @PreAuthorize(hasPermi = "erp:records:export")
    //@Log(title = "手机卡套餐超时信息推送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpSetRecords erpSetRecords) throws IOException
    {
        List<ErpSetRecords> list = erpSetRecordsService.selectErpSetRecordsList(erpSetRecords);
        ExcelUtil<ErpSetRecords> util = new ExcelUtil<ErpSetRecords>(ErpSetRecords.class);
        util.exportExcel(response, list, "手机卡套餐超时信息推送记录数据");
    }

    /**
     * 获取手机卡套餐超时信息推送记录详细信息
     */
    @PreAuthorize(hasPermi = "erp:records:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(erpSetRecordsService.selectErpSetRecordsByConfigId(configId));
    }

    /**
     * 新增手机卡套餐超时信息推送记录
     */
    @PreAuthorize(hasPermi = "erp:records:add")
    //@Log(title = "手机卡套餐超时信息推送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpSetRecords erpSetRecords)
    {
        return toAjax(erpSetRecordsService.insertErpSetRecords(erpSetRecords));
    }

    /**
     * 修改手机卡套餐超时信息推送记录
     */
    @PreAuthorize(hasPermi = "erp:records:edit")
    //@Log(title = "手机卡套餐超时信息推送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpSetRecords erpSetRecords)
    {
        return toAjax(erpSetRecordsService.updateErpSetRecords(erpSetRecords));
    }

    /**
     * 删除手机卡套餐超时信息推送记录
     */
    @PreAuthorize(hasPermi = "erp:records:remove")
    //@Log(title = "手机卡套餐超时信息推送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(erpSetRecordsService.deleteErpSetRecordsByConfigIds(configIds));
    }
}
