package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.nnb.common.core.web.domain.BaseEntity;
import com.nnb.erp.domain.ErpEnterprise;
import com.nnb.erp.domain.ErpOrders;
import com.nnb.erp.domain.dto.CollectionReportDto;
import com.nnb.erp.domain.dto.SaleListingDto;
import com.nnb.erp.domain.enterprise.ErpOrderDetail;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.qzd.LicenseSalesBoardVo;
import com.nnb.erp.domain.vo.report.SalesListingVo;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Repository
public interface ErpOrdersMapper {
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public ErpOrders selectErpOrdersById(Long id);

    public ErpOrders selectOrderByOrderNumber(@Param("orderNumber") String orderNumber);

    public List<ErpOrders> selectErpOrdersByIdList(@Param("idList") List<Long> idList);

    public ErpOrders selectErpOrdersBycipherId(String cipherId);

    @Select("select count(0) from erp_orders where vc_order_number = #{vcOrderNumber}")
    public int selectCountByOrderNumber(@Param("vcOrderNumber")String vcOrderNumber);

    /**
     * 查询订单列表
     *
     * @param erpOrders 订单
     * @return 订单集合
     */
    public List<ErpOrders> selectErpOrdersList(ErpOrders erpOrders);

    /**
     * 根据clientId查询订单
     * @param clientIdList
     * @return
     */
    public List<ErpOrderDetail> selectOrderListByClientId(@Param("clientIdList") List<Long> clientIdList);

    /**
     * 新增订单
     *
     * @param erpOrders 订单
     * @return 结果
     */
    public int insertErpOrders(ErpOrders erpOrders);

    /**
     * 修改订单
     *
     * @param erpOrders 订单
     * @return 结果
     */
    public int updateErpOrders(ErpOrders erpOrders);

    /**
     * 删除订单
     *
     * @param id 订单主键
     * @return 结果
     */
    public int deleteErpOrdersById(Long id);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpOrdersByIds(Long[] ids);

    public List<ErpOrderInfoForOmListVO> selectErpOrdersListByClientId(@Param("ids") Long[] ids, @Param("status") int[] status, @Param("numValidStatus") int[] numValidStatus);

    /**
     * 获取订单列表，用于订单管理列表展示。
     *
     * @param query 筛选条件。
     * @return 返回订单列表。
     * <AUTHOR>
     * @since 2022-03-24 11:18:45
     */
    List<ErpOrderInfoForOmListVO> getOrderListForOm(@Param("query") ErpOrderQueryForOmListDTO query);

    /**
     * 获取指定订单内、服务单的全部已审核退款。
     *
     * @param orderId 订单标识。
     * @return 返回退款金额。
     * <AUTHOR>
     * @since 2022-03-24 11:18:30
     */
    BigDecimal getSumRefundPriceByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取订单各个状态的统计数据。
     *
     * @return 返回订单各个状态的统计数据。
     * <AUTHOR>
     * @since 2022-03-25 11:16:41
     */
    ErpOrderStatusCountVO getOrderStatusCount(BaseEntity e);

    /**
     * 获取指定订单回款记录。
     *
     * @param orderId 订单标识。
     * @return 返回回款记录信息。
     * <AUTHOR>
     * @since 2022-03-27 10:31:14
     */
    List<ErpRetainageForOmListVO> getRetainageByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取客户、订单信息，用于订单详情。
     *
     * @param orderId 订单标识。
     * @return 返回订单详情信息。
     * <AUTHOR>
     * @since 2022-03-27 18:06:23
     */
    ErpOrderDetailForOmVO getClientAndOrderInfoForOrderDetail(@Param("orderId") Long orderId);

    /**
     * 获取客户信息，用于订单详情。
     *
     * @param orderId 订单标识。
     * @return 返回客户信息。
     * <AUTHOR>
     * @since 2022-04-02 15:39:44
     */
    ErpClientForOrderDetailVO getClientForOrderDetailByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取订单基本信息，用于订单详情。
     *
     * @param orderId 订单标识。
     * @return 返回订单基本信息。
     * <AUTHOR>
     * @since 2022-04-02 15:40:173
     */
    ErpOrderForOrderDetailVO getOrderForOrderDetailByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取产品信息，用于订单详情。
     *
     * @param orderId 订单标识。
     * @return 返沪订单信息。
     * <AUTHOR>
     * @since 2022-03-28 10:13:59
     */
    List<ErpProductForOrderDetailVO> getProductsForOrderDetail(@Param("orderId") Long orderId);

    /**
     * 获取指定订单提交订单时的收款记录。
     *
     * @param orderId 订单标识。
     * @return 返回收款记录。
     * <AUTHOR>
     * @since 2022-03-29 18:10:36
     */
    List<ErpPaymentForOmListVO> getCommitPaymentsByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据尾款标识查询回款&服务单详情。
     *
     * @param retainageId 尾款标识。
     * @return 返回回款&服务单详情。
     * <AUTHOR>
     * @since 2022-03-30 09:34:26。
     */
    List<ErpRetainageReturnDetailForOrderListVO> getRetainageDetailByRetainageId(@Param("retainageId") Long retainageId);

    /**
     * 获取指定订单状态。
     *
     * @param orderId 订单标识。
     * @return 返回订单状态。
     * <AUTHOR>
     * @since 2022-03-30 17:19:48
     */
    public ErpOrderStatusVO getOrderStatus(@Param("orderId") Long orderId);

    /**
     * 修改订单支付状态、实收金额、尾款金额。
     *
     * @param orderId 订单标识。
     * <AUTHOR>
     * @since 2022-04-14 14:55:27
     */
    public void payOrderForMobile(@Param("orderId") Long orderId);

    /**
     * 获取指定订单内的所有服务订单标识。
     *
     * @param orderId 订单标识。
     * @return 返回服务订单标识集合。
     * <AUTHOR>
     * @since 2022-04-19 20:20:29
     */
    public List<Long> getServiceOrderIdByOrderId(@Param("orderId") Long orderId);

    /**
     * 查询订单审核列表。
     *
     * @param erpOrderExamineDTO 查询条件。
     * @return 返回订单审核列表。
     * <AUTHOR>
     * @since 2022-04-24 11:11:41
     */
    public List<ErpOrderInfoForExamineVO> getOrderExamineList(@Param("query") ErpOrderExamineDTO erpOrderExamineDTO);

    public List<ErpOrderInfoForExamineVO> getServiceApprovalList(@Param("query") ErpOrderExamineDTO erpOrderExamineDTO);

    /**
     * 更新指定订单的实收金额、尾款金额，用于退款。
     *
     * @param orderId     orderId。
     * @param refundPrice 退款金额。
     * @param userId      当前操作用户标识。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-04-24 15:12:36
     */
    public int updatePayPriceForRefund(@Param("orderId") Long orderId, @Param("refundPrice") BigDecimal refundPrice, @Param("userId") Long userId);

    /**
     * 维护订单信息，通过查询统计各个关联表来维护订单金额。
     *
     * @param orderId 订单标识。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-05-23 10:43:56
     */
    public int maintainOrderInfo(@Param("orderId") Long orderId);

    /**
     * 获取指定订单内的产品类型。
     *
     * @param orderId 订单标识。
     * @return 返回产品类型标识。
     * <AUTHOR>
     * @since 2022-06-09 14:28:50
     */
    public List<Long> getServiceOrderTypeByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取指定订单的电子合同主体。
     *
     * @param orderId 订单标识。
     * @return 返回电子合同主体。
     * <AUTHOR>
     * @since 2022-07-19 17:19:00
     */
    public Integer getContractSubject(@Param("orderId") Integer orderId);

    List<ErpContractVvo> getOnlieContractByOrderId(@Param("orderId") Long orderId);

    ErpContractVvo getContractByOrderId(@Param("orderId") Long orderId);

    List<ErpOrderInfoForOmListVO> getOnlieContractByOrderIdList(ErpOrderQueryForOmListDTO query);

    /**
     * 查询集合
     * @return
     */
    List<PaymentInfoVo> selectPaymentInfoList(@Param("paymentInfo") PaymentInfoVo paymentInfoVo);

    int updateErpOrdersByCipherId(@Param("eleContactUrl") String eleContactUrl, @Param("cipherId") String cipherId);

    int updateXcxMsgByCipherId(@Param("cipherId") String cipherId, @Param("urlLink") String urlLink, @Param("qrCode") String qrCode);

    @Select("select vc_name as k,vc_other_content as v from bd_configurations where num_op_type = 23 and num_status = 1")
    List<Map<String, Object>> getOrderListConfig();

    Map<String, Object> selectSumServiceOrdersPrice(@Param("orderId") Long orderId);

    Map<String, Object> selectRetainageReturnPrice(@Param("orderId") Long orderId);

    List<SalesListingVo> getSalesListing(SaleListingDto saleListingDto);

    long getSalesListingCount(SaleListingDto saleListingDto);

    List<ErpRetainageForOmListVO> getRetainageByOrderIds(@Param("orderList") List<Long> orderList);

    List<ErpOrderStatusVO> getOrderStatuList(@Param("orderIds")List<Long> orderIds);

    ErpOrderStatusCountVO getOrderListForOmCount(@Param("query")ErpOrderQueryForOmListDTO query);

    @Select(" select max(eo.id) as orderId,eed.num_erp_enterprise_id as enterpriseId from erp_orders eo " +
            " left join erp_client ec ON eo.num_client_id = ec.id " +
            " LEFT JOIN erp_enterprise_detail eed ON eed.num_erp_enterprise_id = ec.num_enterprise_id " +
            " where eo.num_valid_status = 0" +
            " and eo.num_valid_status != 5" +
            " and eo.num_create_order_examine_status = 5 and eo.num_cancel_order_examine_status = 0 and eo.num_refund_examine_status = 0 " +
            " and (eo.num_modify_order_examine_status = 0 or eo.num_modify_order_examine_status = 5) " +
            " and (ISNULL( eed.customer_complaint )  or eed.customer_complaint = 2 or (eed.customer_complaint = 1 and eed.customer_complaint_solve = 1))  " +
            " AND eo.num_user_id not in (-1,0)" +
            " GROUP BY eed.num_erp_enterprise_id")
    List<Map<String, Object>> getOrderListByOldEnterpriseFollow();

    @Select("select follow_user_id from bd_clue where vc_phone = #{phone} and num_status = #{numStatus} limit 1;")
    Long selectFollowUserIdByPhoneInKb(@Param("phone") String phone, @Param("numStatus") int numStatus);

    List<LicenseSalesBoardVo> selectLicenseSalesBoardVo(SaleListingDto saleListingDto);

    @Update("update erp_orders set num_valid_status = #{numValidStatus} where id = #{orderId}")
    int updateOrderNumValidStatusById(@Param("orderId") Long orderId, @Param("numValidStatus") Integer numValidStatus);

    @Update("update erp_orders set num_refund_price = num_refund_price + #{orderRefundAmount} where id = #{orderId}")
    int updateOrderRefundAmountById(@Param("orderId") Long orderId, @Param("orderRefundAmount") BigDecimal orderRefundAmount);

    @Select("select count(id) from erp_orders where vc_phone = #{phone}")
    int countOrderByPhone(@Param("phone") String phone);

    @Select("select count(id) from erp_orders where num_client_id = #{clientId}")
    int countOrderByClientId(@Param("clientId") Long clientId);

    @Select("select share_phone from xcx_activity_share where new_phone = #{phone} and share_type = 2 order by create_time limit 1")
    String selectOldCusByNewCusPhone(@Param("phone") String phone);

    @Select("select origin_phone from xcx_activity_share where new_phone = #{phone} and share_type = 2 order by create_time limit 1")
    String selectOriginCusByNewCusPhone(@Param("phone") String phone);

    @Select("select share_phone from xcx_activity_share where new_phone = #{phone} and share_phone != #{sharePhone} and share_type = 2 order by create_time")
    List<String> selectOldPhoneList(@Param("newPhone") String newPhone, @Param("sharePhone") String sharePhone);


    @Select("select * from sys_user where nick_name = #{nickName}")
    SysUser selectUserIdByNickName(@Param("nickName") String nickName);



    List<CollectionReportVo> collectionReport(CollectionReportDto dto);

    @Select("SELECT IFNULL(SUM(errd.num_collection_price), 0) AS fee, GROUP_CONCAT(err.follow_ids) AS followIds " +
            "FROM erp_retainage_return_detail errd " +
            "LEFT JOIN erp_retainage_return err ON errd.num_retainage_return_id = err.id " +
            "WHERE errd.num_service_order_id = #{serviceOrderId} AND err.num_status = 1 AND err.dat_finance_collection_time < #{collectionTime} GROUP BY errd.id")
    List<Map<String, Object>> selectPayPriceByServiceOrderId(@Param("serviceOrderId") Long serviceOrderId, @Param("collectionTime") String collectionTime);

    @Select("select id from erp_orders where cipher_id = #{cipherId}")
    Long selectByCipherId(@Param("cipherId") String cipherId);

    @Select("SELECT epa.id AS approveId, epa.approve_status as approveStatus, eor.refund_amount as refundAmount FROM erp_examine_approve epa LEFT JOIN erp_order_refund eor ON epa.other_id = eor.id " +
            "WHERE epa.approve_type = 33 AND epa.approve_status IN (0,1) AND eor.order_id = #{orderId} ")
    List<Map<String, Object>> getRefoundInfoByOrderId(@Param("orderId") Long orderId);

    @Select("SELECT IFNULL( SUM( eoi.invoice_fee ), 0 ) AS kpFee FROM erp_examine_approve epa LEFT JOIN erp_order_invoice eoi ON epa.other_id = eoi.id " +
            "WHERE epa.approve_type = 116 AND epa.approve_status = #{approveStatus} AND eoi.order_id = #{orderId} ")
    BigDecimal getKPFeeByOrderId(@Param("orderId") Long orderId, @Param("approveStatus") Long approveStatus);

    @Select("SELECT IFNULL( SUM( eso.num_pay_price ), 0 ) as notSupportFee FROM erp_service_orders eso LEFT JOIN erp_product_detail epd ON eso.num_product_id = epd.num_product_id " +
            "WHERE eso.num_status = 1 AND eso.num_is_deprecated = 0 AND epd.kp_support = 2 AND eso.num_order_id = #{orderId}")
    BigDecimal getNoSupportKPFeeByOrderId(@Param("orderId") Long orderId);


    @Select("select * from erp_orders where vc_order_number = #{vcOrderNumber}")
    public ErpOrders selectByOrderNumber(@Param("vcOrderNumber")String vcOrderNumber);

    @Select("SELECT ee.id,ee.vc_company_name FROM erp_orders eo " +
            "LEFT JOIN erp_client ec ON eo.num_client_id = ec.id LEFT JOIN erp_enterprise ee ON ec.num_enterprise_id = ee.id " +
            "WHERE eo.vc_order_number = #{orderNumber}")
    public ErpEnterprise selectEnterpriseByOrderNumber(@Param("orderNumber") String orderNumber);

    List<ErpOrders> selectErpOrdersByIds(@Param("orderIds") List<Long> orderIds);

    @Select("SELECT IFNULL( SUM( eoi.invoice_fee ), 0 ) AS kpFee FROM erp_examine_approve epa LEFT JOIN erp_order_invoice eoi ON epa.other_id = eoi.id " +
            "WHERE epa.approve_type = 116 AND epa.approve_status = #{approveStatus} AND eoi.order_id = #{orderId} ")
    BigDecimal getKPFeeByOrderIdAndApproveStatus(@Param("orderId") Long orderId, @Param("approveStatus") Long approveStatus);

    List<ErpOrders> selectOrderByOrderNumberList(@Param("orderNumberList") List<String> orderNumberList);
}
