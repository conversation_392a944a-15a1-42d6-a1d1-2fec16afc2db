package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-01-04
 * @Version: 1.0
 */
@Data
public class IncrementCustomerExport implements Serializable {

    @Excel(name = "id")
    private Long id;

    @Excel(name = "代办状态", readConverterExp = "0=默认,1=公司代办,2=企业自办")
    private Long dbStatus;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "订单编号")
    private String orderNum;

    @Excel(name = "服务类别")
    private String serviceCatalogueName;

    @Excel(name = "服务类型")
    private String serviceTypeName;

    @Excel(name = "服务状态")
    private String serviceStatusName;

    @Excel(name = "服务节点")
    private String servicePointName;

    @Excel(name = "节点状态")
    private String servicePointStatusName;

    @Excel(name = "是否记账", readConverterExp = "1=是,0=否")
    private int isAccount;

    @Excel(name = "是否资质", readConverterExp = "1=是,0=否")
    private int isQualifications;

    @Excel(name = "BD状态", readConverterExp = "0=未转,1=已转,2=转回")
    private Long isBd;

    @Excel(name = "转售后", readConverterExp = "0=未转,1=已转,2=转回")
    private Long isAftermarket;

    @Excel(name = "联系人名称")
    private String contactName;

    @Excel(name = "公司名称")
    private String vcCompanyName;

    @Excel(name = "城市")
    private String cityName;

    @Excel(name = "区域")
    private String regionName;

    @Excel(name = "部门")
    private String depName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到达业支时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdTime;

    @Excel(name = "销售")
    private String sellerName;

    @Excel(name = "业支")
    private String serverUserName;

    @Excel(name = "产品")
    private String productName;

    @Excel(name = "增值")
    private String incrementUserName;

    @Excel(name = "会计")
    private String accountUserName;

    @Excel(name = "有无尾款",readConverterExp = "1=有,0=无")
    private Long hasTailFee;

    @Excel(name = "供应商")
    private String supplierName;

    @Excel(name = "结算状态",readConverterExp = "1=是,0=否")
    private Long settleStatus;

    @Excel(name = "搁置原因")
    private String shelveName;

    @Excel(name = "金额")
    private BigDecimal orderFee;

    @Excel(name = "收款")
    private BigDecimal collectionFee;

    @Excel(name = "尾款")
    private BigDecimal tailFee;

    @Excel(name = "法人")
    private String legalPersonName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "信息收集时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date getInfoTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收取尾款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tailFeeTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "转工商时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date toBusinessTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "网登时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date onlineTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出执照时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date permitTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "转增值时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date toIncrementTime;

    @Excel(name = "企业曾用名")
    private String vcHistoryName;

}
