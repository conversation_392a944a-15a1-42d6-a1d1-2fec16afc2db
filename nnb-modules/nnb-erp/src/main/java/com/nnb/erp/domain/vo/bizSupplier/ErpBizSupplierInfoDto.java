package com.nnb.erp.domain.vo.bizSupplier;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value="ErpBizSupplierInfoDto",description="供应商信息")
public class ErpBizSupplierInfoDto {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty("主键id")
    private Long numId;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    @ApiModelProperty("供应商名称")
    private String vcSupplierName;

    /** 区域（省市区） */
    @Excel(name = "区域", readConverterExp = "省=市区")
    @ApiModelProperty("区域（省市区）")
    private Long numAreaId;

    /** 执照：1；地址：2；资质：3；知识产权：4 */
    @Excel(name = "执照：1；地址：2；资质：3；知识产权：4")
    @ApiModelProperty("执照：1；地址：2；资质：3；知识产权：4")
    private Long numSupplierType;

    private List<Long> types;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String vcContacts;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty("手机号")
    private String vcContactsPhone;

    /** 是预付款：1；否预付款：2 */
    @Excel(name = "是预付款：1；否预付款：2")
    @ApiModelProperty("是预付款：1；否预付款：2")
    private Long numIsAdvanceCharge;

    /** 合作：1；非合作：2； */
    @Excel(name = "合作：1；非合作：2；")
    @ApiModelProperty("合作：1；非合作：2；")
    private Long numIsCooperation;

    /** 录入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("录入时间")
    private Date datInputDate;

    /** 合作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("合作时间")
    private Date datCooperationDate;

    /** 停止合作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "停止合作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("停止合作时间")
    private Date datStopCooperationDate;

    /** 停止合作原因 */
    @Excel(name = "停止合作原因")
    @ApiModelProperty("停止合作原因")
    private String vcStopCooperationReason;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date datCreatedTime;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long numCreatedBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date datUpdatedTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long datUpdatedBy;

    // 执照数量
    @ApiModelProperty("执照数量")
    private int numLicenseCount;

    // 执照类型ids
    @ApiModelProperty("执照类型ids")
    private List<Long> bizLicenseTypeIds;
}
