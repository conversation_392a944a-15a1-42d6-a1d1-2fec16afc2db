package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.XcxProductShare;

/**
 * 小程序订单产品分享Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-18
 */
public interface XcxProductShareMapper 
{
    /**
     * 查询小程序订单产品分享
     * 
     * @param id 小程序订单产品分享主键
     * @return 小程序订单产品分享
     */
    public XcxProductShare selectXcxProductShareById(Long id);
    public XcxProductShare getByEncryption(String encryption);



    /**
     * 查询小程序订单产品分享列表
     * 
     * @param xcxProductShare 小程序订单产品分享
     * @return 小程序订单产品分享集合
     */
    public List<XcxProductShare> selectXcxProductShareList(XcxProductShare xcxProductShare);

    /**
     * 新增小程序订单产品分享
     * 
     * @param xcxProductShare 小程序订单产品分享
     * @return 结果
     */
    public int insertXcxProductShare(XcxProductShare xcxProductShare);

    /**
     * 修改小程序订单产品分享
     * 
     * @param xcxProductShare 小程序订单产品分享
     * @return 结果
     */
    public int updateXcxProductShare(XcxProductShare xcxProductShare);

    /**
     * 删除小程序订单产品分享
     * 
     * @param id 小程序订单产品分享主键
     * @return 结果
     */
    public int deleteXcxProductShareById(Long id);

    /**
     * 批量删除小程序订单产品分享
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXcxProductShareByIds(Long[] ids);
}
