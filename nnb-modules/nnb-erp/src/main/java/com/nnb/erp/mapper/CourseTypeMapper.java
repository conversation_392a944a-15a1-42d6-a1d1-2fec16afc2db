package com.nnb.erp.mapper;


import com.nnb.erp.domain.CourseTypeEntity;
import com.nnb.erp.domain.vo.course.CourseTypeForParentVO;
import com.nnb.erp.domain.vo.course.CourseTypeForTreeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程分类，mapper，接口。
 *
 * <AUTHOR>
 * @date 2022-06-21 13:53:34
 */
public interface CourseTypeMapper {

    /**
     * 获取分类树形结构。
     *
     * @return 返回树形结构。
     * <AUTHOR>
     * @since 2022-06-22 15:09:28
     */
    List<CourseTypeForTreeVO> getTypeForTree();

    /**
     * 获取指定分类所属的一级、二级分类信息。
     *
     * @param typeId 分类标识。
     * @return 返回分类信息。
     * <AUTHOR>
     * @since 2022-06-21 18:36:19
     */
    CourseTypeForParentVO getTypeInfoById(@Param("typeId") Integer typeId);

    /**
     * 新增分类。
     *
     * @param courseTypeEntity 待新增实体。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-06-21 18:55:45
     */
    Integer insertEntity(CourseTypeEntity courseTypeEntity);

    /**
     * 修改分类。
     *
     * @param courseTypeEntity 待修改实体。
     * @return 返回受影响行数。
     * <AUTHOR>
     * @since 2022-06-21 18:55:45
     */
    Integer updateEntity(CourseTypeEntity courseTypeEntity);

    /**
     * 获取指定分类的子级数量。
     *
     * @param typeId 分类标识。
     * @return 返回子级数量。
     * <AUTHOR>
     * @since 2022-06-22 09:28:35
     */
    Integer getSonCountByTypeId(@Param("typeId") Integer typeId);

    /**
     * 获取指定分类集合。
     *
     * @param ids 分类标识集合。
     * @return 返回分类集合。
     * <AUTHOR>
     * @since 2022-06-22 09:36:33
     */
    List<CourseTypeEntity> getTypeListByIds(@Param("ids") List<Integer> ids);

    /**
     * 批量删除分类。
     *
     * @param ids 待删除分类。
     * <AUTHOR>
     * @since 2022-06-22 09:49:52
     */
    void deleteType(@Param("ids") List<Integer> ids);

}
