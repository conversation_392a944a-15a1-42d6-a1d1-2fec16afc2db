package com.nnb.erp.domain.vo.course;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程信息，用于课程配置，分页查询，DTO。
 *
 * <AUTHOR>
 * @since 2022-06-21 14:20:23
 */
@Data
public class CourseInfoPageForConfigDTO {

    /**
     * 课程名称。
     */
    @ApiModelProperty("课程名称。")
    private String courseName;

    /**
     * 用户名称。
     */
    @ApiModelProperty("用户名称。")
    private String userName;

    /**
     * 是否有效。
     */
    @ApiModelProperty("是否有效。")
    private Integer status;

    /**
     * 课程分类。
     */
    @ApiModelProperty("课程分类。")
    private List<String> courseTypes;

    @ApiModelProperty("用户名称。")
    private String roleName;

}
