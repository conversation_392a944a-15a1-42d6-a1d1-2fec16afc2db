package com.nnb.erp.domain.generalapproval;

import com.nnb.erp.domain.ErpExamineApprove;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdvancePaymentVo  extends ErpExamineApprove {

    @ApiModelProperty("创建人名称")
    private String createdUserName;
    @ApiModelProperty("创建人部门")
    private String createdUserDeptName;
    @ApiModelProperty("审批状态")
    private String approveStatusName;
    @ApiModelProperty("审批类型名称")
    private String approveTypeName;
    @ApiModelProperty("渠道")
    private String agentNumber;
    @ApiModelProperty("付款金额")
    private BigDecimal costFee;
    @ApiModelProperty("银行卡名称")
    private String bankName;
    @ApiModelProperty("收款人")
    private String payeeName;
    @ApiModelProperty("银行卡号")
    private String bankNumber;
    @ApiModelProperty("预付单号")
    private String advanceNumber;
    @ApiModelProperty("提交备注")
    private String remarkCost;
    @ApiModelProperty("附件")
    private String attachment;
//
//
//    @ApiModelProperty("审批类型名称")
//    private String approveTypeName;
//
//
//    @ApiModelProperty("审批类型名称")
//    private String approveTypeName;
//    /** 付款金额 */
//    @ApiModelProperty("付款金额")
//    private BigDecimal paymentAmount;
//    /** 付款日期 */
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @ApiModelProperty("付款日期")
//    private Date paymentDate;
//
//    @ApiModelProperty("结算单位")
//    private String agentNumber;
//
//    /** 银行卡 卡号 */
//    @ApiModelProperty("收款账号")
//    private String bankNumber;
//
//    /** 银行卡名称 */
//
//    @ApiModelProperty("备注")
//    private String remark;
//
//    @ApiModelProperty("收款人")
//    private String payee;
//
//    @ApiModelProperty("申请人")
//    private String userName;
//
//    @ApiModelProperty("申请部门")
//    private String deptName;
//
//    @ApiModelProperty("1 已核销 2未核销")
//    private Integer hxStatus;
//
//    @ApiModelProperty("服务单ID")
//    private String registerIds;

}
