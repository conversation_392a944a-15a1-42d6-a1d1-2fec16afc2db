package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpEnterpriseUser;
import com.nnb.erp.service.IErpEnterpriseUserService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 企业人员信息Controller
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
@RestController
@RequestMapping("/ErpEnterpriseUser")
@Api(tags = "ErpEnterpriseUserController", description = "企业人员信息")
public class ErpEnterpriseUserController extends BaseController
{
    @Autowired
    private IErpEnterpriseUserService erpEnterpriseUserService;

    /**
     * 查询企业人员信息列表
     */
    @ApiOperation(value = "查询企业人员信息列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpEnterpriseUser.class)})
    @PreAuthorize(hasPermi = "erp:ErpBizUser:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpEnterpriseUser erpEnterpriseUser)
    {
        startPage();
        List<ErpEnterpriseUser> list = erpEnterpriseUserService.selectErpBizUserList(erpEnterpriseUser);
        return getDataTable(list);
    }

    /**
     * 导出企业人员信息列表
     */
    @ApiOperation(value = "导出企业人员信息列表")
    @PreAuthorize(hasPermi = "erp:ErpBizUser:export")
    //@Log(title = "企业人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpEnterpriseUser erpEnterpriseUser) throws IOException
    {
        List<ErpEnterpriseUser> list = erpEnterpriseUserService.selectErpBizUserList(erpEnterpriseUser);
        ExcelUtil<ErpEnterpriseUser> util = new ExcelUtil<ErpEnterpriseUser>(ErpEnterpriseUser.class);
        util.exportExcel(response, list, "企业人员信息数据");
    }

    /**
     * 获取企业人员信息详细信息
     */
    @ApiOperation(value = "获取企业人员信息详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpEnterpriseUser.class)})
    @PreAuthorize(hasPermi = "erp:ErpBizUser:query")
    @GetMapping(value = "/{numId}")
    public AjaxResult getInfo(@ApiParam(name="numId",value="企业人员信息id") @PathVariable("numId") Long numId)
    {
        return AjaxResult.success(erpEnterpriseUserService.selectErpBizUserByNumId(numId));
    }

    /**
     * 新增企业人员信息
     */
    @ApiOperation(value = "新增企业人员信息")
    @PreAuthorize(hasPermi = "erp:ErpBizUser:add")
    //@Log(title = "企业人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpEnterpriseUser erpEnterpriseUser)
    {
        return toAjax(erpEnterpriseUserService.insertErpBizUser(erpEnterpriseUser));
    }

    /**
     * 修改企业人员信息
     */
    @ApiOperation(value = "修改企业人员信息")
    @PreAuthorize(hasPermi = "erp:ErpBizUser:edit")
    //@Log(title = "企业人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpEnterpriseUser erpEnterpriseUser)
    {
        return toAjax(erpEnterpriseUserService.updateErpBizUser(erpEnterpriseUser));
    }

    /**
     * 删除企业人员信息
     */
    @ApiOperation(value = "删除企业人员信息")
    @PreAuthorize(hasPermi = "erp:ErpBizUser:remove")
    //@Log(title = "企业人员信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{numIds}")
    public AjaxResult remove(@PathVariable Long[] numIds)
    {
        return toAjax(erpEnterpriseUserService.deleteErpBizUserByNumIds(numIds));
    }
}
