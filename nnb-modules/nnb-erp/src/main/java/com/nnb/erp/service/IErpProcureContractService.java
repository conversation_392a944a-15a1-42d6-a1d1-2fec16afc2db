package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpProcureContract;
import com.nnb.erp.domain.dto.ErpProcureContractDto;
import com.nnb.erp.domain.vo.ErpProcureContractVo;

/**
 * 采购合同Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface IErpProcureContractService 
{
    /**
     * 查询采购合同
     * 
     * @param id 采购合同主键
     * @return 采购合同
     */
    public ErpProcureContractVo selectErpProcureContractById(Long id);
    public ErpProcureContractVo getByLicenseNumber(String licenseNumber);
    public ErpProcureContract selectErpProcureContractByContractNumber(String contractNumber);

    /**
     * 查询采购合同列表
     * 
     * @param erpProcureContract 采购合同
     * @return 采购合同集合
     */
    public List<ErpProcureContractVo> selectErpProcureContractList(ErpProcureContractDto dto);


    public List<ErpProcureContractVo> selectListWithLicenseNull();


    /**
     * 新增采购合同
     * 
     * @param erpProcureContract 采购合同
     * @return 结果
     */
    public String insertErpProcureContract(ErpProcureContract erpProcureContract);

    /**
     * 修改采购合同
     * 
     * @param erpProcureContract 采购合同
     * @return 结果
     */
    public int updateErpProcureContract(ErpProcureContract erpProcureContract);

    /**
     * 批量删除采购合同
     * 
     * @param ids 需要删除的采购合同主键集合
     * @return 结果
     */
    public int deleteErpProcureContractByIds(Long[] ids);

    /**
     * 删除采购合同信息
     * 
     * @param id 采购合同主键
     * @return 结果
     */
    public int deleteErpProcureContractById(Long id);

    public int operateProduceContract(ErpProcureContractDto dto);
}
