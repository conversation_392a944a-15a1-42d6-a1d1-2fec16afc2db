package com.nnb.erp.constant.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *  审核流程配置
 */

public enum ApprovalConfigEnum {

    /**
     * 审核 ---1提单审核，2：订单修改审核，3新增产品审核，4成本结算审核，5预付款审核，6尾款审核
     *      7退款审核，8作废审核，9合作申请审核
     */
    ORDER_SUBMIT(1L,1,"提单审核"),
    ORDER_REVISE(2L,1,"订单修改审核"),
    PRODUCT_ADD(3L,null,"新增产品审核"),
    COST_SETTLEMENT(4L,null,"成本结算审核"),
    ORDER_ADVANCE(5L,null,"预付款审核"),
    ORDER_PAYMENT(6L,2,"尾款审核"),
    ORDER_REFUND(7L,1,"退款审核"),
    ORDER_CANCEL(8L,1,"作废审核"),
    COOPERATION_APPLICATION(9L,null,"合作申请审核");

    /**
     * 审核配置id 1:提单审核，2：订单修改审核，3新增产品审核，4成本结算审核，5预付款审核，6尾款审核
     *      * 7退款审核，8作废审核，9合作申请审核
     */
    private final Long configId;

    /**
     * 业务类型，1：订单； 2：尾款审核
     */
    private final Integer type;
    /**
     * 业务类型，1：订单； 2：尾款审核
     */
    private final String name;

    ApprovalConfigEnum(Long configId, Integer type, String name)
    {
        this.configId = configId;
        this.type = type;
        this.name = name;
    }

    public Long getConfigId()
    {
        return configId;
    }
    public Integer getType()
    {
        return type;
    }
    public String getName()
    {
        return name;
    }

    public static String getNameByConfigId(Long configId) {
        List<ApprovalConfigEnum> resList = Arrays.stream(ApprovalConfigEnum.values()).filter(e -> e.getConfigId().equals(configId)).collect(Collectors.toList());
        if (resList.size() == 0) {
            return configId + "";
        } else {
            return resList.get(0).getName();
        }
    }

}
