package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.XcxActivityCouponLimitMapper;
import com.nnb.erp.domain.XcxActivityCouponLimit;
import com.nnb.erp.service.IXcxActivityCouponLimitService;

/**
 * 小程序活动/优惠券配置与优惠额度关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
public class XcxActivityCouponLimitServiceImpl implements IXcxActivityCouponLimitService
{
    @Autowired
    private XcxActivityCouponLimitMapper xcxActivityCouponLimitMapper;

    /**
     * 查询小程序活动/优惠券配置与优惠额度关联
     *
     * @param id 小程序活动/优惠券配置与优惠额度关联主键
     * @return 小程序活动/优惠券配置与优惠额度关联
     */
    @Override
    public XcxActivityCouponLimit selectXcxActivityCouponLimitById(Long id)
    {
        return xcxActivityCouponLimitMapper.selectXcxActivityCouponLimitById(id);
    }

    /**
     * 查询小程序活动/优惠券配置与优惠额度关联列表
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 小程序活动/优惠券配置与优惠额度关联
     */
    @Override
    public List<XcxActivityCouponLimit> selectXcxActivityCouponLimitList(XcxActivityCouponLimit xcxActivityCouponLimit)
    {
        return xcxActivityCouponLimitMapper.selectXcxActivityCouponLimitList(xcxActivityCouponLimit);
    }

    /**
     * 新增小程序活动/优惠券配置与优惠额度关联
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 结果
     */
    @Override
    public int insertXcxActivityCouponLimit(XcxActivityCouponLimit xcxActivityCouponLimit)
    {
        return xcxActivityCouponLimitMapper.insertXcxActivityCouponLimit(xcxActivityCouponLimit);
    }

    /**
     * 修改小程序活动/优惠券配置与优惠额度关联
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 结果
     */
    @Override
    public int updateXcxActivityCouponLimit(XcxActivityCouponLimit xcxActivityCouponLimit)
    {
        return xcxActivityCouponLimitMapper.updateXcxActivityCouponLimit(xcxActivityCouponLimit);
    }

    /**
     * 批量删除小程序活动/优惠券配置与优惠额度关联
     *
     * @param ids 需要删除的小程序活动/优惠券配置与优惠额度关联主键
     * @return 结果
     */
    @Override
    public int deleteXcxActivityCouponLimitByIds(Long[] ids)
    {
        return xcxActivityCouponLimitMapper.deleteXcxActivityCouponLimitByIds(ids);
    }

    /**
     * 删除小程序活动/优惠券配置与优惠额度关联信息
     *
     * @param id 小程序活动/优惠券配置与优惠额度关联主键
     * @return 结果
     */
    @Override
    public int deleteXcxActivityCouponLimitById(Long id)
    {
        return xcxActivityCouponLimitMapper.deleteXcxActivityCouponLimitById(id);
    }
}
