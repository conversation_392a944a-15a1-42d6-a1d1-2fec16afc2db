package com.nnb.erp.service.impl;

import java.util.List;

import com.nnb.erp.domain.vo.service.SConfigServiceTypeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.SConfigServiceTypeMapper;
import com.nnb.erp.domain.SConfigServiceType;
import com.nnb.erp.service.ISConfigServiceTypeService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
@Service
public class SConfigServiceTypeServiceImpl implements ISConfigServiceTypeService 
{
    @Autowired
    private SConfigServiceTypeMapper sConfigServiceTypeMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SConfigServiceType selectSConfigServiceTypeById(Long id)
    {
        return sConfigServiceTypeMapper.selectSConfigServiceTypeById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServiceType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SConfigServiceTypeVo> selectSConfigServiceTypeList(SConfigServiceType sConfigServiceType)
    {
        return sConfigServiceTypeMapper.selectSConfigServiceTypeList(sConfigServiceType);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServiceType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSConfigServiceType(SConfigServiceType sConfigServiceType)
    {
        return sConfigServiceTypeMapper.insertSConfigServiceType(sConfigServiceType);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServiceType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSConfigServiceType(SConfigServiceType sConfigServiceType)
    {
        return sConfigServiceTypeMapper.updateSConfigServiceType(sConfigServiceType);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSConfigServiceTypeByIds(Long[] ids)
    {
        return sConfigServiceTypeMapper.deleteSConfigServiceTypeByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSConfigServiceTypeById(Long id)
    {
        return sConfigServiceTypeMapper.deleteSConfigServiceTypeById(id);
    }

    @Override
    public List<SConfigServiceType> selectSConfigServiceTypeByIds(String ids) {
        return sConfigServiceTypeMapper.selectSConfigServiceTypeByIds(ids);
    }
}
