package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import static com.nnb.common.core.text.Convert.digitUppercase;

/**
 * 成本结算审核，VO。
 *
 * <AUTHOR>
 * @since 2022/4/11 15:15
 */
@Data
public class ErpBizSettlementForApprovalVO {

    /**
     * 审核标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("审核标识。")
    private Long businessApprovalId;

    /**
     * 订单编号。
     */
    @ApiModelProperty("订单编号。")
    private String orderNumber;

    /**
     * 服务单编号。
     */
    @ApiModelProperty("服务单编号。")
    private String bizNumber;

    /**
     * 供应商。
     */
    @ApiModelProperty("供应商。")
    private String supplierName;

    /**
     * 申请类型。
     */
    @ApiModelProperty("申请类型。")
    private String applicationTypeStr;

    /**
     * 申请人。
     */
    @ApiModelProperty("申请人。")
    private String nickName;

    /**
     * 申请部门。
     */
    @ApiModelProperty("申请部门。")
    private String deptName;

    /**
     * 审核状态。
     */
    @ApiModelProperty("审核状态。")
    private String approvalStatusStr;

    /**
     * 审核时间。
     */
    @ApiModelProperty("审核时间。")
    private String approvalTime;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    private String createdTime;

    /**
     * 预付核销。
     */
    @ApiModelProperty("预付核销。")
    private String isAdvanceChargeStr;

    /**
     * 预付款金额。
     */
    @ApiModelProperty("预付款金额。")
    private BigDecimal supplierAmount;

    /**
     * 预付款金额，中文大写。
     */
    @ApiModelProperty("预付款金额，中文大写。")
    private String supplierAmountCn;

    /**
     * 核销单号。
     */
    @ApiModelProperty("核销单号。")
    private String advanceChargeNo;

    /**
     * 核销状态。
     */
    @ApiModelProperty("核销状态。")
    private String writeOffStatusStr;

    /**
     * 收款人名称。
     */
    @ApiModelProperty("收款人名称。")
    private String payeeUserName;

    /**
     * 开户行。
     */
    @ApiModelProperty("开户行。")
    private String bankName;

    /**
     * 银行账号。
     */
    @ApiModelProperty("银行账号。")
    private String bankNo;

    /**
     * 结算备注。
     */
    @ApiModelProperty("结算备注。")
    private String remarks;

    public void setSupplierAmount(BigDecimal supplierAmount) {
        this.supplierAmount = supplierAmount;
        this.supplierAmountCn = digitUppercase(supplierAmount.doubleValue());
    }

}
