package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpServiceOrders;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品订单状态
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpServiceOrdersProductType extends ErpServiceOrders {


    /** 产品类型id */
    @Excel(name = "产品类型id")
    @ApiModelProperty("产品类型id")
    private Long numTypeId;

    /** 产品类型名称 */
    @Excel(name = "产品类型名称")
    @ApiModelProperty("产品类型名称")
    private String vcTypeName;

    @Excel(name = "产品类型名称")
    @ApiModelProperty("产品类型名称")
    private String serviceTypeId;

    @ApiModelProperty("是否赠品产品1是2否")
    private Integer giveProduct;

}
