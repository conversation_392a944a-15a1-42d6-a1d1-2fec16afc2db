package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 【请填写功能名称】对象 s_service_loss_reason
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@ApiModel(value="SServiceLossReason",description="【请填写功能名称】对象")
public class SServiceLossReason extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 服务单ID  s_service_main.id */
    @Excel(name = "服务单ID  s_service_main.id")
    @ApiModelProperty("服务单ID  s_service_main.id")
    private Long serviceId;

    /** 流失原因  account_loss_reason.id */
    @Excel(name = "流失原因  account_loss_reason.id")
    @ApiModelProperty("流失原因  account_loss_reason.id")
    private Long accountLsId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("流失时间")
    private Date lsDate;

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setServiceId(Long serviceId) 
    {
        this.serviceId = serviceId;
    }

    public Long getServiceId() 
    {
        return serviceId;
    }
    public void setAccountLsId(Long accountLsId)
    {
        this.accountLsId = accountLsId;
    }

    public Long getAccountLsId()
    {
        return accountLsId;
    }

    public Date getLsDate() {
        return lsDate;
    }

    public void setLsDate(Date lsDate) {
        this.lsDate = lsDate;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("serviceId", getServiceId())
            .append("accountLsId", getAccountLsId())
            .toString();
    }
}
