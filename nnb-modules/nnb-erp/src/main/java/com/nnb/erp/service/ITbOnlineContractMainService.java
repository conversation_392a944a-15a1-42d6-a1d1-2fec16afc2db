package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.TbOnlineContractMain;
import com.nnb.erp.domain.vo.TbOnlineContractMainVo;

/**
 * 电子合同主体Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-23
 */
public interface ITbOnlineContractMainService 
{
    /**
     * 查询电子合同主体
     * 
     * @param id 电子合同主体主键
     * @return 电子合同主体
     */
    public TbOnlineContractMain selectTbOnlineContractMainById(Long id);

    /**
     * 查询电子合同主体列表
     * 
     * @param tbOnlineContractMain 电子合同主体
     * @return 电子合同主体集合
     */
    public List<TbOnlineContractMain> selectTbOnlineContractMainList(TbOnlineContractMain tbOnlineContractMain);

    public List<TbOnlineContractMainVo> getListByUser(Long orderId);


    /**
     * 新增电子合同主体
     * 
     * @param tbOnlineContractMain 电子合同主体
     * @return 结果
     */
    public int insertTbOnlineContractMain(TbOnlineContractMain tbOnlineContractMain);

    /**
     * 修改电子合同主体
     * 
     * @param tbOnlineContractMain 电子合同主体
     * @return 结果
     */
    public int updateTbOnlineContractMain(TbOnlineContractMain tbOnlineContractMain);

    /**
     * 批量删除电子合同主体
     * 
     * @param ids 需要删除的电子合同主体主键集合
     * @return 结果
     */
    public int deleteTbOnlineContractMainByIds(Long[] ids);

    /**
     * 删除电子合同主体信息
     * 
     * @param id 电子合同主体主键
     * @return 结果
     */
    public int deleteTbOnlineContractMainById(Long id);
}
