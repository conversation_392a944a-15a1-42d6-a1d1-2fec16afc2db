package com.nnb.erp.domain.inventory;

import com.nnb.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Author: Chen-xy
 * @Description: 收发存汇总Excel
 * @Date: 2024-01-10
 * @Version: 1.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaSummaryResExcelVo {

    @Excel(name = "仓库")
    private String warehouseName;

    @Excel(name = "存货编码")
    private String inventoryCode;

    @Excel(name = "存货名称")
    private String inventoryName;

    @Excel(name = "规格型号")
    private String model;

    @Excel(name = "存货分类")
    private String iaCategoryName;

    @Excel(name = "计量单位")
    private String iaUnitName;

    @Excel(name = "期初余额数量")
    private BigDecimal beginQty;

    @Excel(name = "期初余额单价")
    private BigDecimal beginUnitPrice;

    @Excel(name = "期初余额金额")
    private BigDecimal beginAmount;

    @Excel(name = "本期入库数量")
    private BigDecimal debitQty;

    @Excel(name = "本期入库单价")
    private BigDecimal debitUnitPrice;

    @Excel(name = "本期入库金额")
    private BigDecimal debitAmount;

    @Excel(name = "本期出库数量")
    private BigDecimal creditQty;

    @Excel(name = "本期出库单价")
    private BigDecimal creditUnitPrice;

    @Excel(name = "本期出库金额")
    private BigDecimal creditAmount;

    @Excel(name = "期末结存数量")
    private BigDecimal finalQty;

    @Excel(name = "期末结存单价")
    private BigDecimal finalUnitPrice;

    @Excel(name = "期末结存单价")
    private BigDecimal finalAmount;
}
