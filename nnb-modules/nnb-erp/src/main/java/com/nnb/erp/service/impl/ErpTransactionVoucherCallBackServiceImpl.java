package com.nnb.erp.service.impl;

import java.util.Date;
import java.util.List;

import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.ErpTransactionVoucher;
import com.nnb.erp.mapper.ErpTransactionVoucherMapper;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpTransactionVoucherCallBackMapper;
import com.nnb.erp.domain.ErpTransactionVoucherCallBack;
import com.nnb.erp.service.IErpTransactionVoucherCallBackService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 大额预存回访记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class ErpTransactionVoucherCallBackServiceImpl implements IErpTransactionVoucherCallBackService 
{
    @Autowired
    private ErpTransactionVoucherCallBackMapper erpTransactionVoucherCallBackMapper;
    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询大额预存回访记录
     * 
     * @param id 大额预存回访记录主键
     * @return 大额预存回访记录
     */
    @Override
    public ErpTransactionVoucherCallBack selectErpTransactionVoucherCallBackById(Long id)
    {
        return erpTransactionVoucherCallBackMapper.selectErpTransactionVoucherCallBackById(id);
    }

    /**
     * 查询大额预存回访记录列表
     * 
     * @param erpTransactionVoucherCallBack 大额预存回访记录
     * @return 大额预存回访记录
     */
    @Override
    public List<ErpTransactionVoucherCallBack> selectErpTransactionVoucherCallBackList(ErpTransactionVoucherCallBack erpTransactionVoucherCallBack)
    {
        return erpTransactionVoucherCallBackMapper.selectErpTransactionVoucherCallBackList(erpTransactionVoucherCallBack);
    }

    /**
     * 新增大额预存回访记录
     * 
     * @param erpTransactionVoucherCallBack 大额预存回访记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertErpTransactionVoucherCallBack(ErpTransactionVoucherCallBack erpTransactionVoucherCallBack)
    {
        erpTransactionVoucherCallBack.setCallBackDate(new Date());
        if (erpTransactionVoucherCallBack.getType().intValue() == 2) {
            LoginUser loginUser = tokenService.getLoginUser();
            SysUser sysUser = loginUser.getSysUser();

            erpTransactionVoucherCallBack.setCreateUser(sysUser.getUserId());
            erpTransactionVoucherCallBack.setCallBackStatus(1);
            erpTransactionVoucherCallBackMapper.updateTransactionVoucher(erpTransactionVoucherCallBack);
        } else {
            ErpTransactionVoucher voucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(Integer.parseInt(erpTransactionVoucherCallBack.getVoucher().toString()));
            erpTransactionVoucherCallBack.setPayRecordId(voucher.getPayRecordId());
        }

        return erpTransactionVoucherCallBackMapper.insertErpTransactionVoucherCallBack(erpTransactionVoucherCallBack);
    }

    /**
     * 修改大额预存回访记录
     * 
     * @param erpTransactionVoucherCallBack 大额预存回访记录
     * @return 结果
     */
    @Override
    public int updateErpTransactionVoucherCallBack(ErpTransactionVoucherCallBack erpTransactionVoucherCallBack)
    {
        return erpTransactionVoucherCallBackMapper.updateErpTransactionVoucherCallBack(erpTransactionVoucherCallBack);
    }

    /**
     * 批量删除大额预存回访记录
     * 
     * @param ids 需要删除的大额预存回访记录主键
     * @return 结果
     */
    @Override
    public int deleteErpTransactionVoucherCallBackByIds(Long[] ids)
    {
        return erpTransactionVoucherCallBackMapper.deleteErpTransactionVoucherCallBackByIds(ids);
    }

    /**
     * 删除大额预存回访记录信息
     * 
     * @param id 大额预存回访记录主键
     * @return 结果
     */
    @Override
    public int deleteErpTransactionVoucherCallBackById(Long id)
    {
        return erpTransactionVoucherCallBackMapper.deleteErpTransactionVoucherCallBackById(id);
    }
}
