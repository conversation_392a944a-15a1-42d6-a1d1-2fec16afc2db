package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpSetRecordsMapper;
import com.nnb.erp.domain.ErpSetRecords;
import com.nnb.erp.service.IErpSetRecordsService;

/**
 * 手机卡套餐超时信息推送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-11-11
 */
@Service
public class ErpSetRecordsServiceImpl implements IErpSetRecordsService 
{
    @Autowired
    private ErpSetRecordsMapper erpSetRecordsMapper;

    /**
     * 查询手机卡套餐超时信息推送记录
     * 
     * @param configId 手机卡套餐超时信息推送记录主键
     * @return 手机卡套餐超时信息推送记录
     */
    @Override
    public ErpSetRecords selectErpSetRecordsByConfigId(Long configId)
    {
        return erpSetRecordsMapper.selectErpSetRecordsByConfigId(configId);
    }

    /**
     * 查询手机卡套餐超时信息推送记录列表
     * 
     * @param erpSetRecords 手机卡套餐超时信息推送记录
     * @return 手机卡套餐超时信息推送记录
     */
    @Override
    public List<ErpSetRecords> selectErpSetRecordsList(ErpSetRecords erpSetRecords)
    {
        return erpSetRecordsMapper.selectErpSetRecordsList(erpSetRecords);
    }

    /**
     * 新增手机卡套餐超时信息推送记录
     * 
     * @param erpSetRecords 手机卡套餐超时信息推送记录
     * @return 结果
     */
    @Override
    public int insertErpSetRecords(ErpSetRecords erpSetRecords)
    {
        return erpSetRecordsMapper.insertErpSetRecords(erpSetRecords);
    }

    /**
     * 修改手机卡套餐超时信息推送记录
     * 
     * @param erpSetRecords 手机卡套餐超时信息推送记录
     * @return 结果
     */
    @Override
    public int updateErpSetRecords(ErpSetRecords erpSetRecords)
    {
        return erpSetRecordsMapper.updateErpSetRecords(erpSetRecords);
    }

    /**
     * 批量删除手机卡套餐超时信息推送记录
     * 
     * @param configIds 需要删除的手机卡套餐超时信息推送记录主键
     * @return 结果
     */
    @Override
    public int deleteErpSetRecordsByConfigIds(Long[] configIds)
    {
        return erpSetRecordsMapper.deleteErpSetRecordsByConfigIds(configIds);
    }

    /**
     * 删除手机卡套餐超时信息推送记录信息
     * 
     * @param configId 手机卡套餐超时信息推送记录主键
     * @return 结果
     */
    @Override
    public int deleteErpSetRecordsByConfigId(Long configId)
    {
        return erpSetRecordsMapper.deleteErpSetRecordsByConfigId(configId);
    }
}
