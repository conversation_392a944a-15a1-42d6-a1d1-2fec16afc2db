package com.nnb.erp.domain.vo.task;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务，用于新增，DTO。
 *
 * <AUTHOR>
 * @since 2022-07-06 10:02:35
 */
@Data
public class TaskInfoForSaveDTO implements Serializable {

    /**
     * 主键标识。
     */
    @ApiModelProperty("主键标识。")
    private Integer id;

    @ApiModelProperty("任务类型。1普通外勤2疑难外勤3客户拜访外勤")
    private Integer taskType;
    /**
     * 源任务标识。
     */
    @ApiModelProperty("源任务标识。")
    private Integer sourceId;

    /**
     * 任务编号。
     */
    @ApiModelProperty("任务编号。")
    private String number;

    /**
     * 企业标识。
     */
    @ApiModelProperty("企业标识。")
    private List<String> enterpriseId;

    /**
     * 任务一级分类。
     */
    @ApiModelProperty("任务一级分类。")
    private Integer firstType;

    /**
     * 任务二级分类。
     */
    @ApiModelProperty("任务二级分类。")
    private Integer secondType;

    /**
     * 是否要求时间。
     */
    @ApiModelProperty("是否要求时间。")
    private Integer isTime;

    private List<String> imgSrc;
    /**
     * 任务执行时间。
     */
    @ApiModelProperty("任务执行时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;

    @ApiModelProperty("任务执行开始时间。")
    private Date executeTimeBigen;

    @ApiModelProperty("任务执行结束时间。")
    private Date executeTimeEnd;
    /**
     * 客户到场：0-不到场；1-负责人；2-法人；3-负责人+法人。
     */
    @ApiModelProperty("客户到场：0-不到场；1-负责人；2-法人；3-负责人+法人。")
    private Integer isPresent;


    /**
     * 实际执行人。
     */
    @ApiModelProperty("实际执行人。")
    private Integer realExecuteUserId;
    /**
     * 负责人名称。
     */
    @ApiModelProperty("负责人名称。")
    private String headName;

    /**
     * 负责人电话。
     */
    @ApiModelProperty("负责人电话。")
    private String headPhone;

    /**
     * 法人名称。
     */
    @ApiModelProperty("法人名称。")
    private String legalName;

    /**
     * 法人电话。
     */
    @ApiModelProperty("法人电话。")
    private String legalPhone;

    /**
     * 是否收款：1-是；2-否。
     */
    @ApiModelProperty("是否收款：1-是；2-否。")
    private Integer isMoney;

    @ApiModelProperty("不收费说明。")
    private String noMoneyRemark;

    /**
     * 任务执行人。
     */
    @ApiModelProperty("任务执行人。")
    private Integer executeUserId;

    /**
     * 是否加急。
     */
    @ApiModelProperty("是否加急。")
    private Integer isUrgency;

    /**
     * 资料标识，逗号分隔。
     */
    @ApiModelProperty("资料标识，逗号分隔。")
    private List<String> materialIds;

    /**
     * 所属城市。
     */
    @ApiModelProperty("所属城市。")
    private Integer cityId;

    /**
     * 所属区域。
     */
    @ApiModelProperty("所属区域。")
    private Integer areaId;

    /**
     * 任务地点。
     */
    @ApiModelProperty("任务地点。")
    private Integer addressId;

    /**
     * 详细地点。
     */
    @ApiModelProperty("详细地点。")
    private String addressDetail;

    /**
     * 是否执照任务。
     */
    @ApiModelProperty("是否执照任务。")
    private Integer isLicense;

    /**
     * 任务内容。
     */
    @ApiModelProperty("任务内容。")
    private String content;
    @ApiModelProperty("重新派单任务内容。")
    private String contentR;
    /**
     * 备注。
     */
    @ApiModelProperty("重新派单备注。")
    private String remarkR;

    @ApiModelProperty("备注。")
    private String remark;
    /**
     * 变更时间。
     */
    @ApiModelProperty("变更时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;

    /**
     * 变更地点。
     */
    @ApiModelProperty("变更地点。")
    private String changeAddress;

    /**
     * 任务附件。
     */
    @ApiModelProperty("任务附件。")
    private String attachment;
    private String remarks;

    @ApiModelProperty("订单编号。")
    private String orderNumber;

    @ApiModelProperty("拜访原因：1:客情维护、2:意向挖掘、3:需求逼单、4:客户挽留、5:其他")
    private Integer reasonForVisit;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("拜访方式：1:客户会、2:上门、3:外出")
    private Integer visitingMethods;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("收费金额")
    private BigDecimal collectedAmount;

    @ApiModelProperty("拜访类型1日常拜访2合规拜访")
    private Integer visitWay;
}
