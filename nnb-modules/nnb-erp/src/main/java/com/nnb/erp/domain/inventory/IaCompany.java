package com.nnb.erp.domain.inventory;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 公司存货参数对象 ia_company
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@ApiModel(value="IaCompany",description="公司存货参数对象")
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IaCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 公司名称名称 */
    @Excel(name = "公司名称名称")
    @ApiModelProperty("公司名称名称")
    private String name;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty("开始时间")
    private Integer startPeriod;
    private String startPeriodDate;

    /** 计价方式 1：全月加权平均法 */
    @Excel(name = "计价方式 1：全月加权平均法")
    @ApiModelProperty("计价方式 1：全月加权平均法")
    private Integer pricingMethod;

    /** 当前所在时间 */
    @Excel(name = "当前所在时间")
    @ApiModelProperty("当前所在时间")
    private Integer currentPeriod;
    private String currentPeriodDate;

    /** 非数据库字段*/
    private List<Long> ids;
}
