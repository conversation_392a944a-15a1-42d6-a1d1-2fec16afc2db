package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentTermInfoVo {

    @ApiModelProperty("$column.columnComment")
    private Long id;

    /**
     * 收款方式标识。
     */
    @ApiModelProperty("收款方式标识。")
    private Long termId;

    /** 收款方式：1现金；2刷卡；3扫码；4转账；5微信/支付宝在线支付 */
    @Excel(name = "收款方式：1现金；2刷卡；3扫码；4转账；5微信/支付宝在线支付")
    @ApiModelProperty("收款方式：1现金；2刷卡；3扫码；4转账；5微信/支付宝在线支付")
    private Long numPaymentType;

    /** 收款凭证，上传文件后的url */
    @Excel(name = "收款凭证，上传文件后的url")
    @ApiModelProperty("收款凭证，上传文件后的url")
    private String vcPaymentUrl;

    /** 收款金额 */
    @Excel(name = "收款金额")
    @ApiModelProperty("收款金额")
    private BigDecimal numMoney;

    @ApiModelProperty("订单ID")
    private Long numOrderId;
}
