package com.nnb.erp.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.dto.ErpLicenseDemandDto;
import com.nnb.erp.domain.vo.ErpLicenseDemandVo;
import com.nnb.erp.mapper.ComDictRegionMapper;
import com.nnb.erp.mapper.ErpLicenseMapper;
import com.nnb.erp.service.DingDingService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpLicenseDemandMapper;
import com.nnb.erp.domain.ErpLicenseDemand;
import com.nnb.erp.service.IErpLicenseDemandService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 执照需求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class ErpLicenseDemandServiceImpl implements IErpLicenseDemandService 
{
    @Autowired
    private ErpLicenseDemandMapper erpLicenseDemandMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private DingDingService dingDingService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private ComDictRegionMapper comDictRegionMapper;
    /**
     * 查询执照需求
     * 
     * @param id 执照需求主键
     * @return 执照需求
     */
    @Override
    public ErpLicenseDemand selectErpLicenseDemandById(Long id)
    {
        return erpLicenseDemandMapper.selectErpLicenseDemandById(id);
    }

    /**
     * 查询执照需求列表
     * 
     * @param erpLicenseDemand 执照需求
     * @return 执照需求
     */
    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpLicenseDemandVo> selectErpLicenseDemandList(ErpLicenseDemandDto dto)
    {
        List<ErpLicenseDemandVo> list = erpLicenseDemandMapper.selectLicenseDemandList(dto);
        for (int i = 0; i < list.size(); i++) {
            ErpLicenseDemandVo vo = list.get(i);
            vo.setAreaName(comDictRegionMapper.selectThreeTitleById(vo.getArea()));
        }

        return list;
    }

    /**
     * 新增执照需求
     * 
     * @param erpLicenseDemand 执照需求
     * @return 结果
     */
    @Override
    public int insertErpLicenseDemand(ErpLicenseDemand erpLicenseDemand)
    {

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
//        SysUser sysUser = new SysUser();
//        sysUser.setUserId(1L);

        erpLicenseDemand.setCreatedUser(sysUser.getUserId());
        erpLicenseDemand.setCreatedDate(new Date());
        return erpLicenseDemandMapper.insertErpLicenseDemand(erpLicenseDemand);
    }

    /**
     * 修改执照需求
     * 
     * @param erpLicenseDemand 执照需求
     * @return 结果
     */
    @Override
    public int updateErpLicenseDemand(ErpLicenseDemand erpLicenseDemand)
    {
        return erpLicenseDemandMapper.updateErpLicenseDemand(erpLicenseDemand);
    }

    /**
     * 批量删除执照需求
     * 
     * @param ids 需要删除的执照需求主键
     * @return 结果
     */
    @Override
    public int deleteErpLicenseDemandByIds(Long[] ids)
    {
        return erpLicenseDemandMapper.deleteErpLicenseDemandByIds(ids);
    }

    /**
     * 删除执照需求信息
     * 
     * @param id 执照需求主键
     * @return 结果
     */
    @Override
    public int deleteErpLicenseDemandById(Long id)
    {
        return erpLicenseDemandMapper.deleteErpLicenseDemandById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int matchLicense(ErpLicenseDemandDto dto) {

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();

        ErpLicenseDemand erpLicenseDemand = new ErpLicenseDemand();
        erpLicenseDemand.setId(dto.getId());
        erpLicenseDemand.setLicenseId(dto.getLicenseId());
        erpLicenseDemand.setMatchDate(new Date());
        erpLicenseDemand.setMatchStatus(2);

        erpLicenseDemandMapper.matchLicense(dto.getLicenseId(), 1L);

        R<SysUser> info = remoteUserService.getUserInfoById(sysUser.getUserId(), SecurityConstants.INNER);
        if (info.getCode() == 200) {
            String email = info.getData().getEmail();
            //发送钉钉消息
            String dingContent = "### 执照需求 \n * " + "您好： \n "
                    +" * 您的执照需求已匹配，执照编号：" + dto.getLicenseNumber() + "请及时提单！";
            DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "执照需求", dingContent);
            dingDingService.sendDingMessage(dingSendDTO);
        }

        return erpLicenseDemandMapper.updateErpLicenseDemand(erpLicenseDemand);
    }

    @Override
    public int shelveLicense(ErpLicenseDemandDto dto) {
        ErpLicenseDemand erpLicenseDemand = new ErpLicenseDemand();
        erpLicenseDemand.setId(dto.getId());
        erpLicenseDemand.setMatchStatus(3);
        return erpLicenseDemandMapper.updateErpLicenseDemand(erpLicenseDemand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelLicense(ErpLicenseDemandDto dto) {

        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();

        ErpLicenseDemand demand = erpLicenseDemandMapper.selectErpLicenseDemandById(dto.getId());
        erpLicenseDemandMapper.matchLicense(demand.getLicenseId(), 2L);

        R<SysUser> info = remoteUserService.getUserInfoById(sysUser.getUserId(), SecurityConstants.INNER);
        if (info.getCode() == 200) {
            String email = info.getData().getEmail();
            //发送钉钉消息
            String dingContent = "### 执照需求 \n * " + "您好： \n "
                    +" * 您的执照需求，序号为：" + dto.getId() + "的执照需求已取消匹配！";
            DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "执照需求", dingContent);
            dingDingService.sendDingMessage(dingSendDTO);
        }

        return erpLicenseDemandMapper.cancelMatch(dto.getId());
    }
}
