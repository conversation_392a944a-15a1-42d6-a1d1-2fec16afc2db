package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.erp.domain.ErpBizNodeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 服务单列表筛选dto
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ErpBizMainInfoListDto",description = "服务单列表筛选dto")
public class ErpBizMainInfoListDto extends ErpBizNodeStatus {

    private static final long serialVersionUID = -6402283208488838470L;


    /** 注册明细订单 */
    @ApiModelProperty("注册明细订单")
    private Long registrationDetails;


    /** 订单编号/联系人/电话/公司名称 */
    @ApiModelProperty("订单编号/联系人/电话/公司名称")
    private String message;

    /** 业务对接人 */
    @ApiModelProperty("业务对接人")
    private Long businessUserId;

    /** 城市 */
    @ApiModelProperty("城市")
    private Long cityId;

    /**
     * 服务单状态
     */
    @ApiModelProperty("服务单状态")
    private Long numApproveStatus;

    /** 有无尾款 0无，1有 */
    @ApiModelProperty("有无尾款 0无，1有")
    private Long isLastPrice;

    /** 到达业支时间开始 */
    @ApiModelProperty("到达业支时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date businessDateBegin;

    /** 到达业支时间结束 */
    @ApiModelProperty("到达业支时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date businessDateEnd;

    /** 签约时间开始 */
    @ApiModelProperty("签约时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date datSigningDateBegin;

    /** 签约时间结束 */
    @ApiModelProperty("签约时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date datSigningDateEnd;

}
