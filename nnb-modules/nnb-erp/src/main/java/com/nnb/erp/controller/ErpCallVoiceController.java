package com.nnb.erp.controller;

import java.util.Date;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.nnb.erp.domain.ErpCallVoice;
import com.nnb.erp.service.IErpCallVoiceService;

/**
 * 语音数据Controller
 * 
 * <AUTHOR>
 * @date 2021-11-01
 */
@RestController
@RequestMapping("/voice")
public class ErpCallVoiceController extends BaseController
{

    private static Logger logger = LoggerFactory.getLogger(ErpCallVoiceController.class);

    @Autowired
    private IErpCallVoiceService erpCallVoiceService;

    /**
     * 查询语音数据列表
     */
    @PreAuthorize(hasPermi = "erp:voice:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpCallVoice erpCallVoice)
    {
        startPage();
        List<ErpCallVoice> list = erpCallVoiceService.selectErpCallVoiceList(erpCallVoice);
        return getDataTable(list);
    }

    /**
     * 导出语音数据列表
     */
    @PreAuthorize(hasPermi = "erp:voice:export")
    //@Log(title = "语音数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpCallVoice erpCallVoice) throws IOException
    {
        List<ErpCallVoice> list = erpCallVoiceService.selectErpCallVoiceList(erpCallVoice);
        ExcelUtil<ErpCallVoice> util = new ExcelUtil<ErpCallVoice>(ErpCallVoice.class);
        util.exportExcel(response, list, "语音数据数据");
    }

    /**
     * 获取语音数据详细信息
     */
    @PreAuthorize(hasPermi = "erp:voice:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(erpCallVoiceService.selectErpCallVoiceById(id));
    }

    /**
     * 新增语音数据
     */
    @PreAuthorize(hasPermi = "erp:voice:add")
    //@Log(title = "语音数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpCallVoice erpCallVoice)
    {
        erpCallVoice.setDatCreateTime(new Date());
        erpCallVoice.setDatLastUpdTime(new Date());
        erpCallVoice.setOperation(1);
        erpCallVoice.setNumCreateUserid(SecurityUtils.getUserId());
        erpCallVoice.setNumLastUpdUserid(SecurityUtils.getUserId());
        //向上取整  将秒转化为分钟
        double dura = Double.parseDouble(erpCallVoice.getCallDuration())/60;
        erpCallVoice.setCallDuration(Math.ceil(dura)+"");
        return toAjax(erpCallVoiceService.insertErpCallVoice(erpCallVoice));
    }

    /**
     * 新增语音数据（提供给原crm系统调用 保存语音信息）
     */
    @PostMapping("/add_crm")
    public AjaxResult add_crm(@RequestBody ErpCallVoice erpCallVoice) {
        erpCallVoice.setDatCreateTime(new Date());
        erpCallVoice.setDatLastUpdTime(new Date());
        erpCallVoice.setOperation(1);
        erpCallVoice.setNumCreateUserid(SecurityUtils.getUserId());
        erpCallVoice.setNumLastUpdUserid(SecurityUtils.getUserId());

        logger.info("crm-php平台传入参数：{}",erpCallVoice.toString());
        //向上取整  将秒转化为分钟
        double dura = Double.parseDouble(erpCallVoice.getCallDuration())/60;
        erpCallVoice.setCallDuration(Math.ceil(dura)+"");
        if(erpCallVoice.getSimPlace() == null || "".equals(erpCallVoice.getSimPlace())){
            erpCallVoice.setSimPlace("1");
        }
        return toAjax(erpCallVoiceService.insertErpCallVoice(erpCallVoice));
    }

    /**
     * 修改语音数据
     */
    @PreAuthorize(hasPermi = "erp:voice:edit")
    //@Log(title = "语音数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpCallVoice erpCallVoice)
    {
        return toAjax(erpCallVoiceService.updateErpCallVoice(erpCallVoice));
    }

    /**
     * 删除语音数据
     */
    @PreAuthorize(hasPermi = "erp:voice:remove")
    //@Log(title = "语音数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpCallVoiceService.deleteErpCallVoiceByIds(ids));
    }
}
