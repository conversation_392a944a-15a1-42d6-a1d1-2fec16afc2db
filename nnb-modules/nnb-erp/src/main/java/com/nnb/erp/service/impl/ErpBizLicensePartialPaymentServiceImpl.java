package com.nnb.erp.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.ErpBizLicensePartialPaymentMapper;
import com.nnb.erp.domain.ErpBizLicensePartialPayment;
import com.nnb.erp.service.IErpBizLicensePartialPaymentService;

/**
 * 执照分批付款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-04-27
 */
@Service
public class ErpBizLicensePartialPaymentServiceImpl implements IErpBizLicensePartialPaymentService 
{
    @Autowired
    private ErpBizLicensePartialPaymentMapper erpBizLicensePartialPaymentMapper;

    /**
     * 查询执照分批付款
     * 
     * @param numId 执照分批付款主键
     * @return 执照分批付款
     */
    @Override
    public ErpBizLicensePartialPayment selectErpBizLicensePartialPaymentByNumId(Long numId)
    {
        return erpBizLicensePartialPaymentMapper.selectErpBizLicensePartialPaymentByNumId(numId);
    }

    /**
     * 查询执照分批付款列表
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 执照分批付款
     */
    @Override
    public List<ErpBizLicensePartialPayment> selectErpBizLicensePartialPaymentList(ErpBizLicensePartialPayment erpBizLicensePartialPayment)
    {
        return erpBizLicensePartialPaymentMapper.selectErpBizLicensePartialPaymentList(erpBizLicensePartialPayment);
    }

    /**
     * 新增执照分批付款
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 结果
     */
    @Override
    public int insertErpBizLicensePartialPayment(ErpBizLicensePartialPayment erpBizLicensePartialPayment)
    {
        return erpBizLicensePartialPaymentMapper.insertErpBizLicensePartialPayment(erpBizLicensePartialPayment);
    }

    /**
     * 修改执照分批付款
     * 
     * @param erpBizLicensePartialPayment 执照分批付款
     * @return 结果
     */
    @Override
    public int updateErpBizLicensePartialPayment(ErpBizLicensePartialPayment erpBizLicensePartialPayment)
    {
        return erpBizLicensePartialPaymentMapper.updateErpBizLicensePartialPayment(erpBizLicensePartialPayment);
    }

    /**
     * 批量删除执照分批付款
     * 
     * @param numIds 需要删除的执照分批付款主键
     * @return 结果
     */
    @Override
    public int deleteErpBizLicensePartialPaymentByNumIds(Long[] numIds)
    {
        return erpBizLicensePartialPaymentMapper.deleteErpBizLicensePartialPaymentByNumIds(numIds);
    }

    /**
     * 删除执照分批付款信息
     * 
     * @param numId 执照分批付款主键
     * @return 结果
     */
    @Override
    public int deleteErpBizLicensePartialPaymentByNumId(Long numId)
    {
        return erpBizLicensePartialPaymentMapper.deleteErpBizLicensePartialPaymentByNumId(numId);
    }
}
