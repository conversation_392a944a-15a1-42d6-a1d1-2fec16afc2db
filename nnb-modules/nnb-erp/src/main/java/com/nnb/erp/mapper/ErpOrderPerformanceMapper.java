package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpOrderPerformance;
import com.nnb.erp.domain.ErpOrderPerformanceInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface ErpOrderPerformanceMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpOrderPerformance selectErpOrderPerformanceById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpOrderPerformance 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpOrderPerformance> selectErpOrderPerformanceList(ErpOrderPerformance erpOrderPerformance);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpOrderPerformance 【请填写功能名称】
     * @return 结果
     */
    public int insertErpOrderPerformance(ErpOrderPerformance erpOrderPerformance);

    public int insertErpOrderPerformanceInfo(ErpOrderPerformanceInfo erpOrderPerformanceInfo);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpOrderPerformance 【请填写功能名称】
     * @return 结果
     */
    public int updateErpOrderPerformance(ErpOrderPerformance erpOrderPerformance);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpOrderPerformanceById(Long id);

    public int deleteErpOrderPerformanceInfoByPerformanceId(Long performanceId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpOrderPerformanceByIds(Long[] ids);

    @Update("update erp_order_performance set status = #{newStatus} where order_id = #{orderId} and status = #{oldStatus}")
    public int updateStatusByOrderId(@Param("orderId") Long orderId,@Param("oldStatus") Long oldStatus,@Param("newStatus") Long newStatus);

    @Select("select * from erp_order_performance where order_id = #{orderId} and (status = 1 or status = 4)")
    public List<ErpOrderPerformance> selectListByOrderId(@Param("orderId") Long orderId);

    public BigDecimal selectFeeByUserIdAndReturnId(@Param("userId") String userId, @Param("returnDetailIdList") List<String> returnDetailIdList);

    @Select("select GROUP_CONCAT(DISTINCT user_id) as performanceUserIdStr, SUM(fee) as performancePrice from erp_order_performance_info " +
            "where find_in_set(return_detail_id , #{retainageReturnDetailIds})")
    public Map<String, Object> selectInfoByRetainageReturnDetailIds(@Param("retainageReturnDetailIds") String retainageReturnDetailIds);
}
