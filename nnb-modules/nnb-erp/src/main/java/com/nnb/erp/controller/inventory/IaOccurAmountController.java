package com.nnb.erp.controller.inventory;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.constant.InventoryConstants;
import com.nnb.erp.domain.inventory.*;
import com.nnb.erp.service.inventory.IaOccurAmountService;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description: 存货-发生额（每年不同）Controller
 * @Date: 2024-01-02
 * @Version: 1.0
 */
@RestController
@RequestMapping("/iaOccurAmount")
@Api(tags = "IaOccurAmountController", description = "存货-发生额（每年不同）")
public class IaOccurAmountController extends BaseController {

    @Autowired
    private IaOccurAmountService iaOccurAmountService;

    /**
     * 查询存货-发生额（每年不同）列表
     */
    @ApiOperation(value = "查询存货-发生额（每年不同）列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaOccurAmount.class)})
    @PostMapping("/list")
    public AjaxResult list(IaOccurAmount iaOccurAmount) {
        return AjaxResult.success(iaOccurAmountService.selectIaOccurAmountList(iaOccurAmount));
    }

    /**
     * 导出存货-发生额（每年不同）列表
     */
    @ApiOperation(value = "导出存货-发生额（每年不同）列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, IaOccurAmount iaOccurAmount) throws IOException {
        List<IaOccurAmount> list = iaOccurAmountService.selectIaOccurAmountList(iaOccurAmount);
        ExcelUtil<IaOccurAmount> util = new ExcelUtil<IaOccurAmount>(IaOccurAmount.class);
        util.exportExcel(response, list, "存货-发生额（每年不同）数据");
    }

    /**
     * 获取存货-发生额（每年不同）详细信息
     */
    @ApiOperation(value = "获取存货-发生额（每年不同）详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaOccurAmount.class)})
    @PostMapping("/getById")
    public AjaxResult getInfo(@ApiParam(name = "id", value = "存货-发生额（每年不同）id") @PathVariable("id") Long id) {
        return AjaxResult.success(iaOccurAmountService.selectIaOccurAmountById(id));
    }

    /**
     * 新增存货-发生额（每年不同）
     */
    @ApiOperation(value = "新增存货-发生额（每年不同）")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IaOccurAmount iaOccurAmount) {
        return toAjax(iaOccurAmountService.insertIaOccurAmount(iaOccurAmount));
    }

    /**
     * 修改存货-发生额（每年不同）
     */
    @ApiOperation(value = "修改存货-发生额（每年不同）")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody IaOccurAmount iaOccurAmount) {
        return toAjax(iaOccurAmountService.updateIaOccurAmount(iaOccurAmount));
    }

    /**
     * 删除存货-发生额（每年不同）
     */
    @ApiOperation(value = "删除存货-发生额（每年不同）")
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody IaOccurAmount iaOccurAmount) {
        return toAjax(iaOccurAmountService.deleteIaOccurAmountByIds(iaOccurAmount.getIds()));
    }

    @ApiOperation(value = "收发存汇总表")
    @PostMapping("/summary-list")
    public TableDataInfo summaryList(@RequestBody IaSummarySearch iaSummarySearch) {
        Map<String, Object> map = iaOccurAmountService.summaryList(iaSummarySearch);
        List<IaSummaryRes> list = MapUtil.get(
                map, "list", new TypeReference<List<IaSummaryRes>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    @ApiOperation(value = "收发存明细表")
    @PostMapping("/summary-detail-list")
    public TableDataInfo summaryDetailList(@RequestBody IaSummarySearch iaSummarySearch) {
        Map<String, Object> map = iaOccurAmountService.summaryDetailList(iaSummarySearch);
        List<IaSummaryDetailRes> list = MapUtil.get(
                map, "list", new TypeReference<List<IaSummaryDetailRes>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    @ApiOperation(value = "导出收发存汇总表")
    @PostMapping("/summary-list-export")
    public void summaryListExport(HttpServletResponse response, @RequestBody IaSummarySearch iaSummarySearch) throws IOException {
        iaSummarySearch.setExportFile(InventoryConstants.EXPORT_FILE);
        Map<String, Object> map = iaOccurAmountService.summaryList(iaSummarySearch);
        List<IaSummaryRes> list = MapUtil.get(
                map, "list", new TypeReference<List<IaSummaryRes>>() {
                });
        List<IaSummaryResExcelVo> excelVoList = list.stream()
                .map(en -> {
                    IaSummaryResExcelVo vo = new IaSummaryResExcelVo();
                    BeanUtils.copyProperties(en, vo);
                    return vo;
                }).collect(Collectors.toList());
        ExcelUtil<IaSummaryResExcelVo> util = new ExcelUtil<IaSummaryResExcelVo>(IaSummaryResExcelVo.class);
        util.exportExcel(response, excelVoList, "收发存汇总", "收发存汇总");
    }

    @ApiOperation(value = "导出收发存明细表")
    @PostMapping("/summary-detail-list-export")
    public void summaryDetailListExport(HttpServletResponse response, @RequestBody IaSummarySearch iaSummarySearch) throws IOException {
        iaSummarySearch.setExportFile(InventoryConstants.EXPORT_FILE);
        Map<String, Object> map = iaOccurAmountService.summaryDetailList(iaSummarySearch);
        List<IaSummaryDetailRes> list = MapUtil.get(
                map, "list", new TypeReference<List<IaSummaryDetailRes>>() {
                });
        List<IaSummaryDetailResExcelVo> excelVoList = list.stream()
                .map(en -> {
                    IaSummaryDetailResExcelVo vo = new IaSummaryDetailResExcelVo();
                    BeanUtils.copyProperties(en, vo);
                    return vo;
                }).collect(Collectors.toList());
        ExcelUtil<IaSummaryDetailResExcelVo> util = new ExcelUtil<IaSummaryDetailResExcelVo>(IaSummaryDetailResExcelVo.class);
        util.exportExcel(response, excelVoList, "收发存明细", "收发存明细");
    }

    @ApiOperation(value = "库存存量列表")
    @PostMapping("/inventory-stock-list")
    public TableDataInfo inventoryStockList(@Validated @RequestBody InventoryStockSearch stockSearch){
        Map<String, Object> map = iaOccurAmountService.inventoryStockList(stockSearch);
        List<InventoryStockList> list = MapUtil.get(
                map, "list", new TypeReference<List<InventoryStockList>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    @ApiOperation(value = "库存存量详情列表")
    @PostMapping("/inventory-stock-detail-list")
    public TableDataInfo inventoryStockDetailList(@RequestBody StockDetailSearch stockDetailSearch){
        Map<String, Object> map = iaOccurAmountService.inventoryStockDetailList(stockDetailSearch);
        List<IaStorageDetail> list = MapUtil.get(
                map, "list", new TypeReference<List<IaStorageDetail>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    @ApiOperation(value = "统计总存货量")
    @GetMapping("/count-inventory")
    public AjaxResult countInventory(@RequestParam("showStock") Integer showStock){
        return AjaxResult.success(iaOccurAmountService.countInventory(showStock));
    }

    @ApiOperation(value = "定时任务：定时统计采购价，市场价，总数")
    @GetMapping("/inventory-stock-task")
    public AjaxResult inventoryStockTask(){
        return AjaxResult.success(iaOccurAmountService.inventoryStockTask());
    }

    @ApiOperation(value = "定时修正库存数量")
    @GetMapping("/fix-inventory-qut")
    public AjaxResult fixInventoryQut(){
        return AjaxResult.success(iaOccurAmountService.fixInventoryQty());
    }

}
