package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户，提单，DTO。
 * 用于确认订单。
 *
 * <AUTHOR>
 * @since 2022/3/15 17:04
 */
@Data
@ApiModel(value = "ErpClientForConfirmOrderDTO", description = "客户，提单，DTO。 用于确认订单。")
public class ErpClientForConfirmOrderDTO {

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private String orderId;

    /**
     * 客户城市。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("客户城市。")
    private Long clientCity;

    @ApiModelProperty("企业个人id")
    private String clientId;

    @ApiModelProperty("0 未成交；1 已成交")
    private Integer numStatus;

    /**
     * 客户纳税类型。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("客户纳税类型。")
    private Long clientTax;

    /**
     * 调用来源，0后台调用，1小程序调用。
     */
    @ApiModelProperty("调用来源，0后台调用，1小程序调用。")
    private Integer source;

    @ApiModelProperty("0 优惠券 1组合活动")
    private Integer tab;

    /**
     * 产品。
     */
    @ApiModelProperty("产品。")
    private List<ErpProductForConfirmOrderDTO> products;

    /**
     * 线索ID
     */
    @ApiModelProperty("线索ID")
    private Long clueId;
    @ApiModelProperty("提单类型1 客保 2客户")
    private Integer commitOrderType;
    private Long activityId;

    @ApiModelProperty("执照编号")
    private String licenseNo;

    private String phone;
}
