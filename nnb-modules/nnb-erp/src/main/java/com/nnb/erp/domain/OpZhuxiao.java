package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * order_productions的冗余对象 op_zhuxiao
 * 
 * <AUTHOR>
 * @date 2022-10-20
 */
@ApiModel(value="OpZhuxiao",description="order_productions的冗余对象")
public class OpZhuxiao extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    @ApiModelProperty("主键自增")
    private Long id;

    /** order_productions.id */
    @Excel(name = "order_productions.id")
    @ApiModelProperty("order_productions.id")
    private Integer opId;

    /** 工商注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "工商注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("工商注销状态 1代注销，2注销中，3注销完成")
    private Integer gsZxStatus;

    /** 工商注销执行人 */
    @Excel(name = "工商注销执行人")
    @ApiModelProperty("工商注销执行人")
    private Long gsZxUser;

    /** 税务注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "税务注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("税务注销状态 1代注销，2注销中，3注销完成")
    private Integer swZxStatus;

    /** 税务注销执行人 */
    @Excel(name = "税务注销执行人")
    @ApiModelProperty("税务注销执行人")
    private Long swZxUser;

    /** 银行注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "银行注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("银行注销状态 1代注销，2注销中，3注销完成")
    private Integer yhZxStatus;

    /** 银行注销执行人 */
    @Excel(name = "银行注销执行人")
    @ApiModelProperty("银行注销执行人")
    private Long yhZxUser;

    /** 社保注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "社保注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("社保注销状态 1代注销，2注销中，3注销完成")
    private Integer sbZxStatus;

    /** 社保注销执行人 */
    @Excel(name = "社保注销执行人")
    @ApiModelProperty("社保注销执行人")
    private Long sbZxUser;

    /** 公积金注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "公积金注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("公积金注销状态 1代注销，2注销中，3注销完成")
    private Integer gjjZxStatus;

    /** 公积金注销执行人 */
    @Excel(name = "公积金注销执行人")
    @ApiModelProperty("公积金注销执行人")
    private Long gjjZxUser;

    /** 总注销状态 1代注销，2注销中，3注销完成 */
    @Excel(name = "总注销状态 1代注销，2注销中，3注销完成")
    @ApiModelProperty("总注销状态 1代注销，2注销中，3注销完成")
    private Integer zxStatus;

    /** 工商注销备注 */
    @Excel(name = "工商注销备注")
    @ApiModelProperty("工商注销备注")
    private String gsZxRemark;

    /** 税务注销备注 */
    @Excel(name = "税务注销备注")
    @ApiModelProperty("税务注销备注")
    private String swZxRemark;

    /** 银行注销备注 */
    @Excel(name = "银行注销备注")
    @ApiModelProperty("银行注销备注")
    private String yhZxRemark;

    /** 社保注销备注 */
    @Excel(name = "社保注销备注")
    @ApiModelProperty("社保注销备注")
    private String sbZxRemark;

    /** 公积金注销备注 */
    @Excel(name = "公积金注销备注")
    @ApiModelProperty("公积金注销备注")
    private String gjjZxRemark;

    /** $column.columnComment */
    @Excel(name = "公积金注销备注")
    @ApiModelProperty("$column.columnComment")
    private Long createdBy;

    /** $column.columnComment */
    @Excel(name = "公积金注销备注")
    @ApiModelProperty("$column.columnComment")
    private Long updatedBy;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOpId(Integer opId) 
    {
        this.opId = opId;
    }

    public Integer getOpId() 
    {
        return opId;
    }
    public void setGsZxStatus(Integer gsZxStatus) 
    {
        this.gsZxStatus = gsZxStatus;
    }

    public Integer getGsZxStatus() 
    {
        return gsZxStatus;
    }
    public void setGsZxUser(Long gsZxUser) 
    {
        this.gsZxUser = gsZxUser;
    }

    public Long getGsZxUser() 
    {
        return gsZxUser;
    }
    public void setSwZxStatus(Integer swZxStatus) 
    {
        this.swZxStatus = swZxStatus;
    }

    public Integer getSwZxStatus() 
    {
        return swZxStatus;
    }
    public void setSwZxUser(Long swZxUser) 
    {
        this.swZxUser = swZxUser;
    }

    public Long getSwZxUser() 
    {
        return swZxUser;
    }
    public void setYhZxStatus(Integer yhZxStatus) 
    {
        this.yhZxStatus = yhZxStatus;
    }

    public Integer getYhZxStatus() 
    {
        return yhZxStatus;
    }
    public void setYhZxUser(Long yhZxUser) 
    {
        this.yhZxUser = yhZxUser;
    }

    public Long getYhZxUser() 
    {
        return yhZxUser;
    }
    public void setSbZxStatus(Integer sbZxStatus) 
    {
        this.sbZxStatus = sbZxStatus;
    }

    public Integer getSbZxStatus() 
    {
        return sbZxStatus;
    }
    public void setSbZxUser(Long sbZxUser) 
    {
        this.sbZxUser = sbZxUser;
    }

    public Long getSbZxUser() 
    {
        return sbZxUser;
    }
    public void setGjjZxStatus(Integer gjjZxStatus) 
    {
        this.gjjZxStatus = gjjZxStatus;
    }

    public Integer getGjjZxStatus() 
    {
        return gjjZxStatus;
    }
    public void setGjjZxUser(Long gjjZxUser) 
    {
        this.gjjZxUser = gjjZxUser;
    }

    public Long getGjjZxUser() 
    {
        return gjjZxUser;
    }
    public void setZxStatus(Integer zxStatus) 
    {
        this.zxStatus = zxStatus;
    }

    public Integer getZxStatus() 
    {
        return zxStatus;
    }
    public void setGsZxRemark(String gsZxRemark) 
    {
        this.gsZxRemark = gsZxRemark;
    }

    public String getGsZxRemark() 
    {
        return gsZxRemark;
    }
    public void setSwZxRemark(String swZxRemark) 
    {
        this.swZxRemark = swZxRemark;
    }

    public String getSwZxRemark() 
    {
        return swZxRemark;
    }
    public void setYhZxRemark(String yhZxRemark) 
    {
        this.yhZxRemark = yhZxRemark;
    }

    public String getYhZxRemark() 
    {
        return yhZxRemark;
    }
    public void setSbZxRemark(String sbZxRemark) 
    {
        this.sbZxRemark = sbZxRemark;
    }

    public String getSbZxRemark() 
    {
        return sbZxRemark;
    }
    public void setGjjZxRemark(String gjjZxRemark) 
    {
        this.gjjZxRemark = gjjZxRemark;
    }

    public String getGjjZxRemark() 
    {
        return gjjZxRemark;
    }
    public void setCreatedBy(Long createdBy) 
    {
        this.createdBy = createdBy;
    }

    public Long getCreatedBy() 
    {
        return createdBy;
    }
    public void setUpdatedBy(Long updatedBy) 
    {
        this.updatedBy = updatedBy;
    }

    public Long getUpdatedBy() 
    {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("opId", getOpId())
            .append("gsZxStatus", getGsZxStatus())
            .append("gsZxUser", getGsZxUser())
            .append("swZxStatus", getSwZxStatus())
            .append("swZxUser", getSwZxUser())
            .append("yhZxStatus", getYhZxStatus())
            .append("yhZxUser", getYhZxUser())
            .append("sbZxStatus", getSbZxStatus())
            .append("sbZxUser", getSbZxUser())
            .append("gjjZxStatus", getGjjZxStatus())
            .append("gjjZxUser", getGjjZxUser())
            .append("zxStatus", getZxStatus())
            .append("gsZxRemark", getGsZxRemark())
            .append("swZxRemark", getSwZxRemark())
            .append("yhZxRemark", getYhZxRemark())
            .append("sbZxRemark", getSbZxRemark())
            .append("gjjZxRemark", getGjjZxRemark())
            .append("createdBy", getCreatedBy())
            .append("updatedBy", getUpdatedBy())
            .toString();
    }
}
