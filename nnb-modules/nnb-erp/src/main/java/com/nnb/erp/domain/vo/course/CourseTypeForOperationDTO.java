package com.nnb.erp.domain.vo.course;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 课程分类，用于操作，DTO。
 *
 * <AUTHOR>
 * @since 2022-06-21 18:24:16
 */
@Data
public class CourseTypeForOperationDTO {

    /**
     * 分类标识。
     */
    @ApiModelProperty("分类标识。")
    Integer courseTypeId;

    /**
     * 父级分类标识。
     */
    @ApiModelProperty("父级分类标识。")
    Integer parentId;

    /**
     * 分类名称。
     */
    @ApiModelProperty("分类名称。")
    String typeName;

    /**
     * 分类排序。
     */
    @ApiModelProperty("分类排序。")
    Integer typeSort;

}
