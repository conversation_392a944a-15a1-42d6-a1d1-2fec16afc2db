package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 erp_examine_approve_detail
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@ApiModel(value="ErpExamineApproveDetail",description="【请填写功能名称】对象")
public class ErpExamineApproveDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** erp_examine_approve.id */
    @Excel(name = "erp_examine_approve.id")
    @ApiModelProperty("erp_examine_approve.id")
    private Long approveId;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("操作时间")
    private Date operateTime;

    /** 操作人类型 */
    @Excel(name = "操作人类型")
    @ApiModelProperty("操作人类型")
    private Integer operateUserType;

    /** 1通过2驳回 */
    @Excel(name = "1通过2驳回")
    @ApiModelProperty("1通过2驳回")
    private Integer operateType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApproveId(Long approveId) 
    {
        this.approveId = approveId;
    }

    public Long getApproveId() 
    {
        return approveId;
    }
    public void setOperateTime(Date operateTime) 
    {
        this.operateTime = operateTime;
    }

    public Date getOperateTime() 
    {
        return operateTime;
    }
    public void setOperateUserType(Integer operateUserType) 
    {
        this.operateUserType = operateUserType;
    }

    public Integer getOperateUserType() 
    {
        return operateUserType;
    }
    public void setOperateType(Integer operateType) 
    {
        this.operateType = operateType;
    }

    public Integer getOperateType() 
    {
        return operateType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("approveId", getApproveId())
            .append("operateTime", getOperateTime())
            .append("operateUserType", getOperateUserType())
            .append("operateType", getOperateType())
            .toString();
    }
}
