package com.nnb.erp.domain.vo;

import com.nnb.common.core.annotation.Excel;
import com.nnb.erp.domain.ErpBizServiceAddressCost;
import com.nnb.erp.domain.ErpBizServiceAgent;
import com.nnb.erp.domain.ErpBizServiceBank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpBizServiceAddressCostVo extends ErpBizServiceAddressCost {

    @Excel(name = "代理商名称")
    private String agentNumber;
    @Excel(name = "代理商类型")
    private String agentTypeName;
    @Excel(name = "产品名称")
    private String detailAddress;
    @Excel(name = "产品类型")
    private String costTypeName;
    @Excel(name = "区域")
    private String registerAreaName;
    @Excel(name = "结算类型")
    private String typePriceName;
    @Excel(name = "价格")
    private BigDecimal endPrice;
    @Excel(name = "是否可预付")
    private String isAdvanceName;
    @Excel(name = "合作状态")
    private String cooperateStatusName;



    @Excel(name = "agent")
    private ErpBizServiceAgent agent;
    @Excel(name = "bankList")
    private List<ErpBizServiceBank> bankList;



}
