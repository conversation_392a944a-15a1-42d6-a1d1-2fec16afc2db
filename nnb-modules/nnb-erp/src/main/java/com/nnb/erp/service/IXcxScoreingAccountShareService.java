package com.nnb.erp.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.nnb.erp.domain.XcxScoreingAccountShare;
import com.nnb.erp.domain.XcxScoreingAccountShareStatistics;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IXcxScoreingAccountShareService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public XcxScoreingAccountShare selectXcxScoreingAccountShareById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param xcxScoreingAccountShare 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<XcxScoreingAccountShare> selectXcxScoreingAccountShareList(XcxScoreingAccountShare xcxScoreingAccountShare);

    /**
     * 新增【请填写功能名称】
     * 
     * @param xcxScoreingAccountShare 【请填写功能名称】
     * @return 结果
     */
    public int insertXcxScoreingAccountShare(XcxScoreingAccountShare xcxScoreingAccountShare);

    /**
     * 修改【请填写功能名称】
     * 
     * @param xcxScoreingAccountShare 【请填写功能名称】
     * @return 结果
     */
    public int updateXcxScoreingAccountShare(XcxScoreingAccountShare xcxScoreingAccountShare);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteXcxScoreingAccountShareByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXcxScoreingAccountShareById(Long id);

    public List<XcxScoreingAccountShareStatistics> statistics(XcxScoreingAccountShareStatistics xcxScoreingAccountShareStatistics);
}
