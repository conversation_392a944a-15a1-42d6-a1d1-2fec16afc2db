package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizLicenseProductInfo;

/**
 * 执照关联产品信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface IErpBizLicenseProductInfoService 
{
    /**
     * 查询执照关联产品信息
     * 
     * @param numId 执照关联产品信息主键
     * @return 执照关联产品信息
     */
    public ErpBizLicenseProductInfo selectErpBizLicenseProductInfoByNumId(Long numId);

    /**
     * 查询执照关联产品信息列表
     * 
     * @param erpBizLicenseProductInfo 执照关联产品信息
     * @return 执照关联产品信息集合
     */
    public List<ErpBizLicenseProductInfo> selectErpBizLicenseProductInfoList(ErpBizLicenseProductInfo erpBizLicenseProductInfo);

    /**
     * 新增执照关联产品信息
     * 
     * @param erpBizLicenseProductInfo 执照关联产品信息
     * @return 结果
     */
    public int insertErpBizLicenseProductInfo(ErpBizLicenseProductInfo erpBizLicenseProductInfo);

    /**
     * 修改执照关联产品信息
     * 
     * @param erpBizLicenseProductInfo 执照关联产品信息
     * @return 结果
     */
    public int updateErpBizLicenseProductInfo(ErpBizLicenseProductInfo erpBizLicenseProductInfo);

    /**
     * 批量删除执照关联产品信息
     * 
     * @param numIds 需要删除的执照关联产品信息主键集合
     * @return 结果
     */
    public int deleteErpBizLicenseProductInfoByNumIds(Long[] numIds);

    /**
     * 删除执照关联产品信息信息
     * 
     * @param numId 执照关联产品信息主键
     * @return 结果
     */
    public int deleteErpBizLicenseProductInfoByNumId(Long numId);
}
