package com.nnb.erp.constant.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2022-10-13
 * @Version: 1.0
 */
public enum OrderStatusNewEnum {

    //订单状态：0.作废  1.审核通过
    // 2.待经理审核 3.驳回
    // 4.撤销,5,无6.全额退费
    // 7待财务审核 8待客户签约
    // 9客户驳回, 10待上传收款截图
    // 13订单修改待经理审核 14订单修改待审核  15订单修改驳回

    to_void(0,"作废"),
    ORDER_NOT_AUDIT(1, "审核通过"),
    ORDER_NOT_BIZ(2, "待经理审核"),
    ORDER_BIZ_END(3, "驳回"),
    ORDER_DONED(4, "撤销"),
    ORDER_NO(5, "无"),
    ORDER_FULL_REFUND(6, "全额退费"),
    ORDER_SEVEN(7, "待财务审核"),
    ORDER_EIGHT(8, "待客户签约"),
    ORDER_NINE(9, "客户驳回"),
    ORDER_TEN(10, "待上传收款截图"),
    ORDER_THIRTEEN(13, "订单修改待经理审核"),
    ORDER_FOURTEEN(14, "订单修改待审核"),
    ORDER_FIFTEEN(15, "订单修改驳回"),
    ;

    public Integer getStatusType() {
        return statusType;
    }

    public String getStatusName() {
        return statusName;
    }

    private final Integer statusType;
    private final String statusName;

    OrderStatusNewEnum(Integer statusType, String statusName) {
        this.statusType = statusType;
        this.statusName = statusName;
    }

    public static String getNameByType(Integer statusType) {
        List<OrderStatusNewEnum> resList = Arrays.stream(OrderStatusNewEnum.values()).filter(e -> e.getStatusType().equals(statusType)).collect(Collectors.toList());
        if (resList.size() == 0) {
            return statusType + "";
        } else {
            return resList.get(0).getStatusName();
        }
    }
}
