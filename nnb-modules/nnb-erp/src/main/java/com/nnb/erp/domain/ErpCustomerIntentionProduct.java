package com.nnb.erp.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 记账客户意向产品对象 erp_customer_intention_product
 * 
 * <AUTHOR>
 * @date 2024-11-13
 */
@ApiModel(value="ErpCustomerIntentionProduct",description="记账客户意向产品对象")
public class ErpCustomerIntentionProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 意向ID */
    @Excel(name = "意向ID")
    @ApiModelProperty("意向ID")
    private Long intentionId;

    /** 意向产品二级ID */
    @Excel(name = "意向产品二级ID")
    @ApiModelProperty("意向产品二级ID")
    private Long productTypeId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setIntentionId(Long intentionId) 
    {
        this.intentionId = intentionId;
    }

    public Long getIntentionId() 
    {
        return intentionId;
    }
    public void setProductTypeId(Long productTypeId) 
    {
        this.productTypeId = productTypeId;
    }

    public Long getProductTypeId() 
    {
        return productTypeId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("intentionId", getIntentionId())
            .append("productTypeId", getProductTypeId())
            .toString();
    }
}
