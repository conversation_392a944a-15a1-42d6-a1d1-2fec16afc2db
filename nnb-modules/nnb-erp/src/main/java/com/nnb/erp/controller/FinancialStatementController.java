package com.nnb.erp.controller;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.PageDomain;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.common.core.web.page.TableSupport;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.dto.*;
import com.nnb.erp.domain.dto.service.ServiceMainDto;
import com.nnb.erp.domain.vo.CollectionReportVo;
import com.nnb.erp.domain.vo.SaleStatisticsVo;
import com.nnb.erp.domain.vo.qzd.LicenseSalesBoardExportVo;
import com.nnb.erp.domain.vo.qzd.LicenseSalesBoardVo;
import com.nnb.erp.domain.vo.report.*;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.mapper.ErpRetainageReturnMapper;
import com.nnb.erp.mapper.ErpServiceOrdersMapper;
import com.nnb.erp.service.IErpOrdersService;
import com.nnb.erp.service.IErpServiceOrdersService;
import com.nnb.erp.service.ISServiceMainService;
import com.nnb.erp.service.approval.ICostSettlementService;
import com.nnb.erp.service.impl.ExportLogServiceImpl;
import com.nnb.erp.service.impl.approval.CostSettlementServiceImpl;
import com.nnb.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import net.sf.json.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/financialstatement")
@Api(tags = "FinancialStatementController", description = "FinancialStatementController")
public class FinancialStatementController extends BaseController {

    @Autowired
    private ICostSettlementService costSettlementService;

    @Autowired
    private IErpOrdersService erpOrdersService;

    @Autowired
    private IErpServiceOrdersService erpServiceOrdersService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISServiceMainService serviceMainService;
    @Autowired
    private ExportLogServiceImpl exportLogService;


    /**
     * 查询成本管理报表列表
     */
    @ApiOperation(value = "查询成本管理报表列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = CostSettleManageVo.class)})
    @GetMapping("/list")
    public TableDataInfo list(CostSettleManageDto costSettleManageDto) {
        Page<Object> objects = startPageReturn();

        List<CostSettleManageVo> costSettleManageVoList = costSettlementService.getCostSettleManageVoList(costSettleManageDto);
        long total = objects.getTotal();

        return getDataTableAndTotal(costSettleManageVoList, total);
    }


    /**
     * 查询成本管理报表列表
     */
    @ApiOperation(value = "查询成本管理报表详情")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = CostSettleManageVo.class)})
    @GetMapping("/detail")
    public AjaxResult detail(CostSettleManageDetailDto costSettleManageDetailDto) {
        List<CostSettleManageDetailVo> costSettleManageDetail = costSettlementService.getCostSettleManageDetail(costSettleManageDetailDto);

        return AjaxResult.success(costSettleManageDetail);
    }

    @ApiOperation(value = "导出查询成本管理报表列表")
    @PostMapping("/exportCostSettle")
    public void exportCostSettle(HttpServletResponse response, @RequestBody CostSettleManageDto costSettleManageDto) throws IOException {
        List<CostSettleManageVo> costSettleManageVoList = costSettlementService.getCostSettleManageVoList(costSettleManageDto);
        List<CostSettleManageExportVo> list = new ArrayList<>();
        costSettleManageVoList.forEach(val -> {
            CostSettleManageExportVo costSettleManageExportVo = new CostSettleManageExportVo();
            BeanUtils.copyProperties(val, costSettleManageExportVo);
            list.add(costSettleManageExportVo);
        });
        ExcelUtil<CostSettleManageExportVo> util = new ExcelUtil<CostSettleManageExportVo>(CostSettleManageExportVo.class);
        util.exportExcel(response, list, "成本管理报表");

    }


    @ApiOperation(value = "销售表列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = SalesListingVo.class)})
    @GetMapping("/getSalesListing")
    public TableDataInfo getSalesListing(SaleListingDto saleListingDto) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        saleListingDto.setPageNum(((pageNum - 1) * pageSize));
        saleListingDto.setPageSize(pageSize);

        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeStart())){
            try {
                saleListingDto.setDatFinanceCollectionTimeStart(DateUtils.parseDate(getFirstDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间首天异常，异常信息为：", e);
            }
        }
        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeEnd())){
            try {
                saleListingDto.setDatFinanceCollectionTimeEnd(DateUtils.parseDate(getLastDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间末天异常，异常信息为：", e);
            }
        }

        long total = erpOrdersService.getSalesListingCount(saleListingDto);

        List<SalesListingVo> salesListing = erpOrdersService.getSalesListing(saleListingDto);

        return getDataTableAndTotal(salesListing, total);
    }

    @ApiOperation(value = "导出销售报表")
    @PostMapping("/exportSalesListing")
    public void exportSalesListing(HttpServletResponse response, @RequestBody SaleListingDto saleListingDto) throws IOException {
        saleListingDto.setPageNum(null);
        saleListingDto.setPageSize(null);
        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeStart())){
            try {
                saleListingDto.setDatFinanceCollectionTimeStart(DateUtils.parseDate(getFirstDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间首天异常，异常信息为：", e);
            }
        }
        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeEnd())){
            try {
                saleListingDto.setDatFinanceCollectionTimeEnd(DateUtils.parseDate(getLastDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间末天异常，异常信息为：", e);
            }
        }
        List<SalesListingVo> salesListing = erpOrdersService.getSalesListing(saleListingDto);

        List<SalesListingExportVo> list = new ArrayList<>();
        salesListing.forEach(val -> {
            SalesListingExportVo salesListingExportVo = new SalesListingExportVo();
            BeanUtils.copyProperties(val, salesListingExportVo);
            if(Objects.nonNull(val.getIsFinish())){
                if(1 == val.getIsFinish()){
                    salesListingExportVo.setIsFinishStr("是");
                }
                if(2 == val.getIsFinish()){
                    salesListingExportVo.setIsFinishStr("否");
                }
            }
            list.add(salesListingExportVo);
        });
        ExcelUtil<SalesListingExportVo> util = new ExcelUtil<SalesListingExportVo>(SalesListingExportVo.class);
        util.exportExcel(response, list, "销售报表");

    }

    @ApiOperation(value = "导出执照销售报表")
    @PostMapping("/exportLicenseSalesBoard")
    public void exportLicenseSalesBoard(HttpServletResponse response, @RequestBody SaleListingDto saleListingDto) throws IOException {

        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeStart())){
            try {
                saleListingDto.setDatFinanceCollectionTimeStart(DateUtils.parseDate(getFirstDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间首天异常，异常信息为：", e);
            }
        }
        if(Objects.isNull(saleListingDto.getDatFinanceCollectionTimeEnd())){
            try {
                saleListingDto.setDatFinanceCollectionTimeEnd(DateUtils.parseDate(getLastDayOfMonth(), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                logger.error("获取财务收款时间末天异常，异常信息为：", e);
            }
        }
        List<LicenseSalesBoardVo> licenseSalesBoardVo = erpOrdersService.getLicenseSalesBoardVo(saleListingDto);

        List<LicenseSalesBoardExportVo> list = new ArrayList<>();
        licenseSalesBoardVo.forEach(val -> {
            LicenseSalesBoardExportVo licenseSalesBoardExportVo = new LicenseSalesBoardExportVo();
            BeanUtils.copyProperties(val, licenseSalesBoardExportVo);
            list.add(licenseSalesBoardExportVo);
        });
        ExcelUtil<LicenseSalesBoardExportVo> util = new ExcelUtil<>(LicenseSalesBoardExportVo.class);
        util.exportExcel(response, list, "执照销售报表");

    }

    /**
     * 获取本月第一天
     * @return
     */
    private String getFirstDayOfMonth(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String startDay = df.format(new Date());

        Calendar calendar = Calendar.getInstance();
        calendar.set(Integer.parseInt(startDay.substring(0,4)), Integer.parseInt(startDay.substring(5,7)) - 1, 1);
        String firstDayOfMonth = new SimpleDateFormat( "yyyy-MM-dd ").format(calendar.getTime());

        return firstDayOfMonth + " 00:00:00";
    }

    /**
     * 获取本月最后一天
     * @return
     */
    private String getLastDayOfMonth(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String startDay = df.format(new Date());

        Calendar calendar = Calendar.getInstance();
        calendar.set(Integer.parseInt(startDay.substring(0,4)), Integer.parseInt(startDay.substring(5,7)), 1);
        //这里将日期值减去一天，从而获取到要求的月份最后一天
        calendar.add(Calendar.DATE, -1);
        String lastDayOfMonth = new SimpleDateFormat( "yyyy-MM-dd ").format(calendar.getTime());

        return lastDayOfMonth + " 23:59:59";
    }



    @ApiOperation(value = "导出销售报表")
    @GetMapping("/dealCommitOrderMoneyData")
    public void dealCommitOrderMoneyData(){
        erpServiceOrdersService.dealCommitOrderMoneyData();
    }


    @ApiOperation(value = "查询销售报表不对的数据")
    @GetMapping("/getCommitOrderMoneyErrorData")
    public void getCommitOrderMoneyErrorData(){
        erpServiceOrdersService.getCommitOrderMoneyErrorData();
    }

    @ApiOperation(value = "处理销售报表不对的数据")
    @GetMapping("/dealCommitOrderMoneyErrorData")
    public void dealCommitOrderMoneyErrorData(){
        erpServiceOrdersService.dealCommitOrderMoneyErrorData();
    }


    /**
     * 查询成本管理报表列表
     */
    @ApiOperation(value = "销售收款表新")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = CostSettleManageVo.class)})
    @GetMapping("/saleStatisticsListNew")
    public TableDataInfo saleStatisticsListNew(SaleStatisticsDto saleStatisticsDto) {
        Page<Object> objects = startPageReturn();
        List<SaleStatisticsVo> list = serviceMainService.getSaleStatisticsListNew(saleStatisticsDto);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    /**
     * 查询成本管理报表列表
     */
    @PostMapping("/exportSaleStatisticsListNew")
    public void exportSaleStatisticsListNew(HttpServletResponse response, @RequestBody SaleStatisticsDto saleStatisticsDto) throws IOException {
        List<SaleStatisticsVo> list = serviceMainService.getSaleStatisticsListNew(saleStatisticsDto);
        ExcelUtil<SaleStatisticsVo> util = new ExcelUtil<SaleStatisticsVo>(SaleStatisticsVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 新的销售收款报表
     */
    @ApiOperation(value = "新的销售收款报表")
    @GetMapping("/collectionReport")
    public TableDataInfo collectionReport(CollectionReportDto dto) {
        Page<Object> objects = startPageReturn();
        List<CollectionReportVo> list = erpOrdersService.collectionReport(dto);
        long total = objects.getTotal();
        return getDataTableAndTotal(list, total);
    }

    /**
     * 新的销售收款报表导出
     */
    @PostMapping("/collectionReportExport")
    public void collectionReportExport(HttpServletResponse response, @RequestBody CollectionReportDto dto) throws IOException {
        List<CollectionReportVo> list = erpOrdersService.collectionReport(dto);
        ExcelUtil<CollectionReportVo> util = new ExcelUtil<CollectionReportVo>(CollectionReportVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");

        JSONArray idList = new JSONArray();
        if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                idList.add(list.get(i).getVcOrderNumber());
            }
        }
        exportLogService.insertData("统计分析", "销售表", idList.toString());
    }
}
