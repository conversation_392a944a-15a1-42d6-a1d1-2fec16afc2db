package com.nnb.erp.domain.dto.approval;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 通用审批业务对象 erp_general_approval_service
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
@Data
@ApiModel(value="ErpGeneralApprovalServiceDTO",description="通用审批业务对象")
public class ErpGeneralApprovalServiceDTO
{
    private static final long serialVersionUID = 1L;

    /** 申请类型 */
    @ApiModelProperty("申请类型")
    private Long approveType;

    /** 审批ID */
    @Excel(name = "审批ID")
    @ApiModelProperty("审批ID")
    private Long approveId;

    /** 部门ID */
    @Excel(name = "部门ID")
    @ApiModelProperty("部门ID")
    private Long deptId;

    /** 付款事由 */
    @Excel(name = "付款事由")
    @ApiModelProperty("付款事由")
    private String reason;

    /** 付款金额 */
    @Excel(name = "付款金额")
    @ApiModelProperty("付款金额")
    private BigDecimal money;

    /** 支付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "支付日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("支付日期")
    private Date paymentTime;

    /** 收款人全称 */
    @Excel(name = "收款人全称")
    @ApiModelProperty("收款人全称")
    private String payeeName;

    /** 银行账户 */
    @ApiModelProperty("银行账户")
    private String bankAccount;

    /** 开户行 */
    @ApiModelProperty("开户行")
    private String openingBank;

    /** 附件 */
    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("备注")
    private String remark;

}
