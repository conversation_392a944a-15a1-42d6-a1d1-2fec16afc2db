package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 尾款回款，用于审核，VO。
 *
 * <AUTHOR>
 * @since 2022/4/25 17:38
 */
@Data
public class ErpRetainageReturnForExamineVO {
    /**
     * 尾款标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("尾款标识。")
    private Long returnId;

    /**
     * 订单标识。
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("订单标识。")
    private Long orderId;

    /**
     * 订单编号。
     */
    @ApiModelProperty("订单编号。")
    private String orderNumber;

    /**
     * 客户名称。
     */
    @ApiModelProperty("客户名称。")
    private String clientName;

    /**
     * 线索联系人。
     */
    @ApiModelProperty("线索联系人。")
    private String customerName;

    /**
     * 签约人名称。
     */
    @ApiModelProperty("签约人名称。")
    private String userName;

    /**
     * 剩余尾款。
     */
    @ApiModelProperty("剩余尾款。")
    private BigDecimal collectionPrice;

    /**
     * 本次回款。
     */
    @ApiModelProperty("本次回款。")
    private BigDecimal lastPrice;

    /**
     * 回款审核状态：0待审核，1审核通过，2审核驳回，3撤销。
     */
    @ApiModelProperty("回款审核状态：0待审核，1审核通过，2审核驳回，3撤销。")
    private Integer examineStatusType;

    /**
     * 回款审核状态：0待审核，1审核通过，2审核驳回，3撤销。
     */
    @ApiModelProperty("回款审核状态：0待审核，1审核通过，2审核驳回，3撤销。")
    private String examineStatusName;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    private String crateTime;

    /**
     * 审批时间。
     */
    @ApiModelProperty("审批时间。")
    private String examineTime;

    @ApiModelProperty("财务收款时间。")
    private String financeTime;

    @ApiModelProperty("订单编号")
    private String vcContractNumber;

    @ApiModelProperty("提单来源  1：客保提单；2：客户提单")
    private Integer commitOrderType;

    @ApiModelProperty("线索表id")
    private Long clueId;

    @ApiModelProperty("客户名称")
    private String clientContactName;

}
