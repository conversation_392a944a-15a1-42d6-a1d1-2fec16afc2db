package com.nnb.erp.service;


import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.erp.domain.ComDictRegion;
import com.nnb.erp.domain.ErpEnterprise;
import com.nnb.erp.domain.TaskOperationEntity;
import com.nnb.erp.domain.dto.TaskScheduleDto;
import com.nnb.erp.domain.vo.TaskScheduleVo;
import com.nnb.erp.domain.vo.task.*;
import net.sf.json.JSONArray;

import java.util.List;

/**
 * 任务，服务层，接口。
 *
 * <AUTHOR>
 * @date 2022-07-06 09:51:47
 */
public interface ITaskService {

    /**
     * 获取任务列表。
     *
     * @param query 搜索条件。
     * @return 返回任务列表。
     * <AUTHOR>
     * @since 2022-07-06 09:59:00
     */
    List<QueryForTaskListVO> getTaskList(QueryForTaskListDTO query);

    /**
     * 创建任务。
     *
     * @param taskDTO 待创建任务信息。
     * <AUTHOR>
     * @since 2022-07-06 10:11:42
     */
    void saveTask(TaskInfoForSaveDTO taskDTO);

    /**
     * 分配执行人。
     *
     * @param executeDTO 待分配信息。
     * <AUTHOR>
     * @since 2022-07-06 14:00:04
     */
    void setExecuteUser(TaskForSetExecuteDTO executeDTO);

    /**
     * 撤销任务。
     *
     * @param dto 任务信息。
     * <AUTHOR>
     * @since 2022-07-06 16:28:12
     */
    void undoTask(TaskForUndoDTO dto);

    /**
     * 审核任务。
     *
     * @param dto 任务信息。
     * <AUTHOR>
     * @since 2022-07-06 16:43:07
     */
    void auditTask(TaskForAuditDTO dto);

    /**
     * 分配任务。
     *
     * @param dto 任务信息。
     * <AUTHOR>
     * @since 2022-07-06 17:15:04
     */
    void allotTask(TaskForAllotDTO dto);

    /**
     * 完成任务。
     *
     * @param dto 任务信息。
     * <AUTHOR>
     * @since 2022-07-06 17:30:55
     */
    void finishTask(TaskForFinishDTO dto);

    void finishTaskSure(TaskForFinishDTO dto);

    /**
     * 获取任务详情。
     *
     * @param id         任务标识。
     * @param taskNumber 任务编号。
     * @return 返回任务详情。
     * <AUTHOR>
     * @since 2022-07-07 11:17:18
     */
    TaskInfoForDetailVO getDetail(Integer id, String taskNumber);

    /**
     * 获取任务分类树形结构。
     *
     * @return 返回树形结构。
     * <AUTHOR>
     * @since 2022-07-07 14:55:47
     */
    List<TaskTypeTreeVO> getTypeTree();

    /**
     * 保存任务分类。
     *
     * @param dto 待保存分类。
     * <AUTHOR>
     * @since 2022-07-07 15:33:26
     */
    void saveType(TaskTypeForSubmitDTO dto);

    /**
     * 修改任务分类。
     *
     * @param dto 待修改分类。
     * <AUTHOR>
     * @since 2022-07-07 15:47:01
     */
    void updType(TaskTypeForSubmitDTO dto);

    /**
     * 删除任务分类。
     *
     * @param id 待删除分类。
     * <AUTHOR>
     * @since 2022-07-07 15:51:10
     */
    void delType(Integer id);

    /**
     * 获取任务资料树。
     *
     * @return 返回树信息。
     * <AUTHOR>
     * @since 2022-07-07 16:13:18
     */
    List<TaskMaterialTreeVO> getMaterialTree();

    /**
     * 新增任务资料。
     *
     * @param dto 待新增资料。
     * <AUTHOR>
     * @since 2022-07-07 17:00:45
     */
    void saveMaterial(TaskMaterialForSaveDTO dto);

    /**
     * 修改任务资料。
     *
     * @param dto 待修改资料。
     * <AUTHOR>
     * @since 2022-07-07 17:00:45
     */
    void updMaterial(TaskMaterialForSubmitDTO dto);

    /**
     * 删除资料。
     *
     * @param id 资料标识。
     * <AUTHOR>
     * @since 2022-07-08 15:09:20
     */
    void delMaterial(Integer id);

    /**
     * 获取任务地点列表。
     *
     * @param query 查询条件。
     * @return 返回任务地点列表。
     * <AUTHOR>
     * @since 2022-07-08 17:18:40
     */
    List<QueryForAddressListVO> getAddressList(QueryForAddressListDTO query);

    int saveAddress(QueryForAddressListDTO dto);

    List<QueryForTaskListVO> getTaskListV( QueryForTaskListDTO query);

    void updAddress(QueryForAddressListDTO dto);


    List<QueryForTaskListVO> getCount(TaskForSetExecuteDTO executeDTO);

    List<ComDictRegion> getCity(Long parentId, Long level);

    List<QueryForAddressListVO> getAddress(Long areaId);

    List<QueryForTaskListVO> getCreatedBy();

    List<QueryForTaskListVO> getExecuteUserName();

    int updateStatus(QueryForAddressListDTO dto);


    List<TaskMaterialForSaveDTO> getMaterialIds(TaskMaterialTreeVO vo);

    List<ErpEnterprise> getEnterpriseId(String vcCompanyName);

    List<TaskForAllotDTO> getTaskIn(Integer userId);

    List<TaskOperationForDetailVO> getLog(Long taskId);

    TaskOperationEntity getStatus(TaskOperationEntity dto);

    int updTask(TaskInfoForSaveDTO dto);

    List<QueryForTaskListVO> selectTempTaskDepartmentList(QueryForTaskListDTO query);

    List<QueryForTaskListVO> selectTempTaskPrdinaryList(QueryForTaskListDTO query);

    List<QueryForTaskListVO> selectTempTaskDepartmentListV(QueryForTaskListDTO query);

    List<QueryForTaskListVO> selectTempTaskPrdinaryListV(QueryForTaskListDTO query);

    List<QueryForTaskListVO> selectreally(QueryForTaskListDTO query);

    List<QueryForTaskListVO> selectreallys(QueryForTaskListDTO query);

    List<TaskTypeForSubmitDTO> getType(Long parentId );

    List<TaskTypeForSubmitDTO> getTypeParentId();

    List<QueryForTaskListVO> getManageList( QueryForTaskListDTO query);

    ErpEnterprise getEnterpriseByOrderNumber(String orderNumber);

    List<TaskScheduleVo> schedule(TaskScheduleDto dto);

    JSONArray scheduleDate();

    List<QueryForTaskListVO> getManageListWithCreateUser(QueryForTaskListDTO query);

    List<QueryForTaskListVO> getManageListWithFollowUser(QueryForTaskListDTO query);

    QueryForTaskListVO verifyOrderNumber(TaskInfoForSaveDTO taskDTO);
}
