package com.nnb.erp.service.impl.order;

import com.nnb.common.core.utils.DateUtils;
import com.nnb.erp.domain.order.ErpOrderRefundCostExpenditure;
import com.nnb.erp.mapper.order.ErpOrderRefundCostExpenditureMapper;
import com.nnb.erp.service.order.IErpOrderRefundCostExpenditureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单退款成本支出Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
@Service
public class ErpOrderRefundCostExpenditureServiceImpl implements IErpOrderRefundCostExpenditureService
{
    @Autowired
    private ErpOrderRefundCostExpenditureMapper erpOrderRefundCostExpenditureMapper;

    /**
     * 查询订单退款成本支出
     * 
     * @param id 订单退款成本支出主键
     * @return 订单退款成本支出
     */
    @Override
    public ErpOrderRefundCostExpenditure selectErpOrderRefundCostExpenditureById(Long id)
    {
        return erpOrderRefundCostExpenditureMapper.selectErpOrderRefundCostExpenditureById(id);
    }

    /**
     * 查询订单退款成本支出列表
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 订单退款成本支出
     */
    @Override
    public List<ErpOrderRefundCostExpenditure> selectErpOrderRefundCostExpenditureList(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure)
    {
        return erpOrderRefundCostExpenditureMapper.selectErpOrderRefundCostExpenditureList(erpOrderRefundCostExpenditure);
    }

    /**
     * 新增订单退款成本支出
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 结果
     */
    @Override
    public int insertErpOrderRefundCostExpenditure(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure)
    {
        erpOrderRefundCostExpenditure.setCreateTime(DateUtils.getNowDate());
        return erpOrderRefundCostExpenditureMapper.insertErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditure);
    }

    /**
     * 修改订单退款成本支出
     * 
     * @param erpOrderRefundCostExpenditure 订单退款成本支出
     * @return 结果
     */
    @Override
    public int updateErpOrderRefundCostExpenditure(ErpOrderRefundCostExpenditure erpOrderRefundCostExpenditure)
    {
        erpOrderRefundCostExpenditure.setUpdateTime(DateUtils.getNowDate());
        return erpOrderRefundCostExpenditureMapper.updateErpOrderRefundCostExpenditure(erpOrderRefundCostExpenditure);
    }

    /**
     * 批量删除订单退款成本支出
     * 
     * @param ids 需要删除的订单退款成本支出主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderRefundCostExpenditureByIds(Long[] ids)
    {
        return erpOrderRefundCostExpenditureMapper.deleteErpOrderRefundCostExpenditureByIds(ids);
    }

    /**
     * 删除订单退款成本支出信息
     * 
     * @param id 订单退款成本支出主键
     * @return 结果
     */
    @Override
    public int deleteErpOrderRefundCostExpenditureById(Long id)
    {
        return erpOrderRefundCostExpenditureMapper.deleteErpOrderRefundCostExpenditureById(id);
    }
}
