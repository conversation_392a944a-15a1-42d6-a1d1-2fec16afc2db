package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 服务类型对象 erp_product_service
 * 
 * <AUTHOR>
 * @date 2021-12-27
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ErpProductService extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long numServiceId;

    /** 服务类型 */
    @Excel(name = "服务类型")
    @NotBlank(message = "服务类型不能为空")
    @Size(min = 0, max = 20, message = "服务类型名称长度不能超过20个字符")
    private String vcServiceName;

    /** 状态：0未启用，1启用 */
    @Excel(name = "状态：0未启用，1启用")
    private Long numState;

    /** 创建人 */
    private Long numCreateUserid;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datCreateTime;

    /** 最后修改人 */
    private Long numLastUpdUserid;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datLastUpd;

    public void setNumServiceId(Long numServiceId) 
    {
        this.numServiceId = numServiceId;
    }

    public Long getNumServiceId() 
    {
        return numServiceId;
    }
    public void setVcServiceName(String vcServiceName) 
    {
        this.vcServiceName = vcServiceName;
    }

    public String getVcServiceName() 
    {
        return vcServiceName;
    }
    public void setNumState(Long numState) 
    {
        this.numState = numState;
    }

    public Long getNumState() 
    {
        return numState;
    }
    public void setNumCreateUserid(Long numCreateUserid) 
    {
        this.numCreateUserid = numCreateUserid;
    }

    public Long getNumCreateUserid() 
    {
        return numCreateUserid;
    }
    public void setDatCreateTime(Date datCreateTime) 
    {
        this.datCreateTime = datCreateTime;
    }

    public Date getDatCreateTime() 
    {
        return datCreateTime;
    }
    public void setNumLastUpdUserid(Long numLastUpdUserid) 
    {
        this.numLastUpdUserid = numLastUpdUserid;
    }

    public Long getNumLastUpdUserid() 
    {
        return numLastUpdUserid;
    }
    public void setDatLastUpd(Date datLastUpd) 
    {
        this.datLastUpd = datLastUpd;
    }

    public Date getDatLastUpd() 
    {
        return datLastUpd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("numServiceId", getNumServiceId())
            .append("vcServiceName", getVcServiceName())
            .append("numState", getNumState())
            .append("numCreateUserid", getNumCreateUserid())
            .append("datCreateTime", getDatCreateTime())
            .append("numLastUpdUserid", getNumLastUpdUserid())
            .append("datLastUpd", getDatLastUpd())
            .toString();
    }
}
