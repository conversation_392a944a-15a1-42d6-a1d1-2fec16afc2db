package com.nnb.erp.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 记账客户意向对象 erp_customer_intention
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@ApiModel(value="ErpCustomerIntention",description="记账客户意向对象")
public class ErpCustomerIntention extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** 客户id */
    @Excel(name = "客户id")
    @ApiModelProperty("客户id")
    private Long clientId;

    /** 意向来源 */
    @Excel(name = "意向来源")
    @ApiModelProperty("意向来源")
    private Integer intentionSource;

    /** 意向程度 */
    @Excel(name = "意向程度")
    @ApiModelProperty("意向程度")
    private Long intentionDegree;

    /** 意向程度 */
    @Excel(name = "客户意向配置id")
    @ApiModelProperty("客户意向配置id")
    private Long intentionConfigId;

    /** 跟进状态 */
    @Excel(name = "跟进状态")
    @ApiModelProperty("跟进状态")
    private Integer followStatus;

    /** 预计成交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计成交时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("预计成交时间")
    private Date dealDate;

    /** 预计成交金额 */
    @Excel(name = "预计成交金额")
    @ApiModelProperty("预计成交金额")
    private BigDecimal dealFee;

    /** 跟进记录 */
    @Excel(name = "跟进记录")
    @ApiModelProperty("跟进记录")
    private String memo;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createdUser;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /** 会计 */
    @Excel(name = "会计")
    @ApiModelProperty("会计")
    private Long accountUser;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setClientId(Long clientId)
    {
        this.clientId = clientId;
    }

    public Long getClientId()
    {
        return clientId;
    }
    public void setIntentionSource(Integer intentionSource)
    {
        this.intentionSource = intentionSource;
    }

    public Integer getIntentionSource()
    {
        return intentionSource;
    }
    public void setIntentionDegree(Long intentionDegree)
    {
        this.intentionDegree = intentionDegree;
    }

    public Long getIntentionDegree()
    {
        return intentionDegree;
    }
    public void setFollowStatus(Integer followStatus)
    {
        this.followStatus = followStatus;
    }

    public Long getIntentionConfigId() {
        return intentionConfigId;
    }
    public void setIntentionConfigId(Long intentionConfigId) {
        this.intentionConfigId = intentionConfigId;
    }

    public Integer getFollowStatus()
    {
        return followStatus;
    }
    public void setDealDate(Date dealDate)
    {
        this.dealDate = dealDate;
    }

    public Date getDealDate()
    {
        return dealDate;
    }
    public void setDealFee(BigDecimal dealFee)
    {
        this.dealFee = dealFee;
    }

    public BigDecimal getDealFee()
    {
        return dealFee;
    }
    public void setMemo(String memo)
    {
        this.memo = memo;
    }

    public String getMemo()
    {
        return memo;
    }
    public void setCreatedUser(Long createdUser)
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser()
    {
        return createdUser;
    }
    public void setCreatedTime(Date createdTime)
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime()
    {
        return createdTime;
    }
    public void setAccountUser(Long accountUser)
    {
        this.accountUser = accountUser;
    }

    public Long getAccountUser()
    {
        return accountUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clientId", getClientId())
            .append("intentionSource", getIntentionSource())
            .append("intentionDegree", getIntentionDegree())
            .append("followStatus", getFollowStatus())
            .append("dealDate", getDealDate())
            .append("dealFee", getDealFee())
            .append("memo", getMemo())
            .append("createdUser", getCreatedUser())
            .append("createdTime", getCreatedTime())
            .append("accountUser", getAccountUser())
            .toString();
    }
}
