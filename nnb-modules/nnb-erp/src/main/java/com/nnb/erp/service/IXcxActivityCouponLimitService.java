package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.XcxActivityCouponLimit;

/**
 * 小程序活动/优惠券配置与优惠额度关联Service接口
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
public interface IXcxActivityCouponLimitService
{
    /**
     * 查询小程序活动/优惠券配置与优惠额度关联
     *
     * @param id 小程序活动/优惠券配置与优惠额度关联主键
     * @return 小程序活动/优惠券配置与优惠额度关联
     */
    public XcxActivityCouponLimit selectXcxActivityCouponLimitById(Long id);

    /**
     * 查询小程序活动/优惠券配置与优惠额度关联列表
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 小程序活动/优惠券配置与优惠额度关联集合
     */
    public List<XcxActivityCouponLimit> selectXcxActivityCouponLimitList(XcxActivityCouponLimit xcxActivityCouponLimit);

    /**
     * 新增小程序活动/优惠券配置与优惠额度关联
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 结果
     */
    public int insertXcxActivityCouponLimit(XcxActivityCouponLimit xcxActivityCouponLimit);

    /**
     * 修改小程序活动/优惠券配置与优惠额度关联
     *
     * @param xcxActivityCouponLimit 小程序活动/优惠券配置与优惠额度关联
     * @return 结果
     */
    public int updateXcxActivityCouponLimit(XcxActivityCouponLimit xcxActivityCouponLimit);

    /**
     * 批量删除小程序活动/优惠券配置与优惠额度关联
     *
     * @param ids 需要删除的小程序活动/优惠券配置与优惠额度关联主键集合
     * @return 结果
     */
    public int deleteXcxActivityCouponLimitByIds(Long[] ids);

    /**
     * 删除小程序活动/优惠券配置与优惠额度关联信息
     *
     * @param id 小程序活动/优惠券配置与优惠额度关联主键
     * @return 结果
     */
    public int deleteXcxActivityCouponLimitById(Long id);
}
