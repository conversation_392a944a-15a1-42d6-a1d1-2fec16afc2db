package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpBizNodeStatus;
import com.nnb.erp.domain.vo.BizStatusTree;
import com.nnb.erp.domain.vo.ErpBizNodeStatusEditDto;

/**
 * 服务单状态Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-30
 */
public interface IErpBizNodeStatusService 
{
    /**
     * 查询服务单状态
     * 
     * @param numId 服务单状态主键
     * @return 服务单状态
     */
    public ErpBizNodeStatus selectErpBizNodeStatusByNumId(Long numId);

    /**
     * 查询服务单状态列表
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 服务单状态集合
     */
    public List<ErpBizNodeStatus> selectErpBizNodeStatusList(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 新增服务单状态
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 结果
     */
    public int insertErpBizNodeStatus(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 修改服务单状态
     * 
     * @param erpBizNodeStatus 服务单状态
     * @return 结果
     */
    public int updateErpBizNodeStatus(ErpBizNodeStatus erpBizNodeStatus);

    /**
     * 批量删除服务单状态
     * 
     * @param numIds 需要删除的服务单状态主键集合
     * @return 结果
     */
    public int deleteErpBizNodeStatusByNumIds(Long[] numIds);

    /**
     * 删除服务单状态信息
     * 
     * @param numId 服务单状态主键
     * @return 结果
     */
    public int deleteErpBizNodeStatusByNumId(Long numId);

    /**
     * 服务单新增状态信息-初始化流程状态
     * @param infoId 服务单id
     * @param infoId 菜单id
     * @return
     */
    public Long insertNodeByInfoId(Long infoId,Long menuId);

    /**
     * 状态标记/节点流转
     * @param dto
     * @return
     */
    public int editNodeStatus(ErpBizNodeStatusEditDto dto);

    /**
     * 查询注册订单明细管理—服务进度
     * @return
     */
    public List<BizStatusTree> bizStatusTreeList();
}
