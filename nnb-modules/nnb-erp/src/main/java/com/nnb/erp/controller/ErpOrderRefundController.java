package com.nnb.erp.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.AccountLossReason;
import com.nnb.erp.domain.ErpOrderRefundReason;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpOrderRefund;
import com.nnb.erp.service.IErpOrderRefundService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 订单退款Controller
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@RestController
@RequestMapping("/erpOrderRefund")
@Api(tags = "ErpOrderRefundController", description = "订单退款")
public class ErpOrderRefundController extends BaseController
{
    @Autowired
    private IErpOrderRefundService erpOrderRefundService;

    /**
     * 查询订单退款列表
     */
    @ApiOperation(value = "查询订单退款列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpOrderRefund.class)})
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ErpOrderRefund erpOrderRefund)
    {
        startPage();
        List<ErpOrderRefund> list = erpOrderRefundService.selectErpOrderRefundList(erpOrderRefund);
        return getDataTable(list);
    }

    /**
     * 导出订单退款列表
     */
    @ApiOperation(value = "导出订单退款列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpOrderRefund erpOrderRefund) throws IOException
    {
        List<ErpOrderRefund> list = erpOrderRefundService.selectErpOrderRefundList(erpOrderRefund);
        ExcelUtil<ErpOrderRefund> util = new ExcelUtil<ErpOrderRefund>(ErpOrderRefund.class);
        util.exportExcel(response, list, "订单退款数据");
    }

    /**
     * 获取订单退款详细信息
     */
    @ApiOperation(value = "获取订单退款详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpOrderRefund.class)})
    @PostMapping(value = "/getInfo")
    public AjaxResult getInfo(@RequestBody ErpOrderRefund erpOrderRefund)
    {
        return AjaxResult.success(erpOrderRefundService.selectErpOrderRefundById(erpOrderRefund.getId()));
    }

    /**
     * 新增订单退款
     */
    @ApiOperation(value = "新增订单退款")
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody ErpOrderRefund erpOrderRefund)
    {
        return toAjax(erpOrderRefundService.insertErpOrderRefund(erpOrderRefund));
    }

    /**
     * 修改订单退款
     */
    @ApiOperation(value = "修改订单退款")
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody ErpOrderRefund erpOrderRefund)
    {
        return toAjax(erpOrderRefundService.updateErpOrderRefund(erpOrderRefund));
    }

    /**
     * 删除订单退款
     */
    @ApiOperation(value = "删除订单退款")
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody ErpOrderRefund erpOrderRefund)
    {
        return toAjax(erpOrderRefundService.deleteErpOrderRefundByIds(erpOrderRefund.getIds()));
    }

    @GetMapping("/getRefundReason")
    @ApiOperation("获取退款原因列表。")
    public TableDataInfo getRefundReason() {
        startPage();
        List<ErpOrderRefundReason> list = erpOrderRefundService.getRefundReason();
        return getDataTable(list);
    }
}
