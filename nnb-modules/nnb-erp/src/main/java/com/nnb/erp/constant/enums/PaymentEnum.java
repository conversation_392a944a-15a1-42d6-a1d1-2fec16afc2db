package com.nnb.erp.constant.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付方式，枚举。
 *
 * <AUTHOR>
 * @since 2022-03-28 11:49:24
 */
public enum PaymentEnum {

    /*支付方式：
    +───────────────+─────────────+──────────────────+
    | enum        	| paymentType | paymentName      |
    +───────────────+─────────────+──────────────────+
    | CASH  		| 1           | 现金              |
    | SWIPING_CARD  | 2           | 刷卡              |
    | SCAN_CODE     | 3           | 扫码              |
    | TRANSFER      | 4           | 转账              |
    | ONLINE    	| 5           | 微信、支付宝在线支付 |
    +───────────────+─────────────+──────────────────+
    */

    CASH(1, "现金"),
    SWIPING_CARD(2, "刷卡"),
    SCAN_CODE(3, "扫码"),
    T<PERSON><PERSON>F<PERSON>(4, "转账"),
    ONLINE(5, "微信、支付宝在线支付");


    private final Integer paymentType;
    private final String paymentName;

    public Integer getPaymentType() {
        return paymentType;
    }

    public String getPaymentName() {
        return paymentName;
    }

    PaymentEnum(Integer paymentType, String paymentName) {
        this.paymentType = paymentType;
        this.paymentName = paymentName;
    }

    public static String getNameByType(Integer paymentType) {
        List<PaymentEnum> resList = Arrays.stream(PaymentEnum.values()).filter(e -> e.getPaymentType().equals(paymentType)).collect(Collectors.toList());
        if (resList.size() == 0) {
            return paymentType + "";
        } else {
            return resList.get(0).getPaymentName();
        }
    }

}
