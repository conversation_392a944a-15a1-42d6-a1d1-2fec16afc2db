package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.nnb.erp.domain.ErpPromotionalActivities;
import com.nnb.erp.domain.dto.ErpPromotionalActivitiesDto;
import com.nnb.erp.domain.vo.ErpPromotionalActivitiesVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface ErpPromotionalActivitiesMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpPromotionalActivities selectErpPromotionalActivitiesById(Integer id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param erpPromotionalActivities 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpPromotionalActivitiesVo> selectErpPromotionalActivitiesList(ErpPromotionalActivitiesDto dto);

    /**
     * 新增【请填写功能名称】
     *
     * @param erpPromotionalActivities 【请填写功能名称】
     * @return 结果
     */
    public int insertErpPromotionalActivities(ErpPromotionalActivities erpPromotionalActivities);

    /**
     * 修改【请填写功能名称】
     *
     * @param erpPromotionalActivities 【请填写功能名称】
     * @return 结果
     */
    public int updateErpPromotionalActivities(ErpPromotionalActivities erpPromotionalActivities);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesById(Integer id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpPromotionalActivitiesByIds(Integer[] ids);

    @Select("SELECT * FROM erp_promotional_activities WHERE full_price <= #{fee} AND FIND_IN_SET(#{deptId},dept_ids) AND status = 1")
    public List<ErpPromotionalActivities> reportGetActivitiesList(@Param("fee") BigDecimal fee, @Param("deptId") Long deptId);

    List<ErpPromotionalActivities> selectShouldStopActivities();

    List<Long> selectErpPayRecordId(@Param("id") Long id);
}
