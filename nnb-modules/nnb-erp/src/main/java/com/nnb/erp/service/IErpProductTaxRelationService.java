package com.nnb.erp.service;

import com.nnb.erp.domain.vo.ErpProductTaxProduct;

import java.util.List;

/**
 * 纳税类型Service接口
 *
 * <AUTHOR>
 * @date 2021-12-29
 */
public interface IErpProductTaxRelationService {

    public List<ErpProductTaxProduct> selectTaxByProductId(Long numProductId);

    public int insertRelation(Long productId, Long productTaxid);

    public int delRelationByproductId(Long productId);
}
