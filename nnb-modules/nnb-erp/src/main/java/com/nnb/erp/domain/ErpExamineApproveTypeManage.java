package com.nnb.erp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 执照类型配置对象 erp_examine_approve_type_manage
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
@ApiModel(value="ErpExamineApproveTypeManage",description="执照类型配置对象")
public class ErpExamineApproveTypeManage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 审批类型 */
    @Excel(name = "审批类型")
    @ApiModelProperty("审批类型")
    private String name;

    /** 是否有效1.有效0.无效 */
    @Excel(name = "是否有效1.有效2.无效")
    @ApiModelProperty("是否有效1.有效2.无效")
    private Integer status;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createdUser;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    /** 父级Id，第一级为0 */
    @Excel(name = "父级Id，第一级为0")
    @ApiModelProperty("父级Id，第一级为0")
    private Long parentId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String memo;

    /** 层级 */
    @Excel(name = "层级")
    @ApiModelProperty("层级")
    private Integer typeLevel;

    /** 是否需要打印1是0否 */
    @Excel(name = "是否需要打印1是2否")
    @ApiModelProperty("是否需要打印1是2否")
    private Integer needPrint;

    /** 是否需要付款1是0否 */
    @Excel(name = "是否需要付款1是2否")
    @ApiModelProperty("是否需要付款1是2否")
    private Integer needPay;

    @Excel(name = "是否需要付款1是2否")
    @ApiModelProperty("是否需要付款1是2否")
    private Integer budgetId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setCreatedUser(Long createdUser)
    {
        this.createdUser = createdUser;
    }

    public Long getCreatedUser()
    {
        return createdUser;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdateUser(Long updateUser)
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser()
    {
        return updateUser;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setTypeLevel(Integer typeLevel) 
    {
        this.typeLevel = typeLevel;
    }

    public Integer getTypeLevel() 
    {
        return typeLevel;
    }
    public void setNeedPrint(Integer needPrint) 
    {
        this.needPrint = needPrint;
    }

    public Integer getNeedPrint() 
    {
        return needPrint;
    }
    public void setNeedPay(Integer needPay) 
    {
        this.needPay = needPay;
    }

    public Integer getNeedPay() 
    {
        return needPay;
    }

    public Integer getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(Integer budgetId) {
        this.budgetId = budgetId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("status", getStatus())
            .append("createdUser", getCreatedUser())
            .append("createdTime", getCreatedTime())
            .append("updateUser", getUpdateUser())
            .append("updateTime", getUpdateTime())
            .append("parentId", getParentId())
            .append("memo", getMemo())
            .append("typeLevel", getTypeLevel())
            .append("needPrint", getNeedPrint())
            .append("needPay", getNeedPay())
            .toString();
    }
}
