package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 回访信息，实体。
 *
 * <AUTHOR>
 * @since 2022/7/4 13:20
 */
@Data
public class VisitBacksEntity {

    /**
     * 回访标识。
     */
    @ApiModelProperty("回访标识。")
    private Integer id;

    /**
     * 订单标识。
     */
    @ApiModelProperty("订单标识。")
    private Integer orderId;

    /**
     * 订单产品标识。
     */
    @ApiModelProperty("订单产品标识。")
    private Integer serviceOrderId;

    /**
     * 服务单标识或外勤任务标识。
     */
    @ApiModelProperty("服务单标识或外勤任务标识。")
    private Integer bizId;

    /**
     * 客户标识。
     */
    @ApiModelProperty("客户标识。")
    private Integer clientId;

    /**
     * 回访类型：1-销售回访；2-业支回访；3-增值回访；4-外勤回访；5-会计回访；6-工商回访；
     */
    @ApiModelProperty("回访类型：1-销售回访；2-业支回访；3-增值回访；4-外勤回访；5-会计回访；6-工商回访；")
    private Integer backType;

    /**
     * 是否有效回访：1-是；2-否。
     */
    @ApiModelProperty("是否有效回访：1-是；2-否。")
    private Integer status;

    /**
     * 评分。
     */
    @ApiModelProperty("评分。")
    private Integer number;

    /**
     * 备注。
     */
    @ApiModelProperty("备注。")
    private String remark;

    /**
     * 创建人。
     */
    @ApiModelProperty("创建人。")
    private Integer createdBy;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 更新人。
     */
    @ApiModelProperty("更新人。")
    private Integer updateBy;

    /**
     * 更新时间。
     */
    @ApiModelProperty("更新时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty("企业id")
    private Integer enterpriseId;

    @ApiModelProperty("自定义服务类型")
    private Integer serviceType;

}
