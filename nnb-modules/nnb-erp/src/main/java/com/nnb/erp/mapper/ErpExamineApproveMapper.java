package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.nnb.erp.domain.ApprovalsDetail;
import com.nnb.erp.domain.AreaCity;
import com.nnb.erp.domain.ErpExamineApprove;
import com.nnb.erp.domain.dto.ErpExamineApproveKPDto;
import com.nnb.erp.domain.dto.ErpExamineBudgetDto;
import com.nnb.erp.domain.dto.approval.ErpExamineApproveQueryDTO;
import com.nnb.erp.domain.generalapproval.AdvancePaymentVo;
import com.nnb.erp.domain.generalapproval.AppliedOrder;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.approval.ErpExamineApproveListVO;
import com.nnb.erp.domain.vo.approval.ErpExamineApproveVO;
import com.nnb.erp.domain.vo.approval.ErpOrderRefundDetailVo;
import com.nnb.system.api.domain.SysDept;
import org.apache.ibatis.annotations.Param;
import com.nnb.erp.domain.dto.ErpExamineApproveDTO;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface ErpExamineApproveMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpExamineApprove selectErpExamineApproveById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpExamineApprove 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpExamineApprove> selectErpExamineApproveList(ErpExamineApproveDTO erpExamineApproveDTO);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpExamineApprove 【请填写功能名称】
     * @return 结果
     */
    public int insertErpExamineApprove(ErpExamineApprove erpExamineApprove);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpExamineApprove 【请填写功能名称】
     * @return 结果
     */
    public int updateErpExamineApprove(ErpExamineApprove erpExamineApprove);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpExamineApproveById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpExamineApproveByIds(Long[] ids);

    public int countErpExamineApprove(@Param("licenseId") Long licenseId, @Param("approveStatusList") List<Integer> approveStatusList,@Param("approveTypes")List<Integer> approveTypes);

    public List<ErpExamineApproveVO> selectErpExamineApproveVOList(ErpExamineApproveQueryDTO erpExamineApproveQueryDTO);


    /***
     * 根据审批类型leftjoin不同的表，获取审批的通用方法
     * @param erpExamineApproveDTO
     * @return
     */
//    public List<ErpExamineApprove> selectListPublic(ErpExamineApproveDTO erpExamineApproveDTO);

    public List<AppliedOrder> getAppliedOrder(@Param("serviceMainIds") List<Long> serviceMainIds, @Param("approveType") Integer approveType);

    public List<AppliedOrder> getLicenseAppliedOrder(@Param("licenseId") Long licenseId, @Param("approveType") Integer approveType);

    public ApprovalsDetail getProductDetail(ErpExamineApprove erpExamineApprove);

    List<AreaCity> getProductAreaCity(Long approveId);

    AdvancePaymentVo getAdvancePaymentVo(Long approveId);

    ErpExamineOtherOrderPayVo getOtherOrderPayVo(Long approveId);

    ErpExamineCommonPaymentVo getCommonPaymentVo(Long approveId);

    ErpExamineNoOrderRefoundVo getNoOrderRefoundInfo(Long approveId);

    public List<ErpExamineApproveListVO> selectListPublic(ErpExamineApproveDTO erpExamineApproveDTO);

    public List<ErpExamineApproveListVO> budgetList(ErpExamineBudgetDto dto);

    public List<ErpExamineApproveKPVo> approveListKP(ErpExamineApproveKPDto erpExamineApproveDTO);

    public List<ErpExamineApproveListVO> getApproveByIds(@Param("ids") List<Long> ids);

    List<ErpExamineApprove> selectOtherIdByIdsWithApproveParent(@Param("ids") List<Long> ids, @Param("parentId") Integer parentId);

    public ErpExamineReportOrderVo getReportOrderInfo(Long approveId);

    public ErpOrderInvoiceVo getOrderInvoiceInfo(Long approveId);

    public ErpAccountDateUpdateVo getAccountUpdateInfo(Long approveId);

    public ErpExamineAccountLsVo getJzLsInfo(Long approveId);

    public ErpExamineBudgetVo getBudgetInfo(Long approveId);

    ErpOrderRefundDetailVo getErpOrderRefundDetail(Long approveId);

    @Select("select SUM(eor.refund_amount) from erp_order_refund eor left join erp_examine_approve eea on eea.other_id = eor.id left join erp_examine_approve_type_manage eeatm " +
            "on eea.approve_type = eeatm.id where eor.order_id = #{orderId} and eeatm.parent_id = 8 and eeatm.status = 1 and eea.approve_status = 1")
    BigDecimal getRefunded(Long orderId);

    @Select("select SUM(eorce.finance_confirm_amount) from erp_order_refund eor left join erp_examine_approve eea on eea.other_id = eor.id left join erp_examine_approve_type_manage eeatm " +
            "on eea.approve_type = eeatm.id left join erp_order_refund_cost_expenditure eorce on eorce.approve_id = eea.id where eor.order_id = #{orderId} and eeatm.parent_id = 8 and eeatm.status = 1 and eea.approve_status = 1")
    BigDecimal getFinanceRefunded(Long orderId);

    @Select("select eorce.finance_confirm_amount from erp_order_refund eor left join erp_examine_approve eea on eea.other_id = eor.id left join erp_examine_approve_type_manage eeatm " +
            "on eea.approve_type = eeatm.id left join erp_order_refund_cost_expenditure eorce on eorce.approve_id = eea.id where eor.order_id = #{orderId} and eeatm.parent_id = 8 and eeatm.status = 1 and eea.approve_status = 1 ORDER BY eorce.create_time DESC LIMIT 1")
    BigDecimal getLastFinanceRefunded(Long orderId);

    @Select("select * from sys_dept where parent_id = 0")
    List<SysDept> selectFirstDeptId();

    @Update("UPDATE erp_examine_approve SET revoke_status = null WHERE id = #{id}")
    int updateRevokeStatusById(@Param("id") Long id);

    @Select("SELECT * FROM erp_examine_approve eea LEFT JOIN cost_settlement cs ON cs.id = eea.other_id WHERE cs.register_ids = #{otherId} AND eea.approve_type = #{approveType} AND eea.approve_status IN (0,1)")
    List<ErpExamineApprove> selectListByOtherIdAndType(@Param("otherId") String otherId, @Param("approveType") Long approveType);

    @Select("SELECT eea.id AS id, ssm.product_id AS productId, su.nick_name AS createdUserName, sd.dept_name AS createdUserDeptName, eea.created_date AS createdDate " +
            "FROM erp_examine_approve eea " +
            "LEFT JOIN erp_examine_approve_type_manage eeatm ON eea.approve_type = eeatm.id " +
            "LEFT JOIN cost_settlement cs ON cs.id = eea.other_id " +
            "LEFT JOIN s_service_main ssm ON ssm.id = cs.register_ids " +
            "LEFT JOIN sys_user su ON su.user_id = eea.created_user " +
            "LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id " +
            "WHERE eea.approve_status = 0  AND eeatm.parent_id = 1  AND cs.order_numbers = #{orderNum}")
    List<ErpExamineApproveListVO> getInApproveList(@Param("orderNum") String orderNum);
}
