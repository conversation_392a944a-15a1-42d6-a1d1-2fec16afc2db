package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 课程分类，实体。
 *
 * <AUTHOR>
 * @since 2022-06-21 14:02:28
 */
@Data
public class CourseTypeEntity {

    /**
     * 主键。
     */
    @ApiModelProperty("主键。")
    private Integer id;

    /**
     * 分类级别：1-一级分类；2-二级分类；3-三级分类。
     */
    @ApiModelProperty("分类级别：1-一级分类；2-二级分类；3-三级分类。")
    private Integer typeLevel;

    /**
     * 一级分类Id
     */
    @ApiModelProperty("一级分类Id")
    private Integer firstId;

    /**
     * 一级分类名称。
     */
    @ApiModelProperty("一级分类名称。")
    private String firstName;

    /**
     * 二级分类Id
     */
    @ApiModelProperty("二级分类Id")
    private Integer secondId;

    /**
     * 二级分类名称。
     */
    @ApiModelProperty("二级分类名称。")
    private String secondName;

    /**
     * 三级分类Id
     */
    @ApiModelProperty("三级分类Id")
    private Integer thirdId;

    /**
     * 三级分类名称。
     */
    @ApiModelProperty("三级分类名称。")
    private String thirdName;

    /**
     * 分类排序，默认为0，升序查询。
     */
    @ApiModelProperty("分类排序，默认为0，升序查询。")
    private Integer sort;

    /**
     * 创建人。
     */
    @ApiModelProperty("创建人。")
    private Integer createdBy;

    /**
     * 创建时间。
     */
    @ApiModelProperty("创建时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 更新人。
     */
    @ApiModelProperty("更新人。")
    private Integer updateBy;

    /**
     * 更新时间。
     */
    @ApiModelProperty("更新时间。")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
