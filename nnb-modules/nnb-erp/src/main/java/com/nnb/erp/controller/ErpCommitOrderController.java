package com.nnb.erp.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.erp.domain.ErpCombinedActivity;
import com.nnb.erp.domain.ErpOrders;
import com.nnb.erp.domain.gift.ErpOrderGift;
import com.nnb.erp.domain.gift.ErpOrderGiftLog;
import com.nnb.erp.domain.vo.ErpClientForCommitOrderDTO;
import com.nnb.erp.domain.vo.ErpClientForConfirmOrderDTO;
import com.nnb.erp.domain.vo.gift.ErpGiftVO;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftDetailVO;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftIssueDetailVO;
import com.nnb.erp.domain.xcx.XcxCommitOrderDto;
import com.nnb.erp.mapper.ErpOrdersMapper;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftLogMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftMapper;
import com.nnb.erp.service.IErpCommitOrderService;
import com.nnb.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 提单，控制器。
 *
 * <AUTHOR>
 * @since 2022/3/15 16:15
 */
@RestController
@RequestMapping("/ErpCommitOrder")
@Api(tags = "ErpCommitOrderController", description = "提单。")
public class ErpCommitOrderController {

    @Resource
    private IErpCommitOrderService commitOrderService;
    @Resource
    private ErpOrdersMapper erpOrdersMapper;
    @Resource
    private ErpOrderGiftMapper erpOrderGiftMapper;
    @Resource
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;
    @Resource
    private ErpOrderGiftLogMapper erpOrderGiftLogMapper;

    private static Logger logger = LoggerFactory.getLogger(ErpCallVoiceController.class);
    // 确认订单。
    // 根据指定产品ID集合获取该组产品适合的优惠方案。
    // 1. 获取指定产品集合的产品基本信息。
    // 2. 获取符合指定产品组的组合优惠（适用渠道、活动类型？、开始使用时间，结束使用时间）。
    // 3. 获取指定产品可用产品优惠券（适用渠道、数量、开始使用时间、结束使用时间、优惠券是否上架、产品优惠券、分类优惠券、部门优惠券）。
    /**
     * 确认订单。
     *
     * @param confirmOrderDTO 待确认产品信息。
     * @return 返回产品信息及相关优惠信息。
     * <AUTHOR>
     * @since 2022-03-15 17:17:13
     */
    @PostMapping("/confirmOrder")
    @ApiOperation("确认订单。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clientCity", value = "客户城市/区域ID。", required = true),
            @ApiImplicitParam(name = "clientTax", value = "纳税类型ID。", required = true),
            @ApiImplicitParam(name = "products", value = "产品信息，包含产品ID与购买数量。", required = true)
    })
    public AjaxResult confirmOrder(@RequestBody ErpClientForConfirmOrderDTO confirmOrderDTO) {
        confirmOrderDTO.setSource(ObjectUtil.isNotNull(confirmOrderDTO.getSource()) ? confirmOrderDTO.getSource() : 0);
        return AjaxResult.success(commitOrderService.confirmOrder(confirmOrderDTO));
    }

    // 提交订单。
    // 校验订单优惠方案是否合理。
    // 校验订单金额是否正确。
    /**
     * 提交订单。
     *
     * @param erpClientForCommitOrderDTO 待提交订单信息。
     * @return 返回提交结果。
     * <AUTHOR>
     * @since 2022-03-22 13:19:29
     */
    @PostMapping(value = "/commitOrder", produces = "application/json; charset=utf-8")
    public AjaxResult commitOrder(@RequestBody ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, HttpServletResponse response) {
        erpClientForCommitOrderDTO.setSource(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getSource()) ? erpClientForCommitOrderDTO.getSource() : 0);
        return AjaxResult.success(commitOrderService.commitOrder(erpClientForCommitOrderDTO, response));
    }

    @GetMapping("/listAll")
    public AjaxResult listActivityName(ErpCombinedActivity erpCombinedActivity){
      return AjaxResult.success(commitOrderService.listActivityName(erpCombinedActivity));
    }

    // 提交订单。
    // 校验订单优惠方案是否合理。
    // 校验订单金额是否正确。

    /***
     * 小程序提单
     * @param dto
     * @param response
     * @return
     */
    @PostMapping(value = "/commitOrderFromXcx", produces = "application/json; charset=utf-8")
    public AjaxResult commitOrderFromXcx(@RequestBody XcxCommitOrderDto dto, HttpServletResponse response) {
        return AjaxResult.success(commitOrderService.commitOrderFromXcx(dto, response));
    }

    @GetMapping("/reduceGiftRecordAmount")
    public void reduceGiftRecordAmount(){
         commitOrderService.reduceGiftRecordAmount();
    }

    @PostMapping("/mateGiftByProductIds")
    public List<ErpGiftVO> mateGiftByProductIds(@RequestBody JSONObject obj) {
        JSONArray arr = obj.getJSONArray("productIdList");
        List<Long> productIdList = new ArrayList<>();
        for (int i = 0; i < arr.size(); i++) {
            productIdList.add(arr.getLong(i));
        }
        return commitOrderService.mateGiftByProductIds(obj.getLong("deptId"), productIdList, new BigDecimal(obj.getString("price")));
    }
    @PostMapping(value = "/bindVoucherFollow", produces = "application/json; charset=utf-8")
    public AjaxResult bindVoucherFollow(@RequestBody ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, HttpServletResponse response) {
        return AjaxResult.success(commitOrderService.bindVoucherFollow(erpClientForCommitOrderDTO, response));

    }

}
