package com.nnb.erp.domain.dto.approval;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ErpNewApprovalDTO {

    /** 类型 */
    @ApiModelProperty("类型")
    private Integer type;

    /** 当前审批部门ID */
    @ApiModelProperty("当前审批部门ID")
    private Long deptId;

    /** 当前审批人ID */
    @ApiModelProperty("当前审批人ID")
    private Long userId;

    /** 当前审批节点 */
    @ApiModelProperty("当前审批节点")
    private Long node;

    /** 审批状态，0：未审批，1：审批中，2：已驳回，3：已撤销，4：审批通过 */
    @ApiModelProperty("审批状态，0：未审批，1：审批中，2：已驳回，3：已撤销，4：审批通过")
    private Integer status;

    /** 业务ID，订单模块：订单ID */
    @ApiModelProperty("业务ID，订单模块：订单ID")
    private Long otherId;

    /** 审批json */
    @ApiModelProperty("审批json")
    private String approvalJson;
}
