package com.nnb.erp.service.inventory;


import com.nnb.erp.domain.inventory.IaUnit;

import java.util.List;

/**
 * 存货计量单位Service接口
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IaUnitService {
    /**
     * 查询存货计量单位
     *
     * @param id 存货计量单位主键
     * @return 存货计量单位
     */
    public IaUnit selectIaUnitById(Long id);

    /**
     * 查询存货计量单位列表
     *
     * @param iaUnit 存货计量单位
     * @return 存货计量单位集合
     */
    public List<IaUnit> selectIaUnitList(IaUnit iaUnit);

    /**
     * 新增存货计量单位
     *
     * @param iaUnit 存货计量单位
     * @return 结果
     */
    public int insertIaUnit(IaUnit iaUnit);

    /**
     * 修改存货计量单位
     *
     * @param iaUnit 存货计量单位
     * @return 结果
     */
    public int updateIaUnit(IaUnit iaUnit);

    /**
     * 批量删除存货计量单位
     *
     * @param ids 需要删除的存货计量单位主键集合
     * @return 结果
     */
    public int deleteIaUnitByIds(List<Long> ids);

    /**
     * 删除存货计量单位信息
     *
     * @param id 存货计量单位主键
     * @return 结果
     */
    public int deleteIaUnitById(Long id);

    /**
     * 获取最大编码
     * @return
     */
    String getMaxCode();
}
