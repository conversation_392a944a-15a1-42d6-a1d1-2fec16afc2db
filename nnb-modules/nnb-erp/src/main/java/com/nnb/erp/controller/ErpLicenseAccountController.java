package com.nnb.erp.controller;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.nnb.erp.constant.ErpLicenseConstants;
import com.nnb.erp.domain.ErpLicense;
import com.nnb.erp.domain.dto.license.ErpLicenseAccountDTO;
import com.nnb.erp.domain.dto.license.ErpLicenseDTO;
import com.nnb.erp.domain.vo.license.ErpLicenseVO;
import com.nnb.erp.mapper.ErpLicenseAccountMapper;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.ErpLicenseAccount;
import com.nnb.erp.service.IErpLicenseAccountService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 执照会计服务Controller
 * 
 * <AUTHOR>
 * @date 2023-05-17
 */
@RestController
@RequestMapping("/erpLicenseAccount")
@Api(tags = "ErpLicenseAccountController", description = "执照会计服务")
public class ErpLicenseAccountController extends BaseController
{
    @Autowired
    private IErpLicenseAccountService erpLicenseAccountService;
    @Autowired
    private ErpLicenseAccountMapper erpLicenseAccountMapper;
    /**
     * 查询执照会计服务列表
     */
    @ApiOperation(value = "查询执照会计服务列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpLicenseAccount.class)})
    @GetMapping("/list")
    public TableDataInfo list(ErpLicenseDTO erpLicenseAccount)
    {
        String licenseIds = "";
        if (ObjectUtil.isNotEmpty(erpLicenseAccount.getLicenseTax())) {
            ErpLicenseAccountDTO erpLicenseAccountDTO = new ErpLicenseAccountDTO();

            Calendar ca = Calendar.getInstance();
            ca.setTime(new Date());
            int month = ca.get(Calendar.MONTH) + 1;//第几个月
            int year = ca.get(Calendar.YEAR);//年份数值

            erpLicenseAccountDTO.setAccountYear(year);
            erpLicenseAccountDTO.setAccountMonth(month);
            List<ErpLicenseAccount> erpLicenseAccountList = erpLicenseAccountMapper.selectList(erpLicenseAccountDTO);
            for (int i = 0; i < erpLicenseAccountList.size(); i++) {
                if (!licenseIds.contains(erpLicenseAccountList.get(i).getLicenseId().toString())) {
                    licenseIds += "," + erpLicenseAccountList.get(i).getLicenseId().toString();
                }
            }
        }

        if (ObjectUtil.isNotEmpty(licenseIds)) {
            erpLicenseAccount.setIds(licenseIds.replaceFirst(",", ""));
        }



        startPage();
        erpLicenseAccount.setExamineStatus(ErpLicenseConstants.QZD_REVIEWED_PASS);
        if (ObjectUtil.isEmpty(erpLicenseAccount.getHandoverStatus())) {
            erpLicenseAccount.setHandoverStatus(1);
        }
        if (ObjectUtil.isNotEmpty(erpLicenseAccount.getLicenseAccountStatus())) {
//            1.审核通过=====审核通过，未删除，未上架，待售
//            2.待售=====审核通过，未删除，上架，待售
//            3.已售=====审核通过，未删除，上架，已售
//            4.已提单=====审核通过，未删除，上架，已提单
//            5.删除=====审核通过，删除
//            6.下架=====审核通过，未删除，下架，待售
            switch (erpLicenseAccount.getLicenseAccountStatus()) {
                case 1:
                    erpLicenseAccount.setDeleted(2);
                    erpLicenseAccount.setGrounding(3);
                    erpLicenseAccount.setStatus(1);
                    break;
                case 2:
                    erpLicenseAccount.setDeleted(2);
                    erpLicenseAccount.setGrounding(1);
                    erpLicenseAccount.setStatus(1);
                    break;
                case 3:
                    erpLicenseAccount.setDeleted(2);
                    erpLicenseAccount.setGrounding(1);
                    erpLicenseAccount.setStatus(3);
                    break;
                case 4:
                    erpLicenseAccount.setDeleted(2);
                    erpLicenseAccount.setGrounding(1);
                    erpLicenseAccount.setStatus(2);
                    break;
                case 5:
                    erpLicenseAccount.setDeleted(1);
                    break;
                case 6:
                    erpLicenseAccount.setDeleted(2);
                    erpLicenseAccount.setGrounding(2);
                    erpLicenseAccount.setStatus(1);
                    break;
                default:
                    break;
            }
        }
        List<ErpLicenseVO> list = erpLicenseAccountService.selectErpLicenseAccountList(erpLicenseAccount);
        return getDataTable(list);
    }

    /**
     * 导出执照会计服务列表
     */
//    @ApiOperation(value = "导出执照会计服务列表")
//    @PreAuthorize(hasPermi = "erp:account:export")
//    //@Log(title = "执照会计服务", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ErpLicenseAccount erpLicenseAccount) throws IOException
//    {
//        List<ErpLicenseAccount> list = erpLicenseAccountService.selectErpLicenseAccountList(erpLicenseAccount);
//        ExcelUtil<ErpLicenseAccount> util = new ExcelUtil<ErpLicenseAccount>(ErpLicenseAccount.class);
//        util.exportExcel(response, list, "执照会计服务数据");
//    }

    /**
     * 获取执照会计服务详细信息
     */
    @ApiOperation(value = "获取执照会计服务详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpLicenseAccount.class)})
    @PreAuthorize(hasPermi = "erp:account:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="执照会计服务id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpLicenseAccountService.selectErpLicenseAccountById(id));
    }

    /**
     * 新增执照会计服务
     */
    @ApiOperation(value = "新增执照会计服务")
    @PreAuthorize(hasPermi = "erp:account:add")
    //@Log(title = "执照会计服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpLicenseAccount erpLicenseAccount)
    {
        return toAjax(erpLicenseAccountService.insertErpLicenseAccount(erpLicenseAccount));
    }

    /**
     * 修改执照会计服务
     */
    @ApiOperation(value = "修改执照会计服务")
    @PreAuthorize(hasPermi = "erp:account:edit")
    //@Log(title = "执照会计服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpLicenseAccount erpLicenseAccount)
    {
        return toAjax(erpLicenseAccountService.updateErpLicenseAccount(erpLicenseAccount));
    }

    /**
     * 删除执照会计服务
     */
    @ApiOperation(value = "删除执照会计服务")
    @PreAuthorize(hasPermi = "erp:account:remove")
    //@Log(title = "执照会计服务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpLicenseAccountService.deleteErpLicenseAccountByIds(ids));
    }

    @ApiOperation(value = "分配跟进会计")
    @GetMapping("/operateErpLicenseFollowAccount")
    public AjaxResult operateErpLicenseFollowAccount(
            @ApiParam(name="licenseId",value="执照id")  @RequestParam(name = "licenseId") Long licenseId,
            @ApiParam(name="userId",value="跟进人Id") @RequestParam(name = "userId") Long userId
    ){
        return toAjax(erpLicenseAccountService.operateErpLicenseFollowAccount(licenseId, userId));
    }

    @ApiOperation(value = "执照确认交接")
    @GetMapping("/handoverErpLicense")
    public AjaxResult handoverErpLicense(
            @ApiParam(name="licenseId",value="执照id")  @RequestParam(name = "licenseId") Long licenseId
    ){
        return toAjax(erpLicenseAccountService.handoverErpLicense(licenseId));
    }


    @ApiOperation(value = "操作执照")
    @GetMapping("/operateErpLicenseAccount")
    public AjaxResult operateErpLicenseAccount(
            @ApiParam(name="type",value="1报税2汇算清缴3工商年检") @RequestParam(name = "type") Integer type,
            @ApiParam(name="licenseId",value="执照id")  @RequestParam(name = "licenseId") Long licenseId,
            @ApiParam(name="date",value="报税，汇算清缴，工商年检时间") @RequestParam(name = "date") Date date
    ){
        return toAjax(erpLicenseAccountService.operateErpLicenseAccount(type, licenseId, date));
    }

    @ApiOperation(value = "获取执照报税情况")
    @GetMapping("/getErpLicenseBS")
    public JSONObject getErpLicenseBS(
            @ApiParam(name="licenseId",value="执照id")  @RequestParam(name = "licenseId") Long licenseId
    ){
        return erpLicenseAccountService.getErpLicenseBS(licenseId);
    }
}
