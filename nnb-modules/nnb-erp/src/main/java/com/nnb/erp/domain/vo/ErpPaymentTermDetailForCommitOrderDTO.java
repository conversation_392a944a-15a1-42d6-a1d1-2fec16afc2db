package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 汇款方式详情，用于提交订单，DTO.
 *
 * <AUTHOR>
 * @since 2022/3/22 10:15
 */
@Data
public class ErpPaymentTermDetailForCommitOrderDTO {

    /**
     * 收款方式标识。
     */
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("收款方式标识。")
    private Long termId;

    /**
     * 收款金额。
     */
    @ApiModelProperty("收款金额。")
    private BigDecimal money = new BigDecimal("0");

    /**
     * 收款方式：1现金；2刷卡；3扫码；4转账；5微信/支付宝在线支付。
     */
    @ApiModelProperty("收款方式：1现金；2刷卡；3扫码；4转账；5微信/支付宝在线支付。")
    private Integer paymentType;

    /**
     * 收款凭证，上传文件后的url。
     */
    @ApiModelProperty("收款凭证，上传文件后的url。")
    private String paymentUrl;

}