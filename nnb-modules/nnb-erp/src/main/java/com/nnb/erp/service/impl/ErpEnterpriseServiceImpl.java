package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.core.utils.sql.SqlUtil;
import com.nnb.common.core.web.page.PageDomain;
import com.nnb.common.core.web.page.TableSupport;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.constant.DataScopeConstants;
import com.nnb.erp.constant.DateFormatConstants;
import com.nnb.erp.constant.enums.OrderRefundApprovalStatusEnum;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.enterprise.EnterpriseDetail;
import com.nnb.erp.domain.enterprise.ErpClientDetail;
import com.nnb.erp.domain.enterprise.ErpIndustryCommerceDetail;
import com.nnb.erp.domain.enterprise.ErpOrderDetail;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.client.ErpClientListResultVo;
import com.nnb.erp.domain.vo.client.ErpEnterpriseDto;
import com.nnb.erp.mapper.*;
import com.nnb.erp.service.ISServiceMainService;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.BdOutboundContactsVo;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.service.IErpEnterpriseService;

import javax.annotation.Resource;

/**
 * 企业Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-14
 */
@Service
@Slf4j
public class ErpEnterpriseServiceImpl implements IErpEnterpriseService {
    @Autowired
    private ErpEnterpriseMapper erpEnterpriseMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISServiceMainService serviceMainService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private ErpOrdersMapper erpOrdersMapper;
    @Autowired
    private ErpClientMapper erpClientMapper;

    @Autowired
    private ErpEnterpriseDetailMapper erpEnterpriseDetailMapper;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private ErpEnterpriseUserMapper enterpriseUserMapper;

    @Autowired
    private ErpOldEnterpriseFollowMapper erpOldEnterpriseFollowMapper;

    @Autowired
    private ErpOldEnterpriseTagsMapper erpOldEnterpriseTagsMapper;
    @Autowired
    private ErpOrdersServiceImpl erpOrdersService;

    /**
     * 查询企业
     *
     * @param id 企业主键
     * @return 企业
     */
    @Override
    public ErpEnterprise selectErpEnterpriseById(Long id) {
        return erpEnterpriseMapper.selectErpEnterpriseById(id);
    }

    /**
     * 查询企业列表
     *
     * @param erpEnterprise 企业
     * @return 企业
     */
    @Override
    public Map<String, Object> selectErpEnterpriseList(ErpEnterpriseDto erpEnterprise) {

        Map<String, Object> map = new HashMap<>();
        //组装查询条件
        assemblyQueryCriteria(erpEnterprise);
        //分页
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Page<Object> objects = PageHelper.startPage(pageNum, pageSize, orderBy);
        List<ErpClientListResultVo> erpEnterpriseList = erpEnterpriseMapper.selectErpEnterpriseList(erpEnterprise);
        //总数
        long total = objects.getTotal();
        if (0L == total) {
            map.put("total", 0);
        } else {
            map.put("total", total);
        }

        //组装数据
        assemblyData(map, erpEnterpriseList);

        return map;
    }

    /**
     * 组装查询条件
     * @param erpEnterprise
     */
    private void assemblyQueryCriteria(ErpEnterpriseDto erpEnterprise) {
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        List<SysRole> collect = sysUser.getRoles().stream().sorted(Comparator.comparing(SysRole::getDataScope)).collect(Collectors.toList());
        SysRole sysRole = collect.get(0);
        //这里不用dataScope，对应的列表权限需要调整
        String dataScope = sysRole.getDataScope();
        if (DataScopeConstants.DATA_SCOPE_DEPT.equals(dataScope)) {
            erpEnterprise.setDataScope(DataScopeConstants.DATA_SCOPE_DEPT_AND_CHILD);
        } else if (DataScopeConstants.DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
            erpEnterprise.setDataScope(DataScopeConstants.DATA_SCOPE_DEPT);
        } else {
            erpEnterprise.setDataScope(dataScope);
        }
        erpEnterprise.setDataScope(dataScope);
        if (DataScopeConstants.ADMIN.equals(sysUser.getUserName())) {
            erpEnterprise.setDataScope(DataScopeConstants.DATA_SCOPE_ALL);
        }
        erpEnterprise.setDeptId(sysUser.getDeptId());
        erpEnterprise.setRoleId(sysRole.getRoleId());
        erpEnterprise.setSysUserId(sysUser.getUserId());

        // todo 1 查询所有客户
        if (StringUtils.isNotEmpty(erpEnterprise.getDatSigningDatecreatedTimeStart())) {
            erpEnterprise.setDatSigningDatecreatedTimeStart(erpEnterprise.getDatSigningDatecreatedTimeStart() + DateFormatConstants.TIME_BEGIN);
        }
        if (StringUtils.isNotEmpty(erpEnterprise.getDatSigningDatecreatedTimeEnd())) {
            erpEnterprise.setDatSigningDatecreatedTimeEnd(erpEnterprise.getDatSigningDatecreatedTimeEnd() + DateFormatConstants.TIME_END);
        }
    }

    /**
     * 组装数据
     * @param map
     * @param erpEnterpriseList
     */
    private void assemblyData(Map<String, Object> map, List<ErpClientListResultVo> erpEnterpriseList) {
        //com_dict_region title ec.num_city_id
        if (CollUtil.isNotEmpty(erpEnterpriseList)) {
            //城市信息
            List<Long> cityIds = erpEnterpriseList.stream().map(ErpClientListResultVo::getNumCityId).collect(Collectors.toList());
            List<Map<String, Object>> cityNameList = erpEnterpriseMapper.selectCityNameByCityIds(cityIds);
            if (CollUtil.isNotEmpty(cityNameList)) {
                for (ErpClientListResultVo client : erpEnterpriseList) {
                    for (Map<String, Object> cityMap : cityNameList) {
                        if (client.getNumCityId().equals(MapUtil.getLong(cityMap, "id"))) {
                            client.setCityName(MapUtil.getStr(cityMap, "name"));
                        }
                    }
                }
            }
            //手机号加密
            for (ErpClientListResultVo client : erpEnterpriseList) {
                if (StringUtils.isNotEmpty(client.getContactPhone())){
                    String contactPhone = client.getContactPhone();
                    if (contactPhone.length() == 11) {
                        client.setContactPhone(contactPhone.substring(0, 3) + "****" + contactPhone.substring(7));
                    }
                }
            }
            //查询该企业下的关联订单
            int[] status = {2, 3, 4};
            int[] numValidStatus = {0, 1, 3};
            //将订单信息放入实体
            setOrdersToEnterpriseList(erpEnterpriseList, status, null, 0);
            map.put("list", erpEnterpriseList);
        } else {
            map.put("list", new ArrayList<>());
        }
    }

    /**
     * 将订单信息放入实体
     * @param erpEnterpriseList
     * @param status
     * @param numValidStatus
     */
    private void setOrdersToEnterpriseList(List<ErpClientListResultVo> erpEnterpriseList, int[] status, int[] numValidStatus, int checkOrderKp) {
        //根据企业查询client
        List<Long> enterpriseIds = erpEnterpriseList.stream().map(ErpClientListResultVo::getNumEnterpriseId).collect(Collectors.toList());
        List<ErpClient> erpClientList = erpClientMapper.selectErpClientByEnterpriseIds(enterpriseIds);
        //根据client查询订单
        if (CollUtil.isNotEmpty(erpClientList)) {
            Long[] clientArray = erpClientList.stream().map(ErpClient::getId).toArray(Long[]::new);
            List<ErpOrderInfoForOmListVO> erpOrders = erpOrdersMapper.selectErpOrdersListByClientId(clientArray, status, numValidStatus);


            //组装数据
            if (CollUtil.isNotEmpty(erpOrders)) {
                //查询电子合同号
                Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
                List<Long> orderIdList = erpOrders.stream().map(ErpOrderInfoForOmListVO::getOrderId).collect(Collectors.toList());
                ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
                query.setOrderIdList(orderIdList);
                List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
                if (CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
                    map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
                }

                for (int i = 0; i < erpOrders.size(); i++) {
                    ErpOrderInfoForOmListVO orderInfo = erpOrders.get(i);
                    erpOrdersService.getOrderRefundPayAmount(orderInfo);

                    if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(orderInfo.getIsElectronicContract()) && 1 == orderInfo.getIsElectronicContract())) {
                        if (map.containsKey(orderInfo.getOrderId())) {
                            List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(orderInfo.getOrderId());
                            if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                                StringBuffer vcContractNumber = new StringBuffer();
                                int size = erpOrderInfoForOmListVOS.size();
                                for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                                    if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                                    } else {
                                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                                    }
                                }
                                orderInfo.setVcContractNumber(String.valueOf(vcContractNumber));
                            }
                        }
                    } else {
                        orderInfo.setVcOnlineContractPdf(null);
                    }
                }
                //企业下有多个客户，根据企业分组
                Map<Long, List<ErpClient>> groupClients = erpClientList
                        .stream().collect(Collectors
                                .groupingBy(ErpClient::getNumEnterpriseId)
                        );

                for (ErpClientListResultVo client : erpEnterpriseList) {
                    List<ErpOrderInfoForOmListVO> orders = new ArrayList<>();
                    Map<Long, List<ErpClient>> collect = groupClients.entrySet()
                            .stream()
                            .filter(groupClientMap -> client.getNumEnterpriseId().equals(groupClientMap.getKey()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                    for (Map.Entry<Long, List<ErpClient>> entry : collect.entrySet()) {
                        for (ErpClient erpClient : entry.getValue()) {
                            orders.addAll(
                                    erpOrders.stream()
                                            .filter(en -> erpClient.getId().equals(en.getClientId()))
                                            .collect(Collectors.toList())
                            );
                        }
                    }
                    if (checkOrderKp == 1) {
                        erpOrdersService.checkOrderCanKp(orders);
                    }
                    client.setErpOrders(orders);
                }
            }
        }
    }

    @Override
    public Map<String, Object> companyList(ErpEnterpriseDto erpEnterprise) {

        Map<String, Object> map = new HashMap<>();
        //分页
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Page<Object> objects = PageHelper.startPage(pageNum, pageSize, orderBy);
        List<ErpClientListResultVo> erpEnterpriseList = erpEnterpriseMapper.companyList(erpEnterprise);
        //总数
        long total = objects.getTotal();
        if (0L == total) {
            map.put("total", 0);
        } else {
            map.put("total", total);
        }

        int[] status = {2, 3, 4};
        int[] numValidStatus = {0, 1, 3};

        if (CollUtil.isNotEmpty(erpEnterpriseList)) {
            setOrdersToEnterpriseList(erpEnterpriseList, status, numValidStatus, 1);
            map.put("list", erpEnterpriseList);
        }else {
            map.put("list", new ArrayList<>());
        }

        return map;
    }

    @Override
    public EnterpriseDetail getEnterpriseDetail(Long enterpriseId) {
        EnterpriseDetail enterpriseDetail = new EnterpriseDetail();
        //工商信息
        ErpIndustryCommerceDetail erpIndustryCommerceDetail = erpEnterpriseDetailMapper.selectErpBizDetailByEnterpriseId(enterpriseId);
        ErpEnterpriseUser erpEnterpriseUser = new ErpEnterpriseUser();
        erpEnterpriseUser.setNumErpEnterpriseId(enterpriseId);
        erpEnterpriseUser.setNumUserType(1L);
        //经营年限
        if(Objects.nonNull(erpIndustryCommerceDetail.getOpfrom())){
            String dayComparePrecise = DateUtils.dayComparePrecise(DateUtils.parseDateToStr("yyyy-MM-dd", erpIndustryCommerceDetail.getOpfrom()),
                    DateUtils.parseDateToStr("yyyy-MM-dd", new Date()));
            erpIndustryCommerceDetail.setNumOperatingYears(dayComparePrecise);
        }
        List<ErpEnterpriseUser> erpEnterpriseUsers = enterpriseUserMapper.selectErpBizUserList(erpEnterpriseUser);
        //法人
        erpIndustryCommerceDetail.setLegalPerson(CollectionUtils.isNotEmpty(erpEnterpriseUsers) ? erpEnterpriseUsers.get(0).getVcName() : null);
        enterpriseDetail.setErpIndustryCommerceDetails(erpIndustryCommerceDetail);
        ErpClient erpClient = new ErpClient();
        erpClient.setNumEnterpriseId(enterpriseId);
        //订单信息
        List<ErpClient> erpClients = erpClientMapper.selectErpClientList(erpClient);
        if (CollectionUtils.isNotEmpty(erpClients)) {
            List<Long> collect = erpClients.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<ErpOrderDetail> erpOrderDetails = erpOrdersMapper.selectOrderListByClientId(collect);

            ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
            List<Long> orderIdCollect = erpOrderDetails.stream().map(ErpOrderDetail::getOrderId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderIdCollect)) {
                query.setOrderIdList(collect);
            }

            List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
            log.info("老客户查询电子合同信息为::{},{}", new Date(), onlieContractByOrderIdList);

            //查询合同号
            Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
                map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
                log.info("老客户查询分组后的电子合同信息为::{},{}", new Date(), map);
            }

            if (orderIdCollect.size() > 0) {
                for (ErpOrderDetail orderInfo : erpOrderDetails) {
                    if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(orderInfo.getIsElectronicContract()) && 1 == orderInfo.getIsElectronicContract())) {
                        if (map.containsKey(orderInfo.getOrderId())) {
                            List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(orderInfo.getOrderId());
                            if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                                StringBuffer vcContractNumber = new StringBuffer();
                                int size = erpOrderInfoForOmListVOS.size();
                                for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                                    if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                                    } else {
                                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                                    }
                                }
                                orderInfo.setContractNumber(String.valueOf(vcContractNumber));
                            }
                        }
                    }
                }
            }
            enterpriseDetail.setErpOrderDetails(erpOrderDetails);
            //联系人信息
            List<ErpClientDetail> erpClientDetails = erpClientMapper.selectClientByEnterpriseIds(enterpriseId);
            enterpriseDetail.setErpClientDetails(erpClientDetails);
            //通话记录
            R<List<BdOutboundContactsVo>> vosByEnterpriseId = remoteCustomerService.getBdOutboundVosByEnterpriseId(enterpriseId);
            if (200 == vosByEnterpriseId.getCode()) {
                List<BdOutboundContactsVo> data = vosByEnterpriseId.getData();
                enterpriseDetail.setOutboundDetails(data);
            }
        }
        return enterpriseDetail;
    }

    @Override
    public EnterpriseFollowAndTag getEnterpriseFollowAndTag(Long oldEnterpriseFollowId) {
        EnterpriseFollowAndTag enterpriseFollowAndTag = erpOldEnterpriseFollowMapper.getEnterpriseFollowAndTag(oldEnterpriseFollowId);
        List<String> erpOldEnterpriseTagsById = erpOldEnterpriseTagsMapper.getErpOldEnterpriseTagsById(enterpriseFollowAndTag.getEnterpriseId());
        enterpriseFollowAndTag.setTagName(erpOldEnterpriseTagsById);
        return enterpriseFollowAndTag;
    }

    @Override
    public int scheduledUpdateEnterPriseYearInspect() {
        return erpEnterpriseMapper.updateYearInspectAll();
    }

    @Override
    public List<ErpEnterprise> getEnterpriseByNameLimit(String companyName) {
        return erpEnterpriseMapper.selectErpEnterpriseListLimit(companyName);
    }

    @Override
    public List<Long> getSysUserIdList() {
        LoginUser loginUser = tokenService.getLoginUser();
        Long deptId = loginUser.getSysUser().getDeptId();
        R<List<SysUser>> deptChildren = remoteUserService.getSysDeptChildren(deptId, SecurityConstants.INNER);
        if (200 == deptChildren.getCode()) {
            List<Long> collect = deptChildren.getData().stream().map(val -> val.getDeptId()).collect(Collectors.toList());

            List<SysUser> sysUser = new ArrayList<>();
            for (Long aLong : collect) {
                sysUser = erpEnterpriseMapper.selectUser(aLong);


            }
            for (SysUser user : sysUser) {
                collect.add(user.getUserId());
            }

            return collect;
        }
        return null;
    }


    /**
     * 新增企业
     *
     * @param erpEnterprise 企业
     * @return 结果
     */
    @Override
    public int insertErpEnterprise(ErpEnterprise erpEnterprise) {
        return erpEnterpriseMapper.insertErpEnterprise(erpEnterprise);
    }

    /**
     * 修改企业
     *
     * @param erpEnterprise 企业
     * @return 结果
     */
    @Override
    public int updateErpEnterprise(ErpEnterprise erpEnterprise) {
        return erpEnterpriseMapper.updateErpEnterprise(erpEnterprise);
    }

    /**
     * 批量删除企业
     *
     * @param ids 需要删除的企业主键
     * @return 结果
     */
    @Override
    public int deleteErpEnterpriseByIds(Long[] ids) {
        return erpEnterpriseMapper.deleteErpEnterpriseByIds(ids);
    }

    /**
     * 删除企业信息
     *
     * @param id 企业主键
     * @return 结果
     */
    @Override
    public int deleteErpEnterpriseById(Long id) {
        return erpEnterpriseMapper.deleteErpEnterpriseById(id);
    }
}
