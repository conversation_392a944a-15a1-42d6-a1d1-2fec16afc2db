package com.nnb.erp.controller.inventory;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.common.core.web.page.TableDataInfo;
import com.nnb.erp.constant.InventoryConstants;
import com.nnb.erp.domain.ErpOrders;
import com.nnb.erp.domain.inventory.*;
import com.nnb.erp.enums.IaStorageBusTypeEnum;
import com.nnb.erp.service.inventory.IaStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Chen-xy
 * @Description: 存货出/入库Controller
 * @Date: 2023-12-26
 * @Version: 1.0
 */
@RestController
@RequestMapping("/iaStorage")
@Api(tags = "IaStorageController", description = "存货出/入库")
public class IaStorageController extends BaseController {

    @Autowired
    private IaStorageService iaStorageService;

    /**
     * 查询存货出/入库列表
     */
    @ApiOperation(value = "查询存货出/入库列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaStorage.class)})
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody IaStorage iaStorage) {
        Page<Object> objects = PageHelper.startPage(
                iaStorage.getPageNum(), iaStorage.getPageSize(), null);
        List<IaStorage> list = iaStorageService.selectIaStorageList(iaStorage);
        return getDataTableAndTotal(list, objects.getTotal());
    }

    /**
     * 获取存货出/入库详细信息
     */
    @ApiOperation(value = "获取存货出/入库详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaStorage.class)})
    @PostMapping("/getById")
    public AjaxResult getInfo(@RequestBody IaStorage iaStorage) {
        return AjaxResult.success(iaStorageService.selectIaStorageById(iaStorage.getId()));
    }

    /**
     * 新增存货出/入库
     */
    @ApiOperation(value = "新增存货出/入库")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.insertIaStorage(iaStorage));
    }

    /**
     * 修改存货出/入库
     */
    @ApiOperation(value = "修改存货出/入库")
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.updateIaStorage(iaStorage));
    }

    /**
     * 删除存货出/入库
     */
    @ApiOperation(value = "删除存货出/入库")
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.deleteIaStorageByIds(iaStorage.getIds()));
    }

    /**
     * 查询存货出/入库列表
     */
    @ApiOperation(value = "查询存货出/入库列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaStorage.class)})
    @PostMapping("/iaPageList")
    public TableDataInfo list(@RequestBody IaStorageSearch search) {
        // Map<String, Object> map = iaStorageService.iaPageList(search);
        Map<String, Object> map = iaStorageService.iaPageListWithDetail(search);
        List<IaStorageDetail> list = MapUtil.get(map, "list", new TypeReference<List<IaStorageDetail>>() {});
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    /**
     * 分页查询存货出/入库及详情列表
     */
    @ApiOperation(value = "分页查询存货出/入库及详情列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaStorage.class)})
    @PostMapping("/pageList")
    public TableDataInfo pageList(@RequestBody IaStorageSearch search) {
        Map<String, Object> map = iaStorageService.pageList(search);
        List<IaStorageDetail> list = MapUtil.get(
                map, "list", new TypeReference<List<IaStorageDetail>>() {
                });
        return getDataTableAndTotal(list, MapUtil.getLong(map, "total"));
    }

    /**
     * 导出存货出/入库列表
     */
    @ApiOperation(value = "导出存货出/入库列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody IaStorageSearch search) throws IOException {
        search.setExportFile(InventoryConstants.EXPORT_FILE);
        Map<String, Object> map = iaStorageService.pageList(search);
        List<IaStorageDetail> list = MapUtil.get(
                map, "list", new TypeReference<List<IaStorageDetail>>() {
                });
        //采购入库
        if (IaStorageBusTypeEnum.PURCHASE_RECEIPT.getCode().equals(search.getBusType())
                || IaStorageBusTypeEnum.PURCHASE_RETURN.getCode().equals(search.getBusType())){
            purchaseExportFile(response, list);
        }
        //领用出库
        if (IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getCode().equals(search.getBusType())){
            requisitionExportFile(response, list);
        }
        //销售出库
        if (IaStorageBusTypeEnum.SALES_OUTBOUND.getCode().equals(search.getBusType())
                || IaStorageBusTypeEnum.SALES_RETURNS.getCode().equals(search.getBusType())){
            salesExportFile(response, list);
        }
    }

    @ApiOperation(value = "获取最大编码")
    @PostMapping("/getMaxCode")
    public AjaxResult getMaxCode(@RequestBody IaStorage iaStorage) {
        return AjaxResult.success("success", iaStorageService.getMaxCode(iaStorage));
    }

    @ApiOperation(value = "新增存货出/入库")
    @PostMapping("/save")
    public AjaxResult save(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.save(iaStorage));
    }

    @ApiOperation(value = "更新存货出/入库")
    @PostMapping("/update")
    public AjaxResult update(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.update(iaStorage));
    }

    @ApiOperation(value = "删除存货出/入库")
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.delete(iaStorage));
    }

    @ApiOperation(value = "删除存货出/入库明细")
    @PostMapping("/deleteDetail")
    public AjaxResult deleteDetail(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.deleteDetail(iaStorage));
    }

    @ApiOperation(value = "审核/反审核")
    @PostMapping("/auditing")
    public AjaxResult auditing(@RequestBody IaStorage iaStorage) {
        return toAjax(iaStorageService.auditing(iaStorage));
    }

    @ApiOperation(value = "查找订单里信息放入实体")
    @PostMapping("/relatedOrders")
    public AjaxResult relatedOrders(@RequestBody IaStorage iaStorage) {
        return AjaxResult.success(iaStorageService.relatedOrders(iaStorage));
    }

    /**
     * 导入存期初
     */
    @ApiOperation(value = "导入存期初")
    @PostMapping("/import-begin")
    public AjaxResult importBegin(@RequestParam(required = false) MultipartFile file,
                                  @RequestParam(required = false) Long companyId) throws IOException {
        Map<String, Object> map = iaStorageService.importBegin(file, companyId);
        return AjaxResult.success(map);
    }

    /**
     * 导入采购入库
     */
    @ApiOperation(value = "导入采购入库")
    @PostMapping("/import-purchase-receipt")
    public AjaxResult importPurchaseReceipt(@RequestParam(required = false) MultipartFile file,
                                            @RequestParam(required = false) Long companyId) throws IOException {
        Map<String, Object> map = iaStorageService.importPurchaseReceipt(file, companyId);
        return AjaxResult.success(map);
    }

    /**
     * 查询供应商
     */
    @ApiOperation(value = "查询供应商")
    @PostMapping("/querySuppliers")
    public AjaxResult querySuppliers() {
        return AjaxResult.success(iaStorageService.querySuppliers());
    }

    /**
     * 查询往来单位企业
     */
    @ApiOperation(value = "查询往来单位企业")
    @PostMapping("/queryEnterprises")
    public AjaxResult queryEnterprises(@RequestBody Map<String, Object> map) {
        return AjaxResult.success(iaStorageService.queryEnterprises(MapUtil.getStr(map, "name")));
    }


    private void salesExportFile(HttpServletResponse response, List<IaStorageDetail> list) throws IOException {
        //查询所有订单实收金额
        List<Long> orderIds = list.stream()
                .map(IaStorageDetail::getIaStorage).filter(ObjectUtil::isNotEmpty)
                .map(IaStorage::getErpOrderId).filter(en -> en.intValue() != 0)
                .distinct().collect(Collectors.toList());
        List<ErpOrders> orderList = iaStorageService.selectActualAmountByOrderIds(orderIds);

        List<IaSalesExcelVo> collect = list.stream()
                .map(detail -> {
                    IaSalesExcelVo excelVo = new IaSalesExcelVo();
                    BeanUtils.copyProperties(detail.getIaStorage(), excelVo);
                    BeanUtils.copyProperties(detail, excelVo);
                    excelVo.setModel(detail.getIaInventoryArchives().getModel())
                            .setIaUnitName(detail.getIaInventoryArchives().getIaUnitName())
                            .setCreateTime(detail.getIaStorage().getCreateTime().atTime(LocalTime.MIN))
                            .setRemark(detail.getRemark());
                    orderList.stream()
                            .filter(en -> detail.getIaStorage().getErpOrderId().equals(en.getId()))
                            .findAny()
                            .ifPresent(en ->
                                    excelVo.setActualAmount(en.getNumPayPrice())
                            );
                    return excelVo;
                }).collect(Collectors.toList());

        ExcelUtil<IaSalesExcelVo> util = new ExcelUtil<IaSalesExcelVo>(IaSalesExcelVo.class);
        util.exportExcel(response, collect,
                IaStorageBusTypeEnum.SALES_OUTBOUND.getType(), IaStorageBusTypeEnum.SALES_OUTBOUND.getType()
        );
    }

    private void requisitionExportFile(HttpServletResponse response, List<IaStorageDetail> list) throws IOException {
        List<IaRequisitionExcelVo> collect = list.stream()
                .map(detail -> {
                    IaRequisitionExcelVo excelVo = new IaRequisitionExcelVo();
                    BeanUtils.copyProperties(detail.getIaStorage(), excelVo);
                    BeanUtils.copyProperties(detail, excelVo);
                    excelVo.setModel(detail.getIaInventoryArchives().getModel())
                            .setIaUnitName(detail.getIaInventoryArchives().getIaUnitName())
                            .setCreateTime(detail.getIaStorage().getCreateTime().atTime(LocalTime.MIN))
                            .setRemark(detail.getRemark());
                    return excelVo;
                }).collect(Collectors.toList());

        ExcelUtil<IaRequisitionExcelVo> util = new ExcelUtil<IaRequisitionExcelVo>(IaRequisitionExcelVo.class);
        util.exportExcel(response, collect,
                IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getType(),
                IaStorageBusTypeEnum.REQUISITION_AND_OUTBOUND.getType()
        );
    }


    private void purchaseExportFile(HttpServletResponse response, List<IaStorageDetail> list) throws IOException {
        List<IaPurchaseExcelVo> collect = list.stream()
                .map(detail -> {
                    IaPurchaseExcelVo excelVo = new IaPurchaseExcelVo();
                    BeanUtils.copyProperties(detail.getIaStorage(), excelVo);
                    BeanUtils.copyProperties(detail, excelVo);
                    excelVo.setModel(detail.getIaInventoryArchives().getModel())
                            .setIaUnitName(detail.getIaInventoryArchives().getIaUnitName())
                            .setCreateTime(detail.getIaStorage().getCreateTime().atTime(LocalTime.MIN))
                            .setRemark(detail.getRemark());
                    return excelVo;
                }).collect(Collectors.toList());

        ExcelUtil<IaPurchaseExcelVo> util = new ExcelUtil<IaPurchaseExcelVo>(IaPurchaseExcelVo.class);
        util.exportExcel(response, collect,
                IaStorageBusTypeEnum.PURCHASE_RECEIPT.getType(), IaStorageBusTypeEnum.PURCHASE_RECEIPT.getType()
        );
    }

}
