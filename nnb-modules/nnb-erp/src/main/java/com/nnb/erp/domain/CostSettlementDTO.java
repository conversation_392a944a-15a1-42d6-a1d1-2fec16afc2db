package com.nnb.erp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 业务支持成本申请明细对象 cost_settlement
 * 
 * <AUTHOR>
 * @date 2022-10-14
 */
@ApiModel(value="CostSettlement",description="业务支持成本申请明细对象")
@Data
public class CostSettlementDTO
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Integer id;

    /** 结算客户，注册列表id 字符串 逗号隔开 */
    @Excel(name = "结算客户，注册列表id 字符串 逗号隔开")
    @ApiModelProperty("结算客户，注册列表id 字符串 逗号隔开")
    private String registerIds;

    /** 1.注册地址成本，2.续费地址成本 3.变更 4.资质 */
    @Excel(name = "1.注册地址成本，2.续费地址成本 3.变更 4.资质")
    @ApiModelProperty("1.注册地址成本，2.续费地址成本 3.变更 4.资质")
    private Integer type;

    /** agent表id 代理商名称 */
    @Excel(name = "agent表id 代理商名称")
    @ApiModelProperty("agent表id 代理商名称")
    private Long agentId;

    /** 付款金额 */
    @Excel(name = "付款金额")
    @ApiModelProperty("付款金额")
    private BigDecimal paymentAmount;

    /** 付款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("付款日期")
    private Date paymentDate;

    /** 收款人 */
    @Excel(name = "收款人")
    @ApiModelProperty("收款人")
    private String payee;

    /** 银行卡 卡号 */
    @Excel(name = "银行卡 卡号")
    @ApiModelProperty("银行卡 卡号")
    private Long bankNumber;

    /** 银行卡名称 */
    @Excel(name = "银行卡名称")
    @ApiModelProperty("银行卡名称")
    private String bankName;

    /** 申请人 */
    @Excel(name = "申请人")
    @ApiModelProperty("申请人")
    private Integer userId;

    /** $column.columnComment */
    @Excel(name = "申请人")
    @ApiModelProperty("$column.columnComment")
    private Long createdBy;

    /** $column.columnComment */
    @Excel(name = "申请人")
    @ApiModelProperty("$column.columnComment")
    private Long updatedBy;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请人", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date createdAt;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请人", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("$column.columnComment")
    private Date updatedAt;

    /** 状态 1.待审批, 2,审批通过 4,已支付, 5驳回, 6撤销 */
    @Excel(name = "状态 1.待审批, 2,审批通过 4,已支付, 5驳回, 6撤销")
    @ApiModelProperty("状态 1.待审批, 2,审批通过 4,已支付, 5驳回, 6撤销")
    private Integer status;

    /** 确认金额 */
    @Excel(name = "确认金额")
    @ApiModelProperty("确认金额")
    private BigDecimal confirmAmount;

    /** 确认付款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "确认付款日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("确认付款日期")
    private Date confirmDate;

    /** 确认付款人 */
    @Excel(name = "确认付款人")
    @ApiModelProperty("确认付款人")
    private Integer confirmUser;

    /** 选择的银行账号 */
    @Excel(name = "选择的银行账号")
    @ApiModelProperty("选择的银行账号")
    private Long bankId;

    /** 审核通过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核通过时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("审核通过时间")
    private Date passTime;

    /** 是否预付 1是 2否 */
    @Excel(name = "是否预付 1是 2否")
    @ApiModelProperty("是否预付 1是 2否")
    private Integer isAdvance;

    /** advance.id */
    @Excel(name = "advance.id")
    @ApiModelProperty("advance.id")
    private Long advanceId;

    /** 1 已核销 2未核销 */
    @Excel(name = "1 已核销 2未核销")
    @ApiModelProperty("1 已核销 2未核销")
    private Integer hxStatus;

    /** 区分成本结算是订单或者详情 1-是;2-否 */
    @Excel(name = "区分成本结算是订单或者详情 1-是;2-否")
    @ApiModelProperty("区分成本结算是订单或者详情 1-是;2-否")
    private Integer isGsOrder;

    /** 打印 0.未申请打印 1.申请打印  2已打印 */
    @Excel(name = "打印 0.未申请打印 1.申请打印  2已打印")
    @ApiModelProperty("打印 0.未申请打印 1.申请打印  2已打印")
    private Integer isDy;


    @ApiModelProperty("抄送人")
    private List<Long> duplicateList;

    @ApiModelProperty("addressCostId")
    private Long addressCostId;

    @ApiModelProperty("核销单号")
    private String advanceNumber;

    @ApiModelProperty("服务单ID")
    private Long serviceId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("详细地址")
    private String detailAddress;

    @ApiModelProperty("订单编号")
    private String orderNum;

    @ApiModelProperty("产品Id")
    private Long productId;

    @Excel(name = "附件")
    @ApiModelProperty("附件")
    private String attachment;

    @ApiModelProperty("次数")
    private String numberOfTimes;

    @ApiModelProperty("区域")
    private String registerAreaName;
}
