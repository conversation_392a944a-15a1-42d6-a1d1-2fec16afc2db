package com.nnb.erp.domain.dto.approval;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ErpNewApprovalUserDTO {

    /** 审批类型，1：个人审批，2：部门审批 */
    @ApiModelProperty("审批类型，1：个人审批，2：部门审批")
    private Integer approvalType;

    /** 审批部门ID */
    @ApiModelProperty("审批部门ID")
    private Long deptId;

    /** 审批人ID */
    @ApiModelProperty("审批人ID")
    private Long userId;

    /** 是否有确认的审批人，0：否，1：是 */
    @ApiModelProperty("是否有确认的审批人，0：否，1：是")
    private Integer hasConfirmApprovalUser;

    /** 审批人排序 */
    @ApiModelProperty("审批人排序")
    private Long sort;

    @ApiModelProperty("城市ID")
    private Long cityId;
}
