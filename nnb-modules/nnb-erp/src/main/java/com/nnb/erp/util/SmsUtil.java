package com.nnb.erp.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.nnb.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * ali-sms，util。
 *
 * <AUTHOR>
 * @since 2022/7/19 14:21
 */
@Slf4j
public class SmsUtil {

//    @Value("${aliyun.sms.regionId}")
    private static String regionId = "cn-beijing";

//    @Value("${aliyun.sms.accessKeyId}")
    private static String accessKeyId = "LTAI4G4bvCE7mJ1CitfRvtN6";

//    @Value("${aliyun.sms.accessKeySecret}")
    private static String accessKeySecret = "******************************";

    public static void sendSingle(String code, Map<String, String> dataMap, String phone, String contractSubject) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        log.info("发送短信入参为code{},dataMap{},phone{},contractSubject{}", code, dataMap, phone, contractSubject);

        CommonResponse response = null;

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("SignName", getContractSubject(contractSubject));
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("TemplateCode", code);
        request.putQueryParameter("TemplateParam", JSON.toJSONString(dataMap));

        try {
            response = client.getCommonResponse(request);
        } catch (ClientException var11) {
            log.error("短信发送异常response:{}", JSONUtil.toJsonStr(response));
            //throw new ServiceException("短信发送异常");
        }

        String responseData =  response.getData();
        JSONObject responseDataJsonObject = JSONObject.parseObject(responseData);
        String responseDataMessage =  responseDataJsonObject.getString("Message");
        if (!"OK".equals(responseDataMessage)) {
            log.error("短信发送异常:{}", responseDataMessage);
            //throw new ServiceException(responseDataMessage);
        }

    }

    /**
     * 转换短信的签名
     * @param contractSubject
     * @return
     */
    private static String getContractSubject(String contractSubject){
        if(StringUtils.isNotEmpty(contractSubject)){
            switch (contractSubject){
                case "1":
                case "2":
                    return "北京小苗财税服务有限公司";
                case "3":
                    return "后企之秀";
                case "4":
                    return "上海企苗";
                case "11":
                    return "云企创新（北京）教育咨询有限公司";
                default:
                    return "北京小苗财税服务有限公司";
            }
        }
        return "北京小苗财税服务有限公司";
    }

}
