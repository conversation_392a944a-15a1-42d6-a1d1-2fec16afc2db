package com.nnb.erp.domain.vo.onlineContract.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/7/13 13:25
 */
@Data
@ApiModel(value = "ContractContentXIX", description = "科技类资质服务合同")
public class ContractContentXIX {

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 甲方负责人姓名
     */
    private String firstPrincipalName;

    /**
     * 甲方联系电话
     */
    private String firstContactPhone;

    /**
     * 甲方住址
     */
    private String firstAddress;

    /**
     * 乙方
     */
    private String secondParty;
    /**
     * 乙方负责人姓名
     */
    private String secondPrincipalName;
    /**
     * 乙方联系电话
     */
    private String secondtContactPhone;
    /**
     * 乙方住址
     */
    private String secondAddress;

    /**
     * 应收金额
     */
    private BigDecimal totalPrice;
    /**
     * 实收金额
     */
    private BigDecimal payPrice;

    /**
     * 支付人民币大写
     */
    private String payerMoneyCapital;
    /**
     * 支付人民币小写
     */
    private BigDecimal payerMoneyLowerCase;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 合同其它详情信息
     */
    private Object contractDetailObject;


}
