package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpProductFinanceClassification;
import com.nnb.erp.domain.vo.ErpProductFinanceClassificationVO;

/**
 * 财务产品分类Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
public interface IErpProductFinanceClassificationService 
{
    /**
     * 查询财务产品分类
     * 
     * @param id 财务产品分类主键
     * @return 财务产品分类
     */
    public ErpProductFinanceClassification selectErpProductFinanceClassificationById(Long id);

    /**
     * 查询财务产品分类列表
     * 
     * @param erpProductFinanceClassification 财务产品分类
     * @return 财务产品分类集合
     */
    public List<ErpProductFinanceClassificationVO> selectErpProductFinanceClassificationList(ErpProductFinanceClassification erpProductFinanceClassification);

    /**
     * 新增财务产品分类
     * 
     * @param erpProductFinanceClassification 财务产品分类
     * @return 结果
     */
    public int insertErpProductFinanceClassification(ErpProductFinanceClassification erpProductFinanceClassification);

    /**
     * 修改财务产品分类
     * 
     * @param erpProductFinanceClassification 财务产品分类
     * @return 结果
     */
    public int updateErpProductFinanceClassification(ErpProductFinanceClassification erpProductFinanceClassification);

    /**
     * 批量删除财务产品分类
     * 
     * @param ids 需要删除的财务产品分类主键集合
     * @return 结果
     */
    public int deleteErpProductFinanceClassificationByIds(Long[] ids);

    /**
     * 删除财务产品分类信息
     * 
     * @param id 财务产品分类主键
     * @return 结果
     */
    public int deleteErpProductFinanceClassificationById(Long id);
}
