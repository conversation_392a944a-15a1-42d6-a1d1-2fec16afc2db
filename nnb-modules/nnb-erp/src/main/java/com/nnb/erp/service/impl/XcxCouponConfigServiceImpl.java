package com.nnb.erp.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.nnb.erp.mapper.XcxCouponConfigMapper;
import com.nnb.erp.domain.XcxCouponConfig;
import com.nnb.erp.service.IXcxCouponConfigService;

/**
 * 小程序优惠券配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-14
 */
@Service
public class XcxCouponConfigServiceImpl implements IXcxCouponConfigService {
    @Autowired
    private XcxCouponConfigMapper xcxCouponConfigMapper;

    /**
     * 查询小程序优惠券配置
     *
     * @param id 小程序优惠券配置主键
     * @return 小程序优惠券配置
     */
    @Override
    public XcxCouponConfig selectXcxCouponConfigById(Long id) {
        return xcxCouponConfigMapper.selectXcxCouponConfigById(id);
    }

    /**
     * 查询小程序优惠券配置列表
     *
     * @param xcxCouponConfig 小程序优惠券配置
     * @return 小程序优惠券配置
     */
    @Override
    public List<XcxCouponConfig> selectXcxCouponConfigList(XcxCouponConfig xcxCouponConfig) {
        return xcxCouponConfigMapper.selectXcxCouponConfigList(xcxCouponConfig);
    }

    /**
     * 新增小程序优惠券配置
     *
     * @param xcxCouponConfig 小程序优惠券配置
     * @return 结果
     */
    @Override
    public int insertXcxCouponConfig(XcxCouponConfig xcxCouponConfig) {
        return xcxCouponConfigMapper.insertXcxCouponConfig(xcxCouponConfig);
    }

    /**
     * 修改小程序优惠券配置
     *
     * @param xcxCouponConfig 小程序优惠券配置
     * @return 结果
     */
    @Override
    public int updateXcxCouponConfig(XcxCouponConfig xcxCouponConfig) {
        return xcxCouponConfigMapper.updateXcxCouponConfig(xcxCouponConfig);
    }

    /**
     * 批量删除小程序优惠券配置
     *
     * @param ids 需要删除的小程序优惠券配置主键
     * @return 结果
     */
    @Override
    public int deleteXcxCouponConfigByIds(List<Long> ids) {
        return xcxCouponConfigMapper.deleteXcxCouponConfigByIds(ids);
    }

    /**
     * 删除小程序优惠券配置信息
     *
     * @param id 小程序优惠券配置主键
     * @return 结果
     */
    @Override
    public int deleteXcxCouponConfigById(Long id) {
        return xcxCouponConfigMapper.deleteXcxCouponConfigById(id);
    }
}
