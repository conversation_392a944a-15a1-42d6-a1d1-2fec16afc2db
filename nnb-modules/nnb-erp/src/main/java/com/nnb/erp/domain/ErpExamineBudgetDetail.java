package com.nnb.erp.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 预算审批详情对象 erp_examine_budget_detail
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@ApiModel(value="ErpExamineBudgetDetail",description="预算审批详情对象")
public class ErpExamineBudgetDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /** erp_examine_budget.id */
    @Excel(name = "erp_examine_budget.id")
    @ApiModelProperty("erp_examine_budget.id")
    private Long budgetId;

    /** 预算月份 */
    @Excel(name = "预算月份")
    @ApiModelProperty("预算月份")
    private String budgetMonth;

    /** $column.columnComment */
    @Excel(name = "预算月份")
    @ApiModelProperty("$column.columnComment")
    private Long budgetCity;

    /** 项目1固定费用2不固定 */
    @Excel(name = "项目1固定费用2不固定")
    @ApiModelProperty("项目1固定费用2不固定")
    private Integer budgetProject;

    /** erp_budget_type.id */
    @Excel(name = "erp_budget_type.id")
    @ApiModelProperty("预算明细")
    private Integer budgetType;

    /** 预算部门 */
    @Excel(name = "预算部门")
    @ApiModelProperty("预算部门")
    private Long budgetDept;

    /** 单价 */
    @Excel(name = "单价")
    @ApiModelProperty("单价")
    private BigDecimal budgetUnitPrice;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty("数量")
    private Long budgetNumber;

    @ApiModelProperty("预算金额")
    private BigDecimal budgetFee;

    @ApiModelProperty("预算余额")
    private BigDecimal budgetBalance;

    /** 是否可管控1是2否 */
    @Excel(name = "是否可管控1是2否")
    @ApiModelProperty("是否可管控1是2否")
    private Integer budgetControl;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty("备注")
    private String budgetMemo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBudgetId(Long budgetId) 
    {
        this.budgetId = budgetId;
    }

    public Long getBudgetId() 
    {
        return budgetId;
    }
    public void setBudgetMonth(String budgetMonth) 
    {
        this.budgetMonth = budgetMonth;
    }

    public String getBudgetMonth() 
    {
        return budgetMonth;
    }
    public void setBudgetCity(Long budgetCity) 
    {
        this.budgetCity = budgetCity;
    }

    public Long getBudgetCity() 
    {
        return budgetCity;
    }
    public void setBudgetProject(Integer budgetProject) 
    {
        this.budgetProject = budgetProject;
    }

    public Integer getBudgetProject() 
    {
        return budgetProject;
    }
    public void setBudgetType(Integer budgetType)
    {
        this.budgetType = budgetType;
    }

    public Integer getBudgetType()
    {
        return budgetType;
    }
    public void setBudgetDept(Long budgetDept) 
    {
        this.budgetDept = budgetDept;
    }

    public Long getBudgetDept() 
    {
        return budgetDept;
    }
    public void setBudgetUnitPrice(BigDecimal budgetUnitPrice) 
    {
        this.budgetUnitPrice = budgetUnitPrice;
    }

    public BigDecimal getBudgetUnitPrice() 
    {
        return budgetUnitPrice;
    }
    public void setBudgetNumber(Long budgetNumber) 
    {
        this.budgetNumber = budgetNumber;
    }

    public Long getBudgetNumber() 
    {
        return budgetNumber;
    }
    public void setBudgetControl(Integer budgetControl) 
    {
        this.budgetControl = budgetControl;
    }

    public Integer getBudgetControl() 
    {
        return budgetControl;
    }
    public void setBudgetMemo(String budgetMemo) 
    {
        this.budgetMemo = budgetMemo;
    }

    public String getBudgetMemo() 
    {
        return budgetMemo;
    }

    public BigDecimal getBudgetFee() {
        return budgetFee;
    }

    public void setBudgetFee(BigDecimal budgetFee) {
        this.budgetFee = budgetFee;
    }

    public BigDecimal getBudgetBalance() {
        return budgetBalance;
    }

    public void setBudgetBalance(BigDecimal budgetBalance) {
        this.budgetBalance = budgetBalance;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("budgetId", getBudgetId())
            .append("budgetMonth", getBudgetMonth())
            .append("budgetCity", getBudgetCity())
            .append("budgetProject", getBudgetProject())
            .append("budgetType", getBudgetType())
            .append("budgetDept", getBudgetDept())
            .append("budgetUnitPrice", getBudgetUnitPrice())
            .append("budgetNumber", getBudgetNumber())
            .append("budgetControl", getBudgetControl())
            .append("budgetMemo", getBudgetMemo())
            .toString();
    }
}
