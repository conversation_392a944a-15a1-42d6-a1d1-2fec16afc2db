package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpApprovalFlowRole;

/**
 * 审核流程角色Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-23
 */
public interface IErpApprovalFlowRoleService 
{
    /**
     * 查询审核流程角色
     * 
     * @param id 审核流程角色主键
     * @return 审核流程角色
     */
    public ErpApprovalFlowRole selectErpApprovalFlowRoleById(Long id);

    /**
     * 查询审核流程角色列表
     * 
     * @param erpApprovalFlowRole 审核流程角色
     * @return 审核流程角色集合
     */
    public List<ErpApprovalFlowRole> selectErpApprovalFlowRoleList(ErpApprovalFlowRole erpApprovalFlowRole);

    /**
     * 新增审核流程角色
     * 
     * @param erpApprovalFlowRole 审核流程角色
     * @return 结果
     */
    public int insertErpApprovalFlowRole(ErpApprovalFlowRole erpApprovalFlowRole);

    /**
     * 修改审核流程角色
     * 
     * @param erpApprovalFlowRole 审核流程角色
     * @return 结果
     */
    public int updateErpApprovalFlowRole(ErpApprovalFlowRole erpApprovalFlowRole);

    /**
     * 批量删除审核流程角色
     * 
     * @param ids 需要删除的审核流程角色主键集合
     * @return 结果
     */
    public int deleteErpApprovalFlowRoleByIds(Long[] ids);

    /**
     * 删除审核流程角色信息
     * 
     * @param id 审核流程角色主键
     * @return 结果
     */
    public int deleteErpApprovalFlowRoleById(Long id);
}
