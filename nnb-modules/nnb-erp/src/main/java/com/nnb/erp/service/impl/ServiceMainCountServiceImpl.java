package com.nnb.erp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.erp.constant.MainCountConstants;
import com.nnb.erp.constant.ServiceMainConstants;
import com.nnb.erp.domain.*;
//import com.nnb.erp.domain.dto.ErpBizServiceAddressCostDto;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.domain.vo.service.ServiceEnterpriseVo;
import com.nnb.erp.mapper.ErpClientMapper;
import com.nnb.erp.mapper.SConfigServiceCatalogueMapper;
import com.nnb.erp.mapper.SConfigServiceTypeMapper;
import com.nnb.erp.mapper.ServiceMainCountMapper;
import com.nnb.erp.service.*;
import com.nnb.erp.util.DateUtil;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysDept;
import com.nnb.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-01-09
 * @Version: 1.0
 */
@Service
public class ServiceMainCountServiceImpl implements ServiceMainCountService {

    @Autowired
    private ServiceMainCountMapper serviceMainCountMapper;

    @Autowired
    private ISServiceMainService serviceMainService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IErpProductNameService iErpProductNameService;

    @Autowired
    private ISConfigServiceTypeStatusService isConfigServiceTypeStatusService;

    @Autowired
    private ISConfigServicePointStatusService isConfigServicePointStatusService;
    @Autowired
    private SConfigServiceTypeMapper sConfigServiceTypeMapper;

    @Autowired
    private ErpClientMapper erpClientMapper;

    @Override
    public Map<String, Object> mainCount(MainCountDto mainCountDto) {
        Map<String, Object> returnMap = new HashMap<>();

        setTimeToEn(mainCountDto);
        String typeStr = sConfigServiceTypeMapper.selectServiceTypeIdsByCatalogueIdList(mainCountDto.getServiceCatalogueIdList());
        String[] typeIds = typeStr.split(",");
        List<Long> typeList = new ArrayList<>();
        //排除业支-其他服务，创新-建筑资质
        for (int i = 0; i < typeIds.length; i++) {
            if (!typeIds[i].equals("22")) {
                typeList.add(Long.parseLong(typeIds[i]));
            }
        }
        mainCountDto.setServiceTypeList(typeList);

        //总计
        mainCountDto.setCardType(1);
        mainCountDto.setServiceStatusList(Arrays.asList(1L,2L,3L,4L,5L,7L,9L,10L));
        Map<String, Object> allMap = serviceMainCountMapper.selectMainCountCardType(mainCountDto);
        //流程中
        mainCountDto.setCardType(2);
        mainCountDto.setServiceStatusList(Arrays.asList(1L,2L,3L,7L));
        Map<String, Object> inMap = serviceMainCountMapper.selectMainCountCardType(mainCountDto);
        //搁置
        mainCountDto.setCardType(3);
        mainCountDto.setServiceStatusList(Arrays.asList(4L));
        List<Map<String, Object>> shelveList = serviceMainCountMapper.selectMainCountShelve(mainCountDto);

        mainCountDto.setCardType(4);
        mainCountDto.setServiceStatusList(Arrays.asList(5L,10L));
        Map<String, Object> refundMap = serviceMainCountMapper.selectMainCountCardType(mainCountDto);

        mainCountDto.setCardType(5);
        mainCountDto.setServiceStatusList(Arrays.asList(1L,2L,3L));
        Map<String, Object> finishMap = serviceMainCountMapper.selectMainCountCardType(mainCountDto);
        List<Map<String, Object>> finishTimeList = serviceMainCountMapper.selectMainCountFinishList(mainCountDto);
        Integer sameMonthCount = 0;
        BigDecimal sameMonthPrice = new BigDecimal("0");
        int finishDays = 0;
        for (int i = 0; i < finishTimeList.size(); i++) {
            if (ObjectUtil.isEmpty(finishTimeList.get(i).get("startTime")) || ObjectUtil.isEmpty(finishTimeList.get(i).get("endTime"))) {
                continue;
            }
            String startTime = finishTimeList.get(i).get("startTime").toString();
            String endTime = finishTimeList.get(i).get("endTime").toString();
            BigDecimal price = new BigDecimal(finishTimeList.get(i).get("payPrice").toString());
            finishDays += Integer.parseInt(finishTimeList.get(i).get("finishDays").toString());
            if (StringUtils.equals(startTime, endTime)) {
                sameMonthCount++;
                sameMonthPrice = sameMonthPrice.add(price);
            }
        }

        mainCountDto.setCardType(6);
        mainCountDto.setServiceStatusList(Arrays.asList(9L));
        Map<String, Object> cancelMap = serviceMainCountMapper.selectMainCountCardType(mainCountDto);

        //1：查询服务单个数   2：查询金额
        Map<String, Object> map = new HashMap<>();
        if (1 == mainCountDto.getResultTypeId()) {
            Integer all = Integer.parseInt(allMap.get("idCount").toString());
            Integer in = Integer.parseInt(inMap.get("idCount").toString());
            Integer shelve = 0;
            for (int i = 0; i < shelveList.size(); i++) {
                Map<String, Object> shelveMap = shelveList.get(i);
                shelve += Integer.parseInt(shelveMap.get("idCount").toString());
                returnMap.put("shelveId"+shelveMap.get("shelveId"), Integer.parseInt(shelveMap.get("idCount").toString()));
            }
            Integer refund = Integer.parseInt(refundMap.get("idCount").toString());
            Integer finish = Integer.parseInt(finishMap.get("idCount").toString());
            Integer cancel = Integer.parseInt(cancelMap.get("idCount").toString());

            BigDecimal refundRate = all == 0 ? new BigDecimal("0") :
                    new BigDecimal(refund.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);
            BigDecimal finishRate = all == 0 ? new BigDecimal("0") :
                    new BigDecimal(finish.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);
            BigDecimal shelveRate = all == 0 ? new BigDecimal("0") :
                    new BigDecimal(shelve.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);

//            .setScale(4, BigDecimal.ROUND_HALF_UP)

            returnMap.put("all", all);
            returnMap.put("in", in);
            returnMap.put("shelve", shelve);
            returnMap.put("refund", refund);
            returnMap.put("finish", finish);
            returnMap.put("sameMonth", sameMonthCount);
            returnMap.put("diffMonth", finish-sameMonthCount);
            returnMap.put("shelveRate", shelveRate);
            returnMap.put("refundRate", refundRate);
            returnMap.put("finishRate", finishRate);
            returnMap.put("cancel", cancel);
            returnMap.put("finishDays", finish==0?0:finishDays/finish);
        } else if (2 == mainCountDto.getResultTypeId()) {
            BigDecimal all = new BigDecimal(allMap.get("payPrice").toString());
            BigDecimal in = new BigDecimal(inMap.get("payPrice").toString());
            BigDecimal shelve = new BigDecimal("0");
            for (int i = 0; i < shelveList.size(); i++) {
                shelve = shelve.add(new BigDecimal(shelveList.get(i).get("payPrice").toString()));
            }
            BigDecimal refund = new BigDecimal(refundMap.get("payPrice").toString());
            BigDecimal finish = new BigDecimal(finishMap.get("payPrice").toString());
            BigDecimal cancel = new BigDecimal(cancelMap.get("payPrice").toString());

            BigDecimal refundRate = all.compareTo(new BigDecimal("0")) == 0 ? new BigDecimal("0") :
                    new BigDecimal(refund.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);
            BigDecimal finishRate = all.compareTo(new BigDecimal("0")) == 0 ? new BigDecimal("0") :
                    new BigDecimal(finish.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);
            BigDecimal shelveRate = all.compareTo(new BigDecimal("0")) == 0 ? new BigDecimal("0") :
                    new BigDecimal(shelve.toString()).multiply(new BigDecimal("100")).divide(new BigDecimal(all.toString()),4, BigDecimal.ROUND_HALF_UP);

            returnMap.put("all", all);
            returnMap.put("in", in);
            returnMap.put("shelve", shelve);
            returnMap.put("refund", refund);
            returnMap.put("finish", finish);
            returnMap.put("sameMonth", sameMonthPrice);
            returnMap.put("diffMonth", finish.subtract(sameMonthPrice));
            returnMap.put("shelveRate", shelveRate);
            returnMap.put("refundRate", refundRate);
            returnMap.put("finishRate", finishRate);
            returnMap.put("cancel", cancel);
        }
        return returnMap;
    }



    @Override
    public Map<String, Object> mainChartCount(MainCountDto mainCountDto) {

        Map<String, Object> result = new HashMap<>();
        //1：按日   2：按月   3：按年
        List<String> list = new ArrayList<>();
        LinkedList<String> times = new LinkedList<>();
        LinkedList<String> total = new LinkedList<>();
        LinkedList<String> finish = new LinkedList<>();
        String start = mainCountDto.getStartTime().split(" ")[0];
        String end = mainCountDto.getEndTime().split(" ")[0];
        //根据类型匹配
        if (1 == mainCountDto.getQueryTypeId()) {
            list = DateUtil.getBetweenDate(start, end, MainCountConstants.DAY);
        }
        if (2 == mainCountDto.getQueryTypeId()) {
            list = DateUtil.getBetweenDate(start, end, MainCountConstants.MONTH);
        }
        if (3 == mainCountDto.getQueryTypeId()) {
            list = DateUtil.getBetweenDate(start, end, MainCountConstants.YEAR);
        }

        setTimeToEn(mainCountDto);
        String typeStr = sConfigServiceTypeMapper.selectServiceTypeIdsByCatalogueIdList(mainCountDto.getServiceCatalogueIdList());
        String[] typeIds = typeStr.split(",");
        List<Long> typeList = new ArrayList<>();
        //排除业支-其他服务，创新-建筑资质
        for (int i = 0; i < typeIds.length; i++) {
            if (!typeIds[i].equals("22")) {
                typeList.add(Long.parseLong(typeIds[i]));
            }
        }
        mainCountDto.setServiceTypeList(typeList);

        //查询数据
        mainCountDto.setCardType(1);
        mainCountDto.setServiceStatusList(Arrays.asList(1L,2L,3L,4L,5L,7L,9L,10L));
        List<Map<String, Object>> totalList = serviceMainCountMapper.selectMainChartTotal(mainCountDto);
        mainCountDto.setCardType(5);
        mainCountDto.setServiceStatusList(Arrays.asList(1L,2L,3L));
        List<Map<String, Object>> finishList = serviceMainCountMapper.selectMainChartTotal(mainCountDto);

        for (String date : list) {
            List<Map<String, Object>> totalCollect = totalList.stream().filter(map -> date.equals(MapUtil.getStr(map, "time"))).collect(Collectors.toList());
            List<Map<String, Object>> finishCollect = finishList.stream().filter(map -> date.equals(MapUtil.getStr(map, "time"))).collect(Collectors.toList());
            //total
            if (CollUtil.isEmpty(totalCollect)) {
                total.add("0");
            } else {
                Map<String, Object> map = totalCollect.get(0);
                total.add(MapUtil.getStr(map, "num"));
            }
            //finish
            if (CollUtil.isEmpty(finishCollect)) {
                finish.add("0");
            } else {
                Map<String, Object> map = finishCollect.get(0);
                finish.add(MapUtil.getStr(map, "num"));
            }
            //times
            times.add(date);
        }
        result.put("serviceCountList", total);
        result.put("serviceFinishList", finish);
        result.put("dateList", times);
        return result;
    }

    @Override
    public List<SServiceVo> mainCountList(MainCountDto mainCountDto) {
        setTimeToEn(mainCountDto);
        List<SServiceVo> list = serviceMainCountMapper.selectListCardType(mainCountDto);

        String productIds = "";
        String userIds = "";
        String pointStatusIds = "";
        String clientIds = "";
        if (!ObjectUtil.isEmpty(list) && list.size() > 0) {
            R<SysUser> info = remoteUserService.getUserInfoById(SecurityUtils.getUserId(), SecurityConstants.INNER);
            for (SServiceVo vo : list) {
                if (!ObjectUtil.isEmpty(vo.getClientId()) && !(clientIds + ",").contains("," + vo.getClientId() + ",")) {
                    clientIds += "," + vo.getClientId();
                }

                if (!ObjectUtil.isEmpty(vo.getProductId()) && !(productIds + ",").contains("," + vo.getProductId() + ",")) {
                    productIds += "," + vo.getProductId();
                }

                if (!ObjectUtil.isEmpty(vo.getServicePointStatus()) && !(pointStatusIds + ",").contains("," + vo.getServicePointStatus() + ",")) {
                    pointStatusIds += "," + vo.getServicePointStatus();
                }

                if (!ObjectUtil.isEmpty(vo.getRenewStatus()) && !(pointStatusIds + ",").contains("," + vo.getRenewStatus() + ",")) {
                    pointStatusIds += "," + vo.getRenewStatus();
                }

                if (!ObjectUtil.isEmpty(vo.getAccountUserId()) && !(userIds + ",").contains("," + vo.getAccountUserId() + ",")) {
                    userIds += "," + vo.getAccountUserId();
                }

                if (!ObjectUtil.isEmpty(vo.getIncrementUserId()) && !(userIds + ",").contains("," + vo.getIncrementUserId() + ",")) {
                    userIds += "," + vo.getIncrementUserId();
                }

                if (!ObjectUtil.isEmpty(vo.getServerUserId()) && !(userIds + ",").contains("," + vo.getServerUserId() + ",")) {
                    userIds += "," + vo.getServerUserId();
                }

                if (!ObjectUtil.isEmpty(vo.getSellerId()) && !(userIds + ",").contains("," + vo.getSellerId() + ",")) {
                    userIds += "," + vo.getSellerId();
                }

                if (!ObjectUtil.isEmpty(vo.getServerBeforeUserId()) && !(userIds + ",").contains("," + vo.getServerBeforeUserId() + ",")) {
                    userIds += "," + vo.getServerBeforeUserId();
                }

                if (!ObjectUtil.isEmpty(vo.getServerAfterUserId()) && !(userIds + ",").contains("," + vo.getServerAfterUserId() + ",")) {
                    userIds += "," + vo.getServerAfterUserId();
                }

                if (!ObjectUtil.isEmpty(vo.getShelveId())) {
                    vo.setShelveName(ServiceMainConstants.ServiceShelve.get(vo.getShelveId() + ""));
                }
                if (ObjectUtil.isNotEmpty(vo.getCreatedTime()) && ObjectUtil.isNotEmpty(vo.getToIncrementTime())) {
                    vo.setFinishInCycle(DateUtil.getDays(vo.getToIncrementTime(), vo.getCreatedTime()));
                }
            }
        }

        if (ObjectUtil.isNotEmpty(userIds)) {
            R<List<SysUser>> userListR = remoteUserService.getUserListByIds(userIds.replaceFirst(",", ""), SecurityConstants.INNER);
            List<SysUser> userList = userListR.getData();

            for (int i = 0; i < list.size(); i++) {
                SServiceVo sServiceVo = list.get(i);
                for (int j = 0; j < userList.size(); j++) {
                    SysUser sysUser = userList.get(j);
                    if (ObjectUtil.isNotEmpty(sServiceVo.getSellerId()) && sysUser.getUserId().intValue() == sServiceVo.getSellerId().intValue()) {
                        sServiceVo.setSellerName(sysUser.getNickName());
                        sServiceVo.setDepName(sysUser.getDept().getDeptName());
                    }
                    if (ObjectUtil.isNotEmpty(sServiceVo.getServerBeforeUserId()) && sysUser.getUserId().intValue() == sServiceVo.getServerBeforeUserId().intValue()) {
                        sServiceVo.setServerBeforeUserName(sysUser.getNickName());
                    }
                    if (ObjectUtil.isNotEmpty(sServiceVo.getServerAfterUserId()) && sysUser.getUserId().intValue() == sServiceVo.getServerAfterUserId().intValue()) {
                        sServiceVo.setServerAfterUserName(sysUser.getNickName());
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(productIds)) {
            List<ProductServiceName> productServiceNameList = iErpProductNameService.getServiceNameByProductIds(productIds.replaceFirst(",", ""));
            if (!ObjectUtil.isEmpty(productServiceNameList)) {
                for (ProductServiceName productServiceName : productServiceNameList) {
                    for (int i = 0; i < list.size(); i++) {
                        SServiceVo sServiceVo = list.get(i);
                        if (!ObjectUtil.isEmpty(sServiceVo.getProductId()) && sServiceVo.getProductId().intValue() == productServiceName.getProductId()) {
                            sServiceVo.setProductName(productServiceName.getServiceName());
                        }
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(pointStatusIds)) {
            Map<Long, PointStatusAttribute> servicePointStatusMap = isConfigServicePointStatusService.getAttributeByPointStatusId(pointStatusIds.replaceFirst(",", ""));
            if (!ObjectUtil.isEmpty(servicePointStatusMap)) {
                for (int i = 0; i < list.size(); i++) {
                    SServiceVo sServiceVo = list.get(i);
                    if (!ObjectUtil.isEmpty(sServiceVo.getServicePointStatus())) {
                        sServiceVo.setServiceTypeName(servicePointStatusMap.get(sServiceVo.getServicePointStatus()).getTypeName());
                        sServiceVo.setServiceCatalogueName(servicePointStatusMap.get(sServiceVo.getServicePointStatus()).getCatalogueName());
                        sServiceVo.setServicePointName(servicePointStatusMap.get(sServiceVo.getServicePointStatus()).getPointName());
                        sServiceVo.setServicePointStatusName(servicePointStatusMap.get(sServiceVo.getServicePointStatus()).getPointStatusName());
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(clientIds)) {
            List<ServiceEnterpriseVo> serviceEnterpriseVoList = erpClientMapper.serviceGetEnterpriseByIds(clientIds.replaceFirst(",", ""));
            Map<Long, ServiceEnterpriseVo> map = serviceEnterpriseVoList.stream().collect(Collectors.toMap(ServiceEnterpriseVo::getErpClientId, Function.identity(), (key1, key2) -> key2));
            for (SServiceVo vo : list) {
                if (!ObjectUtil.isEmpty(vo.getClientId())) {
                    ServiceEnterpriseVo serviceEnterpriseVo = map.get(vo.getClientId());
                    if (Objects.nonNull(serviceEnterpriseVo)) {
                        vo.setVcCompanyName(serviceEnterpriseVo.getVcCompanyName());
                        vo.setVcHistoryName(serviceEnterpriseVo.getVcHistoryName());
                        vo.setLegalPersonName(serviceEnterpriseVo.getLegalPersonName());
                        vo.setErpEnterpriseDetailId(serviceEnterpriseVo.getErpEnterpriseDetailId());
                        vo.setContactName(serviceEnterpriseVo.getContactName());
                        vo.setContactPhone(serviceEnterpriseVo.getContactPhone());
                        vo.setDatAppointmentAccountTime(serviceEnterpriseVo.getDatAppointmentAccountTime());
                        vo.setDatCrtAccountTime(serviceEnterpriseVo.getDatCrtAccountTime());
                        vo.setVcBankDeposit(serviceEnterpriseVo.getVcBankDeposit());
                        vo.setVcAccount(serviceEnterpriseVo.getVcAccount());
                        vo.setDatTaxReportDate(serviceEnterpriseVo.getDatTaxReportDate());
                        vo.setVcDutyParagraph(serviceEnterpriseVo.getVcDutyParagraph());
                        vo.setDatTaxControlIssueDate(serviceEnterpriseVo.getDatTaxControlIssueDate());
                        vo.setDatTaxControlCustodyTime(serviceEnterpriseVo.getDatTaxControlCustodyTime());
                        vo.setNumCorporatePropertyId(serviceEnterpriseVo.getNumCorporatePropertyId());
                    }
                }
            }
        }
        return list;
    }

    private void setTimeToEn(MainCountDto mainCountDto) {
//        if (StringUtils.isNotEmpty(mainCountDto.getStartTime()) && StringUtils.isNotEmpty(mainCountDto.getEndTime())){
//            mainCountDto.setStartTime(mainCountDto.getStartTime() + " 00:00:00");
//            mainCountDto.setEndTime(mainCountDto.getEndTime() + " 23:59:59");
//        }
    }
}
