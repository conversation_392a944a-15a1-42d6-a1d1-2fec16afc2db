package com.nnb.erp.service.impl.inventory;

import com.nnb.erp.domain.inventory.IaStorageDetail;
import com.nnb.erp.mapper.inventory.IaStorageDetailMapper;
import com.nnb.erp.mapper.inventory.IaStorageMapper;
import com.nnb.erp.service.inventory.IaStorageDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 存货出/入库明细Service业务层处理
 * @Date: 2023-12-26
 * @Version: 1.0
 */
@Service
public class IaStorageDetailServiceImpl implements IaStorageDetailService {

    @Autowired
    private IaStorageMapper iaStorageMapper;

    @Autowired
    private IaStorageDetailMapper iaStorageDetailMapper;

    /**
     * 查询存货出/入库明细
     *
     * @param id 存货出/入库明细主键
     * @return 存货出/入库明细
     */
    @Override
    public IaStorageDetail selectIaStorageDetailById(Long id) {
        return iaStorageDetailMapper.selectIaStorageDetailById(id);
    }

    /**
     * 查询存货出/入库明细列表
     *
     * @param iaStorageDetail 存货出/入库明细
     * @return 存货出/入库明细
     */
    @Override
    public List<IaStorageDetail> selectIaStorageDetailList(IaStorageDetail iaStorageDetail) {
        return iaStorageDetailMapper.selectIaStorageDetailList(iaStorageDetail);
    }

    /**
     * 新增存货出/入库明细
     *
     * @param iaStorageDetail 存货出/入库明细
     * @return 结果
     */
    @Override
    public int insertIaStorageDetail(IaStorageDetail iaStorageDetail) {
        return iaStorageDetailMapper.insertIaStorageDetail(iaStorageDetail);
    }

    /**
     * 修改存货出/入库明细
     *
     * @param iaStorageDetail 存货出/入库明细
     * @return 结果
     */
    @Override
    public int updateIaStorageDetail(IaStorageDetail iaStorageDetail) {
        return iaStorageDetailMapper.updateIaStorageDetail(iaStorageDetail);
    }

    /**
     * 批量删除存货出/入库明细
     *
     * @param ids 需要删除的存货出/入库明细主键
     * @return 结果
     */
    @Transactional
    @Override
    public Boolean deleteIaStorageDetailByIds(List<Long> ids) {
        //删除子集
        return iaStorageDetailMapper.deleteIaStorageDetailByIds(ids) > 0;
    }

    /**
     * 删除存货出/入库明细信息
     *
     * @param id 存货出/入库明细主键
     * @return 结果
     */
    @Override
    public int deleteIaStorageDetailById(Long id) {
        return iaStorageDetailMapper.deleteIaStorageDetailById(id);
    }
}
