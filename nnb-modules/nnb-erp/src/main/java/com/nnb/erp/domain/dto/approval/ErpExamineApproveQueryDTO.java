package com.nnb.erp.domain.dto.approval;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.erp.domain.ErpExamineApprove;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpExamineApproveQueryDTO extends ErpExamineApprove {
    @ApiModelProperty("1:我发起的审批，2：待我审批，3：审批通过列表，4：抄送列表，5：打印列表")
    private Integer type;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("执照编号")
    private String licenseNumber;

    @ApiModelProperty("是否已付款，0：否，1：是")
    private Integer isPayment;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("提交开始时间")
    private Date createdDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("提交截止时间")
    private Date createdDateEnd;

    @ApiModelProperty("申请人")
    private Long applyer;

    /** 补录状态，0：待审批，1：审批通过，2：驳回 */
    @ApiModelProperty("补录状态，0：待审批，1：审批通过，2：驳回")
    private Integer supplementStatus;

    @ApiModelProperty("打印状态，打印状态1待打印2已打印")
    private Integer printStatus;

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("审批ID")
    private Long id;

    @ApiModelProperty("1:待审批，2：已审批")
    private Integer statusType;
}
