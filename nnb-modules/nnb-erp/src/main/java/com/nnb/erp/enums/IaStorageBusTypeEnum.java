package com.nnb.erp.enums;

import com.nnb.erp.converter.inventory.strategy.BeginConverterStrategy;
import com.nnb.erp.converter.inventory.strategy.SalesConverterStrategy;

/**
 * @Author: Chen-xy
 * @Description:
 * @Date: 2023-12-26
 * @Version: 1.0
 */
public enum IaStorageBusTypeEnum {

    OPENING_RECEIPT(1, "期初入库", "OPENING_RECEIPT"),
    PURCHASE_RECEIPT(2, "采购入库", "PURCHASE_RECEIPT"),
    REQUISITION_AND_OUTBOUND(3, "领用出库", "REQUISITION_AND_OUTBOUND"),
    SALES_OUTBOUND(4, "销售出库", "SALES_OUTBOUND"),

    REQUISITION_SALES(5, "领用出库和销售出库和销售退货", "REQUISITION_SALES"),

    PURCHASE_RETURN(6, "采购退货", "PURCHASE_RETURN"),
    SALES_RETURNS(7, "销售退货", "SALES_RETURNS"),

    RECEIPT_RETURN(8, "采购入库和采购退货", "SALES_RETURNS");


    private Integer code;
    private String type;
    private String busType;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    IaStorageBusTypeEnum(Integer code, String type, String busType) {
        this.code = code;
        this.type = type;
        this.busType = busType;
    }

    public static String getTypeByCode(Integer code){
        for (IaStorageBusTypeEnum busTypeEnum : IaStorageBusTypeEnum.values()) {
            if (busTypeEnum.getCode().equals(code)){
                return busTypeEnum.getType();
            }
        }
        return null;
    }

    public static String getBusTypeByCode(Integer code){
        for (IaStorageBusTypeEnum busTypeEnum : IaStorageBusTypeEnum.values()) {
            if (busTypeEnum.getCode().equals(code)){
                return busTypeEnum.getBusType();
            }
        }
        return null;
    }
}
