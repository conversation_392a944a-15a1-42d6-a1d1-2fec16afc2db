package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.ErpExamineBudgetDetailConsume;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-02
 */
public interface ErpExamineBudgetDetailConsumeMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ErpExamineBudgetDetailConsume selectErpExamineBudgetDetailConsumeById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param erpExamineBudgetDetailConsume 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ErpExamineBudgetDetailConsume> selectErpExamineBudgetDetailConsumeList(ErpExamineBudgetDetailConsume erpExamineBudgetDetailConsume);

    /**
     * 新增【请填写功能名称】
     * 
     * @param erpExamineBudgetDetailConsume 【请填写功能名称】
     * @return 结果
     */
    public int insertErpExamineBudgetDetailConsume(ErpExamineBudgetDetailConsume erpExamineBudgetDetailConsume);

    /**
     * 修改【请填写功能名称】
     * 
     * @param erpExamineBudgetDetailConsume 【请填写功能名称】
     * @return 结果
     */
    public int updateErpExamineBudgetDetailConsume(ErpExamineBudgetDetailConsume erpExamineBudgetDetailConsume);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteErpExamineBudgetDetailConsumeById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpExamineBudgetDetailConsumeByIds(Long[] ids);

    public List<ErpExamineBudgetDetailConsume> selectByDetailIdList(@Param("detailIdList") List<Long> detailIdList);
}
