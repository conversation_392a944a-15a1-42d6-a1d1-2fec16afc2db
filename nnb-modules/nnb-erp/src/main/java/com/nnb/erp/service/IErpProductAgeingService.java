package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpProductAgeing;
import com.nnb.erp.domain.vo.ErpProductAgeingVo;
import io.swagger.annotations.ApiParam;
import net.sf.json.JSONArray;

/**
 * 产品时效Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface IErpProductAgeingService 
{
    /**
     * 查询产品时效
     * 
     * @param id 产品时效主键
     * @return 产品时效
     */
    public ErpProductAgeing selectErpProductAgeingById(Long id);

    /**
     * 查询产品时效列表
     * 
     * @param erpProductAgeing 产品时效
     * @return 产品时效集合
     */
    public List<ErpProductAgeingVo> selectErpProductAgeingList(ErpProductAgeing erpProductAgeing);

    /**
     * 新增产品时效
     * 
     * @param erpProductAgeing 产品时效
     * @return 结果
     */
    public int insertErpProductAgeing(ErpProductAgeing erpProductAgeing);

    /**
     * 修改产品时效
     * 
     * @param erpProductAgeing 产品时效
     * @return 结果
     */
    public int updateErpProductAgeing(ErpProductAgeing erpProductAgeing);

    /**
     * 批量删除产品时效
     * 
     * @param ids 需要删除的产品时效主键集合
     * @return 结果
     */
    public int deleteErpProductAgeingByIds(Long[] ids);

    /**
     * 删除产品时效信息
     * 
     * @param id 产品时效主键
     * @return 结果
     */
    public int deleteErpProductAgeingById(Long id);

    public JSONArray getPointStatusTwo(Long serviceTypeId);
}
