package com.nnb.erp.controller;

import java.util.Date;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.nnb.erp.domain.dto.XcxScoreingAccountDto;
import com.nnb.erp.domain.vo.XcxScoreingAccountVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.erp.domain.XcxScoreingAccount;
import com.nnb.erp.service.IXcxScoreingAccountService;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
@RestController
@RequestMapping("/xcxScoreingAccount")
@Api(tags = "XcxScoreingAccountController", description = "【请填写功能名称】")
public class XcxScoreingAccountController extends BaseController
{
    @Autowired
    private IXcxScoreingAccountService xcxScoreingAccountService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation(value = "查询【请填写功能名称】列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = XcxScoreingAccount.class)})
    @GetMapping("/list")
    public TableDataInfo list(XcxScoreingAccountDto xcxScoreingAccount)
    {
        startPage();
        List<XcxScoreingAccountVo> list = xcxScoreingAccountService.selectXcxScoreingAccountList(xcxScoreingAccount);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation(value = "导出【请填写功能名称】列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, XcxScoreingAccountDto xcxScoreingAccount) throws IOException
    {
        List<XcxScoreingAccountVo> list = xcxScoreingAccountService.selectXcxScoreingAccountList(xcxScoreingAccount);
        ExcelUtil<XcxScoreingAccountVo> util = new ExcelUtil<XcxScoreingAccountVo>(XcxScoreingAccountVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation(value = "获取【请填写功能名称】详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = XcxScoreingAccount.class)})
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="【请填写功能名称】id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(xcxScoreingAccountService.selectXcxScoreingAccountById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation(value = "新增【请填写功能名称】")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody XcxScoreingAccount xcxScoreingAccount)
    {
        return toAjax(xcxScoreingAccountService.insertXcxScoreingAccount(xcxScoreingAccount));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation(value = "修改【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:account:edit")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody XcxScoreingAccount xcxScoreingAccount)
    {
        return toAjax(xcxScoreingAccountService.updateXcxScoreingAccount(xcxScoreingAccount));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation(value = "删除【请填写功能名称】")
    @PreAuthorize(hasPermi = "erp:account:remove")
    //@Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(xcxScoreingAccountService.deleteXcxScoreingAccountByIds(ids));
    }

    @GetMapping("/getCompanyNamesByPhone")
    public AjaxResult getCompanyNamesByPhone(@RequestParam String phone) {
        return AjaxResult.success("操作成功", xcxScoreingAccountService.getCompanyNamesByPhone(phone));
    }
}
