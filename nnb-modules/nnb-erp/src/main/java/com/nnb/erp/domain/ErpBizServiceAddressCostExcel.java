package com.nnb.erp.domain;

import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpBizServiceAddressCostExcel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @Excel(name = "代理商名称(必填)")
    private String agentNumber;
    @Excel(name = "代理商类型(必填)")
    private String agentTypeName;
    @Excel(name = "产品名称(必填)")
    private String detailAddress;
    @Excel(name = "产品ID")
    private Long costNameId;
    @Excel(name = "产品类型(必填)")
    private String costTypeName;
    @Excel(name = "区域(必填)")
    private String registerAreaName;
    @Excel(name = "联系人(必填)")
    private String contactName;
    @Excel(name = "联系人手机号(必填)")
    private String contactPhone;
    @Excel(name = "供货周期(必填)")
    private String supplyCycle;
    @Excel(name = "供货量(必填)")
    private String supplyNumber;
    @Excel(name = "结算类型(必填)")
    private String typePriceName;
    @Excel(name = "价格(必填)")
    private BigDecimal endPrice;
    @Excel(name = "等级(必填)")
    private String level;
    @Excel(name = "地址(必填)")
    private String address;
    @Excel(name = "备注(必填)")
    private String memo;
    @Excel(name = "合作状态(必填)")
    private String cooperateStatusName;
    @Excel(name = "合作开始时间(必填)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cooperateTime;
    @Excel(name = "停止合作时间(必填)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date stopCoopreateTime;
    @Excel(name = "停止合作原因(必填)")
    private String stopCoopreateWhy;
    @Excel(name = "银行名称(必填)")
    private String bankName;
    @Excel(name = "银行卡号(必填)")
    private String bankNumber;
    @Excel(name = "收款人姓名(必填)")
    private String payee;
}
