package com.nnb.erp.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品详情VO
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ErpProductDetailListVo implements Serializable {


    private static final long serialVersionUID = 9126513555031209890L;

    /** ID */
    @Excel(name = "产品ID")
    private Long numProductId;

    /** 产品分类 */
    @Excel(name = "产品分类")
    private String vcClassificationName;

    /** 产品分类 */
    @Excel(name = "产品分类")
    private Long numClassificationId;

    /** 产品类型 */
    @Excel(name = "产品类型")
    private String vcTypeName;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String vcProductName;

    /** 服务单类型 */
    @Excel(name = "服务单类型 ")
    private String serviceName;

    /** 纳税类型 */
    @Excel(name = "纳税类型")
    private String vcTaxNames;

    /** 单位 */
    @Excel(name = "单位")
    private String vcUnitName;

    /** 城市/区域 */
    @Excel(name = "城市/区域")
    private String vcAreaNames;

    /** 价格*/
    @Excel(name = "价格")
    private BigDecimal numPrice;

    /** 状态*/
    @Excel(name = "状态",readConverterExp = "0=下架,1=上架")
    private Integer numIsUp;

    /** 审批状态*/
    @Excel(name = "审批状态",readConverterExp = "0=未审批,1=已审批")
    private Integer numIsCheck;

    /** 创建时间*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datCreateTime;

    /** 备注*/
    @Excel(name = "备注信息")
    private String vcemark;

    /**
     * 产品名称id
     */
    private Long numNameId;

    /**
     * 服务单类型Id
     */
    private Long serviceTypeId;


    /**
     * 产品类型id
     */
    private Long typeId;

    /**
     * 自定义服务类型
     */
    @Excel(name = "自定义服务类型")
    private String vcServiceName;

    private String activityStartTime;

    private String activityEndTime;

    private BigDecimal discountAmount;

    private Integer licenseNumber;

    @Excel(name = "成本价格")
    private BigDecimal costPrice;

    @Excel(name = "外采成本")
    private BigDecimal outCost;

    @Excel(name = "计件提成")
    private BigDecimal numberCommissionPrice;

    @Excel(name = "记账提成")
    private BigDecimal accountCommissionPrice;

    @Excel(name = "带户提成")
    private BigDecimal leadCustomCommissionPrice;

    @Excel(name = "销售提成")
    private BigDecimal saleCommissionPrice;

    @Excel(name = "增值提成")
    private BigDecimal incrementCommissionPrice;

    @Excel(name = "地址续费提成")
    private BigDecimal addressCommissionPrice;

    @Excel(name = "总成本")
    private BigDecimal allCost;

    @ApiModelProperty("计件提成")
    private BigDecimal numberCommission;
    @ApiModelProperty("计件提成类型1数值2比例")
    private Integer numberCommissionType;

    @ApiModelProperty("记账提成")
    private BigDecimal accountCommission;
    @ApiModelProperty("记账提成类型1数值2比例")
    private Integer accountCommissionType;

    @ApiModelProperty("带户提成")
    private BigDecimal leadCustomCommission;
    @ApiModelProperty("带户提成类型1数值2比例")
    private Integer leadCustomCommissionType;

    @ApiModelProperty("销售提成")
    private BigDecimal saleCommission;
    @ApiModelProperty("销售提成类型1数值2比例")
    private Integer saleCommissionType;

    @ApiModelProperty("增值提成")
    private BigDecimal incrementCommission;
    @ApiModelProperty("增值提成类型1数值2比例")
    private Integer incrementCommissionType;

    @ApiModelProperty("地址续费提成")
    private BigDecimal addressCommission;
    @ApiModelProperty("地址续费提成类型1数值2比例")
    private Integer addressCommissionType;

    private Long configurationId;

    private Long approveInfoId;

}
