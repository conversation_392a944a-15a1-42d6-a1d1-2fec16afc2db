package com.nnb.erp.domain.dto.service;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class ServiceMainDto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Excel(name = "企业名称")
    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty("是否合规账1是2否")
    private Integer complianceAccount;

    private List<Long> enterpriseIdList;

    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactName;

    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    @Excel(name = "法人")
    @ApiModelProperty("法人")
    private String legalPersonName;

    @Excel(name = "城市")
    @ApiModelProperty("城市")
    private Long cityId;
    private List<Long> cityIdList;

    @Excel(name = "签约部门")
    @ApiModelProperty("签约部门")
    private Long sellerDeptId;

    @ApiModelProperty("签约部门List")
    private List<Long> sellerDeptIdList;

    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String orderNum;

    @Excel(name = "纳税类型")
    @ApiModelProperty("纳税类型")
    private Long corporatePropertyId;

    @Excel(name = "纳税类型")
    @ApiModelProperty("纳税类型")
    private List<Long> corporatePropertyIdList;

    @Excel(name = "节点状态")
    @ApiModelProperty("节点状态")
    private Long servicePointStatus;

    @ApiModelProperty("节点状态")
    private List<Long> servicePointStatusList;

    @Excel(name = "地址开始日")
    @ApiModelProperty("地址开始日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date adderssBeginBegin;

    @Excel(name = "地址开始日")
    @ApiModelProperty("地址开始日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date adderssBeginEnd;

    @Excel(name = "地址截止日")
    @ApiModelProperty("地址截止日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date adderssEndBegin;

    @Excel(name = "地址截止日")
    @ApiModelProperty("地址截止日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date adderssEndEnd;

    @Excel(name = "销售")
    @ApiModelProperty("销售")
    private Long sellerId;

    @Excel(name = "销售")
    @ApiModelProperty("销售")
    private List<Long> sellerIdList;

    @Excel(name = "税控托管开始开始")
    @ApiModelProperty("税控托管开始开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date TaxControBeginBegin;

    @Excel(name = "税控托管开始开始")
    @ApiModelProperty("税控托管开始开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date TaxControBeginEnd;

    @Excel(name = "税控托管开始开始")
    @ApiModelProperty("税控托管开始开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date TaxControEndBegin;

    @Excel(name = "税控托管开始开始")
    @ApiModelProperty("税控托管开始开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date TaxControEndEnd;

    @Excel(name = "税号")
    @ApiModelProperty("税号")
    private String vcDutyParagraph;

    @Excel(name = "记账服务类型")
    @ApiModelProperty("记账服务类型")
    private Long keepAcountTypeId;

    @Excel(name = "下执照开始")
    @ApiModelProperty("下执照开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date permitTimeBegin;

    @Excel(name = "下执照结束")
    @ApiModelProperty("下执照结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date permitTimeEnd;

    @Excel(name = "记账开始")
    @ApiModelProperty("记账开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date AcountTimeEndBegin;

    @Excel(name = "记账结束")
    @ApiModelProperty("记账结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date AcountTimeEndEnd;

    @Excel(name = "记账结束")
    @ApiModelProperty("记账结束")
    private Long isAddress;

    @Excel(name = "记账结束")
    @ApiModelProperty("记账结束")
    private Long chekRole;

    private String dataScope;
    @Excel(name = "转增值")
    @ApiModelProperty("转增值时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date incrementTimeBegin;

    @Excel(name = "转增值结束时间")
    @ApiModelProperty("转增值结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date incrementTimeEnd;

    @Excel(name = "转增值前的服务单类型集合")
    @ApiModelProperty("转增值前的服务单类型集合")
    private String typeBeforeZzIds;

    @Excel(name = "产品名称模糊查询")
    @ApiModelProperty("产品名称模糊查询")
    private String vcServiceName;

    @Excel(name = "执照编号")
    @ApiModelProperty("执照编号")
    private String licenseNumber;

    @Excel(name = "完结时间")
    @ApiModelProperty("完结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishInTimeBegin;

    @Excel(name = "完结时间")
    @ApiModelProperty("完结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishInTimeEnd;

    @Excel(name = "完结周期")
    @ApiModelProperty("完结周期")
    private Integer finishInCycle;

    @ApiModelProperty(name = "产品大类")
    private List<Long> productClassificationIdList;

    @ApiModelProperty(name = "历史会计")
    private Long historicalAccounting;

    @ApiModelProperty(name = "续费状态1已续费2未续费")
    private Long renewStatus;

    @ApiModelProperty(name = "流失原因")
    private List<Long> accountLossReasonList;

    @ApiModelProperty("条件城市筛选")
    private Long cityIdCondition;

    @ApiModelProperty("类型  1： 查询 ，2 ：导出")
    private Integer type;

    /** $column.columnComment */
    @ApiModelProperty("$column.columnComment")
    private Long id;

    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String ids;

    private String serviceTypes;



    /** 联系人名称 */
    @Excel(name = "联系人名称")
    @ApiModelProperty("联系人名称")
    private String contactUserName;

    /** 销售 */


    /** 是否记账 */
    @Excel(name = "是否记账")
    @ApiModelProperty("是否记账")
    private Long isAccount;

    /** 服务类别 */
    @Excel(name = "服务类别")
    @ApiModelProperty("服务类别")
    private Long serviceCatalogue;

    /** 服务类型 */
    @Excel(name = "服务类型")
    @ApiModelProperty("服务类型")
    private Long serviceType;

    /** 服务状态 */
    @Excel(name = "服务状态")
    @ApiModelProperty("服务状态")
    private Long serviceStatus;

    /** 服务节点 */
    @Excel(name = "服务节点")
    @ApiModelProperty("服务节点")
    private Long servicePoint;



    /** 是否资质 */
    @Excel(name = "是否资质")
    @ApiModelProperty("是否资质")
    private Long isQualifications;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司记账等级")
    private List<String> companyLevelList;



    /** 区域 */
    @Excel(name = "区域")
    @ApiModelProperty("区域")
    private Long regionId;
    private List<Long> regionIdList;



    /** 签约时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("签约时间")
    private Date signTime;

    /** 到达业支时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到达业支时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("到达业支时间")
    private Date createdTime;

    /** 签约时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("签约时间")
    private Date signTimeBegin;

    /** 签约时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("签约时间")
    private Date signTimeEnd;

    /** 到达业支时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到达业支时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("到达业支时间")
    private Date createdTimeBegin;

    /** 到达业支时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到达业支时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("到达业支时间")
    private Date createdTimeEnd;

    /** 到达业支时间 */

    /** 业支 */
    @Excel(name = "业支")
    @ApiModelProperty("业支")
    private Long serverUserId;
    private List<Long> serverUserIdList;

    /** 产品 */
    @Excel(name = "产品")
    @ApiModelProperty("产品")
    private Long productId;

    /** 增值 */
    @Excel(name = "增值")
    @ApiModelProperty("增值")
    private Long incrementUserId;

    /** 会计 */
    @Excel(name = "会计")
    @ApiModelProperty("会计")
    private Long accountUserId;
    @Excel(name = "会计")
    @ApiModelProperty("会计")
    private List<Long> accountUserIdList;

    /** 有无尾款 */
    @Excel(name = "有无尾款")
    @ApiModelProperty("有无尾款")
    private Long hasTailFee;

    /** 供应商 */
    @Excel(name = "供应商")
    @ApiModelProperty("供应商")
    private Long addrestCostId;

    /** 结算状态 */
    @Excel(name = "结算状态")
    @ApiModelProperty("结算状态")
    private Long settleStatus;

    /** 搁置原因 */
    @Excel(name = "搁置原因")
    @ApiModelProperty("搁置原因")
    private Long shelveId;

    @ApiModelProperty("搁置类型")
    private Long shelveType;

    /** 工作台 */
    @Excel(name = "工作台")
    @ApiModelProperty("工作台")
    private Long workPlatformId;

    /** 订单注册注册服务是否完成 */
    @Excel(name = "订单注册注册服务是否完成")
    @ApiModelProperty("订单注册注册服务是否完成")
    private Long orderRegistOver;

    /** 注册完成后可以进行的服务 */
    @Excel(name = "注册完成后可以进行的服务")
    @ApiModelProperty("注册完成后可以进行的服务")
    private String orderRegistServer;

    /** 工作台 */
    @Excel(name = "BD状态")
    @ApiModelProperty("BD状态")
    private Long isBd;

    @Excel(name = "BD状态")
    @ApiModelProperty("BD状态")
    private Long isAftermarket;

    /** 服务单当前跟进人 */
    @Excel(name = "服务单当前跟进人")
    @ApiModelProperty("服务单当前跟进人")
    private Long userId;

    /** 服务单当前跟进人 */
    @Excel(name = "注销服务类型")
    @ApiModelProperty("注销服务类型")
    private Long logOffType;

    @Excel(name = "deptId")
    @ApiModelProperty("deptId")
    private Long deptId;

    @Excel(name = "enterpriseServiceStatus")
    @ApiModelProperty("enterpriseServiceStatus")
    private Long enterpriseServiceStatus;

    private List<Long> deptIdList;

    @ApiModelProperty("服务单IDList")
    private List<Long> sServiceMainIds;

    @ApiModelProperty("操作类型 1：会计绩效跳转至记账客户管理")
    private Integer operateType;

    @ApiModelProperty("是否分配跟进人1未分配2已分配")
    private Integer userIdIsNull;

    @ApiModelProperty("收款金额最低")
    private BigDecimal collectionFeeMin;

    @ApiModelProperty("收款金额最高")
    private BigDecimal collectionFeeMax;

    @ApiModelProperty("是否已核税")
    private Long vcNuclearTax;

    private Long clientId;

    @ApiModelProperty("工商年检1已申报2未申报")
    private Long yearInspect;

    private Long dataHandoverStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("orderNum", getOrderNum())
                .append("contactUserName", getContactUserName())
                .append("sellerId", getSellerId())
                .append("isAccount", getIsAccount())
                .append("serviceCatalogue", getServiceCatalogue())
                .append("serviceType", getServiceType())
                .append("serviceStatus", getServiceStatus())
                .append("servicePoint", getServicePoint())
                .append("servicePointStatus", getServicePointStatus())
                .append("isQualifications", getIsQualifications())
                .append("companyName", getCompanyName())
                .append("cityId", getCityId())
                .append("regionId", getRegionId())
                .append("signTime", getSignTime())
                .append("createdTime", getCreatedTime())
                .append("serverUserId", getServerUserId())
                .append("productId", getProductId())
                .append("incrementUserId", getIncrementUserId())
                .append("hasTailFee", getHasTailFee())
                .append("addrestCostId", getAddrestCostId())
                .append("settleStatus", getSettleStatus())
                .append("shelveId", getShelveId())
                .append("workPlatformId", getWorkPlatformId())
                .append("orderRegistOver", getOrderRegistOver())
                .append("orderRegistServer", getOrderRegistServer())
                .toString();
    }
}
