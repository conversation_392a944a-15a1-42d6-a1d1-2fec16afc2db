package com.nnb.erp.mapper.inventory;

import com.nnb.erp.domain.inventory.IaInventoryWarehouse;
import com.nnb.erp.domain.inventory.InventoryCount;
import com.nnb.erp.domain.inventory.InventoryStockSearch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: Chen-xy
 * @Description: 仓库存货库存Mapper接口
 * @Date: 2024-01-09
 * @Version: 1.0
 */
public interface IaInventoryWarehouseMapper {

    /**
     * 查询仓库存货库存
     *
     * @param id 仓库存货库存主键
     * @return 仓库存货库存
     */
    public IaInventoryWarehouse selectIaInventoryWarehouseById(Long id);

    /**
     * 查询仓库存货库存列表
     *
     * @param iaInventoryWarehouse 仓库存货库存
     * @return 仓库存货库存集合
     */
    public List<IaInventoryWarehouse> selectIaInventoryWarehouseList(IaInventoryWarehouse iaInventoryWarehouse);

    /**
     * 新增仓库存货库存
     *
     * @param iaInventoryWarehouse 仓库存货库存
     * @return 结果
     */
    public int insertIaInventoryWarehouse(IaInventoryWarehouse iaInventoryWarehouse);

    /**
     * 修改仓库存货库存
     *
     * @param iaInventoryWarehouse 仓库存货库存
     * @return 结果
     */
    public int updateIaInventoryWarehouse(IaInventoryWarehouse iaInventoryWarehouse);

    /**
     * 删除仓库存货库存
     *
     * @param id 仓库存货库存主键
     * @return 结果
     */
    public int deleteIaInventoryWarehouseById(Long id);

    /**
     * 批量删除仓库存货库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIaInventoryWarehouseByIds(@Param("ids") List<Long> ids);

    /**
     * 查询仓库存货库存列表（区间）
     *
     * @param inventoryStockSearch 仓库存货库存
     * @return 仓库存货库存集合
     */
    public List<IaInventoryWarehouse> selectIntervalList(InventoryStockSearch inventoryStockSearch);

    /**
     * 根据存货 定时更新 采购价，市场价，总数
     * @param iaInventoryWarehouse iaInventoryWarehouse
     * @return int
     */
    public int updatePriceAndSum(IaInventoryWarehouse iaInventoryWarehouse);

    /**
     * 根据存货id和仓库id更新数量
     * @param iaInventoryWarehouse iaInventoryWarehouse
     * @return int
     */
    public int updateQtyByInventoryIdAndWarehouseId(IaInventoryWarehouse iaInventoryWarehouse);

    /**
     * 统计总存货量
     * @param showStock showStock
     * @return InventoryCount
     */
    InventoryCount countInventory(@Param("showStock") Integer showStock);
}
