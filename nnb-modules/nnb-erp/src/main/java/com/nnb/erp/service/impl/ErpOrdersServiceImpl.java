package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.api.constants.ApiConstants;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.SpringUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.common.core.web.domain.BaseEntity;
import com.nnb.common.datascope.annotation.DataScope;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.config.TlOpenApiPropertiesConfig;
import com.nnb.erp.constant.ErpLicenseConstants;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.erp.constant.OperatingConstants;
import com.nnb.erp.constant.ServiceMainConstants;
import com.nnb.erp.constant.enums.*;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.approval.ErpNewApproval;
import com.nnb.erp.domain.approval.ErpOrderPaymentRecord;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.dto.CollectionReportDto;
import com.nnb.erp.domain.dto.ErpTransactionVoucherFollowDto;
import com.nnb.erp.domain.dto.SaleListingDto;
import com.nnb.erp.domain.dto.approval.ErpNewApprovalDTO;
import com.nnb.erp.domain.gift.ErpGift;
import com.nnb.erp.domain.gift.ErpGiftIssueRecord;
import com.nnb.erp.domain.gift.ErpOrderGift;
import com.nnb.erp.domain.order.ErpOrderRefundCostExpenditure;
import com.nnb.erp.domain.order.ErpServicePersonApproval;
import com.nnb.erp.domain.tLOrder.*;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.approval.ErpOrderRefundDetailVo;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponOrderVo;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftDetailVO;
import com.nnb.erp.domain.vo.qzd.LicenseSalesBoardVo;
import com.nnb.erp.domain.vo.report.SalesListingVo;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.enums.ApprovalStatusEnum;
import com.nnb.erp.enums.ApprovalTypeEnum;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.approval.ErpNewApprovalMapper;
import com.nnb.erp.mapper.approval.ErpQzdPaymentRecordMapper;
import com.nnb.erp.mapper.gift.ErpGiftIssueRecordMapper;
import com.nnb.erp.mapper.gift.ErpGiftMapper;
import com.nnb.erp.mapper.gift.ErpOrderGiftMapper;
import com.nnb.erp.mapper.order.ErpOrderRefundCostExpenditureMapper;
import com.nnb.erp.mapper.order.ErpServicePersonApprovalMapper;
import com.nnb.erp.service.*;
import com.nnb.erp.service.approval.IErpNewApprovalService;
import com.nnb.erp.util.HttpClientUtil;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysRole;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.BdClue;
import com.nnb.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Slf4j
@Service
public class ErpOrdersServiceImpl implements IErpOrdersService {
    @Resource
    private ErpOrdersMapper erpOrdersMapper;
    @Resource
    private ErpServiceOrdersMapper erpServiceOrdersMapper;
    @Resource
    private ErpRetainageReturnMapper erpRetainageReturnMapper;
    @Resource
    private ErpCommitOrderServiceImpl erpCommitOrderService;
    @Resource
    private IErpOrderOperatingRecordService erpOrderOperatingRecordService;
    @Resource
    private IErpServiceOrderRefundService erpServiceOrderRefundService;
    @Resource
    private IErpCouponUseRecordService erpCouponUseRecordService;
    @Resource
    private IErpBizMainInfoService erpBizMainInfoService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Resource
    private TokenService tokenService;
    @Resource
    private IErpServiceOrdersService erpServiceOrdersService;
    @Resource
    private IErpOrderTypeService erpOrderTypeService;

    @Resource
    private SServiceMainMapper sServiceMainMapper;

    @Resource
    private ErpRetainageReturnDetailMapper erpRetainageReturnDetailMapper;

    @Value("${params.xcx_member_up_secret_key}")
    public String xcxMemberUpSecretKey;

    @Resource
    public ErpOrderPaymentTermInfoMapper erpOrderPaymentTermInfoMapper;

    @Autowired
    private ErpClientMapper erpClientMapper;

    @Autowired
    private ErpDiscountCouponMapper erpDiscountCouponMapper;
    @Autowired
    private ErpDiscountCouponAmountMapper erpDiscountCouponAmountMapper;

    @Autowired
    private ErpContractMapper erpContractMapper;
    @Resource
    private OnlineContractMapper onlineContractMapper;

    @Resource
    private ISServiceMainService serviceMainService;

    @Value("${old.system.url}")
    public String url;

    @Autowired
    private ErpServicePersonApprovalMapper erpServicePersonApprovalMapper;

    @Autowired
    private SConfigServiceCatalogueMapper sConfigServiceCatalogueMapper;

    @Autowired
    private ErpOrderPaymentTermMapper erpOrderPaymentTermMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SConfigServicePointStatusMapper sConfigServicePointStatusMapper;

    @Autowired
    private ErpTLOrderService erpTLOrderService;

    @Autowired
    private ErpTLOrderMapper erpTLOrderMapper;

    @Autowired
    private ErpServiceOrderRefundMapper erpServiceOrderRefundMapper;

    @Autowired
    private TlOpenApiPropertiesConfig tlOpenApiPropertiesConfig;

    @Resource
    private IErpNewApprovalService erpNewApprovalService;

    @Autowired
    private ErpNewApprovalMapper erpNewApprovalMapper;

    @Autowired
    private ErpLicenseMapper erpLicenseMapper;

    @Autowired
    private ErpCommitOrderMapper commitOrderMapper;

    @Autowired
    private IErpContractService erpContractService;


    @Autowired
    private ErpProductConfigurationMapper erpProductConfigurationMapper;

    @Autowired
    private SConfigServicePointMapper sConfigServicePointMapper;

    @Autowired
    private ErpOrderRefundMapper erpOrderRefundMapper;

    @Autowired
    private ErpOrdersOperateRecordsMapper erpOrdersOperateRecordsMapper;

    @Autowired
    private ErpEnterpriseMapper erpEnterpriseMapper;

    @Autowired
    private ErpOrderGiftMapper erpOrderGiftMapper;

    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;

    @Autowired
    private ErpGiftMapper erpGiftMapper;

    @Autowired
    private ErpExamineApproveMapper erpExamineApproveMapper;

    @Autowired
    private ErpOrderRefundCostExpenditureMapper erpOrderRefundCostExpenditureMapper;

    @Autowired
    private ErpTransactionVoucherFollowMapper erpTransactionVoucherFollowMapper;
    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;
    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;
    @Autowired
    private ErpOrderPerformanceMapper erpOrderPerformanceMapper;

    @Value("${erp.order.transactionVoucher.orderCreateTime}")
    public String voucherOrderCreateTime;

    @Autowired
    private ErpTransactionVoucherFollowServiceImpl transactionVoucherFollowService;
    @Autowired
    private DingDingService dingDingService;
    @Autowired
    private ErpQzdPaymentRecordMapper erpQzdPaymentRecordMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ISServiceQualificationsExtensionService serviceQualificationsExtensionService;
    @Autowired
    private ErpWtdzKpMapper erpWtdzKpMapper;
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public ErpOrders selectErpOrdersById(Long id) {
        return erpOrdersMapper.selectErpOrdersById(id);
    }

    /**
     * 查询订单列表
     *
     * @param erpOrders 订单
     * @return 订单
     */
    @Override
    public List<ErpOrders> selectErpOrdersList(ErpOrders erpOrders) {
        return erpOrdersMapper.selectErpOrdersList(erpOrders);
    }

    /**
     * 新增订单
     *
     * @param erpOrders 订单
     * @return 结果
     */
    @Override
    public int insertErpOrders(ErpOrders erpOrders) {
        return erpOrdersMapper.insertErpOrders(erpOrders);
    }

    /**
     * 修改订单
     *
     * @param erpOrders 订单
     * @return 结果
     */
    @Override
    public int updateErpOrders(ErpOrders erpOrders) {
        return erpOrdersMapper.updateErpOrders(erpOrders);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteErpOrdersByIds(Long[] ids) {
        return erpOrdersMapper.deleteErpOrdersByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteErpOrdersById(Long id) {
        return erpOrdersMapper.deleteErpOrdersById(id);
    }

    /**
     * 获取订单列表，用于订单管理列表展示。
     *
     * @param query 筛选条件。
     * @return 返回订单列表。
     * <AUTHOR>
     * @since 2022-03-24 11:26:46
     */
    @Override
    @DataScope(deptAlias = "sd", userAlias = "u")
    public List<ErpOrderInfoForOmListVO> getOrderListForOm(ErpOrderQueryForOmListDTO query) {

        ////log.info("订单列表查询条件::{},{}", new Date(), JSONObject.toJSONString(query));
        query.setDeptId(null);
        List<ErpOrderInfoForOmListVO> resList = erpOrdersMapper.getOrderListForOm(query);
        ////log.info("订单列表查询订单信息为::{},{}", new Date(), resList);
        //构造订单审核状态
        if (CollectionUtils.isNotEmpty(resList)) {
            resList.forEach(val -> {
                Integer numValidStatus = val.getNumValidStatus();
                if ((Objects.nonNull(numValidStatus) && 0 != numValidStatus)) {
                    String nameByType = OrderInvalidStatusEnum.getNameByType(numValidStatus);
                    val.setOrderAuditStatusStr(nameByType);
                } else {
                    if (Objects.nonNull(val.getNumModifyOrderExamineStatus()) && (0 != val.getNumModifyOrderExamineStatus())) {
                        String numModifyOrderExamineStatusStr = OrderApprovalStatusEnum.getNameByType(val.getNumModifyOrderExamineStatus());
                        val.setOrderAuditStatusStr(numModifyOrderExamineStatusStr);
                    } else if (Objects.nonNull(val.getNumCancelOrderExamineStatus()) && (0 != val.getNumCancelOrderExamineStatus())) {
                        String numCancelOrderExamineStatusStr = OrderApprovalStatusEnum.getNameByType(val.getNumCancelOrderExamineStatus());
                        val.setOrderAuditStatusStr(numCancelOrderExamineStatusStr);
                    } else {
                        Integer numCreateOrderExamineStatus = val.getNumCreateOrderExamineStatus();
                        String nameByType = OrderApprovalStatusEnum.getNameByType(numCreateOrderExamineStatus);
                        val.setOrderAuditStatusStr(nameByType);
                    }
                }
                if (StringUtils.isNotEmpty(val.getOrderAuditStatusStr()) && "null".equals(val.getOrderAuditStatusStr())) {
                    val.setOrderAuditStatusStr(null);
                }
                //显示联系人
                if ((Objects.nonNull(val.getCommitOrderType()) && 2 == val.getCommitOrderType()) || (Objects.isNull(val.getClueId())) || (Objects.nonNull(val.getClueId()) && (0 == val.getClueId()))) {
                    val.setClueCustomerName(val.getContactName());
                    val.setClueCustomerPhone(val.getContactPhone());
                }
                //驳回电子合同的时候将
                //实退款金额
                getOrderRefundPayAmount(val);
            });
        }
        List<Long> collect = resList.stream().map(ErpOrderInfoForOmListVO::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            query.setOrderIdList(collect);
        }

        List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
        //log.info("订单列表查询电子合同信息为::{},{}", new Date(), onlieContractByOrderIdList);

        //查询合同号
        Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
            map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
            //log.info("订单列表查询分组后的电子合同信息为::{},{}", new Date(), map);
        }

//        //是否记账
//        List<Long> JiZhangServiceList = new ArrayList<>();
//        String ids = resList.stream().map(ErpOrderInfoForOmListVO::getEnterpriseId).map(String::valueOf).collect(Collectors.joining(","));
//        if (StringUtils.isNotEmpty(ids)) {
//            JiZhangServiceList = serviceMainService.getCountIdsByIdList(ids, ServiceMainConstants.JiZhangService);
//        }
        List<Long> orderList = resList.stream().map(ErpOrderInfoForOmListVO::getOrderId).collect(Collectors.toList());
        if (orderList.size() > 0) {
            List<ErpRetainageForOmListVO> retainageByOrderIds = erpOrdersMapper.getRetainageByOrderIds(orderList);
            List<ErpOrderStatusVO> orderStatusList = this.getOrderStatusList(orderList);
            List<ErpServiceOrderRefundVO> refundListByOrderIdAndStatus = erpServiceOrderRefundService.getRefundListByOrderIdAndStatuList(orderList, null, OrderRefundApprovalStatusEnum.REFUND_PASS.getStatusType());
            for (ErpOrderInfoForOmListVO orderInfo : resList) {
                if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(orderInfo.getIsElectronicContract()) && 1 == orderInfo.getIsElectronicContract())) {
                    if (map.containsKey(orderInfo.getOrderId())) {
                        List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(orderInfo.getOrderId());
                        if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                            StringBuffer vcContractNumber = new StringBuffer();
//                        String vcContractNumber = "";
                            int size = erpOrderInfoForOmListVOS.size();
                            for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                                if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
//                                vcContractNumber = erpOrderInfoForOmListVO.getVcContractNumber();
                                } else {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
//                                vcContractNumber = erpOrderInfoForOmListVO.getVcContractNumber() + "，";
                                }
                            }
                            orderInfo.setVcContractNumber(String.valueOf(vcContractNumber));
                        }
                    }

                }
                // 获取订单状态信息。
//            orderInfo.setOrderStatusInfo(this.getOrderStatus(orderInfo.getOrderId()));
//            orderInfo.setOrderStatusTypeStr(orderInfo.getOrderStatusInfo().getOrderStatusName());
                // 获取订单已通过审核的退款信息，去退款金额总和填充到列表内。
//            List<ErpServiceOrderRefundVO> refundListByOrderIdAndStatus = erpServiceOrderRefundService.getRefundListByOrderIdAndStatus(orderInfo.getOrderId(), null, OrderRefundApprovalStatusEnum.REFUND_PASS.getStatusType());
                if (BigDecimal.ZERO.compareTo(orderInfo.getSumRefundPrice()) == 0){
                    List<ErpServiceOrderRefundVO> collect3 = refundListByOrderIdAndStatus.stream().filter(val -> val.getOrderId().equals(orderInfo.getOrderId())).collect(Collectors.toList());
                    orderInfo.setSumRefundPrice(collect3.stream().map(ErpServiceOrderRefundVO::getRefundPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
//            //log.info("订单列表查询退款信息为::{},{}", new Date(), refundListByOrderIdAndStatus);
//            orderInfo.setSumRefundPrice(refundListByOrderIdAndStatus.stream().map(ErpServiceOrderRefundVO::getRefundPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                // 填充订单回款记录信息。
//            orderList.add(orderInfo.getOrderId());
//            List<ErpRetainageForOmListVO> retainage = erpOrdersMapper.getRetainageByOrderId(orderInfo.getOrderId());
//            orderInfo.setRetainages(retainage);
//            //log.info("订单列表查询回款记录信息为::{}, {}", new Date(), retainage);
//            retainageEnumFill(orderInfo.getRetainages());
                List<ErpRetainageForOmListVO> collect1 = retainageByOrderIds.stream().filter(en -> en.getNumOrderId().equals(orderInfo.getOrderId())).collect(Collectors.toList());
                orderInfo.setRetainages(collect1);
                retainageEnumFill(orderInfo.getRetainages());


//            if (CollUtil.isNotEmpty(JiZhangServiceList)){
//                if (JiZhangServiceList.contains(orderInfo.getEnterpriseId())) {
//                    orderInfo.setIsAccount(1);
//                } else {
//                    orderInfo.setIsAccount(0);
//                }
//            }
                List<ErpOrderStatusVO> collect2 = orderStatusList.stream().filter(val -> val.getOrderId().equals(orderInfo.getOrderId())).collect(Collectors.toList());
                for (ErpOrderStatusVO erpOrderStatusVO : collect2) {
                    orderInfo.setOrderStatusInfo(erpOrderStatusVO);
                    orderInfo.setOrderStatusTypeStr(orderInfo.getOrderStatusInfo().getOrderStatusName());
                }
            }
            List<ErpOrderPaymentTerm> termList = erpOrderPaymentTermMapper.selectErpOrderPaymentTermByOrderIdList(orderList);
            for (int i = 0; i < resList.size(); i++) {
                ErpOrderInfoForOmListVO vo = resList.get(i);
                for (int j = 0; j < termList.size(); j++) {
                    ErpOrderPaymentTerm term = termList.get(j);
                    if (term.getNumOrderId().intValue() == vo.getOrderId().intValue()) {
                        vo.setCollectionTime(term.getDatCollectionTime());
                        break;
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(query.getKpSearch()) && query.getKpSearch().intValue() == 1) {
            checkOrderCanKp(resList);
        }
        return resList;
    }


    /**
     * 获取实退款金额
     * @param erpOrderInfoForOmListVO
     */
    public void getOrderRefundPayAmount(ErpOrderInfoForOmListVO erpOrderInfoForOmListVO) {
        erpOrderInfoForOmListVO.setRefundPayPrice(BigDecimal.ZERO);
        BigDecimal refunded = erpExamineApproveMapper.getRefunded(erpOrderInfoForOmListVO.getOrderId());
        //BigDecimal financeRefunded = erpExamineApproveMapper.getFinanceRefunded(erpOrderInfoForOmListVO.getOrderId());
        BigDecimal lastFinanceRefunded = erpExamineApproveMapper.getLastFinanceRefunded(erpOrderInfoForOmListVO.getOrderId());
        if (Objects.nonNull(lastFinanceRefunded)) {
            erpOrderInfoForOmListVO.setRefundPayPrice(Objects.nonNull(lastFinanceRefunded) ? refunded.subtract(lastFinanceRefunded) : refunded);
        }
    }

    /**
     * 获取订单各个状态的统计数据。
     *
     * @return 返回订单各个状态的统计数据。
     * <AUTHOR>
     * @since 2022-03-25 11:15:34
     */
    @Override
    @DataScope(deptAlias = "sd", userAlias = "u")
    public ErpOrderStatusCountVO getOrderStatusCount(ErpOrderQueryForOmListDTO query) {
        return erpOrdersMapper.getOrderListForOmCount(query);
    }

    /**
     * 获取指定订单内产品信息。
     *
     * @param orderId 订单标识。
     * @return 返回产品信息。
     * <AUTHOR>
     * @since 2022-04-09 17:59:52
     */
    @Override
    public List<ErpProductForOrderDetailVO> getProductsForOrderDetail(Long orderId) {
        return erpOrdersMapper.getProductsForOrderDetail(orderId);
    }

    /**
     * 获取订单详情。
     *
     * @param orderId 订单标识。
     * @return 返回订单详情。
     * <AUTHOR>
     * @since 2022-03-27 17:36:44
     */
    @Override
    public ErpOrderDetailForOmVO getOrderDetailForOm(Long orderId, Long interfaceType, Integer getUseVoucher, Integer balanceUseBack) {
        ErpOrderDetailForOmVO orderDetail = new ErpOrderDetailForOmVO();

        if (ObjectUtil.isEmpty(orderId)) {
            throw new ServiceException("订单详情：orderId为空");
        }
        orderDetail.setClient(erpOrdersMapper.getClientForOrderDetailByOrderId(orderId));
        if (Objects.nonNull(orderDetail.getClient())) {
            ErpClientForOrderDetailVO client = orderDetail.getClient();
            if ((Objects.nonNull(client.getCommitOrderType()) && 2 == client.getCommitOrderType())
                    || (Objects.isNull(client.getClueId()))
                    || (Objects.nonNull(client.getClueId()) && (0 == client.getClueId()))) {
                client.setClueCustomerName(client.getContactName());
                client.setClueCustomerPhone(client.getContactPhone());
            }
            orderDetail.setClient(client);
        }
        orderDetail.setOrder(erpOrdersMapper.getOrderForOrderDetailByOrderId(orderId));
        //从redis中获取验证码
        String phone = orderDetail.getOrder().getPhone();
        String verificationCode = stringRedisTemplate.opsForValue().get("contract:valid:" + phone);
        if (StrUtil.isNotBlank(verificationCode)){
            //"5271"
            assert verificationCode != null;
            orderDetail.getOrder().setVerificationCode(verificationCode.replace("\"", ""));
        }


        //订单编辑时判断是否是首次编辑订单,是否纸质和空白合同
        if(Objects.nonNull(interfaceType) && 2 == interfaceType){
            isFirstEditOrder(orderDetail);
        }
        //订单修改经理审核或财务审核时订单详情的合同类型取修改后的合同类型
        if(Objects.nonNull(interfaceType) && (3 == interfaceType || 4 == interfaceType)){
            setElectronicContract(orderDetail);
        }


        orderDetail.getOrder().setSumPrice(orderDetail.getOrder().getTotalPrice().add(orderDetail.getOrder().getDiscountAmount()));
        //查询产品信息
        List<ErpProductForOrderDetailVO> productsForOrderDetail = erpOrdersMapper.getProductsForOrderDetail(orderId);
        //查询优惠券信息
        List<Long> couponIds = productsForOrderDetail.stream().map(ErpProductForOrderDetailVO::getCouponId).collect(Collectors.toList());
        List<ErpDiscountCoupon> erpDiscountCoupons = new ArrayList<>();
        if (CollUtil.isNotEmpty(couponIds)) {
            erpDiscountCoupons = erpDiscountCouponMapper.selectErpDiscountCouponByIds(couponIds);
        }
        //填充部门产品是否需要合同
        erpCommitOrderService.setProductIsNoNeedProduct(2, null,  productsForOrderDetail);
        //放入优惠券信息
        setCouponToEn(productsForOrderDetail, erpDiscountCoupons);
        //税控产品放入税控时间

//        for (ErpProductForOrderDetailVO erpProductForOrderDetailVO : productsForOrderDetail) {
//            if (ObjectUtil.isNotNull(erpProductForOrderDetailVO.getServiceTypeId())
//                    && ObjectUtil.isNotNull(orderDetail.getOrder().getAcStart()) && ObjectUtil.isNotNull(orderDetail.getOrder().getAcEnd())
//                    && erpProductForOrderDetailVO.getServiceTypeId().intValue() == ServiceMainConstants.ShuiKongService) {
//                erpProductForOrderDetailVO.setAcStart(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderDetail.getOrder().getAcStart()));
//                erpProductForOrderDetailVO.setAcEnd(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderDetail.getOrder().getAcEnd()));
//            }
//        }
        orderDetail.setProducts(productsForOrderDetail);
        //查询收款信息
        List<ErpPaymentForOmListVO> commitPaymentsByOrderId = erpOrdersMapper.getCommitPaymentsByOrderId(orderId);
        for (ErpPaymentForOmListVO erpPaymentForOmListVO : commitPaymentsByOrderId) {
            List<ErpPaymentTermForOmListVO> terms = erpPaymentForOmListVO.getTerms();
            for (ErpPaymentTermForOmListVO term : terms) {
                String paymentUrl = term.getPaymentUrl();
                if (Objects.nonNull(paymentUrl)) {
                    String[] split = paymentUrl.split("\\?", 0);
                    String s = null;
                    for (int i = 0; i < split.length; i++) {
                        s = split[0];
                    }
                    term.setPaymentUrl(s);
                }
            }
        }
        orderDetail.setCommitPayments(commitPaymentsByOrderId);
        List<ErpContractVvo> onlieContractByOrderId = erpOrdersMapper.getOnlieContractByOrderId(orderId);
        if (Objects.nonNull(onlieContractByOrderId)) {
            for (ErpContractVvo erpContractVvo : onlieContractByOrderId) {
                orderDetail.getOrder().setContractNumber(erpContractVvo.getContractNumber());
                orderDetail.getOrder().setContractId(erpContractVvo.getContractId());
            }

        }

        List<ErpProductForConfirmOrderVO> productForCouponAndCombinedList = new ArrayList<>();
        List<ErpProductForConfirmOrderDTO> productBaseForCouponAndCombinedList = new ArrayList<>();
        orderDetail.getProducts().forEach(product -> {
            try {
                ErpProductForConfirmOrderVO erpProductForConfirmOrderVO = new ErpProductForConfirmOrderVO();
                erpProductForConfirmOrderVO.setProductId(product.getProductId());
                erpProductForConfirmOrderVO.setUnitPrice(product.getUnitPrice());
                erpProductForConfirmOrderVO.setClassificationId(product.getClassificationId());
                erpProductForConfirmOrderVO.setCount(product.getProductCount());
                productForCouponAndCombinedList.add(erpProductForConfirmOrderVO);

                ErpProductForConfirmOrderDTO erpProductForConfirmOrderDTO = new ErpProductForConfirmOrderDTO();
                erpProductForConfirmOrderDTO.setProductId(product.getProductId());
                erpProductForConfirmOrderDTO.setProductCount(product.getProductCount());
                productBaseForCouponAndCombinedList.add(erpProductForConfirmOrderDTO);

                BigDecimal unitPrice = BigDecimal.ZERO;
                if (Objects.nonNull(product.getUnitPrice())) {
                    unitPrice = product.getUnitPrice();
                }
                product.setSumPrice(unitPrice.multiply(new BigDecimal(product.getProductCount())));

                product.setRefundInfos(erpServiceOrderRefundService.getRefundListByOrderIdAndStatus(orderId, product.getServiceOrderId(), null));
            } catch (Exception e) {
                log.error("订单详情获取产品异常，异常信息为:", e);
            }
        });
//        // 获取产品可用优惠券。
//        Integer commitOrderType = null;
//        for (ErpProductForOrderDetailVO product : orderDetail.getProducts()) {
//            commitOrderType = product.getCommitOrderType();
//        }
//
//        erpCommitOrderService.couponInfoFill(productForCouponAndCombinedList, productBaseForCouponAndCombinedList,
//                orderDetail.getOrder().getOrderSourceType(), orderDetail.getOrder().getPhone(),
//                Objects.nonNull(orderDetail.getClient())
//                        ? orderDetail.getClient().getClueId()
//                        : null,
//                Objects.nonNull(orderDetail.getClient())
//                        ? orderDetail.getClient().getClientId()
//                        : null,
//                commitOrderType, interfaceType);
//        // 获取产品可用组合。
//
////        erpCommitOrderService.combinedInfoFill(productForCouponAndCombinedList, productBaseForCouponAndCombinedList);
//        Map<Long, ErpProductForConfirmOrderVO> couponAndCombinedMap = new HashMap<>();
//        productForCouponAndCombinedList.forEach(productForCouponAndCombined -> {
//            couponAndCombinedMap.put(productForCouponAndCombined.getProductId(), productForCouponAndCombined);
//        });
//        // 填充优惠券与组合到返回结果。
//        orderDetail.getProducts().forEach(product -> {
//            product.setCombinations(couponAndCombinedMap.get(product.getProductId()).getCombinations());
//            //product.setDiscounts(couponAndCombinedMap.get(product.getProductId()).getDiscounts());
//            product.setErpDiscountCoupons(couponAndCombinedMap.get(product.getProductId()).getErpDiscountCoupons());
//        });
        //尾款回款信息
        orderDetail.setRetainages(erpOrdersMapper.getRetainageByOrderId(orderDetail.getOrder().getOrderId()));
        retainageEnumFill(orderDetail.getRetainages()); //回款状态填充
        Map<Long, BigDecimal> retainageDiscountPriceMap = new HashMap<>();
        orderDetail.getRetainages().forEach(retainage -> { //尾款信息
            try {
                retainage.setRetainageDetails(erpOrdersMapper.getRetainageDetailByRetainageId(retainage.getRetainageId()));
                retainage.getRetainageDetails().forEach(retainageDetail -> {
                    Long serviceOrderId = retainageDetail.getServiceOrderId();
                    if (ObjectUtil.isNull(retainageDiscountPriceMap.get(serviceOrderId))) {
                        retainageDiscountPriceMap.put(serviceOrderId, new BigDecimal("0"));
                    }
                    retainageDiscountPriceMap.put(serviceOrderId, retainageDiscountPriceMap.get(serviceOrderId).add(retainageDetail.getDiscounts()));
                });
            } catch (Exception e) {
                log.error("订单详情获取尾款信息异常，异常信息为：", e);
            }
        });

        orderDetail.getProducts().forEach(product -> {
            product.setRetainageDiscountPrice(ObjectUtil.isNull(retainageDiscountPriceMap.get(product.getServiceOrderId())) ? new BigDecimal("0") : retainageDiscountPriceMap.get(product.getServiceOrderId()));
        });

//        orderDetail.setCommitPayments(erpOrdersMapper.getCommitPaymentsByOrderId(orderDetail.getOrder().getOrderId()));

        //财务尾款时间
        String financeTime = orderDetail.getOrder().getFinanceTime();
        orderDetail.getCommitPayments().forEach(i -> {
            i.getTerms().forEach(term -> {
                term.setPaymentName(PaymentEnum.getNameByType(term.getPaymentType()));
            });
            if (i.getTerms().size() > 0 && i.getTerms() != null) {
                i.setPaymentName(i.getTerms().get(i.getTerms().size() - 1).getPaymentName());
                i.setCollectionPrice(i.getTerms().stream().map(ErpPaymentTermForOmListVO::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (ObjectUtil.isNull(financeTime)) {
                    //回款财务收款时间设置为收款时间
                    i.setFinanceTime(i.getCollectionTime());
                } else {
                    i.setFinanceTime(financeTime);
                }
            }
        });

        List<ErpPaymentForOmListVO> summaryPayments = new ArrayList<>(orderDetail.getCommitPayments());
        orderDetail.getRetainages().forEach(retainage -> {
            for (ErpPaymentForOmListVO payment : retainage.getPayments()) {
                for (ErpPaymentTermForOmListVO term : payment.getTerms()) {
                    String paymentUrl = term.getPaymentUrl();
                    if (Objects.nonNull(paymentUrl)) {
                        String[] split = paymentUrl.split("\\?", 0);

                        String s = null;
                        for (int i = 0; i < split.length; i++) {
                            s = split[0];
                        }
                        term.setPaymentUrl(s);
                    }
                }
            }
        });
        orderDetail.setSummaryPayments(summaryPayments);

        //交易流水查询
        PaymentInfoVo paymentInfoVo = new PaymentInfoVo();
        paymentInfoVo.setOrderId(String.valueOf(orderId));
        List<PaymentInfoVo> paymentInfoVos = erpOrdersMapper.selectPaymentInfoList(paymentInfoVo);
        paymentInfoVos.forEach(e -> {
            if ("1".equals(e.getPayStatus())) {
                e.setPayStatus("已支付");
            } else {
                e.setPayStatus("未支付");
            }
        });
        orderDetail.setPaymentInfoVoList(paymentInfoVos);

        // 查询订单的相关状态。
        orderDetail.setOrderStatusInfo(this.getOrderStatus(orderId));

        // 查询订单的相关服务单信息。
        orderDetail.setErpOrderBizMainInfos(erpBizMainInfoService.selectOrderBizInfo(orderId, 0L));

        for (int i = 0; i < orderDetail.getErpOrderBizMainInfos().size(); i++) {
            orderDetail.getErpOrderBizMainInfos().get(i).setInternalServiceList(
                    erpBizMainInfoService.selectOrderBizInfo(orderId, orderDetail.getErpOrderBizMainInfos().get(i).getNumId())
            );
        }

        // 查询订单历史退款信息。
        orderDetail.setRefundInfos(erpServiceOrderRefundService.getRefundListByOrderIdAndStatus(orderId, null, null));
        getOrderRefundInfos(orderId, orderDetail);
        //产品对应退款明细
        if (CollUtil.isNotEmpty(orderDetail.getRefundInfos())){
            for (ErpProductForOrderDetailVO product : orderDetail.getProducts()) {
                List<ErpServiceOrderRefundVO> collect = orderDetail.getRefundInfos().stream()
                        .filter(en -> en.getServiceOrderId().equals(product.getServiceOrderId()))
                        .collect(Collectors.toList());
                product.setRefundInfos(collect);
            }
        }


        // 查询订单操作记录。
        orderDetail.setOrderOperationRecordList(erpOrderOperatingRecordService.getOperationRecordByOrderId(orderId, null, "2"));
        //判断是否有作废待审核的电子合同
        List<OnlineContractEntity> contractEntities = onlineContractMapper.selectOnlineContracById(String.valueOf(orderId));
        if (CollectionUtils.isNotEmpty(contractEntities)) {
            List<OnlineContractEntity> collect = contractEntities.stream().filter(item -> (item.getStatus() == 0 && item.getAuditStatus() == 0)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect) &&
                    ((Objects.nonNull(orderDetail.getOrder().getIsElectronicContract())) && 1 == orderDetail.getOrder().getIsElectronicContract()) &&
                    ((Objects.nonNull(orderDetail.getOrder().getOrderCreateExamineStatusType())) && 11 != orderDetail.getOrder().getOrderCreateExamineStatusType())
            ){
                orderDetail.setIsFinanceRejectContract(1);
            }
        }
        //赠品及寄件信息
        ErpOrderGift erpOrderGift = new ErpOrderGift();
        erpOrderGift.setOrderId(orderId);
        List<ErpOrderGift> erpOrderGifts = erpOrderGiftMapper.getErpOrderGiftList(erpOrderGift);
        if (CollectionUtils.isNotEmpty(erpOrderGifts)) {
            List<ErpOrderGift> collect = erpOrderGifts.stream().filter(item -> (Objects.nonNull(item.getGiftStatus()) && item.getGiftStatus() != 4)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                ErpOrderGiftDetailVO erpOrderGiftDetailVO = new ErpOrderGiftDetailVO();
                ErpOrderGift erpOrderGiftOne = collect.get(0);
                BeanUtils.copyProperties(erpOrderGiftOne, erpOrderGiftDetailVO);
                ErpOrderGiftDetailVO erpOrderGiftDetailVOOne = erpGiftIssueRecordMapper.getErpOrderGiftDetailVO(orderId);
                erpOrderGiftDetailVO.setGiftId(erpOrderGiftDetailVOOne.getGiftId());
                erpOrderGiftDetailVO.setGiftName(erpOrderGiftDetailVOOne.getGiftName());
                erpOrderGiftDetailVO.setGiftType(erpOrderGiftDetailVOOne.getGiftType());
                erpOrderGiftDetailVO.setGiftPrice(erpOrderGiftDetailVOOne.getGiftPrice());
                erpOrderGiftDetailVO.setGiftIssueRecordId(erpOrderGiftDetailVOOne.getGiftIssueRecordId());
                erpOrderGiftDetailVO.setProductId(erpOrderGiftDetailVOOne.getProductId());
                orderDetail.setErpOrderGiftDetailVO(erpOrderGiftDetailVO);
            }
        }
//        mateTransactionVoucherFollowByOrderId(orderDetail);

        List<Long> productIdList = orderDetail.getProducts().stream()
                .map(ErpProductForOrderDetailVO::getProductId)
                .collect(Collectors.toList());

        orderDetail.setTransactionVoucherList(erpCommitOrderService.mateTransactionVoucherByProductIds(
                orderDetail.getOrder().getUserId(),
                productIdList,
                orderId.toString(),
                orderDetail.getOrder().getCommitOrderType() == 1 ? orderDetail.getOrder().getNumClueId() : null,
                orderDetail.getOrder().getCommitOrderType() == 2 ? orderDetail.getOrder().getNumClientId().toString() : null,
                getUseVoucher,
                balanceUseBack)
        );

        List<ErpTransactionVoucherVo> transactionVoucherListUse = new ArrayList<>();
        for (int i = 0; i < orderDetail.getTransactionVoucherList().size(); i++) {
            ErpTransactionVoucherVo vo = orderDetail.getTransactionVoucherList().get(i);
            if (ObjectUtil.isNotEmpty(vo.getSplitList())) {
                List<ErpTransactionVoucherVo> splitList = vo.getSplitList();
                for (int j = 0; j < splitList.size(); j++) {
                    ErpTransactionVoucherVo splitV0 = splitList.get(j);
                    if (splitV0.isCheckFlag()) {
                        transactionVoucherListUse.add(splitV0);
                    }
                }
            }
            if (vo.isCheckFlag()) {
//                vo.setSplitList(null);
                transactionVoucherListUse.add(vo);
            }
        }
        orderDetail.setTransactionVoucherListUse(transactionVoucherListUse);
        orderDetail.setPerformanceList(erpOrderPerformanceMapper.selectListByOrderId(orderId));
        if (DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).after(orderDetail.getOrder().getDatSigningDatecreatedTime())) {
            orderDetail.setContractPaymentInfoNewIsShow(false);
        } else {
            orderDetail.setContractPaymentInfoNewIsShow(true);
        }

        //订单开票记录查询
        BigDecimal kpFeeInApprove = erpOrdersMapper.getKPFeeByOrderId(orderDetail.getOrder().getOrderId(), 0L);
        BigDecimal kpFeeOverApprove = erpOrdersMapper.getKPFeeByOrderId(orderDetail.getOrder().getOrderId(), 1L);
        orderDetail.getOrder().setKpFee(kpFeeInApprove.add(kpFeeOverApprove));

        return orderDetail;
    }

    private void getOrderRefundInfos(Long orderId, ErpOrderDetailForOmVO orderDetail) {
        List<ErpServiceOrderRefundVO> refundInfos = orderDetail.getRefundInfos();
        //查询此订单所有的退款记录
        List<ErpOrderRefund> erpOrderRefundList = erpOrderRefundMapper.selectErpOrderRefundByOrderId(orderId);
        if (CollUtil.isNotEmpty(erpOrderRefundList)) {
            //找到所有的listdetail
            List<ErpOrderRefundDetail> erpOrderRefundDetailList = erpOrderRefundList.stream()
                    .map(ErpOrderRefund::getErpOrderRefundDetailList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            //组装参数
            if (CollUtil.isNotEmpty(erpOrderRefundDetailList)) {
                List<ErpServiceOrderRefundVO> collect = erpOrderRefundDetailList.stream()
                        .map(en -> new ErpServiceOrderRefundVO()
                                .setId(en.getId())
                                .setOrderId(orderId)
                                .setServiceOrderId(en.getServiceOrdersId())
                                .setRefundPrice(en.getRefundAmount())
                                .setStatusName(OrderVoidStatusEnum.getMsgByCode(en.getVoidStatus()))
                        ).collect(Collectors.toList());
                refundInfos.addAll(collect);
                orderDetail.setRefundInfos(refundInfos);
            }
        }
    }

    /**
     * 获取最新的合同类型
     * @param orderDetail
     */
    private void setElectronicContract(ErpOrderDetailForOmVO orderDetail) {
        ErpOrderForOrderDetailVO order = orderDetail.getOrder();

        if (Objects.nonNull(order.getIsElectronicContractNew()) && (-1 != order.getIsElectronicContractNew())) {
            order.setIsElectronicContract(order.getIsElectronicContractNew());
        }
        orderDetail.setOrder(order);
    }


    /**
     * 判断是否是首次编辑订单
     */
    private void isFirstEditOrder(ErpOrderDetailForOmVO orderDetail) {
        ErpOrderForOrderDetailVO order = orderDetail.getOrder();

        ErpNewApproval erpNewApproval = new ErpNewApproval();
        erpNewApproval.setOtherId(order.getOrderId());
        erpNewApproval.setType(ApprovalTypeEnum.EDIT_ORDR.getType());
        List<ErpNewApproval> erpNewApprovals = erpNewApprovalService.selectErpNewApprovalList(erpNewApproval);
        //首次编辑订单、纸质合同、空白合同将isReturn置空,排除撤销订单
        if ((com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(erpNewApprovals) ||
                (Objects.nonNull(order.getIsElectronicContract()) && (2 == order.getIsElectronicContract() || 3 == order.getIsElectronicContract()))) && (Objects.nonNull(order.getOrderCreateExamineStatusType()) && 4 != order.getOrderCreateExamineStatusType()) &&
                (Objects.nonNull(order.getNumValidStatus()) && 1 != order.getNumValidStatus())) {
            order.setIsReturn(null);
        }
        orderDetail.setOrder(order);
    }

    /**
     * 设置订单回流的财务收款时间
     * @param erpPaymentForOmListVO
     */
    private void setFinanceTime(ErpPaymentForOmListVO erpPaymentForOmListVO) {
        //回款时 待审核取销售收款时间， 其它则取回款财务收款时间
        try {
            Long paymentId = erpPaymentForOmListVO.getPaymentId();
            if (Objects.nonNull(paymentId)) {
                List<ErpOrderPaymentTermInfo> erpOrderPaymentTermInfos = erpOrderPaymentTermInfoMapper.selectErpOrderPaymentTermInfoByTermId(paymentId);
                if (CollectionUtils.isNotEmpty(erpOrderPaymentTermInfos)) {
                    ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = erpOrderPaymentTermInfos.get(0);
                    if (Objects.nonNull(erpOrderPaymentTermInfo.getDatFinanceCollectionTime())) {
                        erpPaymentForOmListVO.setFinanceTime(DateUtils.parseDateToStr("yyyy-MM-dd", erpOrderPaymentTermInfo.getDatFinanceCollectionTime()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置回款的财务收款时间异常，异常信息为{}", e);
        }
    }

    private void setCouponToEn(List<ErpProductForOrderDetailVO> productsForOrderDetail, List<ErpDiscountCoupon> erpDiscountCoupons) {
        for (ErpProductForOrderDetailVO erpProductForOrderDetailVO : productsForOrderDetail) {
            erpProductForOrderDetailVO.setCollectionPrice(ObjectUtil.isNull(erpProductForOrderDetailVO.getCollectionPrice())
                    ? new BigDecimal(0)
                    : erpProductForOrderDetailVO.getCollectionPrice());

            erpProductForOrderDetailVO.setDiscountsMe(ObjectUtil.isNull(erpProductForOrderDetailVO.getDiscountsMe())
                    ? new BigDecimal(0)
                    : erpProductForOrderDetailVO.getDiscountsMe());

            //填入优惠券信息
            if (CollUtil.isNotEmpty(erpDiscountCoupons)) {
                if (ObjectUtil.isNotEmpty(erpProductForOrderDetailVO.getCouponId())) {
                    List<ErpDiscountCoupon> coupons = erpDiscountCoupons.stream().filter(en -> erpProductForOrderDetailVO.getCouponId().equals(en.getId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(coupons)) {
                        String name = "";
                        ErpDiscountCoupon erpDiscountCoupon = coupons.get(0);
                        if (1 == erpDiscountCoupon.getNumType()) {
                            name = "优惠券（" + erpDiscountCoupon.getDiscountAmount() + "）";
                        } else if (2 == erpDiscountCoupon.getNumType()) {
                            name = "折扣券（" + erpDiscountCoupon.getDiscountAmount() + "）";
                        }
                        erpProductForOrderDetailVO.setCouponName(name);
                        //放入优惠券
                        List<ErpDiscountCouponOrderVo> collect = coupons.stream().map(en -> {
                            ErpDiscountCouponOrderVo erpDiscountCouponOrderVo = new ErpDiscountCouponOrderVo();
                            erpDiscountCouponOrderVo.setId(en.getId());
                            if (1 == en.getNumType()) {
                                erpDiscountCouponOrderVo.setName("优惠券（" + en.getDiscountAmount() + "）");
                            } else if (2 == en.getNumType()) {
                                erpDiscountCouponOrderVo.setName("折扣券（" + en.getDiscountAmount() + "）");
                            }
                            erpDiscountCouponOrderVo.setDiscountAmount(en.getDiscountAmount());
                            erpDiscountCouponOrderVo.setDiscountCouponAmountId(en.getDiscountCouponAmountId());
                            erpDiscountCouponOrderVo.setBelongUser(en.getBelongUserId());
                            erpDiscountCouponOrderVo.setStatus(en.getStatus());
                            erpDiscountCouponOrderVo.setNumType(en.getNumType());
                            erpDiscountCouponOrderVo.setNumProductId(en.getNumProductId());
                            return erpDiscountCouponOrderVo;
                        }).collect(Collectors.toList());
                        erpProductForOrderDetailVO.setErpDiscountCoupons(collect);
                    }
                }
            }
        }
    }

    /**
     * 回款状态填充。
     *
     * @param retainages 待填充回款记录集合。
     * <AUTHOR>
     * @since 2022-03-28 13:33:47
     */
    private static void retainageEnumFill(List<ErpRetainageForOmListVO> retainages) {
        for (ErpRetainageForOmListVO retainage : retainages) {
            retainage.setAuditStatusName(RetainageStatusEnum.getNameByType(retainage.getAuditStatusType()));
            if (retainage.getPayments() != null && retainage.getPayments().size() > 0) {
                retainage.setCollectionTime(retainage.getPayments().get(retainage.getPayments().size() - 1).getCollectionTime());

                retainage.getPayments().forEach(i -> {
                    i.getTerms().forEach(term -> {
                        term.setPaymentName(PaymentEnum.getNameByType(term.getPaymentType()));
                    });
                    if (i.getTerms() != null && i.getTerms().size() > 0) {
                        i.setPaymentName(i.getTerms().get(i.getTerms().size() - 1).getPaymentName());
                        if (i.getTerms().get(0).getMoney() != null) {
                            i.setCollectionPrice(i.getTerms().stream().map(ErpPaymentTermForOmListVO::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
                        }
                    }
                });
            }
        }
    }

    /**
     * 获取订单内产品信息。
     *
     * @param orderId 订单标识。
     * @return 返回产品信息集合。
     * <AUTHOR>
     * @since 2022-03-28 10:21:06
     */
    @Override
    public List<ErpProductForOrderDetailVO> getProductForDetail(Long orderId) {
        return erpOrdersMapper.getProductsForOrderDetail(orderId);
    }

    /**
     * 根据客户id获取订单。
     *
     * @param clientIds 客户id。
     * @return 返回订单信息。
     * <AUTHOR>
     * @since 2022-03-28
     */
    @Override
    public List<ErpOrderInfoForOmListVO> getOrdersByClientId(Long[] clientIds) {
        return erpOrdersMapper.selectErpOrdersListByClientId(clientIds, null, null);
    }

    /**
     * 保存或更改订单基础信息。
     *
     * @param erpOrders 订单实体。
     * @return 返回操作后的订单标识。
     * <AUTHOR>
     * @since 2022-03-30 11:27:25
     */
    @Override
    public Long saveOrUpdate(ErpOrders erpOrders) {
        if (ObjectUtil.isNull(erpOrders.getId())) {
            erpOrdersMapper.insertErpOrders(erpOrders);
        } else {
            erpOrdersMapper.updateErpOrders(erpOrders);
        }
        return erpOrders.getId();
    }

    /**
     * 撤销订单。
     *
     * @param orderId 订单标识。
     * @return 是否撤销完成。
     * <AUTHOR>
     * @since 2022-04-01 16:07:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId) {
        // 校验当前订单是否可以进行撤销操作。
        this.isOrderToOperation(orderId, 2);

        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();
        // 构建待维护订单对象。
        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setId(orderId);
        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);

        // 设置订单失效状态为：已撤销。
        erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_UNDO.getStatusType());
        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_TERMINATION.getStatusType());
        // 判断是否驳回合同
        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
        Integer isReturn = orders.getIsReturn();
        if (Objects.nonNull(isReturn) && 1 == isReturn) {
            OnlineContractEntity onlineContract = new OnlineContractEntity();
            onlineContract.setOrderId(orderId.intValue());
            onlineContract.setStatus(0);
            //维护电子合同状态
            this.updateOnlineContract(onlineContract);
        }

        // 修改订单状态。
        updateErpOrders(erpOrders);
//        sServiceMainMapper.updateSServiceMainStatusByOrderId(orderId,ServiceMainConstants.SERVICE_STATUS_REVOKE);
        transactionVoucherFollowService.updateTransactionVoucherFollowReject(orderId, 1);


        //合同状态变更
        contractChange(orders);

        //优惠券制订为未使用
        modifyCoupons(orderId);

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.UNDO_ORDER.getTypeInt());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        //企照多撤销订单更新执照状态
        if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
            //企照多撤销订单更新状态
            if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                ErpLicense erpLicense = new ErpLicense();
                erpLicense.setStatus(ErpLicenseConstants.LICENSE_STATUS_SELLING);
                erpLicense.setNumber(Integer.valueOf(orders.getLicenseNumber()));
                int status = erpLicenseMapper.updateErpLicenseByNumber(erpLicense);
                if (status == 0) {
                    throw new ServiceException("更新执照状态失败");
                }
            }
        }
        //存在审批流则同步修改审批流的状态
        ErpNewApproval erpNewApproval = new ErpNewApproval();
        erpNewApproval.setOtherId(orderId);
        erpNewApproval.setStatus(ApprovalStatusEnum.REVOKED.getStatus());
        erpNewApprovalMapper.updateErpNewApprovalByStatus(erpNewApproval);

        reduceGift(orderId);

        serviceQualificationsExtensionService.examineOrder(erpNewApproval.getOtherId(), 2);

        return Boolean.TRUE;
    }

    //释放库存，更新状态
    private void reduceGift(Long orderId) {
        //释放库存，更新状态
        ErpOrderGift orderGift = erpOrderGiftMapper.getErpOrderGiftByOrderId(orderId);
        if (Objects.nonNull(orderGift)) {
            ErpGiftIssueRecord erpGiftIssueRecord = erpGiftIssueRecordMapper.selectErpGiftIssueRecordById(orderGift.getGiftIssueRecordId());
            ErpGift erpGift = erpGiftMapper.selectErpGiftById(erpGiftIssueRecord.getGiftId());
            //释放库存，绑定新赠品
            if (1 == erpGift.getGiftType()) {
                int result = erpGiftIssueRecordMapper.cancelErpOrderGift(orderGift.getGiftIssueRecordId());
                if (result <= 0) {
                    throw new ServiceException("库存释放失败，请稍后重试。");
                }
            }
            ErpOrderGift erpOrderGift = new ErpOrderGift();
            erpOrderGift.setOrderId(orderId);
            erpOrderGift.setGiftStatus(1);
            int i = erpOrderGiftMapper.updateErpOrderGiftByOrderId(erpOrderGift);
            if (i <= 0) {
                throw new ServiceException("更新赠品状态失败");
            }
        }
    }

    /**
     * 移交签约人。
     *
     * @param erpHandOverOrderDTO 移交签约人信息。
     * @return 是否移交完成。
     * <AUTHOR>
     * @since 2022-04-01 16:22:04
     */
    @Override
    public Boolean handOverOrder(ErpHandOverOrderDTO erpHandOverOrderDTO) {
        // 校验订单是否有效。
//        this.isOrderToOperation(erpHandOverOrderDTO.getOrderId(), 5);


        Long orderId = erpHandOverOrderDTO.getOrderId();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();
        ErpOrderForOrderDetailVO orderBefore = getOrderBase(orderId);

        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setId(orderId);
        erpOrders.setNumUserId(erpHandOverOrderDTO.getUserId());
        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        if (updateErpOrders(erpOrders) != 1) {
            throw new ServiceException("移交签约人失败");
        }

        ErpOrderForOrderDetailVO orderAfter = getOrderBase(orderId);
        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.HAND_OVER_ORDER.getTypeInt());
        erpOrderOperatingRecord.setVcOperationContent(orderBefore.getUserName() + "-->" + orderAfter.getUserName());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        return Boolean.TRUE;
    }

    /**
     * 获取订单基本信息。
     *
     * @param orderId 订单标识。
     * @return 返回订单基本信息。
     * <AUTHOR>
     * @since 2022-04-02 15:43:23
     */
    @Override
    public ErpOrderForOrderDetailVO getOrderBase(Long orderId) {
        return erpOrdersMapper.getOrderForOrderDetailByOrderId(orderId);
    }

    /**
     * 订单退款申请。
     *
     * @param erpApprovalForOrderListDTO 申请内容。
     * @return 是否提交完成。
     * <AUTHOR>
     * @since 2022-04-08 09:16:57
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundApproval(ErpApprovalForOrderListDTO erpApprovalForOrderListDTO) {
        // 校验当前订单是否可以进行退款操作。
        this.isOrderToOperation(erpApprovalForOrderListDTO.getOrderId(), 4);

        Long orderId = erpApprovalForOrderListDTO.getOrderId();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();

        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);

        //校验当前订单能否退款
        if ((Objects.nonNull(orders.getNumCancelOrderExamineStatus()) && (1 == orders.getNumCancelOrderExamineStatus() || 3 == orders.getNumCancelOrderExamineStatus() || 12 == orders.getNumCancelOrderExamineStatus()))) {
            throw new ServiceException("该订单正在作废申请中，无法申请退款！");
        }

        if (((Objects.nonNull(orders.getNumModifyOrderExamineStatus()) && (1 == orders.getNumModifyOrderExamineStatus() || 3 == orders.getNumModifyOrderExamineStatus() || 12 == orders.getNumModifyOrderExamineStatus())))) {
            throw new ServiceException("该订单正在修改申请中，无法申请作废！");
        }

        // 构建待维护订单对象。
        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setId(orderId);
        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);

        // 设置订单退款审核为：财务待审核。
        erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType());

        // 修改订单状态。
        this.updateErpOrders(erpOrders);

        // region 将订单内的所有产品进行退款信息维护。
        // 获取订单内所有产品。
        List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(erpApprovalForOrderListDTO.getOrderId());
        // 循环产品进行退款信息维护。
        for (ErpServiceOrders erpServiceOrders : serviceOrdersList) {
            ErpServiceOrderRefund erpServiceOrderRefund = new ErpServiceOrderRefund();
            erpServiceOrderRefund.setNumServiceOrderId(erpServiceOrders.getId());
            erpServiceOrderRefund.setNumRefundPrice(erpServiceOrders.getNumPayPrice());
            // 设置退款状态为待审核，若此次修改被驳回，需将退款金额重新加回到订单内。
            erpServiceOrderRefund.setNumStatus(OrderRefundApprovalStatusEnum.REFUND_NOT.getStatusType());
            erpServiceOrderRefund.setCreateBy(userId.toString());
            erpServiceOrderRefund.setCreateTime(nowDate);
            erpServiceOrderRefund.setUpdateBy(userId.toString());
            erpServiceOrderRefund.setUpdateTime(nowDate);
            erpServiceOrderRefundService.insertErpServiceOrderRefund(erpServiceOrderRefund);
        }
        // endregion

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.REFUND_ORDER.getTypeInt());
        erpOrderOperatingRecord.setVcOperationContent(erpApprovalForOrderListDTO.getRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);
        //创建对应的审批流
        addErpNewApproval(orderId, 4);

        return Boolean.TRUE;
    }

    /**
     * 订单作废申请。
     *
     * @param erpApprovalForOrderListDTO 申请内容。
     * @return 是否提交完成。
     * <AUTHOR>
     * @since 2022-04-08 09:20:13
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelApproval(ErpApprovalForOrderListDTO erpApprovalForOrderListDTO) {
        // 校验当前订单是否可以进行作废操作。
        this.isOrderToOperation(erpApprovalForOrderListDTO.getOrderId(), 4);

        List<SServiceMain> mainList = sServiceMainMapper.selectFinishByOrderId(erpApprovalForOrderListDTO.getOrderId());
        if (ObjectUtil.isNotEmpty(mainList) && mainList.size() > 0) {
            String toastStr = "";
            for (int i = 0; i < mainList.size(); i++) {
                toastStr += "," + mainList.get(i).getProductId();
            }
            throw new ServiceException(toastStr.replaceFirst("," , "") + "（产品id）服务单已完结，无法作废订单");
        }

        Long orderId = erpApprovalForOrderListDTO.getOrderId();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();

        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
        //校验当前订单能否作废
        if ((Objects.nonNull(orders.getNumRefundExamineStatus()) && (1 == orders.getNumRefundExamineStatus() || 3 == orders.getNumRefundExamineStatus() || 12 == orders.getNumRefundExamineStatus()))) {
            throw new ServiceException("该订单正在退款申请中，无法申请作废！");
        }
        if (((Objects.nonNull(orders.getNumModifyOrderExamineStatus()) && (1 == orders.getNumModifyOrderExamineStatus() || 3 == orders.getNumModifyOrderExamineStatus() || 12 == orders.getNumModifyOrderExamineStatus())))) {
            throw new ServiceException("该订单正在修改申请中，无法申请作废！");
        }
        // 构建待维护订单对象。
        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setId(orderId);
        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);

        // 设置订单作废审核为：待审核。
        erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType());

        // 修改订单状态。
        this.updateErpOrders(erpOrders);
        //作废服务单
//        sServiceMainMapper.updateSServiceMainStatusByOrderId(orderId,ServiceMainConstants.SERVICE_STATUS_REVOKE);

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_CANCEL.getTypeInt());
        erpOrderOperatingRecord.setVcOperationContent(erpApprovalForOrderListDTO.getRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);
        //创建对应的审批流
        addErpNewApproval(orderId, 5);


        List<Long> deptIdList = Arrays.stream(tokenService.getLoginUser().getSysUser().getDept().getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (deptIdList.contains(259L)) {
            //发送钉钉消息
            String dingContent = "### 订单作废提醒 \n * " + "您好： \n "
                    +" * 订单编号:" + orders.getVcOrderNumber() + "申请作废  \n"
                    +" * 申请人:" + tokenService.getLoginUser().getSysUser().getNickName() + "，请查看！";
            DingSendDTO dingSendDTO = new DingSendDTO("212310216439948932", "订单作废", dingContent);
            dingDingService.sendDingMessage(dingSendDTO);
        }





        return Boolean.TRUE;
    }


    /**
     * 创建对应的审批流
     * @param orderId
     * @param type
     * @return
     */
    private int addErpNewApproval(Long orderId, Integer type) {
        ErpNewApprovalDTO erpNewApprovalDTO = new ErpNewApprovalDTO();
        erpNewApprovalDTO.setType(type);
        erpNewApprovalDTO.setOtherId(orderId);
        return erpNewApprovalService.addErpNewApproval(erpNewApprovalDTO, null);
    }

    /**
     * 移动端支付回调。
     *
     * @param orderId 订单标识。
     * <AUTHOR>
     * @since 2022-04-14 14:51:41
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payOrderForMobile(Long orderId) {
        //  修改订单支付状态、实收金额、尾款金额。
        erpOrdersMapper.payOrderForMobile(orderId);

        // 修改订单内产品实收金额、尾款金额。
        erpServiceOrdersMapper.payOrderForMobile(orderId);
    }

    /**
     * 获取指定订单所有状态。
     *
     * @param orderId 订单标识。
     * @return 返回订单所有状态。
     * <AUTHOR>
     * @since 2022-04-19 10:07:43
     */
    @Override
    public ErpOrderStatusVO getOrderStatus(Long orderId) {
        ErpOrderStatusVO orderStatus = erpOrdersMapper.getOrderStatus(orderId);
        orderStatus.setOrderStatusName(OrderStatusEnum.getNameByType(orderStatus.getOrderStatusType()));
        orderStatus.setValidStatusName(OrderInvalidStatusEnum.getNameByType(orderStatus.getValidStatusType()));
        orderStatus.setRetainageStatusName(Objects.nonNull(orderStatus.getRetainageStatusType()) ? (orderStatus.getRetainageStatusType().equals(1) ? "有尾款" : "无尾款") : null);
        orderStatus.setBizStatusName(OrderBizStatusEnum.getNameByType(orderStatus.getBizStatusType()));
        orderStatus.setCreateOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(orderStatus.getCreateOrderExamineStatusType()));
        orderStatus.setRetainageReturnExamineCount(erpRetainageReturnMapper.getRetainageReturnExamineCount(orderId));
        combineStatusAndServiceName(orderStatus, orderId);
        return orderStatus;
    }

    @Override
    public List<ErpOrderStatusVO> getOrderStatusList(List<Long> orderIds) {
//        ErpOrderStatusVO orderStatus = erpOrdersMapper.getOrderStatus(orderId);
        List<ErpOrderStatusVO> orderStatus = erpOrdersMapper.getOrderStatuList(orderIds);
        List<ErpOrderStatusVO> list = erpRetainageReturnMapper.getRetainageReturnExamineCountList(orderIds);
        list.removeAll(Collections.singleton(null));
        for (ErpOrderStatusVO status : orderStatus) {
            status.setOrderStatusName(OrderStatusEnum.getNameByType(status.getOrderStatusType()));
            status.setValidStatusName(OrderInvalidStatusEnum.getNameByType(status.getValidStatusType()));
            status.setRetainageStatusName(Objects.nonNull(status.getRetainageStatusType()) ? (status.getRetainageStatusType().equals(1) ? "有尾款" : "无尾款") : null);
            status.setBizStatusName(OrderBizStatusEnum.getNameByType(status.getBizStatusType()));
            status.setCreateOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(status.getCreateOrderExamineStatusType()));
            status.setModifyOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(status.getModifyOrderExamineStatusType()));
            status.setCancelOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(status.getCancelOrderExamineStatusType()));
            status.setRefundExamineStatusName(OrderApprovalStatusEnum.getNameByType(status.getRefundExamineStatusType()));
            List<ErpOrderStatusVO> collect = list.stream().filter(en -> en.getOrderId().equals(status.getOrderId())).collect(Collectors.toList());
            if (collect.size() > 1) {
                for (ErpOrderStatusVO erpOrderStatusVO : collect) {
                    status.setRetainageReturnExamineCount(erpOrderStatusVO.getRetainageReturnExamineCount());
                }
            } else {
                status.setRetainageReturnExamineCount(0);
            }
        }


        return orderStatus;
    }

    /**
     * 拼接审核状态和业务对接人名称
     *
     * @param orderStatus
     */
    private void combineStatusAndServiceName(ErpOrderStatusVO orderStatus, Long orderId) {
        try {
            if (Objects.nonNull(orderStatus.getModifyOrderExamineStatusType())) {
                switch (orderStatus.getModifyOrderExamineStatusType()) {
                    case 12:
                        ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                        erpServicePersonApproval.setType(1);
                        erpServicePersonApproval.setOrderId(orderId);
                        erpServicePersonApproval.setStatus(0);
                        List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(erpServicePersonApproval);
                        StringBuffer sb = new StringBuffer();
                        sb.append("业务对接人待审核");
                        if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                            Map<Long, List<ErpServicePersonApproval>> map = erpServicePersonApprovals.stream().filter(val -> Objects.nonNull(val.getVersion())).collect(Collectors.groupingBy(ErpServicePersonApproval::getVersion));
                            Long maxKey = getMaxKey(map);
                            List<ErpServicePersonApproval> erpServicePersonApprovalsMap = map.get(maxKey);
                            if (CollectionUtils.isNotEmpty(erpServicePersonApprovalsMap)) {
                                sb.append(":->  : ");
                                for (ErpServicePersonApproval personApproval : erpServicePersonApprovalsMap) {
                                    try {
                                        R<SysUser> info = remoteUserService.getUserInfoById(personApproval.getUserId(), SecurityConstants.INNER);
                                        if (200 == info.getCode()) {
                                            if (erpServicePersonApprovalsMap.indexOf(personApproval) == 0) {
                                                sb.append(info.getData().getNickName());
                                            } else {
                                                sb.append("-" + info.getData().getNickName());
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("作废审核获取业务对接人异常，异常信息为：", e);
                                    }
                                }

                            }
                        }
                        orderStatus.setModifyOrderExamineStatusName(sb.toString());
                        break;
                    default:
                        orderStatus.setModifyOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(orderStatus.getModifyOrderExamineStatusType()));
                        break;
                }
            }
            if (Objects.nonNull(orderStatus.getCancelOrderExamineStatusType())) {
                switch (orderStatus.getCancelOrderExamineStatusType()) {
                    case 12:
                        ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                        erpServicePersonApproval.setType(2);
                        erpServicePersonApproval.setOrderId(orderId);
                        List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(erpServicePersonApproval);
                        StringBuffer sb = new StringBuffer();
                        sb.append("业务对接人待审核 ");
                        if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                            Map<Long, List<ErpServicePersonApproval>> map = erpServicePersonApprovals.stream().filter(val -> Objects.nonNull(val.getVersion())).collect(Collectors.groupingBy(ErpServicePersonApproval::getVersion));
                            Long maxKey = getMaxKey(map);
                            List<ErpServicePersonApproval> erpServicePersonApprovalsMap = map.get(maxKey);
                            if (CollectionUtils.isNotEmpty(erpServicePersonApprovalsMap)) {
                                sb.append(":->  : ");
                                for (ErpServicePersonApproval personApproval : erpServicePersonApprovalsMap) {
                                    try {
                                        R<SysUser> info = remoteUserService.getUserInfoById(personApproval.getUserId(), SecurityConstants.INNER);
                                        if (200 == info.getCode()) {
                                            if (erpServicePersonApprovalsMap.indexOf(personApproval) == 0) {
                                                sb.append(info.getData().getNickName());
                                            } else {
                                                sb.append("-" + info.getData().getNickName());
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("作废审核获取业务对接人异常，异常信息为：", e);
                                    }
                                }

                            }
                        }
                        orderStatus.setCancelOrderExamineStatusName(sb.toString());
                        break;
                    default:
                        orderStatus.setCancelOrderExamineStatusName(OrderApprovalStatusEnum.getNameByType(orderStatus.getCancelOrderExamineStatusType()));
                        break;
                }
            }
            if (Objects.nonNull(orderStatus.getRefundExamineStatusType())) {
                switch (orderStatus.getRefundExamineStatusType()) {
                    case 12:
                        ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                        erpServicePersonApproval.setType(3);
                        erpServicePersonApproval.setOrderId(orderId);
                        List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(erpServicePersonApproval);
                        StringBuffer sb = new StringBuffer();
                        sb.append("业务对接人待审核");
                        if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                            Map<Long, List<ErpServicePersonApproval>> map = erpServicePersonApprovals.stream().filter(val -> Objects.nonNull(val.getVersion())).collect(Collectors.groupingBy(ErpServicePersonApproval::getVersion));
                            Long maxKey = getMaxKey(map);
                            List<ErpServicePersonApproval> erpServicePersonApprovalsMap = map.get(maxKey);
                            if (CollectionUtils.isNotEmpty(erpServicePersonApprovalsMap)) {
                                sb.append(":->  : ");
                                for (ErpServicePersonApproval personApproval : erpServicePersonApprovalsMap) {
                                    try {
                                        R<SysUser> info = remoteUserService.getUserInfoById(personApproval.getUserId(), SecurityConstants.INNER);
                                        if (200 == info.getCode()) {
                                            if (erpServicePersonApprovalsMap.indexOf(personApproval) == 0) {
                                                sb.append(info.getData().getNickName());
                                            } else {
                                                sb.append("-" + info.getData().getNickName());
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("退款审核获取业务对接人异常，异常信息为：", e);
                                    }
                                }

                            }
                        }
                        orderStatus.setRefundExamineStatusName(sb.toString());
                        break;
                    default:
                        orderStatus.setRefundExamineStatusName(OrderApprovalStatusEnum.getNameByType(orderStatus.getRefundExamineStatusType()));
                        break;
                }
            }
        } catch (Exception e) {
            log.error("拼接审核状态和业务对接人名称异常，异常信息为：", e);
        }
    }

    /**
     * 获取map中最大的key
     *
     * @param map
     * @return
     */
    public static Long getMaxKey(Map<Long, List<ErpServicePersonApproval>> map) {
        if (map == null) {
            return null;
        }
        Set<Long> set = map.keySet();
        Object[] obj = set.toArray();
        Arrays.sort(obj);
        return Long.valueOf(obj[obj.length - 1].toString());
    }


    /**
     * 小程序会员权益升降级。
     *
     * @param orderId    订单标识。
     * @param sourceType 来源类型：1提单审核通过；2订单退款；3订单作废。
     * <AUTHOR>
     * @since 2022-04-19 20:02:12
     */
    @Override
    public void memberUp(Long orderId, Integer sourceType) {
        //log.info("会员权益升降级接受参数::orderId{},::sourceType{}", orderId, sourceType);
        // 校验参数是否完整。
        if (ObjectUtil.hasEmpty(
                orderId, sourceType
        )) {
            throw new ServiceException("会员权益升降机外调-参数不完整");
        }

        String type = "";
        if (sourceType == 1) {
            type = "ADD";
        } else if (sourceType == 2) {
            type = "SUB";
        } else if (sourceType == 3) {
            type = "SUB";
        } else {
            type = "NONE";
        }

        // 获取指定订单内的全部关联标识。
        List<Long> serviceOrderIds = erpOrdersMapper.getServiceOrderIdByOrderId(orderId);
        //log.info("会员权益升降级::serviceOrderIds{}", serviceOrderIds);

        String finalType = type;
        serviceOrderIds.forEach(serviceOrderId -> {
            // 外调地址。
            String url = "http://test.phplijun.cn/api_nnb_xcx_admin/java/memberUp";

            Map<String, String> bodyMap = new HashMap<>();//存放参数
            bodyMap.put("service_order_id", serviceOrderId.toString());
            bodyMap.put("type", finalType);
            bodyMap.put("token", MD5.create().digestHex(MD5.create().digestHex(serviceOrderId + xcxMemberUpSecretKey)));

            //log.info("会员权益升降级小程序接口传参::{}", JSONObject.toJSONString(bodyMap));
            //发送post请求并接收响应数据
            String result = HttpUtil.createPost(url).body(JSONObject.toJSONString(bodyMap)).execute().body();

            //log.info("会员权益升降级返回结果::{}", result);
        });

    }

    /**
     * 维护订单服务状态。
     *
     * @param orderId 订单标识。
     * @param type    维护类型：1服务进行中，2服务已完成。
     * <AUTHOR>
     * @since 2022-04-20 18:09:23
     */
    @Override
    public void maintainBizStatus(Long orderId, Integer type) {
        Long userId = 1L;
        if (ObjectUtil.isNotEmpty(tokenService.getLoginUser())) {
            userId = tokenService.getLoginUser().getUserid();
        }


        Date nowDate = new Date();

        ErpOrders erpOrders = new ErpOrders();
        erpOrders.setId(orderId);
        erpOrders.setNumStatus(type == 1 ? Long.valueOf(OrderStatusEnum.ORDER_BIZ_END.getStatusType()) : Long.valueOf(OrderStatusEnum.ORDER_DONED.getStatusType()));
        erpOrders.setNumBizStatus(type == 1 ? OrderBizStatusEnum.BIZ_START.getStatusType() : OrderBizStatusEnum.BIZ_OVER.getStatusType());

        erpOrdersMapper.updateErpOrders(erpOrders);

        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setNumOperationType(type.equals(1) ? OrderOperationTypeEnum.EXECUTE_BIZ.getTypeInt() : OrderOperationTypeEnum.DONE_BIZ.getTypeInt());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);
    }

    /**
     * 校验订单是否进行指定操作。
     *
     * @param orderId       订单标识。
     * @param operationType 操作类型：1编辑订单，2撤销订单，3尾款回款，4发起审核。
     * <AUTHOR>
     * @since 2022-04-22 13:45:58
     */
    @Override
    public void isOrderToOperation(Long orderId, Integer operationType) {

        // 获取订单当前状态。
        ErpOrderStatusVO orderStatusVO = this.getOrderStatus(orderId);


        // 校验订单是否可编辑。
        if (operationType.equals(1)) {
            if (
                    !(orderStatusVO.getValidStatusType() == (OrderInvalidStatusEnum.INVALID_UNDO.getStatusType()))
                            &&
                            !(
                                    (orderStatusVO.getCreateOrderExamineStatusType() == (OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType())) ||
                                            (orderStatusVO.getCreateOrderExamineStatusType() == (OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())) ||
                                            (OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType() == (orderStatusVO.getModifyOrderExamineStatusType())) ||
                                            (OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType() == (orderStatusVO.getModifyOrderExamineStatusType())) ||
                                            (OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType() == orderStatusVO.getRefundExamineStatusType()) ||
                                            (OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType() == orderStatusVO.getCancelOrderExamineStatusType()) ||
                                            (OrderApprovalStatusEnum.UNUPLOAD_PROCEEDS_SCREENSHOT.getStatusType() == orderStatusVO.getCreateOrderExamineStatusType())

                            )
                            &&
                            !(
                                    orderStatusVO.getStatus().equals(9)
                            )

            ) {
                throw new ServiceException("订单" + orderStatusVO.getValidStatusName() + "，提单审核当前流程:" + orderStatusVO.getCreateOrderExamineStatusName() + "，不可编辑");
            }
        } else {
            // 校验订单是否无效。
            if (!orderStatusVO.getValidStatusType().equals(OrderInvalidStatusEnum.INVALID_VALID.getStatusType())) {
                throw new ServiceException("订单" + orderStatusVO.getValidStatusName());
            }
        }


        // 校验订单是否可撤销。
        if (operationType.equals(2)) {
            if (
                    !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.CUSTOMER_SIGN_NOT.getStatusType())

            ) {
                throw new ServiceException("提单审核当前流程:" + orderStatusVO.getCreateOrderExamineStatusName() + "，不可撤销");
            }
        }

        if (operationType.equals(3)) {
            // 校验订单是否可以发起审核或回款。
            // 当前订单是否有未完成审核的回款。
            if (orderStatusVO.getRetainageReturnExamineCount() != 0) {
                throw new ServiceException("当前订单内有未审核的回款记录");
            }

            // 校验订单是否处于提单审核中。
            if (
                    !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType())
                            &&
                            !orderStatusVO.getCreateOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType())
            ) {
                throw new ServiceException("提单审核未结束");
            }

            // 校验订单是否处于修改审核中。
            if (
                    !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.LEGAL_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType())


            ) {
                throw new ServiceException("修改审核未结束");
            }

            // 校验订单是否处于作废审核中。
            if (
                    !orderStatusVO.getCancelOrderExamineStatusType().equals(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType())
                            &&
                            !orderStatusVO.getCancelOrderExamineStatusType().equals(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.LEGAL_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getCancelOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType())
                            &&
                            !orderStatusVO.getCancelOrderExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType())
            ) {
                throw new ServiceException("作废审核未结束");
            }

            // 校验订单是否处于退款审核中。
            if (
                    !orderStatusVO.getRefundExamineStatusType().equals(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType())
                            &&
                            !orderStatusVO.getRefundExamineStatusType().equals(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getModifyOrderExamineStatusType().equals(OrderApprovalStatusEnum.LEGAL_REJECTED.getStatusType())
                            &&
                            !orderStatusVO.getRefundExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType())
                            &&
                            !orderStatusVO.getRefundExamineStatusType().equals(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType())
            ) {
                throw new ServiceException("退款审核未结束");
            }
        }

        if (operationType.equals(4)) {
            if ((!(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType() == orderStatusVO.getCreateOrderExamineStatusType()) ||
                    !(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType() == orderStatusVO.getModifyOrderExamineStatusType()) ||
                    !(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType() == orderStatusVO.getCancelOrderExamineStatusType()) ||
                    !(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType() == orderStatusVO.getRefundExamineStatusType())) &&
                    (OrderInvalidStatusEnum.INVALID_DEPRECATED.getStatusType() == orderStatusVO.getValidStatusType() ||
                            OrderInvalidStatusEnum.INVALID_REFUNDED.getStatusType() == orderStatusVO.getValidStatusType()) &&
                    (OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType() == orderStatusVO.getCancelOrderExamineStatusType()) &&
                    (OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType() == orderStatusVO.getRefundExamineStatusType())
            ) {
                throw new ServiceException("订单审核未通过，无法编辑");
            }
        }

        // 校验订单是否可以回款。
        if (operationType.equals(3)) {
            if (orderStatusVO.getRetainageStatusType() != 1) {
                throw new ServiceException("订单无尾款，无法执行回款操作");
            }
        }


    }

    /**
     * 查询订单审核列表。
     *
     * @param erpOrderExamineDTO 查询条件。
     * @return 返回订单审核列表。
     * <AUTHOR>
     * @since 2022-04-24 11:04:49
     */
    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<ErpOrderInfoForExamineVO> getOrderExamineList(ErpOrderExamineDTO erpOrderExamineDTO) {
        // 校验参数是否完整。
        if (ObjectUtil.isNull(erpOrderExamineDTO)) {
            throw new ServiceException("传参为空");
        }
        if (ObjectUtil.hasEmpty(
                erpOrderExamineDTO.getExamineRole(),
                erpOrderExamineDTO.getExamineType()

        )) {
            throw new ServiceException("传参丢失");
        }

        List<ErpOrderInfoForExamineVO> orderExamineList = erpOrdersMapper.getOrderExamineList(erpOrderExamineDTO);

        ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
        List<Long> collect = orderExamineList.stream().map(ErpOrderInfoForExamineVO::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            query.setOrderIdList(collect);
        }
        List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);

        //查询合同号
        Map<Long, List<ErpOrderInfoForOmListVO>> map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(val -> val.getOrderId()));

        orderExamineList.forEach(orderExamine -> {
            //显示联系人
            if ((Objects.nonNull(orderExamine.getCommitOrderType()) && 2 == orderExamine.getCommitOrderType()) || (Objects.isNull(orderExamine.getClueId())) || (Objects.nonNull(orderExamine.getClueId()) && (0 == orderExamine.getClueId()))) {
                orderExamine.setClueCustomerName(orderExamine.getClientContactName());
            }
            List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(orderExamine.getOrderId());
            if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                StringBuffer vcContractNumber = new StringBuffer();
//                String vcContractNumber = "";
                int size = erpOrderInfoForOmListVOS.size();
                for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                    if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
//                        vcContractNumber = erpOrderInfoForOmListVO.getVcContractNumber();
                    } else {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");

//                        vcContractNumber = erpOrderInfoForOmListVO.getVcContractNumber() + "，";
                    }
                }
                orderExamine.setVcContractNumber(String.valueOf(vcContractNumber));
            }
            orderExamine.setOrderStatusInfo(this.getOrderStatus(orderExamine.getOrderId()));
            orderExamine.setOrderStatusName(orderExamine.getOrderStatusInfo().getOrderStatusName());
            orderExamine.setSumRefundPrice(erpOrdersMapper.getSumRefundPriceByOrderId(orderExamine.getOrderId()));
            if (2 == erpOrderExamineDTO.getExamineRole() && 2 == erpOrderExamineDTO.getExamineType()) {
                orderExamine.setCreateTime(orderExamine.getApprovalTime());
            }
        });

        return orderExamineList;
    }

    @Override
    @DataScope(deptAlias = "asd", userAlias = "asu")
    public List<ErpOrderInfoForExamineVO> getServiceApprovalList(ErpOrderExamineDTO erpOrderExamineDTO) {
        // 校验参数是否完整。
        if (ObjectUtil.isNull(erpOrderExamineDTO)) {
            throw new ServiceException("传参为空");
        }
        if (ObjectUtil.hasEmpty(
                erpOrderExamineDTO.getExamineRole(),
                erpOrderExamineDTO.getExamineType()

        )) {
            throw new ServiceException("传参丢失");
        }
        if (1L != SecurityUtils.getUserId()) {
            erpOrderExamineDTO.setUserId(SecurityUtils.getUserId());
        }
        //查询审批列表
        List<ErpOrderInfoForExamineVO> orderExamineList = erpOrdersMapper.getServiceApprovalList(erpOrderExamineDTO);
        //获取订单ID
        List<Long> collect = orderExamineList.stream().map(ErpOrderInfoForExamineVO::getOrderId).collect(Collectors.toList());

        ErpOrderQueryForOmListDTO ErpOrderExamineDTO = new ErpOrderQueryForOmListDTO();
        if (CollectionUtils.isNotEmpty(collect)) {
            ErpOrderExamineDTO.setOrderIdList(collect);
        }
        //查询合同号
        Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderExamineList)) {
            List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(ErpOrderExamineDTO);
            //查询合同号
            map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(val -> val.getOrderId()));
        }

        for (ErpOrderInfoForExamineVO orderExamine : orderExamineList) {
            List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(orderExamine.getOrderId());
            if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                //组合合同号
                StringBuffer vcContractNumber = new StringBuffer();
                int size = erpOrderInfoForOmListVOS.size();
                for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                    if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                    } else {
                        vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                    }
                }
                orderExamine.setVcContractNumber(String.valueOf(vcContractNumber));
            }
            orderExamine.setOrderStatusInfo(this.getOrderStatus(orderExamine.getOrderId()));
            orderExamine.setSumRefundPrice(erpOrdersMapper.getSumRefundPriceByOrderId(orderExamine.getOrderId()));
        }

        return orderExamineList;
    }

    /**
     * 订单操作。
     *
     * @param erpOrderExamineDTO 操作参数。
     * @return 是否操作完成。
     * <AUTHOR>
     * @since 2022-04-24 11:39:49
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object orderExamineOperation(ErpOrderExamineDTO erpOrderExamineDTO) {
        // 校验参数是否完整。
        if (ObjectUtil.isNull(erpOrderExamineDTO)) {
            throw new ServiceException("传参为空");
        }
        if (ObjectUtil.hasEmpty(
                erpOrderExamineDTO.getOrderId(),
                erpOrderExamineDTO.getExamineRole(),
                erpOrderExamineDTO.getExamineType(),
                erpOrderExamineDTO.getExamineOperation()

        )) {
            throw new ServiceException("传参丢失");
        }

        // 审核类型：1提单，2修改，3退款，4作废。
        if (erpOrderExamineDTO.getExamineType().equals(1)) {
            // 执行提单审核操作。
            Map<String, String> map = createOrderExamineOperation(erpOrderExamineDTO);
            //修改螳螂订单
//            ThreadUtil.execute(() -> modifyTLOrder(erpOrderExamineDTO, map));
            modifyTLOrder(erpOrderExamineDTO, map);
            return map;
        } else if (erpOrderExamineDTO.getExamineType().equals(2)) {
            // 执行修改审核操作。
            Map<String, String> map = modifyOrderExamineOperation(erpOrderExamineDTO);
            //修改螳螂订单
//            ThreadUtil.execute(() -> modifyTLOrder(erpOrderExamineDTO, map));
            modifyTLOrder(erpOrderExamineDTO, map);
            return map;
        } else if (erpOrderExamineDTO.getExamineType().equals(3)) {
            // 执行退款审核操作。
            Map<String, String> map = refundOrderExamineOperation(erpOrderExamineDTO);
            //修改螳螂订单
//            ThreadUtil.execute(() -> modifyTLOrder(erpOrderExamineDTO, map));
            modifyTLOrder(erpOrderExamineDTO, map);
        } else if (erpOrderExamineDTO.getExamineType().equals(4)) {
            // 执行作废审核操作。
            Map<String, String> map = cancelOrderExamineOperation(erpOrderExamineDTO);
            //修改螳螂订单
//            ThreadUtil.execute(() -> modifyTLOrder(erpOrderExamineDTO, map));
            modifyTLOrder(erpOrderExamineDTO, map);
        }

        return Boolean.TRUE;
    }

    private void modifyTLOrder(ErpOrderExamineDTO erpOrderExamineDTO, Map<String, String> map) {

        if (!"2".equals(MapUtil.getStr(map, "examineRole")) || !"1".equals(MapUtil.getStr(map, "examineOperation"))) {
            //log.info("无需同步螳螂订单，当前审核类型为：“{}（1提单，2修改，3退款，4作废）”，resultMap：{}",
//                    erpOrderExamineDTO.getExamineType(), JSONUtil.toJsonStr(map));
            return;
        }

        //查询螳螂订单
        ErpOrders erpOrder = erpOrdersMapper.selectErpOrdersById(erpOrderExamineDTO.getOrderId());
        Long numClueId = erpOrder.getNumClueId();
        TlClue tlClue = new TlClue();
        tlClue.setClueId(numClueId);
        List<TlClue> tlClues = erpTLOrderMapper.selectTlClueList(tlClue);
        if (CollUtil.isEmpty(tlClues)) {
            //log.info("为查询到螳螂订单，无需同步");
            return;
        }

        if (tlClues.size() > 1) {
            log.warn("请查看订单，当前线索存在多条螳螂订单，订单：{}", JSONUtil.toJsonStr(tlClues));
            return;
        }

        //解析json，放入实体
        String orderPayList = tlClues.get(0).getOrdePayList();
        Long userId = tokenService.getLoginUser().getUserid();
        JSONArray objects = JSONUtil.parseArray(orderPayList);
        String payId = objects.getJSONObject(0).getStr("payId");

        // 审核类型：1提单，2修改，3退款，4作废。
        if (erpOrderExamineDTO.getExamineType().equals(1)) {
            // 执行提单审核操作。
            ConfirmPayRecordReq confirmPayRecordReq = new ConfirmPayRecordReq();
            confirmPayRecordReq.setPayRecordId(Integer.valueOf(payId));
            confirmPayRecordReq.setConfirmUserId(tlOpenApiPropertiesConfig.getAdminId());
            ConfirmPayRecordRes confirmPayRecordRes = erpTLOrderService.syncConfirmPayRecord(confirmPayRecordReq);
            if (confirmPayRecordRes.getFlag() == 1) {
                //log.info("财务提单审核——>螳螂支付确认成功，返回：{}", JSONUtil.toJsonStr(confirmPayRecordRes));
            } else {
                //log.info("财务提单审核——>螳螂支付确认失败，返回：{}", JSONUtil.toJsonStr(confirmPayRecordRes));
            }
        } else if (erpOrderExamineDTO.getExamineType().equals(2)) {
            // 执行修改审核操作。
            ConfirmPayRecordReq confirmPayRecordReq = new ConfirmPayRecordReq();
            confirmPayRecordReq.setPayRecordId(Integer.valueOf(payId));
            confirmPayRecordReq.setConfirmUserId(tlOpenApiPropertiesConfig.getAdminId());
            ConfirmPayRecordRes confirmPayRecordRes = erpTLOrderService.syncConfirmPayRecord(confirmPayRecordReq);
            if (confirmPayRecordRes.getFlag() == 1) {
                //log.info("财务修改审核——>螳螂支付确认成功，返回：{}", JSONUtil.toJsonStr(confirmPayRecordRes));
            } else {
                //log.info("财务修改审核——>螳螂支付确认失败，返回：{}", JSONUtil.toJsonStr(confirmPayRecordRes));
            }
        } else if (erpOrderExamineDTO.getExamineType().equals(3)) {
            // TODO 执行退款审核操作 目前只支持全额
            //取json
            String orderPayDetailList = objects.getJSONObject(0).getStr("ordePayDetailList");
            JSONArray parseArray = JSONUtil.parseArray(orderPayDetailList);
            String payCategoryId = parseArray.getJSONObject(0).getStr("payCategoryId");

            String responseJson = tlClues.get(0).getResponseJson();
            JSONObject jsonObject = JSONObject.parseObject(responseJson);
            Map orderDetailList = (Map) jsonObject.getJSONArray("orderDetailList").get(0);
            String saleOrderDetailId = MapUtil.getStr(orderDetailList, "saleOrderDetailId");
            //判断是否全额
            BigDecimal numPayPrice = erpOrder.getNumPayPrice();
            //查询退款金额
            List<ErpServiceOrderRefund> erpServiceOrderRefunds = erpServiceOrderRefundMapper.selectErpServiceOrderRefundListbyOrderId(erpOrderExamineDTO.getOrderId());
            BigDecimal refundPrice = erpServiceOrderRefunds.stream().map(ErpServiceOrderRefund::getNumRefundPrice).reduce(BigDecimal::add).orElse(new BigDecimal("0.00"));

            if (numPayPrice.compareTo(refundPrice) != 0) {
                log.error("不是全额退款（订单总实收不等于总退款）");
                return;
            }

            String name = erpTLOrderMapper.selectTlClueName(numClueId);

            RefundOrderReq refundOrderReq = new RefundOrderReq();
            RefundOrderData refundOrderData = new RefundOrderData();
            refundOrderData.setOrderDetailId(Integer.valueOf(saleOrderDetailId));
            refundOrderData.setAmount(numPayPrice);
            refundOrderData.setDeductAmount(new BigDecimal("0"));
            refundOrderData.setBalanceAmount(numPayPrice);
            refundOrderData.setPayee(name);
            refundOrderData.setCategoryId(payCategoryId);
            refundOrderReq.setData(refundOrderData);
            RefundOrderRes refundOrderRes = erpTLOrderService.refundOrder(refundOrderReq);
            if (refundOrderRes.getFlag() == 1) {
                //log.info("财务退款审核——>螳螂退款同步成功，返回：{}", JSONUtil.toJsonStr(refundOrderRes));
            } else {
                //log.info("财务退款审核——>螳螂退款同步失败，返回：{}", JSONUtil.toJsonStr(refundOrderRes));
            }
        } else if (erpOrderExamineDTO.getExamineType().equals(4)) {
            // 执行作废审核操作。
            InvalidOrderReq invalidOrderReq = new InvalidOrderReq();
            invalidOrderReq.setOrderId(tlClues.get(0).getMtsOrderId());
            invalidOrderReq.setUpdaterId(tlOpenApiPropertiesConfig.getAdminId());
            InvalidOrderRes invalidOrderRes = erpTLOrderService.invalidOrder(invalidOrderReq);
            if (invalidOrderRes.getFlag() == 1) {
                //log.info("财务作废审核——>螳螂作废同步成功，返回：{}", JSONUtil.toJsonStr(invalidOrderRes));
            } else {
                //log.info("财务作废审核——>螳螂作废同步失败，返回：{}", JSONUtil.toJsonStr(invalidOrderRes));
            }
        }
    }

    /**
     * 订单提单审核操作。
     *
     * @param erpOrderExamineDTO 审核对象。
     * <AUTHOR>
     * @since 2022-04-24 11:47:07
     */
    public Map<String, String> createOrderExamineOperation(ErpOrderExamineDTO erpOrderExamineDTO) {
        Long orderId = erpOrderExamineDTO.getOrderId();
        Integer examineRole = erpOrderExamineDTO.getExamineRole();
        Integer examineOperation = erpOrderExamineDTO.getExamineOperation();
        Map<String, String> resultMap = new HashMap<>();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();

        // 当前操作订单的数据库内信息。
        ErpOrders erpOrdersBase = this.selectErpOrdersById(orderId);

        // 构建订单对象。
        ErpOrders erpOrders = new ErpOrders();
        OnlineContractEntity onlineContract = new OnlineContractEntity();
        erpOrders.setId(orderId);

        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);

        // 进行业务处理。
        if (examineRole.equals(1)) {
            // region 部门经理操作。
            if (examineOperation.equals(2)) {
                // region 经理审核提单驳回。
                // 维护订单提单审核状态：经理审核驳回。
                erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType());
                //合同设置为已发放
                contractChange(orders);
                //优惠券制订为未使用
                modifyCoupons(orderId);

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_REJECTED_M.getTypeInt());
                // endregion
                //企照多经理驳回更新执照状态
                if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                    //企照多经理驳回更新状态
                    if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                        int status = updateStatusByNumber(orders.getLicenseNumber(), "1");
                        if (status == 0) {
                            throw new ServiceException("更新执照状态失败");
                        }
                    }
                }
            } else if (examineOperation.equals(1)) {
                // region 经理审核提单通过。
                // 维护订单提单审核状态：非电子合同：财务待审核，电子合同：待客户签约。
                if (1 == erpOrdersBase.getIsElectronicContract()) {
                    erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.CUSTOMER_SIGN_NOT.getStatusType());
                    //订单修改如果不驳回电子时，设置状态为待财务审核
                    if (Objects.nonNull(orders) && 0 == orders.getIsReturn()) {
                        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                        erpOrders.setCreateArriveFinance(new Date());
                    }
                } else {
                    if (DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).after(erpOrdersBase.getDatSigningDatecreatedTime())) {
                        //判断是否上传了收款截图,如果没有则跳转至待上传收款截图，如果有则跳转至待财务审核。
                        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.UNUPLOAD_PROCEEDS_SCREENSHOT.getStatusType());

                        List<PaymentTermInfoVo> paymentTermInfoVo = erpOrderPaymentTermInfoMapper.getPaymentTermInfoVo(orderId);
                        if (CollectionUtils.isNotEmpty(paymentTermInfoVo)) {
                            for (PaymentTermInfoVo termInfoVo : paymentTermInfoVo) {
                                if (StringUtils.isNotEmpty(termInfoVo.getVcPaymentUrl())) {
                                    erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                                    erpOrders.setCreateArriveFinance(new Date());
                                    break;
                                }
                            }
                        }
                    } else {
                        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                        erpOrders.setCreateArriveFinance(new Date());
                    }
                }


                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_PASS_M.getTypeInt());
                // endregion
            }

            // 法务提单审核
        } else if (examineRole.equals(3)) {
            // region 法务操作。
            if (examineOperation.equals(2)) {
                // region 法务提单驳回。
                // 维护订单提单审核状态：法务审核驳回。
                erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.LEGAL_REJECTED.getStatusType());

                //优惠券制订为未使用
                modifyCoupons(orderId);

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_REJECTED_L.getTypeInt());
                // endregion
            } else if (examineOperation.equals(1)) {
                // region 法务审核提单通过。
                // 维护订单提单审核状态：非电子合同：财务待审核，电子合同：待客户签约。
                if (1 == erpOrdersBase.getIsElectronicContract()) {
                    erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.CUSTOMER_SIGN_NOT.getStatusType());
                } else {
                    erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                    erpOrders.setCreateArriveFinance(new Date());
                }

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_PASS_L.getTypeInt());
                // endregion
            }

        } else if (examineRole.equals(2)) {
            // region 财务会计操作。
            if (examineOperation.equals(2)) {
                // region 财务审核提单驳回。
                // 维护订单提单审核状态：财务审核驳回。
                erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType());
                erpOrders.setCreateApproveFinance(new Date());
                //合同状态设置为已发放
                contractChange(orders);
                //优惠券制订为未使用
                modifyCoupons(orderId);
                erpOrders.setIsReturn(erpOrderExamineDTO.getIsReturn());
                onlineContract.setStatus(erpOrderExamineDTO.getStatus());
                onlineContract.setOrderId(orderId.intValue());
                //维护电子合同状态
                this.updateOnlineContract(onlineContract);
                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_REJECTED_A.getTypeInt());
                // endregion
                //企照多财务驳回更新执照状态为待售
                if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                    //企照多提单完成更新状态
                    if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                        int status = updateStatusByNumber(orders.getLicenseNumber(), "1");
                        if (status == 0) {
                            throw new ServiceException("更新执照状态失败");
                        }
                    }

                }
            } else if (examineOperation.equals(1)) {
                // region 财务审核提单通过。
                //客户管理提单审核通过后创建一条已成交的线索
                //ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
                if (Objects.nonNull(orders)) {
                    ErpClient erpClient = erpClientMapper.selectErpClientById(orders.getNumClientId());
                    if (2 == orders.getCommitOrderType()) {
                        R<BdClue> clue = remoteCustomerService.addBdClueByPhone(orders.getVcPhone(), orders.getNumClientId(),
                                Objects.nonNull(erpClient)
                                        ? erpClient.getNumCityId()
                                        : null,
                                Objects.nonNull(erpClient)
                                        ? erpClient.getContactName()
                                        : null);

                        //log.info("-----clue----" + clue);
                        if (200 == clue.getCode()) {
                            BdClue data = clue.getData();
                            if (Objects.nonNull(data)) {
                                erpOrders.setNumClueId(data.getId());
                            }
                        }
                    }
                    //将用户置为已成交
                    if (erpClient.getNumStatus() == 2 || erpClient.getNumStatus() == 0) {
                        erpClient.setNumStatus(1);
                        erpClientMapper.updateErpClient(erpClient);
                    }
                    if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                        //企照多提单完成更新状态
                        if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                            int status = updateStatusByNumber(orders.getLicenseNumber(), "3");
                            if (status == 0) {
                                throw new ServiceException("更新执照状态失败");
                            }
                        }
                    }
                }

                // 维护订单类型。
                erpOrderTypeService.maintainOrderType(orderId);

                // 维护订单提单审核状态：财务审核通过。
                erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType());
                erpOrders.setCreateApproveFinance(new Date());

                // 维护订单状态：待服务。
                erpOrders.setNumStatus(Long.valueOf(OrderStatusEnum.ORDER_NOT_BIZ.getStatusType()));

                //维护电子合同状态
                OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                onlineContractEntity.setOrderId(orderId.intValue());
                onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntity.setUpdateTime(new Date());
                onlineContractEntity.setAuditStatus(1);
                onlineContractEntity.setStatusQuery(1);
                onlineContractEntity.setAuditStatusQuery(0);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);

                // 初始化服务单数据。
                resultMap = erpBizMainInfoService.createErpBizMainInfoByOrderId(orderId);

//                // 若为小程序订单。
//                if (erpOrdersBase.getNumSource() == 1) {
//                    // 订单产品表升降级。
//                    this.memberUp(orderId, 1);
//
//                    // 小程序优惠券记录维护。
//                    erpCouponUseRecordService.maintainCouponRecord(orderId, examineOperation);
//
//                }

                // 提单审核通过后需修改线索状态为已完成。
                if (ObjectUtil.isNotNull(erpOrdersBase.getNumClueId())) {
                    remoteCustomerService.completionClue(Long.valueOf(erpOrdersBase.getNumClueId()));
                }

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER_PASS_A.getTypeInt());

                // endregion
                ErpPaymentTermForCommitOrderDTO paymentTerm = erpOrderExamineDTO.getPaymentTerm();
                for (ErpPaymentTermInfoForCommitOrderDTO paymentTermInfo : paymentTerm.getPaymentTermInfos()) {
                    List<ErpOrderPaymentTermInfo> erpOrderPaymentTermInfos = erpOrderPaymentTermInfoMapper.selectErpOrderPaymentTermInfoByTermId(paymentTermInfo.getPaymentId());
                    for (ErpOrderPaymentTermInfo orderPaymentTermInfo : erpOrderPaymentTermInfos) {
                        orderPaymentTermInfo.setFinanceTime(DateUtil.parse(paymentTermInfo.getFinanceTime()));
                        erpOrderPaymentTermInfoMapper.updateErpOrderPaymentTermInfo(orderPaymentTermInfo);
                    }

                }

                resultMap.put("examineRole", "2");
                resultMap.put("examineOperation", "1");
            }
//            ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
//            erpServiceOrders.setNumOrderId(orderId);
//            List<ErpServiceOrders> erpServiceOrdersList = erpServiceOrdersMapper.selectErpServiceOrdersList(erpServiceOrders);
//            for (ErpServiceOrders serviceOrders : erpServiceOrdersList) {
//                List<ErpRetainageReturnDetail> erpRetainageReturnDetails = erpRetainageReturnDetailMapper.selectErpRetainageReturnDetailList(serviceOrders.getId());
//                for (ErpRetainageReturnDetail erpRetainageReturnDetail : erpRetainageReturnDetails) {
//                    ErpRetainageReturnDetail erpRetainageReturnDetailV = new ErpRetainageReturnDetail();
//                    if (erpRetainageReturnDetail.getPayType() == 1) {
//                        erpRetainageReturnDetailV.setNumCollectionPrice(serviceOrders.getNumPayPrice());
//                        erpRetainageReturnDetailV.setId(erpRetainageReturnDetail.getId());
//                        erpRetainageReturnDetailMapper.updateErpRetainageReturnDetail(erpRetainageReturnDetailV);
//                    }
//                    //删除产品
//                    if (serviceOrders.getNumStatus() == 0) {
//                        erpRetainageReturnDetailV.setPayType(4);
//                        erpRetainageReturnDetailV.setId(erpRetainageReturnDetail.getId());
//                        erpRetainageReturnDetailMapper.updateErpRetainageReturnDetail(erpRetainageReturnDetail);
//                    }
//                }
//
//                //添加产品
//                if (CollectionUtils.isEmpty(erpRetainageReturnDetails)) {
//                    ErpRetainageReturnDetail erpRetainageReturnDetail = new ErpRetainageReturnDetail();
//                    erpRetainageReturnDetail.setNumCollectionPrice(serviceOrders.getNumPayPrice());
//                    erpRetainageReturnDetail.setNumServiceOrderId(serviceOrders.getId());
//                    erpRetainageReturnDetail.setPayType(1);
//                    erpRetainageReturnDetail.setCreateTime(new Date());
//                    erpRetainageReturnDetail.setCreateUser(userId);
//                    erpRetainageReturnDetailMapper.insertErpRetainageReturnDetail(erpRetainageReturnDetail);
//                }


//                }
            // endregion
        }


        // 维护订单内产品审核状态。
        if (examineOperation.equals(2)) {
//            erpServiceOrdersService.updateStatusByOrderId(orderId, 0, 2);
        } else if (examineRole.equals(2)) {
            // 财务审核通过。
//            erpServiceOrdersService.updateStatusByOrderId(orderId, 0, 1);
        }

        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
//        erpOrders.setDatFinanceCollectionTime(DateUtil.parse(erpOrderExamineDTO.getFinanceTime()));
        if (examineOperation.equals(2)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        } else if (examineOperation.equals(1)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        }

        //财务审核添加财务收款时间
        if (2 == examineRole && 1 == examineOperation) {
            String time = "";
            //财务审核添加财务收款时间
            for (ErpPaymentTermInfoForCommitOrderDTO erpPaymentTermInfoForCommitOrderDTO : erpOrderExamineDTO.getPaymentTerm().getPaymentTermInfos()) {
                if (erpPaymentTermInfoForCommitOrderDTO.getNumStatus() == 1) {
                    time = erpPaymentTermInfoForCommitOrderDTO.getFinanceTime();
                }
            }
            if (StringUtils.isNotEmpty(time)) {
                String financeTime = time + " 00:00:00";
                DateTime parse = DateUtil.parse(financeTime, "yyyy-MM-dd");
                erpOrders.setDatFinanceCollectionTime(parse);
            } else {
                throw new ServiceException("财务收款时间为空！");
            }
        }

        // 维护订单信息。
        this.updateErpOrders(erpOrders);

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(erpOrderExamineDTO.getExamineRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);


        if (ObjectUtil.isNotNull(resultMap.get("msg"))) {
            // 维护操作记录。
            ErpOrderOperatingRecord erpOrderOperatingRecord1 = new ErpOrderOperatingRecord();
            erpOrderOperatingRecord1.setNumOrderId(orderId);
            erpOrderOperatingRecord1.setNumOperationType(OrderOperationTypeEnum.INIT_BIZ.getTypeInt());
            erpOrderOperatingRecord1.setNumCreatedBy(userId);
            erpOrderOperatingRecord1.setDatCreatedTime(nowDate);
            erpOrderOperatingRecord.setVcOperationContent(erpOrderExamineDTO.getExamineRemark());
            // 添加操作记录。
            erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord1);
        }

        return resultMap;
    }

    private void updateOnlineContract(OnlineContractEntity onlineContract) {
        onlineContractMapper.updateOnlineContract(onlineContract);
    }

    private void contractChange(ErpOrders erpOrders) {

        ErpContract erpContract = erpContractMapper.selectErpContractById(erpOrders.getNumContractId());
        if (erpContract != null) {
            erpContract.setNumStatus(2L);
            erpContractMapper.updateErpContract(erpContract);
        }
    }

    /**
     * 提单完成，更新执照企业状态
     *
     * @param licenseNumber
     * @param status
     * @return
     */
    private int updateStatusByNumber(String licenseNumber, String status) {
        net.sf.json.JSONObject jsonObject = new net.sf.json.JSONObject();
        jsonObject.put("license_number", licenseNumber);
        jsonObject.put("status", status);
        String json = HttpClientUtil.doPostJson(url + "java_api/license_transfer/statusByNumber", jsonObject);
        if (org.apache.commons.lang3.StringUtils.isEmpty(json)) {
            return 0;
        }
        if (JSONObject.parseObject(json).getInteger("data") <= 0) {
            return 0;
        }
        return 1;
    }

    /**
     * 订单修改审核操作。
     *
     * @param erpOrderExamineDTO 审核对象。
     * <AUTHOR>
     * @since 2022-04-24 11:47:07
     */
    public Map<String, String> modifyOrderExamineOperation(ErpOrderExamineDTO erpOrderExamineDTO) {
        Map<String, String> resultMap = new HashMap<>();
        Long orderId = erpOrderExamineDTO.getOrderId();
        Integer examineRole = erpOrderExamineDTO.getExamineRole();
        Integer examineOperation = erpOrderExamineDTO.getExamineOperation();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();
        String remark = erpOrderExamineDTO.getExamineRemark();
        // 构建订单对象。
        ErpOrders erpOrders = new ErpOrders();
        OnlineContractEntity onlineContract = new OnlineContractEntity();
        erpOrders.setId(orderId);
        ErpOrders erpOrdersBase = erpOrdersMapper.selectErpOrdersById(orderId);


        // 进行业务处理。
        if (examineRole.equals(1)) {
            // region 部门经理操作。
            if (examineOperation.equals(2)) {
                // region 经理审核驳回。
                // 维护订单修改审核状态：经理审核驳回。
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType());

                //维护电子合同状态
                OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                onlineContractEntity.setOrderId(orderId.intValue());
                onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntity.setUpdateTime(new Date());
                onlineContractEntity.setStatus(0);
                onlineContractEntity.setStatusQuery(1);
                onlineContractEntity.setAuditStatusQuery(0);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                //维护电子合同状态
                OnlineContractEntity onlineContractEntityTwo = new OnlineContractEntity();
                onlineContractEntityTwo.setOrderId(orderId.intValue());
                onlineContractEntityTwo.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntityTwo.setUpdateTime(new Date());
                onlineContractEntityTwo.setStatus(1);
                onlineContractEntityTwo.setStatusQuery(0);
                onlineContractEntityTwo.setAuditStatusQuery(1);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntityTwo);

                //取消绑定的新合同
                cancelBindNewContract(erpOrdersBase, erpOrders);
                //取消更换为空白合同
                erpOrders.setIsChangeBlankContract(0);
                //取消纸质更换为电子合同
                erpOrders.setIsPaperChangeOnline(0);
                //取消电子合同更换为纸质合同
                erpOrders.setIsOnlineChangePaper(0);
                //取消更换的合同类型
                erpOrders.setIsElectronicContractNew(-1);

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_REJECTED_M.getTypeInt());
                // endregion
            } else {
                // region 经理审核通过。
                // 维护订单修改审核状态：法务待审核。
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.YWDJR_NOT.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_PASS_M.getTypeInt());
                // endregion
                //查询该订单的的业务负责人  或  服务单类型的部门负责人，生成业务对接人审批流
                Long version = 0L;
                ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                erpServicePersonApproval.setOrderId(orderId);
                erpServicePersonApproval.setType(1);
                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalListByOrderId(erpServicePersonApproval);
                if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                    version = erpServicePersonApprovals.get(0).getVersion() + 1L;
                }

                List<SServiceMain> sServiceMains = sServiceMainMapper.selectSServiceMainListByOrderId(orderId);
                if (CollectionUtils.isNotEmpty(sServiceMains)) {
                    for (SServiceMain serviceMain : sServiceMains) {
                        ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                        personApproval.setOrderId(orderId);
                        personApproval.setType(1);
                        personApproval.setUserId(serviceMain.getUserId());
                        if (Objects.isNull(serviceMain.getUserId())) {
                            SConfigServiceCatalogue sConfigServiceCatalogue = sConfigServiceCatalogueMapper.selectSConfigServiceCatalogueById(serviceMain.getServiceCatalogue());
                            if (Objects.nonNull(sConfigServiceCatalogue)) {
                                setUserId(personApproval, sConfigServiceCatalogue.getWorkPlatformId(), serviceMain.getCityId());
                            }
                        }
                        personApproval.setVersion(version);
                        personApproval.setCreateBy(String.valueOf(userId));
                        personApproval.setUpdateBy(String.valueOf(userId));
                        erpServicePersonApprovalMapper.insertErpServicePersonApproval(personApproval);
                    }

                }
            }
            // endregion
        } else if (examineRole.equals(3)) {
            // region 法务操作。
            if (examineOperation.equals(2)) {
                // region 法务审核驳回。
                // 维护订单修改审核状态：法务审核驳回。
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.LEGAL_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_REJECTED_L.getTypeInt());
                // endregion
            } else {
                // region 法务审核通过。
                // 维护订单修改审核状态：财务待审核。
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_PASS_L.getTypeInt());
                // endregion
            }
            // endregion
        } else if (examineRole.equals(4)) {
            if (examineOperation.equals(2)) {
                // region 业务对接人审核驳回。
                // 维护订单修改审核状态和业务对接人审批表的状态：业务对接人审核驳回。
                //将整个审批流驳回
                ErpServicePersonApproval servicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());
                if (Objects.nonNull(servicePersonApproval) && (Objects.nonNull(servicePersonApproval.getOrderId()))) {
                    ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                    personApproval.setOrderId(servicePersonApproval.getOrderId());
                    personApproval.setVersion(servicePersonApproval.getVersion());
                    personApproval.setStatus(2);
                    erpServicePersonApprovalMapper.updateErpServicePersonApprovalByOrderId(personApproval);
                }

                OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                onlineContractEntity.setOrderId(orderId.intValue());
                onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntity.setUpdateTime(new Date());
                onlineContractEntity.setStatus(0);
                onlineContractEntity.setStatusQuery(1);
                onlineContractEntity.setAuditStatusQuery(0);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                //维护电子合同状态
                OnlineContractEntity onlineContractEntityTwo = new OnlineContractEntity();
                onlineContractEntityTwo.setOrderId(orderId.intValue());
                onlineContractEntityTwo.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntityTwo.setUpdateTime(new Date());
                onlineContractEntityTwo.setStatus(1);
                onlineContractEntityTwo.setStatusQuery(0);
                onlineContractEntityTwo.setAuditStatusQuery(1);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntityTwo);

                //取消绑定的新合同
                cancelBindNewContract(erpOrdersBase, erpOrders);
                //取消更换为空白合同
                erpOrders.setIsChangeBlankContract(0);
                //取消纸质更换为电子合同
                erpOrders.setIsPaperChangeOnline(0);
                //取消电子合同更换为纸质合同
                erpOrders.setIsOnlineChangePaper(0);
                //取消更换的合同类型
                erpOrders.setIsElectronicContractNew(-1);


                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.YWDUIJIEREN_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_REJECTED_L.getTypeInt());
            } else {
                // region 业务对接人审核通过。
                // 维护订单修改审核状态和业务对接人审批表的状态。
                //1:若已被其他业务对接人驳回，则无法审批通过
                ErpServicePersonApproval erpServicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());

                ErpServicePersonApproval personApprovalDto = new ErpServicePersonApproval();
                personApprovalDto.setOrderId(orderId);
                personApprovalDto.setVersion(erpServicePersonApproval.getVersion());

                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(personApprovalDto);
                List<Integer> collect = erpServicePersonApprovals.stream().map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect) && collect.contains(2)) {
                    throw new ServiceException("该订单已被其他业务对接人驳回");
                }
                List<Integer> collect_0 = erpServicePersonApprovals.stream().filter(val -> 0 == val.getStatus()).map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());

                //2:若只有一条待审批的数据，并且不存在驳回的数据，则修改业务对接人和订单的状态
                if ((CollectionUtils.isNotEmpty(collect_0) && collect_0.size() == 1)) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);
                    erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());

                    ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
                    Integer contract = null;
                    if(Objects.nonNull(orders.getIsElectronicContractNew()) && (-1 != orders.getIsElectronicContractNew())){
                        contract = orders.getIsElectronicContractNew();
                    }else {
                        contract = orders.getIsElectronicContract();
                    }
                    if ((Objects.nonNull(contract) && 1 == contract) && (Objects.nonNull(orders.getIsReturn()) && 1 == orders.getIsReturn()) &&
                            (Objects.isNull(erpOrdersBase.getIsChangeBlankContract()) || 0 == erpOrdersBase.getIsChangeBlankContract())) {
                        erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.CUSTOMER_SIGN_NOT.getStatusType());
                    }

                    // 填充操作记录类型。
                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());

                }

                //3:若存在其它待审批的的审批流，则只修改业务对接人审批流的的审批状态
                if (CollectionUtils.isNotEmpty(collect_0) && collect_0.size() >= 2) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);

                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());
                }
                // endregion
            }
        } else if (examineRole.equals(2)) {
            // region 财务会计操作。
            if (examineOperation.equals(2)) {
                // region 财务审核驳回。
                // 维护订单修改审核状态：财务审核驳回。
                erpOrders.setIsReturn(erpOrderExamineDTO.getIsReturn());
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType());
                //财务不驳回电子合同且变更了合同类型时更新合同类型
                if((Objects.nonNull(erpOrderExamineDTO.getIsReturn()) && 0 == erpOrderExamineDTO.getIsReturn())
                        && (Objects.nonNull(erpOrdersBase.getIsElectronicContractNew()) && (-1 != erpOrdersBase.getIsElectronicContractNew()))){
                    erpOrders.setIsElectronicContract(erpOrdersBase.getIsElectronicContractNew());
                }
                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_REJECTED_A.getTypeInt());
                //维护电子合同状态
                if(Objects.nonNull(erpOrderExamineDTO.getIsReturn()) && 1 == erpOrderExamineDTO.getIsReturn()){
                    OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                    onlineContractEntity.setOrderId(orderId.intValue());
                    onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                    onlineContractEntity.setUpdateTime(new Date());
                    onlineContractEntity.setStatus(0);
                    onlineContractEntity.setStatusQuery(1);
                    onlineContractEntity.setAuditStatusQuery(0);
                    onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                    //维护电子合同状态
                    OnlineContractEntity onlineContractEntityTwo = new OnlineContractEntity();
                    onlineContractEntityTwo.setOrderId(orderId.intValue());
                    onlineContractEntityTwo.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                    onlineContractEntityTwo.setUpdateTime(new Date());
                    onlineContractEntityTwo.setStatus(1);
                    onlineContractEntityTwo.setStatusQuery(0);
                    onlineContractEntityTwo.setAuditStatusQuery(1);
                    onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntityTwo);

                }

                //取消绑定的新合同
                cancelBindNewContract(erpOrdersBase, erpOrders);
                //取消更换为空白合同
                erpOrders.setIsChangeBlankContract(0);
                //取消纸质更换为电子合同
                erpOrders.setIsPaperChangeOnline(0);
                //取消电子合同更换为纸质合同
                erpOrders.setIsOnlineChangePaper(0);
                //取消更换的合同类型
                erpOrders.setIsElectronicContractNew(-1);
                // endregion
            } else {
                // region 财务审核通过。

                // 维护订单类型。
                erpOrderTypeService.maintainOrderType(orderId);

                // 维护订单修改审核状态：财务审核通过。
                erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_PASS_A.getTypeInt());
                //维护电子合同状态
                OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                onlineContractEntity.setOrderId(orderId.intValue());
                onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntity.setUpdateTime(new Date());
                onlineContractEntity.setAuditStatus(1);
                onlineContractEntity.setAuditStatusQuery(0);
                onlineContractEntity.setStatusQuery(1);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                //维护电子合同状态
                OnlineContractEntity onlineContractEntityTwo = new OnlineContractEntity();
                onlineContractEntityTwo.setOrderId(orderId.intValue());
                onlineContractEntityTwo.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                onlineContractEntityTwo.setUpdateTime(new Date());
                onlineContractEntityTwo.setAuditStatus(2);
                onlineContractEntityTwo.setStatusQuery(0);
                onlineContractEntityTwo.setAuditStatusQuery(1);
                onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntityTwo);

                resultMap.put("examineRole", "2");
                resultMap.put("examineOperation", "1");
                //绑定最新的客户ID
                //查询订单，企业 //更新服务单企业id和客户id
                ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
                ErpClient erpClient = erpClientMapper.selectErpClientById(orders.getNumClientIdNew());
                if (Objects.nonNull(orders.getNumClientIdNew())) {
//                    remark = remark + String.format(OperatingConstants.ORDER_EXAMINE, orders.getNumClientId(), orders.getNumClientIdNew());
//                    erpOrders.setNumClientId(orders.getNumClientIdNew());
                    SServiceMain sServiceMain = new SServiceMain();
                    sServiceMain.setOrderId(orderId);
                    sServiceMain.setClientId(orders.getNumClientIdNew());
                    sServiceMain.setNumEnterpriseId(erpClient.getNumEnterpriseId());
                    sServiceMainMapper.updateSServiceMainByOrderId(sServiceMain);
                }
                //绑定最新的合同
                bindNewContract(orders, erpOrders);
                //如果为更换空白合同，则释放当前合同；纸质更换为电子合同，释放绑定的纸质合同
                releaseContract(orders, erpOrders);
            }

            // region 修改订单内退款信息。
            erpServiceOrderRefundService.maintainOrderRefund(orderId, examineOperation);
            // endregion
        }

        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        if (examineOperation.equals(2)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        } else if (examineOperation.equals(1)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        }
        // 维护订单信息。
        this.updateErpOrders(erpOrders);

        // region 维护订单内产品信息。
        if ((examineRole.equals(1) && examineOperation.equals(2))
                || (examineRole.equals(4) && examineOperation.equals(2))
                || (examineRole.equals(2))) {

            // 获取订单内所有产品。
            List<ErpProductForOrderDetailVO> productForOrderList = getProductForDetail(orderId);

            for (ErpProductForOrderDetailVO product : productForOrderList) {
                ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
                erpServiceOrders.setId(product.getServiceOrderId());
                erpServiceOrders.setVcUpdatedBy(userId);
                erpServiceOrders.setDatUpdatedTime(nowDate);
                //0.正常1.退费，2.退费待审核，3作废，4作废待审核，5部分退费，6部分退费待审核
                if (2 == product.getIsDeprecated()) {
                    //退费---》驳回2，通过1
                    erpServiceOrders.setNumIsDeprecated(examineOperation.equals(2) ? 0 : 1);
                }
                if (4 == product.getIsDeprecated()) {
                    //作废---》驳回4，通过3
                    erpServiceOrders.setNumIsDeprecated(examineOperation.equals(2) ? 0 : 3);
                }
                if (6 == product.getIsDeprecated()) {
                    //部分退费---》驳回6，通过5
                    erpServiceOrders.setNumIsDeprecated(examineOperation.equals(2) ? 0 : 5);
                }
                // 修改产品信息。
                erpServiceOrdersService.updateErpServiceOrders(erpServiceOrders);

                //修改服务单状态
                if (examineRole.equals(2) && examineOperation.equals(1)) {
                    //9  作废;//10  退费;//11  部分退费;
                    if (2 == product.getIsDeprecated()) {
                        //退费---》驳回2，通过1
                        sServiceMainMapper.updateSServiceMainByOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_REFUND, orderId, product.getProductId());
                        sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_REFUND, orderId, product.getProductId());
                    }
                    if (4 == product.getIsDeprecated()) {
                        //作废---》驳回4，通过3
                        sServiceMainMapper.updateSServiceMainByOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_TO_VOID, orderId, product.getProductId());
                        sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_TO_VOID, orderId, product.getProductId());
                    }
                    if (6 == product.getIsDeprecated()) {
                        //部分退费---》驳回6，通过5
                        sServiceMainMapper.updateSServiceMainByOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_PARTIAL_REFUND, orderId, product.getProductId());
                        sServiceMainMapper.updateInternalSServiceMainBySouceOrderIdAndProductId(ServiceMainConstants.SERVICE_STATUS_PARTIAL_REFUND, orderId, product.getProductId());
                    }
                }
            }

            // region 维护订单价格。
            erpOrdersMapper.maintainOrderInfo(orderId);

            if (examineOperation.equals(1)) {
                // 财务审核通过，处理已废弃产品的服务单。
                //log.info("财务审核修改通过后，处理的产品::{}", JSONObject.toJSONString(productForOrderList));
            }
        }
        // endregion

        // 维护订单内产品审核状态。
        if (examineOperation.equals(2)) {
            //erpServiceOrdersService.updateStatusByOrderId(orderId, 0, 2);
        } else if (examineRole.equals(2) && examineOperation.equals(1)) {
            // 财务审核通过
            //log.info("初始化新增产品服务单");
            resultMap = erpBizMainInfoService.createErpBizMainInfoByOrderId(orderId);
        }

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(remark);
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        return resultMap;
    }

    /**
     * 取消绑定最新合同
     * @param orders
     * @param erpOrders
     */
    private void cancelBindNewContract(ErpOrders orders, ErpOrders erpOrders) {
        if ((Objects.nonNull(orders.getNumContractIdNew())) && (-1 != orders.getNumContractIdNew())) {
            erpOrders.setNumContractIdNew(-1L);
        }
    }

    /**
     * 绑定最新的合同
     * @param orders
     */
    private void bindNewContract(ErpOrders orders, ErpOrders erpOrders){
        //绑定最新的合同ID
        if ((Objects.nonNull(orders.getNumContractIdNew())) && (-1 != orders.getNumContractIdNew())) {
            erpOrders.setNumContractId(orders.getNumContractIdNew());
            erpOrders.setContactNum(commitOrderMapper.getContractNum(orders.getNumContractIdNew()));
            //修改原绑定的合同状态为：已发放。
            erpContractService.updateContractStatusById(orders.getNumContractId(), ErpProductConstants.ContractStatus.ISSUED);
            // 修改新合同使用状态为：已使用。选择合同编号
            erpContractService.updateContractStatusById(orders.getNumContractIdNew(), ErpProductConstants.ContractStatus.USED);
            erpOrders.setNumContractIdNew(-1L);
        }
        //更换最新的合同类型
        if (Objects.nonNull(orders.getIsElectronicContractNew()) && (-1 != orders.getIsElectronicContractNew())) {
            erpOrders.setIsElectronicContract(orders.getIsElectronicContractNew());
            erpOrders.setIsElectronicContractNew(-1);
        }
    }


    /**
     * 释放合同
     */
    private void releaseContract(ErpOrders orders, ErpOrders erpOrders) {
        Long userId = SecurityUtils.getUserId();

        Integer isChangeBlankContract = orders.getIsChangeBlankContract();
        Integer isPaperChangeOnline = orders.getIsPaperChangeOnline();
        Integer isOnlineChangePaper = orders.getIsOnlineChangePaper();
        if (Objects.nonNull(isChangeBlankContract) && 1 == isChangeBlankContract) {
            Integer isElectronicContract = orders.getIsElectronicContract();
            if (Objects.nonNull(isElectronicContract) && 2 == isElectronicContract) {
                //将订单绑定的纸质合同置空
                erpOrders.setReleaseContract(1);
                //修改原绑定的合同状态为：已发放。
                erpContractService.updateContractStatusById(orders.getNumContractId(), ErpProductConstants.ContractStatus.ISSUED);
            }
            if (Objects.nonNull(isElectronicContract) && 1 == isElectronicContract) {
                //将原先绑定的电子合同置为废弃
                onlineContractMapper.releaseOnlineContract(orders.getId());
            }
            erpOrders.setIsChangeBlankContract(0);
        }
        //纸质合同更换为电子合同，释放纸质合同
        if (Objects.nonNull(isPaperChangeOnline) && 1 == isPaperChangeOnline) {
            //将订单绑定的纸质合同置空
            erpOrders.setReleaseContract(1);
            //修改原绑定的合同状态为：已发放。
            erpContractService.updateContractStatusById(orders.getNumContractId(), ErpProductConstants.ContractStatus.ISSUED);
            erpOrders.setIsPaperChangeOnline(0);
        }
        //电子更换为纸质，释放电子合同链接
        if(Objects.nonNull(isOnlineChangePaper) && 1 == isOnlineChangePaper){
            //维护电子合同状态
            OnlineContractEntity onlineContractEntityTwo = new OnlineContractEntity();
            onlineContractEntityTwo.setOrderId(orders.getId().intValue());
            onlineContractEntityTwo.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
            onlineContractEntityTwo.setUpdateTime(new Date());
            onlineContractEntityTwo.setStatus(0);
            onlineContractEntityTwo.setStatusQuery(1);
            onlineContractEntityTwo.setAuditStatusQuery(1);
            onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntityTwo);

            //释放链接
            erpOrders.setReleaseEleContractUrl(1);
            erpOrders.setIsOnlineChangePaper(0);
        }
    }



    /**
     * 设置业务负责人ID
     *
     * @param personApproval
     * @param workPlatformId
     */
    private void setUserId(ErpServicePersonApproval personApproval, Long workPlatformId, Long cityId) {
        switch (workPlatformId.intValue()) {
            case 1:
                personApproval.setUserId(818L);
                break;
            case 2:
                if (136L == cityId) {
                    personApproval.setUserId(202L);
                }
                if (64L == cityId) {
                    personApproval.setUserId(191L);
                }
                break;
            case 4:
                personApproval.setUserId(5L);
                break;
            case 5:
                personApproval.setUserId(186L);
                break;
            default:
                personApproval.setUserId(1L);
        }
    }

    /**
     * 订单退款审核操作。
     *
     * @param erpOrderExamineDTO 审核对象。
     * <AUTHOR>
     * @since 2022-04-24 11:47:07
     */
    public Map<String, String> refundOrderExamineOperation(ErpOrderExamineDTO erpOrderExamineDTO) {
        Map<String, String> resultMap = new HashMap<>();
        Long orderId = erpOrderExamineDTO.getOrderId();
        Integer examineRole = erpOrderExamineDTO.getExamineRole();
        Integer examineOperation = erpOrderExamineDTO.getExamineOperation();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();
        Integer serviceType = null;
        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();

        // 当前操作订单的数据库内信息。
        ErpOrders erpOrdersBase = this.selectErpOrdersById(orderId);

        // 构建订单对象。
        ErpOrders erpOrders = new ErpOrders();
        OnlineContractEntity onlineContract = new OnlineContractEntity();
        erpOrders.setId(orderId);

        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);

        // 进行业务操作。
        if (examineRole.equals(1)) {
            // region 部门经理操作。
            if (examineOperation.equals(2)) {
                // region 经理审核驳回。
                // 维护订单修改审核状态：经理审核驳回。
                erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_REJECTED_M.getTypeInt());
                // endregion
            } else {
                // region 经理审核通过。
                // 维护订单修改审核状态：法务待审核。
                erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.YWDJR_NOT.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_PASS_M.getTypeInt());
                // endregion
                //查询该订单的的业务负责人  或  服务单类型的部门负责人，生成业务对接人审批流
                Long version = 0L;
                ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                erpServicePersonApproval.setOrderId(orderId);
                erpServicePersonApproval.setType(3);
                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalListByOrderId(erpServicePersonApproval);
                if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                    version = erpServicePersonApprovals.get(0).getVersion() + 1L;
                }

                List<SServiceMain> sServiceMains = sServiceMainMapper.selectSServiceMainListByOrderId(orderId);
                if (CollectionUtils.isNotEmpty(sServiceMains)) {
                    for (SServiceMain serviceMain : sServiceMains) {
                        ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                        personApproval.setOrderId(orderId);
                        personApproval.setType(3);
                        personApproval.setUserId(serviceMain.getUserId());
                        if (Objects.isNull(serviceMain.getUserId())) {
                            SConfigServiceCatalogue sConfigServiceCatalogue = sConfigServiceCatalogueMapper.selectSConfigServiceCatalogueById(serviceMain.getServiceCatalogue());
                            if (Objects.nonNull(sConfigServiceCatalogue)) {
                                setUserId(personApproval, sConfigServiceCatalogue.getWorkPlatformId(), serviceMain.getCityId());
                            }
                        }
                        personApproval.setVersion(version);
                        personApproval.setCreateBy(String.valueOf(userId));
                        personApproval.setUpdateBy(String.valueOf(userId));
                        erpServicePersonApprovalMapper.insertErpServicePersonApproval(personApproval);
                    }

                }
            }
            // endregion
        } else if (examineRole.equals(4)) {
            if (examineOperation.equals(2)) {
                // region 业务对接人审核驳回。
                // 维护订单修改审核状态和业务对接人审批表的状态：业务对接人审核驳回。
                //将整个审批流驳回
                ErpServicePersonApproval servicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());
                if (Objects.nonNull(servicePersonApproval) && (Objects.nonNull(servicePersonApproval.getOrderId()))) {
                    ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                    personApproval.setOrderId(servicePersonApproval.getOrderId());
                    personApproval.setVersion(servicePersonApproval.getVersion());
                    personApproval.setStatus(2);
                    erpServicePersonApprovalMapper.updateErpServicePersonApprovalByOrderId(personApproval);
                }

                erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.YWDUIJIEREN_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_REJECTED_L.getTypeInt());
            } else {
                // region 业务对接人审核通过。
                // 维护订单修改审核状态和业务对接人审批表的状态。
                //1:若已被其他业务对接人驳回，则无法审批通过
                ErpServicePersonApproval erpServicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());

                ErpServicePersonApproval personApprovalDto = new ErpServicePersonApproval();
                personApprovalDto.setOrderId(orderId);
                personApprovalDto.setVersion(erpServicePersonApproval.getVersion());

                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(personApprovalDto);
                List<Integer> collect = erpServicePersonApprovals.stream().map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect) && collect.contains(2)) {
                    throw new ServiceException("该订单已被其他业务对接人驳回");
                }
                List<Integer> collect_0 = erpServicePersonApprovals.stream().filter(val -> 0 == val.getStatus()).map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());

                //2:若只有一条待审批的数据，并且不存在驳回的数据，则修改业务对接人和订单的状态
                if ((CollectionUtils.isNotEmpty(collect_0) && (collect_0.size() == 1)) && (CollectionUtils.isNotEmpty(collect))) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);

                    erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                    // 填充操作记录类型。
                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());

                }

                //3:若存在其它待审批的的审批流，则只修改业务对接人审批流的的审批状态
                if (CollectionUtils.isNotEmpty(collect_0) && collect_0.size() >= 2) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);

                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());
                }
                // endregion
            }
        } else if (examineRole.equals(2)) {
            // region 财务会计操作。
            if (examineOperation.equals(2)) {
                // region 财务审核驳回。
                // 维护订单退款审核状态：财务审核驳回。
                erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType());
                serviceType = ServiceMainConstants.SERVICE_STATUS_PROCESSING;
                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.REFUND_ORDER_REJECTED_A.getTypeInt());
                onlineContract.setOrderId(orderId.intValue());
                onlineContract.setStatus(erpOrderExamineDTO.getStatus());
                //维护电子合同状态
                this.updateOnlineContract(onlineContract);
                // endregion
            } else {
                // region 财务审核通过。
                // 维护订单退款审核状态：财务审核通过。
                erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType());

                // 维护订单失效状态：已退款。
                erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_REFUNDED.getStatusType());
                serviceType = ServiceMainConstants.SERVICE_STATUS_REFUNDED;
                erpOrders.setIsReturn(erpOrderExamineDTO.getIsReturn());
                onlineContract.setOrderId(orderId.intValue());
                onlineContract.setStatus(erpOrderExamineDTO.getStatus());
                //合同号状态变更
                contractChange(orders);
                //优惠券制订为未使用
                modifyCoupons(orderId);

                // 若为小程序订单。
                if (erpOrdersBase.getNumSource() == 1) {
                    // 订单产品表升降级。
                    this.memberUp(orderId, 2);
                }

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.REFUND_ORDER_PASS_A.getTypeInt());
                resultMap.put("examineRole", "2");
                resultMap.put("examineOperation", "1");
                // endregion
                //企照多退款驳回更新执照状态
                if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                    //企照多退款驳回更新状态
                    if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                        int status = updateStatusByNumber(orders.getLicenseNumber(), "1");
                        if (status == 0) {
                            throw new ServiceException("更新执照状态失败");
                        }
                    }
                }
            }
            // endregion
        }

        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        if (examineOperation.equals(2)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        } else if (examineOperation.equals(1)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        }
        // 维护订单信息。
        this.updateErpOrders(erpOrders);
        if (ObjectUtil.isNotEmpty(serviceType)) {
            sServiceMainMapper.updateSServiceMainStatusByOrderId(orderId, serviceType);
        }

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(erpOrderExamineDTO.getExamineRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        // 修改订单内退款信息。
        erpServiceOrderRefundService.maintainOrderRefund(orderId, examineOperation);

        return resultMap;
    }

    /**
     * 订单作废审核操作。
     *
     * @param erpOrderExamineDTO 审核对象。
     * <AUTHOR>
     * @since 2022-04-24 11:47:07
     */
    public Map<String, String> cancelOrderExamineOperation(ErpOrderExamineDTO erpOrderExamineDTO) {
        Map<String, String> resultMap = new HashMap<>();
        Long orderId = erpOrderExamineDTO.getOrderId();
        Integer examineRole = erpOrderExamineDTO.getExamineRole();
        Integer examineOperation = erpOrderExamineDTO.getExamineOperation();
        Long userId = tokenService.getLoginUser().getUserid();
        Date nowDate = new Date();
        Integer serviceType = null;

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();

        // 当前操作订单的数据库内信息。
        ErpOrders erpOrdersBase = this.selectErpOrdersById(orderId);

        // 构建订单对象。
        ErpOrders erpOrders = new ErpOrders();
        OnlineContractEntity onlineContract = new OnlineContractEntity();
        erpOrders.setId(orderId);

        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);

        // 执行业务操作。
        if (examineRole.equals(1)) {
            // region 部门经理操作。
            if (examineOperation.equals(2)) {
                // region 经理审核驳回。
                // 维护订单修改审核状态：经理审核驳回。
                erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_REJECTED_M.getTypeInt());
                // endregion
            } else {
                // region 经理审核通过。
                // 维护订单修改审核状态：法务待审核。
                erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.YWDJR_NOT.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER_PASS_M.getTypeInt());
                // endregion
                //查询该订单的的业务负责人  或  服务单类型的部门负责人，生成业务对接人审批流
                Long version = 0L;
                ErpServicePersonApproval erpServicePersonApproval = new ErpServicePersonApproval();
                erpServicePersonApproval.setOrderId(orderId);
                erpServicePersonApproval.setType(2);
                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalListByOrderId(erpServicePersonApproval);
                if (CollectionUtils.isNotEmpty(erpServicePersonApprovals)) {
                    version = erpServicePersonApprovals.get(0).getVersion() + 1L;
                }

                List<SServiceMain> sServiceMains = sServiceMainMapper.selectSServiceMainListByOrderId(orderId);
                if (CollectionUtils.isNotEmpty(sServiceMains)) {
                    for (SServiceMain serviceMain : sServiceMains) {
                        ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                        personApproval.setOrderId(orderId);
                        personApproval.setType(2);
                        personApproval.setUserId(serviceMain.getUserId());
                        if (Objects.isNull(serviceMain.getUserId())) {
                            SConfigServiceCatalogue sConfigServiceCatalogue = sConfigServiceCatalogueMapper.selectSConfigServiceCatalogueById(serviceMain.getServiceCatalogue());
                            if (Objects.nonNull(sConfigServiceCatalogue)) {
                                setUserId(personApproval, sConfigServiceCatalogue.getWorkPlatformId(), serviceMain.getCityId());
                            }
                        }
                        personApproval.setVersion(version);
                        personApproval.setCreateBy(String.valueOf(userId));
                        personApproval.setUpdateBy(String.valueOf(userId));
                        erpServicePersonApprovalMapper.insertErpServicePersonApproval(personApproval);
                    }

                }
            }
            // endregion
        } else if (examineRole.equals(4)) {
            if (examineOperation.equals(2)) {
                // region 业务对接人审核驳回。
                // 维护订单修改审核状态和业务对接人审批表的状态：业务对接人审核驳回。
                //将整个审批流驳回
                ErpServicePersonApproval servicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());
                if (Objects.nonNull(servicePersonApproval) && (Objects.nonNull(servicePersonApproval.getOrderId()))) {
                    ErpServicePersonApproval personApproval = new ErpServicePersonApproval();
                    personApproval.setOrderId(servicePersonApproval.getOrderId());
                    personApproval.setVersion(servicePersonApproval.getVersion());
                    personApproval.setStatus(2);
                    erpServicePersonApprovalMapper.updateErpServicePersonApprovalByOrderId(personApproval);
                }

                erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.YWDUIJIEREN_REJECTED.getStatusType());

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_REJECTED_L.getTypeInt());
            } else {
                // region 业务对接人审核通过。
                // 维护订单修改审核状态和业务对接人审批表的状态。
                //1:若已被其他业务对接人驳回，则无法审批通过
                ErpServicePersonApproval erpServicePersonApproval = erpServicePersonApprovalMapper.selectErpServicePersonApprovalById(erpOrderExamineDTO.getApprovalId());

                ErpServicePersonApproval personApprovalDto = new ErpServicePersonApproval();
                personApprovalDto.setOrderId(orderId);
                personApprovalDto.setVersion(erpServicePersonApproval.getVersion());

                List<ErpServicePersonApproval> erpServicePersonApprovals = erpServicePersonApprovalMapper.selectErpServicePersonApprovalList(personApprovalDto);
                List<Integer> collect = erpServicePersonApprovals.stream().map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect) && collect.contains(2)) {
                    throw new ServiceException("该订单已被其他业务对接人驳回");
                }
                List<Integer> collect_0 = erpServicePersonApprovals.stream().filter(val -> 0 == val.getStatus()).map(ErpServicePersonApproval::getStatus).collect(Collectors.toList());

                //2:若只有一条待审批的数据，并且不存在驳回的数据，则修改业务对接人和订单的状态
                if ((CollectionUtils.isNotEmpty(collect_0) && collect_0.size() == 1) && (CollectionUtils.isNotEmpty(collect))) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);

                    erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
                    // 填充操作记录类型。
                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());

                }

                //3:若存在其它待审批的的审批流，则只修改业务对接人审批流的的审批状态
                if (CollectionUtils.isNotEmpty(collect_0) && collect_0.size() >= 2) {
                    ErpServicePersonApproval personApprovalUpdate = new ErpServicePersonApproval();
                    personApprovalUpdate.setId(erpOrderExamineDTO.getApprovalId());
                    personApprovalUpdate.setStatus(1);
                    erpServicePersonApprovalMapper.updateErpServicePersonApproval(personApprovalUpdate);

                    erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.YEWUDUIJIEREN_PASS_A.getTypeInt());
                }
                // endregion
            }
        } else if (examineRole.equals(2)) {
            // region 财务会计操作。
            if (examineOperation.equals(2)) {
                // region 财务审核驳回。
                // 维护订单作废审核状态：财务审核驳回。
                erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_REJECTED.getStatusType());
                serviceType = ServiceMainConstants.SERVICE_STATUS_PROCESSING;
                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_CANCEL_REJECTED_A.getTypeInt());
                onlineContract.setOrderId(orderId.intValue());
                onlineContract.setStatus(erpOrderExamineDTO.getStatus());
                //维护电子合同状态
                this.updateOnlineContract(onlineContract);
                // endregion
            } else {
                // region 财务审核通过。
                // 维护订单作废审核状态：财务审核通过。
                erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_PASS.getStatusType());
                onlineContract.setOrderId(orderId.intValue());
                //合同设置为已发放
                contractChange(orders);
                //优惠券制订为未使用
                modifyCoupons(orderId);

                // 维护订单失效状态：已作废。
                erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_DEPRECATED.getStatusType());
                serviceType = ServiceMainConstants.SERVICE_STATUS_REVOKE;
                // 若为小程序订单。
                if (erpOrdersBase.getNumSource() == 1) {
                    // 订单产品表升降级。
                    this.memberUp(orderId, 3);
                }

                // 填充操作记录类型。
                erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_CANCEL_PASS_A.getTypeInt());
                // endregion
                //企照多订单作废更新执照状态
                if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                    //企照多订单作废更新状态
                    if (StringUtils.isNotEmpty(orders.getLicenseNumber())) {
                        int status = updateStatusByNumber(orders.getLicenseNumber(), "1");
                        if (status == 0) {
                            throw new ServiceException("更新执照状态失败");
                        }
                    }
                }
            }
            resultMap.put("examineRole", "2");
            resultMap.put("examineOperation", "1");
            // endregion
        }

        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        if (examineOperation.equals(2)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        } else if (examineOperation.equals(1)) {
            erpOrders.setVcRemark(erpOrderExamineDTO.getExamineRemark());
        }
        // 维护订单信息。
        this.updateErpOrders(erpOrders);
        if (ObjectUtil.isNotEmpty(serviceType)) {
            sServiceMainMapper.updateSServiceMainStatusByOrderId(orderId, serviceType);
        }

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(erpOrderExamineDTO.getExamineRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);
        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        return resultMap;
    }

    /**
     * 优惠券恢复
     *
     * @param orderId
     */
    public void modifyCoupons(Long orderId) {
        ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
        erpServiceOrders.setNumOrderId(orderId);
        List<ErpServiceOrders> serviceOrders = erpServiceOrdersMapper.selectErpServiceOrdersList(erpServiceOrders);
        if (CollUtil.isNotEmpty(serviceOrders)) {
            List<Long> couponIds = serviceOrders.stream().map(ErpServiceOrders::getNumCouponId).collect(Collectors.toList());
            List<ErpDiscountCoupon> erpDiscountCoupons = erpDiscountCouponMapper.selectErpDiscountCouponByIds(couponIds);
            if (CollUtil.isNotEmpty(erpDiscountCoupons)) {
                for (ErpDiscountCoupon erpDiscountCoupon : erpDiscountCoupons) {
                    if (1 == erpDiscountCoupon.getStatus()) {
//                        //优惠券
//                        if (1 == erpDiscountCoupon.getNumType()) {
//                            ErpDiscountCouponAmount couponAmount = erpDiscountCouponAmountMapper.selectErpDiscountCouponAmountById(erpDiscountCoupon.getDiscountCouponAmountId());
//                            if (ObjectUtil.isNotEmpty(couponAmount)) {
//                                erpDiscountCoupon.setStatus(0L);
//                                couponAmount.setUsingAmount(couponAmount.getUsingAmount().subtract(erpDiscountCoupon.getDiscountAmount()));
//                                couponAmount.setSurplusAmount(couponAmount.getSurplusAmount().add(erpDiscountCoupon.getDiscountAmount()));
//                                //更新优惠券状态
//                                erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
//                                //更新总额度
//                                erpDiscountCouponAmountMapper.updateErpDiscountCouponAmount(couponAmount);
//                            }
//                        }
//                        //折扣券
//                        if (2 == erpDiscountCoupon.getNumType()) {
//                            erpDiscountCoupon.setStatus(0L);
//                            //更新优惠券状态
//                            erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
//                        }

                        erpDiscountCoupon.setStatus(0L);
                        //更新优惠券状态
                        erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
                    }
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> getOrderListConfig() {
        return erpOrdersMapper.getOrderListConfig();
    }

    @DataScope(deptAlias = "sd1", userAlias = "su1")
    @Override
    public List<SalesListingVo> getSalesListing(SaleListingDto saleListingDto) {

        List<SalesListingVo> salesListingVos = erpOrdersMapper.getSalesListing(saleListingDto);

        List<Long> collect = salesListingVos.stream().map(SalesListingVo::getOrderId).collect(Collectors.toList());
        ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
        if (CollectionUtils.isNotEmpty(collect)) {
            query.setOrderIdList(collect);
        }

        List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
        //log.info("订单列表查询电子合同信息为::{},{}", new Date(), onlieContractByOrderIdList);

        //查询合同号
        Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
            map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
            //log.info("订单列表查询分组后的电子合同信息为::{},{}", new Date(), map);
        }

        /**
         * 查询合同号
         */
        for (SalesListingVo salesListingVo : salesListingVos) {
            if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(salesListingVo.getIsElectronicContract()) && 1 == salesListingVo.getIsElectronicContract())) {
                if (map.containsKey(salesListingVo.getOrderId())) {
                    List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(salesListingVo.getOrderId());
                    if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                        StringBuffer vcContractNumber = new StringBuffer();
                        int size = erpOrderInfoForOmListVOS.size();
                        for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                            if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                            } else {
                                vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                            }
                        }
                        salesListingVo.setContractNumber(String.valueOf(vcContractNumber));
                    }
                }
            }
            //查询收款人
            Integer type = salesListingVo.getType();
            if (Objects.nonNull(type)) {
                switch (type) {
                    case 1:
                        ErpOrderPaymentTerm erpOrderPaymentTerm = new ErpOrderPaymentTerm();
                        erpOrderPaymentTerm.setNumOrderId(salesListingVo.getOrderId());
                        erpOrderPaymentTerm.setNumStatus(1);
                        List<ErpOrderPaymentTerm> erpOrderPaymentTerms = erpOrderPaymentTermMapper.selectErpOrderPaymentTermList(erpOrderPaymentTerm);
                        if (CollectionUtils.isNotEmpty(erpOrderPaymentTerms)) {
                            R<SysUser> info = remoteUserService.getUserInfoById(erpOrderPaymentTerms.get(0).getNumPayee(), SecurityConstants.INNER);
                            if (200 == info.getCode()) {
                                salesListingVo.setPayee(info.getData().getNickName());
                                salesListingVo.setPayeeDeptName(info.getData().getDept().getDeptName());
                                salesListingVo.setDatCollectionTime(erpOrderPaymentTerms.get(0).getDatCollectionTime());
                            }
                        }
                        break;
                    case 2:
                        ErpOrderPaymentTerm erpOrderPaymentTerm2 = new ErpOrderPaymentTerm();
                        erpOrderPaymentTerm2.setNumRetainageId(salesListingVo.getNumRetainageId());
                        erpOrderPaymentTerm2.setNumStatus(1);
                        List<ErpOrderPaymentTerm> erpOrderPaymentTerms2 = erpOrderPaymentTermMapper.selectErpOrderPaymentTermList(erpOrderPaymentTerm2);
                        if (CollectionUtils.isNotEmpty(erpOrderPaymentTerms2)) {
                            R<SysUser> info = remoteUserService.getUserInfoById(erpOrderPaymentTerms2.get(0).getNumPayee(), SecurityConstants.INNER);
                            if (200 == info.getCode()) {
                                salesListingVo.setPayee(info.getData().getNickName());
                                salesListingVo.setPayeeDeptName(info.getData().getDept().getDeptName());
                                salesListingVo.setDatCollectionTime(erpOrderPaymentTerms2.get(0).getDatCollectionTime());
                            }
                        }
                        break;
                }
            }

            //查询是否完结
            SServiceMain sServiceMain = sServiceMainMapper.selectSServiceMainById(salesListingVo.getServiceMainId());
            if (Objects.nonNull(sServiceMain)) {
                //节点状态
                SConfigServicePointStatus sConfigServicePointStatus = sConfigServicePointStatusMapper.selectSConfigServicePointStatusById(sServiceMain.getServicePointStatus());
                if (Objects.nonNull(sConfigServicePointStatus)) {
                    if (Objects.nonNull(sConfigServicePointStatus.getIsEnd()) && 1 == sConfigServicePointStatus.getIsEnd()) {
                        salesListingVo.setIsFinish(1);
                    }
                    if (Objects.nonNull(sConfigServicePointStatus.getIsEnd()) && 0 == sConfigServicePointStatus.getIsEnd()) {
                        salesListingVo.setIsFinish(2);
                    }
                    if(sConfigServicePointStatus.getId().equals(545L)){
                        salesListingVo.setIsFinish(1);
                    }
                }
                //转增值前的服务类型
                if (Objects.nonNull(sServiceMain.getTypeBeforeZz())) {
                    if (Objects.nonNull(sServiceMain.getServicePoint()) && 56L == sServiceMain.getServicePoint()) {
                        salesListingVo.setIsFinish(1);
                    } else {
                        salesListingVo.setIsFinish(2);
                    }
                }
                //记账或者税控
                if (Objects.nonNull(sServiceMain.getServiceType()) && (10 == (sServiceMain.getServiceType()) || 18 == sServiceMain.getServiceType())) {
                    salesListingVo.setIsFinish(Objects.nonNull(sServiceMain.getUserId()) ? 1 : 2);
                }

            }
            //查询产品原价
            List<ErpProductConfiguration> erpProductConfigurations = erpProductConfigurationMapper.selectProductByDeptId(salesListingVo.getNumProductId(), salesListingVo.getDeptId());
            salesListingVo.setProductOriginalPrice(CollectionUtils.isNotEmpty(erpProductConfigurations) ? erpProductConfigurations.get(0).getProductPrice() : BigDecimal.ZERO);
            //查询服务单当前跟进人部门
            if(Objects.nonNull(salesListingVo.getServiceUserId())){
                R<SysUser> info = remoteUserService.getUserInfoById(salesListingVo.getServiceUserId(), SecurityConstants.INNER);
                if(200 == info.getCode()){
                    salesListingVo.setServiceUserName(Objects.nonNull(info.getData()) ? info.getData().getNickName() : null);
                    salesListingVo.setServiceUserDept(Objects.nonNull(info.getData()) ? info.getData().getDept().getDeptName() : null);
                }
            }
            //查询服务节点和节点状态
            SConfigServicePoint sConfigServicePoint = sConfigServicePointMapper.selectSConfigServicePointById(salesListingVo.getServicePoint());
            salesListingVo.setServicePointName(Objects.nonNull(sConfigServicePoint) ? sConfigServicePoint.getName() : null);
            SConfigServicePointStatus sConfigServicePointStatus = sConfigServicePointStatusMapper.selectSConfigServicePointStatusById(salesListingVo.getServicePointStatus());
            salesListingVo.setServicePointStatusName(Objects.nonNull(sConfigServicePointStatus) ? sConfigServicePointStatus.getName() : null);

            //查询企业税号
            if(StringUtils.isNotEmpty(salesListingVo.getLicenseNumber())){
                ErpLicense erpLicense = erpLicenseMapper.getErpLicenseListByNumber(salesListingVo.getLicenseNumber());
                salesListingVo.setTaxNo(Objects.nonNull(erpLicense) ? erpLicense.getUnifiedSocialCreditCode() : null);
            }
        }
        return salesListingVos;
    }

    @DataScope(deptAlias = "sd1", userAlias = "su1")
    @Override
    public long getSalesListingCount(SaleListingDto saleListingDto) {
        return erpOrdersMapper.getSalesListingCount(saleListingDto);
    }

    @Override
    public List<LicenseSalesBoardVo> getLicenseSalesBoardVo(SaleListingDto saleListingDto) {
        List<LicenseSalesBoardVo> licenseSalesBoardVos = erpOrdersMapper.selectLicenseSalesBoardVo(saleListingDto);
        List<Long> collect = licenseSalesBoardVos.stream().map(LicenseSalesBoardVo::getOrderId).collect(Collectors.toList());
        ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
        if (CollectionUtils.isNotEmpty(collect)) {
            query.setOrderIdList(collect);
        }

        List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
        //log.info("订单列表查询电子合同信息为::{},{}", new Date(), onlieContractByOrderIdList);

        //查询合同号
        Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
            map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
            //log.info("订单列表查询分组后的电子合同信息为::{},{}", new Date(), map);
        }

        /**
         * 查询合同号
         */
        for (LicenseSalesBoardVo salesListingVo : licenseSalesBoardVos) {
            if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(salesListingVo.getIsElectronicContract()) && 1 == salesListingVo.getIsElectronicContract())) {
                if (map.containsKey(salesListingVo.getOrderId())) {
                    List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(salesListingVo.getOrderId());
                    if (CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                        StringBuffer vcContractNumber = new StringBuffer();
                        int size = erpOrderInfoForOmListVOS.size();
                        for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                            if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                            } else {
                                vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                            }
                        }
                        salesListingVo.setContractNumber(String.valueOf(vcContractNumber));
                    }
                }
            }
            //查询收款人
            Integer type = salesListingVo.getType();
            if (Objects.nonNull(type)) {
                switch (type) {
                    case 1:
                        ErpOrderPaymentTerm erpOrderPaymentTerm = new ErpOrderPaymentTerm();
                        erpOrderPaymentTerm.setNumOrderId(salesListingVo.getOrderId());
                        erpOrderPaymentTerm.setNumStatus(1);
                        List<ErpOrderPaymentTerm> erpOrderPaymentTerms = erpOrderPaymentTermMapper.selectErpOrderPaymentTermList(erpOrderPaymentTerm);
                        if (CollectionUtils.isNotEmpty(erpOrderPaymentTerms)) {
                            R<SysUser> info = remoteUserService.getUserInfoById(erpOrderPaymentTerms.get(0).getNumPayee(), SecurityConstants.INNER);
                            if (200 == info.getCode()) {
                                salesListingVo.setPayee(info.getData().getNickName());
                                salesListingVo.setPayeeDeptName(info.getData().getDept().getDeptName());
                            }
                        }
                        break;
                    case 2:
                        ErpOrderPaymentTerm erpOrderPaymentTerm2 = new ErpOrderPaymentTerm();
                        erpOrderPaymentTerm2.setNumRetainageId(salesListingVo.getNumRetainageId());
                        erpOrderPaymentTerm2.setNumStatus(1);
                        List<ErpOrderPaymentTerm> erpOrderPaymentTerms2 = erpOrderPaymentTermMapper.selectErpOrderPaymentTermList(erpOrderPaymentTerm2);
                        if (CollectionUtils.isNotEmpty(erpOrderPaymentTerms2)) {
                            R<SysUser> info = remoteUserService.getUserInfoById(erpOrderPaymentTerms2.get(0).getNumPayee(), SecurityConstants.INNER);
                            if (200 == info.getCode()) {
                                salesListingVo.setPayee(info.getData().getNickName());
                                salesListingVo.setPayeeDeptName(info.getData().getDept().getDeptName());
                            }
                        }
                        break;
                }
            }
        }
        return licenseSalesBoardVos;
    }

    @Override
    public JSONObject getDifferenceByOrderId(Long orderId) {
        JSONObject returnMsg = new JSONObject();
        returnMsg.put("code", 200);
        returnMsg.put("data", "success");
        if (ObjectUtil.isEmpty(orderId)) {
            returnMsg.put("data", "error");
            returnMsg.put("message", "订单Id不可为空");
            return returnMsg;
        }
        List<Long> idList = erpOrdersOperateRecordsMapper.selectIdListByOrderId(orderId);
        if (idList.size() < 2) {
            returnMsg.put("data", "warn");
            returnMsg.put("message", "此订单暂无修改记录;");
            return returnMsg;
        }
        if (idList.size() == 2) {
            ErpOrdersOperateRecords newRecord = new ErpOrdersOperateRecords();
            ErpOrdersOperateRecords oldRecord = new ErpOrdersOperateRecords();
            for (int i = 0; i < idList.size(); i++) {
                ErpOrdersOperateRecords records = erpOrdersOperateRecordsMapper.selectErpOrdersOperateRecordsById(idList.get(i));
                if (records.getStatus() == 1) {
                    oldRecord = records;
                }
                if (records.getStatus() == 2) {
                    newRecord = records;
                }
            }
            if (ObjectUtil.isEmpty(oldRecord) || oldRecord.getStatus() != 1 || ObjectUtil.isEmpty(oldRecord)
                    || ObjectUtil.isEmpty(newRecord) || newRecord.getStatus() != 2 || ObjectUtil.isEmpty(newRecord)) {
                returnMsg.put("data", "warn");
                returnMsg.put("message", "订单修改记录数据错误;");
                return returnMsg;
            }

            //对比修改记录
            com.alibaba.fastjson.JSONArray infoArr = new com.alibaba.fastjson.JSONArray();
            com.alibaba.fastjson.JSONArray paymentInfoArr = new com.alibaba.fastjson.JSONArray();
            com.alibaba.fastjson.JSONArray productArr = new com.alibaba.fastjson.JSONArray();

            JSONObject oldContent = JSONObject.parseObject(oldRecord.getOrderContent());
            JSONObject newContent = JSONObject.parseObject(newRecord.getOrderContent());

            //提单企业
            if (oldContent.containsKey("numEnterpriseId") && newContent.containsKey("numEnterpriseId")
                    && oldContent.getInteger("numEnterpriseId").compareTo(newContent.getInteger("numEnterpriseId")) != 0) {
                ErpEnterprise enterpriseOld = erpEnterpriseMapper.selectErpEnterpriseById(oldContent.getLong("numEnterpriseId"));
                ErpEnterprise enterpriseNew = erpEnterpriseMapper.selectErpEnterpriseById(newContent.getLong("numEnterpriseId"));

                JSONObject obj = new JSONObject();
                obj.put("name", "提单企业");
                obj.put("old", enterpriseOld.getVcCompanyName());
                obj.put("new", enterpriseNew.getVcCompanyName());
                infoArr.add(obj);
            }
            //优惠金额
            if (oldContent.containsKey("discountAmount") && newContent.containsKey("discountAmount")
                    && oldContent.getBigDecimal("discountAmount").compareTo(newContent.getBigDecimal("discountAmount")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单优惠金额");
                obj.put("old", oldContent.getBigDecimal("discountAmount"));
                obj.put("new", newContent.getBigDecimal("discountAmount"));
                infoArr.add(obj);
            }
            //实付金额
            if (oldContent.containsKey("payPrice") && newContent.containsKey("payPrice")
                    && oldContent.getBigDecimal("payPrice").compareTo(newContent.getBigDecimal("payPrice")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单实付金额");
                obj.put("old", oldContent.getBigDecimal("payPrice"));
                obj.put("new", newContent.getBigDecimal("payPrice"));
                infoArr.add(obj);
            }
            //退款金额
            if (oldContent.containsKey("refundPrice") && newContent.containsKey("refundPrice")
                    && oldContent.getBigDecimal("refundPrice").compareTo(newContent.getBigDecimal("refundPrice")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单退款金额");
                obj.put("old", oldContent.getBigDecimal("refundPrice"));
                obj.put("new", newContent.getBigDecimal("refundPrice"));
                infoArr.add(obj);
            }
            //尾款
            if (oldContent.containsKey("lastPrice") && newContent.containsKey("lastPrice")
                    && oldContent.getBigDecimal("lastPrice").compareTo(newContent.getBigDecimal("lastPrice")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单尾款金额");
                obj.put("old", oldContent.getBigDecimal("lastPrice"));
                obj.put("new", newContent.getBigDecimal("lastPrice"));
                infoArr.add(obj);
            }
            //订单总金额
            if (oldContent.containsKey("sumPrice") && newContent.containsKey("sumPrice")
                    && oldContent.getBigDecimal("sumPrice").compareTo(newContent.getBigDecimal("sumPrice")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单总金额");
                obj.put("old", oldContent.getBigDecimal("sumPrice"));
                obj.put("new", newContent.getBigDecimal("sumPrice"));
                infoArr.add(obj);
            }
            //订单总应付
            if (oldContent.containsKey("totalPrice") && newContent.containsKey("totalPrice")
                    && oldContent.getBigDecimal("totalPrice").compareTo(newContent.getBigDecimal("totalPrice")) != 0) {
                JSONObject obj = new JSONObject();
                obj.put("name", "订单总应付");
                obj.put("old", oldContent.getBigDecimal("totalPrice"));
                obj.put("new", newContent.getBigDecimal("totalPrice"));
                infoArr.add(obj);
            }

            JSONObject contractObj = new JSONObject();
            contractObj.put("name", "合同信息");
            if (oldContent.getInteger("isContract") == 1) {
                contractObj.put("old", "电子合同");
                contractObj.put("oldUrl", oldRecord.getOnlineContract());
            }
            if (oldContent.getInteger("isContract") == 2) {
                contractObj.put("old", "纸质合同："+erpContractMapper.selectErpContractById(oldContent.getLong("contractId")).getVcContractNumber());
            }
            if (oldContent.getInteger("isContract") == 3) {
                contractObj.put("old", "不需要合同");
            }
            if (newContent.getInteger("isContract") == 1) {
                contractObj.put("new", "电子合同");
                ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(orderId);
                contractObj.put("newUrl", erpOrders.getOnlineContractPdf());
            }
            if (newContent.getInteger("isContract") == 2) {
                contractObj.put("new", "纸质合同："+erpContractMapper.selectErpContractById(newContent.getLong("contractId")).getVcContractNumber());
            }
            if (newContent.getInteger("isContract") == 3) {
                contractObj.put("new", "不需要合同");
            }
            infoArr.add(contractObj);







            returnMsg.put("listInfo", infoArr);

            if (oldContent.containsKey("paymentTerm") && newContent.containsKey("paymentTerm")) {
                JSONObject paymentTermOld = oldContent.getJSONObject("paymentTerm");
                JSONObject paymentTermNew = newContent.getJSONObject("paymentTerm");

                //签约人
                if (paymentTermOld.containsKey("userId") && paymentTermNew.containsKey("userId")
                        && paymentTermOld.getInteger("userId").compareTo(paymentTermNew.getInteger("userId")) != 0) {

                    R<SysUser> infoOld = remoteUserService.getUserInfoById(paymentTermOld.getLong("userId"), SecurityConstants.INNER);
                    R<SysUser> infoNew = remoteUserService.getUserInfoById(paymentTermNew.getLong("userId"), SecurityConstants.INNER);

                    JSONObject obj = new JSONObject();
                    if (infoOld.getCode() == 200 && infoNew.getCode() == 200) {
                        obj.put("name", "签约人");
                        obj.put("old", infoOld.getData().getNickName());
                        obj.put("new", infoNew.getData().getNickName());
                    } else {
                        obj.put("name", "签约人");
                        obj.put("old", paymentTermOld.getLong("userId").toString());
                        obj.put("new", paymentTermNew.getLong("userId").toString());
                    }
                    paymentInfoArr.add(obj);
                }

                //收款信息
                if (paymentTermOld.containsKey("paymentTermInfos") && paymentTermNew.containsKey("paymentTermInfos")) {

                    com.alibaba.fastjson.JSONArray paymentTermInfosOld = paymentTermOld.getJSONArray("paymentTermInfos");
                    com.alibaba.fastjson.JSONArray paymentTermInfosNew = paymentTermNew.getJSONArray("paymentTermInfos");

                    if (paymentTermInfosOld.size() == 1 && paymentTermInfosNew.size() == 1) {

                        JSONObject paymentTermInfoOld = paymentTermInfosOld.getJSONObject(0);
                        JSONObject paymentTermInfoNew = paymentTermInfosNew.getJSONObject(0);

                        //收款人
                        if (paymentTermInfoOld.containsKey("payee") && paymentTermInfoNew.containsKey("payee")
                                && paymentTermInfoOld.getInteger("payee").compareTo(paymentTermInfoNew.getInteger("payee")) != 0) {

                            R<SysUser> infoOld = remoteUserService.getUserInfoById(paymentTermInfoOld.getLong("payee"), SecurityConstants.INNER);
                            R<SysUser> infoNew = remoteUserService.getUserInfoById(paymentTermInfoNew.getLong("payee"), SecurityConstants.INNER);

                            JSONObject obj = new JSONObject();
                            if (infoOld.getCode() == 200 && infoNew.getCode() == 200) {
                                obj.put("name", "收款人");
                                obj.put("old", infoOld.getData().getNickName());
                                obj.put("new", infoNew.getData().getNickName());
                            } else {
                                obj.put("name", "收款人");
                                obj.put("old", paymentTermInfoOld.getLong("payee").toString());
                                obj.put("new", paymentTermInfoNew.getLong("payee").toString());
                            }
                            paymentInfoArr.add(obj);
                        }

                        com.alibaba.fastjson.JSONArray paymentDetailsOld = paymentTermInfoOld.getJSONArray("paymentDetails");
                        com.alibaba.fastjson.JSONArray paymentDetailsNew = paymentTermInfoNew.getJSONArray("paymentDetails");
                        for (int z = 0; z < paymentDetailsOld.size(); z++) {
                            JSONObject detail = paymentDetailsOld.getJSONObject(z);
                            if (detail.containsKey("paymentUrl")) {
                                detail.put("paymentUrl", detail.getString("paymentUrl").split("\\?")[0]);
                            }
                        }
                        for (int z = 0; z < paymentDetailsNew.size(); z++) {
                            JSONObject detail = paymentDetailsNew.getJSONObject(z);
                            if (detail.containsKey("paymentUrl")) {
                                detail.put("paymentUrl", detail.getString("paymentUrl").split("\\?")[0]);
                            }
                        }


                        JSONObject obj = new JSONObject();
                        obj.put("name", "收款信息");
                        obj.put("old", paymentTermInfoOld.getJSONArray("paymentDetails"));
                        obj.put("new", paymentTermInfoNew.getJSONArray("paymentDetails"));
                        paymentInfoArr.add(obj);
                    }
                }
                returnMsg.put("listPayment", paymentInfoArr);
            }


            //产品信息
            if (oldContent.containsKey("products") && newContent.containsKey("products")) {
                com.alibaba.fastjson.JSONArray productsOld = oldContent.getJSONArray("products");
                com.alibaba.fastjson.JSONArray productsNew = newContent.getJSONArray("products");
                for (int i = 0; i < productsOld.size(); i++) {
                    JSONObject productOld = productsOld.getJSONObject(i);

                    JSONObject productObj = new JSONObject();
                    productObj.put("name", productOld.getString("productName")+"(id:"+productOld.getInteger("productId")+")");
                    com.alibaba.fastjson.JSONArray productIdArr = new com.alibaba.fastjson.JSONArray();
                    for (int j = 0; j < productsNew.size(); j++) {
                        JSONObject productNew = productsNew.getJSONObject(j);
                        if (productOld.getInteger("productId").compareTo(productNew.getInteger("productId")) == 0) {

                            //产品渠道费
                            if (productOld.containsKey("channelFee") && productNew.containsKey("channelFee")
                                    && productOld.getBigDecimal("channelFee").compareTo(productNew.getBigDecimal("channelFee")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品渠道费");
                                obj.put("old", productOld.getBigDecimal("channelFee"));
                                obj.put("new", productNew.getBigDecimal("channelFee"));
                                productIdArr.add(obj);
                            }
                            //产品优惠金额
                            if (productOld.containsKey("couponPrice") && productNew.containsKey("couponPrice")
                                    && productOld.getBigDecimal("couponPrice").compareTo(productNew.getBigDecimal("couponPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品优惠金额");
                                obj.put("old", productOld.getBigDecimal("couponPrice"));
                                obj.put("new", productNew.getBigDecimal("couponPrice"));
                                productIdArr.add(obj);
                            }
                            //产品尾款
                            if (productOld.containsKey("lastPrice") && productNew.containsKey("lastPrice")
                                    && productOld.getBigDecimal("lastPrice").compareTo(productNew.getBigDecimal("lastPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品尾款");
                                obj.put("old", productOld.getBigDecimal("lastPrice"));
                                obj.put("new", productNew.getBigDecimal("lastPrice"));
                                productIdArr.add(obj);
                            }
                            //产品实付
                            if (productOld.containsKey("payPrice") && productNew.containsKey("payPrice")
                                    && productOld.getBigDecimal("payPrice").compareTo(productNew.getBigDecimal("payPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品实付");
                                obj.put("old", productOld.getBigDecimal("payPrice"));
                                obj.put("new", productNew.getBigDecimal("payPrice"));
                                productIdArr.add(obj);
                            }
                            //产品数量
                            if (productOld.containsKey("productCount") && productNew.containsKey("productCount")
                                    && productOld.getInteger("productCount").compareTo(productNew.getInteger("productCount")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品数量");
                                obj.put("old", productOld.getInteger("productCount"));
                                obj.put("new", productNew.getInteger("productCount"));
                                productIdArr.add(obj);
                            }
                            //产品退款金额
                            if (productOld.containsKey("refundPrice") && productNew.containsKey("refundPrice")
                                    && productOld.getBigDecimal("refundPrice").compareTo(productNew.getBigDecimal("refundPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品退款金额");
                                obj.put("old", productOld.getBigDecimal("refundPrice"));
                                obj.put("new", productNew.getBigDecimal("refundPrice"));
                                productIdArr.add(obj);
                            }
                            //产品总价
                            if (productOld.containsKey("sumPrice") && productNew.containsKey("sumPrice")
                                    && productOld.getBigDecimal("sumPrice").compareTo(productNew.getBigDecimal("sumPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品总价");
                                obj.put("old", productOld.getBigDecimal("sumPrice"));
                                obj.put("new", productNew.getBigDecimal("sumPrice"));
                                productIdArr.add(obj);
                            }
                            //产品应收
                            if (productOld.containsKey("totalPrice") && productNew.containsKey("totalPrice")
                                    && productOld.getBigDecimal("totalPrice").compareTo(productNew.getBigDecimal("totalPrice")) != 0) {
                                JSONObject obj = new JSONObject();
                                obj.put("name", "产品应收");
                                obj.put("old", productOld.getBigDecimal("totalPrice"));
                                obj.put("new", productNew.getBigDecimal("totalPrice"));
                                productIdArr.add(obj);
                            }
                        }
                    }
                    if (productIdArr.size() > 0) {
                        productObj.put("list", productIdArr);
                        productArr.add(productObj);
                    }
                }
                returnMsg.put("listProduct", productArr);
            }
            return returnMsg;
        } else {
            returnMsg.put("data", "warn");
            returnMsg.put("message", "订单修改记录数据错误;");
            return returnMsg;
        }


    }

    public void mateTransactionVoucherFollowByOrderId(ErpOrderDetailForOmVO orderDetail) {
        List<ErpTransactionVoucherVo> returnList = new ArrayList<>();
        Long orderId = orderDetail.getOrder().getOrderId();
        List<Integer> payRecordIdList = new ArrayList<>();
        //如果orderId存在，查询是否存在流水
        //1待扣款数据，补回凭证   2已扣款数据，补回凭证     3待入账数据，补回凭证
        ErpTransactionVoucherFollowDto dto = new ErpTransactionVoucherFollowDto();
        dto.setStatusList(Arrays.asList(1L,2L,3L));
        dto.setOrderId(orderId);
        List<ErpTransactionVoucherFollowVo> followListUse = erpTransactionVoucherFollowMapper.selectList(dto);
        if (ObjectUtil.isNotEmpty(followListUse) && followListUse.size() > 0) {
            for (int i = 0; i < followListUse.size(); i++) {
                ErpTransactionVoucherFollowVo follow = followListUse.get(i);
                if (!payRecordIdList.contains(follow.getPayRecordId())) {
                    payRecordIdList.add(follow.getPayRecordId());
                }
            }
        }
        if (payRecordIdList.size() > 0) {

            //取出所有的 满足的收款本金（非活动本金）
            List<ErpTransactionVoucherVo> list = erpTransactionVoucherMapper.selectByPayRecordIdList(payRecordIdList, null, null);

            for (int i = 0; i < list.size(); i++) {
                ErpTransactionVoucherVo vo = list.get(i);
                com.alibaba.fastjson.JSONArray needProductArr = new com.alibaba.fastjson.JSONArray();

                for (int j = 0; j < followListUse.size(); j++) {
                    ErpTransactionVoucherFollowVo follow = followListUse.get(j);
                    boolean contain = false;
                    for (int z = 0; z < followListUse.size(); z++) {
                        if (follow.getId().equals(followListUse.get(z).getReleaseId())) {
                            contain = true;
                            break;
                        }
                    }
                    if (!contain) {
                        List<ErpTransactionVoucherFollowInfo> infoList = erpTransactionVoucherFollowInfoMapper.selectByFollowId(follow.getId());
                        if (ObjectUtil.isNotEmpty(infoList) && infoList.size() > 0) {
                            for (int z = 0; z < infoList.size(); z++) {
                                boolean containProduct = false;
                                for (int x = 0; x < needProductArr.size(); x++) {
                                    if (needProductArr.getJSONObject(x).getLong("productId").intValue() == infoList.get(z).getProductId().intValue()) {
                                        containProduct = true;
                                    }
                                }
                                if (containProduct) {
                                    for (int x = 0; x < needProductArr.size(); x++) {
                                        JSONObject productObj = needProductArr.getJSONObject(x);
                                        if (productObj.getLong("productId").intValue() == infoList.get(z).getProductId().intValue()) {
                                            productObj.put("fee", productObj.getBigDecimal("fee").add(infoList.get(z).getFee()));
                                            needProductArr.set(x, productObj);
                                        }
                                    }
                                } else {
                                    JSONObject productObj = new JSONObject();
                                    productObj.put("productId", infoList.get(z).getProductId());
                                    productObj.put("fee", infoList.get(z).getFee());
                                    needProductArr.add(productObj);
                                }
                            }
                        }
                    }
                }

                if (vo.getType().intValue() == 1) {
                    returnList.add(vo);
                }
            }
            for (int j = 0; j < returnList.size(); j++) {
                ErpTransactionVoucherVo returnVo = returnList.get(j);
                List<ErpTransactionVoucherVo> splitList = ObjectUtil.isEmpty(returnVo.getSplitList()) ? new ArrayList<>() : returnVo.getSplitList();
                for (int i = 0; i < list.size(); i++) {
                    ErpTransactionVoucherVo vo = list.get(j);
                    if (vo.getType() != 1 && vo.getPayRecordId().intValue() == returnVo.getPayRecordId().intValue()) {
                        splitList.add(vo);
                    }
                }
                returnVo.setSplitList(splitList);
            }
        }
        orderDetail.setTransactionVoucherList(returnList);
    }

    @Override
    @DataScope(deptAlias = "sd", userAlias = "su")
    public List<CollectionReportVo> collectionReport(CollectionReportDto dto) {
        List<CollectionReportVo> list = erpOrdersMapper.collectionReport(dto);

        List<Long> orderIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        List<Long> userIdList1 = new ArrayList<>();
        List<Long> couponIdList = new ArrayList<>();
        List<String> followIdList = new ArrayList<>();

        R<JSONObject> secondDeptObjR = remoteUserService.getAllDeptSecondDeptName();
        JSONObject secondDeptObj = secondDeptObjR.getData();

        for (int i = 0; i < list.size(); i++) {
            CollectionReportVo vo = list.get(i);
            if (ObjectUtil.isNotEmpty(vo.getPdServiceType()) && vo.getPdServiceType().intValue() == ServiceMainConstants.GeTiFaPiaoService
                    && ObjectUtil.isNotEmpty(vo.getGtKp()) && ObjectUtil.isNotEmpty(vo.getInvoiceCost())) {
                vo.setPerformanceCostGTKP(vo.getGtKp().multiply(vo.getInvoiceCost()).divide(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (ObjectUtil.isNotEmpty(vo.getRetainageReturnDetailIds()) && !vo.getRetainageReturnDetailIds().equals("")) {
                Map<String, Object> map = erpOrderPerformanceMapper.selectInfoByRetainageReturnDetailIds(vo.getRetainageReturnDetailIds());
                if (ObjectUtil.isNotEmpty(map)
                        && map.containsKey("performanceUserIdStr") && ObjectUtil.isNotEmpty(map.get("performanceUserIdStr"))
                        && map.containsKey("performancePrice") && ObjectUtil.isNotEmpty(map.get("performancePrice"))) {
                    vo.setPerformanceUserIdStr(map.get("performanceUserIdStr").toString());
                    vo.setPerformancePrice(new BigDecimal(map.get("performancePrice").toString()));
                }
            }

            if (ObjectUtil.isNotEmpty(vo.getOrderId()) && !orderIdList.contains(vo.getOrderId())) {
                orderIdList.add(vo.getOrderId());
            }
            if (ObjectUtil.isNotEmpty(vo.getPayeeUserId()) && !userIdList.contains(vo.getPayeeUserId())) {
                userIdList.add(vo.getPayeeUserId());
            }
            if (ObjectUtil.isNotEmpty(vo.getPerformanceUserIdStr())) {
                List<Long> performanceUserIdList = Arrays.asList(vo.getPerformanceUserIdStr().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                for (int j = 0; j < performanceUserIdList.size(); j++) {
                    if (!userIdList.contains(performanceUserIdList.get(j))) {
                        userIdList.add(performanceUserIdList.get(j));
                    }
                }
                vo.setPerformanceUserIdList(performanceUserIdList);
            }
            if (ObjectUtil.isNotEmpty(vo.getServiceUserId()) && !userIdList.contains(vo.getServiceUserId())) {
                userIdList.add(vo.getServiceUserId());
            }
            if (ObjectUtil.isNotEmpty(vo.getNumUserId()) && !userIdList.contains(vo.getNumUserId())) {
                userIdList.add(vo.getNumUserId());
            }
            if (ObjectUtil.isNotEmpty(vo.getNumProductCount()) && ObjectUtil.isNotEmpty(vo.getProductPrice())) {
                vo.setProductOriginalPrice(vo.getProductPrice().multiply(new BigDecimal(vo.getNumProductCount().toString())));
            }
            if (ObjectUtil.isNotEmpty(vo.getNumCombinedActivityId())) {
                vo.setDiscountType("组合活动");
            }
            if (!couponIdList.contains(vo.getNumCouponId()) && ObjectUtil.isNotEmpty(vo.getNumCouponId())) {
                couponIdList.add(vo.getNumCouponId());
            }
            if (ObjectUtil.isNotEmpty(vo.getOrderTotalPrice()) && ObjectUtil.isNotEmpty(vo.getOrderDiscountAmount())) {
                vo.setOrderPrice(vo.getOrderTotalPrice().add(vo.getOrderDiscountAmount()));
            }

            if (ObjectUtil.isNotEmpty(vo.getFollowIds())) {
                List<String> followIdList_ = Arrays.asList(vo.getFollowIds().split(","));
                for (int j = 0; j < followIdList_.size(); j++) {
                    if (!followIdList.contains(followIdList_.get(j))) {
                        followIdList.add(followIdList_.get(j));
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(vo.getPointStatusIsEnd()) && vo.getPointStatusIsEnd().intValue() == 1) {
                vo.setIsFinish(1);
            }
            if (ObjectUtil.isNotEmpty(vo.getSmServiceType()) && ObjectUtil.isNotEmpty(vo.getSmTypeBeforeZz())
                    && vo.getSmServiceType().intValue() == 9) {
                vo.setIsFinish(1);
            }
            //记账或者税控
            if (ObjectUtil.isNotEmpty(vo.getSmServiceType()) && ObjectUtil.isNotEmpty(vo.getServiceUserId())
                    && (10 == vo.getSmServiceType().intValue() || 18 == vo.getSmServiceType().intValue())) {
                vo.setIsFinish(1);
            }
            if (ObjectUtil.isNotEmpty(vo.getRefoundPrice())
                    && vo.getRefoundPrice().compareTo(new BigDecimal("0")) > 0
                    && ObjectUtil.isNotEmpty(vo.getProductNumPayPrice())) {
                vo.setIsRefund("是");
                if (vo.getRefoundPrice().compareTo(vo.getProductNumPayPrice()) < 0) {
                    vo.setRefoundType("部分退款");
                } else {
                    vo.setRefoundType("全额退款");
                }
            }


            List<Map<String, Object>> payPriceList = erpOrdersMapper.selectPayPriceByServiceOrderId(vo.getServiceOrderId(), DateUtils.dateTime(vo.getDatFinanceCollectionTime())+" 23:59:59");
            BigDecimal payPrice = new BigDecimal("0");
            List<String> followListProduct = new ArrayList<>();
            for (int j = 0; j < payPriceList.size(); j++) {
                payPrice = payPrice.add(new BigDecimal(payPriceList.get(j).get("fee").toString()));
                if (ObjectUtil.isNotEmpty(payPriceList.get(j).get("followIds"))
                        && ObjectUtil.isNotEmpty(payPriceList.get(j).get("followIds").toString())) {

                    String[] followListProducts = payPriceList.get(j).get("followIds").toString().split(",");
                    for (int z = 0; z < followListProducts.length; z++) {
                        if (!followListProduct.contains(followListProducts[z]) && ObjectUtil.isNotEmpty(followListProducts[z])) {
                            followListProduct.add(followListProducts[z]);
                        }
                    }
                }
            }
            if (followListProduct.size() > 0) {
                List<Map<String, Object>> followList = erpTransactionVoucherFollowMapper.selectMapByFollowIds(followListProduct, vo.getProductId(), 2L);
                for (int x = 0; x < followList.size(); x++) {
                    Map<String, Object> followMap = followList.get(x);
                    if (ObjectUtil.isNotEmpty(followMap.get("voucherType")) && Integer.parseInt(followMap.get("voucherType").toString()) == 3) {
                        payPrice = payPrice.subtract(new BigDecimal(followMap.get("fee").toString()));
                    }
                }
            }

            if (ObjectUtil.isNotEmpty(vo.getPdServiceType()) && vo.getPdServiceType().intValue() == 20) {
                vo.setKpFeeOver(erpWtdzKpMapper.selectKPFeeByServiceId(vo.getServiceMainId()));
                vo.setKpFeeLast(vo.getGeTiKp().subtract(vo.getKpFeeOver()));
            }

            vo.setProductPayPrice(payPrice);
        }

        if (orderIdList.size() > 0) {
            //查询合同号
            Map<Long, List<ErpOrderInfoForOmListVO>> map = new HashMap<>();

            ErpOrderQueryForOmListDTO query = new ErpOrderQueryForOmListDTO();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderIdList)) {
                query.setOrderIdList(orderIdList);
            }
            List<ErpOrderInfoForOmListVO> onlieContractByOrderIdList = erpOrdersMapper.getOnlieContractByOrderIdList(query);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(onlieContractByOrderIdList)) {
                map = onlieContractByOrderIdList.stream().collect(Collectors.groupingBy(ErpOrderInfoForOmListVO::getOrderId));
            }

            //查询订单总赠金
            List<Map<String, Object>> followList = erpTransactionVoucherFollowMapper.selectFeeMapByOrderIds(orderIdList);
            JSONObject giveFeeObj = new JSONObject();
            for (int i = 0; i < followList.size(); i++) {
                Map<String, Object> follow = followList.get(i);
                if (ObjectUtil.isNotEmpty(follow.get("fee"))
                        && ObjectUtil.isNotEmpty(follow.get("order_id")) && ObjectUtil.isNotEmpty(follow.get("product_id"))) {

                    Long orderId = Long.parseLong(follow.get("order_id").toString());
                    Long productId = Long.parseLong(follow.get("product_id").toString());
                    BigDecimal fee = new BigDecimal(follow.get("fee").toString());

                    JSONObject orderObj = giveFeeObj.containsKey(orderId.toString()) ? giveFeeObj.getJSONObject(orderId.toString()) : new JSONObject();
                    BigDecimal orderFee = orderObj.containsKey("orderFee") ? orderObj.getBigDecimal("orderFee") : new BigDecimal("0");
                    BigDecimal productFee = orderObj.containsKey(productId.toString()) ? orderObj.getBigDecimal(productId.toString()) : new BigDecimal("0");

                    orderObj.put("orderFee", orderFee.add(fee));
                    orderObj.put(productId.toString(), productFee.add(fee));
                    giveFeeObj.put(orderId.toString(), orderObj);

                }
            }
            for (CollectionReportVo vo : list) {
                if (Objects.nonNull(map) && map.size() > 0 && (Objects.nonNull(vo.getIsElectronicContract()) && 1 == vo.getIsElectronicContract())) {
                    if (map.containsKey(vo.getOrderId())) {
                        List<ErpOrderInfoForOmListVO> erpOrderInfoForOmListVOS = map.get(vo.getOrderId());
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(erpOrderInfoForOmListVOS)) {
                            StringBuffer vcContractNumber = new StringBuffer();
                            int size = erpOrderInfoForOmListVOS.size();
                            for (ErpOrderInfoForOmListVO erpOrderInfoForOmListVO : erpOrderInfoForOmListVOS) {
                                if (erpOrderInfoForOmListVOS.indexOf(erpOrderInfoForOmListVO) + 1 == size) {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber());
                                } else {
                                    vcContractNumber.append(erpOrderInfoForOmListVO.getVcContractNumber() + "，");
                                }
                            }
                            vo.setContractNumber(String.valueOf(vcContractNumber));
                        }
                    }
                }

                if (ObjectUtil.isNotEmpty(vo.getOrderTotalPrice()) && giveFeeObj.containsKey(vo.getOrderId().toString())) {
                    JSONObject orderObj = giveFeeObj.getJSONObject(vo.getOrderId().toString());
                    if (orderObj.containsKey("orderFee")) {
                        vo.setOrderTotalPrice(vo.getOrderTotalPrice().subtract(orderObj.getBigDecimal("orderFee")));
                    }
                    if (orderObj.containsKey(vo.getProductId().toString())) {
                        vo.setProductNumPayPrice(vo.getProductNumPayPrice().subtract(orderObj.getBigDecimal(vo.getProductId().toString())));
                    }
                }
            }
        }

        if (userIdList.size() > 0) {
            R<List<SysUser>> userListR = remoteUserService.getUserListByIds(StringUtils.join(userIdList, ","), SecurityConstants.INNER);
            List<SysUser> userList = userListR.getData();

            JSONObject userObj = new JSONObject();
            for (int i = 0; i < userList.size(); i++) {
                SysUser user = userList.get(i);
                if (!userObj.containsKey(user.getUserId().toString())) {
                    JSONObject obj = new JSONObject();
                    obj.put("nickName", user.getNickName());
                    obj.put("deptName", ObjectUtil.isNotEmpty(user.getDept()) ? user.getDept().getDeptName() : "");
                    obj.put("secondDeptName", secondDeptObj.containsKey(user.getDept().getDeptId().toString()) ? secondDeptObj.getString(user.getDept().getDeptId().toString()) : "");
                    userObj.put(user.getUserId().toString(), obj);
                }
            }

            for (int j = 0; j < list.size(); j++) {
                CollectionReportVo vo = list.get(j);
                if (ObjectUtil.isNotEmpty(vo.getPayeeUserId()) && userObj.containsKey(vo.getPayeeUserId().toString())) {
                    vo.setPayee(userObj.getJSONObject(vo.getPayeeUserId().toString()).getString("nickName"));
                    vo.setPayeeDeptName(userObj.getJSONObject(vo.getPayeeUserId().toString()).getString("deptName"));
                    vo.setPayeeSecondDeptName(userObj.getJSONObject(vo.getPayeeUserId().toString()).getString("secondDeptName"));
                }
                if (ObjectUtil.isNotEmpty(vo.getServiceUserId()) && userObj.containsKey(vo.getServiceUserId().toString())) {
                    vo.setServiceUserName(userObj.getJSONObject(vo.getServiceUserId().toString()).getString("nickName"));
                    vo.setServiceUserDept(userObj.getJSONObject(vo.getServiceUserId().toString()).getString("deptName"));
                }
                if (ObjectUtil.isNotEmpty(vo.getNumUserId()) && userObj.containsKey(vo.getNumUserId().toString())) {
                    vo.setSignSecondDeptName(userObj.getJSONObject(vo.getNumUserId().toString()).getString("secondDeptName"));
                }

                //分业绩人
                String performanceName = "";
                String performanceDeptName = "";
                if (ObjectUtil.isNotEmpty(vo.getPerformanceUserIdList())) {
                    for (int z = 0; z < vo.getPerformanceUserIdList().size(); z++) {
                        if (userObj.containsKey(vo.getPerformanceUserIdList().get(z).toString())) {

                            performanceName += "," + userObj.getJSONObject(vo.getPerformanceUserIdList().get(z).toString()).getString("nickName")
                                    + "(" + erpOrderPerformanceMapper.selectFeeByUserIdAndReturnId(vo.getPerformanceUserIdList().get(z).toString(), Arrays.asList(vo.getRetainageReturnDetailIds().split(","))) + ")";
                            performanceDeptName += "," + userObj.getJSONObject(vo.getPerformanceUserIdList().get(z).toString()).getString("deptName");
                        }
                    }
                    vo.setPerformanceName(performanceName.replaceFirst(",", ""));
                    vo.setPerformanceDeptName(performanceDeptName.replaceFirst(",", ""));
                }
            }
        }
        if (couponIdList.size() > 0) {
            List<Map<String, Object>> couponMapList = erpDiscountCouponMapper.selectDCMessage(couponIdList);
            JSONObject couponObj = new JSONObject();
            for (int j = 0; j < couponMapList.size(); j++) {
                Map<String, Object> couponMap = couponMapList.get(j);
                if (!couponMap.containsKey("id")) {
                    continue;
                }
                if (!couponObj.containsKey(couponMap.get("id").toString())) {
                    couponObj.put(couponMap.get("id").toString(), couponMap.get("serviceName").toString());
                }
            }

            for (int i = 0; i < list.size(); i++) {
                CollectionReportVo vo = list.get(i);
                if (ObjectUtil.isNotEmpty(vo.getNumCouponId()) && couponObj.containsKey(vo.getNumCouponId().toString())) {
                    vo.setDiscountType(couponObj.getString(vo.getNumCouponId().toString()));
                }
            }
        }
        if (followIdList.size() > 0) {
            List<Map<String, Object>> followList = erpTransactionVoucherFollowMapper.selectMapByFollowIds(followIdList, null, 2L);
            JSONObject followObj = new JSONObject();
            for (int i = 0; i < followList.size(); i++) {
                Map<String, Object> followMap = followList.get(i);
                if (!followMap.containsKey("followId") || ObjectUtil.isEmpty(followMap.get("followId"))
                        || !followMap.containsKey("productId") || ObjectUtil.isEmpty(followMap.get("productId"))
                        || !followMap.containsKey("fee") || ObjectUtil.isEmpty(followMap.get("fee"))
                        || !followMap.containsKey("voucherType") || ObjectUtil.isEmpty(followMap.get("voucherType"))) {
                    continue;
                }
                String followId = followMap.get("followId").toString();
                Long productId = Long.parseLong(followMap.get("productId").toString());
                BigDecimal fee = new BigDecimal(followMap.get("fee").toString());
                Integer voucherType = Integer.parseInt(followMap.get("voucherType").toString());
                Integer prestore = 0;
                String activitiesName = "";
                if (followMap.containsKey("prestore") || ObjectUtil.isNotEmpty(followMap.get("prestore"))) {
                    prestore = Integer.parseInt(followMap.get("prestore").toString());
                }
                if (followMap.containsKey("activitiesName") || ObjectUtil.isNotEmpty(followMap.get("activitiesName"))) {
                    activitiesName = followMap.get("activitiesName").toString();
                }

                JSONObject followIdObj = new JSONObject();
                if (followObj.containsKey(followId)) {
                    followIdObj = followObj.getJSONObject(followId);
                }
                JSONObject followInfoObj = new JSONObject();
                followInfoObj.put("fee", fee);
                followInfoObj.put("voucherType", voucherType);
                followInfoObj.put("prestore", prestore);
                followInfoObj.put("activitiesName", activitiesName);
                followIdObj.put(productId.toString(), followInfoObj);
                followObj.put(followId, followIdObj);
            }
            for (int i = 0; i < list.size(); i++) {
                CollectionReportVo vo = list.get(i);
                if (ObjectUtil.isNotEmpty(vo.getFollowIds())) {
                    List<String> followIdList_ = Arrays.asList(vo.getFollowIds().split(","));
                    for (int j = 0; j < followIdList_.size(); j++) {
                        if (followObj.containsKey(followIdList_.get(j))) {
                            JSONObject followIdObj = followObj.getJSONObject(followIdList_.get(j));
                            if (followIdObj.containsKey(vo.getProductId().toString())) {
                                JSONObject followProductObj = followIdObj.getJSONObject(vo.getProductId().toString());
                                if (followProductObj.getInteger("voucherType").intValue() == 3) {
                                    if (ObjectUtil.isNotEmpty(vo.getDiscountType())) {
                                        vo.setDiscountType(vo.getDiscountType() + "," + "凭证赠金");
                                    } else {
                                        vo.setDiscountType("凭证赠金");
                                    }
                                    if (ObjectUtil.isNotEmpty(vo.getCollectionPrice())) {
                                        vo.setCollectionPrice(vo.getCollectionPrice().subtract(followProductObj.getBigDecimal("fee")));
                                    }
                                }
                                if (followProductObj.getInteger("prestore").intValue() == 1
                                        && followProductObj.getInteger("voucherType").intValue() == 2) {

                                    if (ObjectUtil.isNotEmpty(vo.getDiscountType())) {
                                        vo.setDiscountType(vo.getDiscountType() + "," + followProductObj.getString("activitiesName"));
                                    } else {
                                        vo.setDiscountType(followProductObj.getString("activitiesName"));
                                    }
                                    BigDecimal activitiesSelfPrice = ObjectUtil.isNotEmpty(vo.getActivitiesSelfPrice()) ? vo.getActivitiesSelfPrice() : new BigDecimal("0");
                                    vo.setActivitiesSelfPrice(activitiesSelfPrice.add(followProductObj.getBigDecimal("fee")));
                                }


                            }
                        }
                    }
                }
            }
        }
        for (int i = 0; i < list.size(); i++) {
            CollectionReportVo vo = list.get(i);
            vo.setProductNumLastPrice(vo.getProductNumPayPrice().subtract(vo.getProductPayPrice()));
        }

        return list;
    }

    @Override
    public List<ErpOrderPaymentRecord> getCostSettleByOrderId(Long orderId) {
        List<ErpOrderPaymentRecord> erpOrderPaymentRecord = new ArrayList<>();
        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(orderId);
        if (Objects.nonNull(erpOrders)) {
            erpOrderPaymentRecord = erpQzdPaymentRecordMapper.getErpOrderPaymentRecord(erpOrders.getVcOrderNumber());
            for (int i = 0; i < erpOrderPaymentRecord.size(); i++) {
                ErpOrderPaymentRecord record = erpOrderPaymentRecord.get(i);
                record.setApproveStatusName("已通过");
                record.setApproveName("成本类-" + record.getApproveName());
            }
        }
        return erpOrderPaymentRecord;
    }


    public void checkOrderCanKp(List<ErpOrderInfoForOmListVO> resList) {
        if (ObjectUtil.isNotEmpty(resList) && resList.size() > 0) {
            for (int i = 0; i < resList.size(); i++) {
                ErpOrderInfoForOmListVO vo = resList.get(i);

                vo.setOverKP(2);
                //订单开票记录查询
                BigDecimal kpFeeInApprove = erpOrdersMapper.getKPFeeByOrderId(vo.getOrderId(), 0L);
                BigDecimal kpFeeOverApprove = erpOrdersMapper.getKPFeeByOrderId(vo.getOrderId(), 1L);
                if (kpFeeInApprove.compareTo(new BigDecimal("0")) > 0) {
                    vo.setOverKP(3);
                } else if (kpFeeOverApprove.compareTo(new BigDecimal("0")) > 0){
                    vo.setOverKP(1);
                }


                vo.setCanKP(1);
                /***
                 * 1.getNumValidStatus '作废状态：0：有效；1：已撤销；2：已作废；3：已退款；4：已部分退款；5：退款待审核；6部分退款待审核；'
                 * 2.订单创建审核通过
                 * 3.订单未修改或订单修改审核通过
                 * 4.订单未发起作废流程
                 */
                //0未参与，2经理审核驳回，5财务审核通过,4财务审核驳回,7法务审核驳回,14业务对接人驳回
                List<Integer> examineStatusList = Arrays.asList(0,2,4,5,7,14);
                if (Arrays.asList(1,2,3,5,6).contains(vo.getNumValidStatus().intValue())
                        || vo.getNumCreateOrderExamineStatus() != 5
                        || !examineStatusList.contains(vo.getNumModifyOrderExamineStatus())
                        || !examineStatusList.contains(vo.getNumCancelOrderExamineStatus())) {
                    vo.setCanKP(2);
                }
                BigDecimal refoundFee = new BigDecimal("0");
                //订单退款记录查询
                if (vo.getCanKP().intValue() == 1) {
                    List<Map<String, Object>> mapList = erpOrdersMapper.getRefoundInfoByOrderId(vo.getOrderId());
                    if (ObjectUtil.isNotEmpty(mapList) && mapList.size() > 0) {
                        for (int j = 0; j < mapList.size(); j++) {
                            Map<String, Object> map = mapList.get(j);
                            if (map.containsKey("approveId") && ObjectUtil.isNotEmpty(map.get("approveId"))
                                    && map.containsKey("approveStatus") && ObjectUtil.isNotEmpty(map.get("approveStatus"))
                                    && map.containsKey("refundAmount") && ObjectUtil.isNotEmpty(map.get("refundAmount"))) {
                                Integer approveStatus = Integer.parseInt(map.get("approveStatus").toString());
                                BigDecimal refundAmount = new BigDecimal(map.get("refundAmount").toString());

                                if (approveStatus == 0) {
                                    vo.setCanKP(2);
                                } else {
                                    refoundFee = refoundFee.add(refundAmount);
                                }
                            }
                        }
                    }
                }
                //赠金
                BigDecimal giveFee = new BigDecimal("0");
                if (vo.getCanKP().intValue() == 1) {
                    List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(vo.getOrderId());
                    for (int j = 0; j < serviceOrdersList.size(); j++) {
                        ErpServiceOrders erpServiceOrders = serviceOrdersList.get(j);
                        giveFee = giveFee.add(erpTransactionVoucherFollowInfoMapper.getGiveFeeByOrderIdAndProductId(vo.getOrderId(), erpServiceOrders.getNumProductId()));
                    }
                }
                BigDecimal kpFee = kpFeeInApprove.add(kpFeeOverApprove);
                BigDecimal noSupportKpFee = erpOrdersMapper.getNoSupportKPFeeByOrderId(vo.getOrderId());
                vo.setCanKpFee(vo.getPayPrice().subtract(giveFee).subtract(refoundFee).subtract(kpFee).subtract(noSupportKpFee));
            }
        }
    }
}
