package com.nnb.erp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import com.nnb.common.core.constant.SecurityConstants;
import com.nnb.common.core.domain.R;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.DateUtils;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.security.service.TokenService;
import com.nnb.erp.config.TlOpenApiPropertiesConfig;
import com.nnb.erp.constant.*;
import com.nnb.erp.constant.enums.*;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.approval.ErpNewApproval;
import com.nnb.erp.domain.dingding.DingSendDTO;
import com.nnb.erp.domain.dto.ErpDiscountCouponDto;
import com.nnb.erp.domain.dto.ErpTransactionVoucherFollowDto;
import com.nnb.erp.domain.dto.approval.ApprovalPassDTO;
import com.nnb.erp.domain.dto.approval.ErpNewApprovalDTO;
import com.nnb.erp.domain.dto.service.ServiceByEnterpriseDto;
import com.nnb.erp.domain.dto.license.ErpLicenseDTO;
import com.nnb.erp.domain.gift.*;
import com.nnb.erp.domain.tLOrder.TlClue;
import com.nnb.erp.domain.vo.*;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponOrderVo;
import com.nnb.erp.domain.vo.gift.ErpGiftVO;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftDetailVO;
import com.nnb.erp.domain.vo.gift.ErpOrderGiftIssueDetailVO;
import com.nnb.erp.domain.vo.onlineContract.CheckContractVO;
import com.nnb.erp.domain.vo.product.LinceseProductVo;
import com.nnb.erp.domain.vo.service.SServiceVo;
import com.nnb.erp.domain.xcx.XcxCommitOrderDto;
import com.nnb.erp.enums.ApprovalTypeEnum;
import com.nnb.erp.enums.ErpOrderPayRecordEnum;
import com.nnb.erp.mapper.*;
import com.nnb.erp.mapper.approval.ErpNewApprovalMapper;
import com.nnb.erp.mapper.gift.*;
import com.nnb.erp.service.*;
import com.nnb.erp.service.approval.IErpNewApprovalService;
import com.nnb.erp.util.HttpClientUtil;
import com.nnb.erp.util.MD5Util;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.RemoteUserService;
import com.nnb.system.api.domain.SysUser;
import com.nnb.system.api.model.BdClue;
import com.nnb.system.api.model.BdClueContacts;
import com.nnb.system.api.model.LoginUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提单，业务实现层。
 *
 * <AUTHOR>
 * @since 2022/3/15 16:37
 */
@Slf4j
@Service
@RefreshScope
public class ErpCommitOrderServiceImpl implements IErpCommitOrderService {

    @Resource
    private TokenService tokenService;
    @Autowired
    private EmailService emailService;
    @Resource
    private ErpCommitOrderMapper commitOrderMapper;
    @Resource
    private ErpClientClueMapper clientClueMapper;
    @Resource
    private IErpOrdersService erpOrdersService;
    @Resource
    private IErpServiceOrdersService erpServiceOrdersService;
    @Resource
    private IErpServiceOrdersInfoService erpServiceOrdersInfoService;
    @Resource
    private IErpOrderPaymentTermService erpOrderPaymentTermService;
    @Resource
    private IErpOrderPaymentTermInfoService erpOrderPaymentTermInfoService;
    @Resource
    private IErpServiceOrderRefundService erpServiceOrderRefundService;
    @Resource
    private IErpContractService erpContractService;
    @Resource
    private IErpClientService erpClientService;
    @Resource
    private IErpOrderOperatingRecordService erpOrderOperatingRecordService;
    @Resource
    private IOnlineContractService onlineContractService;
    @Resource
    private ErpCombinedActivityMapper erpCombinedActivityMapper;
    @Resource
    private ErpDiscountCouponMapper erpDiscountCouponMapper;
    @Resource
    private ErpServiceOrdersMapper erpServiceOrdersMapper;
    @Resource
    private ErpCombinedActivityProductMapper erpCombinedActivityProductMapper;
    @Resource
    private ErpOrdersMapper erpOrdersMapper;
    @Resource
    private IErpRetainageReturnDetailService erpRetainageReturnDetailService;
    @Resource
    private ErpTLOrderService erpTLOrderService;
    @Resource
    private ErpTLOrderMapper erpTLOrderMapper;
    @Resource
    private ErpOrderPaymentTermMapper erpOrderPaymentTermMapper;

    @Value("${params.xcx_user_id}")
    public Long xcxUserId;

    @Resource
    public RemoteCustomerService remoteCustomerService;

    @Resource
    private ErpClientMapper erpClientMapper;

    @Resource
    private ErpProductDetailMapper erpProductDetailMapper;

    @Autowired
    private ErpContractMapper erpContractMapper;

    @Autowired
    private OnlineContractMapper onlineContractMapper;

    @Resource
    private ErpRetainageReturnMapper erpRetainageReturnMapper;
    @Resource
    private ErpRetainageReturnDetailMapper erpRetainageReturnDetailMapper;
    @Value("${old.system.url}")
    public String url;
    @Value("${erp.order.transactionVoucher.orderCreateTime}")
    public String voucherOrderCreateTime;

    private static final String TLUrl = "https://api8.bjmantis.cn/api";

    private static final String TLhcUrl = "https://mcapi8.bjmantis.cn/hc-api";

    @Autowired
    private TlOpenApiPropertiesConfig tlOpenApiPropertiesConfig;

    @Resource
    private IErpNewApprovalService erpNewApprovalService;

    @Autowired
    private PaymentMapper paymentMapper;
    @Autowired
    private SServiceMainMapper sServiceMainMapper;
    @Autowired
    private ErpOrdersOperateRecordsMapper erpOrdersOperateRecordsMapper;


    @Autowired
    private ErpLicenseProductMapper erpLicenseProductMapper;

    @Autowired
    private ErpLicenseMapper erpLicenseMapper;
    @Autowired
    private ErpOldEnterpriseFollowMapper erpOldEnterpriseFollowMapper;
    @Resource
    private ErpProductConfigurationMapper erpProductConfigurationMapper;
    @Resource
    private ErpEnterpriseMapper erpEnterpriseMapper;
    @Resource
    private ErpProductTaxRelationMapper erpProductTaxRelationMapper;
    @Resource
    private WechatAppletBindMapper wechatAppletBindMapper;

    @Resource
    private ErpNewApprovalMapper erpNewApprovalMapper;

    @Resource
    private DingDingService dingDingService;

    @Value("${erp.qizhaoduo.send.email}")
    private String erpqzdEmail;

    @Autowired
    private RemoteUserService remoteUserService
            ;
    @Value("${erp.order.contract.createTime}")
    private String erpOrderContractCreateTime;

    @Autowired
    private IXcxActivityCouponLimitService xcxActivityCouponLimitService;

    @Autowired
    private IXcxCouponConfigService xcxCouponConfigService;

    @Autowired
    private IErpDiscountCouponService erpDiscountCouponService;

    @Autowired
    private ErpGiftRuleProductMapper erpGiftRuleProductMapper;

    @Autowired
    private ErpGiftMapper erpGiftMapper;

    @Autowired
    private ErpGiftIssueRecordMapper erpGiftIssueRecordMapper;

    @Autowired
    private ErpOrderGiftMapper erpOrderGiftMapper;

    @Autowired
    private ErpOrderGiftLogMapper erpOrderGiftLogMapper;

    @Autowired
    private ErpPromotionalActivitiesProductMapper erpPromotionalActivitiesProductMapper;
    @Autowired
    private ErpTransactionVoucherMapper erpTransactionVoucherMapper;
    @Autowired
    private ErpTransactionVoucherFollowMapper erpTransactionVoucherFollowMapper;

    @Autowired
    private IErpTransactionVoucherFollowService transactionVoucherFollowService;
    @Autowired
    private ErpTransactionVoucherFollowInfoMapper erpTransactionVoucherFollowInfoMapper;
    @Autowired
    private ErpOrderPerformanceMapper erpOrderPerformanceMapper;
    @Autowired
    private ErpPromotionalActivitiesMapper erpPromotionalActivitiesMapper;
    @Autowired
    private ErpAccountVisitMapper accountVisitMapper;
    @Autowired
    private SServiceQualificationsExtensionMapper sServiceQualificationsExtensionMapper;


/*    @Resource
    private RedissonClient redission;*/

    @Value("${erp.commitOrder.skip-review-dept}")
    private String skipReviewDept;

    /**
     * 确认订单数据获取。
     *
     * @param confirmOrderDTO 待确认订单信息。
     * @return 返回产品基本信息及相关优惠信息。
     * <AUTHOR>
     * @since 2022-03-15 17:20:14
     */
    @Override
    public ErpConfirmOrderVO confirmOrder(ErpClientForConfirmOrderDTO confirmOrderDTO) {

        // 构建返回对象。
        ErpConfirmOrderVO resObject = new ErpConfirmOrderVO();
        // 提取待校验产品ID集合，。用于查询参评基本信息。
        List<Long> productIdList = confirmOrderDTO.getProducts().stream()
                .map(ErpProductForConfirmOrderDTO::getProductId)
                .collect(Collectors.toList());

        List<Long> activityIdList = confirmOrderDTO.getProducts().stream()
                .map(ErpProductForConfirmOrderDTO::getActivityId)
                .collect(Collectors.toList());
        // 获取产品基本信息。
        ErpProductConfiguration configuration = new ErpProductConfiguration();
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        Long userId = sysUser.getUserId();
        Long deptId = sysUser.getDeptId();

        if (!userId.equals(1L)) {
            // 获取当前的用户
            //查询配置信息
            configuration.setDeptId(String.valueOf(erpProductDetailMapper.selectSecondDeptIdByDeptId(sysUser.getDeptId())));
        }

        if (CollUtil.isEmpty(productIdList)){
            throw new ServiceException(OrderExceptionConstants.CONFIRM_PRODUCT_IS_EMPTY);
        }

        configuration.setProductList(productIdList);
        //放入税控时间和产品活动价格
        List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList = setProductInfoToEn(productIdList, configuration, deptId);
        //获取组合活动基本信息
        List<ErpCombinedActivity> list = erpCombinedActivityMapper.activityForConfirmOrderVOList(activityIdList);
        //处理启照多产品单价
        productForConfirmOrderVOList = dealQiZhaoDuoProducts(productForConfirmOrderVOList, confirmOrderDTO);
        //填充产品信息到返回结果。
        resObject.setProductInfos(productForConfirmOrderVOList);
        //校验产品组合活动合理性
        verifyProductRationality(confirmOrderDTO, productForConfirmOrderVOList, list);
        // 填充产品信息。
        setProductInfoToEn(confirmOrderDTO, resObject);
        //填充返回结果
        fillResult(confirmOrderDTO, resObject);
        //填充部门产品是否需要合同
        setProductIsNoNeedProduct(1, resObject.getProductInfos(),  null);

        BigDecimal giftNeedPrice = new BigDecimal("0");
        for (int i = 0; i < productForConfirmOrderVOList.size(); i++) {
            giftNeedPrice = giftNeedPrice.add(productForConfirmOrderVOList.get(i).getTotalPrice());
        }

        //匹配赠品
        resObject.setGiftList(mateGiftByProductIds(deptId, productIdList, giftNeedPrice));
        resObject.setTransactionVoucherList(mateTransactionVoucherByProductIds(
                userId,
                productIdList,
                confirmOrderDTO.getOrderId(),
                confirmOrderDTO.getCommitOrderType() == 1 ? confirmOrderDTO.getClueId() : null,
                confirmOrderDTO.getCommitOrderType() == 2 ? confirmOrderDTO.getClientId() : null,
                1,
                1)
        );

        return resObject;

    }

    /**
     * 处理启照多产品单价
     */
    private List<ErpProductForConfirmOrderVO> dealQiZhaoDuoProducts(List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList, ErpClientForConfirmOrderDTO confirmOrderDTO) {
        //如果包含  执照产品 放入订单，则取执照产品的价格
        List<Long> collect = productForConfirmOrderVOList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        String licenseNo = null;
        if (CollectionUtils.isNotEmpty(collect) && collect.contains(3413L)) {
            if (StringUtils.isEmpty(confirmOrderDTO.getOrderId())) {
                licenseNo = confirmOrderDTO.getLicenseNo();
            } else {
                ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(Long.valueOf(confirmOrderDTO.getOrderId()));
                licenseNo = erpOrders.getLicenseNumber();
            }

            ErpLicenseDTO erpLicense = new ErpLicenseDTO();
            erpLicense.setNumber(Integer.valueOf(licenseNo));
            List<ErpLicense> erpLicenses = erpLicenseMapper.selectErpLicenseList(erpLicense);

            List<ErpLicenseProduct> erpLicenseProductBylicenseNo = erpLicenseProductMapper.getErpLicenseProductBylicenseNo(licenseNo);
            erpLicenseProductBylicenseNo.remove(null);
            for (ErpProductForConfirmOrderVO erpProductForConfirmOrderVO : productForConfirmOrderVOList) {
                Long productId = erpProductForConfirmOrderVO.getProductId();
                Integer integer = Integer.valueOf(productId.toString());
                if(CollectionUtils.isNotEmpty(erpLicenseProductBylicenseNo)){
                    List<ErpLicenseProduct> products = erpLicenseProductBylicenseNo.stream().filter(item -> (item.getProductId().equals(integer))).collect(Collectors.toList());
                    erpProductForConfirmOrderVO.setUnitPrice(CollectionUtils.isNotEmpty(products) ? products.get(0).getProductPrice() : erpProductForConfirmOrderVO.getUnitPrice());
                    erpProductForConfirmOrderVO.setLicenseNumber(CollectionUtils.isNotEmpty(products) ? Integer.valueOf(licenseNo) : null);
                }

                if(erpProductForConfirmOrderVO.getProductId().equals(3413L)){
                    erpProductForConfirmOrderVO.setLicenseNumber(Integer.valueOf(licenseNo));
                    erpProductForConfirmOrderVO.setUnitPrice(CollectionUtils.isNotEmpty(erpLicenses) ? erpLicenses.get(0).getLicensePrice() : BigDecimal.ZERO);
                }
            }
        }

        return productForConfirmOrderVOList;
    }

    /***
     * 校验订单信息的完整性
     * @param erpClientForCommitOrderDTO
     * wangyu20230224
     */
    private void checkOrderInfoComplete(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        if (ObjectUtil.isNull(erpClientForCommitOrderDTO)) {
            throw new ServiceException("订单信息为空");
        }
        if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getSource())) {
            throw new ServiceException("未知调用来源");
        }
        if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getProducts()) || ObjectUtil.isEmpty(erpClientForCommitOrderDTO.getProducts())) {
            throw new ServiceException("订单内无选中产品");
        }
        if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getPaymentTerm())) {
            throw new ServiceException("签约信息不完整");
        }
        if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getOptionType())) {
            throw new ServiceException("操作类型错误");
        }

        if (erpClientForCommitOrderDTO.getSource().equals(0)) {
            // 调用来源：CRM。
            if (Objects.isNull(erpClientForCommitOrderDTO.getCommitOrderType())) {
                throw new ServiceException("提单来源不准确");
            }
            if (1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                if (ObjectUtil.hasEmpty(
                        erpClientForCommitOrderDTO.getClientId(),       // 客户汇总标识。
                        erpClientForCommitOrderDTO.getClientType(),     // 客户类型：1企业，2个人。
                        erpClientForCommitOrderDTO.getClientCity(),     // 客户城市。
                        erpClientForCommitOrderDTO.getClueId()         // 线索标识。
                )) {
                    throw new ServiceException("客保信息不完整");
                }

                R<List<BdClueContacts>> r = remoteCustomerService.getBdClueContactByClueId(erpClientForCommitOrderDTO.getClueId());
                if (r.getCode() != 200 || r.getData().size() <= 0) {
                    throw new ServiceException("线索联系人为空！");
                }
            }
            if (2 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                if (ObjectUtil.hasEmpty(
                        erpClientForCommitOrderDTO.getClientId(),       // 客户汇总标识。
                        erpClientForCommitOrderDTO.getClientType(),     // 客户类型：1企业，2个人。
                        erpClientForCommitOrderDTO.getClientCity()     // 客户城市。
                )) {
                    throw new ServiceException("客户信息不完整");
                }
            }
        } else if (erpClientForCommitOrderDTO.getSource().equals(1)) {
            // 调用来源：小程序。
        }

        // 校验签约信息是否完整。
        if (ObjectUtil.hasEmpty(
                erpClientForCommitOrderDTO.getPaymentTerm().getUserId(),       // 签约人标识。
                erpClientForCommitOrderDTO.getPaymentTerm().getSigningDate()   // 签约时间。
        )) {
            throw new ServiceException("签约人信息不完整");
        }

        if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos())
                || erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().size() <= 0) {
            throw new ServiceException("收款信息不完整");
        }

        erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().forEach(term -> {
            if (ObjectUtil.hasEmpty(
                    term.getPayee()            // 收款人标识。
//                    term.getCollectionTime()    // 收款时间。
            )) {
                throw new ServiceException("收款人信息不完整");
            }
            if (ObjectUtil.isNull(term.getPaymentDetails())) {
                throw new ServiceException("收款详情为空");
            }
            if (term.getPaymentDetails().size() == 0) {
                throw new ServiceException("收款详情为空");
            }
            term.getPaymentDetails().forEach(detail -> {
                if (ObjectUtil.hasEmpty(
                        detail.getMoney()          // 收款金额。
//                        detail.getPaymentType()    // 收款方式。
                )) {
                    throw new ServiceException("收款详情不完整");
                }
            });
        });

        // 校验订单内产品信息必填字段是否完整。
        erpClientForCommitOrderDTO.getProducts().forEach(product -> {
            if (!ObjectUtil.hasEmpty(
                    product.getCouponId(),                // 优惠券与组合不可同时使用。
                    product.getCombinedId()               // 优惠券与组合不可同时使用。
            )) {
                throw new ServiceException("产品不可同时使用优惠券与组合");
            }

            if (ObjectUtil.hasEmpty(
                    product.getProductId(),                 // 产品标识必传。
                    product.getServiceTypeId(),             // 产品服务单类型Id。
                    product.getProductUnit(),               // 产品单位必传。
                    product.getProductCount(),              // 产品购买数量必传。
                    product.getProductPrice(),              // 产品单价必传。
                    product.getSumPrice()                   // 产品总价。产品单价 * 产品数量。
            )) {
                throw new ServiceException("订单内产品信息不完整，产品ID为：" + product.getProductId());
            }
            product.setSumPrice(ObjectUtil.isNull(product.getSumPrice()) ? new BigDecimal("0") : product.getSumPrice());
            // 维护产品的优惠申请额度。
            product.setProductPreferential(ObjectUtil.isNull(product.getProductPreferential()) ? new BigDecimal("0") : product.getProductPreferential());
            // 维护产品的渠道费。
            product.setChannelFee(ObjectUtil.isNull(product.getChannelFee()) ? new BigDecimal("0") : product.getChannelFee());
            // 维护产品的优惠金额。
            product.setCouponPrice(ObjectUtil.isNull(product.getCouponPrice()) ? new BigDecimal("0") : product.getCouponPrice());
            // 维护产品的积分数据。
            product.setPoints(ObjectUtil.isNull(product.getPoints()) ? new BigDecimal("0") : product.getPoints());
            // 维护产品应收。
            product.setTotalPrice(ObjectUtil.isNull(product.getTotalPrice()) ? new BigDecimal("0") : product.getTotalPrice());
            // 维护产品实收。
            product.setPayPrice(ObjectUtil.isNull(product.getPayPrice()) ? new BigDecimal("0") : product.getPayPrice());
            // 维护尾款金额。
            product.setLastPrice(ObjectUtil.isNull(product.getLastPrice()) ? new BigDecimal("0") : product.getLastPrice());
            // 维护退款金额。
            product.setRefundPrice(ObjectUtil.isNull(product.getRefundPrice()) ? new BigDecimal("0") : product.getRefundPrice());
            // 维护废弃状态。
            product.setIsDeprecated(ObjectUtil.isNull(product.getIsDeprecated()) ? 0 : product.getIsDeprecated());
            // 维护尾款优惠金额汇总。
            product.setRetainageDiscountPrice(ObjectUtil.isNull(product.getRetainageDiscountPrice()) ? new BigDecimal("0") : product.getRetainageDiscountPrice());
        });

        erpClientForCommitOrderDTO.setSumPrice(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getSumPrice()) && erpClientForCommitOrderDTO.getSumPrice().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getSumPrice() : new BigDecimal("0"));
        erpClientForCommitOrderDTO.setTotalPrice(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getTotalPrice()) && erpClientForCommitOrderDTO.getTotalPrice().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getTotalPrice() : new BigDecimal("0"));
        erpClientForCommitOrderDTO.setPayPrice(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getPayPrice()) && erpClientForCommitOrderDTO.getPayPrice().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getPayPrice() : new BigDecimal("0"));
        erpClientForCommitOrderDTO.setLastPrice(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getLastPrice()) && erpClientForCommitOrderDTO.getLastPrice().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getLastPrice() : new BigDecimal("0"));
        erpClientForCommitOrderDTO.setRefundPrice(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getRefundPrice()) && erpClientForCommitOrderDTO.getRefundPrice().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getRefundPrice() : new BigDecimal("0"));
        erpClientForCommitOrderDTO.setDiscountAmount(ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getDiscountAmount()) && erpClientForCommitOrderDTO.getDiscountAmount().compareTo(new BigDecimal("0")) > 0 ? erpClientForCommitOrderDTO.getDiscountAmount() : new BigDecimal("0"));

        if (erpClientForCommitOrderDTO.getOptionType() == 2) {
            // 若当前操作为修改订单。
            // 校验当前订单是否可以进行修改操作。
            erpOrdersService.isOrderToOperation(erpClientForCommitOrderDTO.getOrderId(), 4);
        } else if (erpClientForCommitOrderDTO.getOptionType() == 3) {
            // 若当前操作为编辑订单。
            // 校验当前订单是否可以进行编辑操作。
            erpOrdersService.isOrderToOperation(erpClientForCommitOrderDTO.getOrderId(), 1);
        }
    }


    /***
     * 校验订单产品相关
     * @param erpClientForCommitOrderDTO
     * wangyu20230224
     */
    private void checkProductInfo(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, List<ErpProductForConfirmOrderVO> productForDBList, Long userUserId) {

        //活动价格填充
        for (ErpProductForConfirmOrderVO vo : productForDBList) {
            //活动价格
            if (ObjectUtil.isNotEmpty(vo.getActivityStartTime())
                    && ObjectUtil.isNotEmpty(vo.getActivityEndTime())) {
                if (DateUtil.parse(vo.getActivityStartTime(), DateFormatConstants.TIME_FORMAT_BEGIN).isBefore(new Date())
                        && DateUtil.parse(vo.getActivityEndTime(), DateFormatConstants.TIME_FORMAT_BEGIN).isAfter(new Date())) {
                    if (ObjectUtil.isNotEmpty(vo.getActivityDiscountAmount())) {
                        vo.setUnitPrice(vo.getActivityDiscountAmount());
                    }
                } else if (vo.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))
                        && vo.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))) {
                    if (ObjectUtil.isNotEmpty(vo.getActivityDiscountAmount())) {
                        vo.setUnitPrice(vo.getActivityDiscountAmount());
                    }
                } else if (vo.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))
                        || vo.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))) {
                    if (ObjectUtil.isNotEmpty(vo.getActivityDiscountAmount())) {
                        vo.setUnitPrice(vo.getActivityDiscountAmount());
                    }
                }
            }
        }
        ErpOrderStatusVO orderStatusVO = new ErpOrderStatusVO();
        if (erpClientForCommitOrderDTO.getOptionType() == 2) {
            orderStatusVO = erpOrdersService.getOrderStatus(erpClientForCommitOrderDTO.getOrderId());
        }
        ErpOrderStatusVO finalOrderStatusVO = orderStatusVO;
        List<ErpProductForCommitOrderDTO> products = erpClientForCommitOrderDTO.getProducts().stream().filter(product -> ObjectUtil.isNull(product.getIsDeprecated()) || product.getIsDeprecated() != 3).collect(Collectors.toList());
        Long finalUserUserId = userUserId;

        products.forEach(product -> {
            BigDecimal productSumPrice = product.getSumPrice();                             // 前端传参产品总价。
            BigDecimal productTotalPrice = product.getTotalPrice();                         // 前端传参应收金额。
            BigDecimal productPayPrice = product.getPayPrice();                             // 前端传参实收金额。
            BigDecimal productLastPrice = product.getLastPrice();                           // 前端传参尾款金额。
            BigDecimal productDiscountPrice = product.getCouponPrice();                     // 前端传参优惠券/组合金额。
            BigDecimal productPreferential = product.getProductPreferential();              // 前端传参优惠申请。
            BigDecimal productChannelFee = product.getChannelFee();                         // 前端传参渠道费用。
            BigDecimal productRefundPrice = product.getRefundPrice();                       // 前端传参退款费用。
            BigDecimal productRetainageDiscountPrice = product.getRetainageDiscountPrice(); // 产品尾款优惠汇总。


            if (productForDBList != null && productForDBList.size() > 0) {
                ErpProductForConfirmOrderVO productForDB = productForDBList.stream().filter(e -> e.getProductId().equals(product.getProductId())).collect(Collectors.toList()).get(0);

                //serviceOrder已存在
//                boolean isProductForService = false;
                //校验当前产品内是否有不可废弃。
                if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getOrderId())) {
                    // 若当前订单不为第一次提单，则判断当前订单内产品是否可进行废弃操作，废弃后的产品不参与后续的计算逻辑。
                    ErpProductForServiceOrdersVO productForServiceOrder = commitOrderMapper.getProductForServiceOrder(erpClientForCommitOrderDTO.getOrderId(), productForDB.getProductId());

                    if (ObjectUtil.isNotNull(productForServiceOrder)) {
                        if (productForServiceOrder.getDeprecatedStatus() != 0 && !productForServiceOrder.getDeprecatedStatus().equals(product.getIsDeprecated())) {
                            throw new ServiceException("产品" + product.getProductId() + "不允许操作");
                        }

                        // 若校验通过，则将产品传参进来的废弃状态赋值给基本信息内，用于后续的逻辑判断。
                        productForDB.setIsDeprecated(ObjectUtil.isNull(product.getIsDeprecated()) ? 0 : product.getIsDeprecated());

                        // 填充尾款优惠汇总，用于后续判断。
                        productForDB.setRetainageDiscountPrice(ObjectUtil.isNotNull(productForServiceOrder) ? productForServiceOrder.getRetainageDiscountPrice() : new BigDecimal("0"));

                        // 填充产品数据库内的实付信息，用于后续判断。
                        productForDB.setPayPrice(ObjectUtil.isNotNull(productForServiceOrder) ? productForServiceOrder.getPayPrice() : new BigDecimal("0"));

                        if (product.getIsDeprecated() == 2 || product.getIsDeprecated() == 6) {
                            if (productForDB.getPayPrice().compareTo(productRefundPrice) < 0) {
                                throw new ServiceException("产品" + product.getProductId() + "退款金额大于实收金额");
                            }
                        }
                        return;
                    }
                }

                // 产品总价 = 产品应收 + 产品优惠券/组合优惠 + 手动优惠申请 + 尾款优惠汇总 - 渠道费
                if (productSumPrice.compareTo(productTotalPrice.add(productDiscountPrice).add(productPreferential).add(productRetainageDiscountPrice).subtract(productChannelFee)) != 0) {
                    throw new ServiceException("产品总价 = 产品应收 + 产品优惠券/组合优惠 + 手动优惠申请 + 尾款优惠汇总 - 渠道费");
                }

                // 产品尾款 = 产品应收 - 产品实收
                if (productLastPrice.compareTo(productTotalPrice.subtract(productPayPrice)) != 0) {
                    throw new ServiceException("产品尾款 = 产品应收 - 产品实收");
                }

                // 设置产品购买数量。
                productForDB.setCount(product.getProductCount());
                // 设置产品总价。
                productForDB.setSumPrice(productForDB.getUnitPrice().multiply(new BigDecimal(productForDB.getCount())));

                productForDB.setTotalPrice(productForDB.getSumPrice());

                List<Long> collect = products.stream().map(item -> item.getProductId()).collect(Collectors.toList());
                if (product.getProductPrice().compareTo(productForDB.getUnitPrice()) != 0 && !(collect.contains(3413L))) {
                    log.error("product::{}", JSONObject.toJSONString(product));
                    log.error("productForDB::{}", JSONObject.toJSONString(productForDB));
                    throw new ServiceException("产品基础信息错误");
                } else {
                    product.setProductName(productForDB.getProductName());
                }

                //             * 提取产品相关金额，用于校验（产品金额校验规则）
//     * 提单校验：
//     *     产品总价 = 产品单价 * 产品数量
//                *     产品优惠 = 手动优惠 + 优惠券优惠或组合优惠
//                *     产品总价 = 产品应收 + 产品优惠
//                *     产品应收 = 产品总价 - 产品优惠 + 渠道费
//                *     产品尾款 = 产品应收 - 产品实收
//                *     产品实收 = 手动输入实收金额
//                * 修改订单校验：
//     *     产品总价 = 产品单价 * 产品数量
//                *     产品优惠 = 手动优惠 + 优惠券优惠或组合优惠 + 尾款优惠汇总
//                *     产品总价 = 产品应收 + 产品优惠
//                *     产品应收 = 产品总价 - 产品优惠 + 渠道费
//                *     产品尾款 = 产品应收（产品总价 - 产品优惠 + 渠道费） - 产品实收
//                *     产品实收 = 数据库内产品实收 - 本次修改退款金额


                if (ObjectUtil.isNotEmpty(product.getCouponId())) {
                    Long couponId = product.getCouponId();
                    //查询优惠券
                    ErpDiscountCoupon coupon = erpDiscountCouponMapper.selectErpDiscountCouponById(couponId);
                    if (ObjectUtil.isEmpty(coupon) || 0 != coupon.getStatus()) {
                        throw new ServiceException("优惠券不存在/优惠券已使用");
                    }

                    Long clientId = product.getClientId();
                    //优惠券ID获取优惠券
                    // TODO
                    //校验当前优惠券的线索ID，客户ID，所属人，产品              客户ID，最低价格
                    //查询优惠券
                    //1.提单校验优惠券是否使用
                    //2.上传收款截图优惠券已使用要放开
                    //3.订单修改优惠券已经使用要放开
                    if (CouponConstants.COUPON_USED.equals(coupon.getStatus())) {
                        isCouponUse(erpClientForCommitOrderDTO, finalOrderStatusVO);
                    }
                    if (CouponConstants.COUPON_INVALID.equals(coupon.getStatus())) {
                        isCouponUse(erpClientForCommitOrderDTO, finalOrderStatusVO);
                    }
                    if (CouponConstants.NUM_TYPE_COUPON.equals(coupon.getNumType())) {
                        if (!coupon.getNumProductId().equals(product.getProductId())) {
                            throw new ServiceException("优惠券与产品不匹配");
                        }

                        if (ObjectUtil.isEmpty(coupon.getClueId())) {
                            if (!coupon.getClientId().equals(clientId)) {
                                throw new ServiceException("优惠券与客户不匹配");
                            }
                        } else {
                            if (!coupon.getClueId().equals(erpClientForCommitOrderDTO.getClueId())) {
                                throw new ServiceException("优惠券与线索不匹配");
                            }
                        }
                        if (!coupon.getBelongUserId().equals(finalUserUserId)) {
                            throw new ServiceException("优惠券与所属人不匹配");
                        }
                    }
                    if (CouponConstants.NUM_TYPE_DEDUCTION.equals(coupon.getNumType())) {
                        if (!coupon.getClientId().equals(clientId)) {
                            throw new ServiceException("折扣券与客户不匹配");
                        }
                        if (product.getProductPrice().multiply(new BigDecimal(product.getProductCount())).compareTo(coupon.getMinPrice()) == -1) {
                            throw new ServiceException("产品金额小于优惠券使用的最低金额");
                        }
                    }
                    //应收金额

                    if (product.getCouponPrice().compareTo(coupon.getDiscountAmount()) != 0) {
                        throw new ServiceException("产品优惠券优惠金额 = 库内优惠券优惠金额");
                    }

                    if (product.getSumPrice().compareTo(product.getTotalPrice().add(product.getCouponPrice())) != 0) {
                        throw new ServiceException("优惠券优惠金额计算错误");
                    }

                    if (CouponConstants.VALIDITY_PERIOD_ON.equals(coupon.getValidityPeriod())){
                        if (!LocalDateTime.now().isAfter(coupon.getActStartTime())
                                || !LocalDateTime.now().isBefore(coupon.getActEndTime())){
                            throw new ServiceException("优惠券不在使用范围内");
                        }
                    }

                    productForDB.setCouponId(couponId);
                    productForDB.setDiscountAmount(coupon.getDiscountAmount());
                    productForDB.setTotalPrice(productForDB.getSumPrice().subtract(productForDB.getDiscountAmount()));
                }

                // 尾款优惠汇总。
//                BigDecimal dbSummaryLastDiscount = ObjectUtil.isNull(productForDB.getRetainageDiscountPrice()) ?
//                        new BigDecimal("0") : productForDB.getRetainageDiscountPrice();

                // 数据库内的产品实付金额，用于比较退款金额是否正确。
//                BigDecimal dbPayPrice = ObjectUtil.isNull(productForDB.getPayPrice()) ?
//                        new BigDecimal("0") : productForDB.getPayPrice();

                // 校验组合优惠计算。
                if (ObjectUtil.isNotNull(product.getCombinedId())) {

                    List<CombunedActivityDto> list = erpCombinedActivityMapper.selectErpCombinedActivity(product.getCombinedId());

                    if (list.isEmpty()){
                        throw new ServiceException("组合活动不存在");
                    }

                    boolean match = list.stream().anyMatch(e -> e.getNumProductId().equals(product.getProductId()));

                    if (!match){
                        throw new ServiceException("组合内产品需全部选中该组合");
                    }

                    CombunedActivityDto combined = list.stream()
                            .filter(e -> e.getNumProductId().equals(product.getProductId())).collect(Collectors.toList()).get(0);

                    //产品应收 = 组合活动产品优惠价格
                    if (product.getTotalPrice().compareTo(combined.getNumActivityPrice()) != 0){
                        throw new ServiceException("产品应收 = 组合活动产品优惠价格");
                    }

//                    //产品价格 = 组合活动产品价格
//                    if (product.getProductPrice().compareTo(combined.getNumPrice()) != 0){
//                        throw new ServiceException("产品价格 = 组合活动产品价格");
//                    }
                }
            }
        });
    }
    /**
     * 提交订单。
     *
     * @param erpClientForCommitOrderDTO 待提交订单信息。
     * @return 返回提交结果。
     * <AUTHOR>
     * @since 2022-03-22 13:22:22
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> commitOrder(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, HttpServletResponse response) {
        // TODO 编辑订单时，将订单内产品全部清除再重新添加。
        // TODO 修改产品时不直接修改到数据库内，需等修改审核通过后统一修改原数据。

        Map<String, Object> resMap = new HashMap<>(2);
        ErpOrders erpOrders = new ErpOrders();

        //校验合同选择是否正确
        checkAllProductContract(erpClientForCommitOrderDTO);
        //正常状态的注册产品
        List<Long> productIdNormalListZC = new ArrayList<>();
        for (int i = 0; i < erpClientForCommitOrderDTO.getProducts().size(); i++) {
            ErpProductForCommitOrderDTO product = erpClientForCommitOrderDTO.getProducts().get(i);
            //产品类型为注册，状态为正常或者修改新增，且数据库不存在
            if (product.getServiceTypeId().intValue() == ServiceMainConstants.ZhuCeService) {
                if (ObjectUtil.isNull(product.getIsDeprecated()) || product.getIsDeprecated() == 0 || product.getIsDeprecated() == 7) {
                    productIdNormalListZC.add(product.getProductId());
                }
            }
        }
        if (CollUtil.isNotEmpty(productIdNormalListZC)){
            ErpClient client = erpClientMapper.selectErpClientById(erpClientForCommitOrderDTO.getClientId());
            //查询企业注册/记账产品数据去除当前订单数据 且订单状态不为作废和退款 且产品状态不为退费或作废
            List<ErpServiceOrders> zcOrJzList = erpServiceOrdersMapper.selectCountByEnterpriseIdWithZhuCeOrJZ(
                    client.getNumEnterpriseId(), erpClientForCommitOrderDTO.getOrderId());
            if (CollUtil.isNotEmpty(zcOrJzList)){
                throw new ServiceException("该企业已有注册或记账服务，需先作废原注册/记账产品或者更换企业提单");
            }
            //判断当前提交的产品只能存在一个注册产品
            if (productIdNormalListZC.size() > 1){
                throw new ServiceException("不能存在多个注册产品");
            }
        }
        // 校验订单基本信息是否完整。
        checkOrderInfoComplete(erpClientForCommitOrderDTO);

        //获取当前登录用户的部门Id和userId，后续使用
        Long userDeptId = -1L;
        Long userUserId = -1L;
        //调用来源CRM
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException(UserExceptionConstants.LOGIN_USER_IS_EMPTY);
        }
        SysUser sysUser = loginUser.getSysUser();
        userDeptId = sysUser.getDeptId();
        userUserId = sysUser.getUserId();
//        if (erpClientForCommitOrderDTO.getSource().equals(0)) {
//        } else {
//            //调用来源小程序
//
//        }

        // 获取产品基本信息条件
        ErpProductConfiguration configuration = new ErpProductConfiguration();
        configuration.setFilterIsOldData(1);
        // 获取待提交产品标识，用于获取产品基本信息。
        List<Long> productIdList = erpClientForCommitOrderDTO.getProducts().stream().map(ErpProductForCommitOrderDTO::getProductId).collect(Collectors.toList());
        if (1L != userUserId) {
            configuration.setDeptId(String.valueOf(erpProductDetailMapper.selectSecondDeptIdByDeptId(sysUser.getDeptId())));
        }
        configuration.setProductList(productIdList);

        List<ErpProductForConfirmOrderVO> productForDBList = commitOrderMapper.getProductInfoByIds(configuration);
        //校验订单产品相关
        checkProductInfo(erpClientForCommitOrderDTO, productForDBList, userUserId);

        //校验启照多提单
        checkQiZhaoDuo(erpClientForCommitOrderDTO);

        //订单金额校验
        BigDecimal orderTotalPrice = orderCalibration(erpClientForCommitOrderDTO);

        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();

        //根据手机号和企业或个人ID查询erp_client是否存在此条数据，不存在则新建一条关联至改订单,存在则将此客户关联至订单
        ErpClient addErpClient = relatedCustomers(erpClientForCommitOrderDTO);

        // 添加订单2信息。
        // 构建订单2对象。
        Date nowDate = new Date();

        //创建订单相关信息入库
        Long orderId = createOrderRelatedInformation(erpClientForCommitOrderDTO, erpOrders, userUserId,
                productForDBList, orderTotalPrice, erpOrderOperatingRecord, addErpClient, nowDate);

        erpServiceOrdersService.checkGiveWithServiceType(orderId);

        //将订单修改日志里所有状态未待审核的，置为无效
        erpOrdersOperateRecordsMapper.updateStatusByOrderId(2, orderId);

        ErpOrdersOperateRecords erpOrdersOperateRecords = new ErpOrdersOperateRecords();
        if (erpClientForCommitOrderDTO.getOptionType() == 2) {
            erpOrdersOperateRecords.setOperateType(2);
        } else {
            erpOrdersOperateRecords.setOperateType(1);
        }
        erpOrdersOperateRecords.setOrderId(orderId);
        erpOrdersOperateRecords.setOrderContent(JSONObject.toJSONString(erpClientForCommitOrderDTO));
        erpOrdersOperateRecords.setCreatedTime(new Date());
        erpOrdersOperateRecords.setCreatedUser(userUserId);
        erpOrdersOperateRecords.setStatus(2);
        erpOrdersOperateRecordsMapper.insertErpOrdersOperateRecords(erpOrdersOperateRecords);

        if (erpClientForCommitOrderDTO.getSource() == 1) {
            // 若当前请求来自小程序，则返回所需的产品信息.
            resMap.put("orderId", orderId);
            resMap.put("serviceOrders", erpOrdersService.getProductsForOrderDetail(orderId));
            return resMap;
        } else {
            // 修改指定客户的线索标识为当前线索。
            if (Objects.nonNull(erpClientForCommitOrderDTO.getCommitOrderType()) && 1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                ErpClient erpClient = new ErpClient();
                erpClient.setId(addErpClient.getId());
                erpClient.setNumClueId(erpClientForCommitOrderDTO.getClueId());
                erpClientService.updateErpClient(erpClient);
            }
        }

        //生成电子合同
        productionElectronicContract(erpClientForCommitOrderDTO, erpOrders, userUserId, nowDate, orderId);

        //注销服务处理
        logoutServiceHandle(erpClientForCommitOrderDTO, userUserId, orderId);

        //启超多提单完成更新状态
        if (StringUtils.isNotEmpty(erpClientForCommitOrderDTO.getLicenseNo())) {
            ErpLicense erpLicense = new ErpLicense();
            erpLicense.setStatus(ErpLicenseConstants.LICENSE_STATUS_BILL);
            erpLicense.setNumber(Integer.valueOf(erpClientForCommitOrderDTO.getLicenseNo()));
            erpLicense.setSaleTime(new Date());
            int status = erpLicenseMapper.updateErpLicenseByNumber(erpLicense);
            if (status == 0) {
                throw new ServiceException("更新执照状态失败");
            }
        }
        //更新优惠券状态
        updateCoupon(orderId);

        //赠品扣减库存
        if (1 == erpClientForCommitOrderDTO.getOptionType()) {
            addErpOrderGift(erpClientForCommitOrderDTO, orderId);
        } else if (3 == (erpClientForCommitOrderDTO.getOptionType()) && Objects.isNull(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot())) {
            ErpOrderGiftDetailVO erpOrderGiftVO = erpClientForCommitOrderDTO.getErpOrderGiftVO();
            cancelErpOrderGift(erpOrderGiftVO, orderId);
        } else if (2 == erpClientForCommitOrderDTO.getOptionType() && Objects.nonNull(erpClientForCommitOrderDTO.getIsCancelErpOrderGift()) && 1 == erpClientForCommitOrderDTO.getIsCancelErpOrderGift()) {
            ErpOrders erpOrdersUpdate = new ErpOrders();
            erpOrdersUpdate.setId(orderId);
            erpOrdersUpdate.setIsCancelOrderGift(1);
            erpOrdersMapper.updateErpOrders(erpOrdersUpdate);
        }

        int newApprovalId = addErpNewApproval(erpClientForCommitOrderDTO, erpOrders);
        List<String> deptList = Arrays.asList(skipReviewDept.split(","));
        if (newApprovalId > 1 && deptList.contains(sysUser.getDept().getDeptId().toString())) {
            ApprovalPassDTO approvalPassDTO = new ApprovalPassDTO();
            approvalPassDTO.setNewApprovalId(Long.parseLong(newApprovalId+""));
            approvalPassDTO.setOrderId(orderId);
            approvalPassDTO.setExamineOperation(1);
            erpNewApprovalService.approvalPassOrReject(approvalPassDTO);

            emailService.sendEmail("<EMAIL>", "提单提醒", "您好:\r\n"
                    + "      订单: " + erpOrders.getVcOrderNumber() + " 提单成功\r\n"
                    + "      操作人：" + sysUser.getNickName() + "\r\n"
                    + "      会计提单已跳过经理审核!");

            //发送钉钉消息
            String dingContent = "### 提单提醒 \n * " +  "冯雪倩，您好： \n "
                    + " * 订单: " + erpOrders.getVcOrderNumber() + " 提单成功\n"
                    + " * 操作人：" + sysUser.getNickName() + "\n"
                    + " * 会计提单已跳过经理审核!";
            DingSendDTO dingSendDTO = new DingSendDTO(erpqzdEmail, "提单提醒", dingContent);
            dingDingService.sendDingMessage(dingSendDTO);
        }
        return resMap;
    }

    /**
     * 赠品扣减库存
     * @param erpClientForCommitOrderDTO
     * @param orderId
     */
    private void addErpOrderGift(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, Long orderId) {
        if (Objects.nonNull(erpClientForCommitOrderDTO.getErpOrderGiftVO())) {

            ErpOrderGiftDetailVO erpOrderGiftVO = erpClientForCommitOrderDTO.getErpOrderGiftVO();

            String lockKey = "gift001";
            //RLock lock = redission.getLock(lockKey);
            //扣减库存，生成订单赠品记录

            ErpOrderGift erpOrderGift = new ErpOrderGift();
            erpOrderGift.setOrderId(orderId);
            erpOrderGift.setConsignee(erpOrderGiftVO.getConsignee());
            erpOrderGift.setGiftIssueRecordId(erpOrderGiftVO.getGiftIssueRecordId());
            erpOrderGift.setAddress(erpOrderGiftVO.getAddress());
            erpOrderGift.setPhone(erpOrderGiftVO.getPhone());
            erpOrderGift.setCreateUser(tokenService.getLoginUser().getUserid());

            int i = erpOrderGiftMapper.insertErpOrderGift(erpOrderGift);
            if (i <= 0) {
                throw new ServiceException("生成订单赠品记录失败。");
            }
            /*try {
                lock.lock();*/
            ErpOrderGiftIssueDetailVO detailVOByRecordId = erpGiftIssueRecordMapper.getErpOrderGiftDetailVOByRecordId(erpOrderGiftVO.getGiftIssueRecordId());
            if (detailVOByRecordId.getSurplusAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("所选赠品已无库存，请更换赠品。");
            }
            int result = erpGiftIssueRecordMapper.reduceErpGiftIssueRecord(erpOrderGiftVO.getGiftIssueRecordId());
            if (result <= 0) {
                throw new ServiceException("库存扣减失败，请稍后重试。");
            }

           /* } catch (Exception e) {
                log.error("库存扣减异常，异常信息为：", e);
            } finally {
                lock.unlock();
            }*/
            ErpOrderGiftLog erpOrderGiftLog = new ErpOrderGiftLog();
            erpOrderGiftLog.setGiftIssueRecordId(erpOrderGiftVO.getGiftIssueRecordId());
            erpOrderGiftLog.setOrderId(orderId);
            erpOrderGiftLog.setCreateUser(tokenService.getLoginUser().getUserid());
            erpOrderGiftLogMapper.insertErpOrderGiftLog(erpOrderGiftLog);
        }
    }

    private void cancelErpOrderGift(ErpOrderGiftDetailVO erpOrderGiftVO, Long orderId) {
        if (Objects.nonNull(erpOrderGiftVO)) {
            ErpOrderGift orderGift = erpOrderGiftMapper.getErpOrderGiftByOrderId(orderId);
            if (Objects.nonNull(orderGift)) {
                ErpGiftIssueRecord erpGiftIssueRecord = erpGiftIssueRecordMapper.selectErpGiftIssueRecordById(orderGift.getGiftIssueRecordId());
                if (!erpOrderGiftVO.getGiftId().equals(erpGiftIssueRecord.getGiftId())) {
                    if (Arrays.asList(1, 2).contains(orderGift.getGiftStatus())) {
                        //释放旧库存
                        int result = erpGiftIssueRecordMapper.cancelErpOrderGift(orderGift.getGiftIssueRecordId());
                        if (result <= 0) {
                            throw new ServiceException("库存释放失败，请稍后重试。");
                        }
                    }
                    reduceNewGiftRecord(erpOrderGiftVO, orderId);
                } else {
                    if (4 == orderGift.getGiftStatus()) {
                        reduceNewGiftRecord(erpOrderGiftVO, orderId);
                    }
                }
            } else {
                reduceNewGiftRecord(erpOrderGiftVO, orderId);
            }
        } else {
            ErpOrderGift orderGift = erpOrderGiftMapper.getErpOrderGiftByOrderId(orderId);
            if (Objects.nonNull(orderGift)) {
                ErpOrderGiftIssueDetailVO voByRecordId = erpGiftIssueRecordMapper.getErpOrderGiftDetailVOByRecordId(orderGift.getGiftIssueRecordId());
                //更新状态
                int i = erpOrderGiftMapper.updateGiftStatusByOrderId(orderId, 4);
                if (i <= 0) {
                    throw new ServiceException("状态更新失败，请稍后重试。");
                }
                //释放库存
                int result = erpGiftIssueRecordMapper.cancelErpOrderGift(orderGift.getGiftIssueRecordId());
                if (result <= 0) {
                    throw new ServiceException("库存释放失败，请稍后重试。");
                }
            }

        }
    }

    /**
     * 扣减新库存 绑定新赠品
     * @param erpOrderGiftVO
     * @param orderId
     */
    private void reduceNewGiftRecord(ErpOrderGiftDetailVO erpOrderGiftVO, Long orderId) {
        //扣减新库存
        int record = erpGiftIssueRecordMapper.reduceErpGiftIssueRecord(erpOrderGiftVO.getGiftIssueRecordId());
        if (record <= 0) {
            throw new ServiceException("扣减新库存失败");
        }
        //绑定新赠品
        ErpOrderGift erpOrderGift = new ErpOrderGift();
        erpOrderGift.setOrderId(orderId);
        erpOrderGift.setGiftIssueRecordId(erpOrderGiftVO.getGiftIssueRecordId());
        erpOrderGift.setGiftStatus(1);
        erpOrderGift.setPhone(erpOrderGiftVO.getPhone());
        erpOrderGift.setConsignee(erpOrderGiftVO.getConsignee());
        erpOrderGift.setAddress(erpOrderGiftVO.getAddress());
        erpOrderGift.setUpdateUser(tokenService.getLoginUser().getUserid());
        int i = erpOrderGiftMapper.updateErpOrderGiftByOrderId(erpOrderGift);
        if (i <= 0) {
            throw new ServiceException("绑定新赠品失败");
        }
    }

    public void checkAllProductContract(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {

        ErpOrders erpOrders = erpOrdersMapper.selectErpOrdersById(erpClientForCommitOrderDTO.getOrderId());
        Long deptId = tokenService.getLoginUser().getSysUser().getDeptId();
        int needContract = 0;
        for (ErpProductForCommitOrderDTO product : erpClientForCommitOrderDTO.getProducts()) {
            if (Arrays.asList(3,4).contains(product.getIsDeprecated())) {
                continue;
            }
            ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(product.getProductId());
            String noContractNeededDeptId = erpProductDetail.getNoContractNeededDeptId();
            Long isNeedContract = erpProductDetail.getNumIsNeedContract();
            List<Long> list = new ArrayList<>();
            if (com.nnb.common.core.utils.StringUtils.isNotEmpty(noContractNeededDeptId)) {
                for (String s : noContractNeededDeptId.split(",")) {
                    list.add(Long.valueOf(s));
                }
            }

            // 产品需要合同
            if ((Objects.nonNull(isNeedContract) && (1L == isNeedContract) || Objects.isNull(isNeedContract)) && (!list.contains(deptId)) || (Objects.nonNull(product.getNoContractSelectedStatus()) && 1 == product.getNoContractSelectedStatus())) {
                ++needContract;
            }
        }

        Date date = null;
        try {
            date = DateUtils.parseDate(erpOrderContractCreateTime, "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if ((erpClientForCommitOrderDTO.getOptionType() == 1 && needContract > 0 && erpClientForCommitOrderDTO.getIsContract() == 3)) {
            throw new ServiceException("订单中存在产品需要合同，请选择电子合同或纸质合同");
        } else if ((Arrays.asList(2, 3).contains(erpClientForCommitOrderDTO.getOptionType()) && (Objects.nonNull(erpOrders.getIsElectronicContract()) && Arrays.asList(1, 2).contains(erpOrders.getIsElectronicContract()))) ||
                (Arrays.asList(2, 3).contains(erpClientForCommitOrderDTO.getOptionType()) && (erpOrders.getDatSigningDatecreatedTime().compareTo(date) > 0) && 3 == erpOrders.getIsElectronicContract())) {
            if(needContract > 0 && erpClientForCommitOrderDTO.getIsContract() == 3){
                throw new ServiceException("订单中存在产品需要合同，请选择电子合同或纸质合同");
            }
        }
    }

    /**
     * 填充不需要产品部门产品
     * @param type 1：放入订单 2：订单详情
     * @param productInfos
     * @param productsForOrderDetail
     */
    public void setProductIsNoNeedProduct(Integer type, List<ErpProductForConfirmOrderVO> productInfos, List<ErpProductForOrderDetailVO> productsForOrderDetail) {
        Long deptId = tokenService.getLoginUser().getSysUser().getDeptId();
        if (1 == type) {
            productInfos.forEach(item -> {
                ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(item.getProductId());
                String noContractNeededDeptId = erpProductDetail.getNoContractNeededDeptId();
                Long isNeedContract = erpProductDetail.getNumIsNeedContract();
                List<Long> list = new ArrayList<>();
                if (com.nnb.common.core.utils.StringUtils.isNotEmpty(noContractNeededDeptId)) {
                    for (String s : noContractNeededDeptId.split(",")) {
                        list.add(Long.valueOf(s));
                    }
                }

                // 产品需要合同
                if ((Objects.nonNull(isNeedContract) && (1L == isNeedContract) || Objects.isNull(isNeedContract)) && (list.contains(deptId))) {
                    item.setIsNoNeedContractProduct(1);
                }
            });
        } else if (2 == type) {
            productsForOrderDetail.forEach(item -> {
                ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(item.getProductId());
                String noContractNeededDeptId = erpProductDetail.getNoContractNeededDeptId();
                Long isNeedContract = erpProductDetail.getNumIsNeedContract();
                List<Long> list = new ArrayList<>();
                if (com.nnb.common.core.utils.StringUtils.isNotEmpty(noContractNeededDeptId)) {
                    for (String s : noContractNeededDeptId.split(",")) {
                        list.add(Long.valueOf(s));
                    }
                }

                // 产品需要合同
                if ((Objects.nonNull(isNeedContract) && (1L == isNeedContract) || Objects.isNull(isNeedContract)) && (list.contains(deptId))) {
                    item.setIsNoNeedContractProduct(1);
                }
            });
        }
    }

    @Override
    public void reduceGiftRecordAmount() {
        /*String lockKey = "product001";
        RLock lock = redission.getLock(lockKey);
        try {
            lock.lock();
            int result = erpGiftIssueRecordMapper.reduceErpGiftIssueRecord(21L);
        } catch (Exception e) {
        } finally {
            lock.unlock();
        }*/
    }

    /**
     * 不重签时不保存产品打开关闭状态
     * @param erpClientForCommitOrderDTO
     */
    private void checkOpenProductContract(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        if (2 == erpClientForCommitOrderDTO.getOptionType() || 3 == erpClientForCommitOrderDTO.getOptionType()) {
            erpClientForCommitOrderDTO.getProducts().forEach(item -> {
                ErpServiceOrders erpServiceOrders = erpServiceOrdersMapper.selectErpServiceOrdersById(item.getServiceOrderId());
                if (Objects.nonNull(erpServiceOrders) && Objects.nonNull(erpServiceOrders.getNoContractSelectedStatus()) && 1 == erpServiceOrders.getNoContractSelectedStatus()
                        && Objects.nonNull(item.getNoContractSelectedStatus()) && 0 == item.getNoContractSelectedStatus() && StringUtils.isNotEmpty(erpClientForCommitOrderDTO.getIsReturn()) && "0".equals(erpClientForCommitOrderDTO.getIsReturn())) {
                    item.setNoContractSelectedStatus(null);
                }
            });
        }
    }


    /**
     * 创建对应的审批流
     * @param erpClientForCommitOrderDTO
     * @return
     */
    private int addErpNewApproval(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders) {
        ErpNewApprovalDTO erpNewApprovalDTO = new ErpNewApprovalDTO();
        erpNewApprovalDTO.setOtherId(erpOrders.getId());
        Long numUserId = null;
        ErpOrders orders = erpOrdersService.selectErpOrdersById(erpOrders.getId());
        switch (erpClientForCommitOrderDTO.getOptionType()) {
            case 1:
                erpNewApprovalDTO.setType(ApprovalTypeEnum.COMMIT_ORDER.getType());
                numUserId = erpOrders.getNumUserId();
                break;
            case 2:
                erpNewApprovalDTO.setType(ApprovalTypeEnum.MODIFY_ORDER.getType());
                numUserId = orders.getNumUserId();
                break;
            case 3:
                erpNewApprovalDTO.setType(ApprovalTypeEnum.EDIT_ORDR.getType());
                numUserId = orders.getNumUserId();
                break;
        }
        //如果为上传收款截图时调用则不再生成审批流
        if (Objects.nonNull(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot()) && 1 == erpClientForCommitOrderDTO.getEditOrderUploadScreenshot()) {
            return 1;
        }
        return erpNewApprovalService.addErpNewApproval(erpNewApprovalDTO, numUserId);
    }

    /**
     * 填充返回结果
     * @param confirmOrderDTO
     * @param resObject
     */
    private void fillResult(ErpClientForConfirmOrderDTO confirmOrderDTO, ErpConfirmOrderVO resObject) {
        //此判断的依据：每次放入要么是产品，要么是组合活动
        if (confirmOrderDTO.getProducts().get(0).getTab() == 1) {
            resObject.setSumPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setTotalPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setLastPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getLastPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setDiscountAmount(resObject.getSumPrice().subtract(resObject.getTotalPrice()));
        } else {
            // 填充订单信息。
            resObject.setSumPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getSumPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setTotalPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setDiscountAmount(resObject.getSumPrice().subtract(resObject.getTotalPrice()));
            resObject.setLastPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getLastPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            resObject.setRefundPrice(resObject.getProductInfos().stream().map(ErpProductForConfirmOrderVO::getRefundPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        // 返回封装结果。list != null && list.size() > 0
        if (CollUtil.isNotEmpty(resObject.getProductInfos())) {
            boolean b = resObject.getProductInfos().stream().anyMatch(erpProductForConfirmOrderVO -> erpProductForConfirmOrderVO.getNumIsNeedContract() == 1);
            resObject.setNumIsNeedContract(b);
        }
        confirmOrderDTO.getProducts().forEach(products -> {
            if (products.getActivityId() != null) {
                for (ErpProductForConfirmOrderVO productInfo : resObject.getProductInfos()) {
                    productInfo.setIsActivity(1);
                }
            } else {
                for (ErpProductForConfirmOrderVO productInfo : resObject.getProductInfos()) {
                    productInfo.setIsActivity(2);
                }
            }
        });
    }

    /**
     * 填充产品信息
     * @param confirmOrderDTO
     * @param resObject
     */
    private void setProductInfoToEn(ErpClientForConfirmOrderDTO confirmOrderDTO, ErpConfirmOrderVO resObject) {
        for (ErpProductForConfirmOrderVO productInfo : resObject.getProductInfos()) {

            //获取前端传参的当前产品相关信息
            List<ErpProductForConfirmOrderDTO> productInfos = confirmOrderDTO.getProducts().stream()
                    .filter(en -> en.getProductId().equals(productInfo.getProductId()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(productInfos)) {
                throw new ServiceException(OrderExceptionConstants.UNMATCHED_PRODUCTS);
            }
            ErpProductForConfirmOrderDTO product = productInfos.get(0);

            //税控产品填充税控开始时间
            if (!ObjectUtil.isNull(productInfo.getServiceTypeId()) && productInfo.getServiceTypeId().intValue() == ServiceMainConstants.ShuiKongService) {
                Date acStart = null;
                //获取当前客户税控服务
                ErpClient erpClient = erpClientMapper.selectErpClientById(product.getClientId());
                if (ObjectUtil.isNotNull(erpClient)) {
                    ServiceByEnterpriseDto serviceByEnterpriseDtoSk = ServiceByEnterpriseDto.builder()
                            .serviceType(Long.parseLong(ServiceMainConstants.ShuiKongService+""))
                            .numEnterpriseId(erpClient.getNumEnterpriseId()).build();
                    List<SServiceVo> listSK = sServiceMainMapper.selectSServiceMainDZList(serviceByEnterpriseDtoSk);

                    if (!ObjectUtil.isEmpty(listSK) && listSK.size() > 0) {
                        for (int i = 0; i < listSK.size(); i++) {
                            if (!ObjectUtil.isEmpty(listSK.get(i).getAcEnd())) {
                                if (ObjectUtil.isNotEmpty(acStart) && acStart.before(listSK.get(i).getAcEnd())) {
                                    acStart = listSK.get(i).getAcEnd();
                                }
                            }
                            if (ObjectUtil.isEmpty(acStart)) {
                                acStart = listSK.get(i).getAcEnd();
                            }
                        }
                        if (ObjectUtil.isNotEmpty(acStart)) {
                            acStart = com.nnb.erp.util.DateUtil.plusDaysDate(acStart, 1);
                        }
                    }
                }
                if (ObjectUtil.isNull(acStart)) {
                    acStart = com.nnb.erp.util.DateUtil.getMonthsFirstDay(new Date(), 1);
                }
                productInfo.setAcStart(DateUtils.parseDateToStr(DateUtils.YYYY_MM, acStart));
            }

            // 填充弃用信息。
            productInfo.setIsDeprecated(ObjectUtil.isNull(product.getIsDeprecated()) ? 0 : product.getIsDeprecated());

            // 填充优惠金额。
            if (product.getTab() == 1) {
                //组合活动
                ErpCombinedActivityProduct activityProduct = new ErpCombinedActivityProduct();
                activityProduct.setNumCombinedActivityId(product.getActivityId());
                activityProduct.setNumProductId(product.getProductId());
                List<ErpCombinedActivityProduct> products = erpCombinedActivityProductMapper.selectErpCombinedActivityProductList(activityProduct);


                productInfo.setDiscountAmount(new BigDecimal("0"));
                productInfo.setSumPrice(products.get(0).getAllPrice());
                productInfo.setCount(Integer.parseInt(products.get(0).getNumProductCount().toString()));
                productInfo.setCouponPrice(products.get(0).getAllPrice().subtract(products.get(0).getNumActivityPrice()));
                productInfo.setCombinedId(products.get(0).getNumCombinedActivityId());
            }

            // 填充产品尾款优惠汇总。
            productInfo.setRetainageDiscountPrice(
                    ObjectUtil.isNull(productInfo.getRetainageDiscountPrice()) ?
                            new BigDecimal("0") : productInfo.getRetainageDiscountPrice());

            // 填充产品实收金额。
            productInfo.setPayPrice(ObjectUtil.isNull(product.getPayPrice()) ? new BigDecimal("0") : product.getPayPrice());

            // 填充产品渠道费。
            productInfo.setChannelFee(ObjectUtil.isNull(product.getChannelFee()) ? new BigDecimal("0") : product.getChannelFee());

            // 填充产品手动优惠申请。
            productInfo.setProductPreferential(ObjectUtil.isNull(product.getProductPreferential()) ? new BigDecimal("0") : product.getProductPreferential());

            if (product.getTab() == 1) {
                productInfo.setTotalPrice(
                        productInfo.getSumPrice()
                                .add(ObjectUtil.isEmpty(productInfo.getChannelFee()) ? new BigDecimal("0") : productInfo.getChannelFee())
                                .subtract(ObjectUtil.isEmpty(productInfo.getRetainageDiscountPrice()) ? new BigDecimal("0") : productInfo.getRetainageDiscountPrice())
                                .subtract(ObjectUtil.isEmpty(productInfo.getProductPreferential()) ? new BigDecimal("0") : productInfo.getProductPreferential())
                                .subtract(ObjectUtil.isEmpty(productInfo.getCouponPrice()) ? new BigDecimal("0") : productInfo.getCouponPrice())
                );
            } else {
                productInfo.setTotalPrice(
                        productInfo.getSumPrice()
                                .add(ObjectUtil.isEmpty(productInfo.getChannelFee()) ? new BigDecimal("0") : productInfo.getChannelFee())
                                .subtract(ObjectUtil.isEmpty(productInfo.getRetainageDiscountPrice()) ? new BigDecimal("0") : productInfo.getRetainageDiscountPrice())
                                .subtract(ObjectUtil.isEmpty(productInfo.getProductPreferential()) ? new BigDecimal("0") : productInfo.getProductPreferential()));
            }
            // 计算产品尾款。
            productInfo.setLastPrice(productInfo.getTotalPrice().subtract(productInfo.getPayPrice()));

            // 填充订单退款状态。
            productInfo.setRefundStatus(ObjectUtil.isNull(product.getRefundStatus()) ? 0 : product.getRefundStatus());

            // 填充订单退款金额。
            productInfo.setRefundPrice(ObjectUtil.isNull(product.getRefundPrice()) ? new BigDecimal("0") : product.getRefundPrice());
        }
    }

    /**
     * 校验产品组合活动合理性
     *
     * @param confirmOrderDTO
     * @param productForConfirmOrderVOList
     * @param list
     */
    private void verifyProductRationality(ErpClientForConfirmOrderDTO confirmOrderDTO,
                                          List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList,
                                          List<ErpCombinedActivity> list) {
        for (ErpProductForConfirmOrderDTO products : confirmOrderDTO.getProducts()) {
            if (ObjectUtil.isEmpty(products.getTab())) {
                products.setTab(0);
                if (ObjectUtil.isNotEmpty(products.getActivityId())) {
                    products.setTab(1);
                }
            }
            if (products.getTab() == 1) {
                // 校验产品合理性。
                productForConfirmOrderVOList.forEach(product -> {
                    if (product.getIsUp() == 0) {
                        throw new ServiceException("您所选组合活动包含已下架产品，请重新选择", 501);
                    }
                });
                boolean fb = list.stream().anyMatch(activity -> !activity.getNumAreaId().equals(confirmOrderDTO.getClientCity()));
                if (fb) {
                    throw new ServiceException("您所选组合活动城市/区域与企业城市不一致，请重新选择！", 501);
                }
            } else {
                // 校验产品合理性。
                checkProductRationality(productForConfirmOrderVOList, String.valueOf(confirmOrderDTO.getClientCity()));
                Long clientId = null;
                if (confirmOrderDTO.getProducts().get(0).getClientId() != null) {
                    clientId = confirmOrderDTO.getProducts().get(0).getClientId();
                } else {
                    clientId = Long.valueOf(confirmOrderDTO.getClientId());
                }
                // 填充优惠券信息到产品信息。
                couponInfoFill(productForConfirmOrderVOList, confirmOrderDTO.getProducts(),
                        confirmOrderDTO.getSource(), confirmOrderDTO.getClueId(),
                        clientId, confirmOrderDTO.getCommitOrderType(), 1L, confirmOrderDTO.getPhone());
            }
        }
    }

    /**
     * 放入税控时间和产品活动价格
     *
     * @param productIdList
     * @param configuration
     * @param deptId
     * @return
     */
    private List<ErpProductForConfirmOrderVO> setProductInfoToEn(List<Long> productIdList, ErpProductConfiguration configuration, Long deptId) {
        List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(productIdList)) {
            productForConfirmOrderVOList = commitOrderMapper.getProductInfoByIds(configuration);
            for (ErpProductForConfirmOrderVO erpProductForConfirmOrderVO : productForConfirmOrderVOList) {
                //税控的放入税控开始时间
                //若产品在活动期间活动价格
                if (ObjectUtil.isNotEmpty(erpProductForConfirmOrderVO.getActivityStartTime())
                        && ObjectUtil.isNotEmpty(erpProductForConfirmOrderVO.getActivityEndTime())) {
                    if (DateUtil.parse(erpProductForConfirmOrderVO.getActivityStartTime(), DateFormatConstants.TIME_FORMAT_BEGIN).isBefore(new Date())
                            && DateUtil.parse(erpProductForConfirmOrderVO.getActivityEndTime(), DateFormatConstants.TIME_FORMAT_BEGIN).isAfter(new Date())) {
                        if (ObjectUtil.isNotEmpty(erpProductForConfirmOrderVO.getActivityDiscountAmount())) {
                            erpProductForConfirmOrderVO.setUnitPrice(erpProductForConfirmOrderVO.getActivityDiscountAmount());
                        }
                    } else if (erpProductForConfirmOrderVO.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))
                            && erpProductForConfirmOrderVO.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))) {
                        if (ObjectUtil.isNotEmpty(erpProductForConfirmOrderVO.getActivityDiscountAmount())) {
                            erpProductForConfirmOrderVO.setUnitPrice(erpProductForConfirmOrderVO.getActivityDiscountAmount());
                        }
                    } else if (erpProductForConfirmOrderVO.getActivityStartTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))
                            || erpProductForConfirmOrderVO.getActivityEndTime().equals(DateUtil.format(LocalDateTime.now(), DateFormatConstants.TIME_FORMAT_BEGIN))) {
                        if (ObjectUtil.isNotEmpty(erpProductForConfirmOrderVO.getActivityDiscountAmount())) {
                            erpProductForConfirmOrderVO.setUnitPrice(erpProductForConfirmOrderVO.getActivityDiscountAmount());
                        }
                    }
                }
            }
        } else {
            throw new ServiceException("产品为空");
        }
        return productForConfirmOrderVOList;
    }

    /**
     * 校验选择的执照产品是否正确
     *
     * @param linceseProductVos
     * @param products
     * @return
     */
    private Boolean checkLincenseProducts(List<LinceseProductVo> linceseProductVos, List<ErpProductForCommitOrderDTO> products, ErpLicense erpLicense) {
        Boolean flag = true;

        //根据产品numNameId分组获取产品
        Map<Long, List<ErpProductForCommitOrderDTO>> map = products.stream().collect(Collectors.groupingBy((a) -> a.getNumNameId()));
        //新系统提单所选产品应收价格
        List<BigDecimal> collect = products.stream().map(ErpProductForCommitOrderDTO::getTotalPrice).collect(Collectors.toList());

        //老系统产品ID不为空的产品ID
        List<Long> idList = linceseProductVos.stream().filter(val -> Objects.nonNull(val.getProductId())).map(LinceseProductVo::getProductId).collect(Collectors.toList());

        //提单所选产品中老系统中产品ID为空的数据
        List<ErpProductForCommitOrderDTO> diff = new ArrayList<>();
        Map<Long, Long> productMap = new HashMap<>(products.size());
        for (Long aLong : idList) {
            productMap.put(aLong, aLong);
        }
        for (ErpProductForCommitOrderDTO pro : products) {
            if (productMap.get(pro.getProductId()) == null) {
                diff.add(pro);
            }

        }

        List<BigDecimal> newIdEmptyList = null;
        newIdEmptyList = diff.stream().map(ErpProductForCommitOrderDTO::getTotalPrice).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            newIdEmptyList = products.stream().map(ErpProductForCommitOrderDTO::getTotalPrice).collect(Collectors.toList());
        }
        //格式化数据
        if (CollectionUtils.isNotEmpty(newIdEmptyList)) {
            List<BigDecimal> decimalList = new ArrayList<>();
            for (BigDecimal bigDecimal : newIdEmptyList) {
                BigDecimal decimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                decimalList.add(decimal);
            }
            newIdEmptyList.clear();
            newIdEmptyList.addAll(decimalList);

        }

        for (LinceseProductVo linceseProductVo : linceseProductVos) {
            if (Objects.nonNull(linceseProductVo.getProductId())) {
                ErpProductDetail erpProductDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(linceseProductVo.getProductId());
                if (Objects.isNull(erpProductDetail)) {
                    throw new ServiceException("老系统产品不存在！");
                }
                /*Long numNameId = erpProductDetail.getNumNameId();
                if (Objects.isNull(numNameId)) {
                    throw new ServiceException("产品的三级分类不存在！");
                }
                List<ErpProductForCommitOrderDTO> erpProductForCommitOrderDTOS = map.get(erpProductDetail.getNumNameId());
                if (CollectionUtils.isEmpty(erpProductForCommitOrderDTOS)) {
                    throw new ServiceException("所选产品分类不匹配！");
                }
                List<BigDecimal> collect = erpProductForCommitOrderDTOS.stream().map(ErpProductForCommitOrderDTO::getTotalPrice).collect(Collectors.toList());*/

                //格式化数据
                if (CollectionUtils.isNotEmpty(collect)) {
                    List<BigDecimal> decimalList = new ArrayList<>();
                    for (BigDecimal bigDecimal : collect) {
                        BigDecimal decimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                        decimalList.add(decimal);
                    }
                    if (!decimalList.contains(linceseProductVo.getProductPrice())) {
                        throw new ServiceException("所选产品价格不匹配！");
                    }
                }

            } else {
                BigDecimal productPrice = linceseProductVo.getProductPrice();
                //校验没有产品ID的产品在提单所选的产品里是否包含该价格
                if (!newIdEmptyList.contains(productPrice)) {
                    throw new ServiceException("所选产品价格不匹配！");
                }
            }
        }
        //校验产品总价
        BigDecimal bigDecimal = products.stream().map(ErpProductForCommitOrderDTO::getTotalPrice).collect(Collectors.toList()).stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal totalPrice = erpLicense.getTotalPrice();
        BigDecimal sum = linceseProductVos.stream().map(LinceseProductVo::getProductPrice).collect(Collectors.toList()).stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (!(bigDecimal.compareTo(totalPrice) == 0)) {
            throw new ServiceException("所选产品产品总价不匹配！");
        }
        return flag;
    }

    /**
     * 创建订单相关信息入库
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param productForConfirmOrderVOList
     * @param orderTotalPrice
     * @param erpOrderOperatingRecord
     * @param addErpClient
     * @param nowDate
     * @return
     */
    private Long createOrderRelatedInformation(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders,
                                               Long userId, List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList,
                                               BigDecimal orderTotalPrice, ErpOrderOperatingRecord erpOrderOperatingRecord,
                                               ErpClient addErpClient, Date nowDate) {

        //构建订单数据
        buildOrderObject(erpClientForCommitOrderDTO, erpOrders, userId, orderTotalPrice, erpOrderOperatingRecord, addErpClient, nowDate);

        //默认财务收款时间为销售的收款时间
//        erpOrders.setDatFinanceCollectionTime(erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().get(0).getCollectionTime());
//        erpOrders.setDatFinanceCollectionTime(DateUtils.parseDate("2020-05-01"));

        Long orderId = erpOrdersService.saveOrUpdate(erpOrders);
        erpOrders.setId(orderId);

        if (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getOldFollowId())) {
            ErpOldEnterpriseFollow erpOldEnterpriseFollow = erpOldEnterpriseFollowMapper.selectErpOldEnterpriseFollowById(erpClientForCommitOrderDTO.getOldFollowId());
            erpOldEnterpriseFollow.setFollowStatus(7);
            erpOldEnterpriseFollow.setOrderId(orderId);
            erpOldEnterpriseFollowMapper.updateErpOldEnterpriseFollow(erpOldEnterpriseFollow);
        }

        ErpOldEnterpriseFollow erpOldEnterpriseFollow = erpOldEnterpriseFollowMapper.selectByEnterpriseId(erpOrders.getNumClientId());
        if (ObjectUtil.isNotEmpty(erpOldEnterpriseFollow) && erpOldEnterpriseFollow.getFollowStatus() != 6 && erpOldEnterpriseFollow.getFollowStatus() != 7) {
            erpOldEnterpriseFollow.setStatus(3);
            erpOldEnterpriseFollowMapper.updateErpOldEnterpriseFollow(erpOldEnterpriseFollow);
        }

        //关联流水账单更新
        relatedTransactionFlow(erpClientForCommitOrderDTO, erpOrders, userId, orderId);

        //维护产品-订单关系（erp_service_order）
        maintenanceServiceOrder(erpClientForCommitOrderDTO, userId, productForConfirmOrderVOList, nowDate, erpOrders);
        cancelErpOrderPayment(userId, orderId);
        //凭证流水上线之前
        if (!DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).before(erpOrders.getDatSigningDatecreatedTime())) {
            //维护收款方式
            maintenanceTerm(erpClientForCommitOrderDTO, userId, nowDate, orderId);

            // 更新指定订单的最新收款信息。
            commitOrderMapper.updatePayeeForOrder(orderId);
        }
        //凭证流水上线之后
        if (DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).before(erpOrders.getDatSigningDatecreatedTime())) {
            List<ErpOrderPerformance> performanceList = erpClientForCommitOrderDTO.getPaymentTerm().getPerformanceList();
            erpOrderPerformanceMapper.updateStatusByOrderId(orderId,1L,3L);
            if (ObjectUtil.isNotEmpty(performanceList) && performanceList.size() > 0) {
                for (int i = 0; i < performanceList.size(); i++) {
                    ErpOrderPerformance performance = performanceList.get(i);
                    performance.setStatus(1);
                    performance.setOrderId(orderId);
                    erpOrderPerformanceMapper.insertErpOrderPerformance(performance);
                }
            }


            //编辑/提单，如果未选择支付凭证，不匹配凭证相关，直接通过
            if (erpClientForCommitOrderDTO.getOptionType().intValue() == 2) {
                createPaymentAndRetainageByVoucher(orderId, erpClientForCommitOrderDTO,userId,nowDate, erpOrders.getNumPayPrice());
                // 更新指定订单的最新收款信息。
                commitOrderMapper.updatePayeeForOrder(orderId);
            }
            if (Arrays.asList(1,3).contains(erpClientForCommitOrderDTO.getOptionType().intValue())
                    && ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getTransactionVoucherList())
                    && erpClientForCommitOrderDTO.getTransactionVoucherList().size() > 0){
                createPaymentAndRetainageByVoucher(orderId, erpClientForCommitOrderDTO,userId,nowDate, erpOrders.getNumPayPrice());
                // 更新指定订单的最新收款信息。
                commitOrderMapper.updatePayeeForOrder(orderId);
            }
        }

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(erpClientForCommitOrderDTO.getRemark());
        erpOrderOperatingRecord.setNumCreatedBy(userId);
        erpOrderOperatingRecord.setDatCreatedTime(nowDate);

        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        //校验开票金额
        BigDecimal kpFee = erpOrdersMapper.getKPFeeByOrderId(orderId, 1L);
        if (kpFee.compareTo(new BigDecimal("0")) > 0) {

            List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(orderId);
            //是否新增记账产品
            Boolean addAccount = Boolean.FALSE;
            //是否作废记账产品
            Boolean cancelAccount = Boolean.FALSE;
            for (int i = 0; i < serviceOrdersList.size(); i++) {
                ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                if (Arrays.asList(4,7).contains(serviceOrders.getNumIsDeprecated())) {
                    ErpProductDetail productDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(serviceOrders.getNumProductId());
                    if (productDetail.getServiceTypeId().intValue() == 10) {
                        //新增记账产品
                        if (serviceOrders.getNumIsDeprecated().intValue() == 7) {
                            addAccount = Boolean.TRUE;
                        }
                        //作废记账产品
                        if (serviceOrders.getNumIsDeprecated().intValue() == 4) {
                            cancelAccount = Boolean.TRUE;
                        }
                    }
                }
            }
            if (addAccount) {
                //是否有正常记账产品
                Boolean normalAccount = Boolean.FALSE;
                for (int i = 0; i < serviceOrdersList.size(); i++) {
                    ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                    if (serviceOrders.getNumStatus().intValue() == 1 &&
                            (serviceOrders.getNumIsDeprecated().intValue() == 0 || serviceOrders.getNumIsDeprecated().intValue() == 4)) {
                        ErpProductDetail productDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(serviceOrders.getNumProductId());
                        if (productDetail.getServiceTypeId().intValue() == 10) {
                            normalAccount = Boolean.TRUE;
                        }
                    }
                }
                if (!normalAccount) {
                    throw new ServiceException("已开票订单无法添加记账产品，请撤销开票或冲红");
                }
            }
            if (cancelAccount) {
                //是否有正常记账产品
                Boolean normalAccount = Boolean.FALSE;
                for (int i = 0; i < serviceOrdersList.size(); i++) {
                    ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                    if (serviceOrders.getNumStatus().intValue() == 1 &&
                            (serviceOrders.getNumIsDeprecated().intValue() == 0 || serviceOrders.getNumIsDeprecated().intValue() == 7)) {
                        ErpProductDetail productDetail = erpProductDetailMapper.selectErpProductDetailByNumProductId(serviceOrders.getNumProductId());
                        if (productDetail.getServiceTypeId().intValue() == 10) {
                            normalAccount = Boolean.TRUE;
                        }
                    }
                }
                if (!normalAccount) {
                    throw new ServiceException("已开票订单，记账服务产品不支持作废，请撤销开票或冲红");
                }
            }

            //赠金
            BigDecimal giveFee = new BigDecimal("0");
            for (int j = 0; j < serviceOrdersList.size(); j++) {
                ErpServiceOrders erpServiceOrders = serviceOrdersList.get(j);
                giveFee = giveFee.add(erpTransactionVoucherFollowInfoMapper.getGiveFeeByOrderIdAndProductId(orderId, erpServiceOrders.getNumProductId()));
            }
            //订单退款记录查询
            BigDecimal refoundFee = new BigDecimal("0");
            List<Map<String, Object>> mapList = erpOrdersMapper.getRefoundInfoByOrderId(orderId);
            if (ObjectUtil.isNotEmpty(mapList) && mapList.size() > 0) {
                for (int j = 0; j < mapList.size(); j++) {
                    Map<String, Object> map = mapList.get(j);
                    if (map.containsKey("approveId") && ObjectUtil.isNotEmpty(map.get("approveId"))
                            && map.containsKey("approveStatus") && ObjectUtil.isNotEmpty(map.get("approveStatus"))
                            && map.containsKey("refundAmount") && ObjectUtil.isNotEmpty(map.get("refundAmount"))) {
                        Integer approveStatus = Integer.parseInt(map.get("approveStatus").toString());
                        BigDecimal refundAmount = new BigDecimal(map.get("refundAmount").toString());

                        refoundFee = refoundFee.add(refundAmount);
                    }
                }
            }
            BigDecimal noSupportKpFee = erpOrdersMapper.getNoSupportKPFeeByOrderId(orderId);
            if (erpClientForCommitOrderDTO.getPayPrice().subtract(giveFee).subtract(refoundFee).subtract(noSupportKpFee).compareTo(kpFee) < 0) {
                throw new ServiceException("订单可开票金额不得小于已开票金额"+kpFee+"元，请检查产品实收");
            }
        }

        return orderId;
    }

    /***
     * 作废收款记录
     */
    private void cancelErpOrderPayment(Long userId, Long orderId) {
        // 2022-11-25 查询出原有的term，全部作废，重新插入新的
        ErpOrderPaymentTerm updateErpOrderPayment = new ErpOrderPaymentTerm();
        updateErpOrderPayment.setNumOrderId(orderId);
        updateErpOrderPayment.setNumStatus(0);
        updateErpOrderPayment.setNumUpdatedBy(userId);
        updateErpOrderPayment.setDatSigningDateupdatedTime(new Date());
        erpOrderPaymentTermMapper.updateErpOrderPaymentTermStatus(updateErpOrderPayment);
    }

    /***
     * 作废回款记录
     */
    private void cancelRetainageReturn(List<ErpServiceOrders> serviceOrders, Long userId, Long orderId) {
        for (int i = 0; i < serviceOrders.size(); i++) {
            ErpServiceOrders serviceOrder = serviceOrders.get(i);
            //订单回款记录置为无效
            ErpRetainageReturnDetail erpRetainageReturnDetailUpdate = new ErpRetainageReturnDetail();
            erpRetainageReturnDetailUpdate.setNumServiceOrderId(serviceOrder.getId());
            erpRetainageReturnDetailUpdate.setNumStatus(CommitOrderConstants.RETURN_DETAIL_DELETE);
            erpRetainageReturnDetailUpdate.setUpdateUser(userId);
            erpRetainageReturnDetailUpdate.setUpdateTime(new Date());
            erpRetainageReturnDetailMapper.updateErpRetainageReturnDetailStatus(erpRetainageReturnDetailUpdate);
        }
        //回款作废
        erpRetainageReturnMapper.updateErpRetainageReturnStatusByOrderId(orderId, CommitOrderConstants.RETURN_CANCEL);
    }

    private void createPaymentAndRetainageByVoucher(Long orderId, ErpClientForCommitOrderDTO erpClientForCommitOrderDTO,
                                                    Long userId, Date nowDate, BigDecimal orderPayPrice) {
        commitOrderOperateTransactionVoucherFollow(
                orderId,
                erpClientForCommitOrderDTO.getTransactionVoucherList(),
                erpClientForCommitOrderDTO.getProducts(),
                erpClientForCommitOrderDTO.getOptionType(), orderPayPrice);

        JSONObject payObject = new JSONObject();
        List<Map<String, Object>> payList = erpTransactionVoucherFollowInfoMapper.selectFeeByPayTime(orderId);
        for (int i = 0; i < payList.size(); i++) {
            Map<String, Object> payMap = payList.get(i);
            String monthDate = payMap.get("monthDate").toString();
            String payTime = payMap.get("payTime").toString();
            String payProductId = payMap.get("productId").toString();
            String payProductFee = payMap.get("fee").toString();
            String followId = payMap.get("followId").toString();

            JSONObject payObjectMonth = new JSONObject();
            if (payObject.containsKey(monthDate)) {
                payObjectMonth = payObject.getJSONObject(monthDate);
            }

            if (!payObjectMonth.containsKey("collectionTime") ||
                    DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,payTime).after(payObjectMonth.getDate("collectionTime"))) {
                payObjectMonth.put("collectionTime", DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,payTime));
            }

            BigDecimal allFee = payObjectMonth.containsKey(payProductId) ? payObjectMonth.getBigDecimal(payProductId) : new BigDecimal("0");
            payObjectMonth.put(payProductId, allFee.add(new BigDecimal(payProductFee)));

            JSONArray followIdList = payObjectMonth.containsKey("followIdList") ? payObjectMonth.getJSONArray("followIdList") : new JSONArray();
            if (!followIdList.contains(followId)) {
                followIdList.add(followId);
            }
            payObjectMonth.put("followIdList", followIdList);
            payObject.put(monthDate, payObjectMonth);
        }

        BigDecimal allFee = new BigDecimal("0");
        List<ErpServiceOrders> serviceOrdersList = erpServiceOrdersMapper.getServiceOrdersByOrderId(orderId);

        Long payee = null;
        if (ObjectUtil.isEmpty(erpClientForCommitOrderDTO.getPaymentTerm())) {
            payee = erpClientForCommitOrderDTO.getTransactionVoucherList().get(0).getOperateUser();
        } else {
            payee = erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().get(0).getPayee();
        }
        ErpOrderPerformance erpOrderPerformance = new ErpOrderPerformance();
        erpOrderPerformance.setOrderId(orderId);
        erpOrderPerformance.setStatus(1);
        List<ErpOrderPerformance> orderPerformanceList = erpOrderPerformanceMapper.selectErpOrderPerformanceList(erpOrderPerformance);

        for (String key : payObject.keySet()) {
            JSONObject payObjectMonth = payObject.getJSONObject(key);
            BigDecimal summaryReturnPrice = new BigDecimal("0");
            for (int i = 0; i < serviceOrdersList.size(); i++) {
                ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                if (payObjectMonth.containsKey(serviceOrders.getNumProductId().toString())) {
                    summaryReturnPrice = summaryReturnPrice.add(payObjectMonth.getBigDecimal(serviceOrders.getNumProductId().toString()));
                }
            }
            List<String> followIdList = new ArrayList<>();
            JSONArray followIdArr = payObjectMonth.getJSONArray("followIdList");
            for (int i = 0; i < followIdArr.size(); i++) {
                if (!followIdList.contains(followIdArr.getString(i))) {
                    followIdList.add(followIdArr.getString(i));
                }
            }

            allFee = allFee.add(summaryReturnPrice);
            // 维护尾款回款表。
            ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
            erpRetainageReturn.setNumOrderId(orderId);
            erpRetainageReturn.setNumCollectionPrice(summaryReturnPrice);
//                erpRetainageReturn.setNumDiscounts(summaryDiscountPrice);
            erpRetainageReturn.setNumStatus(RetainageStatusEnum.AUDITING.getStatusType());
            erpRetainageReturn.setDatFinanceCollectionTime(payObjectMonth.getDate("collectionTime"));
            erpRetainageReturn.setNumCreatedBy(userId);
            erpRetainageReturn.setDatSigningDatecreatedTime(nowDate);
            erpRetainageReturn.setNumType(1L);
            erpRetainageReturn.setNumPayee(payee);
            erpRetainageReturn.setFollowIds(com.nnb.common.core.utils.StringUtils.join(followIdList, ","));
            erpRetainageReturnMapper.insertErpRetainageReturn(erpRetainageReturn);

            for (int i = 0; i < serviceOrdersList.size(); i++) {
                ErpServiceOrders serviceOrders = serviceOrdersList.get(i);
                if (payObjectMonth.containsKey(serviceOrders.getNumProductId().toString())) {

                    ErpRetainageReturnDetail erpRetainageReturnDetail = new ErpRetainageReturnDetail();
                    erpRetainageReturnDetail.setNumRetainageReturnId(erpRetainageReturn.getId());
                    erpRetainageReturnDetail.setNumServiceOrderId(serviceOrders.getId());
//                        erpRetainageReturnDetail.setNumDiscounts(retainage.getDiscounts());
                    erpRetainageReturnDetail.setNumCollectionPrice(payObjectMonth.getBigDecimal(serviceOrders.getNumProductId().toString()));
                    erpRetainageReturnDetail.setPayType(1);
                    erpRetainageReturnDetail.setCreateTime(new Date());
                    erpRetainageReturnDetail.setCreateUser(userId);
                    erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetail);

                    if (ObjectUtil.isNotEmpty(orderPerformanceList) && orderPerformanceList.size() > 0) {
                        for (int z = 0; z < orderPerformanceList.size(); z++) {
                            ErpOrderPerformance productPerformance = orderPerformanceList.get(z);
                            if (productPerformance.getProductId().intValue() == serviceOrders.getNumProductId().intValue()) {
//                                erpOrderPerformanceMapper.deleteErpOrderPerformanceInfoByPerformanceId(productPerformance.getId());
                                List<Map<String, Object>> followList = erpTransactionVoucherFollowMapper.selectMapByFollowIds(followIdList, serviceOrders.getNumProductId(), null);
//                                if (ObjectUtil.isEmpty(followList) || followList.size() == 0) {
//                                    followList = erpTransactionVoucherFollowMapper.selectMapByFollowIds(followIdList, serviceOrders.getNumProductId(), 2L);
//                                }
                                for (int x = 0; x < followList.size(); x++) {
                                    if (productPerformance.getFee().compareTo(new BigDecimal("0")) == 0) {
                                        continue;
                                    }
                                    Map<String, Object> followMap = followList.get(x);
                                    if (followMap.containsKey("voucherType") && ObjectUtil.isNotEmpty(followMap.get("voucherType"))
                                            && followMap.containsKey("fee") && ObjectUtil.isNotEmpty(followMap.get("fee"))
                                            && Integer.parseInt(followMap.get("voucherType").toString()) != 3) {
                                        BigDecimal followMapFee = new BigDecimal(followMap.get("fee").toString());

                                        ErpOrderPerformanceInfo erpOrderPerformanceInfo = new ErpOrderPerformanceInfo();
                                        erpOrderPerformanceInfo.setReturnDetailId(erpRetainageReturnDetail.getId());
                                        erpOrderPerformanceInfo.setPerformanceId(productPerformance.getId());
                                        erpOrderPerformanceInfo.setUserId(productPerformance.getUserId());
                                        if (followMapFee.compareTo(productPerformance.getFee()) >= 0) {
                                            erpOrderPerformanceInfo.setFee(productPerformance.getFee());
                                            productPerformance.setFee(new BigDecimal("0"));
                                        } else {
                                            erpOrderPerformanceInfo.setFee(followMapFee);
                                            productPerformance.setFee(productPerformance.getFee().subtract(followMapFee));
                                        }
                                        erpOrderPerformanceMapper.insertErpOrderPerformanceInfo(erpOrderPerformanceInfo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        // 维护收款方式表。
        ErpOrderPaymentTerm erpOrderPaymentTerm = new ErpOrderPaymentTerm();
        erpOrderPaymentTerm.setNumPayee(payee);
        erpOrderPaymentTerm.setNumOrderId(orderId);
        erpOrderPaymentTerm.setNumType(1);
        erpOrderPaymentTerm.setNumCreatedBy(userId);
        erpOrderPaymentTerm.setDatSigningDatecreatedTime(nowDate);
        Long termId = erpOrderPaymentTermService.saveOrUpdate(erpOrderPaymentTerm);

        ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = new ErpOrderPaymentTermInfo();
        erpOrderPaymentTermInfo.setTermId(termId);
        erpOrderPaymentTermInfo.setNumMoney(allFee);
        erpOrderPaymentTermInfo.setNumStatus(1);
        erpOrderPaymentTermInfoService.saveOrUpdate(erpOrderPaymentTermInfo);
    }

    /**
     * 更新优惠券状态
     * @param orderId
     */
    private void updateCoupon(Long orderId) {
        ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
        erpServiceOrders.setNumOrderId(orderId);
        List<ErpServiceOrders> serviceOrderList = erpServiceOrdersMapper.selectErpServiceOrdersList(erpServiceOrders);
        List<Long> couponIds = serviceOrderList.stream()
                .map(ErpServiceOrders::getNumCouponId)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(couponIds)) {
            for (Long couponId : couponIds) {
                ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
                erpDiscountCoupon.setStatus(1L);
                erpDiscountCoupon.setId(couponId);
                erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
            }
        }
    }

    /**
     * //注销服务处理
     * @param erpClientForCommitOrderDTO
     * @param userId
     * @param orderId
     */
    private void logoutServiceHandle(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, Long userId, Long orderId) {
        //注销
        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getZhuXiaoVo())) {
            OpZhuXiaoVo zhuXiaoVo = erpClientForCommitOrderDTO.getZhuXiaoVo();
            if (!Objects.isNull(zhuXiaoVo)) {
                if (zhuXiaoVo.getSbZxStatus() != null) {
                    zhuXiaoVo.setSbZxStatus(58);
                }
                if (ObjectUtil.isNotEmpty(zhuXiaoVo.getGjjZxStatus())) {
                    zhuXiaoVo.setGjjZxStatus(58);
                }
                if (zhuXiaoVo.getGsZxStatus() != null) {
                    zhuXiaoVo.setGsZxStatus(58);
                }
                if (zhuXiaoVo.getYhZxStatus() != null) {
                    zhuXiaoVo.setYhZxStatus(58);
                }
                if (zhuXiaoVo.getSwZxStatus() != null) {
                    zhuXiaoVo.setSwZxStatus(58);
                }
                zhuXiaoVo.setCreatedBy(userId.intValue());
                zhuXiaoVo.setOpId(orderId.intValue());
                commitOrderMapper.insterZhuxiao(zhuXiaoVo);
            }
        }
    }

    /**
     * 生成电子合同
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param nowDate
     * @param orderId
     */
    private void productionElectronicContract(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO,
                                              ErpOrders erpOrders, Long userId, Date nowDate, Long orderId) {

        if ((1 == erpClientForCommitOrderDTO.getOptionType() && Integer.valueOf(1).equals(erpClientForCommitOrderDTO.getIsContract()))
                || (3 == erpClientForCommitOrderDTO.getOptionType() && Integer.valueOf(1).equals(erpClientForCommitOrderDTO.getIsContract())
                && "1".equals(erpClientForCommitOrderDTO.getIsReturn()))
                || (2 == erpClientForCommitOrderDTO.getOptionType() && Integer.valueOf(1).equals(erpClientForCommitOrderDTO.getIsContract())
                && "1".equals(erpClientForCommitOrderDTO.getIsReturn()))
        ) {

            if (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getContractSubject()) && ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getOnlineContracts())) {
                //订单编辑更新电子合同状态
                if (3 == erpClientForCommitOrderDTO.getOptionType() && Integer.valueOf(1).equals(erpClientForCommitOrderDTO.getIsContract()) && "1".equals(erpClientForCommitOrderDTO.getIsReturn())) {
                    OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                    onlineContractEntity.setOrderId(orderId.intValue());
                    onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                    onlineContractEntity.setUpdateTime(new Date());
                    onlineContractEntity.setStatusQuery(1);
                    onlineContractEntity.setStatus(0);
                    onlineContractEntity.setAuditStatusQuery(0);
                    onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                }
                // 生成电子合同编号。
                String contractNumber = onlineContractService.genContractNumber();
                // 电子合同不为空，新增电子合同。
                List<CheckContractVO> onlineContracts = erpClientForCommitOrderDTO.getOnlineContracts();
                onlineContracts.forEach(val -> {
                    Object contractContent = val.getContractContent();
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(contractContent);
                    val.setContractContent(jsonObject);
                });
                List<OnlineContractEntity> onlineContractEntities = BeanUtil.copyToList(erpClientForCommitOrderDTO.getOnlineContracts(), OnlineContractEntity.class);
//                log.info("提单入参的电子合同的数据集合为：{}", onlineContractEntities);
                int size = onlineContractEntities.size();
                //订单编辑或者修改时取原订单的clientId
                ErpOrders orders = new ErpOrders();
                if (erpClientForCommitOrderDTO.getOptionType() == 2 || erpClientForCommitOrderDTO.getOptionType() == 3) {
                    orders = erpOrdersService.selectErpOrdersById(orderId);
                }
                for (OnlineContractEntity onlineContractEntity : onlineContractEntities) {
                    onlineContractEntity.setContractNumber(contractNumber);
                    onlineContractEntity.setContractIndex(onlineContractEntities.indexOf(onlineContractEntity) + 1);
                    if (size > 1) {
                        onlineContractEntity.setShowNumber(onlineContractEntity.getContractNumber() + "-" + onlineContractEntity.getContractIndex());
                    } else {
                        onlineContractEntity.setShowNumber(onlineContractEntity.getContractNumber());
                    }
                    if (1 == erpClientForCommitOrderDTO.getOptionType()) {
                        onlineContractEntity.setClientId(erpOrders.getNumClientId().intValue());
                    } else {
                        onlineContractEntity.setClientId(orders.getNumClientId().intValue());
                    }
                    onlineContractEntity.setOrderId(orderId.intValue());
                    onlineContractEntity.setCreatedBy(userId.intValue());
                    onlineContractEntity.setCreatedTime(nowDate);
                }
//                log.info("提单生成电子合同的数据集合为：{}", onlineContractEntities);
                onlineContractService.saveContractBatch(onlineContractEntities);

                //订单修改更新电子合同状态
                if (2 == erpClientForCommitOrderDTO.getOptionType() && Integer.valueOf(1).equals(erpClientForCommitOrderDTO.getIsContract()) && "1".equals(erpClientForCommitOrderDTO.getIsReturn())) {
                    OnlineContractEntity onlineContractEntity = new OnlineContractEntity();
                    onlineContractEntity.setOrderId(orderId.intValue());
                    onlineContractEntity.setUpdateBy(Objects.nonNull(userId) ? userId.intValue() : null);
                    onlineContractEntity.setUpdateTime(new Date());
                    onlineContractEntity.setStatusQuery(1);
                    onlineContractEntity.setStatus(0);
                    onlineContractEntity.setAuditStatusQuery(1);
                    onlineContractMapper.updateOnlineContractByContractNumber(onlineContractEntity);
                }

            }
        }
    }

    /**
     * 维护收款方式
     * @param erpClientForCommitOrderDTO
     * @param userId
     * @param nowDate
     * @param orderId
     */
    private void maintenanceTerm(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, Long userId, Date nowDate, Long orderId) {

        for (ErpPaymentTermInfoForCommitOrderDTO paymentTermInfo : erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos()) {
            // 添加收款方式信息。
            ErpOrderPaymentTerm erpOrderPayment = new ErpOrderPaymentTerm();
            //erpOrderPayment.setId(paymentTermInfo.getPaymentId());
            erpOrderPayment.setNumOrderId(orderId);
            erpOrderPayment.setNumRetainageId(null);
            erpOrderPayment.setNumType(1);
            erpOrderPayment.setNumCreatedBy(userId);
            erpOrderPayment.setDatSigningDatecreatedTime(nowDate);
            erpOrderPayment.setNumPayee(paymentTermInfo.getPayee());
            erpOrderPayment.setDatCollectionTime(paymentTermInfo.getCollectionTime());

            Long paymentId = erpOrderPaymentTermService.saveOrUpdate(erpOrderPayment);

            for (ErpPaymentTermDetailForCommitOrderDTO paymentDetail : paymentTermInfo.getPaymentDetails()) {
                // 添加收款详情信息。
                ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = new ErpOrderPaymentTermInfo();
                erpOrderPaymentTermInfo.setTermId(paymentId);
                erpOrderPaymentTermInfo.setNumPaymentType(paymentDetail.getPaymentType().longValue());
                erpOrderPaymentTermInfo.setVcPaymentUrl(paymentDetail.getPaymentUrl());
                erpOrderPaymentTermInfo.setNumMoney(paymentDetail.getMoney());
                erpOrderPaymentTermInfo.setNumStatus(1);
                erpOrderPaymentTermInfoService.saveOrUpdate(erpOrderPaymentTermInfo);
            }
        }
    }

    /**
     * 维护产品-订单关系（erp_service_order）
     * @param erpClientForCommitOrderDTO
     * @param userId
     * @param productForConfirmOrderVOList
     * @param nowDate
     * @param orderId
     */
    private void maintenanceServiceOrder(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, Long userId, List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList, Date nowDate, ErpOrders erpOrders) {

        Long orderId = erpOrders.getId();
        //2022-11-25 查询出原有的serviceOrder
        ErpServiceOrders erpServiceOrder = new ErpServiceOrders();
        erpServiceOrder.setNumOrderId(orderId);
        List<ErpServiceOrders> serviceOrders = erpServiceOrdersService.selectErpServiceOrdersList(erpServiceOrder);
        //新
        List<Long> newServiceOrderIds = erpClientForCommitOrderDTO.getProducts().stream()
                .map(ErpProductForCommitOrderDTO::getProductId)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
        //逻辑删除，更改     1) num_status（0删除，1正常），求collect2中没有哪些collect1的
        for (ErpServiceOrders serviceOrder : serviceOrders) {
            if (CollUtil.isNotEmpty(newServiceOrderIds)) {
                if (!newServiceOrderIds.contains(serviceOrder.getNumProductId())) {
                    serviceOrder.setNumStatus(0);
                    erpServiceOrdersService.updateErpServiceOrders(serviceOrder);
                }
            }
        }
        //作废所有的回款记录
        cancelRetainageReturn(serviceOrders, userId, orderId);
        //保存产品的选择状态
        checkOpenProductContract(erpClientForCommitOrderDTO);

        Long retainageReturnId = 0L;
        if (!DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).before(erpOrders.getDatSigningDatecreatedTime())
                && ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getPaymentTerm())
                && ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos())
                && erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().size() > 0) {
            // 维护尾款回款表。
            ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
            erpRetainageReturn.setNumOrderId(orderId);
            erpRetainageReturn.setNumCollectionPrice(erpOrders.getNumPayPrice());
            erpRetainageReturn.setNumStatus(RetainageStatusEnum.AUDITING.getStatusType());
//        erpRetainageReturn.setDatFinanceCollectionTime(payObjectMonth.getDate("collectionTime"));
            erpRetainageReturn.setNumCreatedBy(userId);
            erpRetainageReturn.setDatSigningDatecreatedTime(nowDate);
            erpRetainageReturn.setNumType(1L);
            erpRetainageReturn.setNumPayee(erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos().get(0).getPayee());
//        erpRetainageReturn.setFollowIds(com.nnb.common.core.utils.StringUtils.join(followIdList, ","));
            erpRetainageReturnMapper.insertErpRetainageReturn(erpRetainageReturn);
            retainageReturnId = erpRetainageReturn.getId();
        }

        for (ErpProductForCommitOrderDTO product : erpClientForCommitOrderDTO.getProducts()) {
            // 添加订单-产品（erp_service_order）信息。

            ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
            List<ErpServiceOrders> _serviceOrders = serviceOrders.stream().filter(val -> val.getNumProductId().equals(product.getProductId())).collect(Collectors.toList());
            if (_serviceOrders.size() == 1) {
                erpServiceOrders.setId(_serviceOrders.get(0).getId());
                erpServiceOrders.setNumStatus(_serviceOrders.get(0).getNumStatus());
                product.setServiceOrderId(_serviceOrders.get(0).getId());
            }
            if (ObjectUtil.isNotEmpty(product.getServiceTypeId()) && product.getServiceTypeId().intValue() == ServiceMainConstants.GeTiFaPiaoService) {
                ErpEnterprise enterprise = erpClientMapper.selectEnterpriseByClientId(erpClientForCommitOrderDTO.getClientId());
                if (ObjectUtil.isEmpty(enterprise) || ObjectUtil.isEmpty(enterprise.getKpLimit()) || enterprise.getKpLimit().compareTo(new BigDecimal("0")) <= 0) {
                    throw new ServiceException("未配置企业的可开票额度");
                }

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(erpOrders.getDatSigningDate());
                int year = calendar.get(Calendar.YEAR);
                BigDecimal kpLimit = erpServiceOrdersMapper.getGtKpByEnterprise(enterprise.getId(), DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, year+"-01-01 00:00:00"), DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, (year+1)+"-01-01 00:00:00"));
                if (kpLimit.add(product.getGtKp()).compareTo(enterprise.getKpLimit()) > 0) {
                    throw new ServiceException("个体开票已超额");
                }
            }

            erpServiceOrders.setGtKp(product.getGtKp());
            erpServiceOrders.setKpEnterprise(product.getKpEnterprise());
            erpServiceOrders.setNumOrderId(orderId);
            erpServiceOrders.setNumProductId(product.getProductId());
            erpServiceOrders.setNumProductCount(product.getProductCount());
//            erpServiceOrders.setNumProductInfoId(); // TODO 闲置字段，待删除。
            erpServiceOrders.setNumTotalPrice(product.getTotalPrice());
            erpServiceOrders.setNumPayPrice(product.getPayPrice());
            erpServiceOrders.setNumLastPrice(product.getLastPrice());
            erpServiceOrders.setNumCouponId(product.getCouponId());
            erpServiceOrders.setNumCombinedActivityId(product.getCombinedId());
            erpServiceOrders.setNumCouponPrice(product.getCouponPrice());
            erpServiceOrders.setNumProductPreferential(ObjectUtil.isNotNull(product.getProductPreferential()) ? product.getProductPreferential() : new BigDecimal("0"));
            erpServiceOrders.setNumChannelFee(ObjectUtil.isNotNull(product.getChannelFee()) ? product.getChannelFee() : new BigDecimal("0"));
            erpServiceOrders.setNumPoints(product.getPoints());
            erpServiceOrders.setExtensionServiceId(product.getExtensionServiceId());
            if (ObjectUtil.isNotNull(erpServiceOrders.getId())) {
                erpServiceOrders.setNumIsDeprecated(product.getIsDeprecated());
                erpServiceOrders.setVcUpdatedBy(userId);
                erpServiceOrders.setDatUpdatedTime(nowDate);
                //判断优惠券是否释放
                ErpServiceOrders erpServiceOrdersDB = erpServiceOrdersMapper.selectErpServiceOrdersById(erpServiceOrders.getId());
                if (ObjectUtil.isNotEmpty(erpServiceOrdersDB.getNumCouponId())) {
                    if (ObjectUtil.isEmpty(erpServiceOrders.getNumCouponId())
                            || erpServiceOrders.getNumCouponId().intValue() != erpServiceOrdersDB.getNumCouponId()) {

                        ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
                        erpDiscountCoupon.setStatus(0L);
                        erpDiscountCoupon.setId(erpServiceOrdersDB.getNumCouponId());
                        erpDiscountCouponMapper.updateErpDiscountCoupon(erpDiscountCoupon);
                        if (ObjectUtil.isEmpty(erpServiceOrders.getNumCouponId())) {
                            erpServiceOrdersMapper.updateCouponIdNullById(erpServiceOrders.getId());
                        }
                    }
                }
            } else {
                if (erpClientForCommitOrderDTO.getOptionType() == 2) {
                    erpServiceOrders.setNumIsDeprecated(7);
                } else {
                    erpServiceOrders.setNumIsDeprecated(0);
                }
                erpServiceOrders.setVcCreatedBy(userId);
                erpServiceOrders.setDatCreatedTime(nowDate);
            }
            // 2022-11-25     1) num_status（0删除，1正常）
            erpServiceOrders.setNumStatus(1);
            if (ObjectUtil.isNotEmpty(product.getConfigurationId()) && product.getConfigurationId().intValue() != 0) {
                erpServiceOrders.setConfigurationId(product.getConfigurationId());
            }
            erpServiceOrders.setLicenseNumber(ObjectUtil.isNotNull(product.getLicenseNumber()) ? product.getLicenseNumber() : null);
            erpServiceOrders.setNoContractSelectedStatus(product.getNoContractSelectedStatus());
            //税控托管产品处理
            taxControlCustodyProductHandle(erpClientForCommitOrderDTO, erpServiceOrders);
            Long serviceOrderId = erpServiceOrdersService.saveOrUpdate(erpServiceOrders);

            //有资质延期或年检,处理关联关系
            handleServiceQualificationsExtension(product, erpServiceOrders);



            erpServiceOrdersMapper.updateFinishDateNUll(serviceOrderId);
            //凭证流水上线之前
            if (!DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,voucherOrderCreateTime).before(erpOrders.getDatSigningDatecreatedTime())) {
                //修改，提单，编辑金额维护（维护erp_retainage_return_detail表）
                maintenanceRetainReturnDetail(userId, product, erpServiceOrders,orderId, retainageReturnId);
            }

            //维护订单-产品详情表，只插入不删除
            maintenanceServiceOrderInfo(productForConfirmOrderVOList, product, serviceOrderId);

            //维护退款金额
            maintenanceOrderRefund(userId, nowDate, product);
        }
    }

    /**
     * 维护退款金额
     * @param userId
     * @param nowDate
     * @param product
     */
    private void maintenanceOrderRefund(Long userId, Date nowDate, ErpProductForCommitOrderDTO product) {
        if (ObjectUtil.isNotNull(product.getServiceOrderId())
                && ObjectUtil.isNotNull(product.getRefundPrice())
                && product.getRefundPrice().compareTo(new BigDecimal("0")) != 0) {
            // 退款金额维护。
            ErpServiceOrderRefund erpServiceOrderRefund = new ErpServiceOrderRefund();
            erpServiceOrderRefund.setNumServiceOrderId(product.getServiceOrderId());
            erpServiceOrderRefund.setNumRefundPrice(product.getRefundPrice());
            // 设置退款状态为待审核，若此次修改被驳回，需将退款金额重新加回到订单内。
            erpServiceOrderRefund.setNumStatus(OrderRefundApprovalStatusEnum.REFUND_NOT.getStatusType());
            erpServiceOrderRefund.setNumCreatedBy(userId);
            erpServiceOrderRefund.setDatCreateTime(nowDate);
            erpServiceOrderRefund.setUpdateBy(userId.toString());
            erpServiceOrderRefund.setUpdateTime(nowDate);

            erpServiceOrderRefundService.insertErpServiceOrderRefund(erpServiceOrderRefund);
        }
    }

    //维护订单-产品详情表，只插入不删除
    private void maintenanceServiceOrderInfo(List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList, ErpProductForCommitOrderDTO product, Long serviceOrderId) {
        // 添加订单-产品info（erp_service_order_info）信息。
        ErpProductForConfirmOrderVO productBase = productForConfirmOrderVOList.stream()
                .filter(e -> e.getProductId().equals(product.getProductId()))
                .collect(Collectors.toList()).get(0);

        if (ObjectUtil.isNull(product.getServiceOrderId())) {
            ErpServiceOrdersInfo erpServiceOrdersInfo = new ErpServiceOrdersInfo();
            erpServiceOrdersInfo.setNumServiceOrders(serviceOrderId);
            erpServiceOrdersInfo.setVcProductName(product.getProductName());
            erpServiceOrdersInfo.setVcCorporateProperty(productBase.getTaxName());
            erpServiceOrdersInfo.setNumPrice(product.getProductPrice());
            erpServiceOrdersInfo.setVcProductType(productBase.getServiceName());
            erpServiceOrdersInfo.setVcUnit(product.getProductUnit());
            erpServiceOrdersInfo.setVcArea(productBase.getRegion());
            erpServiceOrdersInfoService.saveOrUpdate(erpServiceOrdersInfo);
        }
    }

    /**修改，提单，编辑金额维护（维护erp_retainage_return_detail表）
     * @param userId
     * @param product
     * @param erpServiceOrders
     */
    private void maintenanceRetainReturnDetail(Long userId, ErpProductForCommitOrderDTO product, ErpServiceOrders erpServiceOrders, Long orderId, Long retainageReturnId) {
            //再插入新记录
        ErpRetainageReturnDetail erpRetainageReturnDetailSave = new ErpRetainageReturnDetail();
        erpRetainageReturnDetailSave.setNumServiceOrderId(erpServiceOrders.getId());
        erpRetainageReturnDetailSave.setNumStatus(erpServiceOrders.getNumIsDeprecated());
        erpRetainageReturnDetailSave.setPayType(CommitOrderConstants.RETURN_DETAIL_COMMIT);
        erpRetainageReturnDetailSave.setNumCollectionPrice(product.getPayPrice());
        erpRetainageReturnDetailSave.setCreateTime(new Date());
        erpRetainageReturnDetailSave.setCreateUser(userId);
        if (retainageReturnId.intValue() != 0) {
            erpRetainageReturnDetailSave.setNumRetainageReturnId(retainageReturnId);
        }
        erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetailSave);
    }

    private void relatedTransactionFlow(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, Long userId, Long orderId) {
        if (CommitOrderConstants.OPTION_COMMIT.equals(erpClientForCommitOrderDTO.getOptionType())){
            if (1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                //客保提单
                if (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getClueId())) {
                    PaymentInfoVo paymentInfoVo = new PaymentInfoVo();
                    paymentInfoVo.setClueId(erpClientForCommitOrderDTO.getClueId().intValue());
                    paymentInfoVo.setUserId(userId.intValue());
                    List<PaymentInfoVo> paymentInfoVos = erpOrdersMapper.selectPaymentInfoList(paymentInfoVo);
                    if (CollUtil.isNotEmpty(paymentInfoVos)){
                        for (PaymentInfoVo infoVo : paymentInfoVos) {
                            if (StringUtils.isEmpty(infoVo.getOrderId())) {
                                //日志
                                String remark = String.format(CommitOrderConstants.PAY_LOG_CONSTANT,
                                        erpClientForCommitOrderDTO.getCommitOrderType(),
                                        erpClientForCommitOrderDTO.getOptionType(),
                                        erpOrders.getVcOrderNumber(), orderId);

                                PaymentLog paymentLog = new PaymentLog(null, Integer.valueOf(infoVo.getId()),
                                        CommitOrderConstants.OP_TYPE_ORDER_COMMIT, userId, LocalDateTime.now(), remark);
                                //入库
                                commitOrderMapper.updateOrderBill(infoVo.getId(), String.valueOf(orderId), erpOrders.getVcOrderNumber());
                                paymentMapper.insertPaymentLog(paymentLog);
                            }
                        }
                    }
                }
            } else if (2 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                //客户提单
                if (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getProducts().get(0).getClientId())) {
                    PaymentInfoVo paymentInfoVo = new PaymentInfoVo();
                    paymentInfoVo.setClientId(String.valueOf(erpClientForCommitOrderDTO.getProducts().get(0).getClientId()));
                    paymentInfoVo.setUserId(userId.intValue());
                    List<PaymentInfoVo> paymentInfoVos = erpOrdersMapper.selectPaymentInfoList(paymentInfoVo);
                    if (CollUtil.isNotEmpty(paymentInfoVos)){
                        for (PaymentInfoVo infoVo : paymentInfoVos) {
                            if (StringUtils.isEmpty(infoVo.getOrderId())) {
                                //日志
                                String remark = String.format(CommitOrderConstants.PAY_LOG_CONSTANT,
                                        erpClientForCommitOrderDTO.getCommitOrderType(),
                                        erpClientForCommitOrderDTO.getOptionType(),
                                        erpOrders.getVcOrderNumber(), orderId);

                                PaymentLog paymentLog = new PaymentLog(null, Integer.valueOf(infoVo.getId()),
                                        CommitOrderConstants.OP_TYPE_ORDER_COMMIT, userId, LocalDateTime.now(), remark);

                                //入库
                                commitOrderMapper.updateOrderBill(infoVo.getId(), String.valueOf(orderId), erpOrders.getVcOrderNumber());
                                paymentMapper.insertPaymentLog(paymentLog);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 构建订单
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param orderTotalPrice
     * @param erpOrderOperatingRecord
     * @param addErpClient
     * @param nowDate
     */
    private void buildOrderObject(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders,
                                  Long userId, BigDecimal orderTotalPrice,
                                  ErpOrderOperatingRecord erpOrderOperatingRecord, ErpClient addErpClient, Date nowDate) {
        erpOrders.setCommitOrderType(erpClientForCommitOrderDTO.getCommitOrderType());
        erpOrders.setId(erpClientForCommitOrderDTO.getOrderId());
        erpOrders.setNumTotalPrice(orderTotalPrice);
        erpOrders.setNumPayPrice(erpClientForCommitOrderDTO.getPayPrice());
        erpOrders.setNumLastPrice(erpClientForCommitOrderDTO.getLastPrice());

        erpOrders.setNumRetainageStatus(ObjectUtil.isNotNull(erpOrders.getNumLastPrice())
                && erpOrders.getNumLastPrice().compareTo(new BigDecimal("0")) > 0 ?
                OrderLastStatusEnum.LAST_WITH.getStatusType() :
                OrderLastStatusEnum.LAST_WITHOUT.getStatusType());

        erpOrders.setNumDiscountAmount(erpClientForCommitOrderDTO.getDiscountAmount());

        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getPayPrice())
                && erpClientForCommitOrderDTO.getPayPrice().compareTo(new BigDecimal("0")) != 0) {
            // 订单订单内有支付金额，设置支付状态为已支付。
            erpOrders.setNumPaymentStatus(1);
        } else {
            // 当前订单内无支付金额，设置支付状态为未支付。
            erpOrders.setNumPaymentStatus(0);
        }

        ErpOrders ordersDB = erpOrdersMapper.selectErpOrdersById(erpClientForCommitOrderDTO.getOrderId());
        if (erpClientForCommitOrderDTO.getOptionType() == 1) {
            //第一次提单
            initiateOrder(erpClientForCommitOrderDTO, erpOrders, userId, erpOrderOperatingRecord, addErpClient, nowDate);
        } else if (erpClientForCommitOrderDTO.getOptionType() == 2) {
            //修改订单
            modifyOrder(erpClientForCommitOrderDTO, erpOrders, userId, erpOrderOperatingRecord, nowDate,ordersDB);
            //历史订单补充订单创建时间，后续做判断
            erpOrders.setDatSigningDatecreatedTime(ordersDB.getDatSigningDatecreatedTime());
        } else if (erpClientForCommitOrderDTO.getOptionType() == 3) {
            //编辑订单。
            editOrder(erpClientForCommitOrderDTO, erpOrders, userId, erpOrderOperatingRecord, nowDate,ordersDB);
            //历史订单补充订单创建时间，后续做判断
            erpOrders.setDatSigningDatecreatedTime(ordersDB.getDatSigningDatecreatedTime());
        }

        // 积分。
        erpOrders.setNumPoints(erpClientForCommitOrderDTO.getProducts().stream().map(ErpProductForCommitOrderDTO::getPoints).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 成长值。
        erpOrders.setNumGrowthValue(new BigDecimal("0"));
        // 备注。
        erpOrders.setVcRemark(erpClientForCommitOrderDTO.getRemark());
        // 电子合同主体。
        erpOrders.setContractSubject(erpClientForCommitOrderDTO.getContractSubject());
        //执照编号
        erpOrders.setLicenseNumber(erpClientForCommitOrderDTO.getLicenseNo());
        //提单时手机号，关联小程序或后期其他平台
        if (Objects.nonNull(erpClientForCommitOrderDTO.getCommitOrderType()) && 1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
            R<List<BdClueContacts>> bdClueContact = remoteCustomerService.getBdClueContactByClueId(erpClientForCommitOrderDTO.getClueId());
            if (bdClueContact.getCode() == 200 && bdClueContact.getData().size() > 0) {
                erpOrders.setVcPhone(bdClueContact.getData().get(0).getVcPhone());
            } else {
                R<BdClue> clue = remoteCustomerService.getBdClueById(erpClientForCommitOrderDTO.getClueId());
                if (clue.getCode() == 200 && Objects.nonNull(clue.getData())) {
                    erpOrders.setVcPhone(clue.getData().getVcPhone());
                }
            }
        }
        //生成密文
        erpOrders.setCipherId(SecurityUtils.encryptPassword(erpOrders.getVcOrderNumber() + erpOrders.getContractSubject() + ""));
        //商机池ID
        if (Objects.nonNull(erpClientForCommitOrderDTO.getClueId())) {
            R<BdClue> clue = remoteCustomerService.getBdClueById(erpClientForCommitOrderDTO.getClueId());
            if (clue.getCode() == 200 && Objects.nonNull(clue.getData())) {
                erpOrders.setNicheFlowConfId(clue.getData().getNicheFlowConfId());
            }
        }
        if (ObjectUtil.isEmpty(erpOrders.getId())) {
            //新建订单，查询是否客户拜访后成单
            ErpAccountVisit accountVisit = accountVisitMapper.getLatestAccountVisitByClientId(erpOrders.getNumClientId());
            if (ObjectUtil.isNotEmpty(accountVisit) && ObjectUtil.isNotEmpty(accountVisit.getVisitDate())) {
                Date visitDate = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.dateTime(accountVisit.getVisitDate()) + " 00:00:00");
                if (!erpOrders.getDatSigningDate().before(visitDate)
                        && erpOrders.getDatSigningDate().before(com.nnb.erp.util.DateUtil.plusMonths(visitDate, 400))) {
                    erpOrders.setVisitId(accountVisit.getId());
                }
            }
        }
    }

    /**
     * 第一次提单
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param erpOrderOperatingRecord
     * @param addErpClient
     * @param nowDate
     */
    private void initiateOrder(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, Long userId, ErpOrderOperatingRecord erpOrderOperatingRecord, ErpClient addErpClient, Date nowDate) {
        if (erpClientForCommitOrderDTO.getSource() == 0) {
            if (Objects.nonNull(erpClientForCommitOrderDTO.getCommitOrderType()) && 1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
                erpOrders.setNumClueId(erpClientForCommitOrderDTO.getClueId());
            }
            erpOrders.setNumClientId(addErpClient.getId());
        }
        String vcOrderNumber = DateUtil.format(new Date(), "yyyyMMdd") + userId + RandomUtil.randomNumbers(3);
        int count = erpOrdersMapper.selectCountByOrderNumber(vcOrderNumber);

        while (count > 0) {
            vcOrderNumber = DateUtil.format(new Date(), "yyyyMMdd") + userId + RandomUtil.randomNumbers(3);
            count = erpOrdersMapper.selectCountByOrderNumber(vcOrderNumber);
        }
        erpOrders.setVcOrderNumber(vcOrderNumber);
        erpOrders.setNumSource(erpClientForCommitOrderDTO.getSource());
        erpOrders.setNumUserId(erpClientForCommitOrderDTO.getPaymentTerm().getUserId());
        erpOrders.setDatSigningDate(erpClientForCommitOrderDTO.getPaymentTerm().getSigningDate());
        erpOrders.setNumCreatedBy(userId);
        erpOrders.setDatSigningDatecreatedTime(nowDate);
        erpOrders.setNumCustomerType(erpClientForCommitOrderDTO.getClientType());
        erpOrders.setVcPhone(erpClientForCommitOrderDTO.getPhone());
        erpOrders.setNumContractId(erpClientForCommitOrderDTO.getContractId());
        erpOrders.setContactNum(commitOrderMapper.getContractNum(erpClientForCommitOrderDTO.getContractId()));
        erpOrders.setIsElectronicContract(erpClientForCommitOrderDTO.getIsContract());
        erpOrders.setCustomerIntentionId(erpClientForCommitOrderDTO.getCustomerIntentionId());

        // 设置合同编号。
        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getContractId())) {
            erpOrders.setNumContractId(erpClientForCommitOrderDTO.getContractId());
            // 修改合同使用状态为：已使用。选择合同编号
            erpContractService.updateContractStatusById(erpClientForCommitOrderDTO.getContractId(), ErpProductConstants.ContractStatus.USED);
        }

        // 将订单提单审核设置为：经理待审核。
        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType());

        // 设置除提单外的各个审核状态为默认状态。
        erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());
        erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());
        erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());

        // 订单状态修改为。
        erpOrders.setNumStatus(Long.valueOf(OrderStatusEnum.ORDER_NOT_AUDIT.getStatusType()));
        // 订单失效状态设置为：有效。
        erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_VALID.getStatusType());
        // 订单服务状态设置为：未开始。
        erpOrders.setNumBizStatus(OrderBizStatusEnum.BIZ_NO_START.getStatusType());
        // 设置收款方式为线上支付/线下支付。
        erpOrders.setNumPaymentTerm(ObjectUtil.isNull(erpClientForCommitOrderDTO.getContractId()) ? 1 : 2);

        // 填充订单操作记录信息，用以记录当前操作。
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER.getTypeInt());

        // 维护客户线索关联表。
        //
        if (Objects.nonNull(erpClientForCommitOrderDTO.getCommitOrderType()) && 1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
            ErpClientClue clientClue = new ErpClientClue();
            clientClue.setNumClientId(erpClientForCommitOrderDTO.getClientId());
            clientClue.setNumClueId(erpClientForCommitOrderDTO.getClueId());
            clientClueMapper.insertErpClientClue(clientClue);
        }

        // 维护线索联系人。
        if (Objects.nonNull(erpClientForCommitOrderDTO.getCommitOrderType()) && 1 == erpClientForCommitOrderDTO.getCommitOrderType()) {
            erpClientService.maintainContact(addErpClient.getId(), erpClientForCommitOrderDTO.getClueId());
        }
    }

    /**
     * 编辑订单
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param erpOrderOperatingRecord
     * @param nowDate
     */
    private void editOrder(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, Long userId,
                           ErpOrderOperatingRecord erpOrderOperatingRecord, Date nowDate, ErpOrders ordersDB) {

        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        erpOrders.setNumUserId(erpClientForCommitOrderDTO.getPaymentTerm().getUserId());
        erpOrders.setDatSigningDate(erpClientForCommitOrderDTO.getPaymentTerm().getSigningDate());
        erpOrders.setIsElectronicContract(erpClientForCommitOrderDTO.getIsContract());
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && (2 == erpClientForCommitOrderDTO.getIsContract())) {
            erpOrders.setNumContractId(erpClientForCommitOrderDTO.getContractId());
            erpOrders.setContactNum(commitOrderMapper.getContractNum(erpClientForCommitOrderDTO.getContractId()));
        }
        erpOrders.setIsReturn(Objects.nonNull(erpClientForCommitOrderDTO.getIsReturn()) ? Integer.valueOf(erpClientForCommitOrderDTO.getIsReturn()) : null);

        if (!erpClientForCommitOrderDTO.getClientId().equals(ordersDB.getNumClientId())){
            erpOrders.setNumClientIdNew(erpClientForCommitOrderDTO.getClientId());
            erpOrders.setNumClientId(erpClientForCommitOrderDTO.getClientId());
            String remark = String.format(OperatingConstants.ORDER_EXAMINE, ordersDB.getNumClientId(), erpClientForCommitOrderDTO.getClientId());
            erpOrderOperatingRecord.setVcOperationContent(remark);
        }

        //更换合同 排除上传收款截图调用
        if(!new Integer(1).equals(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot())){
            editOrderContract(erpClientForCommitOrderDTO, erpOrders, ordersDB);
        }

        // 将订单提单审核设置为：经理待审核。
        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType());
        //如果为上传收款截图时调用的则置为：财务待审核。
        if (Objects.nonNull(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot()) && 1 == erpClientForCommitOrderDTO.getEditOrderUploadScreenshot()) {
            erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
            erpOrders.setCreateArriveFinance(new Date());
            try {
                send(ordersDB);
            } catch (Exception e) {
                log.error("上传完收款截图发送财务审核提醒邮件异常，异常信息为：", e);
            }
        }
        // 订单失效状态设置为：有效。
        erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_VALID.getStatusType());

        // 填充订单操作记录信息，用以记录当前操作。
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.EDIT_ORDER.getTypeInt());

        //重签电子合同释放原先的连接
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsReturn()) && erpClientForCommitOrderDTO.getIsReturn().equals("1")) {
            erpOrders.setDeleteEleContactUrl(Constants.Y);
        }
    }

    /**
     * 上传完收款截图发送财务审核提醒邮件
     * @param orders
     */
    private void send(ErpOrders orders) {
        ErpNewApproval erpNewApproval = new ErpNewApproval();
        erpNewApproval.setOtherId(orders.getId());
        erpNewApproval.setStatus(1);
        List<ErpNewApproval> erpNewApprovals = erpNewApprovalMapper.selectErpNewApprovalList(erpNewApproval);
        if (CollectionUtils.isNotEmpty(erpNewApprovals)) {
            Integer type = erpNewApprovals.get(0).getType();
            try {
                ErpNewApproval erpNewApprovalSend = new ErpNewApproval();
                erpNewApprovalSend.setOtherId(orders.getId());
                erpNewApprovalSend.setType(type);
                erpNewApprovalService.sendFinanceOrderEmail(orders.getNumUserId(), erpNewApprovalSend);
            } catch (Exception e) {
                log.error("上传完收款截图发送财务审核提醒邮件异常，异常信息为：", e);
            }
        }
    }

    /**
     * 编辑订单合同
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param ordersDB
     */
    private void editOrderContract(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, ErpOrders ordersDB) {
        //更换新的纸质合同
        if ((2 == erpClientForCommitOrderDTO.getIsContract())) {
            ErpContract erpContract = erpContractMapper.selectErpContractById(erpClientForCommitOrderDTO.getContractId());

            if (ObjectUtil.isNotEmpty(erpContract) && (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot()))) {
                if (erpContract.getNumStatus() == 3 && !new Integer(1).equals(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot())) {
                    throw new ServiceException("该合同号已被使用");
                }
            }
            if (erpContract != null) {
                if (erpContract.getNumStatus() == 2) {
                    erpContract.setId(erpClientForCommitOrderDTO.getContractId());
                    erpContract.setNumStatus(3L);
                    erpContractMapper.updateErpContract(erpContract);
                }
            }
            //释放合同
            releaseContract(erpOrders, ordersDB, erpClientForCommitOrderDTO);

        }
        //更换为空白合同
        if ((3 == erpClientForCommitOrderDTO.getIsContract())) {
            //将前端所传合同ID和合同编号置空
            erpOrders.setNumContractId(null);
            erpOrders.setContactNum(null);
            erpClientForCommitOrderDTO.setContractId(null);
            Integer isElectronicContract = ordersDB.getIsElectronicContract();
            //纸质合同 释放订单绑定的合同
            if (2 == isElectronicContract) {
                erpOrders.setReleaseContract(1);
            }
            //电子合同 如果为经理驳回，则作废电子合同，财务驳回已作废电子合同
            if (1 == isElectronicContract && (ordersDB.getNumCreateOrderExamineStatus() == OrderApprovalStatusEnum.MANAGER_REJECTED.getStatusType())) {
                OnlineContractEntity onlineContract = new OnlineContractEntity();
                onlineContract.setStatus(0);
                onlineContract.setOrderId(ordersDB.getId().intValue());
                //维护电子合同状态
                onlineContractMapper.updateOnlineContract(onlineContract);
            }
        }
    }

    /**
     * 释放合同
     */
    private void releaseContract(ErpOrders orders, ErpOrders erpOrders, ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        Integer isContract = erpClientForCommitOrderDTO.getIsContract();
        Integer isElectronicContract = erpOrders.getIsElectronicContract();
        //电子更换纸质释放电子合同
        if((Objects.nonNull(isContract) && 2 == isContract) && (Objects.nonNull(isElectronicContract) && 1 == isElectronicContract)){
            //将原先绑定的电子合同置为废弃
            onlineContractMapper.releaseOnlineContractInEdit(orders.getId());
        }
    }


    /**
     * 修改订单
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     * @param userId
     * @param erpOrderOperatingRecord
     * @param nowDate
     */
    private void modifyOrder(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, Long userId,
                             ErpOrderOperatingRecord erpOrderOperatingRecord, Date nowDate, ErpOrders ordersDB) {
        // 修改订单。
        erpOrders.setIsReturn(Objects.nonNull(erpClientForCommitOrderDTO.getIsReturn()) ? Integer.valueOf(erpClientForCommitOrderDTO.getIsReturn()) : null);
        erpOrders.setNumUpdatedBy(userId);
        erpOrders.setDatSigningDateupdatedTime(nowDate);
        erpOrders.setNumUserId(erpClientForCommitOrderDTO.getPaymentTerm().getUserId());
        erpOrders.setDatSigningDate(erpClientForCommitOrderDTO.getPaymentTerm().getSigningDate());
        if ((Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && (Objects.nonNull(ordersDB.getIsElectronicContract()))) &&
                (!erpClientForCommitOrderDTO.getIsContract().equals(ordersDB.getIsElectronicContract()))) {
            erpOrders.setIsElectronicContractNew(erpClientForCommitOrderDTO.getIsContract());
        }
        if (!erpClientForCommitOrderDTO.getClientId().equals(ordersDB.getNumClientId())){
            erpOrders.setNumClientId(erpClientForCommitOrderDTO.getClientId());
            erpOrders.setNumClientIdNew(erpClientForCommitOrderDTO.getClientId());
            String remark = String.format(OperatingConstants.ORDER_EXAMINE, ordersDB.getNumClientId(), erpClientForCommitOrderDTO.getClientId());
            erpOrderOperatingRecord.setVcOperationContent(remark);
        }
        // 更换合同
        changeContract(erpClientForCommitOrderDTO, erpOrders, ordersDB);
        // 设置修改审核状态状态为：经理待审核。
        erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.MANAGER_NOT.getStatusType());
        //绑定最新的客户ID
        //erpOrders.setNumClientIdNew(addErpClient.getId());

        // 填充订单操作记录信息，用以记录当前操作。
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.MODIFY_ORDER.getTypeInt());
    }

    /**
     * 更换合同
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     */
    private void changeContract(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrders erpOrders, ErpOrders ordersDB) {
        //是否为纸质更换电子合同
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && 1 == erpClientForCommitOrderDTO.getIsContract()) {
            if (Objects.nonNull(ordersDB.getIsElectronicContract()) && 2 == ordersDB.getIsElectronicContract()) {
                erpOrders.setIsPaperChangeOnline(1);
            }
        }
        //电子更换为纸质
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && 2 == erpClientForCommitOrderDTO.getIsContract() && Objects.nonNull(erpClientForCommitOrderDTO.getContractId())) {
            if (Objects.nonNull(ordersDB.getIsElectronicContract()) && 1 == ordersDB.getIsElectronicContract()) {
                erpOrders.setIsOnlineChangePaper(1);
                erpOrders.setNumContractIdNew(erpClientForCommitOrderDTO.getContractId());
            }
        }
        //更换成纸质合同
        if ((Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && 2 == erpClientForCommitOrderDTO.getIsContract())
                && ((Objects.nonNull(ordersDB.getNumContractId()) && Objects.nonNull(erpClientForCommitOrderDTO.getContractId()) &&
                !ordersDB.getNumContractId().equals(erpClientForCommitOrderDTO.getContractId())) || (Objects.isNull(ordersDB.getNumContractId()) && StringUtils.isEmpty(ordersDB.getContactNum())))) {
            // 保存新的合同编号。
            if (ObjectUtil.isNull(erpClientForCommitOrderDTO.getContractId())) {
                throw new ServiceException("请选择合同编号!");
            }
            ErpContract erpContract = erpContractMapper.selectErpContractById(erpClientForCommitOrderDTO.getContractId());

            if (ObjectUtil.isNotEmpty(erpContract) && erpContract.getNumStatus() == 3) {
                throw new ServiceException("该合同号已被使用");
            }
            erpOrders.setNumContractIdNew(erpClientForCommitOrderDTO.getContractId());
        }
        //更换成空白合同
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsContract()) && 3 == erpClientForCommitOrderDTO.getIsContract()) {
            erpOrders.setIsChangeBlankContract(1);
        }
        //重签电子合同释放原先的连接
        if (Objects.nonNull(erpClientForCommitOrderDTO.getIsReturn()) && erpClientForCommitOrderDTO.getIsReturn().equals("1")) {
            erpOrders.setDeleteEleContactUrl(Constants.Y);
        }
    }

    /**
     * //税控托管产品处理
     * @param erpClientForCommitOrderDTO
     * @param erpOrders
     */
    private void taxControlCustodyProductHandle(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpServiceOrders erpServiceOrders) {
        if (erpClientForCommitOrderDTO.getEditOrderUploadScreenshot() == null) {
            for (ErpProductForCommitOrderDTO product : erpClientForCommitOrderDTO.getProducts()) {
                //税控服务，校验税控托管时间
                if (product.getServiceTypeId().intValue() == ServiceMainConstants.ShuiKongService &&
                        product.getProductId().intValue() == erpServiceOrders.getNumProductId().intValue()) {

                    if (ObjectUtil.isNull(product.getAcStart())
                            && ObjectUtil.isNull(product.getAcStart())) {
                        throw new ServiceException("税控产品时间不正确");
                    }

                    Date acStartProduct = DateUtils.dateTime(DateUtils.YYYY_MM_DD, product.getAcStart() + "-01");
                    Date acEndProduct = DateUtils.dateTime(DateUtils.YYYY_MM_DD, product.getAcEnd() + "-01");
                    acEndProduct = com.nnb.erp.util.DateUtil.plusMonths(acEndProduct, 1);
                    acEndProduct = com.nnb.erp.util.DateUtil.plusDays(acEndProduct, -1);


                    int numUnit = 0;
                    if (product.getProductUnit().equals("年")) {
                        numUnit = 12;
                    }
                    if (product.getProductUnit().equals("月")) {
                        numUnit = 1;
                    }
                    int numProductCount = product.getProductCount();
                    if (numUnit == 0 || ObjectUtil.isEmpty(numProductCount)) {
                        throw new ServiceException("产品数量单位错误");
                    }
                    int months = numUnit * numProductCount;

                    Date acEnd = com.nnb.erp.util.DateUtil.plusMonthsDate(acStartProduct, months);
                    acEnd = com.nnb.erp.util.DateUtil.plusDays(acEnd, -1);
                    if (!acEnd.equals(acEndProduct)) {
                        throw new ServiceException("税控产品时间不正确");
                    }
                    erpServiceOrders.setAcStart(acStartProduct);
                    erpServiceOrders.setAcEnd(acEndProduct);
                }
            }
        }
    }

    /**
     * 关联客户，根据手机号和企业或个人ID查询erp_client是否存在此条数据，不存在则新建一条关联至改订单,存在则将此客户关联至订单
     * @param erpClientForCommitOrderDTO
     * @param products
     * @return
     */
    private ErpClient relatedCustomers(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        List<ErpProductForCommitOrderDTO> products = erpClientForCommitOrderDTO.getProducts();
        Long clientId = null;
        if (2 == erpClientForCommitOrderDTO.getCommitOrderType()) {
            clientId = CollectionUtils.isNotEmpty(products) ? products.get(0).getClientId() : null;
        } else {
            clientId = erpClientForCommitOrderDTO.getClientId();
        }
        ErpClient client = erpClientMapper.selectErpClientById(clientId);
        if (Objects.isNull(client)) {
            throw new ServiceException("不存在该客户！");
        }
        Integer numType = client.getNumType();
        if (1 == numType) {
            erpClientForCommitOrderDTO.setNumEnterpriseId(client.getNumEnterpriseId());
        }
        if (2 == numType) {
            erpClientForCommitOrderDTO.setNumPersonalId(client.getNumPersonalId());
        }
        ErpClient addErpClient = addErpClient(erpClientForCommitOrderDTO);
        if (Objects.isNull(addErpClient)) {
            throw new ServiceException("不存在订单关联客户！");
        }
        if(1 == erpClientForCommitOrderDTO.getCommitOrderType()){
            maintainClueIntoClient(erpClientForCommitOrderDTO);
        }
        return addErpClient;
    }

    /**
     * 客保提单时将将线索联系人相关信息维护至客户表
     * @param erpClientForCommitOrderDTO
     */
    private void maintainClueIntoClient(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        Long clientId = erpClientForCommitOrderDTO.getClientId();
        Long clueId = erpClientForCommitOrderDTO.getClueId();

        R<List<BdClueContacts>> contact = remoteCustomerService.getBdClueContactByClueId(clueId);
        if (200 == contact.getCode()) {
            List<BdClueContacts> data = contact.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                BdClueContacts bdClueContacts = data.get(0);
                if (Objects.nonNull(bdClueContacts)) {
                    ErpClient erpClient = new ErpClient();
                    erpClient.setId(clientId);
                    erpClient.setSource(1);
                    erpClient.setSex(bdClueContacts.getNumSex());
                    erpClient.setPostBox(bdClueContacts.getVcEmail());
                    erpClient.setRole(bdClueContacts.getVcContactRole());
                    if(Objects.nonNull(erpClient.getSex()) || StringUtils.isNotEmpty(erpClient.getPostBox()) || StringUtils.isNotEmpty(erpClient.getRole())){
                        erpClientMapper.updateErpClient(erpClient);
                    }
                }
            }
        }
    }

    /**
     *校验应付、实付、尾款、优惠金额是否计算正确。
     *校验规则:
     *    提单校验：
     *        订单总金额 = 产品总金额
     *        订单总优惠 = 产品总优惠
     *        订单总应收 = 产品总应收
     *        订单总实收 = 产品总实收
     *        订单总实收 = 收款金额汇总
     *        订单总尾款 = 产品总尾款
     *    修改订单校验：
     *        订单总金额 = 产品总金额
     *        订单总优惠 = 产品总优惠
     *        订单总应收 = 产品总应收
     *        订单总实收 = 产品总实收
     *        订单总实收 = 收款金额汇总 - 本次修改退款总金额
     *        订单总尾款 = 产品总尾款
     * @param erpClientForCommitOrderDTO
     * @return
     */
    private BigDecimal orderCalibration(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        BigDecimal orderSumPrice = erpClientForCommitOrderDTO.getSumPrice();
        BigDecimal orderDiscountPrice = erpClientForCommitOrderDTO.getDiscountAmount();
        BigDecimal orderTotalPrice = erpClientForCommitOrderDTO.getTotalPrice();
        BigDecimal orderPayPrice = erpClientForCommitOrderDTO.getPayPrice();
        BigDecimal orderLastPrice = erpClientForCommitOrderDTO.getLastPrice();
        BigDecimal orderRefundPrice = erpClientForCommitOrderDTO.getRefundPrice();

        // 提取服务单内参数，用于订单总金额校验。
        BigDecimal serviceSumPrice = new BigDecimal("0");
        BigDecimal serviceDiscountPrice = new BigDecimal("0");
        BigDecimal serviceTotalPrice = new BigDecimal("0");
        BigDecimal servicePayPrice = new BigDecimal("0");
        BigDecimal serviceLastPrice = new BigDecimal("0");
        BigDecimal serviceDeprecatedPayPrice = new BigDecimal("0");
        BigDecimal servicePaymentPrice = new BigDecimal("0");
        BigDecimal serviceRefundPrice = new BigDecimal("0");
//        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getOrderId())) {
//            dbSummaryRefundPrice = commitOrderMapper.getSummaryRefundPriceByOrderId(erpClientForCommitOrderDTO.getOrderId());
//        }

        for (ErpProductForCommitOrderDTO product : erpClientForCommitOrderDTO.getProducts()) {
            //todo wy
            if (product.getIsDeprecated() == 0) {
                // 服务单内总价。
                serviceSumPrice = serviceSumPrice.add(product.getSumPrice().add(product.getChannelFee()));
                // 服务单内优惠总金额。产品优惠券/组合优惠金额 + 产品手动优惠申请 + 产品尾款优惠汇总。
                serviceDiscountPrice = serviceDiscountPrice.add(product.getCouponPrice()).add(product.getProductPreferential().add(product.getRetainageDiscountPrice()));
                // 服务单应收。
                serviceTotalPrice = serviceTotalPrice.add(product.getTotalPrice());
                // 服务单实收。
                servicePayPrice = servicePayPrice.add(product.getPayPrice());
                // 服务单尾款。
                serviceLastPrice = serviceLastPrice.add(product.getLastPrice());
                //退款金额
                serviceRefundPrice = serviceRefundPrice.add(product.getRefundPrice());
            }

            if (product.getIsDeprecated() == 2) {
                serviceDeprecatedPayPrice = serviceDeprecatedPayPrice.add(product.getPayPrice());
            }
            servicePaymentPrice = servicePaymentPrice.add(product.getPayPrice());
        }

//        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getPaymentTerm())) {
//            for (ErpPaymentTermInfoForCommitOrderDTO payment : erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos()) {
//                for (ErpPaymentTermDetailForCommitOrderDTO paymentDetail : payment.getPaymentDetails()) {
//                    servicePaymentPrice = servicePaymentPrice.add(paymentDetail.getMoney());
//                }
//            }
//        }
        BigDecimal money = new BigDecimal(0);
        if (ObjectUtil.isNotNull(erpClientForCommitOrderDTO.getPaymentTerm())) {
            for (ErpPaymentTermInfoForCommitOrderDTO payment : erpClientForCommitOrderDTO.getPaymentTerm().getPaymentTermInfos()) {
                for (ErpPaymentTermDetailForCommitOrderDTO paymentDetail : payment.getPaymentDetails()) {
                    money = money.add(paymentDetail.getMoney());
                }
            }
            //尾款
//            if (ObjectUtil.isNotEmpty(erpClientForCommitOrderDTO.getOrderId())) {
//                ErpRetainageReturn erpRetainageReturn = new ErpRetainageReturn();
//                erpRetainageReturn.setNumOrderId(erpClientForCommitOrderDTO.getOrderId());
//                List<ErpRetainageReturn> erpRetainageReturns = erpRetainageReturnMapper.selectErpRetainageReturnList(erpRetainageReturn);
//                if (CollUtil.isNotEmpty(erpRetainageReturns)) {
//                    //找出审核通过的
//                    List<ErpRetainageReturn> returns = erpRetainageReturns.stream().filter(en -> 1 == en.getNumStatus()).collect(Collectors.toList());
//                    if (CollUtil.isNotEmpty(returns)) {
//                        for (ErpRetainageReturn retainageReturn : returns) {
//                            money = money.add(retainageReturn.getNumCollectionPrice()).subtract(retainageReturn.getNumDiscounts());
//                        }
//                    }
//                }
//            }

            if (money.compareTo(erpClientForCommitOrderDTO.getPayPrice()) != 0) {
                throw new ServiceException("实收金额与付款金额不符");
            }
        }
        // 订单总金额 = 产品总金额
        if (orderSumPrice.compareTo(serviceSumPrice) != 0) {
            throw new ServiceException("订单总金额 = 产品总金额");
        }
        // 订单总优惠 = 产品总优惠
        if (orderDiscountPrice.compareTo(serviceDiscountPrice) != 0) {
            throw new ServiceException("订单总优惠 = 产品总优惠");
        }
        // 订单总应收 = 产品总应收
        if (orderTotalPrice.compareTo(serviceTotalPrice) != 0) {
            throw new ServiceException("订单总应收 = 产品总应收");
        }
        // 订单总实收 = 产品总实收
        if (orderPayPrice.compareTo(servicePayPrice) != 0) {
            throw new ServiceException("订单总实收 = 产品总实收");
        }
        // 订单总退款 = 产品总退款
        if (orderRefundPrice.compareTo(serviceRefundPrice) != 0) {
            throw new ServiceException("订单总退款 = 产品总退款");
        }

        // 订单总尾款 = 产品总尾款
        if (orderLastPrice.compareTo(serviceLastPrice) != 0) {
            throw new ServiceException("订单总尾款 = 产品总尾款");
        }
        return orderTotalPrice;
    }


    /**
     * 校验螳螂系统订单总金额是否与CRM系统相等
     *
     * @param clueId
     * @param erpClientForCommitOrderDTO
     */
    private void checkoutTLTotalPrice(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        if (Objects.nonNull(erpClientForCommitOrderDTO.getClueId())) {
            Long clueId = erpClientForCommitOrderDTO.getClueId();
            TlClue tlClue = new TlClue();
            tlClue.setClueId(clueId);
            List<TlClue> tlClues = erpTLOrderMapper.selectTlClueList(tlClue);
            if (CollectionUtils.isNotEmpty(tlClues)) {
                TlClue clue = tlClues.get(0);
                String responseJson = clue.getResponseJson();
                JSONObject jsonObject = JSONObject.parseObject(responseJson);
                //应缴总金额
                BigDecimal totalAmount = jsonObject.getBigDecimal("totalAmount");
                //订单总金额
                BigDecimal totalPrice = erpClientForCommitOrderDTO.getTotalPrice();
                if (Objects.nonNull(totalAmount) && Objects.nonNull(totalPrice) && (totalAmount.compareTo(totalPrice) != 0)) {
                    throw new ServiceException("订单总金额应与螳螂系统相同，为" + totalAmount);
                }
            }
        }
    }

    /**
     * 校验启照多提单
     *
     * @param erpClientForCommitOrderDTO
     */
    private void checkQiZhaoDuo(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        //校验为执照产品是否选择了执照编号
        if (CollectionUtils.isNotEmpty(erpClientForCommitOrderDTO.getProducts())) {
            List<ErpProductForCommitOrderDTO> products = erpClientForCommitOrderDTO.getProducts();
            List<Long> collect = products.stream().map(ErpProductForCommitOrderDTO::getProductId).collect(Collectors.toList());
            if (collect.contains(3413L) && erpClientForCommitOrderDTO.getOptionType() == 1) {
                if (StringUtils.isEmpty(erpClientForCommitOrderDTO.getLicenseNo())) {
                    throw new ServiceException("执照收转产品必须选择执照编号!");
                }
            }
        }
        //如果为执照类型的提单校验产品是否选择正确
        if (StringUtils.isNotEmpty(erpClientForCommitOrderDTO.getLicenseNo())) {
            ErpLicenseDTO erpLicense = new ErpLicenseDTO();
            erpLicense.setNumber(Integer.valueOf(erpClientForCommitOrderDTO.getLicenseNo()));
            List<ErpLicense> erpLicenses = erpLicenseMapper.selectErpLicenseList(erpLicense);
            if (CollectionUtils.isEmpty(erpLicenses)) {
                throw new ServiceException("执照编号不存在！");
            }
            ErpLicense license = erpLicenses.get(0);

            if (1 != license.getStatus()) {
                throw new ServiceException("所选执照状态非待售，无法提单");
            }
            List<ErpLicenseProduct> productBylicenseNo = erpLicenseProductMapper.getErpLicenseProductBylicenseNo(erpClientForCommitOrderDTO.getLicenseNo());
            productBylicenseNo.remove(null);
            List<LinceseProductVo> linceseProductVos = BeanUtil.copyToList(productBylicenseNo, LinceseProductVo.class);
            //添加执照产品

            LinceseProductVo linceseProductVoZZ = new LinceseProductVo();
            linceseProductVoZZ.setProductId(3413L);
            linceseProductVoZZ.setProductPrice(license.getLicensePrice());
            linceseProductVos.add(linceseProductVoZZ);

            if (CollectionUtils.isNotEmpty(linceseProductVos)) {
                Boolean result = checkLincenseProducts(linceseProductVos, erpClientForCommitOrderDTO.getProducts(), license);
                if (result) {
                    log.info("启照多产品校验通过");
                }
            }
        }
    }

    /**
     * 作废螳螂订单
     */
    private void cancelledTLOrder(Long orderId) {
        Date date = new Date();
        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
        if (Objects.nonNull(orders) && Objects.nonNull(orders.getNumClueId())) {
            TlClue tlClue = new TlClue();
            tlClue.setClueId(orders.getNumClueId());
            List<TlClue> tlClues = erpTLOrderMapper.selectTlClueList(tlClue);
            if (CollectionUtils.isNotEmpty(tlClues)) {
                TlClue clue = tlClues.get(0);
                Long mtsOrderId = clue.getMtsOrderId();

                net.sf.json.JSONObject jsonObjectReq = new net.sf.json.JSONObject();
                jsonObjectReq.put("orderId", String.valueOf(mtsOrderId));
                jsonObjectReq.put("companyId", 6021);
                jsonObjectReq.put("time", date.getTime());
                jsonObjectReq.put("token", MD5Util.md5(tlOpenApiPropertiesConfig.getSecret() + date.getTime()));
                jsonObjectReq.put("updaterId", SecurityUtils.getUserId());
                String json = HttpClientUtil.doPostJson(TLhcUrl + "/syncApi/invalidOrder", jsonObjectReq);
                JSONObject jsonObject = JSONObject.parseObject(json);
                Boolean data = jsonObject.getBoolean("data");
                if (!data) {
                    throw new ServiceException("作废螳螂系统订单失败");
                }
            }
        }
    }

    /**
     * 根据订单操作类型校验优惠券
     *
     * @param erpClientForCommitOrderDTO
     * @param finalOrderStatusVO
     */
    private void isCouponUse(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, ErpOrderStatusVO finalOrderStatusVO) {
        if (1 == erpClientForCommitOrderDTO.getOptionType()) {
            throw new ServiceException("优惠券/折扣券已使用");
        }
        if (2 == erpClientForCommitOrderDTO.getOptionType()) {
            if ((OrderApprovalStatusEnum.MANAGER_NOT.getStatusType().equals(finalOrderStatusVO.getModifyOrderExamineStatusType())) ||
                    (OrderApprovalStatusEnum.YWDJR_NOT.getStatusType().equals(finalOrderStatusVO.getModifyOrderExamineStatusType())) ||
                    (OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType().equals(finalOrderStatusVO.getModifyOrderExamineStatusType()))) {
                throw new ServiceException("订单修改审核当前流程：" + finalOrderStatusVO.getModifyOrderExamineStatusName() + "，不可修改");
            }
        }
        if (3 == erpClientForCommitOrderDTO.getOptionType()) {
            if (!new Integer(1).equals(erpClientForCommitOrderDTO.getEditOrderUploadScreenshot())) {
                throw new ServiceException("优惠券/折扣券已使用");
            }
        }
    }

    /**
     * 提单完成，更新执照企业状态
     *
     * @param licenseNumber
     * @param status
     * @return
     */
    private int updateStatusByNumber(String licenseNumber, String status) {
        net.sf.json.JSONObject jsonObject = new net.sf.json.JSONObject();
        jsonObject.put("license_number", licenseNumber);
        jsonObject.put("status", status);
        String json = HttpClientUtil.doPostJson(url + "java_api/license_transfer/statusByNumber", jsonObject);
        if (StringUtils.isEmpty(json)) {
            return 0;
        }
        if (JSONObject.parseObject(json).getInteger("data") <= 0) {
            return 0;
        }
        return 1;
    }


    /**
     * 客保管理提单维护erpClient
     *
     * @param erpClientForCommitOrderDTO
     */
    private ErpClient addErpClient(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO) {
        ErpClient erpClient = new ErpClient();
        ErpClient erpClientAdd = new ErpClient();
        erpClient.setContactPhone(erpClientForCommitOrderDTO.getPhone());
        if (Objects.nonNull(erpClientForCommitOrderDTO.getClientType())) {
            switch (erpClientForCommitOrderDTO.getClientType()) {
                case 1:
                    erpClient.setNumEnterpriseId(erpClientForCommitOrderDTO.getNumEnterpriseId());
                    erpClientAdd.setNumEnterpriseId(erpClientForCommitOrderDTO.getNumEnterpriseId());
                    break;
                case 2:
                    erpClient.setNumPersonalId(erpClientForCommitOrderDTO.getNumPersonalId());
                    erpClientAdd.setNumPersonalId(erpClientForCommitOrderDTO.getNumPersonalId());
                    break;
                default:
                    break;
            }
        }
//        R<List<BdClueContacts>> r = remoteCustomerService.getBdClueContactByClueId(erpClientForCommitOrderDTO.getClueId());
//        if (r.getCode() == 200 && r.getData().size() > 0) {
//            erpClientAdd.setContactName(r.getData().get(0).getVcName());
//        }
        R<BdClue> r = remoteCustomerService.getBdClueById(erpClientForCommitOrderDTO.getClueId());
        if (r.getCode() == 200 && ObjectUtil.isNotNull(r.getData())) {
            erpClientAdd.setContactName(r.getData().getVcCustomerName());
        }
        List<ErpClient> erpClients = erpClientMapper.selectErpClientList(erpClient);
        if (CollectionUtils.isEmpty(erpClients) || erpClients.size() <= 0) {
            erpClientAdd.setNumType(erpClientForCommitOrderDTO.getClientType());
            erpClientAdd.setNumClueId(erpClientForCommitOrderDTO.getClueId());
            erpClientAdd.setContactPhone(erpClientForCommitOrderDTO.getPhone());
            erpClientAdd.setNumStatus(1);
            erpClientAdd.setNumCityId(erpClientForCommitOrderDTO.getClientCity());
            erpClientAdd.setNumCreatedBy(SecurityUtils.getUserId());
            erpClientAdd.setDatSigningDatecreatedTime(new Date());
            erpClientAdd.setNumUpdatedBy(SecurityUtils.getUserId());
            erpClientAdd.setDatSigningDateupdatedTime(new Date());
            erpClientMapper.insertErpClient(erpClientAdd);
            return erpClientAdd;
        } else {
            return erpClients.get(0);
        }
    }

    /**
     * 校验产品合理性。
     *
     * @param productForConfirmOrderVOList 待校验产品。
     * @param clientCity                   客户城市。
     * <AUTHOR>
     * @since 2022-03-22 15:47:12
     */
    public void checkProductRationality(List<ErpProductForConfirmOrderVO> productForConfirmOrderVOList, String clientCity) {
        productForConfirmOrderVOList.forEach(product -> {
            if (ObjectUtil.isNull(product.getIsDeprecated()) || product.getIsDeprecated() == 0) {
                // 校验产品适用城市区域是否与企业一致。
                if (StringUtil.isNotBlank(product.getBelongCity())) {
                    if (Math.toIntExact(Arrays.stream(product.getBelongCity().split(",")).filter(region -> region.equals(clientCity)).count()) == 0) {
                        log.error("product.getBelongCity()::{}", JSONObject.toJSONString(product.getBelongCity()));
                        log.error("clientCity::{}", JSONObject.toJSONString(clientCity));
                        throw new ServiceException("您所选产品城市/区域与企业城市不一致，请重新选择！", 501);
                    }
                }
                // 校验产品纳税类型是否与企业纳税类型一致。
                if (StringUtil.isNotBlank(product.getTaxIds())) {
                /*if (Math.toIntExact(Arrays.stream(product.getTaxIds().split(",")).filter(region -> region.equals(String.valueOf(confirmOrderDTO.getClientTax()))).count()) == 0) {
                    throw new ServiceException("您所选产品纳税类型与企业纳税类型不一致，请重新选择！", 501);
                }*/
                }
                // 校验产品是否已下架。
                if (product.getIsUp() == 0) {
                    throw new ServiceException("您所选产品已下架，请重新选择", 501);
                }

            }
        });
    }

    /**
     * 优惠券信息填充。
     *
     * @param products      产品信息。
     * @param productSource 待确认订单产品集合。
     * @param source        调用来源：0PC，1小程序。
     * <AUTHOR>
     * @since 2022-03-22 15:17:18
     */
    public void couponInfoFill(List<ErpProductForConfirmOrderVO> products, List<ErpProductForConfirmOrderDTO> productSource,
                               Integer source, Long clueId, Long clientId, Integer commitOrderType, Long interfaceType, String phone) {
        for (ErpProductForConfirmOrderVO product : products) {
            try {
                // 设置产品购买数量。
                product.setCount(productSource.stream().filter(e -> e.getProductId().equals(product.getProductId())).collect(Collectors.toList()).get(0).getProductCount());

                // 设置产品总价。
                BigDecimal unitPrice = BigDecimal.ZERO;
                if (Objects.nonNull(product.getUnitPrice())) {
                    unitPrice = product.getUnitPrice();
                }
                product.setSumPrice(unitPrice.multiply(new BigDecimal(product.getCount())));
                product.setCouponId(null);
                product.setDiscountAmount(null);
                // 查询并填充优惠券.
                if (ObjectUtil.isNull(product.getIsDeprecated()) || product.getIsDeprecated() == 0) {
                    // 获取产品优惠券并填充。
                    // 查询当前产品的可用优惠券并填充至返回信息。
                    LoginUser loginUser = tokenService.getLoginUser();
                    SysUser currentUser = loginUser.getSysUser();
                    if (source == 0) {
                        clueOrClientCommitOrder(clueId, clientId, commitOrderType, interfaceType, product, currentUser);
                    } else if (source == 1) {
                        //小程序优惠券，先放入crm的优惠券
                        List<ErpDiscountCouponOrderVo> list = clueOrClientCommitOrder(clueId, clientId, commitOrderType, interfaceType, product, currentUser);
                        List<ErpDiscountCouponOrderVo> couponOrderVos = new ArrayList<>(list);
                        //再放入小程序优惠券
                        List<ErpDiscountCouponOrderVo> xcxList = xcxCommitOrder(interfaceType, phone, product);
                        couponOrderVos.addAll(xcxList);
                        product.setErpDiscountCoupons(couponOrderVos);
                    }
                }
            } catch (Exception e) {
                log.error("订单列表获取产品信息异常:", e);
            }
        }
    }

    private List<ErpDiscountCouponOrderVo> clueOrClientCommitOrder(Long clueId, Long clientId, Integer commitOrderType, Long interfaceType, ErpProductForConfirmOrderVO product, SysUser currentUser) {
        if (commitOrderType == 1) {
            return clueCommitOrder(clueId, interfaceType, product, currentUser);
        } else {
            return clientCommitOrder(clientId, interfaceType, product, currentUser);
        }
    }

    private List<ErpDiscountCouponOrderVo> xcxCommitOrder(Long interfaceType, String phone, ErpProductForConfirmOrderVO product) {
        List<ErpDiscountCouponOrderVo> list = new ArrayList<>();
        ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
        erpDiscountCoupon.setInterfaceType(0L);
        erpDiscountCoupon.setXcxPhone(phone);
        if (Objects.nonNull(interfaceType) && 1L == interfaceType) {
            erpDiscountCoupon.setInterfaceType(1L);
        }
        List<ErpDiscountCoupon> erpDiscountCoupons = erpDiscountCouponMapper.selectDiscountCouponList(erpDiscountCoupon);

        if (CollectionUtils.isNotEmpty(erpDiscountCoupons)) {
            erpDiscountCoupons.forEach(val -> {
                ErpDiscountCouponOrderVo discountCouponOrderVo = new ErpDiscountCouponOrderVo();
                BeanUtils.copyProperties(val, discountCouponOrderVo);
                discountCouponOrderVo.setName("优惠券(" + discountCouponOrderVo.getDiscountAmount() + ")");
                list.add(discountCouponOrderVo);
            });
            if (ObjectUtil.isNotEmpty(product.getCouponPrice())
                    && product.getCouponPrice().compareTo(BigDecimal.ZERO) == 0){
                product.setCouponPrice(erpDiscountCoupons.get(0).getDiscountAmount());
            }
        }
        product.setErpDiscountCoupons(list);
        return list;
    }

    private List<ErpDiscountCouponOrderVo> clientCommitOrder(Long clientId, Long interfaceType, ErpProductForConfirmOrderVO product, SysUser currentUser) {
        List<ErpDiscountCouponOrderVo> list = new ArrayList<>();
        if (Objects.nonNull(clientId)) {
            ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
            erpDiscountCoupon.setClientId(clientId);
            erpDiscountCoupon.setBelongUserId(currentUser.getUserId());
            erpDiscountCoupon.setNumProductId(product.getProductId());
            erpDiscountCoupon.setNumType(1);
            erpDiscountCoupon.setInterfaceType(0L);
            if (Objects.nonNull(interfaceType) && 1L == interfaceType) {
                erpDiscountCoupon.setInterfaceType(1L);
            }
            List<ErpDiscountCoupon> erpDiscountCoupons = erpDiscountCouponMapper.selectDiscountCouponList(erpDiscountCoupon);

            if (CollectionUtils.isNotEmpty(erpDiscountCoupons)) {
                erpDiscountCoupons.forEach(val -> {
                    ErpDiscountCouponOrderVo discountCouponOrderVo = new ErpDiscountCouponOrderVo();
                    BeanUtils.copyProperties(val, discountCouponOrderVo);
                    discountCouponOrderVo.setName("优惠券(" + discountCouponOrderVo.getDiscountAmount() + ")");
                    list.add(discountCouponOrderVo);
                });
//                                    product.setCouponId(erpDiscountCoupons.get(0).getId());
                product.setCouponPrice(erpDiscountCoupons.get(0).getDiscountAmount());
            }

            erpDiscountCoupon = new ErpDiscountCoupon();
            erpDiscountCoupon.setClientId(clientId);
            erpDiscountCoupon.setInterfaceType(0L);
            if (Objects.nonNull(interfaceType) && 1L == interfaceType) {
                erpDiscountCoupon.setInterfaceType(1L);
            }
            erpDiscountCoupon.setNumType(2);
            erpDiscountCoupons = erpDiscountCouponMapper.selectDiscountCouponList(erpDiscountCoupon);

            if ((CollectionUtils.isNotEmpty(erpDiscountCoupons))) {
                erpDiscountCoupons.forEach(val -> {
                    ErpDiscountCouponOrderVo discountCouponOrderVo = new ErpDiscountCouponOrderVo();
                    BeanUtils.copyProperties(val, discountCouponOrderVo);
                    discountCouponOrderVo.setName("抵扣券(" + discountCouponOrderVo.getDiscountAmount() + ")");
                    list.add(discountCouponOrderVo);
                });
            }
            product.setErpDiscountCoupons(list);
        }
        return list;
    }

    private List<ErpDiscountCouponOrderVo> clueCommitOrder(Long clueId, Long interfaceType, ErpProductForConfirmOrderVO product, SysUser currentUser) {
        List<ErpDiscountCouponOrderVo> list = new ArrayList<>();
        if (Objects.nonNull(clueId)) {
            // 获取当前产品和线索对应的优惠券
            ErpDiscountCoupon erpDiscountCoupon = new ErpDiscountCoupon();
            erpDiscountCoupon.setClueId(clueId);
            erpDiscountCoupon.setBelongUserId(currentUser.getUserId());
            erpDiscountCoupon.setNumProductId(product.getProductId());
            erpDiscountCoupon.setNumType(1);
            erpDiscountCoupon.setInterfaceType(0L);
            if (Objects.nonNull(interfaceType) && 1L == interfaceType) {
                erpDiscountCoupon.setInterfaceType(1L);
            }
            List<ErpDiscountCoupon> erpDiscountCoupons = erpDiscountCouponMapper.selectDiscountCouponList(erpDiscountCoupon);
            if (CollectionUtils.isNotEmpty(erpDiscountCoupons)) {
                erpDiscountCoupons.forEach(val -> {
                    ErpDiscountCouponOrderVo discountCouponOrderVo = new ErpDiscountCouponOrderVo();
                    BeanUtils.copyProperties(val, discountCouponOrderVo);
                    discountCouponOrderVo.setName("优惠券(" + discountCouponOrderVo.getDiscountAmount() + ")");
                    list.add(discountCouponOrderVo);
                });
                product.setCouponPrice(erpDiscountCoupons.get(0).getDiscountAmount());
            }
            product.setErpDiscountCoupons(list);
        }
        return list;
    }

    /**
     * 组合信息填充。
     *
     * @param products      产品信息。
     * @param productSource 待确认订单产品集合。
     * <AUTHOR>
     * @since 2022-03-22 15:24:40
     */
    public void combinedInfoFill(List<ErpProductForConfirmOrderVO> products, List<ErpProductForConfirmOrderDTO> productSource) {
        // 获取符合指定产品的组合优惠（适用渠道、活动类型？、开始使用时间，结束使用时间）。
        // 构建待组合产品标识集合。
        List<Long> productIdsForCombination = new ArrayList<>();
        // 提取产品标识（数量不为1的产品需提取多个标识）。
        productSource.forEach(product -> {
            if (ObjectUtil.isNull(product.getIsDeprecated()) || product.getIsDeprecated() == 0) {
                for (int i = 1; i <= product.getProductCount(); i++) {
                    productIdsForCombination.add(product.getProductId());
                }
            }
        });

        // 构建排列组合对象。
        List<List<Long>> resList = new ArrayList<>();
        // 获取所有排列组合。
        recursive(productIdsForCombination.stream().sorted().collect(Collectors.toList()), resList, 0, "");
        // 处理排列组合，生成排列对象集合。
        for (List<Long> combinationList : resList.stream().distinct().collect(Collectors.toList())) {
            ErpCombinedForConfirmOrderVO combinationByIdList = commitOrderMapper.getCombinationByIdList(JSONObject.toJSONString(combinationList));
            if (ObjectUtil.isNotEmpty(combinationByIdList)) {
                // 获取该组合最多可匹配到的份数。
                List<Long> handleProductIds = new ArrayList<>(productIdsForCombination);
                Integer combinationCount = repeatCombined(0, combinationList, handleProductIds);
                // 如果最多匹配分数不为0，则填充组合优惠信息到返回结果内。
                if (combinationCount != 0) {
                    // 填充组合优惠信息，单价*份数+组合外产品单价+份数。
                    products.forEach(productInfo -> {
                        combinationByIdList.getProductInfo().forEach(combinedProduct -> {
                            if (productInfo.getProductId().equals(combinedProduct.getProductId())) {
                                // 产品总数。
//                                Integer totalCount = productInfo.getCount();
                                // 组合内产品总数。
                                Integer combinationProductCount = combinationCount * combinedProduct.getDiscountCount();
                                // 剩余产品数量。
//                                int residueCount = totalCount - combinationProductCount;
                                // 构建待返回的产品组合优惠对象。
                                ErpCombinedForProductVO erpCombinedForProductVO = new ErpCombinedForProductVO();
                                erpCombinedForProductVO.setCombinedId(combinationByIdList.getCombinationId());
                                erpCombinedForProductVO.setCombinedName(combinationByIdList.getCombinationName());
                                erpCombinedForProductVO.setProductType(combinedProduct.getProductType());
                                erpCombinedForProductVO.setCombinationProductCount(combinationProductCount);
//                                erpCombinedForProductVO.setResidueCount(residueCount);
                                erpCombinedForProductVO.setCombinedUnitPrice(combinedProduct.getDiscountedPrice());
//                                erpCombinedForProductVO.setPayablePrice((combinedProduct.getDiscountedPrice().multiply(new BigDecimal(combinationProductCount))).add(productInfo.getUnitPrice().multiply(new BigDecimal(residueCount))));
//                                erpCombinedForProductVO.setDiscountPrice(new BigDecimal(combinationProductCount).multiply(productInfo.getUnitPrice().subtract(combinedProduct.getDiscountedPrice())));
                                erpCombinedForProductVO.setCombinedProductIds(combinationByIdList.getProductInfo().stream().map(ele -> {
                                    return ele.getProductId() + "";
                                }).collect(Collectors.toList()));
                                if (ObjectUtil.isNull(productInfo.getCombinations())) {
                                    productInfo.setCombinations(new ArrayList<>());
                                }
                                productInfo.getCombinations().add(erpCombinedForProductVO);
                            }
                        });
                    });
                }

            }

        }
    }

    /**
     * 获取所选产品的全部排列组合。
     *
     * @param productIdList 待组合的产品标识集合。
     * @param resList       组合结果。
     * @param num           递归参数，调用时传null或0。
     * @param beforeStr     递归参数，调用时传null或""。
     * <AUTHOR>
     * @since 2022-03-16 15:43:57
     */
    public static void recursive(List<Long> productIdList, List<List<Long>> resList, Integer num, String beforeStr) {
        num = ObjectUtil.isNull(num) ? 0 : num;
        beforeStr = ObjectUtil.isNull(beforeStr) ? "" : beforeStr;
        for (int i = num; i < productIdList.size(); i++) {
            String beforeStrForIn = "";
            if (!"".equals(beforeStr)) {
                List<Long> handleList = Arrays.stream(beforeStr.split(",")).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
                handleList.add(productIdList.get(i));
                resList.add(handleList);
                beforeStrForIn = beforeStr + "," + productIdList.get(i).toString();
            } else {
                beforeStrForIn = productIdList.get(i).toString();
            }
            num++;
            if (num < productIdList.size()) {
                recursive(productIdList, resList, num, beforeStrForIn);
            }
        }
    }

    /**
     * 递归获取某个组合一共可重复份数。
     *
     * @param num              计数。
     * @param combinationList  排列内的产品标识。
     * @param handleProductIds 处理过程。
     * <AUTHOR>
     * @since 2022-03-21 09:51:24
     */
    public static Integer repeatCombined(Integer num, List<Long> combinationList, List<Long> handleProductIds) {
        for (Long productId : combinationList) {
            if (handleProductIds.stream().noneMatch(e -> e.equals(productId))) {
                return num;
            }
            if (!handleProductIds.remove(productId)) {
                log.error("删除标识失败");
                return num;
            }
        }
        num++;
        return repeatCombined(num, combinationList, handleProductIds);
    }


    @Override
    public List<ErpCombinedActivity> listActivityName(ErpCombinedActivity erpCombinedActivity) {
        if (ObjectUtil.isNotEmpty(erpCombinedActivity.getDatStartTime())) {
            erpCombinedActivity.setDatStartTimeString(DateUtil.format(erpCombinedActivity.getDatStartTime(), DateFormatConstants.TIME_FORMAT_BEGIN));
        }
        if (ObjectUtil.isNotEmpty(erpCombinedActivity.getDatEndTime())) {
            erpCombinedActivity.setDatEndTimeString(DateUtil.format(erpCombinedActivity.getDatStartTime(), DateFormatConstants.TIME_FORMAT_END));
        }
        LoginUser loginUser = tokenService.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        Long userId = sysUser.getUserId();

        if (!userId.equals(1L)) {
            // 获取当前的用户
            //查询配置信息
            erpCombinedActivity.setDeptId(sysUser.getDeptId());

        }
        List<ErpCombinedActivity> combinationActivity = commitOrderMapper.getCombinationActivity(erpCombinedActivity);

        List<ErpCombinedActivity> erpCombinedActivityList = new ArrayList<>();
        Map<Long, List<ErpCombinedActivity>> collect = combinationActivity.stream().collect(Collectors.groupingBy(ErpCombinedActivity::getId));

        for (Map.Entry<Long, List<ErpCombinedActivity>> entry : collect.entrySet()) {
            ErpCombinedActivity activity = new ErpCombinedActivity();
            activity.setId(entry.getKey());
            activity.setVcName(entry.getValue().get(0).getVcName());
            activity.setValiTime(entry.getValue().get(0).getValiTime());
            activity.setNumAreaName(entry.getValue().get(0).getNumAreaName());
            List<CombunedActivityDto> productList = new ArrayList<>();
            for (ErpCombinedActivity value : entry.getValue()) {
                CombunedActivityDto combunedActivityDto = new CombunedActivityDto();
                combunedActivityDto.setNumProductId(value.getNumProductId());
                combunedActivityDto.setVcProductName(value.getVcProductName());
                combunedActivityDto.setVcClassificationName(value.getVcClassificationName());
                combunedActivityDto.setNumPrice(value.getNumPrice());
                combunedActivityDto.setNumActivityPrice(value.getNumActivityPrice());
                combunedActivityDto.setNumProductCount(value.getNumProductCount());
                combunedActivityDto.setAllPrice(value.getAllPrice());
                productList.add(combunedActivityDto);
            }
            activity.setErpCombinedActivityProducts(productList);
            erpCombinedActivityList.add(activity);
        }
        return erpCombinedActivityList;
    }


    /**
     * 提交订单。
     *
     * @param erpClientForCommitOrderDTO 待提交订单信息。
     * @return 返回提交结果。
     * <AUTHOR>
     * @since 2022-03-22 13:22:22
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> commitOrderFromXcx(XcxCommitOrderDto dto, HttpServletResponse response) {
        // TODO 编辑订单时，将订单内产品全部清除再重新添加。
        // TODO 修改产品时不直接修改到数据库内，需等修改审核通过后统一修改原数据。
        Map<String, Object> resMap = new HashMap<>(3);
        resMap.put("code", "success");
        resMap.put("message", "创建成功");
        resMap.put("orderId", "");
        if (ObjectUtil.isEmpty(dto.getSource()) || dto.getSource() != 1) {
            resMap.put("code", "error");
            resMap.put("message", "来源不可为空");
            return resMap;
        }
        if (ObjectUtil.isEmpty(dto.getPhone())) {
            resMap.put("code", "error");
            resMap.put("message", "手机号不可同时为空");
            return resMap;
        }
        if (ObjectUtil.isEmpty(dto.getProductId()) || ObjectUtil.isEmpty(dto.getProductCount())) {
            resMap.put("code", "error");
            resMap.put("message", "产品Id/数量不可为空");
            return resMap;
        }
        if (ObjectUtil.isEmpty(dto.getTotalPrice())
                || ObjectUtil.isEmpty(dto.getPayPrice())
                || ObjectUtil.isEmpty(dto.getLastPrice())) {

            resMap.put("code", "error");
            resMap.put("message", "订单总金额，实际付款金额，尾款不可为空");
            return resMap;
        }
        if (ObjectUtil.isEmpty(dto.getNumAreaId())
                || ObjectUtil.isEmpty(dto.getNumTaxId())) {

            resMap.put("code", "error");
            resMap.put("message", "地区/纳税类型不可为空");
            return resMap;
        }
        if (ObjectUtil.isEmpty(dto.getClientId()) &&
                (ObjectUtil.isEmpty(dto.getOpenId()) || ObjectUtil.isEmpty(dto.getAppId()))) {

            resMap.put("code", "error");
            resMap.put("message", "clientId为空时，openId,appId不可为空");
            return resMap;
        }

        if (ObjectUtil.isEmpty(dto.getOpenId())) {
            resMap.put("code", "error");
            resMap.put("message", "openId不可为空");
            return resMap;
        }

        if (ObjectUtil.isEmpty(dto.getEnterpriseDominant())) {
            dto.setEnterpriseDominant(1L);
        }

//        BigDecimal sumPrice = dto.getSumPrice();
        BigDecimal totalPrice = dto.getTotalPrice();
        BigDecimal payPrice = dto.getPayPrice();
        BigDecimal lastPrice = dto.getLastPrice();
        BigDecimal couponPrice = dto.getCouponPrice();


        if (!totalPrice.equals(payPrice.add(lastPrice).add(couponPrice))) {
            resMap.put("code", "error");
            resMap.put("message", "订单总应付 != 实际支付 + 剩余尾款 + 优惠券金额");
            return resMap;
        }

        ErpProductConfiguration erpProductConfiguration = erpProductConfigurationMapper.selectByProductIdFromXcx(dto.getProductId(), dto.getEnterpriseDominant());
        if (ObjectUtil.isEmpty(erpProductConfiguration) || ObjectUtil.isEmpty(erpProductConfiguration.getProductPrice())) {
            resMap.put("code", "error");
            resMap.put("message", "产品有误");
            return resMap;
        }
        if (!totalPrice.equals(erpProductConfiguration.getProductPrice().multiply(new BigDecimal(dto.getProductCount())))) {
            resMap.put("code", "error");
            resMap.put("message", "订单总应付 != 产品价格 * 产品数量");
            return resMap;
        }

        //检验优惠券是否低于最低金额，是否在有效时间内
        //xcxActivityCouponLimitService
        //xcxCouponConfigService
        if (ObjectUtil.isNotEmpty(dto.getCouponId())){
            //查询优惠券信息
            ErpDiscountCoupon coupon = erpDiscountCouponMapper.selectErpDiscountCouponById(dto.getCouponId());
            if (ObjectUtil.isNotEmpty(coupon)){
                if (totalPrice.compareTo(coupon.getMinPrice()) < 0){
                    resMap.put("code", "error");
                    resMap.put("message", "金额小于此优惠券使用的最低金额");
                }
                if (CouponConstants.VALIDITY_PERIOD_ON.equals(coupon.getValidityPeriod())){
                    if (!LocalDateTime.now().isAfter(coupon.getActStartTime())
                            || !LocalDateTime.now().isBefore(coupon.getActEndTime())){
                        resMap.put("code", "error");
                        resMap.put("message", "优惠券不在使用范围内");
                    }
                }
            }
        }


        Long numUserIdXcx = 4086L;
        ErpClient erpClient = new ErpClient();
        if (ObjectUtil.isEmpty(dto.getClientId())) {
            ErpEnterprise erpEnterprise = new ErpEnterprise();
            erpEnterprise.setVcCompanyName("小程序" + dto.getPhone());
            erpEnterprise.setNumCorporatePropertyId(Long.parseLong(dto.getNumTaxId()));
            erpEnterpriseMapper.insertErpEnterprise(erpEnterprise);

            erpClient.setContactPhone(dto.getPhone());
            erpClient.setContactName("小程序" + dto.getPhone());
            erpClient.setNumStatus(0);
            erpClient.setCreateTime(new Date());
            erpClient.setNumType(1);
            erpClient.setNumCityId(dto.getCityId());
            erpClient.setNumEnterpriseId(erpEnterprise.getId());
            erpClientMapper.insertErpClient(erpClient);
//            erpClient.setId(Long.parseLong(clientId+""));

            WechatAppletBind wechatAppletBind = new WechatAppletBind();
            wechatAppletBind.setAppid(dto.getAppId());
            wechatAppletBind.setClientid(erpClient.getId());
            wechatAppletBind.setName("小程序" + dto.getPhone());
            wechatAppletBind.setPhone(dto.getPhone());
            wechatAppletBind.setOpenId(dto.getOpenId());
            wechatAppletBind.setIsDefault(1);
            wechatAppletBind.setCreatedAt(new Date());
            wechatAppletBindMapper.insertWechatAppletBind(wechatAppletBind);
        } else {
            erpClient = erpClientMapper.selectErpClientById(dto.getClientId());
        }

        //检验新客
        boolean flag = inspectNewCustomers(dto, erpClient);

        String kbName = "无";
        String accountName = "无";
        String gsSignName = "无";
        String shareUserName = "无";
        //标识起始分享人是不是销售
        boolean match = false;

        SysUser user = new SysUser();
        if (ObjectUtil.isNotEmpty(dto.getShareUserPhone())) {
            R<SysUser> info = remoteUserService.getUserByShareUserPhone(dto.getShareUserPhone(), SecurityConstants.INNER);
            if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getData())) {
                user = info.getData();
                shareUserName = user.getNickName();
                match = true;
            }
        }
        if (ObjectUtil.isNotEmpty(user.getUserId())) {
            numUserIdXcx = user.getUserId();
        } else {
            //客保
            Long kbUserId = 0L;
            //会计
            Long accountUserId = 0L;
            //推荐销售
            Long gsSignUserId = 0L;

            //根据手机号获取客保
            kbUserId = erpOrdersMapper.selectFollowUserIdByPhoneInKb(dto.getPhone(), 1);
            if (ObjectUtil.isNotEmpty(kbUserId)) {
                R<SysUser> info = remoteUserService.getUserInfoById(kbUserId, SecurityConstants.INNER);
                if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getData())) {
                    kbName = info.getData().getNickName();
                }
            }
            if (numUserIdXcx.intValue() == 4086 && ObjectUtil.isNotNull(kbUserId) && kbUserId.intValue() != 0) {
                numUserIdXcx = kbUserId;
            }


            //小程序提单的推荐码
            net.sf.json.JSONObject gsRemark = dto.getGsRemark();
            if (gsRemark.containsKey("gsSignCode") && ObjectUtil.isNotEmpty(gsRemark.get("gsSignCode"))) {
                Long gsSignCode = Long.parseLong(gsRemark.get("gsSignCode").toString());
                R<SysUser> info = remoteUserService.getUserInfoById(gsSignCode, SecurityConstants.INNER);
                if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getData())) {
                    gsSignUserId = info.getData().getUserId();
                    gsSignName = info.getData().getNickName();
                }
            }
            if (numUserIdXcx.intValue() == 4086 && ObjectUtil.isNotNull(gsSignUserId) && gsSignUserId.intValue() != 0) {
                numUserIdXcx = gsSignUserId;
            }


            //获取历史记账服务，更新订单的签约人，收款人为最新一条记录的会计
            if (ObjectUtil.isNotEmpty(erpClient.getNumEnterpriseId())) {
                ServiceByEnterpriseDto serviceByEnterpriseDto = ServiceByEnterpriseDto.builder()
                        .serviceType(Long.parseLong(ServiceMainConstants.JiZhangService+""))
                        .numEnterpriseId(erpClient.getNumEnterpriseId()).build();
                List<SServiceVo> listJZ = sServiceMainMapper.selectSServiceMainDZList(serviceByEnterpriseDto);

                if (listJZ.size() > 0) {
                    for (int i = 0; i < listJZ.size(); i++) {
                        if (ObjectUtil.isNotEmpty(listJZ.get(i).getAccountUserId()) && accountUserId.intValue() == 0) {
                            accountUserId = listJZ.get(i).getAccountUserId();
                            R<SysUser> info = remoteUserService.getUserInfoById(accountUserId, SecurityConstants.INNER);
                            accountName = info.getData().getNickName();
                        }
                    }
                }
            }
            //企业最新的负责会计
            if (numUserIdXcx.intValue() == 4086 && ObjectUtil.isNotNull(accountUserId) && accountUserId.intValue() != 0) {
                numUserIdXcx = accountUserId;
            }
        }

        ErpOrders erpOrders = new ErpOrders();
        // 构建订单操作记录对象，用以记录当前操作。
        ErpOrderOperatingRecord erpOrderOperatingRecord = new ErpOrderOperatingRecord();

        erpOrders.setCommitOrderType(2);
        erpOrders.setNumTotalPrice(dto.getTotalPrice());
        erpOrders.setNumPayPrice(dto.getPayPrice());
        erpOrders.setNumLastPrice(dto.getLastPrice());
        erpOrders.setNumRetainageStatus(ObjectUtil.isNotNull(erpOrders.getNumLastPrice())
                && erpOrders.getNumLastPrice().compareTo(new BigDecimal("0")) > 0 ?
                OrderLastStatusEnum.LAST_WITH.getStatusType() :
                OrderLastStatusEnum.LAST_WITHOUT.getStatusType());
        erpOrders.setNumDiscountAmount(dto.getCouponPrice());

        erpOrders.setAreaId(Long.parseLong(dto.getNumAreaId()));
        erpOrders.setCorporateProperty(Long.parseLong(dto.getNumTaxId()));
        erpOrders.setNumPaymentStatus(0);
        erpOrders.setNumClientId(erpClient.getId());

        String vcOrderNumber = DateUtil.format(new Date(), "yyyyMMdd") + erpClient.getId() + RandomUtil.randomNumbers(2);
        int count = erpOrdersMapper.selectCountByOrderNumber(vcOrderNumber);
        while (count > 0) {
            vcOrderNumber = DateUtil.format(new Date(), "yyyyMMdd") + erpClient.getId() + RandomUtil.randomNumbers(2);
            count = erpOrdersMapper.selectCountByOrderNumber(vcOrderNumber);
        }
        erpOrders.setVcOrderNumber(vcOrderNumber);
        erpOrders.setNumSource(dto.getSource());
        erpOrders.setOpenId(dto.getOpenId());
        erpOrders.setGsRemark(dto.getGsRemark().toString());
        erpOrders.setNumUserId(numUserIdXcx);
        erpOrders.setDatSigningDate(new Date());
//        erpOrders.setNumCreatedBy(userId);
        erpOrders.setDatSigningDatecreatedTime(new Date());
//        erpOrders.setNumCustomerType(dto.getClientType());
        erpOrders.setVcPhone(erpClient.getContactPhone());
        erpOrders.setIsElectronicContract(3);
//        if (ObjectUtil.isNotEmpty(dto.getContractId())) {
//            erpOrders.setNumContractId(dto.getContractId());
//            erpOrders.setContactNum(commitOrderMapper.getContractNum(dto.getContractId()));
//            erpOrders.setIsElectronicContract(1);
//        }

        // 将订单提单审核设置为：经理待审核。
        erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());

        // 设置除提单外的各个审核状态为默认状态。
        erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());
        erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());
        erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.DEFAULT_STATUS.getStatusType());

        // 订单状态修改为。
        erpOrders.setNumStatus(Long.valueOf(OrderStatusEnum.ORDER_NOT_AUDIT.getStatusType()));
        // 订单失效状态设置为：有效。
        erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_VALID.getStatusType());
        // 订单服务状态设置为：未开始。
        erpOrders.setNumBizStatus(OrderBizStatusEnum.BIZ_NO_START.getStatusType());
        // 设置收款方式为线上支付/线下支付。
        erpOrders.setNumPaymentTerm(1);

        // 填充订单操作记录信息，用以记录当前操作。
        erpOrderOperatingRecord.setNumOperationType(OrderOperationTypeEnum.CREATE_ORDER.getTypeInt());
        // 备注。
        erpOrders.setVcRemark(dto.getRemark());

        Long orderId = erpOrdersService.saveOrUpdate(erpOrders);

        //关联流水账单更新
        PaymentInfoVo paymentInfoVo = new PaymentInfoVo();
        paymentInfoVo.setClientId(String.valueOf(erpClient.getId()));
        List<PaymentInfoVo> paymentInfoVos = erpOrdersMapper.selectPaymentInfoList(paymentInfoVo);
        if (CollUtil.isNotEmpty(paymentInfoVos)){
            for (PaymentInfoVo infoVo : paymentInfoVos) {
                if (StringUtils.isEmpty(infoVo.getOrderId())) {
                    //日志
                    String remark = String.format(CommitOrderConstants.PAY_LOG_CONSTANT,
                            2,
                            1,
                            erpOrders.getVcOrderNumber(), orderId);

                    PaymentLog paymentLog = new PaymentLog(null, Integer.valueOf(infoVo.getId()),
                            CommitOrderConstants.OP_TYPE_ORDER_COMMIT, null, LocalDateTime.now(), remark);

                    //入库
                    commitOrderMapper.updateOrderBill(infoVo.getId(), String.valueOf(orderId), erpOrders.getVcOrderNumber());
                    paymentMapper.insertPaymentLog(paymentLog);
                }
            }
        }

        ErpServiceOrders erpServiceOrders = new ErpServiceOrders();
        erpServiceOrders.setNumOrderId(orderId);
        erpServiceOrders.setNumProductId(dto.getProductId());
        erpServiceOrders.setNumProductCount(dto.getProductCount());
//            erpServiceOrders.setNumProductInfoId(); // TODO 闲置字段，待删除。
        erpServiceOrders.setNumLastPrice(dto.getLastPrice());
        erpServiceOrders.setNumIsDeprecated(0);
        erpServiceOrders.setDatCreatedTime(new Date());
        // 2022-11-25     1) num_status（0删除，1正常）
        erpServiceOrders.setNumStatus(1);

        BigDecimal registPayFee = payPrice;
        Long registCouponId = 0L;


        Map<String, String> xcxCommitOrderProductMap = commitOrderMapper.selectXcxCommitOrderBindProductByProductId(dto.getProductId());
        if (ObjectUtil.isNotEmpty(xcxCommitOrderProductMap)
                && xcxCommitOrderProductMap.containsKey("commit_order_product")
                && ObjectUtil.isNotEmpty(xcxCommitOrderProductMap.get("commit_order_product"))) {

            List<String> xcxBindProductIdList = Arrays.asList(xcxCommitOrderProductMap.get("bindProduct").toString().split(","));
            List<String> xcxBindProducFeeList = Arrays.asList(xcxCommitOrderProductMap.get("fee").toString().split(","));
            List<String> xcxBindProducDiscountList = Arrays.asList(xcxCommitOrderProductMap.get("discount").toString().split(","));

            for (int i = 0; i < xcxBindProductIdList.size(); i++) {
                Long bindProductId = Long.parseLong(xcxBindProductIdList.get(i));
                BigDecimal bindProductFee = new BigDecimal(xcxBindProducFeeList.get(i));
                Integer bindProductDiscount = Integer.parseInt(xcxBindProducDiscountList.get(i));

                ErpServiceOrders erpServiceOrdersBind = new ErpServiceOrders();
                erpServiceOrdersBind.setNumOrderId(orderId);
                erpServiceOrdersBind.setNumProductId(bindProductId);
                erpServiceOrdersBind.setNumProductCount(1);
//            erpServiceOrdersBind.setNumProductInfoId(); // TODO 闲置字段，待删除。
                erpServiceOrdersBind.setNumTotalPrice(bindProductFee);
                erpServiceOrdersBind.setNumPayPrice(bindProductFee);
                erpServiceOrdersBind.setNumLastPrice(new BigDecimal("0"));

                if (bindProductDiscount == 1 && ObjectUtil.isNotEmpty(dto.getCouponId())) {
                    erpServiceOrdersBind.setNumCouponId(dto.getCouponId());
                    erpServiceOrdersBind.setNumCouponPrice(dto.getCouponPrice());

                    erpServiceOrdersBind.setNumPayPrice(bindProductFee.subtract(erpServiceOrdersBind.getNumCouponPrice()));
                    registCouponId = dto.getCouponId();
                }
                registPayFee = registPayFee.subtract(erpServiceOrdersBind.getNumPayPrice());

                erpServiceOrdersBind.setNumIsDeprecated(0);
                erpServiceOrdersBind.setDatCreatedTime(new Date());
                // 2022-11-25     1) num_status（0删除，1正常）
                erpServiceOrdersBind.setNumStatus(1);
                erpServiceOrdersService.saveOrUpdate(erpServiceOrdersBind);

                ErpProductDetailListDto erpProductDetailListDtoBind = new ErpProductDetailListDto();
                erpProductDetailListDtoBind.setNumProductId(bindProductId);
//                erpProductDetailListDtoBind.setConfigurationAppletShow(1);
                List<ErpProductDetailListVo> detailListBind = erpProductDetailMapper.selectErpProductList(erpProductDetailListDtoBind);
                if (detailListBind.size() > 0) {
                    ErpServiceOrdersInfo erpServiceOrdersInfo = new ErpServiceOrdersInfo();
                    erpServiceOrdersInfo.setNumServiceOrders(erpServiceOrdersBind.getId());
                    erpServiceOrdersInfo.setVcProductName(detailListBind.get(0).getVcProductName());
                    erpServiceOrdersInfo.setVcCorporateProperty(detailListBind.get(0).getVcTaxNames());
                    erpServiceOrdersInfo.setNumPrice(bindProductFee);
                    erpServiceOrdersInfo.setVcProductType(detailListBind.get(0).getServiceName());
                    erpServiceOrdersInfo.setVcUnit(detailListBind.get(0).getVcUnitName());
                    erpServiceOrdersInfo.setVcArea(detailListBind.get(0).getVcAreaNames());
                    erpServiceOrdersInfoService.saveOrUpdate(erpServiceOrdersInfo);
                }

                //修改，提单，编辑金额维护（维护erp_retainage_return_detail表）
                //再插入新记录
                ErpRetainageReturnDetail erpRetainageReturnDetailBind = new ErpRetainageReturnDetail();
                erpRetainageReturnDetailBind.setNumServiceOrderId(erpServiceOrdersBind.getId());
                erpRetainageReturnDetailBind.setNumStatus(erpServiceOrdersBind.getNumIsDeprecated());
                erpRetainageReturnDetailBind.setPayType(CommitOrderConstants.RETURN_DETAIL_COMMIT);
                erpRetainageReturnDetailBind.setNumCollectionPrice(erpServiceOrdersBind.getNumPayPrice());
                erpRetainageReturnDetailBind.setCreateTime(new Date());
                erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetailBind);
            }
        }

        if (registCouponId.intValue() == 0) {
            erpServiceOrders.setNumCouponId(dto.getCouponId());
            erpServiceOrders.setNumCouponPrice(dto.getCouponPrice());
        }
        erpServiceOrders.setNumTotalPrice(registPayFee);
        erpServiceOrders.setNumPayPrice(registPayFee);
        Long serviceOrderId = erpServiceOrdersService.saveOrUpdate(erpServiceOrders);


        ErpProductDetailListDto erpProductDetailListDto = new ErpProductDetailListDto();
        erpProductDetailListDto.setNumProductId(dto.getProductId());
        erpProductDetailListDto.setConfigurationAppletShow(1);
        List<ErpProductDetailListVo> detailList = erpProductDetailMapper.selectErpProductList(erpProductDetailListDto);
        if (detailList.size() > 0) {
            ErpServiceOrdersInfo erpServiceOrdersInfo = new ErpServiceOrdersInfo();
            erpServiceOrdersInfo.setNumServiceOrders(serviceOrderId);
            erpServiceOrdersInfo.setVcProductName(detailList.get(0).getVcProductName());
            erpServiceOrdersInfo.setVcCorporateProperty(detailList.get(0).getVcTaxNames());
            erpServiceOrdersInfo.setNumPrice(registPayFee);
            erpServiceOrdersInfo.setVcProductType(detailList.get(0).getServiceName());
            erpServiceOrdersInfo.setVcUnit(detailList.get(0).getVcUnitName());
            erpServiceOrdersInfo.setVcArea(detailList.get(0).getVcAreaNames());
            erpServiceOrdersInfoService.saveOrUpdate(erpServiceOrdersInfo);
        }

        //修改，提单，编辑金额维护（维护erp_retainage_return_detail表）
        //再插入新记录
        ErpRetainageReturnDetail erpRetainageReturnDetailSave = new ErpRetainageReturnDetail();
        erpRetainageReturnDetailSave.setNumServiceOrderId(erpServiceOrders.getId());
        erpRetainageReturnDetailSave.setNumStatus(erpServiceOrders.getNumIsDeprecated());
        erpRetainageReturnDetailSave.setPayType(CommitOrderConstants.RETURN_DETAIL_COMMIT);
        erpRetainageReturnDetailSave.setNumCollectionPrice(registPayFee);
        erpRetainageReturnDetailSave.setCreateTime(new Date());
        erpRetainageReturnDetailService.saveOrUpdate(erpRetainageReturnDetailSave);

        //维护收款方式
        // 添加收款方式信息。
        ErpOrderPaymentTerm erpOrderPayment = new ErpOrderPaymentTerm();
        //erpOrderPayment.setId(paymentTermInfo.getPaymentId());
        erpOrderPayment.setNumOrderId(orderId);
        erpOrderPayment.setNumRetainageId(null);
        erpOrderPayment.setNumType(1);
        erpOrderPayment.setDatSigningDatecreatedTime(new Date());
        erpOrderPayment.setDatCollectionTime(new Date());
        erpOrderPayment.setNumPayee(numUserIdXcx);

        Long paymentId = erpOrderPaymentTermService.saveOrUpdate(erpOrderPayment);

        // 添加收款详情信息。
        ErpOrderPaymentTermInfo erpOrderPaymentTermInfo = new ErpOrderPaymentTermInfo();
        erpOrderPaymentTermInfo.setTermId(paymentId);
        erpOrderPaymentTermInfo.setNumPaymentType(5L);
        erpOrderPaymentTermInfo.setNumMoney(dto.getPayPrice());
        erpOrderPaymentTermInfo.setNumStatus(1);
        erpOrderPaymentTermInfoService.saveOrUpdate(erpOrderPaymentTermInfo);

        // 更新指定订单的最新收款信息。
        commitOrderMapper.updatePayeeForOrder(orderId);

        erpOrderOperatingRecord.setNumOrderId(orderId);
        erpOrderOperatingRecord.setVcOperationContent(dto.getRemark());
        erpOrderOperatingRecord.setDatCreatedTime(new Date());

        // 添加操作记录。
        erpOrderOperatingRecordService.insertErpOrderOperatingRecord(erpOrderOperatingRecord);

        //更新优惠券状态
        updateCoupon(orderId);


        //发送钉钉消息
        String dingContent = "### 小程序提单,订单号： \n * " + vcOrderNumber + "，\n "
                + " * " + "客保" + kbName + "，\n"
                + " * " + "历史会计" + accountName + "，\n"
                + " * " + "推荐销售" + gsSignName + "，\n"
                + " * " + "分享销售" + shareUserName + "，\n";

        R<SysUser> info = remoteUserService.getUserInfoById(4086L, SecurityConstants.INNER);
        DingSendDTO dingSendDTO = new DingSendDTO(info.getData().getDingUserId(), "小程序提单", dingContent);
        dingDingService.sendDingMessage(dingSendDTO);

        R<SysUser> info1 = remoteUserService.getUserInfoById(1778L, SecurityConstants.INNER);
        DingSendDTO dingSendDTO1 = new DingSendDTO(info1.getData().getDingUserId(), "小程序提单", dingContent);
        dingDingService.sendDingMessage(dingSendDTO1);

        R<SysUser> info2 = remoteUserService.getUserInfoById(2407L, SecurityConstants.INNER);
        DingSendDTO dingSendDTO2 = new DingSendDTO(info2.getData().getDingUserId(), "小程序提单", dingContent);
        dingDingService.sendDingMessage(dingSendDTO2);


//        dingContent += "==========" + info.getData().getDingUserId();
//        dingContent += "==========" + info1.getData().getDingUserId();
//        dingContent += "==========" + info2.getData().getDingUserId();
//        R<SysUser> info4 = remoteUserService.getUserInfoById(9L, SecurityConstants.INNER);
//        DingSendDTO dingSendDTO2 = new DingSendDTO(info4.getData().getDingUserId(), "小程序提单", dingContent);
//        dingDingService.sendDingMessage(dingSendDTO2);
        //给老客反优惠券
        try {
            antiCoupon(dto, flag, match);
        } catch (Exception e) {
            log.error("老客返优惠券异常", e.getMessage());
        }


        resMap.put("orderId", orderId);
        resMap.put("orderNumber", vcOrderNumber);
        resMap.put("clientId", erpClient.getId());
        resMap.put("enterpriseId", erpClient.getNumEnterpriseId());
        return resMap;
    }

    /**
     * 检验新客
     * @param dto
     * @param erpClient
     * @return
     */
    private boolean inspectNewCustomers(XcxCommitOrderDto dto, ErpClient erpClient) {
        //判断是否是新客 true：新客，false：老客
        boolean flag = true;
        //先根据手机号校验
        int k = erpOrdersMapper.countOrderByPhone(dto.getPhone());
        if (k > 0){
            flag = false;
        }else {
            flag = erpOrdersMapper.countOrderByClientId(erpClient.getId()) <= 0;
        }
        //新客则把 起始分享人手机号赋值给 ShareUserPhone
        if (flag){
            String originPhone = erpOrdersMapper.selectOriginCusByNewCusPhone(dto.getPhone());
            if (StrUtil.isNotEmpty(originPhone) && StrUtil.isEmpty(dto.getShareUserPhone())){
                dto.setShareUserPhone(originPhone);
            }
        }
        return flag;
    }

    /**
     * 老客返优惠券
     * @param dto
     * @param flag  是否为新客
     * @param match 起始分享人是不是销售
     */
    private void antiCoupon(XcxCommitOrderDto dto, boolean flag, boolean match) {
        if (flag){
            //根据新客手机号查询老客手机号
            String oldPhone = erpOrdersMapper.selectOldCusByNewCusPhone(dto.getPhone());
            if (StrUtil.isNotEmpty(oldPhone)){
                //若是销售，则销售不反优惠券，找是老客的第二次分享人
                if (match && oldPhone.equals(dto.getShareUserPhone())){
                    //起始分享人是销售，且第一个查询的分享人和起始分享人一样
                    //查询分享的List 且分享人不为 dto.getShareUserPhone() 的
                    List<String> phoneList = erpOrdersMapper.selectOldPhoneList(dto.getPhone(), dto.getShareUserPhone());
                    if (CollUtil.isEmpty(phoneList)){
                        //说明就只有销售分享，直接返回
                        return;
                    }
                    for (String phone : phoneList) {
                        R<SysUser> info = remoteUserService.getUserByShareUserPhone(phone, SecurityConstants.INNER);
                        if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getData())) {
                            //不为空就找下一个
                        }else {
                            //找到就跳出循环
                            oldPhone = phone;
                            break;
                        }
                    }
                }
                //找活动 老带新 活动id默认为2 再找活动对应的优惠券额度id
                XcxActivityCouponLimit couponLimit = xcxActivityCouponLimitService.selectXcxActivityCouponLimitById(XcxActivityConstants.ACTIVITY_NEW_OLD);
                //再找规则 先确定优惠券额度为多少，此额度配置对应的类型为1
                XcxCouponConfig xcxCouponConfig = new XcxCouponConfig()
                        .setActivityId(couponLimit.getId()).setType(XcxActivityConstants.ACTIVITY_CONFIG_NEW_OLD);
                List<XcxCouponConfig> configList = xcxCouponConfigService.selectXcxCouponConfigList(xcxCouponConfig);
                //得到配置
                XcxCouponConfig couponConfig = configList.get(0);
                //进行校验：1、优惠券总发放张数，2、优惠券每个客户最大领取张数
                int i = erpDiscountCouponMapper.countXcxCouponConfig(couponConfig.getId());
                if (i >= couponConfig.getLimitNum()){
                    log.warn("已经达到优惠券最大分配数量，请联系管理员");
                }
                int j = erpDiscountCouponMapper.countUserXcxCouponConfig(couponConfig.getId(), oldPhone);
                if (j >= couponConfig.getClaimNum()){
                    log.warn("此额度优惠已达到该客户最大领取数量");
                }
                //发放优惠券
                if (couponConfig.getLimitNum() > i && couponConfig.getClaimNum() > j){
                    //调用发放优惠券接口
                    ErpDiscountCouponDto erpDiscountCouponDto = new ErpDiscountCouponDto()
                            .setId(couponLimit.getCouponLimitId())
                            .setNumType(CouponConstants.NUM_TYPE_XCX)
                            .setDiscountCouponAmount(couponConfig.getLimitAmount())
                            .setXcxPhone(oldPhone).setMinPrice(couponConfig.getMinPrice())
                            .setRemark(couponConfig.getRemark())
                            .setXcxCouponConfigId(couponConfig.getId());
                    if (ObjectUtil.isNotEmpty(couponConfig.getActStartTime())
                            && ObjectUtil.isNotEmpty(couponConfig.getActEndTime())){
                        erpDiscountCouponDto.setValidityPeriod(CouponConstants.VALIDITY_PERIOD_ON)
                                .setActStartTime(couponConfig.getActStartTime())
                                .setActEndTime(couponConfig.getActEndTime());
                    }else {
                        erpDiscountCouponDto.setValidityPeriod(CouponConstants.VALIDITY_PERIOD_OFF);
                    }
                    erpDiscountCouponService.generateErpDiscountCoupon(erpDiscountCouponDto);
                }
            }
        }
    }
    @Override
    public List<ErpGiftVO> mateGiftByProductIds(Long deptId, List<Long> productIdList, BigDecimal price) {
        List<ErpGiftVO> returnList = new ArrayList<>();
        if (ObjectUtil.isEmpty(deptId) || ObjectUtil.isEmpty(productIdList) || productIdList.size() == 0) {
            return returnList;
        }

        //find_in_set拼接所有的productId
        StringBuilder productFindStrInvert = new StringBuilder();
        productFindStrInvert.append("(");
        for (int i = 0; i < productIdList.size(); i++) {
            if (i > 0) {
                productFindStrInvert.append(" OR ");
            }
            productFindStrInvert.append("FIND_IN_SET("+productIdList.get(i)+",egrp.product_id) = 0");
        }
        productFindStrInvert.append(")");
        //遍历出所有匹配上的规则Id
        List<Long> mateRuleIdList = erpGiftRuleProductMapper.selectRuleIdByProductInvertStr(deptId, productFindStrInvert.toString(), price);
        if (ObjectUtil.isEmpty(mateRuleIdList)) {
            mateRuleIdList = new ArrayList<>();
        }

        //find_in_set拼接所有的productId
        StringBuilder productFindStr = new StringBuilder();
        productFindStr.append("(");
        for (int i = 0; i < productIdList.size(); i++) {
            if (i > 0) {
                productFindStr.append(" OR ");
            }
            productFindStr.append("FIND_IN_SET("+productIdList.get(i)+",egrp.product_id)");
        }
        productFindStr.append(")");
        List<Long> ruleIdList = erpGiftRuleProductMapper.selectRuleIdByProductStr(deptId, productFindStr.toString(), price);
        if (ruleIdList.size() > 0) {
            //查询出所有满足一个productId的规则产品
            List<ErpGiftRuleProduct> ruleProductList = erpGiftRuleProductMapper.selectByRuleIds(ruleIdList);
            //组合json格式为{"ruleId":true/false};   false表示未完全匹配
            JSONObject ruleMateObj = new JSONObject();
            for (int i = 0; i < ruleIdList.size(); i++) {
                if (!ruleMateObj.containsKey(ruleIdList.get(i).toString())) {
                    ruleMateObj.put(ruleIdList.get(i).toString(), true);
                }
            }
            //匹配所有的产品id，是否在一个规则下全部存在
            for (int i = 0; i < ruleProductList.size(); i++) {
                ErpGiftRuleProduct giftRuleProduct = ruleProductList.get(i);
                if (!ruleMateObj.getBoolean(giftRuleProduct.getRuleId().toString())) {
                    continue;
                }
                List<Long> ruleProductIdList = Arrays.asList(giftRuleProduct.getProductId().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

                boolean contains = false;
                for (int j = 0; j < productIdList.size(); j++) {
                    if (ruleProductIdList.contains(productIdList.get(j))) {
                        contains = true;
                        break;
                    }
                }

                if (!contains) {
                    ruleMateObj.put(giftRuleProduct.getRuleId().toString(), false);
                }
            }

            for (String key : ruleMateObj.keySet()) {
                if (ruleMateObj.getBoolean(key)) {
                    mateRuleIdList.add(Long.parseLong(key));
                }
            }
        }



        //根据规则ID取出所有有库存的赠品信息
        List<ErpGiftVO> giftList = erpGiftMapper.selectListByRuleIdList(mateRuleIdList, deptId);
        for (int i = 0; i < giftList.size(); i++) {
            ErpGiftVO vo = giftList.get(i);
            vo.setGiftTypeName(vo.getGiftType() == 1 ? "实物" : "服务产品");
            returnList.add(vo);
        }
        return returnList;
    }

    /**
     * 订单放入产品时查询可用的交易流水
     * @param dto
     * @return
     * getUseVoucher  是否查询订单已用凭证            1是
     * balanceUseBack 凭证金额是否回滚到可用余额     1是
     */
    public List<ErpTransactionVoucherVo> mateTransactionVoucherByProductIds(Long userId, List<Long> productIdList, String orderId, Long clueId, String clientId, Integer getUseVoucher, Integer balanceUseBack) {
        List<ErpTransactionVoucherVo> returnList = new ArrayList<>();
        if (ObjectUtil.isEmpty(userId) || ObjectUtil.isEmpty(productIdList) || productIdList.size() == 0) {
            return returnList;
        }
        //find_in_set拼接所有的productId
        StringBuilder productFindStr = new StringBuilder();
        productFindStr.append("(");
        for (int i = 0; i < productIdList.size(); i++) {
            if (i > 0) {
                productFindStr.append(" OR ");
            }
            productFindStr.append("FIND_IN_SET("+productIdList.get(i)+",epap.product_id)");
        }
        productFindStr.append(")");

        //获取当前所选产品适用的所有活动------分别查活动本金/活动赠金产品
//        List<List<Long>> activitieIdListAll = new ArrayList<>();
        List<Long> activitieIdList1 = erpPromotionalActivitiesProductMapper.selectActivitieIdByProductStr(productFindStr.toString(), 1);
        List<Long> activitieIdList2 = erpPromotionalActivitiesProductMapper.selectActivitieIdByProductStr(productFindStr.toString(), 2);

        JSONArray activitieIdArr = new JSONArray();
        if (activitieIdList1.size() > 0) {
            JSONObject obj = new JSONObject();
            obj.put("waitMateActivitieIdList", activitieIdList1);   //待匹配的活动ID
            obj.put("activitiesProductType", 1);    //活动产品表 1为活动本金
            obj.put("transactionVoucherType", 2);   //凭证表    2为活动本金
            activitieIdArr.add(obj);
        }
        if (activitieIdList2.size() > 0) {
            JSONObject obj = new JSONObject();
            obj.put("waitMateActivitieIdList", activitieIdList2);   //待匹配的活动ID
            obj.put("activitiesProductType", 2);    //活动产品表 2为活动赠金
            obj.put("transactionVoucherType", 3);   //凭证表    3为活动赠金
            activitieIdArr.add(obj);
        }

        //遍历出所有匹配上的活动Id
        List<Long> mateActivitieIdListAll = new ArrayList<>();  //包括本金和赠金
        if (activitieIdArr.size() > 0) {
            for (int j = 0; j < activitieIdArr.size(); j++) {
                List<Long> mateActivitieIdList = new ArrayList<>();

                JSONObject obj = activitieIdArr.getJSONObject(j);
                List<Long> waitMateActivitieIdList = (List<Long>) obj.get("waitMateActivitieIdList");

                //组合json格式为{"ruleId":true/false};   false表示未完全匹配
                JSONObject activitiesMateObj = new JSONObject();
                for (int i = 0; i < waitMateActivitieIdList.size(); i++) {
                    if (!activitiesMateObj.containsKey(waitMateActivitieIdList.get(i).toString())) {
                        activitiesMateObj.put(waitMateActivitieIdList.get(i).toString(), true);
                    }
                }

                //查询出所有满足一个productId的规则产品
                List<ErpPromotionalActivitiesProduct> activitiesProductList = erpPromotionalActivitiesProductMapper.selectByActivitieIds(waitMateActivitieIdList, obj.getInteger("activitiesProductType"));

                if (ObjectUtil.isNotEmpty(activitiesProductList) && activitiesProductList.size() > 0) {
                    //匹配所有的产品id，是否在一个规则下全部存在
                    for (int i = 0; i < activitiesProductList.size(); i++) {
                        ErpPromotionalActivitiesProduct erpPromotionalActivitiesProduct = activitiesProductList.get(i);
                        if (!activitiesMateObj.getBoolean(erpPromotionalActivitiesProduct.getActivitieId().toString())) {
                            continue;
                        }
                        List<Long> activitiesProductIdList = Arrays.asList(erpPromotionalActivitiesProduct.getProductId().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

                        boolean contains = false;
                        for (int z = 0; z < productIdList.size(); z++) {
                            if (activitiesProductIdList.contains(productIdList.get(z))) {
                                contains = true;
                                break;
                            }
                        }

                        if (!contains) {
                            activitiesMateObj.put(erpPromotionalActivitiesProduct.getActivitieId().toString(), false);
                        }
                    }
                }

                for (String key : activitiesMateObj.keySet()) {
                    if (activitiesMateObj.getBoolean(key)) {
                        mateActivitieIdList.add(Long.parseLong(key));
                        mateActivitieIdListAll.add(Long.parseLong(key));
                    }
                }
                //所有匹配上的
                if (mateActivitieIdList.size() > 0) {
                    obj.put("mateActivitieIdList", mateActivitieIdList);
                }
            }
        }

        //获取所有可用的支付记录(包括本金/赠金)
        List<Integer> payRecordIdList = erpTransactionVoucherMapper.selectOrderCanUseList(userId,clueId,clientId,mateActivitieIdListAll);

        List<Integer> transactionVoucherIdList = new ArrayList<>();
        //如果orderId存在，查询是否存在流水
        List<ErpTransactionVoucherFollowVo> followListUse = new ArrayList<>();
        //1待扣款数据，补回凭证   2已扣款数据，补回凭证     3待入账数据，补回凭证
        if (ObjectUtil.isNotEmpty(orderId) && getUseVoucher.intValue() == 1) {
            ErpTransactionVoucherFollowDto dto = new ErpTransactionVoucherFollowDto();
            dto.setStatusList(Arrays.asList(1L,2L,3L));
            dto.setOrderId(Long.parseLong(orderId));
            //判断是否已经释放
            List<ErpTransactionVoucherFollowVo> followList = erpTransactionVoucherFollowMapper.selectList(dto);
            for (int i = 0; i < followList.size(); i++) {
                ErpTransactionVoucherFollowVo followVo = followList.get(i);
                boolean noRelease = true;
                for (int j = 0; j < followList.size(); j++) {
                    ErpTransactionVoucherFollowVo releaseVo = followList.get(j);
                    if (StringUtils.equals(followVo.getId(), releaseVo.getReleaseId())) {
                        noRelease = false;
                        break;
                    }
                }
                if (noRelease && ObjectUtil.isEmpty(followVo.getReleaseId())) {
                    followListUse.add(followVo);
                }
            }
            if (ObjectUtil.isNotEmpty(followListUse) && followListUse.size() > 0) {
                for (int i = 0; i < followListUse.size(); i++) {
                    ErpTransactionVoucherFollowVo follow = followListUse.get(i);
                    if (!payRecordIdList.contains(follow.getPayRecordId())) {
                        payRecordIdList.add(follow.getPayRecordId());
                    }
                    if (!transactionVoucherIdList.contains(follow.getTransactionVoucher())) {
                        transactionVoucherIdList.add(follow.getTransactionVoucher());
                    }
                }
            }
        }
        if (payRecordIdList.size() > 0) {

            //取出所有的 满足的收款本金（非活动本金）
            returnList = erpTransactionVoucherMapper.selectByPayRecordIdList(payRecordIdList, 1, null);

            if (activitieIdArr.size() > 0) {
                for (int z = 0; z < activitieIdArr.size(); z++) {
                    JSONObject mateObj = activitieIdArr.getJSONObject(z);
                    //根据活动Id，支付记录取出  活动本金/活动赠金  凭证
                    List<ErpTransactionVoucherVo> voList = erpTransactionVoucherMapper.selectByPayRecordIdList(payRecordIdList, mateObj.getInteger("transactionVoucherType"), (List<Long>) mateObj.get("mateActivitieIdList"));
                    //根据活动Id, 取出本金/活动   所限制的产品
                    List<ErpPromotionalActivitiesProduct> mateProductList = erpPromotionalActivitiesProductMapper.selectByActivitieIds((List<Long>) mateObj.get("mateActivitieIdList"), mateObj.getInteger("activitiesProductType"));
                    //遍历收款本金    填充活动本金/赠金数据
                    for (int i = 0; i < returnList.size(); i++) {
                        ErpTransactionVoucherVo vo = returnList.get(i);
                        if (ObjectUtil.isEmpty(vo.getPaymentTypeStr())) {
                            vo.setPaymentTypeStr(ErpOrderPayRecordEnum.getPaymentTypeStr(vo.getPaymentType()));
                        }
                        //本金/赠金数据
                        List<ErpTransactionVoucherVo> splitList = ObjectUtil.isEmpty(vo.getSplitList()) ? new ArrayList<>() : vo.getSplitList();
                        for (int j = 0; j < voList.size(); j++) {
                            ErpTransactionVoucherVo splitVo = voList.get(j);
                            if (splitVo.getApproveIn() == 1) {
                                continue;
                            }
                            if (vo.getPayRecordId().intValue() == splitVo.getPayRecordId().intValue()) {

                                JSONArray needProductArr = new JSONArray();
                                for (int x = 0; x < mateProductList.size(); x++) {
                                    ErpPromotionalActivitiesProduct mateProduct = mateProductList.get(x);
                                    //取出当前本金/赠金参与的活动 所限制的产品
                                    if (mateProduct.getActivitieId().intValue() == splitVo.getActivitieId().intValue()) {
                                        List<Long> mateProductIdList = Arrays.asList(mateProduct.getProductId().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

                                        for (Long productId : productIdList) {
                                            //匹配所提交的产品id，补充该凭证在当前提单可用的产品Id
                                            if (mateProductIdList.contains(productId)) {
                                                JSONObject needProductObj = new JSONObject();
                                                needProductObj.put("productId",productId);
                                                needProductObj.put("fee", 0);
                                                needProductArr.add(needProductObj);
                                            }
                                        }
                                    }
                                }
                                splitVo.setNeedProductArr(needProductArr);
                                if (needProductArr.size() > 0) {
                                    splitVo.setNeedProduct(1);
                                }
                                if (splitVo.getType().intValue() == 3 && needProductArr.size() == 0) {
                                    for (Long productId : productIdList) {
                                        JSONObject needProductObj = new JSONObject();
                                        needProductObj.put("productId",productId);
                                        needProductObj.put("fee", 0);
                                        needProductArr.add(needProductObj);
                                    }
                                    splitVo.setNeedProductArr(needProductArr);
                                }
                                splitVo.setPaymentTypeStr(ErpOrderPayRecordEnum.getPaymentTypeStr(splitVo.getPaymentType()));
                                banlanceUseRollBack(splitVo, followListUse, balanceUseBack);
                                if (transactionVoucherIdList.contains(splitVo.getId()) ||
                                        splitVo.getBalanceUse().compareTo(new BigDecimal("0")) > 0) {
                                    splitList.add(splitVo);
                                }
                            }
                        }
                        vo.setSplitList(splitList);
                        banlanceUseRollBack(vo, followListUse, balanceUseBack);
                    }
                }
            } else {
                for (int i = 0; i < returnList.size(); i++) {
                    ErpTransactionVoucherVo vo = returnList.get(i);
                    if (ObjectUtil.isEmpty(vo.getPaymentTypeStr())) {
                        vo.setPaymentTypeStr(ErpOrderPayRecordEnum.getPaymentTypeStr(vo.getPaymentType()));
                    }
                    vo.setSplitList(new ArrayList<>());
                    banlanceUseRollBack(vo, followListUse, balanceUseBack);

                }
            }
        }
        return returnList;
    }

    /***
     * 将已使用的凭证流水金额，回滚到凭证可用余额
     * @param vo
     * @param followListUse
     */
    public void banlanceUseRollBack(ErpTransactionVoucherVo vo, List<ErpTransactionVoucherFollowVo> followListUse, Integer balanceUseBack) {
        BigDecimal deductionFee = new BigDecimal("0");
        if (ObjectUtil.isNotEmpty(followListUse) && followListUse.size() > 0) {
            for (int i = 0; i < followListUse.size(); i++) {
                ErpTransactionVoucherFollowVo followUse = followListUse.get(i);
                if (vo.getId().intValue() == followUse.getTransactionVoucher().intValue()) {
                    if (balanceUseBack.intValue() == 1) {
                        if (followUse.getStatus().intValue() == 1) {
                            //待扣款数据-----直接回滚
                            vo.setWaitDeduction(vo.getWaitDeduction().subtract(followUse.getFee()));
                            vo.setBalanceUse(vo.getBalanceUse().add(followUse.getFee()));
                            followUse.setFee(new BigDecimal("0"));
                        }
                        if (followUse.getStatus().intValue() == 2) {
                            //已扣款数据
                            deductionFee = deductionFee.add(followUse.getFee());
                            followUse.setFee(new BigDecimal("0"));
                        }
                        if (followUse.getStatus().intValue() == 3) {
                            //待入账数据
                            vo.setWaitEntry(vo.getWaitEntry().add(followUse.getFee()));
                            followUse.setFee(new BigDecimal("0"));
                        }
                    }
                    vo.setCheckFlag(true);

                    List<ErpTransactionVoucherFollowInfo> infoList = erpTransactionVoucherFollowInfoMapper.selectByFollowId(followUse.getId());
                    if (ObjectUtil.isNotEmpty(infoList) && infoList.size() > 0) {
                        JSONArray needProductArr = vo.getNeedProductArr();
                        if (ObjectUtil.isNotEmpty(needProductArr) && needProductArr.size() > 0) {
                            for (int j = 0; j < infoList.size(); j++) {
                                ErpTransactionVoucherFollowInfo info = infoList.get(j);
                                for (int z = 0; z < needProductArr.size(); z++) {
                                    JSONObject needProductObj = needProductArr.getJSONObject(z);
                                    if (info.getProductId().intValue() == needProductObj.getIntValue("productId")) {
                                        needProductObj.put("fee", needProductObj.getBigDecimal("fee").add(info.getFee()));
                                        needProductArr.set(z, needProductObj);
                                        vo.setNeedProductArr(needProductArr);
                                        vo.setCheckFlag(true);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        vo.setBalanceUse(vo.getBalanceUse().add(deductionFee));
    }


    /***
     * 提单时根据前段传来的支付流水，调整状态
     * @param voucherList
     * @param orderId
     * @param optionType
     */
    @Transactional(rollbackFor = Exception.class)
    public void commitOrderOperateTransactionVoucherFollow(Long orderId,
                                                           List<ErpTransactionVoucherVo> transactionVoucherVoList,
                                                           List<ErpProductForCommitOrderDTO> productForConfirmOrderVOList,
                                                           Integer optionType, BigDecimal orderPayPrice) {

        //组合产品列表
        JSONArray productArr = new JSONArray();
        for (int i = 0; i < productForConfirmOrderVOList.size(); i++) {
            if (optionType == 4
                    || productForConfirmOrderVOList.get(i).getIsDeprecated() == 0
                    || productForConfirmOrderVOList.get(i).getIsDeprecated() == 7) {
                JSONObject object = new JSONObject();
                object.put("productId", productForConfirmOrderVOList.get(i).getProductId());
                object.put("payPrice", productForConfirmOrderVOList.get(i).getPayPrice());
                object.put("isZero", "0");//0：非0元产品，1：0元产品待生成，2:0元产品已生成
                if (productForConfirmOrderVOList.get(i).getPayPrice().compareTo(new BigDecimal("0")) == 0) {
                    object.put("isZero", "1");
                }
                productArr.add(object);
            }
        }
        //组合所选凭证列表----二维数组变更为一维数组
        List<ErpTransactionVoucherVo> newList = new ArrayList<>();
        for (int i = 0; i < transactionVoucherVoList.size(); i++) {
            ErpTransactionVoucherVo vo = transactionVoucherVoList.get(i);
            newList.add(vo);
        }
        newList.sort(Comparator.comparing(ErpTransactionVoucherVo::getId));

        //限制产品的凭证
        for (int i = 0; i < productArr.size(); i++) {
            JSONObject productObj = productArr.getJSONObject(i);
            Long productId = productObj.getLong("productId");
            BigDecimal payPrice = productObj.getBigDecimal("payPrice");
            String isZero = productObj.getString("isZero");
            for (int j = 0; j < newList.size(); j++) {
                ErpTransactionVoucherVo vo = newList.get(j);
                if (vo.getNeedProduct() == 1 || vo.getType() == 3) {
                    JSONArray needProductArr = vo.getNeedProductArr();

                    List<ErpTransactionVoucherFollowInfo> followInfoList = ObjectUtil.isEmpty(vo.getFollowInfoList())
                            ? new ArrayList<>() : vo.getFollowInfoList();

                    for (int z = 0; z < needProductArr.size(); z++) {
                        JSONObject needProductObj = needProductArr.getJSONObject(z);
                        if (Integer.parseInt(isZero) == 1) {
                            ErpTransactionVoucherFollowInfo followInfo = new ErpTransactionVoucherFollowInfo();
                            followInfo.setFee(new BigDecimal("0"));
                            followInfo.setProductId(productId);
                            followInfo.setStatus(1);
                            followInfoList.add(followInfo);
                            isZero = "2";
                            productObj.put("isZero",isZero);
                        }
                        //当前产品ID为当前凭证所需产品，且扣款金额大于0
                        if (needProductObj.getString("productId").equals(productId.toString())
                                && needProductObj.getBigDecimal("fee").compareTo(new BigDecimal("0")) > 0) {

                            BigDecimal needProductPrice = needProductObj.getBigDecimal("fee");

                            ErpTransactionVoucherFollowInfo followInfo = new ErpTransactionVoucherFollowInfo();
                            followInfo.setFee(needProductPrice);
                            followInfo.setProductId(productId);
                            followInfo.setStatus(1);
                            followInfoList.add(followInfo);

                            payPrice = payPrice.subtract(needProductPrice);
                        }
                    }
                    vo.setFollowInfoList(followInfoList);
                }
            }
            productObj.put("payPrice",payPrice);
            productArr.set(i, productObj);
            if (payPrice.compareTo(new BigDecimal("0")) < 0) {
                throw new ServiceException("凭证填写的产品:"+productId+"金额超出产品实收");
            }
        }
        //不限制产品的凭证
        for (int i = 0; i < newList.size(); i++) {
            ErpTransactionVoucherVo vo = newList.get(i);
            BigDecimal balanceUse = vo.getBalanceUse();
            if (vo.getNeedProduct() == 0 && vo.getType() != 3
                    && (balanceUse.compareTo(new BigDecimal("0")) > 0
                        || (ObjectUtil.isNotEmpty(vo.getZeroPay()) && vo.getZeroPay().intValue() == 1)
                        )
            ) {

                List<ErpTransactionVoucherFollowInfo> followInfoList = ObjectUtil.isEmpty(vo.getFollowInfoList())
                        ? new ArrayList<>() : vo.getFollowInfoList();

                for (int j = 0; j < productArr.size(); j++) {
                    JSONObject productObj = productArr.getJSONObject(j);
                    Long productId = productObj.getLong("productId");
                    BigDecimal payPrice = productObj.getBigDecimal("payPrice");
                    String isZero = productObj.getString("isZero");
                    if (payPrice.compareTo(new BigDecimal("0")) > 0 && balanceUse.compareTo(new BigDecimal("0")) > 0) {
                        ErpTransactionVoucherFollowInfo followInfo = new ErpTransactionVoucherFollowInfo();
                        followInfo.setProductId(productId);
                        followInfo.setStatus(1);
                        if (balanceUse.compareTo(payPrice) > 0) {
                            followInfo.setFee(payPrice);
                            balanceUse = balanceUse.subtract(payPrice);
                        } else {
                            followInfo.setFee(balanceUse);
                            balanceUse = new BigDecimal("0");
                        }
                        followInfoList.add(followInfo);
                        payPrice = payPrice.subtract(followInfo.getFee());
                    }
                    if (Integer.parseInt(isZero) == 1) {
                        ErpTransactionVoucherFollowInfo followInfo = new ErpTransactionVoucherFollowInfo();
                        followInfo.setFee(new BigDecimal("0"));
                        followInfo.setProductId(productId);
                        followInfo.setStatus(1);
                        followInfoList.add(followInfo);
                        isZero = "2";
                        productObj.put("isZero",isZero);
                    }
                    productObj.put("payPrice",payPrice);
                    productArr.set(j, productObj);
//                    if (balanceUse.compareTo(new BigDecimal("0")) <= 0) {
//                        break;
//                    }
                }
                vo.setFollowInfoList(followInfoList);
            }
        }

        for (int i = 0; i < productArr.size(); i++) {
            JSONObject productObj = productArr.getJSONObject(i);
            BigDecimal payPrice = productObj.getBigDecimal("payPrice");
            if (payPrice.compareTo(new BigDecimal("0")) > 0) {
                throw new ServiceException("凭证金额无法满足产品实收");
            }
        }

        if (optionType.intValue() == 3) {
            List<ErpTransactionVoucherFollow> voucherFollowList = erpTransactionVoucherFollowMapper.selectListByOrderId(orderId);

            for (int i = 0; i < voucherFollowList.size(); i++) {
                ErpTransactionVoucherFollow follow = voucherFollowList.get(i);

                ErpTransactionVoucher voucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(follow.getTransactionVoucher());
                voucher.setBalanceUse(voucher.getBalanceUse().add(follow.getFee()));
                voucher.setWaitDeduction(voucher.getWaitDeduction().subtract(follow.getFee()));
                erpTransactionVoucherMapper.updateErpTransactionVoucher(voucher);

                erpTransactionVoucherFollowInfoMapper.deleteByFollowId(follow.getId());

                erpTransactionVoucherFollowMapper.deleteErpTransactionVoucherFollowById(follow.getId());
            }
        }

        if (optionType.intValue() == 1 || optionType.intValue() == 3 || optionType.intValue() == 4) {
            for (int i = 0; i < newList.size(); i++) {
                ErpTransactionVoucherVo vo = newList.get(i);
                insertTransactionVoucherFollow(vo,orderId, orderPayPrice);
            }
        }

        if (optionType == 2) {
            List<ErpTransactionVoucherFollow> voucherFollowList = erpTransactionVoucherFollowMapper.selectListByOrderId(orderId);
            //未释放退回的 流水
            JSONObject noReleaseObj = new JSONObject();
            for (int i = 0; i < voucherFollowList.size(); i++) {
                ErpTransactionVoucherFollow follow = voucherFollowList.get(i);
                if (follow.getStatus().intValue() != 2 && follow.getStatus().intValue() != 4) {
                    throw new ServiceException("交易流水有误1");
                }
                if (follow.getFee().compareTo(new BigDecimal("0")) == 0 || follow.getStatus().intValue() == 4) {
                    continue;
                }
                if (follow.getType() == 1) {
                    boolean release = false;
                    for (int j = 0; j < voucherFollowList.size(); j++) {
                        //有针对于消费记录的释放退回记录
                        if (ObjectUtil.isNotEmpty(voucherFollowList.get(j).getReleaseId()) &&
                                voucherFollowList.get(j).getReleaseId().equals(follow.getId()) &&
                                voucherFollowList.get(j).getStatus() != 4) {
                            release = true;
                            break;
                        }
                    }
                    if (!release) {
                        //针对于一条凭证，一个订单只应该存在一条   未释放退回的消费记录
                        if (noReleaseObj.containsKey(follow.getTransactionVoucher().toString())) {
                            throw new ServiceException("交易流水有误2");
                        }
                        JSONObject obj = new JSONObject();
                        obj.put("fee", follow.getFee());
                        obj.put("followId", follow.getId());
                        noReleaseObj.put(follow.getTransactionVoucher().toString(), obj);
                    }
                }
            }

            for (int i = 0; i < newList.size(); i++) {
                ErpTransactionVoucherVo vo = newList.get(i);
                List<ErpTransactionVoucherFollowInfo> followInfoList = vo.getFollowInfoList();
                //该条凭证预计总扣款
                BigDecimal allFee = new BigDecimal("0");
                for (int j = 0; j < followInfoList.size(); j++) {
                    allFee = allFee.add(followInfoList.get(j).getFee());
                }

                if (noReleaseObj.containsKey(vo.getId().toString())) {
                    //如果针对于凭证，存在流水，先更改流水详情
                    JSONObject obj = noReleaseObj.getJSONObject(vo.getId().toString());
                    erpTransactionVoucherFollowInfoMapper.updateByFollowIdWithStatus(obj.getString("followId"), 2, 3);
                    /***
                     * 判断当前流水的金额与凭证预计扣款金额是否相同
                     * 相同   插入新的流水详情，流水，凭证都不懂
                     * 不同   插入一条释放退回流水，，，凭证，流水，流水详情都需要操作
                     */
                    if (obj.getBigDecimal("fee").compareTo(allFee) == 0) {
                        for (int j = 0; j < followInfoList.size(); j++) {
                            ErpTransactionVoucherFollowInfo followInfo = followInfoList.get(j);
                            followInfo.setFollowId(obj.getString("followId"));
                            erpTransactionVoucherFollowInfoMapper.insertErpTransactionVoucherFollowInfo(followInfo);
                        }
                    } else {
                        ErpTransactionVoucherFollow voucherFollowRelease = erpTransactionVoucherFollowMapper.selectErpTransactionVoucherFollowById(obj.getString("followId"));
                        voucherFollowRelease.setReleaseId(voucherFollowRelease.getId());
                        voucherFollowRelease.setWaitEntry(voucherFollowRelease.getFee());
                        voucherFollowRelease.setWaitDeduction(new BigDecimal("0"));
                        voucherFollowRelease.setFee(voucherFollowRelease.getFee().negate());
                        voucherFollowRelease.setStatus(3);
                        voucherFollowRelease.setType(2);
                        voucherFollowRelease.setCreatedTime(new Date());
                        voucherFollowRelease.setId(transactionVoucherFollowService.createdId());
                        erpTransactionVoucherFollowMapper.insertErpTransactionVoucherFollow(voucherFollowRelease);

                        ErpTransactionVoucher erpTransactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(vo.getId());
                        erpTransactionVoucher.setWaitEntry(erpTransactionVoucher.getWaitEntry().subtract(voucherFollowRelease.getFee()));
//                        erpTransactionVoucher.setBalanceUse(erpTransactionVoucher.getBalanceUse().subtract(voucherFollowRelease.getFee()));
//                        erpTransactionVoucher.setBalance(erpTransactionVoucher.getBalance().subtract(voucherFollowRelease.getFee()));
                        erpTransactionVoucherMapper.updateErpTransactionVoucher(erpTransactionVoucher);

                        insertTransactionVoucherFollow(vo,orderId, orderPayPrice);
                    }
                    obj.put("fee", new BigDecimal("0"));
                    noReleaseObj.put(vo.getId().toString(),obj);
                } else {
                    insertTransactionVoucherFollow(vo,orderId, orderPayPrice);
                }
            }
            //未释放退款的流水，在新提交里未使用
            for (String key : noReleaseObj.keySet()) {
                JSONObject obj = noReleaseObj.getJSONObject(key);
                if (obj.getBigDecimal("fee").compareTo(new BigDecimal("0")) > 0) {
                    ErpTransactionVoucherFollow voucherFollowRelease = erpTransactionVoucherFollowMapper.selectErpTransactionVoucherFollowById(obj.getString("followId"));
                    voucherFollowRelease.setReleaseId(voucherFollowRelease.getId());
                    voucherFollowRelease.setWaitEntry(voucherFollowRelease.getFee());
                    voucherFollowRelease.setWaitDeduction(new BigDecimal("0"));
                    voucherFollowRelease.setFee(voucherFollowRelease.getFee().negate());
                    voucherFollowRelease.setStatus(3);
                    voucherFollowRelease.setType(2);
                    voucherFollowRelease.setCreatedTime(new Date());
                    voucherFollowRelease.setId(transactionVoucherFollowService.createdId());
                    erpTransactionVoucherFollowMapper.insertErpTransactionVoucherFollow(voucherFollowRelease);

                    ErpTransactionVoucher erpTransactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(Integer.parseInt(key));
                    erpTransactionVoucher.setWaitEntry(erpTransactionVoucher.getWaitEntry().subtract(voucherFollowRelease.getFee()));
                    erpTransactionVoucherMapper.updateErpTransactionVoucher(erpTransactionVoucher);

                    erpTransactionVoucherFollowInfoMapper.updateByFollowIdWithStatus(obj.getString("followId"), 2, 3);
                }
            }
        }
    }

    public void insertTransactionVoucherFollow(ErpTransactionVoucherVo vo, Long orderId, BigDecimal orderPayPrice) {

        List<ErpTransactionVoucherFollowInfo> followInfoList = vo.getFollowInfoList();

        BigDecimal allFee = new BigDecimal("0");
        for (int j = 0; j < followInfoList.size(); j++) {
            allFee = allFee.add(followInfoList.get(j).getFee());
        }
        if (allFee.compareTo(new BigDecimal("0")) == 0 && vo.getZeroPay() == 2) {
            return;
        }

        ErpTransactionVoucher erpTransactionVoucher = erpTransactionVoucherMapper.selectErpTransactionVoucherById(vo.getId());
        if (vo.getType() == 3) {
            ErpPromotionalActivities activities = erpPromotionalActivitiesMapper.selectErpPromotionalActivitiesById(vo.getActivitieId());
            if (activities.getMinPrice().compareTo(orderPayPrice) > 0) {
                throw new ServiceException("订单实收总金额不满足活动赠金凭证:"+vo.getId()+"的最低消费");
            }
            if (activities.getUseType().intValue() == 1) {
                if (allFee.compareTo(orderPayPrice.multiply(new BigDecimal(activities.getUseProportion())).divide(new BigDecimal("100"))) > 0) {
                    throw new ServiceException("凭证编号:"+vo.getId()+"只可使用订单实收的"+activities.getUseProportion()+"%");
                }
            } else {
                if (erpTransactionVoucherFollowMapper.selectListByTransactionVoucher(vo.getId()).compareTo(new BigDecimal("0")) != 0) {
                    throw new ServiceException("凭证编号:"+vo.getId()+"只可使用一次");
                }
            }
        }

        ErpTransactionVoucherFollow voucherFollow = new ErpTransactionVoucherFollow();
        voucherFollow.setId(transactionVoucherFollowService.createdId());
        voucherFollow.setTransactionVoucher(erpTransactionVoucher.getId());
        voucherFollow.setStatus(1);
        voucherFollow.setType(1);
        voucherFollow.setOrderId(orderId);
        voucherFollow.setCreatedTime(new Date());
        voucherFollow.setCreatedUser(erpTransactionVoucher.getOperateUser());
        voucherFollow.setFee(allFee);


        //查询凭证下待入账的数据
        BigDecimal waitEntry = new BigDecimal("0");
        List<ErpTransactionVoucherFollow> waitEntryList = erpTransactionVoucherFollowMapper.selectWaitEntryByTransactionVoucher(vo.getId(), orderId);
        for (int j = 0; j < waitEntryList.size(); j++) {
            ErpTransactionVoucherFollow waitEntryFollow = waitEntryList.get(j);
            if (allFee.compareTo(waitEntryFollow.getFee().negate()) > 0) {
                allFee = allFee.add(waitEntryFollow.getFee());
                erpTransactionVoucher.setWaitEntry(erpTransactionVoucher.getWaitEntry().add(waitEntryFollow.getFee()));
//                erpTransactionVoucher.setBalanceUse(erpTransactionVoucher.getBalanceUse().add(waitEntryFollow.getFee()));
                waitEntryFollow.setWaitEntry(new BigDecimal("0"));
            } else {
                waitEntryFollow.setWaitEntry(waitEntryFollow.getFee().negate().subtract(allFee));
                erpTransactionVoucher.setWaitEntry(erpTransactionVoucher.getWaitEntry().subtract(allFee));
//                erpTransactionVoucher.setBalanceUse(erpTransactionVoucher.getBalanceUse().add(allFee));
//                waitEntry = waitEntry.add()
                allFee = new BigDecimal("0");
            }
            erpTransactionVoucherFollowMapper.updateErpTransactionVoucherFollow(waitEntryFollow);
        }
        if (voucherFollow.getType().intValue() == 1) {
            voucherFollow.setWaitDeduction(allFee);
        }

        if (erpTransactionVoucher.getBalanceUse().compareTo(allFee) < 0) {
            throw new ServiceException("凭证金额无法满足产品实收");
        }
        erpTransactionVoucher.setBalanceUse(erpTransactionVoucher.getBalanceUse().subtract(allFee));
        erpTransactionVoucher.setWaitDeduction(erpTransactionVoucher.getWaitDeduction().add(allFee));

        erpTransactionVoucherMapper.updateErpTransactionVoucher(erpTransactionVoucher);

        erpTransactionVoucherFollowMapper.insertErpTransactionVoucherFollow(voucherFollow);

        for (int j = 0; j < followInfoList.size(); j++) {
            ErpTransactionVoucherFollowInfo followInfo = followInfoList.get(j);
            followInfo.setFollowId(voucherFollow.getId());
            erpTransactionVoucherFollowInfoMapper.insertErpTransactionVoucherFollowInfo(followInfo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int bindVoucherFollow(ErpClientForCommitOrderDTO erpClientForCommitOrderDTO, HttpServletResponse response) {
        Long orderId = erpClientForCommitOrderDTO.getOrderId();
        if (ObjectUtil.isEmpty(orderId)) {
            throw new ServiceException("订单ID为空");
        }
        ErpOrders orders = erpOrdersMapper.selectErpOrdersById(orderId);
        if (ObjectUtil.isEmpty(orders) || ObjectUtil.isEmpty(orders.getNumCreateOrderExamineStatus())
                || orders.getNumCreateOrderExamineStatus().intValue() != OrderApprovalStatusEnum.WAIT_BIND_TRANSACTIONVOUCHERFOLLOW.getStatusType()) {
            throw new ServiceException("提单审核状态错误");
        }

        //调用来源CRM
        LoginUser loginUser = tokenService.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException(UserExceptionConstants.LOGIN_USER_IS_EMPTY);
        }
        SysUser sysUser = loginUser.getSysUser();

        createPaymentAndRetainageByVoucher(orderId, erpClientForCommitOrderDTO, sysUser.getUserId(), new Date(), orders.getNumPayPrice());
        // 更新指定订单的最新收款信息。
        commitOrderMapper.updatePayeeForOrder(orderId);

        //如果为上传收款截图时调用的则置为：财务待审核。
        orders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.ACCOUNTANT_NOT.getStatusType());
        orders.setCreateArriveFinance(new Date());
        erpOrdersMapper.updateErpOrders(orders);
        try {
            send(orders);
        } catch (Exception e) {
            log.error("上传完收款截图发送财务审核提醒邮件异常，异常信息为：", e);
        }
        return 1;
    }

    /***
     * 资质延期，年检订单，关联服务单
     * @param product
     * @param erpServiceOrders
     */
    private void handleServiceQualificationsExtension(ErpProductForCommitOrderDTO product, ErpServiceOrders erpServiceOrders) {

//
        int type = 0;
        //资质延期
        if (ObjectUtil.isNotEmpty(product.getQualificationsExtension()) && product.getQualificationsExtension().intValue() == 1) {
            type = 1;
        }
        //年检
        if (ObjectUtil.isNotEmpty(product.getAnnualInspection()) && product.getAnnualInspection().intValue() == 1) {
            type = 2;
        }

        if (type != 0) {
            //查询服务单是否存在状态正常的关联
            SServiceQualificationsExtension dto = new SServiceQualificationsExtension();
            dto.setServiceOrderId(erpServiceOrders.getId());
            dto.setType(type);
            List<SServiceQualificationsExtension> list = sServiceQualificationsExtensionMapper.selectSServiceQualificationsExtensionList(dto);
            if (ObjectUtil.isNotEmpty(list)) {
                //状态1新增待审核2正常3删除待审核4删除
                //提单时只可能有正常和删除的数据
                for (int i = 0; i < list.size(); i++) {
                    if (!Arrays.asList(2,4).contains(list.get(i).getStatus())) {
                        throw new ServiceException("延期关联错误");
                    }
                }

                int normalCount = 0;
                SServiceQualificationsExtension qualificationsExtension = new SServiceQualificationsExtension();
                for (int i = 0; i < list.size(); i++) {
                    if (list.get(i).getStatus().intValue() == 2) {
                        normalCount++;
                        qualificationsExtension = list.get(i);
                    }
                }

                if (normalCount > 1) {
                    throw new ServiceException("延期关联错误2");
                }
                //历史延期状态变为作废待审核
                if (normalCount == 1) {
                    qualificationsExtension.setStatus(3);
                    sServiceQualificationsExtensionMapper.updateSServiceQualificationsExtension(qualificationsExtension);
                }
            }

            if (ObjectUtil.isNotEmpty(product.getExtensionServiceId())
                    && erpServiceOrders.getNumStatus().intValue() == 1 && !Arrays.asList(1,2,3,4).contains(erpServiceOrders.getNumIsDeprecated())) {

                SServiceMain extensionService = sServiceMainMapper.selectSServiceMainById(product.getExtensionServiceId());
                if (ObjectUtil.isEmpty(extensionService)) {
                    throw new ServiceException("延期关联错误3");
                }
                SServiceQualificationsExtension qualificationsExtension = new SServiceQualificationsExtension();
                qualificationsExtension.setNumEnterpriseId(extensionService.getNumEnterpriseId());
                qualificationsExtension.setStatus(1);
                qualificationsExtension.setServiceOrderId(erpServiceOrders.getId());
                qualificationsExtension.setServiceMainId(product.getExtensionServiceId());
                qualificationsExtension.setType(type);
                if (type == 1) {
                    qualificationsExtension.setOldStatus(extensionService.getQualificationsExtensionStatus());
                }
                if (type == 2) {
                    qualificationsExtension.setOldStatus(extensionService.getAnnualInspectionStatus());
                }
                sServiceQualificationsExtensionMapper.insertSServiceQualificationsExtension(qualificationsExtension);
            }
        }
    }
}
