package com.nnb.erp.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.ObjectUtil;
import com.nnb.common.core.exception.ServiceException;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.erp.constant.ErpProductConstants;
import com.nnb.erp.constant.enums.ApprovalConfigEnum;
import com.nnb.erp.constant.enums.OrderApprovalStatusEnum;
import com.nnb.erp.constant.enums.OrderInvalidStatusEnum;
import com.nnb.erp.constant.enums.OrderStatusEnum;
import com.nnb.erp.domain.*;
import com.nnb.erp.domain.vo.ErpApprovalRecordDto;
import com.nnb.erp.mapper.*;
import com.nnb.erp.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 审核记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-12
 */
@Service
public class ErpApprovalRecordServiceImpl implements IErpApprovalRecordService 
{
    @Resource
    private ErpApprovalRecordMapper erpApprovalRecordMapper;
    @Resource
    private ErpBusinessApprovalMapper erpBusinessApprovalMapper;
    @Resource
    private IErpRetainageReturnService erpRetainageReturnService;
    @Resource
    private IErpOrdersService erpOrdersService;
    @Resource
    private ErpBizSettlementMapper erpBizSettlementMapper;
    @Resource
    private ErpBizAdvanceChargeInfoMapper erpBizAdvanceChargeInfoMapper;

    @Resource
    private IErpBizMainInfoService erpBizMainInfoService;
    @Resource
    private IErpServiceOrderRefundService erpServiceOrderRefundService;
    @Resource
    private IErpCouponUseRecordService erpCouponUseRecordService;

    /**
     * 查询审核记录
     * 
     * @param id 审核记录主键
     * @return 审核记录
     */
    @Override
    public ErpApprovalRecord selectErpApprovalRecordById(Long id)
    {
        return erpApprovalRecordMapper.selectErpApprovalRecordById(id);
    }

    /**
     * 查询审核记录列表
     * 
     * @param erpApprovalRecord 审核记录
     * @return 审核记录
     */
    @Override
    public List<ErpApprovalRecord> selectErpApprovalRecordList(ErpApprovalRecord erpApprovalRecord)
    {
        return erpApprovalRecordMapper.selectErpApprovalRecordList(erpApprovalRecord);
    }

    /**
     * 新增审核记录
     * 
     * @param erpApprovalRecord 审核记录
     * @return 结果
     */
    @Override
    public int insertErpApprovalRecord(ErpApprovalRecord erpApprovalRecord)
    {
        return erpApprovalRecordMapper.insertErpApprovalRecord(erpApprovalRecord);
    }

    /**
     * 修改审核记录
     * 
     * @param erpApprovalRecord 审核记录
     * @return 结果
     */
    @Override
    public int updateErpApprovalRecord(ErpApprovalRecord erpApprovalRecord)
    {
        return erpApprovalRecordMapper.updateErpApprovalRecord(erpApprovalRecord);
    }

    /**
     * 批量删除审核记录
     * 
     * @param ids 需要删除的审核记录主键
     * @return 结果
     */
    @Override
    public int deleteErpApprovalRecordByIds(Long[] ids)
    {
        return erpApprovalRecordMapper.deleteErpApprovalRecordByIds(ids);
    }

    /**
     * 删除审核记录信息
     * 
     * @param id 审核记录主键
     * @return 结果
     */
    @Override
    public int deleteErpApprovalRecordById(Long id)
    {
        return erpApprovalRecordMapper.deleteErpApprovalRecordById(id);
    }

    /**
     * 提交审核
     *
     * @param dto 审核记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object insertErpApprovalRecordDto(ErpApprovalRecordDto dto) {
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();
        dto.setNumAuditor(userId);
        dto.setDatCreatedBy(userId);
        dto.setDatCreatedAt(date);
        //当前流程下一步步骤id-返回null当前为最后一步
        Long flowId = erpBusinessApprovalMapper.selectSortStatusByBusinessApprovalId(dto.getNumBusinessApprovalId());
        //业务审核流程更新下一步信息
        ErpBusinessApproval erpBusinessApproval = ErpBusinessApproval.builder()
                .id(dto.getNumBusinessApprovalId())
                .numStatus(dto.getNumStatus().equals(2L) || ObjectUtil.isEmpty(flowId) ? dto.getNumStatus().intValue() : null)
                .numApprovalFlowId(flowId)
                .datUpdatedBy(userId)
                .datUpdatedAt(date)
                .build();
        erpBusinessApprovalMapper.updateErpBusinessApproval(erpBusinessApproval);

        // 构建提单审核通过返回值MAP。
        Map<String, String> erpBizMainInfoByOrderId = new HashMap<>(2);

        //总审核结束-订单表、尾款表更新
        if (ObjectUtil.isNotEmpty(erpBusinessApproval.getNumStatus())){
            if (dto.getOtherType() == 1){
                //尾款
                //尾款信息更新
                ErpRetainageReturn erpRetainageReturn = erpRetainageReturnService.selectErpRetainageReturnById(dto.getNumOtherId());
                erpRetainageReturn.setDatFinanceCollectionTime(dto.getNumStatus().equals(1L) ? dto.getDatFinanceCollectionTime() : null);
                erpRetainageReturn.setNumStatus(dto.getNumStatus().intValue());
                erpRetainageReturn.setNumUpdatedBy(userId);
                erpRetainageReturn.setDatSigningDateupdatedTime(date);
                erpRetainageReturnService.updateErpRetainageReturn(erpRetainageReturn);

                // 维护订单信息。
                erpRetainageReturnService.maintainRetainageReturn(null);
                /*//订单信息更新
                ErpOrders erpOrders = erpOrdersService.selectErpOrdersById(erpRetainageReturn.getNumOrderId());
                //实付=原实付+尾款回款
                erpOrders.setNumPayPrice(erpOrders.getNumPayPrice().add(erpRetainageReturn.getNumCollectionPrice()));
                //剩余尾款=原尾款-尾款回款
                BigDecimal num = erpOrders.getNumLastPrice().subtract(erpRetainageReturn.getNumCollectionPrice());
                if (num.compareTo(BigDecimal.ZERO) < 0){
                    throw new ServiceException("尾款金额异常!");
                }
                erpOrders.setNumLastPrice(num);
                //优惠金额=原优惠+尾款优惠
                erpOrders.setNumDiscountAmount(erpOrders.getNumDiscountAmount().add(erpRetainageReturn.getNumDiscounts()));
                erpOrdersService.updateErpOrders(erpOrders);*/

            }else if (dto.getOtherType() == 2) {
                ErpOrders erpOrderBase = erpOrdersService.selectErpOrdersById(dto.getNumOtherId());
                if (ObjectUtil.isNotNull(dto.getApprovalConfigId())) {
                    //订单
                    ErpOrders erpOrders = new ErpOrders();
                    erpOrders.setId(dto.getNumOtherId());
                    erpOrders.setNumUpdatedBy(userId);
                    erpOrders.setDatSigningDateupdatedTime(date);

                    if (dto.getApprovalConfigId().equals(ApprovalConfigEnum.ORDER_SUBMIT.getConfigId())) {
                        // 当前操作为：提单审核。
                        if (dto.getNumStatus().equals(1L)) {
                            // 审核通过。
                            // 设置订单状态为：待服务。
                            erpOrders.setNumStatus(Long.valueOf(OrderStatusEnum.ORDER_NOT_BIZ.getStatusType()));

                            // 设置订单提单审核状态为：已通过。
                            /*erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_PASS.getStatusType());*/

                            if (erpOrderBase.getNumSource() == 1) {
                                // 当前审核为：小程序提单审核通过，需做升降级。
                                erpOrdersService.memberUp(dto.getNumOtherId(), 1);

                                // 当前审核为：小程序提单审核通过，需维护客户优惠券。
                                erpCouponUseRecordService.maintainCouponRecord(dto.getNumOtherId(), 1);

                            }

                        } else {
                            // 审核不通过。
                            // 设置订单提单审核状态为：已驳回。
                            /*erpOrders.setNumCreateOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_REJECTED.getStatusType());*/

                        }
                    } else if (dto.getApprovalConfigId().equals(ApprovalConfigEnum.ORDER_REVISE.getConfigId())) {
                        // 当前操作为：修改审核。
                        if (dto.getNumStatus().equals(1L)) {
                            // 设置订单修改审核状态为：已通过。
                            /*erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_PASS.getStatusType());*/

                        } else {
                            // 设置订单修改审核状态为：已驳回。
                            /*erpOrders.setNumModifyOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_REJECTED.getStatusType());*/

                        }

                    } else if (dto.getApprovalConfigId().equals(ApprovalConfigEnum.ORDER_REFUND.getConfigId())) {
                        // 当前操作为：退款审核。
                        if (dto.getNumStatus().equals(1L)) {
                            // 设置订单失效状态为：已退款。
                            erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_REFUNDED.getStatusType());

                            // 设置订单退款审核状态为：已通过。
                            /*erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.APPROVAL_PASS.getStatusType());*/

                            if (erpOrderBase.getNumSource() == 1) {
                                // 当前审核为：小程序退款审核通过，需做升降级。
                                erpOrdersService.memberUp(dto.getNumOtherId(), 3);
                            }

                            // 将当前订单全部退款。
                            erpServiceOrderRefundService.maintainOrderRefund(dto.getNumOtherId(), 0);

                        } else {
                            // 设置订单退款审核状态为：已驳回。
                            /*erpOrders.setNumRefundExamineStatus(OrderApprovalStatusEnum.APPROVAL_REJECTED.getStatusType());*/

                        }

                    } else if (dto.getApprovalConfigId().equals(ApprovalConfigEnum.ORDER_CANCEL.getConfigId())) {
                        // 当前操作为：作废审核。
                        if (dto.getNumStatus().equals(1L)) {
                            // 设置订单失效状态为：已作废。
                            erpOrders.setNumValidStatus(OrderInvalidStatusEnum.INVALID_DEPRECATED.getStatusType());

                            // 设置订单作废审核状态为：已通过。
                            /*erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_PASS.getStatusType());*/

                            if (erpOrderBase.getNumSource() == 1) {
                                // 当前审核为：小程序作废审核通过，需做升降级。
                                erpOrdersService.memberUp(dto.getNumOtherId(), 2);
                            }

                        } else {
                            // 设置订单作废审核状态为：已驳回。
                            /*erpOrders.setNumCancelOrderExamineStatus(OrderApprovalStatusEnum.APPROVAL_REJECTED.getStatusType());*/


                        }

                    } else {
                        throw new ServiceException("审核类型无效（" + dto.getApprovalConfigId().toString() + "）");
                    }

                    erpOrdersService.updateErpOrders(erpOrders);
                }

            } else if (dto.getOtherType() == 3) {
                // 成本结算审核。
                ErpBizSettlement erpBizSettlement = new ErpBizSettlement();
                erpBizSettlement.setNumId(dto.getNumOtherId());
                erpBizSettlement.setNumApproveStatus(Long.valueOf(erpBusinessApproval.getNumStatus()));
                erpBizSettlement.setNumUpdatedBy(userId);
                erpBizSettlement.setDatUpdatedAt(new Date());

                erpBizSettlementMapper.updateErpBizSettlement(erpBizSettlement);
            } else if (dto.getOtherType() == 4) {
                // 预付款审核。
                ErpBizAdvanceChargeInfo erpBizAdvanceChargeInfo = new ErpBizAdvanceChargeInfo();
                erpBizAdvanceChargeInfo.setNumId(dto.getNumOtherId());
                erpBizAdvanceChargeInfo.setNumApproveStatus(Long.valueOf(erpBusinessApproval.getNumStatus()));
                erpBizAdvanceChargeInfo.setDatUpdatedBy(userId);
                erpBizAdvanceChargeInfo.setDatUpdatedTime(new Date());

                erpBizAdvanceChargeInfoMapper.updateErpBizAdvanceChargeInfo(erpBizAdvanceChargeInfo);
            }

            if (dto.getOtherType() == 2 && dto.getNumStatus().equals(1L) && dto.getApprovalConfigId().equals(ApprovalConfigEnum.ORDER_SUBMIT.getConfigId())) {
                // 提单审核通过。
                // 初始化服务单数据。
                erpBizMainInfoByOrderId = erpBizMainInfoService.createErpBizMainInfoByOrderId(dto.getNumOtherId());
            }

        }

        erpApprovalRecordMapper.insertErpApprovalRecord(dto);

        return erpBizMainInfoByOrderId;
    }
}
