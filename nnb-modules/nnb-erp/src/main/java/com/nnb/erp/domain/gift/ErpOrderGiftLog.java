package com.nnb.erp.domain.gift;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.nnb.common.core.web.domain.BaseEntity;

/**
 * 订单赠品发放日志对象 erp_order_gift_log
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
@ApiModel(value="ErpOrderGiftLog",description="订单赠品发放日志对象")
public class ErpOrderGiftLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty("主键")
    private Long id;

    /** 订单ID */
    @Excel(name = "订单ID")
    @ApiModelProperty("订单ID")
    private Long orderId;

    /** 赠品发放记录ID */
    @Excel(name = "赠品发放记录ID")
    @ApiModelProperty("赠品发放记录ID")
    private Long giftIssueRecordId;

    /** 剩余量 */
    @Excel(name = "剩余量")
    @ApiModelProperty("剩余量")
    private BigDecimal surplusAmount;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty("创建人")
    private Long createUser;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty("更新人")
    private Long updateUser;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setGiftIssueRecordId(Long giftIssueRecordId) 
    {
        this.giftIssueRecordId = giftIssueRecordId;
    }

    public Long getGiftIssueRecordId() 
    {
        return giftIssueRecordId;
    }
    public void setSurplusAmount(BigDecimal surplusAmount) 
    {
        this.surplusAmount = surplusAmount;
    }

    public BigDecimal getSurplusAmount() 
    {
        return surplusAmount;
    }
    public void setCreateUser(Long createUser) 
    {
        this.createUser = createUser;
    }

    public Long getCreateUser() 
    {
        return createUser;
    }
    public void setUpdateUser(Long updateUser) 
    {
        this.updateUser = updateUser;
    }

    public Long getUpdateUser() 
    {
        return updateUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("giftIssueRecordId", getGiftIssueRecordId())
            .append("surplusAmount", getSurplusAmount())
            .append("createUser", getCreateUser())
            .append("updateUser", getUpdateUser())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
