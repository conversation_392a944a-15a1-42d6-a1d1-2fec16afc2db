package com.nnb.erp.service;

import java.util.List;
import com.nnb.erp.domain.ErpLicenseDemand;
import com.nnb.erp.domain.dto.ErpLicenseDemandDto;
import com.nnb.erp.domain.vo.ErpLicenseDemandVo;

/**
 * 执照需求Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IErpLicenseDemandService 
{
    /**
     * 查询执照需求
     * 
     * @param id 执照需求主键
     * @return 执照需求
     */
    public ErpLicenseDemand selectErpLicenseDemandById(Long id);

    /**
     * 查询执照需求列表
     * 
     * @param erpLicenseDemand 执照需求
     * @return 执照需求集合
     */
    public List<ErpLicenseDemandVo> selectErpLicenseDemandList(ErpLicenseDemandDto dto);

    /**
     * 新增执照需求
     * 
     * @param erpLicenseDemand 执照需求
     * @return 结果
     */
    public int insertErpLicenseDemand(ErpLicenseDemand erpLicenseDemand);

    /**
     * 修改执照需求
     * 
     * @param erpLicenseDemand 执照需求
     * @return 结果
     */
    public int updateErpLicenseDemand(ErpLicenseDemand erpLicenseDemand);

    /**
     * 批量删除执照需求
     * 
     * @param ids 需要删除的执照需求主键集合
     * @return 结果
     */
    public int deleteErpLicenseDemandByIds(Long[] ids);

    /**
     * 删除执照需求信息
     * 
     * @param id 执照需求主键
     * @return 结果
     */
    public int deleteErpLicenseDemandById(Long id);


    public int matchLicense(ErpLicenseDemandDto dto);


    public int shelveLicense(ErpLicenseDemandDto dto);


    public int cancelLicense(ErpLicenseDemandDto dto);
}
