package com.nnb.erp.domain.vo;

import com.nnb.erp.domain.ErpExamineBudget;
import com.nnb.erp.domain.vo.approval.ErpExamineApproveListVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ErpExamineBudgetVo extends ErpExamineBudget {

    private String approveTypeName;
    private Integer budgetTypeFirst;
    private BigDecimal budgetFee;
    private Integer parentId;



    private List<ErpExamineApproveListVO> historyList;
}
