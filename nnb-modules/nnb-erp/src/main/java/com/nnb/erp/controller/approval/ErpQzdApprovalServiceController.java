package com.nnb.erp.controller.approval;

import java.util.ArrayList;
import java.util.List;
import java.io.IOException;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.utils.SecurityUtils;
import com.nnb.common.core.utils.StringUtils;
import com.nnb.erp.domain.approval.ErpQzdApprovalService;
import com.nnb.erp.domain.dto.approval.ErpExamineApproveQueryDTO;
import com.nnb.erp.domain.dto.approval.ErpQzdApprovalServiceDTO;
import com.nnb.erp.domain.dto.approval.ErpQzdPaymentRecordDTO;
import com.nnb.erp.domain.vo.approval.ErpExamineApproveVO;
import com.nnb.erp.service.IErpExamineApproveService;
import com.nnb.erp.service.approval.IErpQzdApprovalServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.nnb.common.log.annotation.Log;
import com.nnb.common.log.enums.BusinessType;
import com.nnb.common.security.annotation.PreAuthorize;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 启照多审批业务Controller
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/ErpQzdApprovalService")
@Api(tags = "ErpQzdApprovalServiceController", description = "启照多审批业务")
public class ErpQzdApprovalServiceController extends BaseController
{
    @Autowired
    private IErpQzdApprovalServiceService erpQzdApprovalServiceService;

    @Autowired
    private IErpExamineApproveService erpExamineApproveService;

    /**
     * 查询启照多审批业务列表
     */
    @ApiOperation(value = "查询启照多审批业务列表")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpQzdApprovalService.class)})
    @PreAuthorize(hasPermi = "erp:service:list")
    @GetMapping("/list")
    public TableDataInfo list(ErpQzdApprovalService erpQzdApprovalService)
    {
        startPage();
        List<ErpQzdApprovalService> list = erpQzdApprovalServiceService.selectErpQzdApprovalServiceList(erpQzdApprovalService);
        return getDataTable(list);
    }

    /**
     * 导出启照多审批业务列表
     */
    @ApiOperation(value = "导出启照多审批业务列表")
    @PreAuthorize(hasPermi = "erp:service:export")
    //@Log(title = "启照多审批业务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ErpQzdApprovalService erpQzdApprovalService) throws IOException
    {
        List<ErpQzdApprovalService> list = erpQzdApprovalServiceService.selectErpQzdApprovalServiceList(erpQzdApprovalService);
        ExcelUtil<ErpQzdApprovalService> util = new ExcelUtil<ErpQzdApprovalService>(ErpQzdApprovalService.class);
        util.exportExcel(response, list, "启照多审批业务数据");
    }

    /**
     * 获取启照多审批业务详细信息
     */
    @ApiOperation(value = "获取启照多审批业务详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpQzdApprovalService.class)})
    @PreAuthorize(hasPermi = "erp:service:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(name="id",value="启照多审批业务id") @PathVariable("id") Long id)
    {
        return AjaxResult.success(erpQzdApprovalServiceService.selectErpQzdApprovalServiceById(id));
    }

    /**
     * 新增启照多审批业务
     */
    @ApiOperation(value = "新增启照多审批业务")
    @PreAuthorize(hasPermi = "erp:service:add")
    //@Log(title = "启照多审批业务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ErpQzdApprovalService erpQzdApprovalService)
    {
        return toAjax(erpQzdApprovalServiceService.insertErpQzdApprovalService(erpQzdApprovalService));
    }

    /**
     * 新增启照多付款或退款申请
     */
    @ApiOperation(value = "新增启照多付款或退款申请")
    @PostMapping("/add")
    public AjaxResult addQzdApprovalService(@RequestBody ErpQzdApprovalService erpQzdApprovalService)
    {
        return toAjax(erpQzdApprovalServiceService.insertErpQzdApprovalService(erpQzdApprovalService));
    }

    @ApiOperation(value = "补录接口")
    @PostMapping("/supplement")
    public AjaxResult supplementQzdApprovalService(@RequestBody ErpQzdApprovalServiceDTO erpQzdApprovalServiceDTO)
    {
        return toAjax(erpQzdApprovalServiceService.supplementQzdApprovalService(erpQzdApprovalServiceDTO));
    }

    /**
     * 获取启照多审批业务详细信息
     */
    @ApiOperation(value = "获取启照多审批业务详细信息")
    @ApiResponses({@ApiResponse(code=200 ,message = "成功响应", response = ErpQzdApprovalService.class)})
    @PostMapping(value = "/getQzdApprovalServiceInfo")
    public AjaxResult getQzdApprovalServiceInfo(@ApiParam(name="id",value="启照多审批业务id") @RequestParam("id") Long id, @RequestParam(name = "approvalId")Long approvalId)
    {
        return AjaxResult.success(erpQzdApprovalServiceService.getQzdApprovalServiceInfo(id, approvalId));
    }

    /**
     * 修改启照多审批业务
     */
    @ApiOperation(value = "修改启照多审批业务")
    @PreAuthorize(hasPermi = "erp:service:edit")
    //@Log(title = "启照多审批业务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ErpQzdApprovalService erpQzdApprovalService)
    {
        return toAjax(erpQzdApprovalServiceService.updateErpQzdApprovalService(erpQzdApprovalService));
    }

    /**
     * 删除启照多审批业务
     */
    @ApiOperation(value = "删除启照多审批业务")
    @PreAuthorize(hasPermi = "erp:service:remove")
    //@Log(title = "启照多审批业务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erpQzdApprovalServiceService.deleteErpQzdApprovalServiceByIds(ids));
    }

    /**
     *
     * @param id
     * @param type  审批流程传1，打印审批流程传 2
     * @return
     */
    @ApiOperation(value = "启照多审批流程")
    @PostMapping("/getErpExamineFlow/{id}/{type}")
    public AjaxResult getErpExamineFlow(@PathVariable("id") Long id, @PathVariable("type") Integer type){
        return AjaxResult.success(erpQzdApprovalServiceService.getErpExamineFlow(id, type));
    }

    @ApiOperation(value = "启照多审批列表")
    @PostMapping("/getErpExamineApproveVOList")
    public TableDataInfo getErpExamineApproveVOList(@RequestBody ErpExamineApproveQueryDTO erpExamineApproveQueryDTO){
        List<ErpExamineApproveVO> erpExamineApproveVOS = new ArrayList<>();
        Integer pageNum = erpExamineApproveQueryDTO.getPageNum();
        Integer pageSize = erpExamineApproveQueryDTO.getPageSize();
        Long total = 0L;

        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            erpExamineApproveQueryDTO.setCreatedUser(SecurityUtils.getUserId().intValue());
            Page<Object> page = PageHelper.startPage(pageNum, pageSize, null);
            erpExamineApproveVOS = erpExamineApproveService.selectErpExamineApproveVOList(erpExamineApproveQueryDTO);
            total = page.getTotal();
        }
        return getDataTableAndTotal(erpExamineApproveVOS, total);
    }

    @ApiOperation(value = "标记付款")
    @PostMapping("/markPayment")
    public AjaxResult markPayment(@RequestBody ErpQzdPaymentRecordDTO erpQzdPaymentRecordDTO) {
        return toAjax(erpExamineApproveService.markPayment(erpQzdPaymentRecordDTO));
    }

    @ApiOperation(value = "标记付款数量")
    @PostMapping("/markPaymentAmount/{otherId}/{approveType}")
    public AjaxResult markPaymentAmount(@PathVariable(value = "otherId") Long otherId, @PathVariable(value = "approveType") int approveType) {
        return AjaxResult.success(erpExamineApproveService.markPaymentAmount(otherId, approveType));
    }

    @ApiOperation(value = "校验该执照是否可申请")
    @PostMapping("/checkQzdApprovalService")
    public AjaxResult checkQzdApprovalService(@RequestBody ErpQzdApprovalService erpQzdApprovalService){
        return erpQzdApprovalServiceService.checkAddQzdApprovalService(erpQzdApprovalService);
    }




}
