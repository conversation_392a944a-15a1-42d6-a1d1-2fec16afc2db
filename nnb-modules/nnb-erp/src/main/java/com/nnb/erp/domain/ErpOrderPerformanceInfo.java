package com.nnb.erp.domain;

import com.nnb.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 尾款回款详情 erp_retainage_return_detail
 *
 * <AUTHOR>
 * @since 2022-04-06 09:10:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpOrderPerformanceInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long performanceId;
    private Long returnDetailId;
    private BigDecimal fee;
    private Integer userId;

}
