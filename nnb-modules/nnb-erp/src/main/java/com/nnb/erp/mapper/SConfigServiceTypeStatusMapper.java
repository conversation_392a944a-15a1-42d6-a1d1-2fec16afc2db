package com.nnb.erp.mapper;

import java.util.List;
import com.nnb.erp.domain.SConfigServiceTypeStatus;
import com.nnb.erp.domain.ServiceTypeStatus;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-07
 */
public interface SConfigServiceTypeStatusMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SConfigServiceTypeStatus selectSConfigServiceTypeStatusById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SConfigServiceTypeStatus> selectSConfigServiceTypeStatusList(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 结果
     */
    public int insertSConfigServiceTypeStatus(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sConfigServiceTypeStatus 【请填写功能名称】
     * @return 结果
     */
    public int updateSConfigServiceTypeStatus(SConfigServiceTypeStatus sConfigServiceTypeStatus);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSConfigServiceTypeStatusById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSConfigServiceTypeStatusByIds(Long[] ids);

    /**
     * 查询状态
     */
    List<ServiceTypeStatus> getServiceTypeStatusByIds(@Param("ids") List<Long> ids);
}
