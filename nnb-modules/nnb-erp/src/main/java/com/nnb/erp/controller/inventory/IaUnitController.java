package com.nnb.erp.controller.inventory;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.nnb.common.core.utils.poi.ExcelUtil;
import com.nnb.erp.domain.inventory.IaInventoryArchives;
import com.nnb.erp.domain.inventory.IaUnit;
import com.nnb.erp.service.inventory.IaUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.nnb.common.security.annotation.PreAuthorize;

import com.nnb.common.core.web.controller.BaseController;
import com.nnb.common.core.web.domain.AjaxResult;
import io.swagger.annotations.*;
import com.nnb.common.core.web.page.TableDataInfo;

/**
 * 存货计量单位Controller
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@RestController
@RequestMapping("/iaUnit")
@Api(tags = "IaUnitController", description = "存货计量单位")
public class IaUnitController extends BaseController {

    @Autowired
    private IaUnitService iaUnitService;

    /**
     * 查询存货计量单位列表-分页
     */
    @ApiOperation(value = "查询存货计量单位列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaUnit.class)})
    @PostMapping("/pageList")
    public TableDataInfo pageList(@RequestBody IaUnit iaUnit) {
        Page<Object> objects = PageHelper.startPage(iaUnit.getPageNum(), iaUnit.getPageSize(), null);
        List<IaUnit> list = iaUnitService.selectIaUnitList(iaUnit);
        return getDataTableAndTotal(list, objects.getTotal());
    }

    @ApiOperation(value = "查询存货计量单位列表")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaUnit.class)})
    @PostMapping("/list")
    public AjaxResult list(@RequestBody IaUnit iaUnit) {
        return AjaxResult.success(iaUnitService.selectIaUnitList(iaUnit));
    }

    /**
     * 导出存货计量单位列表
     */
    @ApiOperation(value = "导出存货计量单位列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, IaUnit iaUnit) throws IOException {
        List<IaUnit> list = iaUnitService.selectIaUnitList(iaUnit);
        ExcelUtil<IaUnit> util = new ExcelUtil<IaUnit>(IaUnit.class);
        util.exportExcel(response, list, "存货计量单位数据");
    }

    /**
     * 获取存货计量单位详细信息
     */
    @ApiOperation(value = "获取存货计量单位详细信息")
    @ApiResponses({@ApiResponse(code = 200, message = "成功响应", response = IaUnit.class)})
    @PostMapping("getById")
    public AjaxResult getInfo(@ApiParam(name = "id", value = "存货计量单位id") @PathVariable("id") Long id) {
        return AjaxResult.success(iaUnitService.selectIaUnitById(id));
    }

    /**
     * 新增存货计量单位
     */
    @ApiOperation(value = "新增存货计量单位")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody IaUnit iaUnit) {
        return toAjax(iaUnitService.insertIaUnit(iaUnit));
    }

    /**
     * 修改存货计量单位
     */
    @ApiOperation(value = "修改存货计量单位")
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody IaUnit iaUnit) {
        return toAjax(iaUnitService.updateIaUnit(iaUnit));
    }

    /**
     * 删除存货计量单位
     */
    @ApiOperation(value = "删除存货计量单位")
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody IaUnit iaUnit) {
        return toAjax(iaUnitService.deleteIaUnitByIds(iaUnit.getIds()));
    }

    @ApiOperation(value = "获取最大编码")
    @PostMapping("/getMaxCode")
    public AjaxResult getMaxCode() {
        return AjaxResult.success("success", iaUnitService.getMaxCode());
    }
}
