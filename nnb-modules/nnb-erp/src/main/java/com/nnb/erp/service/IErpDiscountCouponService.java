package com.nnb.erp.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.erp.domain.CompanyCoupon;
import com.nnb.erp.domain.ErpDiscountCoupon;
import com.nnb.erp.domain.dto.CouponUsageDto;
import com.nnb.erp.domain.dto.ErpDiscountCouponDto;
import com.nnb.erp.domain.vo.CouponUsageVo;
import com.nnb.erp.domain.vo.couponVo.ErpDiscountCouponVo;
import net.sf.json.JSONArray;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2022-08-12
 */
public interface IErpDiscountCouponService 
{
    /**
     * 查询优惠券
     * 
     * @param id 优惠券主键
     * @return 优惠券
     */
    public ErpDiscountCoupon selectErpDiscountCouponById(Long id);

    /**
     * 查询优惠券列表
     * 
     * @param erpDiscountCoupon 优惠券
     * @return 优惠券集合
     */
    public List<ErpDiscountCoupon> selectErpDiscountCouponList(ErpDiscountCoupon erpDiscountCoupon);


    public List<ErpDiscountCouponVo> selectErpDiscountCouponVoList(ErpDiscountCoupon erpDiscountCoupon);


    /**
     * 新增优惠券
     * 
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    public int insertErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon);

    /**
     * 修改优惠券
     * 
     * @param erpDiscountCoupon 优惠券
     * @return 结果
     */
    public int updateErpDiscountCoupon(ErpDiscountCoupon erpDiscountCoupon);

    /**
     * 批量删除优惠券
     * 
     * @param ids 需要删除的优惠券主键集合
     * @return 结果
     */
    public int deleteErpDiscountCouponByIds(Long[] ids);

    /**
     * 删除优惠券信息
     * 
     * @param id 优惠券主键
     * @return 结果
     */
    public int deleteErpDiscountCouponById(Long id);

    int generateErpDiscountCoupon(ErpDiscountCouponDto erpDiscountCouponDto);

    int revocationErpDiscountCoupon(Long id);

    public ErpDiscountCoupon getErpDiscountCoupon(Long numProductId, Long clueId);

    List<CompanyCoupon> companyCouponList(CompanyCoupon companyCoupon);

    /**
     * 优惠券使用统计
     * @param couponUsageDto
     * @return
     */
    List<CouponUsageVo> couponUsage(CouponUsageDto couponUsageDto);

    List<ErpDiscountCoupon> getXcxCouponByPhone(String phone);
}
