package com.nnb.erp.constant;

import java.util.HashMap;
import java.util.Map;

public class ErpLicenseConstants {

    /***
     * 执照上架
     */
    public static final int LICENSE_GROUDING_UP = 1;                        //
    /***
     * 执照下架
     */
    public static final int LICENSE_GROUDING_DOWN = 2;                      //
    /***
     * 执照上架
     */
    public static final int LICENSE_GROUDING__WAIT_UP = 3;                        //
    /***
     * 执照删除
     */
    public static final int LICENSE_DELETED = 1;                            //
    /***
     * 执照未删除
     */
    public static final int LICENSE_NO_DELETED = 2;                         //
    /***
     *  执照新增
     */
    public static final int LICENSE_STATUS_NEW = 0;              //
    /***
     *  待售
     */
    public static final int LICENSE_STATUS_SELLING = 1;                     //
    /***
     *  已提单
     */
    public static final int LICENSE_STATUS_BILL = 2;                        //
    /***
     *  已售
     */
    public static final int LICENSE_STATUS_SELLED = 3;                      //

    //执照审核状态常量**********start*********//
    /**
     * 待提交
     */
    public static final int TO_BE_SUBMITTED = 0;
    /**
     * 主管待审核（内部）
     */
    public static final int SUPERVISOR_TO_BE_REVIEWED = 1;
    /**
     * 主管驳回（内部）
     */
    public static final int SUPERVISOR_TO_REJECTED = 2;
    /**
     * 启照多待审核
     */
    public static final int QZD_TO_BE_REVIEWED = 3;
    /**
     * 启照多审核驳回
     */
    public static final int QZD_REVIEWED_REJECTED = 4;
    /**
     * 启照多审核通过
     */
    public static final int QZD_REVIEWED_PASS = 5;
    //执照审核状态常量**********end*********//

    public static final Map<String, String> ERP_LICENSE_STATUS = new HashMap();          //状态
    public static final Map<String, String> ERP_LICENSE_EXAMINE_STATUS = new HashMap();          //审核状态
    public static final Map<String, String> ERP_LICENSE_SOURCE = new HashMap();          //执照来源
    public static final Map<String, String> ERP_LICENSE_HANDOVER_STATUS = new HashMap();          //交接状态

    static {
        ERP_LICENSE_HANDOVER_STATUS.put(	"1"  , "未交接"	);
        ERP_LICENSE_HANDOVER_STATUS.put(	"2"  , "已交接"	);

        ERP_LICENSE_SOURCE.put(	"1"  , "内部"	);
        ERP_LICENSE_SOURCE.put(	"2"  , "直客"	);
        ERP_LICENSE_SOURCE.put(	"3"  , "同行"	);
        ERP_LICENSE_SOURCE.put(	"4"  , "自营"	);
        ERP_LICENSE_SOURCE.put(	"5"  , "外部"	);

        ERP_LICENSE_EXAMINE_STATUS.put(	"0"  , "待提交"	);
        ERP_LICENSE_EXAMINE_STATUS.put(	"1"  , "主管待审核"	);
        ERP_LICENSE_EXAMINE_STATUS.put(	"2"  , "主管驳回"	);
        ERP_LICENSE_EXAMINE_STATUS.put(	"3"  , "启照多待审核"	);
        ERP_LICENSE_EXAMINE_STATUS.put(	"4"  , "启照多审核驳回"	);
        ERP_LICENSE_EXAMINE_STATUS.put(	"5"  , "启照多审核通过"	);

        ERP_LICENSE_STATUS.put(	"0"  , "新建"	);
        ERP_LICENSE_STATUS.put(	"1"  , "待售"	);
        ERP_LICENSE_STATUS.put(	"2"  , "已提单"	);
        ERP_LICENSE_STATUS.put(	"3"  , "已售"	);
        ERP_LICENSE_STATUS.put(	"4"  , "无"	);

    }
}
