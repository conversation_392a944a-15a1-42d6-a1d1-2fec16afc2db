package com.nnb.erp.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.nnb.erp.domain.ErpProductDetail;
import com.nnb.erp.domain.ErpProductService;
import com.nnb.erp.domain.vo.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 产品管理Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface IErpProductDetailService
{
    /**
     * 查询产品管理
     *
     * @param numProductId 产品管理主键
     * @return 产品管理
     */
    public ErpProductDetail selectErpProductDetailByNumProductId(Long numProductId);

    /**
     * 查询产品管理列表
     *
     * @param erpProductDetail 产品管理
     * @return 产品管理集合
     */
    public List<ErpProductDetail> selectErpProductDetailList(ErpProductDetail erpProductDetail);

    /**
     * 新增产品管理
     *
     * @param erpProductDetail 产品管理
     * @return 结果
     */
    public int insertErpProductDetail(ErpProductDetail erpProductDetail);

    /**
     * 修改产品管理
     *
     * @param erpProductDetail 产品管理
     * @return 结果
     */
    public int updateErpProductDetail(ErpProductDetail erpProductDetail);

    /**
     * 批量删除产品管理
     *
     * @param numProductIds 需要删除的产品管理主键集合
     * @return 结果
     */
    public int deleteErpProductDetailByNumProductIds(Long[] numProductIds);

    /**
     * 删除产品管理信息
     *
     * @param numProductId 产品管理主键
     * @return 结果
     */
    public int deleteErpProductDetailByNumProductId(Long numProductId);


    /**
     * 查询产品列表
     *
     * @param erpProductDetailListDto 产品管理
     * @return 产品管理集合
     */
    public List<ErpProductDetailListVo> selectErpProductList(ErpProductDetailListDto erpProductDetailListDto);

    public List<ErpProductDetailListVo> selectErpProductListByNumber(ErpProductDetailListDto erpProductDetailListDto);



    /**
     * 查询产品详情
     *
     * @param numProductId 产品管理主键
     * @return 产品管理
     */
    public ErpProductDetailVo selectErpProductDetail(Long numProductId);


    /**
     * 新增产品
     *
     * @param detailDto 产品管理
     * @return 结果
     */
    public int insertErpProduct(ErpProductDetailDto detailDto);


    /**
     * 修改产品
     *
     * @param detailDto 产品管理
     * @return 结果
     */
    public int updateErpProduct(ErpProductDetailDto detailDto);

    /**
     * 批量上/下架
     * @param numProductIds
     * @param state
     * @return
     */
    public int updateStates(List<Long> numProductIds, Long state);

    /**
     * 导入产品数据
     * @param file
     */
    public void importProduct(MultipartFile file) throws Exception;

    public ErpProductVo selectErpProductByNumProductId(Long numProductId);

    List<ErpProductService> getVcServiceName(String vcServiceName);

    void updateFinanceClassificationId(List<Map<String, Long>> list);
}
