package com.nnb.erp.domain.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 销售表Vo
 */

@Data
public class SalesListingExportVo {

    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号")
    private Long vcOrderNumber;

    @ApiModelProperty("合同编号")
    @Excel(name = "合同编号")
    private String contractNumber;

    @ApiModelProperty("服务单ID")
    @Excel(name = "服务单ID")
    private Long serviceMainId;

    @ApiModelProperty("服务单更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "服务单更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sServiceMainCreateTime;

    @ApiModelProperty("城市")
    @Excel(name = "城市")
    private String city;

    @ApiModelProperty("签约时间")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datSigningDate;

    @ApiModelProperty("签约部门")
    @Excel(name = "签约部门")
    private String signDeptName;

    @ApiModelProperty("签约人")
    @Excel(name = "签约人")
    private String signer;

    @ApiModelProperty("收款部门")
    @Excel(name = "收款部门")
    private String payeeDeptName;

    @ApiModelProperty("收款人")
    @Excel(name = "收款人")
    private String payee;

    @ApiModelProperty("企业名称")
    @Excel(name = "企业名称")
    private String enterpriseName;


    @ApiModelProperty("税号")
    @Excel(name = "税号")
    private String taxNo;

    @ApiModelProperty("企业联系人")
    @Excel(name = "企业联系人")
    private String enterpriseContactName;

    @ApiModelProperty("纳税类型")
    @Excel(name = "纳税类型")
    private String vcCorporateProperty;

    @ApiModelProperty("产品区域")
    @Excel(name = "产品区域")
    private String vcArea;

    @ApiModelProperty("产品大类")
    @Excel(name = "产品大类")
    private String vcClassificationName;

    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称")
    private String  vcProductName;

    @ApiModelProperty("自定义服务类型")
    @Excel(name = "自定义服务类型")
    private String vcServiceName;

    @ApiModelProperty("服务类型")
    @Excel(name = "服务类型")
    private String serviceTypeName;

    @ApiModelProperty("购买数量")
    @Excel(name = "购买数量")
    private Integer numProductCount;

    @ApiModelProperty("产品单价")
    @Excel(name = "产品单价")
    private BigDecimal productPrice;

    @ApiModelProperty("产品原价")
    @Excel(name = "产品原价")
    private BigDecimal productOriginalPrice;

    @ApiModelProperty("优惠类型")
    @Excel(name = "优惠类型")
    private String discountType;

    @ApiModelProperty("优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("服务费")
    @Excel(name = "服务费")
    private BigDecimal serviceFee;

    @ApiModelProperty("产品优惠金额")
    @Excel(name = "产品优惠金额")
    private BigDecimal numCouponPrice;

    @ApiModelProperty("订单总额")
    @Excel(name = "订单总额")
    private BigDecimal orderNumTotalPrice;

    @ApiModelProperty("订单总金额")
    @Excel(name = "订单总金额")
    private BigDecimal orderTotalPrice;

    @ApiModelProperty("尾款状态")
    @Excel(name = "尾款状态")
    private String lastPriceNumStatus;

    @ApiModelProperty("订单尾款")
    @Excel(name = "订单尾款")
    private BigDecimal numLastPrice;

    @ApiModelProperty("本次收款金额")
    @Excel(name = "本次收款金额")
    private BigDecimal numCollectionPrice;

    @ApiModelProperty("产品应收")
    @Excel(name = "产品应收")
    private BigDecimal productNumTotalPrice;

    @ApiModelProperty("产品实收")
    @Excel(name = "产品实收")
    private BigDecimal productPayPrice;

    @ApiModelProperty("产品尾款")
    @Excel(name = "产品尾款")
    private BigDecimal productNumLastPrice;

    @ApiModelProperty("产品状态")
    @Excel(name = "产品状态")
    private String productStatus;

    @ApiModelProperty("是否完结 1：是， 2： 否")
    @Excel(name = "是否完结")
    private String isFinishStr;

    @ApiModelProperty("收款日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datCollectionTime;

    @ApiModelProperty("财务收款日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务收款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date datFinanceCollectionTime;

    @ApiModelProperty("退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundTime;

    @ApiModelProperty("行业")
    @Excel(name = "行业")
    private String industry;

    @ApiModelProperty("客户渠道来源")
    @Excel(name = "客户渠道来源")
    private String guestVcName;

    @ApiModelProperty("地址类型")
    @Excel(name = "地址类型")
    private String addressType;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderCreateTime;

    @ApiModelProperty("记账开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记账开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date acStart;

    @ApiModelProperty("记账结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记账结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date acEnd;

    @ApiModelProperty("收款备注")
    @Excel(name = "收款备注")
    private String payeeRemark;

    @ApiModelProperty("转增值时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "转增值时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date toIncrementTime;

    @ApiModelProperty("纯记账/首次托管")
    @Excel(name = "纯记账/首次托管")
    private String firstTrusteeship;

    @ApiModelProperty("首次记账赠送月份")
    @Excel(name = "首次记账赠送月份")
    private String firstAccountMonth;

    @ApiModelProperty("企业首笔订单")
    @Excel(name = "企业首笔订单")
    private String firstOrder;

    @ApiModelProperty("是否退款")
    @Excel(name = "是否退款")
    private String isRefund;

    @Excel(name = "搁置状态")
    @ApiModelProperty("搁置状态")
    private String shelveStatus;

    @Excel(name = "服务单当前跟进部门")
    @ApiModelProperty("服务单当前跟进部门")
    private String serviceUserDept;

    @Excel(name = "服务单业务对接人")
    @ApiModelProperty("服务单业务对接人")
    private String serviceUserName;


    @Excel(name = "服务单当前服务节点")
    @ApiModelProperty("服务单当前服务节点")
    private String servicePointName;

    @Excel(name = "服务单当前节点状态")
    @ApiModelProperty("服务单当前节点状态")
    private String servicePointStatusName;

}
