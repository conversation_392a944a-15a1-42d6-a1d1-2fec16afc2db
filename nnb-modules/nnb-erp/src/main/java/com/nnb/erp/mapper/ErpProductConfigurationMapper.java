package com.nnb.erp.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.nnb.erp.domain.ErpProductConfiguration;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 产品库配置信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
public interface ErpProductConfigurationMapper
{
    /**
     * 查询产品库配置信息
     *
     * @param id 产品库配置信息主键
     * @return 产品库配置信息
     */
    public ErpProductConfiguration selectErpProductConfigurationById(Long id);

    /**
     * 查询产品库配置信息列表
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 产品库配置信息集合
     */
    public List<ErpProductConfiguration> selectErpProductConfigurationList(ErpProductConfiguration erpProductConfiguration);

    /**
     * 新增产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    public int insertErpProductConfiguration(ErpProductConfiguration erpProductConfiguration);

    /**
     * 修改产品库配置信息
     *
     * @param erpProductConfiguration 产品库配置信息
     * @return 结果
     */
    public int updateErpProductConfiguration(ErpProductConfiguration erpProductConfiguration);

    /**
     * 删除产品库配置信息
     *
     * @param id 产品库配置信息主键
     * @return 结果
     */
    public int deleteErpProductConfigurationById(Long id);
    public int deleteErpProductConfigurationByProductId(@Param("productId") Long productId);

    /**
     * 批量删除产品库配置信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErpProductConfigurationByIds(Long[] ids);

    @Select("select * from erp_product_configuration where product_id = ${productId} and configuration_status = 1 " +
            "and num_is_applet_show = 1 and enterprise_dominant = #{enterpriseDominant}")
    public ErpProductConfiguration selectByProductIdFromXcx(@Param("productId") Long productId, @Param("enterpriseDominant") Long enterpriseDominant);

    @Select("select * from erp_product_configuration where product_id = #{productId} and FIND_IN_SET(#{deptId}, dept_id)")
    public List<ErpProductConfiguration> selectProductByDeptId(@Param("productId") Long productId, @Param("deptId") Long deptId);

    @Select("SELECT out_cost FROM erp_product_detail WHERE num_product_id = #{productId}")
    public BigDecimal selectOutCostByProductId(@Param("productId") Long productId);

    int updateGuidePrice(ErpProductConfiguration en);
}
