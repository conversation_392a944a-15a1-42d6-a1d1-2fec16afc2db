package com.nnb.erp.domain.vo.gift;

import com.nnb.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ErpOrderGiftIssueDetailVO {

    @ApiModelProperty("赠品ID")
    private Long giftId;

    @ApiModelProperty("赠品发放记录ID")
    private Long giftIssueRecordId;

    @ApiModelProperty("收货人")
    private String consignee;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("赠品类型,  1 :实物，2：服务产品")
    private Integer giftType;

    /** 赠品名称 */
    @ApiModelProperty("赠品名称")
    private String giftName;

    /** 产品ID */
    @ApiModelProperty("产品ID")
    private String productId;

    @ApiModelProperty("赠品价格")
    private BigDecimal giftPrice;

    @Excel(name = "剩余量")
    @ApiModelProperty("剩余量")
    private BigDecimal surplusAmount;
}
