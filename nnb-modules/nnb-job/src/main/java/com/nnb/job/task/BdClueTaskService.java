package com.nnb.job.task;


import com.nnb.common.core.domain.R;
import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.system.api.RemoteCustomerInsideService;
import com.nnb.system.api.RemoteCustomerService;
import com.nnb.system.api.domain.DingTalkMsgDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 定时任务-线索相关
 *
 * <AUTHOR>
 */
@Component("bdClueTaskService")
public class BdClueTaskService {

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private RemoteCustomerInsideService remoteCustomerInsideService;

    /**
     * 客保掉保 0 1 0 * * ?
     */
    public void refreshKb()
    {
        remoteCustomerService.refreshKb();
    }

    /**
     * 线索管理回收
     */
    public void refreshClueManagement()
    {
        remoteCustomerService.refreshClueManagement();
    }

    /**
     * 商机池流转下一商机池
     */
    public void refreshOpportunity()
    {
        remoteCustomerService.refreshOpportunity();
    }


    /**
     * 客户发展部  人工意向度是高意向的  加入到指定商机池
     */
    public void moveNichePool()
    {
        remoteCustomerService.moveNichePool();
    }

    /**
     * 线索中心、公海，根据自动流转配置下发线索管理（高意向，入过客保）
     */
    public void refreshAutoAssign()
    {
        remoteCustomerService.refreshAutoAssign();
    }

    /**
     * 自动分配跟进统计
     */
    public void refreshAutoAssignCount()
    {
        remoteCustomerService.refreshAutoAssignCount();
    }

    public void moveCenter(){
        remoteCustomerService.moveCenter();
    }

    public void moveDown(){remoteCustomerService.moveDown();}

    /**
     * 根据螳螂系统订单创建客保
     */
    public void createKbByTangLang(){
        remoteCustomerService.createKbByTangLang();
    }

    /**
     * 24小时内未填写跟进记录，线索流向意向池
     */
    public void refreshIntentionPool(){
        remoteCustomerService.refreshIntentionPool();
    }

    /**
     * 启照多30天内未操作提交审核、输单，线索强制流向意向池
     */
    public void refreshIntentionPoolByUnAudit(){
        remoteCustomerService.refreshIntentionPoolByUnAudit();
    }

    /**
     * 同步员工钉钉ID
     */
    public void updateUserDingID(){
        remoteCustomerService.updateUserDingID();
    }

    /**
     * 统计线索信息
     */
    public void statisticsBdClue(){remoteCustomerService.statisticsBdClue();}

    /**
     * 统计商机池接通率、客保率
     */
    public void statisticsNichePoolParam(){remoteCustomerService.statisticsNichePoolParam();}

    /**
     * 删除redis商机池、公海领取key
     */
    public void delClueCountRedis(){remoteCustomerService.delClueCountRedis();}

    /**
     * 推广线索线索管理掉入公海
     */
    public void refreshPopularizeClue(){remoteCustomerService.refreshPopularizeClue();}

    /**
     * 推广线索半小时未跟进ding一下
     */
    public void dingUnFollowPopularClue(){remoteCustomerService.dingUnFollowPopularClue();}

    /**
     * 每天一点定时把不在客保管理，线索管理 更新回到源企业主体
     */
    public void updateEnterpriseDominant(){remoteCustomerService.updateEnterpriseDominant();}

    /**
     * 每天两点半定时清洗分发数据
     */
    public void cleanData(Integer quantity, Integer cityId){remoteCustomerService.cleanData(quantity, cityId);}

    /**
     * 每半小时定时更新清洗分发数据为已拨打
     */
    public void updateCleanData(){remoteCustomerService.updateCleanData();}

    /**
     * 卡卡定时获取质检内容
     */
    public void getAsrTxt(){
        remoteCustomerService.getAsrTxt();
    }

    /**
     * 北斗 - AI外呼任务匹配北斗数据
     */
    public void syncAiClue(){
        remoteCustomerService.syncAiClue();
    }

    /**
     * 北斗内部 - 推广线索发送钉钉通知
     */
    public void sendDingTalkNotifications(Long clueId, Long bdCustomersFollowId, String userName,
                                          String bdGuestSrcName, String contactWay,
                                          String dingTalkUserId, Integer dingTalkType){
        remoteCustomerInsideService.sendDingTalkNotifications(
                clueId, bdCustomersFollowId, userName, bdGuestSrcName, contactWay, dingTalkUserId, dingTalkType
        );
    }

    /**
     * 北斗内部 - 推广线索移交经理
     */
    public void distributionClueManager(Long clueId, Long userId, Long bdCustomersFollowId){
        remoteCustomerInsideService.distributionClueManager(clueId, userId, bdCustomersFollowId);
    }

    /**
     * 北斗内部 - 推广线索经理未分配掉入公海
     */
    public void fallIntoTheSea(Long clueId, Long bdCustomersFollowId){
        remoteCustomerInsideService.fallIntoTheSea(clueId, bdCustomersFollowId);
    }

    /**
     * 北斗内部 - 推广线索数字部门掉入个人
     */
    public void fallIntoThePerson(Long clueId, Long userId, Long bdCustomersFollowId, Integer type){
        remoteCustomerInsideService.fallIntoThePerson(clueId, userId, bdCustomersFollowId, type);
    }

    /**
     * 北斗内部 - 推广线索数字部门掉入商机池
     */
    public void fallIntoTheNichePool(Long clueId, Long bdCustomersFollowId, Integer type){
        remoteCustomerInsideService.fallIntoTheNichePool(clueId, bdCustomersFollowId, type);
    }
}
