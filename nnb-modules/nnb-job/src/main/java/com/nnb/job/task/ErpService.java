package com.nnb.job.task;


import com.nnb.common.core.web.domain.AjaxResult;
import com.nnb.system.api.RemoteErpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定时任务-erp相关
 */
@Component("erpService")
public class ErpService {

    @Autowired
    private RemoteErpService remoteErpService;

    public void clearErpDiscountCouponAmount() {
        remoteErpService.clearAmount();
    }

    public void sendServiceMainMail(){
        remoteErpService.sendServiceMainMail();
    }

    public void followDistribution() { remoteErpService.followDistribution(); }

    public void erpOldEnterpriseFollowDistribution() { remoteErpService.erpOldEnterpriseFollowDistribution(); }

    public void filterErpOldEnterpriseFollow() { remoteErpService.filterErpOldEnterpriseFollow(); }

    public void serviceProductAgeing() { remoteErpService.serviceProductAgeing(); }

    public void giveVoucherRemind() { remoteErpService.giveVoucherRemind(); }

    public void scheduledSubscriptionMsg(String strategyId) { remoteErpService.scheduledSubscriptionMsg(strategyId); }

    public void scheduledPaymentReminder() { remoteErpService.scheduledPaymentReminder(); }

    public void scheduledSendEmailFile() { remoteErpService.scheduledSendEmailFile(); }

    public void sendEmailAccount() { remoteErpService.sendEmailAccount(); }

    public void countNumberFollow() { remoteErpService.countNumberFollow(); }

    public void scheduledUpdateActivitiesReportStatus() { remoteErpService.scheduledUpdateActivitiesReportStatus(); }

    public void matchErpOldEnterpriseTag() { remoteErpService.matchErpOldEnterpriseTag(); }

    public void scheduledUpdateEnterPriseYearInspect() { remoteErpService.scheduledUpdateEnterPriseYearInspect(); }

    public void qualificationsExtensionLossTask() { remoteErpService.qualificationsExtensionLossTask(); }
}
