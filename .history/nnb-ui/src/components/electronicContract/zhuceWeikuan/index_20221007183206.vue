<template>
  <div>
    <div id="dz-contract-main" class="main-content dz-contract-main" :style="'font-size: 20px; width:'+ width +'; margin: 0 auto;padding: 20px'">
      <el-row class-name="header">
        <h3 style="text-align: right;">
          合同编号：<span class="contract-number">{{ orderDataMain.showNumber }}</span>
        </h3>
      </el-row>
      <h3 style="margin-left:300px">{{orderDataMain.contractName}}</h3>
      <div class="main">
        <div class="contract-head">
          <div class="partA">
            <p class="name">
              <span>甲方(委托方)：</span>
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstParty" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstParty }}</u>
            </p>
            <p>
              负责人姓名：
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.principalName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.principalName }}</u>
            </p>
            <p>
              联系电话：<span class="phone">{{ orderDataMain.contractContent.firstContactPhone }}</span>
            </p>
            <p>
              住  址:<input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstAddress" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstAddress }}</u>
            </p>
            <div />
          </div>
          <div class="partB">
            <p v-if="onlineContracts == 2" class="name">
              乙方：北京繁一企业管理咨询有限公司</p>
            <p v-if="onlineContracts == 1" class="name">
              乙方：北京小苗财税服务有限公司</p>
            <p v-if="onlineContracts == 3" class="name">
              乙方：北京后企之秀企业管理咨询有限公司</p>
            <p v-if="onlineContracts == 4" class="name">
              乙方：上海企苗企业管理咨询有限公司</p>
            <div>
              <p>
                销售姓名：
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.sellerName" type="text" style="font-size: 20px;width: 600px;">
                <u v-else>{{ orderDataMain.contractContent.sellerName }}</u>
              </p>
              <p>联系电话：010-62667788</p>
              <!-- <p>
              联系电话：<span class="phone">{{ orderDataMain.contractContent.secondtContactPhone }}</span>
            </p> -->
              <p>
              住  址:<input v-if="source == 'add'" v-model="orderDataMain.contractContent.secondAddress" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.secondAddress }}</u>
              </p>
            </div>
          </div>
          <p class="partTips block">
            甲、乙双方根据《中华人民共和国民法典》和《中华人民共和国会计法》及其他相关的法律法规的规定，本着平等公平、诚实信用、互惠互利的原则，就合作事宜达成如下内容，以资共同遵守。
          </p>
        </div>
        <div class="contract-content">
          <div class="contract-rule">
            <div class="rules">
              <b class="font-28">第一条 委托事项</b>
              <div class="font-28">
                <p>1.记账服务（预存）</p>
                <p>小规模-300元/月</p>
                <div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','1')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '1' ? checked : unchecked" alt="" />
                    <span>3000元（10个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','2')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '2' ? checked : unchecked" alt="" />
                    <span>3600元（1年）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','3')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '3' ? checked : unchecked" alt="" />
                    <span>6000元（20个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','4')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '4' ? checked : unchecked" alt="" />
                    <span>7200元（2年）</span>
                  </div>
                </div>
                  <p>一般纳税人-650元/月</p>
                  <div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','5')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '5' ? checked : unchecked" alt="" />
                    <span>6500元（10个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','6')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.keepAccountsService == '6' ? checked : unchecked" alt="" />
                    <span>7800元（1年）</span>
                  </div>
                </div>
              </div>
              <p class="font-28">注：1.新注册客户代理记账服务按营业执照成立日期的次月开始计算，纯记账客户具体记账日期以我司会计下发的记账服务合同补充条款记账日期为准。</p>
              <p class="font-28">2.次年记账续费无需另行签订合同，本协议对双方继续有效。</p>
               <el-table
                    :data="tableData"
                    border
                    style="width: 100%">
                    <el-table-column
                      prop="project"
                      label="会员服务项目"
                      width="120">
                    </el-table-column>
                    <el-table-column
                      prop="content"
                      label="基础服务内容">
                    </el-table-column>
                  </el-table>
              <p>2.注册服务</p>
               <span>注册区域：</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.registrationDistrict" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.registrationDistrict}}</u>
               <!-- <input v-model="orderDataMain.contractContent.registrationDistrict" type="text" style="font-size: 20px;width: 200px;"> -->
                <span>主营业务:</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.mainBusiness" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.mainBusiness}}</u>
               <span>费  用:</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.charge" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.charge}}</u>
               <!-- <input v-model="orderDataMain.contractContent.charge" type="text" style="font-size: 20px;width: 200px;"> -->
               <div>
                  <div class="flexMode vc" @click="changetypeRadio('chargeType','1')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.chargeType == '1' ? checked : unchecked" alt="" />
                    <span>每年收费</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('chargeType','2')">
                    <img :src="orderDataMain.contractContent.contractDetailObject.chargeType == '2' ? checked : unchecked" alt="" />
                    <span>一次性收费</span>
                  </div>
                </div>
                 <p class="font-28">注： ①地址价格以当年政策和市场行情为准；</p>
                 <p class="font-28">②注册交付：五章（公章、合同章、财务章、发票章、法人章）一照（营业执照正副本）</p>
                <p>3.其他服务</p>
               <div><span>其他服务1：</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceName" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceName}}</u>
                <span>服务费:</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceCharge" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceCharge}}</u></div>
               <div><span>其他服务2：</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceNameSecond" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceNameSecond}}</u>
                <span>服务费:</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceChargeSecond" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceChargeSecond}}</u></div>
               <div><span>其他服务3：</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceNameThird" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceNameThird}}</u>
                <span>服务费:</span> 
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.contractDetailObject.serviceChargeThird" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.serviceChargeThird}}</u></div>
              <p class="font-28">
                <b>第二条 费用支付</b>
              </p>
              <p>
                甲方需支付的总服务费(大写)共
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.sumServiceChageCapital" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.sumServiceChageCapital }}</u>
                元，小写
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.sumServiceChageLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.sumServiceChageLowerCase }}</u>元，</p>
              <p>
               甲方在签订本合同后支付人民币
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.payerMoneyCapital" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.payerMoneyCapital }}</u>
                元，小写
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.payerMoneyLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.payerMoneyLowerCase }}</u>元，
              </p>
              <p>
               尾款金额（大写）
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.balancePaymentCapital" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.balancePaymentCapital }}</u>
                元，小写
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.balancePaymentLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.balancePaymentLowerCase }}</u>元，
              </p>
              <p>对公账户信息：</p>
              <div v-if="onlineContracts == 2">
                <p>公司名称：北京繁一企业管理咨询有限公司</p>
                <p>开户行：工商银行北京羊坊店支行</p>
                <p>帐号：0200282209200020012</p>
              </div>
              <div v-if="onlineContracts == 1">
                <p>公司名称：北京小苗财税服务有限公司</p>
                <p>开户行：中国建设银行北京北环支行</p>
                <p>帐号：11050162540000000320</p>
              </div>
              <div v-if="onlineContracts == 3">
                <p>公司名称：北京后企之秀企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司北京海淀黄庄支行</p>
                <p>帐号： 110941339410601</p>
              </div>
              <div v-if="onlineContracts == 4">
                <p>公司名称：上海企苗企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司上海嘉定支行</p>
                <p>账号：121927216110701</p>
              </div>
              <div class="font-22">
                <p class="font-28">
                  <b>第三条  关于记账服务的权利义务</b>
                </p>
                <ZhuceJizhang />
                <p class="font-28">
                  <b>第四条 违约责任</b>
                </p>
                  <p>
                    1.任何一方如违反本合同的规定给对方造成损失，按照《中华人民共和国民法典》及其相关法律的规定，承担违约责任；
                  </p>
                <p>
                  2.本合同有效期内，甲方因记账代理问题违约解除合同，其中代理记账服务费全额扣除乙方不予退费；甲方因注册地址及其他非代理记账问题单方面违约解除合同，需向乙方支付乙方已进行成本价+合同价款20%作为违约金；
                </p>
                <p>
                  3.甲方无特殊原因，未支付乙方当期代理服务费，乙方次月不再继续为甲方提供账务处理、纳税申报等代理服务工作，由此而引起的税务机关等相关部门的罚款由甲方负全部责任；
                </p>
                <p>4.甲乙双方应严格按照规定时间节点履行相关办理业务的续费义务与相关业务办理落地执行完结义务，如因一方拒不履行相关义务，由此造成的一切后果与责任由一方独自承担，对方不承担由此造成的任何后果及法律责任； </p>
                <p>
                  5.如因不可抗力（包括但不限于政策变动，市场调整等）导致地址无法继续使用，乙方不承担任何责任。
                </p>
                <p class="font-28">
                  <b>第五条 争议解决</b>
                </p>
                <p>在本合同履行过程中若发生争议，双方应协商解决，当协商不成时，任何一方均可向乙方所在地人民法院提起诉讼。 </p>
                <p class="font-28">
                  <b>第六条 其他</b>
                </p>
                <p>1.合同自甲乙双方签字盖章之日起生效，本合同一式两份，双方各持一份，具有同等效力。 </p>
                <p>2.本合同及相关补充协议全部内容均为书面文件，除甲乙双方授权代表签字或盖章，其它手写、口述、聊天记录内容均无效。请认真阅读合同内容，由手写、口述、聊天记录等书面文件之外的部分产生的歧义，概不做为合同依据，乙方概不负责。</p>
                <p>3.如需异地签署或往来沟通，短信、QQ 聊天记录、微信聊天记录、邮件往来等电子数据均具有同等法律效力。 </p>
              </div>
            </div>
            <div class="signature" style="margin-bottom: 40px;">
              <div class="signA" style="width: 50%;float: left;">
                <div class="signAImg">甲方：{{ orderDataMain.contractContent.firstParty }}</div>
                <div class="signAImg" style="position: relative;">授权代表：{{ orderDataMain.contractContent.principalName }}
                  <i v-if="!resultImg && source =='mobile'" style="cursor: pointer;" @click="signBoard">签字区</i>
                  <img v-if="resultImg" style="width: 100px;position: absolute;" :src="resultImg" class="avatar" @click="signBoard" />
                </div>
                <div class="signAImg">盖章：</div>
                <div class="time-mes">
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.date}}</div> -->
                </div>
              </div>
              <div class="signB" style="width: 50%;float: right;position: relative;">

                <div v-if="onlineContracts == 2" class="signBImg">
                  乙方（盖章）：北京繁一企业管理咨询有限公司</div>
                <!-- 此处需要判断地理id ，显示北京小苗或者河南z苗 -->
                <div v-if="onlineContracts == 1" class="signBImg">
                  乙方（盖章）：北京小苗财税服务有限公司</div>
                <div v-if="onlineContracts == 4" class="signBImg">
                  乙方：上海企苗企业管理咨询有限公司</div>
                <img v-if="source != 'add'" style="position: absolute;width: 100px;left: 120px;" :src="onlineContracts == 1?Seal:onlineContracts == 2?FY_Seal:HQ_Seal" />
                <!-- <div class='signBImg'
                  v-if="onlineContracts == 1">
                  乙方（盖章）：河南助苗企业管理咨询有限公司</div> -->

                <div v-if="onlineContracts == 3" class="signBImg">乙方（盖章）：北京后企之秀企业管理咨询有限公司</div>
                <div class="">授权代表：{{ orderDataMain.contractContent.sellerName }}</div>
                <div class="signAImg">盖章：</div>
                <div class="time-mes  time-mesB">
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.date}}</div> -->
                </div>
              </div>
              <div style="clear: both;" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div>
      <signTure
        :dz-city-id="dzCityId"
        :phone-number="phoneNumber"
        :online-contract="onlineContracts"
        :source="source"
        :order-id="orderId"
        :order-data-main="orderDataMain"
        :width="'900px'"
        @close="closeFileForm"
      />
    </div> -->
  
    <el-dialog
      title="电子签名"
      append-to-body
      width="70%"
      :modal-append-to-body="false"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <el-card class="qianming-container" body-style="padding:0px">
        <vue-esign ref="esign" :is-crop="isCrop" :width="source == 'mobile'?300:600" :height="300" :line-width="lineWidth" :line-color="lineColor" :bg-color.sync="bgColor" />
        <div class="contro-container">
          <el-button type="danger" @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleGenerate">保存</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisibleCode"
      :modal-append-to-body="false"
      width="30%"
      :before-close="handleCloseCode"
    >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import FY_Seal from '../../../assets/images/FY_seal.png';
import Seal from '../../../assets/images/seal.png';
import HQ_Seal from '../../../assets/images/HQ_Seal.png';
import {client} from '@/utils/alioss';
import ZhuceJizhang from './zhucejizhang.vue'
import signTure from '../chip/signaTure.vue'
import checked from '@/assets/images/checked.jpg'
import unchecked from '@/assets/images/unchecked.jpg'
import {
  checkoutContract, genPdf, getPdf, sendMessage, checkValidCode
} from '@/api/contract/electronicContract';
export default {
  name: 'Edit',
  components:{
    ZhuceJizhang,
    signTure
  },
  props: {
    productData: {
      type: Array,
      default() {
        return [];
      },
    },
    customData: {
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      type: String,
      default() {
        return '';
      },
    },
    onlineContract: {// 1 牛牛帮 2 繁一 3 后企
      type: String,
      default() {
        return '';
      },
    },
    dzCityId: {
      type: String,
      default() {
        return '';
      },
    },
    source: {// add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      type: String,
      default() {
        return '';
      },
    },
    width: {
      type: String,
      default() {
        return '';
      },
    },
    orderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checked,
      unchecked,
      orderDataMain:{},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      HQ_Seal: HQ_Seal,
      FY_Seal: FY_Seal,
      Seal: Seal,
      dialogVisibleCode: false,
      signCode: '',
      xiaoguimo:'',
      nashuiren:'',
      tableData:[
        {
          project:'记账服务',
          content:'1、账套初始化建立，日常账务处理 2、出具资产负债表、利润表、现金流量表3、国地税综合申报及明细申报（进出口企业不负责出口退税）；4、通过企业信用信息公示系统向工商行政管理部门报送上年度报告(其他日常信息变更由企业自行公示)；5、企业所得税年度汇算清缴；6、配合协助税务调查。'
        }
      ],
      otherServiceList:[],
      disabled: false,
      codeText: "获取验证码",
      onlineContracts:'',
      year:'',
      month:'',
      day:'',
    };
  },
  mounted: function() {

  },
  created() {
    // 用户短信入口orderId从url中取值
    // 进去电子合同初始化
    // console.info('souces', this.source);
    // if (this.source == 'add') { // 提单新增电子合同
    //   this.getInitData();
    // } else { // 其他状态
    //   this.getInitViewData();
    // }
    // 电子合同主体，页面判断使用
    let date = new Date()
    this.year = date.getFullYear()
    this.month = Number(date.getMonth()) + 1
    this.day = date.getDate()
    this.onlineContracts = this.onlineContract;
  },
  watch:{
     orderData: {
      immediate: true,
      handler: function (n) {
        if (n) {
          this.orderDataMain = JSON.parse(JSON.stringify(n))
          this.orderDataMain.contractContent.contractDetailObject = this.orderDataMain.contractContent.contractDetailObject || {}
          this.orderDataMain.contractContent.firstContactPhone = this.phoneNumber;
          this.orderDataMain.contractContent.secondParty = this.orderDataMain.contractContent.secondParty ||this.$store.state.user.userInfo.dept.deptName;
          this.orderDataMain.contractContent.sellerName = this.orderDataMain.contractContent.sellerName || this.$store.state.user.userInfo.userName;
        }
      },
    },
  },
  methods: {
    changetypeRadio(k,v){
      const radio = this.orderDataMain
      radio[k] = radio[k]=== v ? '': v 
      this.orderDataMain = {...this.orderDataMain,...radio,}
    },
   emitData(){
      return this.orderDataMain
    },
    // 签约成功之后，生成pdf 文件
    getHtml() {
      let html = document.getElementById('dz-contract-main').innerHTML;
      // html就是整个合同文字内容
      // console.log(html)
      // genPdf接口报500
      genPdf({orderId: this.orderId, code: html}).then(response => {
        if(response.code == 200){
          this.$router.go(-1)
        }
      });
    },
    // 电子签名弹框打开
    signBoard() {
      this.dialogVisible = true;
    },
    // 电子签名弹框隐藏
    handleClose() {
      this.dialogVisible = false;
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 生成签名图片..
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        console.log(res,'qianming')
        let randnum = Math.random() * 10000000000000;
        randnum = Math.floor(randnum);
        // let fileName = "dianziqianming/" + randnum + '.png'
        let fileName = randnum + '.png';
        let file = this.dataURLtoFile(res, fileName);
        console.log(fileName, 'fileName');
        console.log(file, 'file');
        let date = new Date();
        let dateYear = date.getFullYear(); // 获取年
        let dateMonth = date.getMonth() + 1; // 获取月
        let dateDate = date.getDate(); // 获取当日
        dateMonth = dateMonth > 9 ? dateMonth : '0' + dateMonth;
        dateDate = dateDate > 9 ? dateDate : '0' + dateDate;
        let dir = 'information/' + dateYear + dateMonth + dateDate + '/' + fileName;
        file.uid = '1657869457487';
        client().multipartUpload(dir, file).then(res => {
          console.log(res, 'res');
          this.resultImg = res.res.requestUrls[0];
          this.dialogVisible = false;
        }).catch(err => {});
      }).catch(err => {
        this.$message.error('请签名之后提交！');
      });
    },
    // 将base64转换为文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','); var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]); var n = bstr.length; var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {type: mime});
    },
    // 提单 传递电子合同数据    ++++ 需要传递自填的数据 this.contractDetailObject
    // submit() {
    //   // this.$emit('getSignData', this.orderData);
    //   this.$emit('getSignData', this.orderDataMain);
    //   // this.$emit('getSignData',this.contractDetailObject);
    // },
    // 新增初始化
    // getInitData() {
    //   let arr = [];
    //   this.productData.map((item, index) => {
    //     arr.push({
    //       'productId': item.productId,
    //       'typeId': item.typeId,
    //       'totalPrice': item.totalPrice,
    //       'payPrice': item.payPrice,
    //       'numNameId':item.numNameId
    //     });
    //   });
    //   checkoutContract({products: arr,'clientId':this.customData.clientId}).then(response => {
    //     console.log('checkout', response);
    //     // this.orderData = response.data[0];
    //     // this.orderData.contractContent
    //     this.contractDetailObject = response.data[0].contractContent;
    //     // this.contractDetailObject.firstParty = this.customData.clientName;
    //     this.contractDetailObject.firstContactPhone = this.phoneNumber;
    //     this.contractDetailObject.yfSignPhone = '010-62667788';
    //     this.contractDetailObject.sellerName = this.contractDetailObject.sellerName || this.$store.state.user.userInfo.userName;
    //     // console.log(this.orderData,'orderData')

    //   });
    // },
    // 其他状态初始化
    // getInitViewData() {
    //   // 别的数据有问题，暂时先写死id 走流程
    //   // getPdf({orderId:this.orderId}).then(response=>{
    //   getPdf({orderId: '1'}).then(response => {
    //     console.log(response,'res')
    //     // this.orderDataMain = response.data[0];
    //     this.contractDetailObject = response.data[0].contractContent;
    //     this.contractDetailObject.sellerName = this.contractDetailObject.sellerName || this.$store.state.user.userInfo.userName;
    //     // this.onlineContracts = this.orderDataMain.contractSubject;
    //     this.onlineContracts = this.contractDetailObject.contractSubject;
    //   });
    // },
    // 经理审批同意，发送短信
    submitSend() {
      console.log('经理审批同意，发送短信');
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('close');
        // 别的数据有问题，暂时先写死id 走流程
        // const data = {
        //   orderId: this.orderId,
        //   phone: this.contractDetailObject.firstContactPhone,
        //   ...this.contractDetailObject,
        //   ...this.otherService
        // }
        // sendMessage({orderId: this.orderId, phone: this.orderDataMain.contractContent.firstContactPhone}).then(response=>{
        // sendMessage(data).then(response=>{
        sendMessage({orderId: '1', phone: '18211193616', contractSubject: this.onlineContracts}).then(response => {
          if (response.code == 200) {
            //  移动端跳转地址,接口没有，暂时先本地跳转看页面
            window.open(window.location.protocol + '//' + window.location.host + '/sendEmail?orderId=' + this.orderId);
          }
        });
      }).catch(() => {

      });
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 驳回
    submitReject() {
      // 暂时没有接口
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.orderDataMain.contractContent.firstContactPhone}).then(response=>{
    // console.log(this.signCode,'signCode')
    checkValidCode({code: this.signCode, phone: '17854561089'}).then(response => {
        if (response.code == 200) {
          this.getHtml();
          this.dialogVisibleCode = false;
        }
      });
    },
    // 获取验证码
    getSignCode() {
       let maxNum = 60;
        let oldCodeText = this.codeText;
        this.codeText = `${maxNum}s重新发送`; //初始显示倒计时
        let codeCountDown = setInterval(() => {
          this.disabled = true;
          let countDownNum = maxNum--;
          this.codeText = `${countDownNum}s重新发送`;
          if (countDownNum == 0) {
            //倒计时结束
            this.codeText = oldCodeText;
            clearInterval(codeCountDown);
            this.disabled = false;
          }
        }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderDataMain.contractContent.firstContactPhone}).then(response=>{
      sendMessage({phone: '17854561089', contractSubject: this.onlineContracts}).then(response => {
        if (response.code == 200) {

        }
      });
    }
  }
};
</script>
