<template>
  <div>
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="title"
      width="1200px"
      :visible.sync="open"
      append-to-body
      :before-close="handleDialogClose"
    >
      <div>
        <div>
          <!-- 添加判断，一共13个合同类型，现在只有资质，这里是入口，从这里判断进入那个合同 -->
          <zizhi
            ref="elecontract8"
            v-if="contractType.includes(8)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataBa"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <zhuce
            ref="elecontract2"
            v-if="contractType.includes(2)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataEr"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <zhuceWeikuan
            ref="elecontract1"
            v-if="contractType.includes(1)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataYi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <daili
           ref="elecontract9"
            v-if="contractType.includes(9)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataJiu"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <shuikong
           ref="elecontract4"
            v-if="contractType.includes(4)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <zhuxiao
           ref="elecontract6"
            v-if="contractType.includes(6)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataLiu"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <guquan
           ref="elecontract7"
           v-if="contractType.includes(7)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataQi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <dizhi
           ref="elecontract10"
           v-if="contractType.includes(10)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataShi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <jizhang
           ref="elecontract3"
            v-if="contractType.includes(3)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSan"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <jizhangVip
           ref="elecontract5"
            v-if="contractType.includes(5)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataWu"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <jianzhu
           ref="elecontract11"
            v-if="contractType.includes(11)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSy"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <shangbiao
           ref="elecontract12"
            v-if="contractType.includes(12)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSe"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <oldZhuan
           ref="elecontract16"
            v-if="contractType.includes(16)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSliu"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
         <div>
          <newZhuan
           ref="elecontract17"
            v-if="contractType.includes(17)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSqi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <newZizhi
           ref="elecontract15"
            v-if="contractType.includes(15)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSwu"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <newJianzhu
           ref="elecontract14"
            v-if="contractType.includes(14)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSsi"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <shougou
           ref="elecontract13"
            v-if="contractType.includes(13)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSs"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <zhuceSH
           ref="elecontract16"
            v-if="contractType.includes(16)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSl"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
        <div>
          <jizhangSH
           ref="elecontract17"
            v-if="contractType.includes(17)"
            :dz-city-id="dzCityId"
            :product-data="productData"
            :custom-data="customData"
            :phone-number="phoneNumber"
            :online-contract="onlineContracts"
            :source="source"
            :order-id="orderId"
            :order-data="orderDataSq"
            :width="'900px'"
            @close="closeFileForm"
          />
        </div>
      <div id="remove">
        <div className="submit" style="text-align: center;margin-top: 30px;">
          <button v-if="source == 'add'" className="submit-btn" 
          style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;" 
          @click="submit" :loading="submitLoading">
            提单
          </button>
          <button v-if="source == 'send'" className="submit-btn" 
          style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;" 
          @click="submitSend()">
            以上合同确认无误，发送客户
          </button>
          <!-- 短信签约 -->
          <button v-if="source == 'mobile'" className="submit-btn" 
          style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;margin-bottom: 20px;" 
          @click="submitSign">
            以上合同确认无误，提交
          </button>
          <button v-if="source == 'mobile'" className="submit-btn" 
          style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;margin-bottom: 20px;" 
          @click="submitReject">
            驳回
          </button>
          <!-- 生成pdf文件方法，已完成 -->
          <!-- <button @click="getHtml">获取dom</button> -->
        </div>
      </div>
       <el-dialog
          title="提示"
          :visible.sync="dialogVisibleCode"
          :modal-append-to-body="false"
          width="30%"
          :before-close="handleCloseCode"
        >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
    </div>
    </el-dialog>
  </div>
</template>

<script>
import zizhi from './zizhi/index';
import zhuce from './zhuce/index';
import daili from './daili/index';
import zhuceWeikuan from './zhuceWeikuan/index';
import shuikong from './shuikong/index';
import zhuxiao from './zhuxiao/index';
import guquan from './guquan/index';
import dizhi from './dizhi/index';
import jizhang from './jizhang/index';
import oldZhuan from './oldZhuan/index';
import newZhuan from './newZhuan/index';
import jizhangVip from './jizhangVip/index';
// import gongshang from './gongshang/index';
import jianzhu from './jianzhu/index';
import shangbiao from './shangbiao/index';
import newZizhi from './newZizhi/index';
import newJianzhu from './newJianzhu/index';
import shougou from './shougou/index';
import jizhangSH from './jizhangSH/index';
import zhuceSH from './zhuceSH/index';
import { 
  checkoutContract,
  genPdf,
  getPdf,
  sendMessage,
  checkValidCode, } from '@/api/contract/electronicContract';

export default {
  name: 'ElectronicContract',
  components: {
    zizhi,
    zhuce,
    daili,
    zhuceWeikuan,
    shuikong,
    zhuxiao,
    guquan,
    dizhi,
    jizhang,
    oldZhuan,
    newZhuan,
    jizhangVip,
    // gongshang,
    jianzhu,
    shangbiao,
    newZizhi,
    newJianzhu,
    shougou,
    jizhangSH,
    zhuceSH
  },
  props: {
    title: {
      type: String,
      default() {
        return '合同';
      },
    },
    open: {
      type: Boolean,
      default() {
        return false;
      },
    },
    source: {
      // add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      // 订单id
      type: String,
      default() {
        return '';
      },
    },
    type: {
      // 合同类型
      type: String,
      default() {
        return '';
      },
    },
    orderDataTotal: {
      // 选择商品数据
      type: Array,
      default() {
        return [];
      },
    },
    productData: {
      // 选择商品数据
      type: Array,
      default() {
        return [];
      },
    },
    activityProductData: {
      // 选择商品数据
      type: Array,
      default() {
        return [];
      },
    },
    radio: {
      // 选择商品数据
      type: String,
      default() {
        return '1';
      },
    },
    onlineContracts: {
      // 1 牛牛帮 2 繁一 3 后企 4 上海起苗
      type: [String , Number],
      default() {
        return '' ;
      },
    },
    dzCityId: {
      // 电子合同所选地址id
      type: String,
      default() {
        return '';
      },
    },
    paymentData: {
      // 签约收款信息
      type: Object,
      default() {
        return {};
      },
    },
    customData: {
      // 客户信息
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      // 电话号码
      type: String,
      default() {
        return '';
      },
    },
    numNameId: {
      //
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      // contractType: '',
      contractType: [],
      orderData:{},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      dialogVisibleCode: false,
      signCode: '',
      disabled: false,
      codeText: '获取验证码',
      submitLoading:false,
      // onlineContracts:'',
    };
  },
  watch: {
    customData: {
      immediate: true,
      handler: function (n) {
        if (n) {
        }
      },
    },
    onlineContracts: {
      immediate: true,
      handler: function (n) {
        if (n) {
        }
      },
    },
  },
  created() {
    if (this.source == 'add') {
      // 提单新增电子合同
      this.getInitData();
    } else {
      //其他状态下
      this.getInitViewData();
    }
  },
  mounted() {
    console.log(this.phoneNumber,this.onlineContracts);
    // console.log(this.orderData.contractContent);
  },
  methods: {
    getInitData() {
      let arr = [];
        this.orderDataTotal.map((item, index) => {
          arr.push({
            productId: item.productId,
            typeId: item.typeId,
            totalPrice: item.totalPrice,
            payPrice: item.payPrice,
            numNameId: item.numNameId,
            lastPrice: item.lastPrice,
            vcAreaNames: item.region,
          });
        });
      checkoutContract({
        products: arr,
        clientId: this.customData.clientId,
        clueId:this.$route.query.clueId,
        signerId:this.paymentData.userId,
        contractSubject: this.onlineContracts, // 电子合同主体
        // payerId:''
      }).then((response) => {
        if(response.data){
          response.data.map(item=>{
          // this.orderData.push(item),
          this.contractType.push(item.contractType)
         })
        }
        // const data = response.data[0];
        const data1 = response.data.filter(item=>{
          return item.contractType == 1
        });
        const data2 = response.data.filter(item=>{
          return item.contractType == 2
        });
        const data3 = response.data.filter(item=>{
          return item.contractType == 3
        });
         const data4 = response.data.filter(item=>{
          return item.contractType == 4
        });
         const data5 = response.data.filter(item=>{
          return item.contractType == 5
        });
         const data6 = response.data.filter(item=>{
          return item.contractType == 6
        });
         const data7 = response.data.filter(item=>{
          return item.contractType == 7
        });
         const data8 = response.data.filter(item=>{
          return item.contractType == 8
        });
         const data9 = response.data.filter(item=>{
          return item.contractType == 9
        });
         const data10 = response.data.filter(item=>{
          return item.contractType == 10
        });
         const data11 = response.data.filter(item=>{
          return item.contractType == 11
        });
         const data12 = response.data.filter(item=>{
          return item.contractType == 12
        });
         const data13 = response.data.filter(item=>{
          return item.contractType == 13
        });
         const data14 = response.data.filter(item=>{
          return item.contractType == 14
        });
        const data15 = response.data.filter(item=>{
          return item.contractType == 15
        });
        const data16 = response.data.filter(item=>{
          return item.contractType == 16
        });
        const data17 = response.data.filter(item=>{
          return item.contractType == 17
        });

        // 判断合同类型
        // this.contractType = data.contractType;
        this.orderDataYi = data1[0]
        this.orderDataEr = data2[0]
        this.orderDataSan = data3[0]
        this.orderDataSi = data4[0]
        this.orderDataWu = data5[0]
        this.orderDataLiu = data6[0]
        this.orderDataQi = data7[0]
        this.orderDataBa = data8[0]
        this.orderDataJiu = data9[0]
        this.orderDataShi = data10[0]
        this.orderDataSy = data11[0]
        this.orderDataSe = data12[0]
        this.orderDataSs= data13[0]
        this.orderDataSsi = data14[0]
        this.orderDataSwu = data15[0]
        this.orderDataSl = data16[0]
        this.orderDataSq = data17[0]
      });
    },
    getInitViewData() {
      // 别的数据有问题，暂时先写死id 走流程
      getPdf({orderId:this.orderId}).then(response=>{
      // getPdf({ orderId: '1' }).then((response) => {
        if(response.data){
          response.data.map(item=>{
          this.contractType.push(item.contractType)
         })
        }
        const data1 = response.data.filter(item=>{
          return item.contractType == 1
        });
        const data2 = response.data.filter(item=>{
          return item.contractType == 2
        });
        const data3 = response.data.filter(item=>{
          return item.contractType == 3
        });
         const data4 = response.data.filter(item=>{
          return item.contractType == 4
        });
         const data5 = response.data.filter(item=>{
          return item.contractType == 5
        });
         const data6 = response.data.filter(item=>{
          return item.contractType == 6
        });
         const data7 = response.data.filter(item=>{
          return item.contractType == 7
        });
         const data8 = response.data.filter(item=>{
          return item.contractType == 8
        });
         const data9 = response.data.filter(item=>{
          return item.contractType == 9
        });
         const data10 = response.data.filter(item=>{
          return item.contractType == 10
        });
         const data11 = response.data.filter(item=>{
          return item.contractType == 11
        });
         const data12 = response.data.filter(item=>{
          return item.contractType == 12
        });
         const data13 = response.data.filter(item=>{
          return item.contractType == 13
        });
         const data14 = response.data.filter(item=>{
          return item.contractType == 14
        });
        const data15= response.data.filter(item=>{
          return item.contractType == 15
        });
        const data16 = response.data.filter(item=>{
          return item.contractType == 16
        });
        const data17 = response.data.filter(item=>{
          return item.contractType == 17
        });
        // 判断合同类型
        // this.contractType = data.contractType;
        this.orderDataYi = data1[0]
        this.orderDataEr = data2[0]
        this.orderDataSan = data3[0]
        this.orderDataSi = data4[0]
        this.orderDataWu = data5[0]
        this.orderDataLiu = data6[0]
        this.orderDataQi = data7[0]
        this.orderDataBa = data8[0]
        this.orderDataJiu = data9[0]
        this.orderDataShi = data10[0]
        this.orderDataSy = data11[0]
        this.orderDataSe = data12[0]
        this.orderDataSs= data13[0]
        this.orderDataSsi = data14[0]
        this.orderDataSwu = data15[0]
        this.orderDataSl = data16[0]
        this.orderDataSq = data17[0]
      });
    },
    // 关闭电子合同弹框
    closeFileForm() {
      this.$emit('closePopUp');
    },
    // 关闭电子合同弹框
    handleDialogClose() {
      this.$emit('closePopUp');
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 提单 传递电子合同数据
    submit() {
      // this.submitLoading = true
      let data = []
      for(let key in this.$refs){
        data.push(this.$refs[key].emitData())
      }
      // console.log(data);
      this.$emit('submitForm',data);
    },
    // 经理审批同意，发送短信
    submitSend() {
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$emit('close');
          // 别的数据有问题，暂时先写死id 走流程
          // sendMessage({orderId: this.orderId, phone: this.phoneNumber;}).then(response=>{
          sendMessage({
            // orderId: '1',
            orderId: this.orderId,
            phone: this.phoneNumber,
            // phone: '15936569250',
            contractSubject: this.onlineContracts,
          }).then((response) => {
            if (response.code == 200) {
              //  移动端跳转地址,接口没有，暂时先本地跳转看页面
              // window.open(
              //   window.location.protocol +
              //     '//' +
              //     window.location.host +
              //     '/sendEmail?orderId=' +
              //     this.orderId
              // );
              this.$router.push({
                path: '/sendEmail',
                query: {
                  orderId: this.orderId, 
                  onlineContracts: this.onlineContracts,
                  phoneNumber:this.phoneNumber
                },
              });
              this.$emit('orderListRefench')
              this.$message.success('发送成功');
              
              // this.source = 'mobile'
            }
          });
        })
        .catch(() => {});
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.phoneNumber}).then(response=>{
      // console.log(this.signCode,'signCode')
      checkValidCode({ code: this.signCode, phone: this.phoneNumber}).then(
        (response) => {
          if (response.code == 200) {
            // this.getHtml();
            for(let key in this.$refs){
              this.$refs[key].getHtml();
            }
            this.dialogVisibleCode = false;
          }
        },
      );
    },
    // 获取验证码
    getSignCode() {
      let maxNum = 60;
      let oldCodeText = this.codeText;
      this.codeText = `${maxNum}s重新发送`; //初始显示倒计时
      let codeCountDown = setInterval(() => {
        this.disabled = true;
        let countDownNum = maxNum--;
        this.codeText = `${countDownNum}s重新发送`;
        if (countDownNum == 0) {
          //倒计时结束
          this.codeText = oldCodeText;
          clearInterval(codeCountDown);
          this.disabled = false;
        }
      }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderData.contractContent.jfContactPhone}).then(response=>{
      sendMessage({
        // phone: '17854561089',
        phone:this.phoneNumber,
        contractSubject: this.onlineContracts,
      }).then((response) => {
        if (response.code == 200) {
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss"></style>
