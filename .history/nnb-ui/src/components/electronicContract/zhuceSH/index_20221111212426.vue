<template>
  <div>
    <div id="dz-contract-main" class="main-content dz-contract-main" :style="'font-size: 20px; width:'+ width +'; margin: 0 auto;padding: 20px'">
      <el-row class-name="header">
        <h3 style="text-align: right;">
          合同编号：<span class="contract-number">{{ orderDataMain.showNumber }}</span>
        </h3>
      </el-row>
      <h3 style="margin-left:300px">{{orderDataMain.contractName}}</h3>
      <div class="main">
        <div class="contract-head">
          <div class="partA">
            <p class="name">
              <span>甲方：</span>
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstParty" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstParty }}</u>
            </p>
            <p>
              负责人姓名：
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.principalName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.principalName }}</u>
            </p>
            <p>
              联系电话：<span class="phone">{{ orderDataMain.contractContent.firstContactPhone }}</span>
            </p>
            <p>
              住  址:<input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstAddress" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstAddress }}</u>
            </p>
          </div>
          <div class="partB">
            <!-- <p v-if="onlineContracts == 2" class="name">
              乙方：北京繁一企业管理咨询有限公司</p>
            <p v-if="onlineContracts == 1" class="name">
              乙方：北京小苗财税服务有限公司</p>
            <p v-if="onlineContracts == 3" class="name">
              乙方：北京后企之秀企业管理咨询有限公司</p> -->
            <p>
              乙方：上海企苗企业管理咨询有限公司</p>
            <div>
              <p>
                销售姓名：
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.sellerName" type="text" style="font-size: 20px;width: 600px;">
                <u v-else>{{ orderDataMain.contractContent.sellerName }}</u>
              </p>
              <!-- <p>联系电话：010-62667788</p> -->
              <p>
              联系电话：<span class="phone">{{ orderDataMain.contractContent.secondtContactPhone || '010-62667788'}}</span>
            </p>
              <p>
             地址: <u >上海市嘉定区云谷路499号T3办公楼1105室</u>
              </p>
            </div>
          </div>
          <p class="partTips block">
            依据《中华人民共和国民法典》及其它相关的法律法规的规定，甲方因经营管理需要，委托乙方进行代理服务，为了维护当事人的合法权益，双方本着诚信、平等、互利之原则，经双方代表协商达成如下协议：
          </p>
        </div>
        <div class="contract-content">
          <div class="contract-rule">
            <div class="rules">
              <b class="font-28">一、 乙方为甲方提供如下服务：</b>
              <p>（一）服务价目</p>
              <b>1. 记账服务（预存）： </b>
              <p>本次记账自
                <input v-if="source == 'add'" v-model="contractDetailObject.startYear" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.startYear }}</u>年
                <input v-if="source == 'add'" v-model="contractDetailObject.startMonth" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.startMonth }}</u>月
                <input v-if="source == 'add'" v-model="contractDetailObject.startDay" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.startDay }}</u>日起,
                至<input v-if="source == 'add'" v-model="contractDetailObject.endYear" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.endYear }}</u>年
                <input v-if="source == 'add'" v-model="contractDetailObject.endMonth" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.endMonth }}</u>月
                <input v-if="source == 'add'" v-model="contractDetailObject.endDay" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.endDay }}</u>日截止；共
                <input v-if="source == 'add'" v-model="contractDetailObject.totalMonth" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.totalMonth }}</u>个月。</p>
              <div class="font-28">
                <p>小规模-300元/月</p>
                <div >
                  <div class="flexMode vc" @click="changetype('keepAccountsService','1')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '1' ? checked : unchecked" alt="" />
                    <span>3600/年</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','2')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '2' ? checked : unchecked" alt="" />
                    <span>7200/2年</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','3')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '3' ? checked : unchecked" alt="" />
                    <span>汇算清缴300元/次</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','4')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '4' ? checked : unchecked" alt="" />
                    <span>其他</span>
                    <input v-if="source=='add'" type="text" v-model="contractDetailObject.qt" :disabled="contractDetailObject.keepAccountsService !== '4'"/>
                    <span v-else>{{contractDetailObject.qt}}</span>
                  </div>
                </div>
                <p>一般纳税人-500元/月</p>
                  <div >
                  <div class="flexMode vc" @click="changetype('keepAccountsService','5')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '5' ? checked : unchecked" alt="" />
                    <span>6000/年</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','6')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '6' ? checked : unchecked" alt="" />
                    <span>7200/2年</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','7')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '7' ? checked : unchecked" alt="" />
                    <span>汇算清缴500元/次</span>
                  </div>
                  <div class="flexMode vc" @click="changetype('keepAccountsService','8')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.keepAccountsService == '8' ? checked : unchecked" alt="" />
                    <span>其他</span>
                    <input v-if="source=='add'" type="text" v-model="contractDetailObject.qtyiban" :disabled="contractDetailObject.keepAccountsService !== '8'"/>
                    <span v-else>{{contractDetailObject.qtyiban}}</span>
                  </div>
                  <div @click="changetype('haocaiFee','1')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.haocaiFee == '1' ? checked : unchecked" alt="" />
                    <span>耗材费：150元/年</span>
                  </div>
                </div>
               <div><span>2.其他服务1：</span> 
               <input v-if="source == 'add'" v-model="contractDetailObject.serviceName" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{contractDetailObject.serviceName}}</u>
                <span>服务费:</span> 
               <input v-if="source == 'add'" v-model="contractDetailObject.serviceCharge" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{contractDetailObject.serviceCharge}}</u>元；
              </div>
               <div><span>其他服务2：</span>
               <input v-if="source == 'add'" v-model="contractDetailObject.serviceNameSecond" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{contractDetailObject.serviceNameSecond}}</u>
                <span>服务费:</span> 
               <input v-if="source == 'add'" v-model="contractDetailObject.serviceChargeSecond" type="text" style="font-size: 20px;width: 200px;">
               <u v-else>{{contractDetailObject.serviceChargeSecond}}</u>元
              </div>
              <p>注：代理记账服务按营业执照成立日期的次月开始计算。</p>
              <b>2.工商代理服务：</b>
              <p>设立登记</p>
              <div class="flexMode vc" @click="changetype('radio','1')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.radio == '1' ? checked : unchecked" alt="" />
                </div>
                  <p style="display:inline" >
                 注册区域为<input v-model="contractDetailObject.registerRegion" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.registerRegion}}</span>区;
                 注册地址费<input v-model="contractDetailObject.registerRegionFee" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.registerRegionFee}}</span>元；
                 注册服务费：<input v-model="contractDetailObject.registerFee" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.registerFee}}</span> 元；
                 主营业务：<input v-model="contractDetailObject.mainBusiness" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.mainBusiness}}</span>;
                </p>
                <div class="flexMode vc" @click="changetype('radio','2')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.radio == '2' ? checked : unchecked" alt="" />
                </div>
                <p style="display:inline">
                 自有地址注册区域<input v-model="contractDetailObject.registerOn" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.registerOn}}</span>区 ；
                 注册服务费：<input v-model="contractDetailObject.registerFeeOn" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.registerFeeOn}}</span> 元；
                 主营业务：<input v-model="contractDetailObject.mainBusinessSecond" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.mainBusinessSecond}}</span> ；（包含：一证四章）</p>
                 <p>变更登记:</p>
                 <div class="flexMode flexWrap ">
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','1')"  :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('1')>-1 ? checked : unchecked" alt="" />
                  <span>法人</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','2')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('2')> -1 ? checked : unchecked" alt="" />
                  <span>注册地址</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','3')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('3')> -1 ? checked : unchecked" alt="" />
                  <span>注册资金</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','4')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('4')> -1 ? checked : unchecked" alt="" />
                  <span>股权变更 （是、否含公证）</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','5')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('5')> -1 ? checked : unchecked" alt="" />
                  <span>股东变更</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','6')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('6')> -1 ? checked : unchecked" alt="" />
                  <span>经营范围</span></div>
                  <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','7')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.checkedAll.indexOf('7')> -1 ? checked : unchecked" alt="" />
                  <span>名称</span></div>
                  <div class="flexMode vc p0-10" :class="contractDetailObject.checkedAll.indexOf('7')> -1 ? '' :'noevent' ">
                    <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','8')" :class="source == 'add' ? '' :'noevent' ">
                    (<img :src="contractDetailObject.checkedAll.indexOf('8')> -1 ? checked : unchecked" alt="" />
                    <span>含章</span></div>
                    <div class="flexMode vc pr10" @click="changetypeAll('checkedAll','9')" :class="source == 'add' ? '' :'noevent' ">
                    <img :src="contractDetailObject.checkedAll.indexOf('9')> -1 ? checked : unchecked" alt="" />
                    <span>不含章</span>)</div>
                 </div>
                  </div>
                <div class="flexMode vc pt10" @click="changetype('radioQt','1')" :class="source == 'add' ? '' :'noevent' ">
                  <img :src="contractDetailObject.radioQt == '1' ? checked : unchecked" alt="" />
                </div>
                  <p class="flexMode vc">
                    其他变更项：<input v-model="contractDetailObject.qitaBg" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.qitaBg}}</span>;
                 费用<input v-model="contractDetailObject.bgFee" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.bgFee}}</span>元/次；
                </p>
                <p class="flexMode vc" >
                  其他代办内容：<input v-model="contractDetailObject.qitaDaiban" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.qitaDaiban}}</span>;
                 费用<input v-model="contractDetailObject.qitaFee" v-if="source == 'add'" type="text" />
                 <span v-else>{{contractDetailObject.qitaFee}}</span>元/次；
                </p>
                <b>3.服务内容</b>
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%">
                    <el-table-column
                      prop="project"
                      label="会员服务项目"
                      width="120">
                    </el-table-column>
                    <el-table-column
                      prop="content"
                      label="基础服务内容">
                    </el-table-column>
                  </el-table>
              <p class="font-28">
                <b>★汇总：</b>
              </p>
              <p>
                【总计】本合同应收金额：
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.sumServiceChageLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.sumServiceChageLowerCase }}</u>
                元，实收金额
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.payerMoneyLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.payerMoneyLowerCase }}</u>元，剩余尾款金额
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.balancePaymentLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.balancePaymentLowerCase }}</u>元
              （尾款应在<input v-if="source == 'add'" v-model="contractDetailObject.payAll" type="text" style="font-size: 20px;">
                <u v-else>{{ contractDetailObject.payAll }}</u>付清）</p>
              <!-- <div v-if="onlineContracts == 2">
                <p>乙方收款账户：北京繁一企业管理咨询有限公司</p>
                <p>开户行：工商银行北京羊坊店支行</p>
                <p>帐号：0200282209200020012</p>
              </div>
              <div v-if="onlineContracts == 1">
                <p>乙方收款账户：北京小苗财税服务有限公司</p>
                <p>开户行：中国建设银行北京北环支行</p>
                <p>帐号：11050162540000000320</p>
              </div>
              <div v-if="onlineContracts == 3">
                <p>乙方收款账户：北京后企之秀企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司北京海淀黄庄支行</p>
                <p>帐号： 110941339410601</p>
              </div> -->
              <div>
                <p>乙方收款账户：上海企苗企业管理咨询有限公司</p>
                <p>账号：121927216110701</p>
                <p>开户行：招商银行股份有限公司上海嘉定支行</p>
              </div>
              <div class="font-22">
                <p class="font-28">
                  <b>二、 记账服务甲乙双方责任和义务</b>
                </p>
                <jizhang />
            </div>
            <div class="signature" style="margin-bottom: 40px;">
              <div class="signA" style="width: 50%;float: left;">
                <div class="signAImg">甲方：{{ orderDataMain.contractContent.firstParty }}</div>
                <div class="signAImg" style="position: relative;">授权代表：{{ orderDataMain.contractContent.firstSignName ||orderDataMain.contractContent.firstPartyRepresentative}}
                  <i v-if="!resultImg && source =='mobile'" style="cursor: pointer;font-size: 22px;border:1px dashed black" @click="signBoard">签字区</i>
                  <img v-if="resultImg" style="width: 100px;position: absolute;" :src="resultImg" class="avatar" @click="signBoard" />
                </div>
                <div class="signAImg">盖章：</div>
                <div class="time-mes">
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.contractDetailObject.fristPartyDate }}</div> -->
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.date }}</div> -->
                  <span>日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
              <div class="signB" style="width: 50%;float: right;position: relative;">

                <!-- <div v-if="onlineContracts == 2" class="signBImg">
                  乙方（盖章）：北京繁一企业管理咨询有限公司</div> -->
                <!-- 此处需要判断地理id ，显示北京小苗或者河南z苗 -->
                <!-- <div v-if="onlineContracts == 1" class="signBImg">
                  乙方（盖章）：北京小苗财税服务有限公司</div> -->
                <div class="signBImg">
                  乙方：上海企苗企业管理咨询有限公司</div>
                <img v-if="source != 'add'" style="position: absolute;width: 100px;left: 120px;"
                src="'https://crm-file-com.oss-cn-beijing.aliyuncs.com/pdf/20221009/lQLPJxbCym8aTBDNAXDNAWGw52Tf9s8FXDgDQDYjswCFAA_353_368.png'" 
                />
                <!-- <div class='signBImg'
                  v-if="onlineContracts == 1">
                  乙方（盖章）：河南助苗企业管理咨询有限公司</div> -->
                <!-- <div v-if="onlineContracts == 3" class="signBImg">乙方（盖章）：北京后企之秀企业管理咨询有限公司</div> -->
                <div class="">授权代表：{{ orderDataMain.contractContent.secondSignName|| orderDataMain.contractContent.secondPartyRepresentative }}</div>
                <div class="signAImg">盖章：</div>
                <div class="time-mes  time-mesB">
                  <span>日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<el-dialog
      title="电子签名"
      append-to-body
      width="70%"
      :modal-append-to-body="false"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <el-card class="qianming-container" body-style="padding:0px">
        <vue-esign ref="esign" :is-crop="isCrop" :width="source == 'mobile'?500:600" :height="300" :line-width="lineWidth" :line-color="lineColor" :bg-color.sync="bgColor" />
        <div class="contro-container">
          <el-button type="danger" @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleGenerate">保存</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisibleCode"
      :modal-append-to-body="false"
      width="30%"
      :before-close="handleCloseCode"
    >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
</div>
</template>
<script>
import FY_Seal from '../../../assets/images/FY_seal.png';
import Seal from '../../../assets/images/seal.png';
import HQ_Seal from '../../../assets/images/HQ_Seal.png';
import {client} from '@/utils/alioss';
import jizhang from './jizhang.vue'
import {
  checkoutContract, genPdf, getPdf, sendMessage, checkValidCode
} from '@/api/contract/electronicContract';
import checked from '@/assets/images/checked.jpg'
import unchecked from '@/assets/images/unchecked.jpg'
export default {
  name: 'Edit',
  components:{
    jizhang
  },
  props: {
    productData: {
      type: Array,
      default() {
        return [];
      },
    },
    customData: {
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      type: String,
      default() {
        return '';
      },
    },
    onlineContract: {// 1 牛牛帮 2 繁一 3 后企 4上海
      type: [String,Number],
      default() {
        return '';
      },
    },
    dzCityId: {
      type: String,
      default() {
        return '';
      },
    },
    source: {// add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      type: String,
      default() {
        return '';
      },
    },
    width: {
      type: String,
      default() {
        return '';
      },
    },
    orderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checked,
      unchecked,
      orderDataMain: {},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      HQ_Seal: HQ_Seal,
      FY_Seal: FY_Seal,
      Seal: Seal,
      dialogVisibleCode: false,
      signCode: '',
      xiaoguimo:'',
      nashuiren:'',
       tableData:[
        {
          project:'记账服务',
          content:'1、账套初始化建立，日常账务处理 2、出具资产负债表、利润表、现金流量表3、国地税综合申报及明细申报（进出口企业不负责出口退税）；4、通过企业信用信息公示系统向工商行政管理部门报送上年度报告(其他日常信息变更由企业自行公示)；5、企业所得税年度汇算清缴；6、配合协助税务调查。'
        }
      ],
      disabled: false,
      codeText: "获取验证码",
      onlineContracts:'',
      year:'',
      month:'',
      day:'',
      radio:'',
      contractDetailObject:{
        keepAccountsService: '',
        checkedAll:[],
        radio:'',
      },
    };
  },
   watch:{
    orderData: {
      immediate: true,
      handler: function (n) {
        this.orderDataMain = JSON.parse(JSON.stringify(n))
        if (n.contractContent.contractDetailObject && Object.keys(n.contractContent.contractDetailObject).length != '0') {
          this.contractDetailObject = n.contractContent.contractDetailObject
        }
        this.orderDataMain.contractContent.firstContactPhone =this.orderDataMain.contractContent.firstContactPhone|| this.phoneNumber;
          this.orderDataMain.contractContent.secondParty = this.orderDataMain.contractContent.secondParty ||this.$store.state.user.userInfo.dept.deptName;
          this.orderDataMain.contractContent.secondSignName = this.orderDataMain.contractContent.secondSignName || this.$store.state.user.userInfo.nickName;
      },
  },
    //  orderData: {
    //   immediate: true,
    //   handler: function (n) {
    //     if (n) {
    //       this.orderDataMain = JSON.parse(JSON.stringify(n))
    //       this.orderDataMain.contractContent.contractDetailObject = this.orderDataMain.contractContent.contractDetailObject || {}
    //       
    //     }
    //   },
    // },
  },
  mounted: function() {

  },
  created() {
    // 用户短信入口orderId从url中取值
    // 进去电子合同初始化
    // console.info('souces', this.source);
    // if (this.source == 'add') { // 提单新增电子合同
    //   this.getInitData();
    // } else { // 其他状态
    //   this.getInitViewData();
    // }
    // 电子合同主体，页面判断使用
    let date = new Date()
    this.year = date.getFullYear()
    this.month = Number(date.getMonth()) + 1
    this.day = date.getDate()
    this.onlineContracts = this.onlineContract;
  },
  methods: {
    changetypeAll(k,v){
      const radio = this.contractDetailObject
      const radioKey = radio[k]
      // console.log(radioKey);
      const index = radioKey.indexOf(v)
      if(index > -1){
        radioKey.splice(index,1)
      }else{
        radioKey.push(v)
      }
      radio[k] = radioKey
      this.contractDetailObject = {...this.contractDetailObject,...radio,}
    },
    changetype(k,v){
      const radio = this.contractDetailObject
      radio[k] = radio[k]=== v ? '': v 
      this.contractDetailObject = {...this.contractDetailObject,...radio,}
    },
    emitData(){
      const all = this.orderDataMain
      all.contractContent.contractDetailObject = this.contractDetailObject
      this.orderDataMain = {...this.orderDataMain,...all}
      return this.orderDataMain
    },
    // 签约成功之后，生成pdf 文件
    getHtml() {
      let html = document.getElementById('dz-contract-main').innerHTML;
      // html就是整个合同文字内容
      // console.log(html)
      // genPdf接口报500
      genPdf({orderId: this.orderId, code: html}).then(response => {
        if(response.code == 200){
          // this.$router.go(-1)
          this.$emit('btnFalse',true)
        }
      });
    },
    // 电子签名弹框打开
    signBoard() {
      this.dialogVisible = true;
    },
    // 电子签名弹框隐藏
    handleClose() {
      this.dialogVisible = false;
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 生成签名图片..
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        console.log(res,'qianming')
        let randnum = Math.random() * 10000000000000;
        randnum = Math.floor(randnum);
        // let fileName = "dianziqianming/" + randnum + '.png'
        let fileName = randnum + '.png';
        let file = this.dataURLtoFile(res, fileName);
        console.log(fileName, 'fileName');
        console.log(file, 'file');
        let date = new Date();
        let dateYear = date.getFullYear(); // 获取年
        let dateMonth = date.getMonth() + 1; // 获取月
        let dateDate = date.getDate(); // 获取当日
        dateMonth = dateMonth > 9 ? dateMonth : '0' + dateMonth;
        dateDate = dateDate > 9 ? dateDate : '0' + dateDate;
        let dir = 'information/' + dateYear + dateMonth + dateDate + '/' + fileName;
        file.uid = '1657869457487';
        client().multipartUpload(dir, file).then(res => {
          console.log(res, 'res');
          this.resultImg = res.res.requestUrls[0];
          this.dialogVisible = false;
        }).catch(err => {});
        this.$emit('signFalse',true)
      }).catch(err => {
        this.$message.error('请签名之后提交！');
      });
    },
    // 将base64转换为文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','); var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]); var n = bstr.length; var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {type: mime});
    },
    // 提单 传递电子合同数据    ++++ 需要传递自填的数据 this.contractDetailObject
    // submit() {
    //   // this.$emit('getSignData', this.orderData);
    //   this.$emit('getSignData', this.orderDataMain);
    //   // this.$emit('getSignData',this.contractDetailObject);
    // },
    
    // 经理审批同意，发送短信
    submitSend() {
      console.log('经理审批同意，发送短信');
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('close');
        // 别的数据有问题，暂时先写死id 走流程
        // const data = {
        //   orderId: this.orderId,
        //   phone: this.contractDetailObject.firstContactPhone,
        //   ...this.contractDetailObject,
        //   ...this.otherService
        // }
        // sendMessage({orderId: this.orderId, phone: this.contractDetailObject.firstContactPhone}).then(response=>{
        // sendMessage(data).then(response=>{
        sendMessage({orderId: '1', phone: '18211193616', contractSubject: this.onlineContracts}).then(response => {
          if (response.code == 200) {
            //  移动端跳转地址,接口没有，暂时先本地跳转看页面
            window.open(window.location.protocol + '//' + window.location.host + '/sendEmail?orderId=' + this.orderId);
          }
        });
      }).catch(() => {

      });
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 驳回
    submitReject() {
      // 暂时没有接口
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.orderData.contractContent.firstContactPhone}).then(response=>{
    // console.log(this.signCode,'signCode')
    checkValidCode({code: this.signCode, phone: '17854561089'}).then(response => {
        if (response.code == 200) {
          this.getHtml();
          this.dialogVisibleCode = false;
        }
      });
    },
    // 获取验证码
    getSignCode() {
       let maxNum = 60;
        let oldCodeText = this.codeText;
        this.codeText = `${maxNum}s重新发送`; //初始显示倒计时
        let codeCountDown = setInterval(() => {
          this.disabled = true;
          let countDownNum = maxNum--;
          this.codeText = `${countDownNum}s重新发送`;
          if (countDownNum == 0) {
            //倒计时结束
            this.codeText = oldCodeText;
            clearInterval(codeCountDown);
            this.disabled = false;
          }
        }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
      sendMessage({phone: '17854561089', contractSubject: this.onlineContracts}).then(response => {
        if (response.code == 200) {

        }
      });
    }
  }
};
</script>
