<template>
  <div>
    <div id="dz-contract-main" cl="main-content dz-contract-main" :style="'font-size: 20px; width:'+ width +'; margin: 0 auto;padding: 20px'">
      <el-row class-name="header">
        <h3 style="text-align: right;">
          合同编号：<span cl="contract-number">{{ orderData.showNumber }}</span>
        </h3>
      </el-row>
      <h3 style="margin-left:300px">{{orderData.contractName}}</h3>
      <div cl="main">
        <div cl="contract-head">
          <div cl="partA">
            <p cl="name">
              <span>甲 方：</span>
              <input v-if="source == 'add'" v-model="orderData.contractContent.firstParty" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderData.contractContent.firstParty }}(以下简称甲方)</u>
            </p>
            <p>
              代表人姓名：
              <input v-if="source == 'add'" v-model="orderData.contractContent.firstPrincipalName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderData.contractContent.firstPrincipalName }}</u>
            </p>
            <p>
              联系电话：<u cl="phone">{{ orderData.contractContent.firstContactPhone }}</u>
            </p>
            <p>
              部  门：
              <input v-if="source == 'add'" v-model="orderData.contractContent.firstDeptName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderData.contractContent.firstDeptName }}</u>
            </p>
            <div />
          </div>
          <div cl="partB">
            <p v-if="onlineContracts == 2" cl="name">
              乙方：北京繁一企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 1" cl="name">
              乙方：北京小苗财税服务有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 3" cl="name">
              乙方：北京后企之秀企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 4" cl="name">
              乙方：上海企苗企业管理咨询有限公司</p>
            <div>
              <p>
                代表人姓名：
                <input v-if="source == 'add'" v-model="orderData.contractContent.secondPrincipalName" type="text" style="font-size: 20px;width: 600px;">
                <u v-else>{{ orderData.contractContent.secondPrincipalName }}</u>
              </p>
              <!-- <p>联系电话：010-62667788</p> -->
              <p>联系电话：{{ orderData.contractContent.secondtContactPhone ||'010-62667788' }}</p>
              <p>
                部  门：<input v-if="source == 'add'" v-model="orderData.contractContent.secondDeptName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderData.contractContent.secondDeptName }}</u>
              </p>
            </div>
          </div>
          <p cl="partTips block">
            甲、乙双方根据《中华人民共和国民法典》和《中华人民共和国会计法》及其他相关的法律法规的规定，本着平等公平、诚实信用、互惠互利的原则，就合作事宜达成如下内容，以资共同遵守。
          </p>
        </div>
        <div cl="contract-content">
          <div cl="contract-rule">
            <div cl="rules">
              <div cl="font-28">
                <b>第一条 :</b>
                <span>本合同委托服务内容:</span>
                <div class="flexMode flexWrap p10-0">
                  <div class="flexMode vc pr10" @click="changetype('checkedAll','1')">
                  <img :src="contractDetailObject.checkedAll.indexOf('1')>-1 ? checked : unchecked" alt="" />
                  <span>增资(减资)</span></div>
                  <div class="flexMode vc pr10" @click="changetype('checkedAll','2')">
                  <img :src="contractDetailObject.checkedAll.indexOf('2')> -1 ? checked : unchecked" alt="" />
                  <span>变更</span></div>
                  <div class="flexMode vc p0-10" :class="contractDetailObject.checkedAll.indexOf('2')> -1 ? '' :'noevent' ">
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','3')">
                    (<img :src="contractDetailObject.checkedAll.indexOf('3')> -1 ? checked : unchecked" alt="" />
                    <span>名称</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','4')">
                    <img :src="contractDetailObject.checkedAll.indexOf('4')> -1 ? checked : unchecked" alt="" />
                    <span>地址</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','5')">
                    <img :src="contractDetailObject.checkedAll.indexOf('5')> -1 ? checked : unchecked" alt="" />
                    <span>年限</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','6')">
                    <img :src="contractDetailObject.checkedAll.indexOf('6')> -1 ? checked : unchecked" alt="" />
                    <span>法人</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','7')">
                    <img :src="contractDetailObject.checkedAll.indexOf('7')> -1 ? checked : unchecked" alt="" />
                    <span>监事</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','8')">
                    <img :src="contractDetailObject.checkedAll.indexOf('8')> -1 ? checked : unchecked" alt="" />
                    <span>范围</span>)</div>
                  </div>
                  <div class="flexMode vc pr10" @click="changetype('checkedAll','9')">
                    <img :src="contractDetailObject.checkedAll.indexOf('9')> -1 ? checked : unchecked" alt="" />
                    <span>年报</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','10')">
                    <img :src="contractDetailObject.checkedAll.indexOf('10')> -1 ? checked : unchecked" alt="" />
                    <span>记账服务</span></div>
                    <div class="flexMode vc pr10" @click="changetype('checkedAll','11')">
                    <img :src="contractDetailObject.checkedAll.indexOf('11')> -1 ? checked : unchecked" alt="" />
                    <span>其他</span>
                    <input v-model="contractDetailObject.qt" type="text" :disabled="contractDetailObject.checkedAll.indexOf('11')== -1" v-if="source== 'add'">
                    <span v-else>{{ contractDetailObject.qt }}</span>
                  </div>
                </div>
              </div>
              <p cl="font-28">
                <b>第二条 :</b>
              </p>
              <p>乙方应按照甲方的要求处理委托事务，甲方拟申请设立或变更的公司基本情况如下： </p>
              <p>1.公司名称变更为：<input v-model="contractDetailObject.nameChange" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.nameChange}}</span> ,
              若此名称未能获机关预先核准，则公司查名依次为：<input v-model="contractDetailObject.nameCheck" v-if="source== 'add'" type="text">
              <span v-else>{{contractDetailObject.nameCheck}}</span> ,甲方在乙方通过企业名称预先核准后又要求更改公司名称，每次需另外向乙方支付人民币100元。 </p>
              <p>2.公司注册资本为人民币（增资或减资）为<input v-model="contractDetailObject.price" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.price}}</span> ,万元， </p>
              <p>3.公司注册地址变更为：<input v-model="contractDetailObject.addressChange" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.addressChange}}</span> 。 </p>
              <p>4.3.公司法定代表人变更为：<input v-model="contractDetailObject.userChange" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.userChange}}</span> 。 </p>
              <p>5.公司监事为：<input v-model="contractDetailObject.userCheck" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.userCheck}}</span> 。 </p>
              <p>6.公司经理为：<input v-model="contractDetailObject.manager" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.manager}}</span> 。 </p>
              <p>7.法人联系电话：<input v-model="contractDetailObject.userPhone" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.userPhone}}</span> 。 </p>
              <p>甲方应向乙方明确告知公司法定代表人曾担任其他公司法定代表人的情况，其有不得担任公司法定代表人情形的，甲方应予更换。</p>
              <p>8.公司经营范围变更为:<input v-model="contractDetailObject.range" v-if="source== 'add'" type="text">
                <span v-else>{{contractDetailObject.range}}</span> 。</p>
              <p>（以登记机关最终核定为准，对需要取得行政许可的经营内容乙方不予负责，若需乙方代办相关许可证双方另行签订委托合同）。</p>
              <div cl="font-28">
              <p>9.记账服务</p>
              <p>小规模-300元/月</p>
              <div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','1')">
                    <img :src="contractDetailObject.keepAccountsService == '1' ? checked : unchecked" alt="" />
                    <span>3000元（10个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','2')">
                    <img :src="contractDetailObject.keepAccountsService == '2' ? checked : unchecked" alt="" />
                    <span>3600元（1年）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','3')">
                    <img :src="contractDetailObject.keepAccountsService == '3' ? checked : unchecked" alt="" />
                    <span>6000元（20个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','4')">
                    <img :src="contractDetailObject.keepAccountsService == '4' ? checked : unchecked" alt="" />
                    <span>7200元（2年）</span>
                  </div>
                </div>
                
              <p>一般纳税人-650元/月</p>
              <div >
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','5')">
                    <img :src="contractDetailObject.keepAccountsService == '5' ? checked : unchecked" alt="" />
                    <span>6500元（10个月）</span>
                  </div>
                  <div class="flexMode vc" @click="changetypeRadio('keepAccountsService','6')">
                    <img :src="contractDetailObject.keepAccountsService == '6' ? checked : unchecked" alt="" />
                    <span>7800元（1年）</span>
                  </div>
                </div>
              </div>
              <p cl="font-28">注：1.新注册客户代理记账服务按营业执照成立日期的次月开始计算，纯记账客户具体记账日期以我司会计下发的记账服务合同补充条款记账日期为准。</p>
              <p cl="font-28">2.次年记账续费无需另行签订合同，本协议对双方继续有效。</p>
              <el-table
                :data="tableData"
                border
                style="width: 100%"
              >
                <el-table-column
                  prop="project"
                  label="会员服务项目"
                  width="120"
                />
                <el-table-column
                  prop="content"
                  label="基础服务内容"
                />
              </el-table>
              <p>10.其他<input type="text" v-model="contractDetailObject.qita" v-if="source== 'add'">
                <span v-else>{{contractDetailObject.qita}}</span> 。 </p>
              <p cl="font-28">
                <b>第四条 支付款项及方式</b>
              </p>
              <p>
                甲方应向乙方支付总费用人民币(大写)共
                <input v-if="source == 'add'" v-model="orderData.contractContent.payerMoneyCapital" type="text" style="font-size: 20px;">
                <u v-else>{{ orderData.contractContent.payerMoneyCapital }}</u>
                元，小写<input v-if="source == 'add'" v-model="orderData.contractContent.payerMoneyLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderData.contractContent.payerMoneyLowerCase }}</u>元，
                实收
                <input v-if="source == 'add'" v-model="orderData.contractContent.payPrice" type="text" style="font-size: 20px;">
                <u v-else>{{ orderData.contractContent.payPrice }}</u>
                元， 剩余金额
                <input v-if="source == 'add'" v-model="orderData.contractContent.balancePayment" type="text" style="font-size: 20px;">
                <u v-else>{{ orderData.contractContent.balancePayment }}</u>
                元。
              </p>
              <!-- <p>乙方收取服务费统一账户：</p> -->
              <div v-if="onlineContracts == 2">
                <p>公司名称：北京繁一企业管理咨询有限公司</p>
                <p>开户行：工商银行北京羊坊店支行</p>
                <p>帐号：0200282209200020012</p>
                <p>地址：北京市朝阳区北辰东路8号院北京国际会议中心东配楼三楼</p>
              </div>

              <div v-if="onlineContracts == 1">
                <p>公司名称：北京小苗财税服务有限公司</p>
                <p>开户行：中国建设银行北京北环支行</p>
                <p>帐号：11050162540000000320</p>
                <p>地址：北京市朝阳区北辰东路8号院北京国际会议中心东配楼三楼</p>
              </div>

              <div v-if="onlineContracts == 3">
                <p>公司名称：北京后企之秀企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司北京海淀黄庄支行</p>
                <p>帐号： 110941339410601</p>
                <p>地址：北京市朝阳区北辰东路8号汇宾大厦A座1901室</p>
              </div>
              <div v-if="onlineContracts == 4">
                <p>公司名称：上海企苗企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司上海嘉定支行</p>
                <p>账号：121927216110701</p>
                <p>地址：上海市嘉定区平城路1055号创新创业大厦809室</p>
              </div>
              <div cl="font-22">
                <p cl="font-28">
                  <b>第五条 甲乙双方权利义务</b>
                </p>
                <QuanliYiwu />
                <p cl="font-28">
                  <b>第六条 违约责任</b>
                </p>
                <p>
                  1.甲方应当严格按照本合同的约定时间支付服务费，如未按照合同约定履行付款义务，经乙方催告后3日内仍未支付的，每迟延一日，按照总服务费的千分之五计算违约金，如甲方在30日内仍未支付的，乙方有权停止业务办理，解除合同并不退还已支付的服务费；如相应办理手续无法终止的，所办理服务成果自成功办理后15日内未自提或未签收的，从次日起算，每满一个月支付500元保管费；
                </p>
                <p>
                  2.如因甲方原因（包括甲方所提供的资料不全面、非法被追究责任、补充资料不及时及对接人员无法联系、该到场人员未到场或拒不到场办理手续等，办理注册或相关资质后迟迟不领取或无法通过邮寄送达等）导致委托事项办理不成功或无法送达的，甲方所付款项不退，乙方将所收相关申请资料原件退回。
                </p>
                <p>
                  3.如因不可抗力原因导致委托事项无法完成的，因此导致办理期限延长的，双方均不承担任何责任，如甲方继续要求办理的，乙方可在该不可抗力因素消除后继续办理；
                </p>
                <p>4.因甲方存在违法行为而导致的政府罚款，由甲方另行支付，不包含在上述代理费中。 </p>
                <p>
                  5.本合同有效期内，因甲方原因违约解除合同，其中代理记账服务费全额扣除乙方不予退费，其他部分乙方扣除20%后退还甲方；
                </p>
                <p cl="font-28">
                  <span><b>第七条</b> 本合同经甲乙双方、签字盖章后并在乙方收到第五条第一款约定的预付款后生效，本合同一式两份，双方各执壹份，各份具有同等法律效力。</span>
                </p>
                <p cl="font-28">
                  <span> <b>第八条 </b>  本合同及相关补充协议全部内容均为书面文件，除甲乙双方授权代表签字或盖章，其它手写、口述、聊天记录内容均无效。请认真阅读合同内容，由手写、口述、聊天记录等书面文件之外的部分产生的歧义，概不作为合同依据，乙方概不负责。
                  </span>
                </p>
              </div>
            </div>
            <div cl="signature" style="margin-bottom: 40px;">
              <div cl="signA" style="width: 50%;float: left;">
                <!-- <div cl="signAImg">甲方：（盖章）</div> -->
                <div cl="signAImg" style="position: relative;">甲方盖章（签字）：{{ orderData.contractContent.firstParty }}
                  <i v-if="!resultImg && source =='mobile'" style="cursor: pointer;" @click="signBoard">签字区</i>
                  <img v-if="resultImg" style="width: 100px;position: absolute;" :src="resultImg" class="avatar" @click="signBoard" />
                </div>
                <p>
                  电话：<u cl="phone">{{ orderData.contractContent.firstContactPhone }}</u>
                </p>
                <div cl="time-mes">
                  <!-- <div cl="time">签字日期：{{ orderData.contractContent.date }}</div> -->
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
              <div cl="signB" style="width: 50%;float: right;position: relative;">

                <div v-if="onlineContracts == 2" cl="signBImg">
                  乙方（盖章）：北京繁一企业管理咨询有限公司</div>
                <!-- 此处需要判断地理id ，显示北京小苗或者河南z苗 -->
                <div v-if="onlineContracts == 1" cl="signBImg">
                  乙方（盖章）：北京小苗财税服务有限公司</div>
                <div v-if="onlineContracts == 4" cl="signBImg">
                  乙方：上海企苗企业管理咨询有限公司</div>
                <img v-if="source != 'add'" style="position: absolute;width: 100px;left: 120px;" :src="onlineContracts == 1?Seal:onlineContracts == 2?FY_Seal:HQ_Seal" />

                <div v-if="onlineContracts == 3" cl="signBImg"> 乙方（盖章）：北京后企之秀企业管理咨询有限公司</div>
                <p>电话：010-62667788</p>
                <div cl="time-mes  time-mesB">
                  <!-- <div cl="time">签字日期：{{ orderData.contractContent.date }}</div> -->
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
              <div style="clear: both;" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div id="remove"> -->
      <!-- <div cl="submit" style="text-align: center;margin-top: 30px;">
        <button v-if="source == 'add'" cl="submit-btn" style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;" @click="submit">
          提单
        </button>
        <button v-if="source == 'send'" cl="submit-btn" style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;" @click="submitSend()">
          以上合同确认无误，发送客户
        </button> -->
        <!-- 短信签约 -->
        <!-- <button v-if="source == 'mobile'" cl="submit-btn" style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;margin-bottom: 20px;" @click="submitSign">
          以上合同确认无误，提交
        </button>
        <button v-if="source == 'mobile'" cl="submit-btn" style="width: 100%;line-height: 50px;font-size: 22px;border-radius: 10px;border: none;background: #1890ff;color: #fff;cursor: pointer;margin-bottom: 20px;" @click="submitReject">
          驳回
        </button> -->
        <!-- 生成pdf文件方法，已完成 -->
        <!-- <button @click="getHtml">获取dom</button> -->
      <!-- </div> -->
    <!-- </div> -->
    <el-dialog
      title="电子签名"
      append-to-body
      width="70%"
      :modal-append-to-body="false"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <el-card class="qianming-container" body-style="padding:0px">
        <vue-esign ref="esign" :is-crop="isCrop" :width="source == 'mobile'?300:600" :height="300" :line-width="lineWidth" :line-color="lineColor" :bg-color.sync="bgColor" />
        <div class="contro-container">
          <el-button type="danger" @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleGenerate">保存</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisibleCode"
      :modal-append-to-body="false"
      width="30%"
      :before-close="handleCloseCode"
    >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()">
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import FY_Seal from '../../../assets/images/FY_seal.png';
import Seal from '../../../assets/images/seal.png';
import HQ_Seal from '../../../assets/images/HQ_Seal.png';
import {client} from '@/utils/alioss';
import QuanliYiwu from './quanliyiwu.vue';
import checked from '@/assets/images/checked.jpg'
import unchecked from '@/assets/images/unchecked.jpg'
import {
  checkoutContract, genPdf, getPdf, sendMessage, checkValidCode
} from '@/api/contract/electronicContract';
export default {
  name: 'Edit',
  components: {
    QuanliYiwu
  },
  props: {
    productData: {
      type: Array,
      default() {
        return [];
      },
    },
    customData: {
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      type: String,
      default() {
        return '';
      },
    },
    onlineContract: {// 1 牛牛帮 2 繁一 3 后企
      type: String,
      default() {
        return '';
      },
    },
    dzCityId: {
      type: String,
      default() {
        return '';
      },
    },
    source: {// add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      type: String,
      default() {
        return '';
      },
    },
    width: {
      type: String,
      default() {
        return '';
      },
    },
    orderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checked,
      unchecked,
      checkList: [],
      orderDataMain: {},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      HQ_Seal: HQ_Seal,
      FY_Seal: FY_Seal,
      Seal: Seal,
      dialogVisibleCode: false,
      signCode: '',
      keepAccountsService: '',
      tableData: [
        {
          project: '记账服务',
          content: '1、账套初始化建立，日常账务处理 2、出具资产负债表、利润表、现金流量表3、国地税综合申报及明细申报（进出口企业不负责出口退税）；4、通过企业信用信息公示系统向工商行政管理部门报送上年度报告(其他日常信息变更由企业自行公示)；5、企业所得税年度汇算清缴；6、配合协助税务调查。'
        }
      ],
      contractDetailObject: {
        keepAccountsService: '',
        checkList:[],
        checkedAll:[],
        checked:false,
        fristPartyDate:'',
        secondPartyDate:'',
      },
      checkListAll: [],
      checkedAll: [],
      disabled: '',
      codeText: '获取验证码',
      onlineContracts: '',
      checkOptions: ['原件', '复印件'],
      year:'',
      month:'',
      day:'',
    };
  },
  watch: {
    onlineContracts:{
      immediate: true,
      handler: function(n){
      }
    },
    orderData: {
      immediate: true,
      handler: function (n) {
        if (n.contractContent.contractDetailObject && Object.keys(n.contractContent.contractDetailObject).length != '0') {
          this.contractDetailObject = n.contractContent.contractDetailObject
        }
      },
  },
  },
  mounted() {
  },
  created() {
    // 用户短信入口orderId从url中取值
    // 进去电子合同初始化
    // if (this.source == 'add') { // 提单新增电子合同
    //   this.getInitData();
    // } else { // 其他状态
    //   this.getInitViewData();
    // }
    // 电子合同主体，页面判断使用
    //  if(this.orderData.contractContent.contractDetailObject !=null){
    //   this.contractDetailObject = this.orderData.contractContent.contractDetailObject
    // // }
    // this.orderData.contractContent.firstContactPhone = this.phoneNumber;
    // this.orderData.contractContent.secondDeptName = this.orderData.contractContent.secondDeptName ||this.$store.state.user.userInfo.dept.deptName;
    // this.orderData.contractContent.secondPrincipalName = this.orderData.contractContent.secondPrincipalName || this.$store.state.user.userInfo.userName;
    this.onlineContracts = this.onlineContract;
    let date = new Date()
    //  let timer = date.split('-')
      this.year = date.getFullYear()
      this.month = Number(date.getMonth()) + 1
      this.day = date.getDate()
  },
  methods: {
    changetype(k,v){
      const radio = this.contractDetailObject
      const radioKey = radio[k]
      // console.log(radioKey);
      const index = radioKey.indexOf(v)
      if(index > -1){
        radioKey.splice(index,1)
      }else{
        radioKey.push(v)
      }
      radio[k] = radioKey
      this.contractDetailObject = {...this.contractDetailObject,...radio,}
    },
    changetypeRadio(k,v){
      const radio = this.contractDetailObject
      radio[k] = radio[k]=== v ? '': v 
      this.contractDetailObject = {...this.contractDetailObject,...radio,}
    },
    emitData(){
      this.orderData.contractDetailObject = this.contractDetailObject
      return this.orderData
    },
    serveContent(val) {
      this.checkListAll = val;
    },
    // 签约成功之后，生成pdf 文件
    getHtml() {
      let html = document.getElementById('dz-contract-main').innerHTML;
      // html就是整个合同文字内容
      // console.log(html)
      // genPdf接口报500
      genPdf({orderId: this.orderId, code: html}).then(response => {
        if(response.code == 200){
          this.$router.go(-1)
        }
      });
    },
    // 电子签名弹框打开
    signBoard() {
      this.dialogVisible = true;
    },
    // 电子签名弹框隐藏
    handleClose() {
      this.dialogVisible = false;
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 生成签名图片..
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        console.log(res, 'qianming');
        let randnum = Math.random() * 10000000000000;
        randnum = Math.floor(randnum);
        // let fileName = "dianziqianming/" + randnum + '.png'
        let fileName = randnum + '.png';
        let file = this.dataURLtoFile(res, fileName);
        console.log(fileName, 'fileName');
        console.log(file, 'file');
        let date = new Date();
        let dateYear = date.getFullYear(); // 获取年
        let dateMonth = date.getMonth() + 1; // 获取月
        let dateDate = date.getDate(); // 获取当日
        dateMonth = dateMonth > 9 ? dateMonth : '0' + dateMonth;
        dateDate = dateDate > 9 ? dateDate : '0' + dateDate;
        let dir = 'information/' + dateYear + dateMonth + dateDate + '/' + fileName;
        file.uid = '1657869457487';
        client().multipartUpload(dir, file).then(res => {
          console.log(res, 'res');
          this.resultImg = res.res.requestUrls[0];
          this.dialogVisible = false;
        }).catch(err => {});
      }).catch(err => {
        this.$message.error('请签名之后提交！');
      });
    },
    // 将base64转换为文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','); var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]); var n = bstr.length; var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {type: mime});
    },
    // 提单 传递电子合同数据  this.contractDetailObject
    // submit() {
    //   // this.$emit('getSignData', this.orderData);
    //   // this.orderData.contractContent.contractDetailObject = this.contractDetailObject
    //   // this.$emit('getSignData', this.orderData);
    // },
    // 经理审批同意，发送短信
    submitSend() {
      console.log('经理审批同意，发送短信');
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('close');
        // 别的数据有问题，暂时先写死id 走流程
        // sendMessage({orderId: this.orderId, phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
        sendMessage({orderId: '1', phone: '18211193616', contractSubject: this.onlineContracts}).then(response => {
          if (response.code == 200) {
            //  移动端跳转地址,接口没有，暂时先本地跳转看页面
            window.open(window.location.protocol + '//' + window.location.host + '/sendEmail?orderId=' + this.orderId);
          }
        });
      }).catch(() => {

      });
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 驳回
    submitReject() {
      // 暂时没有接口
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.orderData.contractContent.firstContactPhone}).then(response=>{
    // console.log(this.signCode,'signCode')
      checkValidCode({code: this.signCode, phone: '17854561089'}).then(response => {
        if (response.code == 200) {
          this.getHtml();
          this.dialogVisibleCode = false;
        }
      });
    },
    // 获取验证码
    getSignCode() {
      let maxNum = 60;
      let oldCodeText = this.codeText;
      this.codeText = `${maxNum}s重新发送`; // 初始显示倒计时
      let codeCountDown = setInterval(() => {
        this.disabled = true;
        let countDownNum = maxNum--;
        this.codeText = `${countDownNum}s重新发送`;
        if (countDownNum == 0) {
          // 倒计时结束
          this.codeText = oldCodeText;
          clearInterval(codeCountDown);
          this.disabled = false;
        }
      }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
      sendMessage({phone: '17854561089', contractSubject: this.onlineContracts}).then(response => {
        if (response.code == 200) {

        }
      });
    }
  }
};
</script>
