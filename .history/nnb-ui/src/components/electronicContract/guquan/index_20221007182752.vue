<template>
  <div>
    <div id="dz-contract-main" clas="main-content dz-contract-main" :style="'font-size: 20px; width:'+ width +'; margin: 0 auto;padding: 20px'">
      <el-row class-name="header">
        <h3 style="text-align: right;">
          合同编号：<span clas="contract-number">{{ orderDataMain.showNumber }}</span>
        </h3>
      </el-row>
      <h3 style="margin-left:300px">{{orderDataMain.contractName}}</h3>
      <div clas="main">
        <div clas="contract-head">
          <div clas="partA">
            <p clas="name">
              <span>甲 方：</span>
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstParty" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstParty }}</u>(以下简称甲方)
            </p>
            <p>
              代表人姓名：
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstPrincipalName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstPrincipalName }}</u>
            </p>
            <p>
              联系电话：<u clas="phone">{{ orderDataMain.contractContent.firstContactPhone || phoneNumber}}</u>
            </p>
            <div />
          </div>
          <div clas="partB">
            <p v-if="onlineContracts == 2" clas="name">
              乙方：北京繁一企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 1" clas="name">
              乙方：北京小苗财税服务有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 3" clas="name">
              乙方：北京后企之秀企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 4" clas="name">
              乙方：上海企苗企业管理咨询有限公司</p>
            <div>
              <p>
                代表人姓名：
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.secondPrincipalName" type="text" style="font-size: 20px;width: 600px;">
                <u v-else>{{ orderDataMain.contractContent.secondPrincipalName }}</u>
              </p>
              <!-- <p>联系电话：010-62667788</p> -->
              <p>联系电话：{{ orderDataMain.contractContent.secondtContactPhone || '010-62667788'}}</p>
              <p>
                部  门：<input v-if="source == 'add'" v-model="orderData.contractContent.deptName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.deptName }}</u>
              </p>
            </div>
          </div>
          <p clas="partTips block">
            甲、乙双方本着平等互利、等价有偿的原则，经友好协商，就甲方将其所持有的股权变更交于交予乙方办理，特签订本协议。
          </p>
        </div>
        <div clas="contract-content">
          <div clas="contract-rule">
            <div clas="rules">
              <div clas="font-28">
                <b>一、变更事项</b>
                <p>1.1</p>
                  <div>
                    <div class="flexMode vc" @click="changeType('bgsx','1')">
                      <img :src="orderDataMain.contractContent.contractDetailObject.bgsx == '1' ? checked : unchecked" alt="" />
                      <span>法人变更</span>
                    </div>
                    <div class="flexMode vc" @click="changeType('bgsx','2')">
                      <img :src="orderDataMain.contractContent.contractDetailObject.bgsx == '2' ? checked : unchecked" alt=""/>
                      <span>监事变更</span>
                    </div>
                    <div class="flexMode vc" @click="changeType('bgsx','3')">
                      <img :src="orderDataMain.contractContent.contractDetailObject.bgsx == '3' ? checked : unchecked" alt=""/>
                      <span>股权变更</span>
                    </div>
                    <div class="flexMode vc" @click="changeType('bgsx','4')">
                      <img :src="orderDataMain.contractContent.contractDetailObject.bgsx == '4' ? checked : unchecked" alt=""/>
                      <span>其他</span>
                      <input v-model="orderDataMain.contractContent.contractDetailObject.qita" type="text" :disabled="orderDataMain.contractContent.contractDetailObject.bgsx !== '4'" v-if="source== 'add'">
                    <span v-else>{{ orderDataMain.contractContent.contractDetailObject.qita }}</span>
                    </div>
                  </div>
               <p>1.2.现公司股权所有情况说明：</p>
               <p>原股东有：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.ygdy" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.ygdy}}</u>
               分别占公司股权比例为：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.zb" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.zb}}</u>   </p>
              <p>1.3股权变更情况说明：</p>
               <p>原股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.ygd" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.ygd}}</u>
                将目前持有公司股权<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.mqzb" style="width:50px" v-if="source=='add'"> 
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.mqzb}}</u>%
                转让给新股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xgd" v-if="source=='add'"> 
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.xgd}}</u>
              </p>
               <p>原股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.ygd2" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.ygd2}}</u>
               将目前持有公司股权<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.mqzb2" style="width:50px" v-if="source=='add'">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.mqzb2}}</u> %
               
               转让给新股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xgd2" v-if="source=='add'"> 
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.xgd2}}</u>
              </p>
               <p>原股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.ygd3" v-if="source=='add'"> 
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.ygd3}}</u>
               将目前持有公司股权<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.mqzb3" style="width:50px" v-if="source=='add'"> 
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.mqzb3}}</u>%
              
               转让给新股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xgd3" v-if="source=='add'"> 
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.xgd3}}</u>
              </p>
               <p>原股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.ygd4" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.ygd4}}</u>
               将目前持有公司股权<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.mqzb4" style="width:50px" v-if="source=='add'"> 
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.mqzb4}}</u>%
               
               转让给新股东<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xgd4" v-if="source=='add'"> 
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.xgd4}}</u>
              </p>
              <p>1.4法人及监事变更说明：</p>
               <p>原法人：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.yfr" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.yfr}}</u>；
               新法人：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xfr" v-if="source=='add'">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.xfr}}</u>
              </p>
               <p>原监事:<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.yjs" v-if="source=='add'">
                <u v-else>{{orderDataMain.contractContent.contractDetailObject.yjs}}</u>；
               新监事：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.xjs" v-if="source=='add'">
               <u v-else>{{orderDataMain.contractContent.contractDetailObject.xjs}}</u>
              </p>
               <p>注：上述1.2-1.4约定的内容，在合同签订时若尚未确定，可在本合同签订后通过微信、电话、邮件等方式书面文件进行补充确定。</p>
              </div>
              <p clas="font-28">
                <b>二、股权变更时间及费用</b>
              </p>
              <p> 股权变更时间：<input type="text" v-model="orderDataMain.contractContent.contractDetailObject.changeDate" />个工作日左右（自甲方提供办理委托事项的所有资料以及缴纳相关费用时开始起算）。 </p>
              <p>甲方应支付乙方委托服务费：（大写）
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.stockRightFeeCapital" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.stockRightFeeCapital }}</u>
                （小写）
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.stockRightFeeLowerCase" type="text" style="font-size: 20px;">
                <u v-else>{{ orderDataMain.contractContent.stockRightFeeLowerCase }}</u> 
                </p>
              <!-- <p>乙方收取服务费统一账户：</p> -->
              <div v-if="onlineContracts == 2">
                <p>公司名称：北京繁一企业管理咨询有限公司</p>
                <p>开户行：工商银行北京羊坊店支行</p>
                <p>帐号：0200282209200020012</p>
                <p>地址：北京市朝阳区北辰东路8号院北京国际会议中心东配楼三楼</p>
              </div>
              <div v-if="onlineContracts == 1">
                <p>公司名称：北京小苗财税服务有限公司</p>
                <p>开户行：中国建设银行北京北环支行</p>
                <p>帐号：11050162540000000320</p>
                <p>地址：北京市朝阳区北辰东路8号院北京国际会议中心东配楼三楼</p>
              </div>
              <div v-if="onlineContracts == 3">
                <p>公司名称：北京后企之秀企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司北京海淀黄庄支行</p>
                <p>帐号： 110941339410601</p>
                <p>地址：北京市朝阳区北辰东路8号汇宾大厦A座1901室</p>
              </div>
              <div v-if="onlineContracts == 4">
                <p>公司名称：上海企苗企业管理咨询有限公司</p>
                <p>开户行：招商银行股份有限公司上海嘉定支行</p>
                <p>账号：121927216110701</p>
                <p>地址：上海市嘉定区平城路1055号创新创业大厦809室</p>
              </div>
              <div clas="font-22">
                <p clas="font-28">
                  <b>三、甲乙双方的权利和义务</b>
                </p>
                <p>3.1甲方保证其所转让的股权产权清晰，不存在抵押、质押、留置或其他担保权益及司法查封、冻结等限制性措施，并对本次转让的股权享有完全的处分权。否则，由此引起的所有责任，由甲方承担。</p>
                <p>3.2甲方应于本协议签署之日起3日内提供其主体资格证明等必要文件，以供乙方办理变更登记之用，工商及税务涉及签字、出示身份证明文件，甲方需及时配合，由于甲方原因导致业务暂停或终止的，乙方免责。</p>
                <p>3.3公司股权变更中涉及到转让双方需支付的包含但不限于：印花税、个人所得税、银行保管费等必要费用，由甲方承担。</p>
                <p>3.4乙方应于规定时间内完成股权变更事宜。</p>
                <p clas="font-28">
                  <b>四、违约责任</b>
                </p>
                <p>
                  1、甲、乙双方中由于其中一方的过失，造成本协议不能履行或不能完全履行的，则由过失的一方承担违约责任；如属甲、乙双方的过失，则根据实际情况由甲、乙双方分别承担各自应承担的违约责任。
                </p>
                <p>
                  2、如因甲方原因（包括甲方所提供的资料不全面、非法被追究责任、补充资料不及时及对接人员无法联系、该到场人员未到场或拒不到场办理手续等，办理注册或相关资质后迟迟不领取或无法通过邮寄送达等）导致委托事项办理不成功或无法送达的，甲方所付款项不退，乙方将所收相关申请资料原件退回。
                </p>
                <p>
                  3、合同办理期间，若甲方单方要求解除代理协议，需向乙方支付已进行成本价+合同价款的20%作为赔偿金。
                </p>
                <p clas="font-28">
                  <b>五、适用法律及争议解决</b>
                </p>
                <p>本协议适用中华人民共和国法律管辖。凡因本协议引发的争议，首先应由争议各方协商解决；协商不成，可向乙方所在地的人民法院提起诉讼。</p>
                <p clas="font-28">
                  <b>六、协议的变更与解除</b>
                </p>
                <p>本协议及相关补充协议全部内容均为书面打印文件，除甲乙双方授权代表签字或盖章，其它手写、口述、聊天记录内容均无效。请认真阅读合同内容，由手写、口述、聊天记录等书面打印件之外的部分产生的歧义，概不做为合同依据，乙方概不负责。若甲方严重违反本协议，乙方有权保留服务费用不予退还。</p>
                <p clas="font-28">
                <b>七、甲方需提供资料：</b>
              </p>
              <p>1.未三证合一需提供：</p>
              <p>1.执照正、副本原件
                    组织机构代码正、副本原件
                    税务登记证正、副本原件
                    统计证正、副本原件 </p>
                <p> 2.公章</p>
                <p> 3.新旧法人身份证复印件各1份 </p>
                <p> 4.新旧监事人身份证复印件各1份</p>
                <p> 5.新旧法人、股东、监事的身份联系电话、邮箱</p>
                <p> 6.后期税务股权变更，需要提供新旧股东的身份证原件（会提前3天通知）</p>
                <p> 7.股权变更前一个月的资产负债表</p>
                <p> 8.其他</p>
                <p>三证合一后的需提供：</p>
                  <p>1.执照正、副本原件 </p>
                  <p> 2.公章</p>
                  <p> 3.新旧法人身份证复印件各1份 </p>
                  <p> 4.新旧监事人身份证复印件各1份</p>
                  <p> 5.新旧法人、股东、监事的身份联系电话、邮箱</p>
                  <p> 6.后期税务股权变更，需要提供新旧股东的身份证原件（会提前3天通知）</p>
                  <p> 7.股权变更前一个月的资产负债表</p>
                  <p> 8.其他</p>
               <p clas="font-28">
                   <b>第八条 </b>
                </p>
                <p>本协议由甲、乙双方签订，自签订之日起生效，本协议一式二份，其中甲乙双方各执一份。</p>
              </div>
            </div>
            <div clas="signature" style="margin-bottom: 40px;">
              <div clas="signA" style="width: 50%;float: left;">
                <!-- <div clas="signAImg">甲方：（盖章）</div> -->
                <div clas="signAImg" style="position: relative;">甲方盖章（签字）：{{ orderDataMain.contractContent.firstParty }}
                  <i v-if="!resultImg && source =='mobile'" style="cursor: pointer;" @click="signBoard">签字区</i>
                  <img v-if="resultImg" style="width: 100px;position: absolute;" :src="resultImg" class="avatar" @click="signBoard" />
                </div>
                <p>
                  电话：<u clas="phone">{{ orderDataMain.contractContent.firstContactPhone || phoneNumber}}</u>
                </p>
                <div clas="time-mes">
                  <!-- <div clas="time">签字日期：{{ orderDataMain.contractContent.date}}</div> -->
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
              <div clas="signB" style="width: 50%;float: right;position: relative;">

                <div v-if="onlineContracts == 2" clas="signBImg">
                  乙方（盖章）：北京繁一企业管理咨询有限公司</div>
                <!-- 此处需要判断地理id ，显示北京小苗或者河南z苗 -->
                <div v-if="onlineContracts == 1" clas="signBImg">
                  乙方（盖章）：北京小苗财税服务有限公司</div>
                <div v-if="onlineContracts == 4" clas="signBImg">
                  乙方：上海企苗企业管理咨询有限公司</div>
                <img v-if="source != 'add'" style="position: absolute;width: 100px;left: 120px;" :src="onlineContracts == 1?Seal:onlineContracts == 2?FY_Seal:HQ_Seal" />
                <!-- <div clas='signBImg'
                  v-if="onlineContracts == 1">
                  乙方（盖章）：河南助苗企业管理咨询有限公司</div> -->

                <div v-if="onlineContracts == 3" clas="signBImg"> 乙方（盖章）：北京后企之秀企业管理咨询有限公司</div>
                <p>电话：010-62667788</p>
                <div clas="time-mes  time-mesB">
                  <!-- <div clas="time">签字日期：{{ orderDataMain.contractContent.date}}</div> -->
                  <span>签字日期：{{year}}  年{{month}}  月{{day}}  日</span>
                </div>
              </div>
              <div style="clear: both;" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="电子签名"
      append-to-body
      width="70%"
      :modal-append-to-body="false"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <el-card class="qianming-container" body-style="padding:0px">
        <vue-esign ref="esign" :is-crop="isCrop" :width="source == 'mobile'?300:600" :height="300" :line-width="lineWidth" :line-color="lineColor" :bg-color.sync="bgColor" />
        <div class="contro-container">
          <el-button type="danger" @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleGenerate">保存</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisibleCode"
      :modal-append-to-body="false"
      width="30%"
      :before-close="handleCloseCode"
    >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()">
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import FY_Seal from '../../../assets/images/FY_seal.png';
import Seal from '../../../assets/images/seal.png';
import HQ_Seal from '../../../assets/images/HQ_Seal.png';
import {client} from '@/utils/alioss';
import checked from '@/assets/images/checked.jpg'
import unchecked from '@/assets/images/unchecked.jpg'

import {
  checkoutContract, genPdf, getPdf, sendMessage, checkValidCode
} from '@/api/contract/electronicContract';
export default {
  name: 'Edit',
  components: {
  },
  props: {
    productData: {
      type: Array,
      default() {
        return [];
      },
    },
    customData: {
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      type: String,
      default() {
        return '';
      },
    },
    onlineContract: {// 1 牛牛帮 2 繁一 3 后企
      type: String,
      default() {
        return '';
      },
    },
    dzCityId: {
      type: String,
      default() {
        return '';
      },
    },
    source: {// add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      type: String,
      default() {
        return '';
      },
    },
    width: {
      type: String,
      default() {
        return '';
      },
    },
    orderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checked,
      unchecked,
      checkList: [],
      orderDataMain: {},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      HQ_Seal: HQ_Seal,
      FY_Seal: FY_Seal,
      Seal: Seal,
      dialogVisibleCode: false,
      signCode: '',
      checkListAll: [],
      checked: false,
      checkedAll: [],
      disabled: false,
      codeText: '获取验证码',
      onlineContracts: '',
      checkOptions: ['原件', '复印件'],
      year:'',
      month:'',
      day:'',
    };
  },
  watch: {
    onlineContracts:{
      immediate: true,
      handler: function(n){
      }
    },
    orderData: {
      immediate: true,
      handler: function (n) {
        if (n) {
          this.orderDataMain = JSON.parse(JSON.stringify(n))
          // console.log(this.orderDataMain.contractContent);
          // let timer = this.orderDataMain.contractContent.date.split('-')
          // this.year = timer[0]
          // this.month = timer[1]
          // this.day = timer[2]
          this.orderDataMain.contractContent.contractDetailObject = this.orderDataMain.contractContent.contractDetailObject || {}
          // this.orderDataMain.contractContent.firstContactPhone = this.phoneNumber;
          this.orderDataMain.contractContent.deptName = this.orderDataMain.contractContent.deptName ||this.$store.state.user.userInfo.dept.deptName;
          this.orderDataMain.contractContent.secondPrincipalName = this.orderDataMain.contractContent.secondPrincipalName || this.$store.state.user.userInfo.userName;
        }
      },
    },
  },
  mounted() {
  },
  created() {
    // 用户短信入口orderId从url中取值
    // 进去电子合同初始化
    // if (this.source == 'add') { // 提单新增电子合同
    //   this.getInitData();
    // } else { // 其他状态
    //   this.getInitViewData();
    // }
    // 电子合同主体，页面判断使用
    this.onlineContracts = this.onlineContract;
    let date = new Date()
    //  let timer = date.split('-')
      this.year = date.getFullYear()
      this.month = Number(date.getMonth()) + 1
      this.day = date.getDate()
  },
  methods: {
    changeType(k,v){
      const radio = this.orderDataMain
      radio.contractContent.contractDetailObject[k] = radio.contractContent.contractDetailObject[k] === v ? '':v
      this.orderDataMain = {...this.orderDataMain,...radio}
    },
    emitData(){
      return this.orderDataMain
    },
    serveContent(val) {
      this.checkListAll = val;
    },
    // 签约成功之后，生成pdf 文件
    getHtml() {
      let html = document.getElementById('dz-contract-main').innerHTML;
      // html就是整个合同文字内容
      // console.log(html)
      // genPdf接口报500
      genPdf({orderId: this.orderId, code: html}).then(response => {
        if(response.code == 200){
          this.$router.go(-1)
        }
      });
    },
    // 电子签名弹框打开
    signBoard() {
      this.dialogVisible = true;
    },
    // 电子签名弹框隐藏
    handleClose() {
      this.dialogVisible = false;
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 生成签名图片..
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        console.log(res, 'qianming');
        let randnum = Math.random() * 10000000000000;
        randnum = Math.floor(randnum);
        // let fileName = "dianziqianming/" + randnum + '.png'
        let fileName = randnum + '.png';
        let file = this.dataURLtoFile(res, fileName);
        console.log(fileName, 'fileName');
        console.log(file, 'file');
        let date = new Date();
        let dateYear = date.getFullYear(); // 获取年
        let dateMonth = date.getMonth() + 1; // 获取月
        let dateDate = date.getDate(); // 获取当日
        dateMonth = dateMonth > 9 ? dateMonth : '0' + dateMonth;
        dateDate = dateDate > 9 ? dateDate : '0' + dateDate;
        let dir = 'information/' + dateYear + dateMonth + dateDate + '/' + fileName;
        file.uid = '1657869457487';
        client().multipartUpload(dir, file).then(res => {
          console.log(res, 'res');
          this.resultImg = res.res.requestUrls[0];
          this.dialogVisible = false;
        }).catch(err => {});
      }).catch(err => {
        this.$message.error('请签名之后提交！');
      });
    },
    // 将base64转换为文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','); var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]); var n = bstr.length; var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {type: mime});
    },
    // 提单 传递电子合同数据  this.contractDetailObject
    // submit() {
    //   // this.$emit('getSignData', this.orderData);
    //   this.$emit('getSignData', this.orderDataMain);
    // },
    // 经理审批同意，发送短信
    submitSend() {
      console.log('经理审批同意，发送短信');
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('close');
        // 别的数据有问题，暂时先写死id 走流程
        // sendMessage({orderId: this.orderId, phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
        sendMessage({orderId: '1', phone: '18211193616', contractSubject: this.onlineContracts}).then(response => {
          if (response.code == 200) {
            //  移动端跳转地址,接口没有，暂时先本地跳转看页面
            window.open(window.location.protocol + '//' + window.location.host + '/sendEmail?orderId=' + this.orderId);
          }
        });
      }).catch(() => {

      });
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 驳回
    submitReject() {
      // 暂时没有接口
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.orderData.contractContent.firstContactPhone}).then(response=>{
    // console.log(this.signCode,'signCode')
      checkValidCode({code: this.signCode, phone: '17854561089'}).then(response => {
        if (response.code == 200) {
          this.getHtml();
          this.dialogVisibleCode = false;
        }
      });
    },
    // 获取验证码
    getSignCode() {
      let maxNum = 60;
      let oldCodeText = this.codeText;
      this.codeText = `${maxNum}s重新发送`; // 初始显示倒计时
      let codeCountDown = setInterval(() => {
        this.disabled = true;
        let countDownNum = maxNum--;
        this.codeText = `${countDownNum}s重新发送`;
        if (countDownNum == 0) {
          // 倒计时结束
          this.codeText = oldCodeText;
          clearInterval(codeCountDown);
          this.disabled = false;
        }
      }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
      sendMessage({phone: '17854561089', contractSubject: this.onlineContracts}).then(response => {
        if (response.code == 200) {

        }
      });
    }
  }
};
</script>
