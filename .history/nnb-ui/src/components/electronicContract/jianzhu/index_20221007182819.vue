<template>
  <div>
    <div id="dz-contract-main" class="main-content dz-contract-main" :style="'font-size: 20px; width:'+ width +'; margin: 0 auto;padding: 20px'">
      <el-row class-name="header">
        <h3 style="text-align: right;">
          合同编号：<span class="contract-number">{{ orderDataMain.showNumber }}</span>
        </h3>
      </el-row>
      <h3 style="margin-left:300px">{{orderDataMain.contractName}}</h3>
      <div class="main">
        <div class="contract-head">
          <div class="partA">
            <p class="name">
              <span>甲 方：</span>
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstParty" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstParty }}(以下简称甲方)</u>
            </p>
            <p>
              联 系 人：
              <input v-if="source == 'add'" v-model="orderDataMain.contractContent.firstPrincipalName" type="text" style="font-size: 20px;width: 600px;">
              <u v-else>{{ orderDataMain.contractContent.firstPrincipalName }}</u>
            </p>
            <p>
              联系电话：<u class="phone">{{ orderDataMain.contractContent.firstContactPhone }}</u>
            </p>
            <div />
          </div>
          <div class="partB">
            <p v-if="onlineContracts == 2" class="name">
              乙方：北京繁一企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 1" class="name">
              乙方：北京小苗财税服务有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 3" class="name">
              乙方：北京后企之秀企业管理咨询有限公司(以下简称乙方)</p>
            <p v-if="onlineContracts == 4" class="name">
              乙方：上海企苗企业管理咨询有限公司</p>
            <div>
              <p>
                联 系 人：
                <input v-if="source == 'add'" v-model="orderDataMain.contractContent.secondPrincipalName" type="text" style="font-size: 20px;width: 600px;">
                <u v-else>{{ orderDataMain.contractContent.secondPrincipalName }}</u>
              </p>
              <!-- <p>联系电话：010-62667788</p> -->
              <p>联系电话：{{ orderDataMain.contractContent.secondtContactPhone }}</p>
            </div>
          </div>
          <p class="partTips block">
            甲、乙双方根据《中华人民共和国民法典》以及相关法律法规，就甲方委托乙方代理申请建筑业企业资质办理、网上办公、资料组卷、报送、取证工作。经友好协商，达成如下协议：
          </p>
        </div>
        <div class="contract-content">
          <div class="contract-rule">
            <div class="rules">
              <div class="font-28">
                <b>第一条	委托代理内容</b>
                <p>1. 申请建筑业企业资质类别（全称）
                  <input v-if="source == 'add'" type="text" v-model="orderDataMain.contractContent.contractDetailObject.zizhiType" />
                  <u v-else>{{orderDataMain.contractContent.contractDetailObject.zizhiType}}</u>
                </p>
              </div>
              <jianzhu />
              <p class="font-28">
                <b>第五条	代理费的支付</b>
              </p>
              <p> （一）经甲乙双方协商，确定本项目实施甲方应付给乙方服务总金额及付款方式如下： 总金额（大写）：
               <input v-if="source == 'add'" v-model="orderDataMain.contractContent.payerMoneyCapital" type="text">
                <u v-else>{{ orderDataMain.contractContent.payerMoneyCapital }}</u>元整
                （小写：<input v-if="source == 'add'" v-model="orderDataMain.contractContent.payerMoneyLowerCase" type="text">
                <u v-else>{{ orderDataMain.contractContent.payerMoneyLowerCase }}</u>元 ）；（不含发票及印花）  </p>
              <p> 付款方式：</p>
              <p>1.合同签订时甲方应付乙方订金
                 <input v-model="orderDataMain.contractContent.contractDetailObject.yfdj" type="text">
                元整，（小写：￥<input v-model="orderDataMain.contractContent.contractDetailObject.yfdjLower" type="text">）</p>
              <p>经甲方确认人员配齐后，甲方应付乙方
                 <input v-model="orderDataMain.contractContent.contractDetailObject.yfyf" type="text">
                元整，（小写：￥<input v-model="orderDataMain.contractContent.contractDetailObject.yfyfLower" type="text">）备注：不含人员社保。</p>
              <p>待官方网站查询甲方
                <input v-model="orderDataMain.contractContent.contractDetailObject.zzsp" type="text">资质审批通过后
                甲方付乙方
                 <input v-model="orderDataMain.contractContent.contractDetailObject.yfsf" type="text">
                元整，（小写：￥<input v-model="orderDataMain.contractContent.contractDetailObject.yfsfLower" type="text">）收到费用办理安全生产许可证。</p>
              <!-- <p>乙方收取服务费统一账户：</p> -->
              <div>
                <p>建行账户：耿彬</p>
                <p>开户行：建行北京北环支行</p>
                <p>帐号：6217 0000 1011 1037 779</p>
              </div>
              <!-- <div v-if="onlineContracts == 1">
               <p>建行账户：耿彬</p>
                <p>开户行：中国建设银行北京北环支行</p>
                <p>帐号：11050162540000000320</p>
              </div>
              <div v-if="onlineContracts == 3">
               <p>建行账户：耿彬</p>
                <p>开户行：招商银行股份有限公司北京海淀黄庄支行</p>
                <p>帐号： 110941339410601</p>
              </div>
              <div v-if="onlineContracts == 4">
                <p>建行账户：耿彬</p>
                <p>开户行：招商银行股份有限公司上海嘉定支行</p>
                <p>账号：121927216110701</p>
              </div> -->
              <div class="font-22">
                <p class="font-28">
                  <b>第五条 违约责任</b>
                </p>
                <p>1.双方自签订本协议之日起，乙方应在<input v-model="orderDataMain.contractContent.contractDetailObject.dateLine" type="text">个工作日办妥取证事宜；并将证交付甲方（不可抗力除外）</p>
                <p>2.乙方不履行合同约定的义务，经甲方催告后仍不履行或适当履行的，或乙方没有能力继续履行的，甲方有权解除合同，并要求乙方退还已支付的全部费用和全部资料；</p>
                <p>3.甲方不履行付款义务，经乙方催告后 10 日仍不履行的，乙方有权解除合同并不退还已支付的预付款；</p>
                <p>4.甲方中途无故解除合同，乙方不退还甲方已支付的预付款；乙方中途无故解除合同的，应退还甲方已支付的全部费用及全部资料。</p>
                <p>5.因不可抗力原因导致委托事项无法完成的，经过双方协商解决。</p>
                <p>6.如因乙方办理不当导致办理资质办理不成功，乙方退回甲方所付费用，并退还所有资料（如甲方需要乙方继续办理，甲乙双方再协商）。</p>
                <p>7.如因甲方原因（包括人员证书真实性、人员到场等）导致办理资质办理不成功，乙方不退回甲方所付款项,退回相关资料。</p>
               <p class="font-28">
                  <b>第六条 保密义务</b>
                </p>
                <p>
                  乙方对所掌握的有关甲方的全部材料和信息，除履行本协议所需或因本协议履行必然导致的披露和公开以及甲方的书面许可外，负有义务进行保密。协议终止后，乙方应将掌握的甲方的所有资料及其复印件全部归还甲方，并删除所有与协议相关的信息记录。
                </p>
                <p class="font-28">
                  <b>第七条  未尽事宜</b>
                </p>
                <p>本协议未尽事宜由双方协商解决，并按《中华人民共和国民法典》以及相关法律办理。</p>
                <p class="font-28">
                  <b>第八条 争议解决</b>
                </p>
                <p>1、 本协议履行中若发生争议，双方协商解决，协商不成时，任何一方均可向北京仲裁委员会提起仲裁。</p>
                <p>2、 本协议自甲乙双方签字盖章之日起生效，本协议一式两份，双方各持一份，具有同等效力。</p>
              </div>
              <p>(以下无正文，为双方签字、盖章页)</p>
            </div>
            <div class="signature" style="margin-bottom: 40px;">
              <div class="signA" style="width: 50%;float: left;">
                <!-- <div class="signAImg">甲方：（盖章）</div> -->
                <div class="signAImg" style="position: relative;">甲方盖章（签字）：{{ orderDataMain.contractContent.firstParty }}
                  <i v-if="!resultImg && source =='mobile'" style="cursor: pointer;" @click="signBoard">签字区</i>
                  <img v-if="resultImg" style="width: 100px;position: absolute;" :src="resultImg" class="avatar" @click="signBoard" />
                </div>
                <p>
                  签约代表：<u class="phone">{{ orderDataMain.contractContent.firstPrincipalName }}</u>
                </p>
                <div class="time-mes">
                  <div class="time">签字日期：{{ orderDataMain.contractContent.date}}</div>
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.contractDetailObject.fristPartyDate }}</div> -->
                </div>
              </div>
              <div class="signB" style="width: 50%;float: right;position: relative;">

                <div v-if="onlineContracts == 2" class="signBImg">
                  乙方（盖章）：北京繁一企业管理咨询有限公司</div>
                <!-- 此处需要判断地理id ，显示北京小苗或者河南z苗 -->
                <div v-if="onlineContracts == 1" class="signBImg">
                  乙方（盖章）：北京小苗财税服务有限公司</div>
                <div v-if="onlineContracts == 4" class="signBImg">
                  乙方：上海企苗企业管理咨询有限公司</div>
                <img v-if="source != 'add'" style="position: absolute;width: 100px;left: 120px;" :src="onlineContracts == 1?Seal:onlineContracts == 2?FY_Seal:HQ_Seal" />
                <div v-if="onlineContracts == 3" class="signBImg"> 乙方（盖章）：北京后企之秀企业管理咨询有限公司</div>
               <div class="">签约代表：{{ orderDataMain.contractContent.secondPrincipalName }}</div>
                <div class="time-mes  time-mesB">
                 <div class="time">签字日期：{{ orderDataMain.contractContent.date}}</div>
                  <!-- <div class="time">签字日期：{{ orderDataMain.contractContent.contractDetailObject.secondPartyDate }}</div> -->
                </div>
              </div>
              <div style="clear: both;" />
            </div>
          </div>
        </div>
      </div>
    </div>
  
    <el-dialog
      title="电子签名"
      append-to-body
      width="70%"
      :modal-append-to-body="false"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
    >
      <el-card class="qianming-container" body-style="padding:0px">
        <vue-esign ref="esign" :is-crop="isCrop" :width="source == 'mobile'?300:600" :height="300" :line-width="lineWidth" :line-color="lineColor" :bg-color.sync="bgColor" />
        <div class="contro-container">
          <el-button type="danger" @click="handleReset">清空</el-button>
          <el-button type="primary" @click="handleGenerate">保存</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisibleCode"
      :modal-append-to-body="false"
      width="30%"
      :before-close="handleCloseCode"
    >
      <input v-model="signCode" type="text">
      <!-- <button @click="getSignCode()">获取验证码</button> -->
      <input type="button" class="yan" :value="codeText" :disabled="disabled" @click="getSignCode()">
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleCode = false">取 消</el-button>
        <el-button type="primary" @click="signCodeSubmit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>
<script>
import FY_Seal from '../../../assets/images/FY_seal.png';
import Seal from '../../../assets/images/seal.png';
import HQ_Seal from '../../../assets/images/HQ_Seal.png';
import {client} from '@/utils/alioss';
import jianzhu from './jianzhu.vue'
import {
  checkoutContract, genPdf, getPdf, sendMessage, checkValidCode
} from '@/api/contract/electronicContract';
export default {
  name: 'Edit',
  components: {
    jianzhu
  },
  props: {
    productData: {
      type: Array,
      default() {
        return [];
      },
    },
    customData: {
      type: Object,
      default() {
        return {};
      },
    },
    phoneNumber: {
      type: String,
      default() {
        return '';
      },
    },
    onlineContract: {// 1 牛牛帮 2 繁一 3 后企
      type: String,
      default() {
        return '';
      },
    },
    dzCityId: {
      type: String,
      default() {
        return '';
      },
    },
    source: {// add 提单  view 查看电子合同  send 发送电子合同页面  mobile 手机端打开签约链接
      type: String,
      default() {
        return '';
      },
    },
    orderId: {
      type: String,
      default() {
        return '';
      },
    },
    width: {
      type: String,
      default() {
        return '';
      },
    },
    orderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      checkList: [],
      orderDataMain: {},
      time: null,
      lineWidth: 6,
      lineColor: '#000000',
      bgColor: '',
      resultImg: '',
      isCrop: false,
      dialogVisible: false,
      HQ_Seal: HQ_Seal,
      FY_Seal: FY_Seal,
      Seal: Seal,
      dialogVisibleCode: false,
      signCode: '',
      checkListAll: [],
      checked: false,
      checkedAll: [],
      disabled: false,
      codeText: '获取验证码',
      onlineContracts: '',
      checkOptions: ['原件', '复印件']
    };
  },
  watch: {
    onlineContracts:{
      immediate: true,
      handler: function(n){
      }
    },
    orderData: {
      immediate: true,
      handler: function (n) {
        if (n) {
          this.orderDataMain = JSON.parse(JSON.stringify(n))
          this.orderDataMain.contractContent.contractDetailObject = this.orderDataMain.contractContent.contractDetailObject || {}
           this.orderDataMain.contractContent.firstContactPhone = this.phoneNumber;
          this.orderDataMain.contractContent.secondParty = this.orderDataMain.contractContent.secondParty ||this.$store.state.user.userInfo.dept.deptName;
          this.orderDataMain.contractContent.sellerName = this.orderDataMain.contractContent.sellerName || this.$store.state.user.userInfo.userName;
        }
      },
    },
  },
  mounted() {
  },
  created() {
    // 用户短信入口orderId从url中取值
    // 进去电子合同初始化
    // if (this.source == 'add') { // 提单新增电子合同
    //   this.getInitData();
    // } else { // 其他状态
    //   this.getInitViewData();
    // }
    // 电子合同主体，页面判断使用
    this.onlineContracts = this.onlineContract;
  },
  methods: {
    emitData(){
      return this.orderDataMain
    },
    serveContent(val) {
      this.checkListAll = val;
    },
    // 签约成功之后，生成pdf 文件
    getHtml() {
      let html = document.getElementById('dz-contract-main').innerHTML;
      // html就是整个合同文字内容
      // console.log(html)
      // genPdf接口报500
      genPdf({orderId: this.orderId, code: html}).then(response => {
        if(response.code == 200){
          this.$router.go(-1)
        }
      });
    },
    // 电子签名弹框打开
    signBoard() {
      this.dialogVisible = true;
    },
    // 电子签名弹框隐藏
    handleClose() {
      this.dialogVisible = false;
    },
    // 清空画板..
    handleReset() {
      this.$refs.esign.reset();
      this.resultImg = '';
      this.dialogVisible = false;
    },
    // 生成签名图片..
    handleGenerate() {
      this.$refs.esign.generate().then(res => {
        console.log(res, 'qianming');
        let randnum = Math.random() * 10000000000000;
        randnum = Math.floor(randnum);
        // let fileName = "dianziqianming/" + randnum + '.png'
        let fileName = randnum + '.png';
        let file = this.dataURLtoFile(res, fileName);
        console.log(fileName, 'fileName');
        console.log(file, 'file');
        let date = new Date();
        let dateYear = date.getFullYear(); // 获取年
        let dateMonth = date.getMonth() + 1; // 获取月
        let dateDate = date.getDate(); // 获取当日
        dateMonth = dateMonth > 9 ? dateMonth : '0' + dateMonth;
        dateDate = dateDate > 9 ? dateDate : '0' + dateDate;
        let dir = 'information/' + dateYear + dateMonth + dateDate + '/' + fileName;
        file.uid = '1657869457487';
        client().multipartUpload(dir, file).then(res => {
          console.log(res, 'res');
          this.resultImg = res.res.requestUrls[0];
          this.dialogVisible = false;
        }).catch(err => {});
      }).catch(err => {
        this.$message.error('请签名之后提交！');
      });
    },
    // 将base64转换为文件
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','); var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]); var n = bstr.length; var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, {type: mime});
    },
    // 提单 传递电子合同数据  this.contractDetailObject
    // submit() {
    //   // this.$emit('getSignData', this.orderData);
    //   this.$emit('getSignData', this.orderDataMain);
    // },
    // 经理审批同意，发送短信
    submitSend() {
      console.log('经理审批同意，发送短信');
      this.$confirm('是否发送给客户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('close');
        // 别的数据有问题，暂时先写死id 走流程
        // sendMessage({orderId: this.orderId, phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
        sendMessage({orderId: '1', phone: '18211193616', contractSubject: this.onlineContracts}).then(response => {
          if (response.code == 200) {
            //  移动端跳转地址,接口没有，暂时先本地跳转看页面
            window.open(window.location.protocol + '//' + window.location.host + '/sendEmail?orderId=' + this.orderId);
          }
        });
      }).catch(() => {

      });
    },
    // 签约
    submitSign() {
      this.dialogVisibleCode = true;
    },
    // 驳回
    submitReject() {
      // 暂时没有接口
    },
    // 关闭获取验证码弹框
    handleCloseCode() {
      this.dialogVisibleCode = false;
    },
    // 签约
    signCodeSubmit() {
      //  code 验证码 写死 走流程
      // checkValidCode({code:this.signCode, phone:this.orderData.contractContent.firstContactPhone}).then(response=>{
    // console.log(this.signCode,'signCode')
      checkValidCode({code: this.signCode, phone: '17854561089'}).then(response => {
        if (response.code == 200) {
          this.getHtml();
          this.dialogVisibleCode = false;
        }
      });
    },
    // 获取验证码
    getSignCode() {
      let maxNum = 60;
      let oldCodeText = this.codeText;
      this.codeText = `${maxNum}s重新发送`; // 初始显示倒计时
      let codeCountDown = setInterval(() => {
        this.disabled = true;
        let countDownNum = maxNum--;
        this.codeText = `${countDownNum}s重新发送`;
        if (countDownNum == 0) {
          // 倒计时结束
          this.codeText = oldCodeText;
          clearInterval(codeCountDown);
          this.disabled = false;
        }
      }, 1000);
      // 先写死数据
      // sendMessage({ phone: this.orderData.contractContent.firstContactPhone}).then(response=>{
      sendMessage({phone: '17854561089', contractSubject: this.onlineContracts}).then(response => {
        if (response.code == 200) {

        }
      });
    }
  }
};
</script>
