<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="输入订单编号/签约人/企业名称"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="examineStatus">
        <el-select v-model="queryParams.examineStatus" clearable placeholder="请选择">
          <el-option
            v-for="item in examineStatusOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属部门" prop="deptId">
        <el-cascader
          v-model="queryParams.deptId"
          :options="deptOptions"
          :props="propsAdd"
          collapse-tags
          clearable
        />
      </el-form-item>
      <el-form-item label="回款时间" prop="examineTime">
        <el-date-picker
          v-model="queryParams.examineTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="审核时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>
    <el-table
      v-loading="loading"
      :data="contractList"
      size="small"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="订单编号" align="left">
        <template slot-scope="scope">
          <span class="buld-text" @click="getDetails(scope.row)">{{ scope.row.orderNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="left" prop="clientName" />
      <el-table-column label="联系人" align="left" prop="customerName" />
      <el-table-column label="签约人" align="left" prop="userName" />
      <el-table-column label="本次回款" align="left" prop="collectionPrice" />
      <el-table-column label="剩余尾款" align="left" prop="lastPrice" />
      <el-table-column label="审核状态" align="left" prop="examineStatusName" />
      <el-table-column label="审核时间" align="left" prop="examineTime" />
      <el-table-column label="创建时间" align="left" prop="crateTime" />
      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
        width="250px"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.examineStatusType == 0">
            <el-button
              size="mini"
              type="text"
              @click="handleUpdate(scope.row, 'pass')"
            >通过</el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleUpdate(scope.row, 'reject')"
            >驳回</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <popUp
      v-if="popUpOpen"
      :open="popUpOpen"
      :type="type"
      :source="'finalPayment'"
      :pop-up-data="popUpData"
      :pop-up-query="popUpQuery"
      @closePopUp="closePopUp"
    />
  </div>
</template>

<script>
import {
  retainageReturnExamineList,
  retainageReturnExamineOperation
} from '@/api/audit/order';
import {treeselect} from '@/api/system/dept';
import popUp from '../children/popUp.vue';

export default {
  name: 'ContractApplyFor',
  components: {popUp},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 部门线索管理时限配置表格数据
      contractList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        deptId: undefined,
        examineStatus: undefined,
        examineTime: null,
        createTime: null,
      },
      type:'',
      // 表单参数
      form: {},
      examineStatusOptions: [
        {name: '待审核', id: '0'},
        {name: '审核通过', id: '1'},
        {name: '审核驳回', id: '2'},
        {name: '撤销', id: '3'},
        {name: '删除', id: '4'},
        {name: '作废', id: '5'},
      ],
      deptOptions: [],
      propsAdd: {
        value: 'id',
        label: 'label',
        children: 'children',
        multiple: false,
        emitPath: false,
      },
      popUpOpen: false,
      popUpData: {},
      popUpQuery: {},
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
  },
  methods: {
    getDetails(data) {
      this.$router.push({
        path: '/order/orderManageDetails',
        query: {id: data.orderId},
      });
    },
    /** 列表 */
    getList() {
      
      this.loading = true;
      let query = {...this.queryParams};
      if (query.createTime) {
        query.createTimeBegin = query.createTime[0];
        query.createTimeEnd = query.createTime[1];
      }
      if (query.examineTime) {
        query.examineTimeBegin = query.examineTime[0];
        query.examineTimeEnd = query.examineTime[1];
      }
      delete query.createTime;
      delete query.examineTime;
      retainageReturnExamineList(query).then((response) => {
        let data = response.rows;
        this.contractList = data;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },

    // 查看详情
    queryDetails(rows) {
      this.$router.push({
        path: '/contract/contractDetails',
        query: {id: rows.id},
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        keyword: '',
        deptId: undefined,
        examineStatus: undefined,
        examineTime: null,
        createTime: null,
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 修改按钮操作 */
    handleUpdate(row, type) {
      this.type=type;
      let query = {};
      if (type == 'pass') {
        query = {
          orderId: row.orderId,
          returnId: row.returnId,
          examineOperation: '1',
        };
        retainageReturnExamineOperation(query).then((response) => {
          if(response.code===200){
          this.msgSuccess(response.msg);
          this.getList();

          }
        });
      } else if (type == 'reject') {
        this.popUpOpen = true;
        this.popUpQuery = {
          orderId: row.orderId,
          returnId: row.returnId,
          examineOperation: '2',
        };
      }
    },
    closePopUp() {
      this.popUpOpen = false;
      this.getList();
    },
  },
};
</script>
<style scoped>
.text-red {
  color: red;
}
.result-box {
  margin-bottom: 20px;
}
.buld-text {
  color: #1890ff;
  cursor: pointer;
}
</style>
