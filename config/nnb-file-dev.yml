# 本地文件上传    
file:
    domain: http://************:9300
    path: D:/nnb/uploadPath
    prefix: /statics

# FastDFS配置
fdfs:
  domain: http://************
  soTimeout: 3000
  connectTimeout: 2000
  trackerList: ************:22122

# Minio配置
minio:
  url: http://************:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: test
# swagger配置
swagger:
  title: 文件服务接口文档
  license: Powered By nnb
  licenseUrl: https://nnb.vip