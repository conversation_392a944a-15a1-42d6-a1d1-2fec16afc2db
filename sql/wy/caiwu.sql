--财务记账续费数据导出-郭红涛
SELECT
    eo.vc_order_number AS "订单编号",
    eo.dat_finance_collection_time AS "财务首笔收款时间",
    su.nick_name AS "签约销售",
    ec.contactName AS "客户名称",
    ee.vc_company_name AS "企业",
    eo.num_total_price AS "订单总金额",
    sm.ac_start AS "记账开始",
    sm.ac_end AS "记账结束"
FROM
    s_service_main sm
        LEFT JOIN erp_client ec ON ec.num_enterprise_id = sm.num_enterprise_id
        LEFT JOIN erp_enterprise ee ON ee.id = sm.num_enterprise_id
        LEFT JOIN erp_orders eo ON eo.id = sm.order_id
        LEFT JOIN sys_user su ON su.user_id = sm.seller_id
WHERE
        sm.id IN (
        SELECT
            MAX( id )
        FROM
            s_service_main
        WHERE
                service_type = 10
          AND service_status != 8
  AND service_status != 9
  AND service_status != 10
  AND num_enterprise_id IN (
    SELECT
    num_enterprise_id
    FROM
    (
    SELECT
    num_enterprise_id,
    COUNT( 1 ) AS smcount
    FROM
    s_service_main smids
    WHERE
    ( service_type = 10 OR type_before_zz = 10 )
  AND service_status != 8
  AND service_status != 9
  AND service_status != 10
    GROUP BY
    num_enterprise_id
    ) count
    WHERE
    smcount > 1
    )
GROUP BY
    num_enterprise_id
    )
    AND eo.dat_finance_collection_time >= "2023-01-01 00:00:00"
    AND eo.dat_finance_collection_time <= "2023-04-30 23:59:59"



-- 尾款订单数据
SELECT
    eo.vc_order_number AS "订单编号",
    ee.vc_company_name AS "企业名称",
    ec.contactName AS "联系人",
    eso.num_product_id AS "产品ID",
    epc.vc_classification_name AS "产品分类",
    ept.vc_type_name AS "产品类型",
    epn.vc_product_name AS "产品名称",
    eps.vc_service_name AS "服务类型",
    su.nick_name AS "签约人",
    sd.dept_name AS "签约部门",
    eso.num_last_price AS "尾款",
    eo.dat_signing_date AS "签约时间"
FROM
    erp_service_orders eso
        LEFT JOIN erp_orders eo ON eo.id = eso.num_order_id
        LEFT JOIN erp_client ec ON ec.id = eo.num_client_id
        LEFT JOIN erp_enterprise ee ON ee.id = ec.num_enterprise_id
        LEFT JOIN erp_product_detail epd ON epd.num_product_id = eso.num_product_id
        LEFT JOIN erp_product_name epn ON epd.num_name_id = epn.num_name_id
        LEFT JOIN erp_product_type ept ON ept.num_type_id = epn.num_type_id
        LEFT JOIN erp_product_classification epc ON epc.num_classification_id = ept.num_classification_id
        LEFT JOIN erp_product_service eps ON eps.num_service_id = epd.num_service_id
        LEFT JOIN sys_user su ON su.user_id = eo.num_user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id
WHERE
        eo.dat_signing_date > "2023-01-01 00:00:00"
  AND eso.num_last_price > 0
  AND eo.num_create_order_examine_status = 5
  AND eo.num_valid_status = 0